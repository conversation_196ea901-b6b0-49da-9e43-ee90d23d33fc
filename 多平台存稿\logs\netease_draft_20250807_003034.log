[2025-08-07 00:30:39] [INFO] 🚀 启动多平台存稿工具...
[2025-08-07 00:30:39] [INFO] ✅ 定时任务管理器UI初始化完成
[2025-08-07 00:30:40] [INFO] ✅ 定时任务管理标签页创建完成
[2025-08-07 00:32:33] [INFO] ✅ 定时任务管理器UI清理完成
25-08-07 00:30:39] [INFO] 已从toutiao平台加载存稿详情数据，共 83 个账号
[2025-08-07 00:30:40] [INFO] 🚀 定时任务调度器已启动
[2025-08-07 00:30:40] [DEBUG] 🔄 调度器主循环开始
[2025-08-07 00:30:40] [INFO] 已确保所有必要目录存在
[2025-08-07 00:30:40] [DEBUG] 🔍 [调试] 当前平台: toutiao
[2025-08-07 00:30:40] [DEBUG] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-08-07 00:30:40] [DEBUG] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-08-07 00:30:40] [SUCCESS] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-08-07 00:30:40] [DEBUG] 🔍 [调试] 更新账号树 - 当前平台: toutiao
[2025-08-07 00:30:40] [DEBUG] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-08-07 00:30:40] [DEBUG] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-08-07 00:30:40] [INFO] 已加载账号数据: 85 条记录
[2025-08-07 00:30:40] [DEBUG] 🔍 [调试] 账号数据为空或未加载
[2025-08-07 00:30:40] [SUCCESS] ✅ 初始UI更新完成
[2025-08-07 00:31:06] [INFO] 已选择账号: ***********
[2025-08-07 00:31:08] [DEBUG] 🚀 开始为账号 *********** 存稿
[2025-08-07 00:31:08] [INFO] 已确保所有必要目录存在
[2025-08-07 00:31:08] [INFO] 使用单线程模式处理账号
[2025-08-07 00:31:08] [DEBUG] 🔍 [DEBUG] 准备启动单线程任务，参数: ***********
[2025-08-07 00:31:08] [DEBUG] 🔍 [DEBUG] 单线程任务已启动
[2025-08-07 00:31:08] [DEBUG] 🔍 [DEBUG] _single_task_thread 开始执行，参数: ***********
[2025-08-07 00:31:08] [DEBUG] 📋 开始处理单个账号: ***********
[2025-08-07 00:31:08] [INFO] 视频分配方式: 随机分配
[2025-08-07 00:31:08] [DEBUG] 开始处理账号: ***********
[2025-08-07 00:31:08] [SUCCESS] 正在使用Cookie登录账号: ***********
[2025-08-07 00:31:08] [SUCCESS] ✅ Cookie预检测通过，启动浏览器验证...
[2025-08-07 00:31:15] [SUCCESS] 开始查询账号数据: *********** [toutiao] 浏览器驱动创建成功，使用端口: 9515
[2025-08-07 00:31:19] [SUCCESS] 正在加载Cookie文件: D:/网易号全自动/头条号账号\***********.txt
[2025-08-07 00:31:29] [SUCCESS] ✅ Cookie添加完成
[2025-08-07 00:31:30] [SUCCESS] ✅ 头条号快速登录成功 (等待0.5秒)
[2025-08-07 00:31:30] [SUCCESS] 登录成功，开始处理视频: ***********
[2025-08-07 00:31:31] [INFO] 找到 652 个视频文件
[2025-08-07 00:31:31] [INFO] 已随机打乱 652 个视频文件的顺序
[2025-08-07 00:31:31] [DEBUG] 开始处理视频: 利剑玫瑰：男人为儿子找了个保姆，不料保姆是人贩子.mp4
[2025-08-07 00:31:31] [DEBUG] 🎬 开始头条号视频存稿流程
[2025-08-07 00:31:31] [DEBUG] 🔍 正在定位视频菜单...
[2025-08-07 00:31:31] [SUCCESS] ✅ 已点击视频菜单
[2025-08-07 00:31:34] [INFO] 🌐 当前不在上传页面，导航到视频上传页面...
[2025-08-07 00:31:38] [SUCCESS] ✅ 已导航到视频上传页面
[2025-08-07 00:31:38] [INFO] 📁 准备上传视频: 利剑玫瑰：男人为儿子找了个保姆，不料保姆是人贩子.mp4
[2025-08-07 00:31:38] [DEBUG] 🔍 找到上传按钮，准备点击...
[2025-08-07 00:31:38] [SUCCESS] ✅ 已点击上传视频按钮
[2025-08-07 00:31:40] [SUCCESS] ✅ 视频文件已选择并开始上传
[2025-08-07 00:31:40] [DEBUG] ⏳ 开始监控视频上传状态...
[2025-08-07 00:31:40] [INFO] 📊 当前上传状态: 上传中… 0%
[2025-08-07 00:31:40] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 00:31:45] [INFO] 📊 当前上传状态: 上传中… 48.11%
[2025-08-07 00:31:45] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 00:31:50] [INFO] 📊 当前上传状态: 上传中… 100%
[2025-08-07 00:31:50] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 00:31:55] [SUCCESS] 📊 当前上传状态: 上传成功
[2025-08-07 00:31:55] [SUCCESS] ✅ 视频上传成功！
[2025-08-07 00:31:55] [DEBUG] 📷 开始上传视频封面...
[2025-08-07 00:31:55] [SUCCESS] ✅ 已点击上传封面按钮
[2025-08-07 00:31:58] [SUCCESS] ✅ 已选择本地上传
[2025-08-07 00:32:00] [INFO] 📁 准备上传封面文件: D:\头条全自动\视频搬运\已处理封面\利剑玫瑰：男人为儿子找了个保姆，不料保姆是人贩子.jpg
[2025-08-07 00:32:00] [INFO] 📊 封面文件大小: 334178 bytes
[2025-08-07 00:32:00] [SUCCESS] ✅ 封面文件已选择，开始上传...
[2025-08-07 00:32:00] [INFO] 🔄 触发文件上传事件...
[2025-08-07 00:32:00] [SUCCESS] ✅ 文件上传事件已触发
[2025-08-07 00:32:00] [INFO] ⏳ 等待封面图片加载...
[2025-08-07 00:32:00] [DEBUG] 🔍 开始全面检测封面上传状态...
[2025-08-07 00:32:00] [DEBUG] ⏳ 等待文件开始处理...
[2025-08-07 00:32:03] [DEBUG] 🔍 页面中找到 0 个canvas元素
[2025-08-07 00:32:03] [DEBUG] 🔍 页面中找到 2 个可见img元素
[2025-08-07 00:32:03] [DEBUG] 🔍 页面中找到 3 个文件输入元素
[2025-08-07 00:32:03] [DEBUG] 🔍 页面中找到 1 个确认按钮
[2025-08-07 00:32:03] [ERROR] ❌ 未找到Canvas元素
[2025-08-07 00:32:04] [ERROR] ❌ 检测到上传错误: 视频格式错误
[2025-08-07 00:32:04] [WARNING] ⚠️ 封面上传检测超时，尝试强制处理...
[2025-08-07 00:32:04] [INFO] 🔄 强制处理文件上传...
[2025-08-07 00:32:04] [SUCCESS] ✅ 点击了处理按钮: 上传成功
[2025-08-07 00:32:05] [INFO] 🔄 已尝试强制处理，再次等待...
[2025-08-07 00:32:08] [DEBUG] 🔍 开始全面检测封面上传状态...
[2025-08-07 00:32:08] [DEBUG] ⏳ 等待文件开始处理...
[2025-08-07 00:32:11] [DEBUG] 🔍 页面中找到 0 个canvas元素
[2025-08-07 00:32:11] [DEBUG] 🔍 页面中找到 2 个可见img元素
[2025-08-07 00:32:11] [DEBUG] 🔍 页面中找到 3 个文件输入元素
[2025-08-07 00:32:11] [DEBUG] 🔍 页面中找到 1 个确认按钮
[2025-08-07 00:32:11] [ERROR] ❌ 未找到Canvas元素
[2025-08-07 00:32:11] [ERROR] ❌ 检测到上传错误: 视频格式错误
[2025-08-07 00:32:11] [WARNING] ⚠️ 强制处理后仍未检测到上传完成，使用最终备用策略
[2025-08-07 00:32:16] [DEBUG] 🔍 寻找第一个确认按钮...
[2025-08-07 00:32:26] [WARNING] ⚠️ 原XPath无法找到第一个确认按钮，尝试其他方法
[2025-08-07 00:32:26] [SUCCESS] ✅ 通过文本匹配点击第一个确定按钮
[2025-08-07 00:32:29] [DEBUG] 🔍 寻找第二个确认按钮...
[2025-08-07 00:32:32] [DEBUG] 正在关闭程序，清理后台资源...
[2025-08-07 00:32:33] [DEBUG] 🔄 调度器主循环结束
[2025-08-07 00:32:33] [DEBUG] 正在关闭任务执行器...
[2025-08-07 00:32:33] [INFO] 任务执行器已关闭
[2025-08-07 00:32:33] [INFO] ⏹️ 定时任务调度器已停止
[2025-08-07 00:32:33] [SUCCESS] ✅ 调度器管理器清理完成
[2025-08-07 00:32:33] [SUCCESS] ✅ 定时任务管理器UI清理完成
[2025-08-07 00:32:33] [DEBUG] 🧹 开始清理所有资源...
[2025-08-07 00:32:33] [DEBUG] ⏹️ 正在停止存稿任务...
[2025-08-07 00:32:33] [INFO] ⏹️ 存稿任务已停止
[2025-08-07 00:32:33] [DEBUG] 正在停止并发管理器...
[2025-08-07 00:32:33] [SUCCESS] ✅ 运行中的任务已停止
[2025-08-07 00:32:33] [SUCCESS] ✅ 定时器线程已清理（升级版系统自动管理）
[2025-08-07 00:32:33] [WARNING] ⚠️ 原XPath无法找到第二个确认按钮，尝试其他方法
[2025-08-07 00:32:33] [WARNING] ⚠️ 通过文本查找第二个确认按钮失败: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff6c2416f75+76917]
	GetHandleVerifier [0x0x7ff6c2416fd0+77008]
	(No symbol) [0x0x7ff6c21c9c1c]
	(No symbol) [0x0x7ff6c221055f]
	(No symbol) [0x0x7ff6c2248332]
	(No symbol) [0x0x7ff6c2242e53]
	(No symbol) [0x0x7ff6c2241f19]
	(No symbol) [0x0x7ff6c2194b05]
	GetHandleVerifier [0x0x7ff6c26ed2ad+3051437]
	GetHandleVerifier [0x0x7ff6c26e7903+3028483]
	GetHandleVerifier [0x0x7ff6c270589d+3151261]
	GetHandleVerifier [0x0x7ff6c243183e+185662]
	GetHandleVerifier [0x0x7ff6c24396ff+218111]
	(No symbol) [0x0x7ff6c2193b00]
	GetHandleVerifier [0x0x7ff6c2805f18+4201496]
	BaseThreadInitThunk [0x0x7ffcc2f7e8d7+23]
	RtlUserThreadStart [0x0x7ffcc4e5c34c+44]

[2025-08-07 00:32:33] [WARNING] ⚠️ 通过可见按钮查找确认按钮失败: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff6c2416f75+76917]
	GetHandleVerifier [0x0x7ff6c2416fd0+77008]
	(No symbol) [0x0x7ff6c21c9c1c]
	(No symbol) [0x0x7ff6c221055f]
	(No symbol) [0x0x7ff6c2248332]
	(No symbol) [0x0x7ff6c2242e53]
	(No symbol) [0x0x7ff6c2241f19]
	(No symbol) [0x0x7ff6c2194b05]
	GetHandleVerifier [0x0x7ff6c26ed2ad+3051437]
	GetHandleVerifier [0x0x7ff6c26e7903+3028483]
	GetHandleVerifier [0x0x7ff6c270589d+3151261]
	GetHandleVerifier [0x0x7ff6c243183e+185662]
	GetHandleVerifier [0x0x7ff6c24396ff+218111]
	(No symbol) [0x0x7ff6c2193b00]
	GetHandleVerifier [0x0x7ff6c2805f18+4201496]
	BaseThreadInitThunk [0x0x7ffcc2f7e8d7+23]
	RtlUserThreadStart [0x0x7ffcc4e5c34c+44]

[2025-08-07 00:32:33] [ERROR] ❌ 无法找到第二个确认按钮
[2025-08-07 00:32:33] [ERROR] ❌ 点击确认按钮失败
[2025-08-07 00:32:33] [WARNING] ⚠️ 上传封面失败，继续存稿流程
[2025-08-07 00:32:33] [DEBUG] 💾 开始保存草稿...
[2025-08-07 00:32:34] [ERROR] ❌ 保存草稿失败: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff6c2416f75+76917]
	GetHandleVerifier [0x0x7ff6c2416fd0+77008]
	(No symbol) [0x0x7ff6c21c9c1c]
	(No symbol) [0x0x7ff6c221055f]
	(No symbol) [0x0x7ff6c2248332]
	(No symbol) [0x0x7ff6c2242e53]
	(No symbol) [0x0x7ff6c2241f19]
	(No symbol) [0x0x7ff6c2194b05]
	GetHandleVerifier [0x0x7ff6c26ed2ad+3051437]
	GetHandleVerifier [0x0x7ff6c26e7903+3028483]
	GetHandleVerifier [0x0x7ff6c270589d+3151261]
	GetHandleVerifier [0x0x7ff6c243183e+185662]
	GetHandleVerifier [0x0x7ff6c24396ff+218111]
	(No symbol) [0x0x7ff6c2193b00]
	GetHandleVerifier [0x0x7ff6c2805f18+4201496]
	BaseThreadInitThunk [0x0x7ffcc2f7e8d7+23]
	RtlUserThreadStart [0x0x7ffcc4e5c34c+44]

[2025-08-07 00:32:34] [WARNING] ⚠️ 第1次尝试失败，2次重试机会剩余
[2025-08-07 00:32:34] [INFO] ⏳ 等待2秒后重试...
[2025-08-07 00:32:34] [SUCCESS] ✅ Chrome进程强制清理完成
[2025-08-07 00:32:34] [SUCCESS] ✅ 浏览器进程已清理
[2025-08-07 00:32:35] [SUCCESS] ✅ 日志处理线程已清理
[2025-08-07 00:32:35] [SUCCESS] ✅ 存稿处理器已清理
[2025-08-07 00:32:35] [SUCCESS] ✅ 线程池已清理
[2025-08-07 00:32:35] [DEBUG] 正在停止串行刷新工作线程...
[2025-08-07 00:32:36] [DEBUG] 💾 开始保存草稿...
