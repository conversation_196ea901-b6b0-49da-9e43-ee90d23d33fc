"""
进度面板组件 - 显示处理进度
"""

import tkinter as tk
from tkinter import ttk

def create_progress_panel(parent, ui_instance):
    """创建进度面板"""
    # 创建进度框架
    progress_frame = ttk.LabelFrame(parent, text="处理进度")
    progress_frame.pack(fill=tk.X, padx=5, pady=5)

    # 进度条区域
    progress_bar_frame = ttk.Frame(progress_frame)
    progress_bar_frame.pack(fill=tk.X, padx=10, pady=(10, 5))

    # 创建进度条
    ttk.Label(progress_bar_frame, text="处理进度:").pack(side=tk.LEFT, padx=(0, 10))
    progress_bar = ttk.Progressbar(progress_bar_frame, orient="horizontal", length=600, mode="determinate")
    progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True)
    progress_label = ttk.Label(progress_bar_frame, text="0%")
    progress_label.pack(side=tk.LEFT, padx=(10, 0))

    # 状态统计区域
    stats_frame = ttk.Frame(progress_frame)
    stats_frame.pack(fill=tk.X, padx=10, pady=(5, 10))

    # 统计标签
    stats_style = ttk.Style()
    stats_style.configure("Success.TLabel", foreground="green")
    stats_style.configure("Fail.TLabel", foreground="red")

    ttk.Label(stats_frame, text="总计:").pack(side=tk.LEFT)
    total_label = ttk.Label(stats_frame, text="0")
    total_label.pack(side=tk.LEFT, padx=(5, 15))

    ttk.Label(stats_frame, text="成功:").pack(side=tk.LEFT)
    success_label = ttk.Label(stats_frame, text="0", style="Success.TLabel")
    success_label.pack(side=tk.LEFT, padx=(5, 15))

    ttk.Label(stats_frame, text="失败:").pack(side=tk.LEFT)
    fail_label = ttk.Label(stats_frame, text="0", style="Fail.TLabel")
    fail_label.pack(side=tk.LEFT, padx=(5, 15))

    # 删除提示标签，保持界面简洁

    # 保存引用
    ui_instance.progress_bar = progress_bar
    ui_instance.progress_label = progress_label
    ui_instance.total_label = total_label
    ui_instance.success_label = success_label
    ui_instance.fail_label = fail_label

    # 保存进度框架引用
    ui_instance.progress_frame = progress_frame

    # 初始化计数器
    ui_instance.success_count = 0
    ui_instance.failed_count = 0
    ui_instance.total_processed = 0

    # 确保进度面板可见
    print("进度面板已创建")

    return progress_frame

def reset_progress(ui_instance):
    """重置进度UI"""
    if hasattr(ui_instance, 'progress_bar'):
        ui_instance.progress_bar['value'] = 0
    if hasattr(ui_instance, 'progress_label'):
        ui_instance.progress_label.config(text="0%")
    if hasattr(ui_instance, 'total_label'):
        ui_instance.total_label.config(text="0")
    if hasattr(ui_instance, 'success_label'):
        ui_instance.success_label.config(text="0")
    if hasattr(ui_instance, 'fail_label'):
        ui_instance.fail_label.config(text="0")

    # 重置计数器
    ui_instance.success_count = 0
    ui_instance.failed_count = 0

    # 更新UI
    ui_instance.root.update_idletasks()

def update_progress(ui_instance, success=None, fail=None, total=None, percent=None):
    """更新进度UI"""
    if success is not None and hasattr(ui_instance, 'success_label'):
        ui_instance.success_count = success
        ui_instance.success_label.config(text=str(success))

    if fail is not None and hasattr(ui_instance, 'fail_label'):
        ui_instance.failed_count = fail
        ui_instance.fail_label.config(text=str(fail))

    if total is not None and hasattr(ui_instance, 'total_label'):
        # 只更新显示标签，不修改内部计数器
        ui_instance.total_label.config(text=str(total))

    if percent is not None and hasattr(ui_instance, 'progress_bar') and hasattr(ui_instance, 'progress_label'):
        ui_instance.progress_bar['value'] = percent
        ui_instance.progress_label.config(text=f"{percent}%")

    # 更新UI
    ui_instance.root.update_idletasks()
