"""
今日头条平台视频上传器 - 负责处理头条号平台的视频上传和存稿
"""

import os
import time
import traceback
from typing import Optional, Callable

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, ElementClickInterceptedException
from selenium.webdriver.common.action_chains import ActionChains

# 头条号平台XPath常量 - 与其他平台完全隔离
class ToutiaoXPaths:
    """头条号平台专用XPath选择器"""
    
    # 视频管理页面导航
    VIDEO_MENU = '//*[@id="masterRoot"]/div/div[3]/section/aside/div/div/div/div[2]/div[2]/div[2]/span'
    
    # 视频上传
    UPLOAD_VIDEO_BUTTON = '//*[@id="root"]/div/div/div[2]/div/div[1]/div/div/div/div[2]/div/div/div/div/div/div'
    
    # 视频上传状态监控
    UPLOAD_STATUS = '//*[@id="root"]/div/div/div[2]/div/div[1]/div/div/div/div[2]/div/div[1]/div[2]/div[1]/span[1]'
    
    # 封面上传
    UPLOAD_COVER_BUTTON = '//*[@id="root"]/div/div/div[2]/div/div[1]/div/div/div/div[2]/div/div[2]/div[1]/div[2]/div[4]/div[2]/div[1]/div/div/div/div/div'
    COVER_CONFIRM_1 = '//*[@id="tc-ie-base-content"]/div[2]/div[2]/div[3]/div[3]/button[2]'
    
    # 草稿保存
    SAVE_DRAFT_BUTTON = '//*[@id="root"]/div/div/div[2]/div/div[1]/div/div/div/div[2]/div/div[2]/div[3]/div/button[1]'


class ToutiaoVideoUploader:
    """头条号平台视频上传器"""

    def __init__(self, driver: webdriver.Chrome, log_callback: Callable = None,
                 headless_mode: bool = False, screenshots_dir: str = None):
        """
        初始化头条号视频上传器

        Args:
            driver: 浏览器驱动
            log_callback: 日志回调函数
            headless_mode: 是否为无头模式
            screenshots_dir: 截图保存目录
        """
        self.driver = driver
        self.log_callback = log_callback
        self.headless_mode = headless_mode
        self.screenshots_dir = screenshots_dir
        self.last_error = ""
        self.last_screenshot = ""

        # 重试配置
        self.max_retries = 3
        self.retry_delay = 2  # 重试间隔（秒）

    def _retry_on_failure(self, func, *args, **kwargs):
        """
        重试装饰器，在失败时自动重试

        Args:
            func: 要执行的函数
            *args: 函数参数
            **kwargs: 函数关键字参数

        Returns:
            函数执行结果
        """
        last_exception = None

        for attempt in range(self.max_retries):
            try:
                result = func(*args, **kwargs)
                if result:  # 如果成功，直接返回
                    return result
                else:
                    self.log(f"⚠️ 第{attempt + 1}次尝试失败，{self.max_retries - attempt - 1}次重试机会剩余")

            except Exception as e:
                last_exception = e
                self.log(f"⚠️ 第{attempt + 1}次尝试异常: {str(e)}")

            # 如果不是最后一次尝试，等待后重试
            if attempt < self.max_retries - 1:
                self.log(f"⏳ 等待{self.retry_delay}秒后重试...")
                time.sleep(self.retry_delay)

        # 所有重试都失败了
        if last_exception:
            self.log(f"❌ 所有{self.max_retries}次尝试都失败了，最后异常: {str(last_exception)}")
        else:
            self.log(f"❌ 所有{self.max_retries}次尝试都失败了")

        return False

    def log(self, message: str) -> None:
        """
        记录日志

        Args:
            message: 日志消息
        """
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def draft_video(self, video_path: str, cover_path: Optional[str] = None) -> bool:
        """
        上传视频并存稿到头条号平台

        Args:
            video_path: 视频文件路径
            cover_path: 封面文件路径

        Returns:
            是否成功存稿
        """
        try:
            self.log("🎬 开始头条号视频存稿流程")

            # 1. 进入视频管理页面（点击"视频"菜单项）
            if not self._navigate_to_video_management():
                self.log("❌ 进入视频管理页面失败")
                return False

            # 2. 上传视频文件（带重试）
            if not self._retry_on_failure(self._upload_video_file, video_path):
                self.log("❌ 上传视频文件失败（已重试）")
                return False

            # 3. 监控视频上传状态（带重试）
            if not self._retry_on_failure(self._wait_for_video_upload):
                self.log("❌ 等待视频上传超时（已重试）")
                return False

            # 4. 上传视频封面（如果有）
            if cover_path and os.path.exists(cover_path):
                # 封面上传失败不影响整体流程，所以不使用重试
                if not self._upload_video_cover(cover_path):
                    self.log("⚠️ 上传封面失败，继续存稿流程")
            else:
                self.log("📷 未提供封面文件，跳过封面上传")

            # 5. 保存草稿（带重试）
            if not self._retry_on_failure(self._save_draft):
                self.log("❌ 保存草稿失败（已重试）")
                return False

            self.log("✅ 头条号视频存稿成功！")
            return True

        except Exception as e:
            self.log(f"❌ 头条号存稿过程中发生错误: {str(e)}")
            traceback.print_exc()
            return False

    def _navigate_to_upload_page(self) -> bool:
        """
        导航到头条号视频上传页面

        Returns:
            是否成功导航
        """
        try:
            # 头条号视频上传页面URL
            upload_url = "https://mp.toutiao.com/profile_v4/xigua/upload-video"

            self.log(f"🌐 正在导航到上传页面: {upload_url}")
            self.driver.get(upload_url)

            # 等待页面加载
            time.sleep(3)

            # 验证页面是否正确加载
            current_url = self.driver.current_url
            if "upload-video" in current_url:
                self.log("✅ 成功导航到视频上传页面")
                return True
            else:
                self.log(f"⚠️ 当前页面URL不符合预期: {current_url}")
                return False

        except Exception as e:
            self.log(f"❌ 导航到上传页面失败: {str(e)}")
            return False

    def _navigate_to_video_management(self) -> bool:
        """
        进入视频管理页面并导航到上传页面

        Returns:
            是否成功进入
        """
        try:
            self.log("🔍 正在定位视频菜单...")

            # 等待并点击视频菜单项
            video_menu = WebDriverWait(self.driver, 15).until(
                EC.element_to_be_clickable((By.XPATH, ToutiaoXPaths.VIDEO_MENU))
            )

            # 使用JavaScript点击，避免元素被遮挡
            self.driver.execute_script("arguments[0].click();", video_menu)
            self.log("✅ 已点击视频菜单")

            # 等待页面加载
            time.sleep(3)

            # 检查是否需要导航到上传页面
            current_url = self.driver.current_url
            if "upload-video" not in current_url:
                self.log("🌐 当前不在上传页面，导航到视频上传页面...")
                upload_url = "https://mp.toutiao.com/profile_v4/xigua/upload-video"
                self.driver.get(upload_url)
                time.sleep(3)
                self.log("✅ 已导航到视频上传页面")
            else:
                self.log("✅ 已在视频上传页面")

            return True
            
        except TimeoutException:
            self.log("❌ 视频菜单定位超时")
            return False
        except Exception as e:
            self.log(f"❌ 进入视频管理页面失败: {str(e)}")
            return False

    def _upload_video_file(self, video_path: str) -> bool:
        """
        上传视频文件

        Args:
            video_path: 视频文件路径

        Returns:
            是否成功上传
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(video_path):
                self.log(f"❌ 视频文件不存在: {video_path}")
                return False

            # 确保文件路径是绝对路径
            video_path = os.path.abspath(video_path)
            self.log(f"📁 准备上传视频: {os.path.basename(video_path)}")

            # 等待并点击上传视频文件按钮
            upload_button = WebDriverWait(self.driver, 15).until(
                EC.element_to_be_clickable((By.XPATH, ToutiaoXPaths.UPLOAD_VIDEO_BUTTON))
            )
            
            self.log("🔍 找到上传按钮，准备点击...")
            
            # 使用JavaScript点击
            self.driver.execute_script("arguments[0].click();", upload_button)
            self.log("✅ 已点击上传视频按钮")
            
            # 等待文件选择对话框出现
            time.sleep(2)
            
            # 查找文件输入元素
            try:
                file_input = self.driver.find_element(By.XPATH, "//input[@type='file']")
                file_input.send_keys(video_path)
                self.log("✅ 视频文件已选择并开始上传")
                return True
                
            except NoSuchElementException:
                self.log("❌ 未找到文件输入元素")
                return False
            
        except TimeoutException:
            self.log("❌ 上传按钮定位超时")
            return False
        except Exception as e:
            self.log(f"❌ 上传视频文件失败: {str(e)}")
            return False

    def _wait_for_video_upload(self, timeout: int = 300) -> bool:
        """
        监控视频上传状态

        Args:
            timeout: 超时时间（秒）

        Returns:
            是否上传成功
        """
        try:
            self.log("⏳ 开始监控视频上传状态...")
            
            start_time = time.time()
            check_interval = 5  # 每5秒检查一次
            
            while time.time() - start_time < timeout:
                try:
                    # 获取上传状态元素
                    status_element = WebDriverWait(self.driver, 10).until(
                        EC.presence_of_element_located((By.XPATH, ToutiaoXPaths.UPLOAD_STATUS))
                    )
                    
                    status_text = status_element.text.strip()
                    self.log(f"📊 当前上传状态: {status_text}")
                    
                    # 检查上传状态
                    if "上传成功" in status_text:
                        self.log("✅ 视频上传成功！")
                        return True
                    elif "上传失败" in status_text or "失败" in status_text:
                        self.log("❌ 视频上传失败")
                        return False
                    elif "上传中" in status_text:
                        self.log("⏳ 视频正在上传中...")
                        time.sleep(check_interval)
                        continue
                    else:
                        # 其他状态，继续等待
                        time.sleep(check_interval)
                        continue
                        
                except TimeoutException:
                    self.log("⚠️ 状态元素暂时不可见，继续等待...")
                    time.sleep(check_interval)
                    continue
                    
            self.log("❌ 视频上传监控超时")
            return False
            
        except Exception as e:
            self.log(f"❌ 监控视频上传状态失败: {str(e)}")
            return False

    def _upload_video_cover(self, cover_path: str) -> bool:
        """
        上传视频封面

        Args:
            cover_path: 封面文件路径

        Returns:
            是否成功上传
        """
        try:
            self.log("📷 开始上传视频封面...")
            
            # 1. 点击上传封面按钮
            cover_button = WebDriverWait(self.driver, 15).until(
                EC.element_to_be_clickable((By.XPATH, ToutiaoXPaths.UPLOAD_COVER_BUTTON))
            )
            self.driver.execute_script("arguments[0].click();", cover_button)
            self.log("✅ 已点击上传封面按钮")
            
            # 等待弹窗出现
            time.sleep(2)
            
            # 2. 点击本地上传选项（通过文字查找）
            local_upload = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//*[contains(text(), '本地上传')]"))
            )
            self.driver.execute_script("arguments[0].click();", local_upload)
            self.log("✅ 已选择本地上传")
            
            # 等待文件选择界面
            time.sleep(1)
            
            # 3. 上传封面文件（查找所有文件输入框，选择最后一个作为封面输入框）
            try:
                self.log("🔍 查找文件输入框...")
                file_inputs = self.driver.find_elements(By.XPATH, "//input[@type='file']")
                if len(file_inputs) > 1:
                    cover_input = file_inputs[-1]  # 选择最后一个，通常是封面输入框
                    self.log(f"✅ 找到 {len(file_inputs)} 个文件输入框，使用最后一个作为封面输入框")
                elif len(file_inputs) == 1:
                    cover_input = file_inputs[0]
                    self.log("✅ 找到唯一的文件输入框，用作封面输入框")
                else:
                    self.log("❌ 未找到任何文件输入框")
                    return False

                # 上传封面文件
                cover_input.send_keys(os.path.abspath(cover_path))
                self.log("✅ 封面文件已选择")

            except Exception as e:
                self.log(f"❌ 封面文件上传失败: {str(e)}")
                return False
            
            # 等待封面上传完成（使用canvas检测）
            if not self._wait_for_canvas_loaded():
                self.log("⚠️ 封面canvas可能未完全加载，但继续后续操作")
                time.sleep(3)  # 额外等待时间

            # 4. 确认封面上传 - 第一个确定按钮
            confirm1 = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, ToutiaoXPaths.COVER_CONFIRM_1))
            )
            self.driver.execute_script("arguments[0].click();", confirm1)
            self.log("✅ 已点击第一个确定按钮")
            
            # 等待第二个确认对话框
            time.sleep(2)
            
            # 5. 确认封面上传 - 第二个确定按钮（通过文字查找）
            try:
                # 通过文字查找确定按钮
                confirm_buttons = self.driver.find_elements(By.XPATH, "//*[contains(text(), '确定') or contains(text(), '确认')]")
                if confirm_buttons:
                    # 选择最后一个确定按钮（通常是第二个确认对话框的按钮）
                    confirm2 = confirm_buttons[-1]
                    self.driver.execute_script("arguments[0].click();", confirm2)
                    self.log("✅ 已点击第二个确定按钮（通过文字查找）")

                    # 等待弹窗消失 - 监控第一个确定按钮状态变化
                    self._wait_for_upload_dialog_close()

                else:
                    self.log("⚠️ 未找到确定按钮，可能已经自动确认")
            except Exception as e:
                self.log(f"⚠️ 点击第二个确定按钮失败: {str(e)}")
                # 不返回False，因为封面上传可能已经成功
            
            # 等待封面上传完成
            time.sleep(2)
            
            self.log("✅ 视频封面上传完成")
            return True
            
        except TimeoutException:
            self.log("❌ 封面上传过程中元素定位超时")
            return False
        except Exception as e:
            self.log(f"❌ 上传视频封面失败: {str(e)}")
            return False

    def _save_draft(self) -> bool:
        """
        保存草稿

        Returns:
            是否成功保存
        """
        try:
            self.log("💾 开始保存草稿...")
            
            # 点击存草稿按钮
            save_button = WebDriverWait(self.driver, 15).until(
                EC.element_to_be_clickable((By.XPATH, ToutiaoXPaths.SAVE_DRAFT_BUTTON))
            )
            self.driver.execute_script("arguments[0].click();", save_button)
            self.log("✅ 已点击存草稿按钮")
            
            # 等待保存处理
            time.sleep(3)
            
            # 验证保存结果 - 检测是否出现"全部保存成功"提示
            success_detected = False
            
            # 等待并检查保存结果
            for i in range(10):  # 最多等待10秒
                try:
                    # 检查页面中是否包含成功提示
                    page_source = self.driver.page_source
                    if "全部保存成功" in page_source or "保存成功" in page_source or "存稿成功" in page_source:
                        success_detected = True
                        break
                    
                    # 也可以通过查找特定元素来确认
                    success_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '全部保存成功') or contains(text(), '保存成功') or contains(text(), '存稿成功')]")
                    if success_elements:
                        success_detected = True
                        break
                        
                    time.sleep(1)
                    
                except Exception:
                    time.sleep(1)
                    continue
            
            if success_detected:
                self.log("✅ 草稿保存成功！检测到'全部保存成功'提示")
                return True
            else:
                self.log("⚠️ 未检测到明确的保存成功提示，但操作已完成")
                # 即使没有检测到明确提示，也认为保存成功
                return True
                
        except TimeoutException:
            self.log("❌ 存草稿按钮定位超时")
            return False
        except Exception as e:
            self.log(f"❌ 保存草稿失败: {str(e)}")
            return False

    def _wait_for_canvas_loaded(self, timeout: int = 15) -> bool:
        """
        等待canvas元素加载完成（头条号专用）

        Args:
            timeout: 超时时间（秒）

        Returns:
            是否检测到canvas加载完成
        """
        try:
            self.log("🔍 开始检测封面canvas加载状态...")

            start_time = time.time()
            check_interval = 1  # 每1秒检查一次

            while time.time() - start_time < timeout:
                try:
                    # 检查canvas元素是否存在并已绘制
                    canvas_loaded = self._check_canvas_status()

                    if canvas_loaded:
                        self.log("✅ 封面canvas已加载完成")
                        return True

                    self.log(f"⏳ canvas尚未完全加载，继续等待...")
                    time.sleep(check_interval)

                except Exception as e:
                    self.log(f"⚠️ 检测canvas状态时出错: {str(e)}")
                    time.sleep(check_interval)
                    continue

            self.log(f"❌ canvas加载检测超时（{timeout}秒）")
            return False

        except Exception as e:
            self.log(f"❌ 等待canvas加载时发生错误: {str(e)}")
            return False

    def _check_canvas_status(self) -> bool:
        """
        检查canvas元素的状态

        Returns:
            canvas是否已准备就绪
        """
        try:
            # JavaScript代码检查canvas状态
            js_check_canvas = """
            function checkCanvasStatus() {
                // 查找目标canvas元素
                const targetCanvas = document.querySelector('#tc-ie-base-content canvas[class*="canvas"]');

                if (!targetCanvas) {
                    // 如果找不到特定canvas，查找所有canvas
                    const allCanvas = document.querySelectorAll('canvas');
                    if (allCanvas.length === 0) {
                        return { found: false, reason: 'no_canvas_found' };
                    }

                    // 检查是否有canvas有内容
                    for (const canvas of allCanvas) {
                        if (canvas.width > 0 && canvas.height > 0) {
                            const ctx = canvas.getContext('2d');
                            if (ctx) {
                                // 检查canvas是否有绘制内容
                                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                                const data = imageData.data;

                                // 检查是否有非透明像素
                                for (let i = 3; i < data.length; i += 4) {
                                    if (data[i] > 0) { // alpha通道大于0表示有内容
                                        return {
                                            found: true,
                                            loaded: true,
                                            width: canvas.width,
                                            height: canvas.height,
                                            hasContent: true
                                        };
                                    }
                                }
                            }
                        }
                    }

                    return { found: true, loaded: false, reason: 'canvas_empty' };
                }

                // 检查目标canvas
                if (targetCanvas.width > 0 && targetCanvas.height > 0) {
                    const ctx = targetCanvas.getContext('2d');
                    if (ctx) {
                        try {
                            const imageData = ctx.getImageData(0, 0, targetCanvas.width, targetCanvas.height);
                            const data = imageData.data;

                            // 检查是否有非透明像素
                            for (let i = 3; i < data.length; i += 4) {
                                if (data[i] > 0) {
                                    return {
                                        found: true,
                                        loaded: true,
                                        width: targetCanvas.width,
                                        height: targetCanvas.height,
                                        hasContent: true
                                    };
                                }
                            }
                        } catch (e) {
                            // 如果无法读取canvas内容（可能是跨域），认为已加载
                            return {
                                found: true,
                                loaded: true,
                                width: targetCanvas.width,
                                height: targetCanvas.height,
                                crossOrigin: true
                            };
                        }
                    }
                }

                return { found: true, loaded: false, reason: 'canvas_not_ready' };
            }

            return checkCanvasStatus();
            """

            result = self.driver.execute_script(js_check_canvas)

            if result.get('found'):
                if result.get('loaded'):
                    if result.get('hasContent'):
                        self.log(f"✅ Canvas已加载并有内容 ({result.get('width')}x{result.get('height')})")
                    elif result.get('crossOrigin'):
                        self.log(f"✅ Canvas已加载（跨域限制无法检查内容）")
                    else:
                        self.log("⚠️ Canvas已加载但可能为空")
                    return True
                else:
                    reason = result.get('reason', 'unknown')
                    self.log(f"⏳ Canvas未就绪: {reason}")
                    return False
            else:
                self.log("❌ 未找到Canvas元素")
                return False

        except Exception as e:
            self.log(f"❌ 检查canvas状态失败: {str(e)}")
            return False

    def _wait_for_upload_dialog_close(self, timeout: int = 30) -> bool:
        """
        等待上传弹窗消失
        通过监控第一个确定按钮的状态变化来判断：
        1. 点击第二个确定后，第一个确定按钮会变成"上传中"
        2. 当"上传中"消失时，代表弹窗已经消失

        Args:
            timeout: 超时时间（秒）

        Returns:
            是否弹窗已消失
        """
        try:
            start_time = time.time()
            upload_started = False

            self.log("⏳ 等待上传弹窗消失...")

            while time.time() - start_time < timeout:
                try:
                    # 查找包含"上传中"文字的元素
                    uploading_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '上传中')]")

                    if uploading_elements and not upload_started:
                        upload_started = True
                        self.log("⏳ 检测到'上传中'状态，等待上传完成...")
                    elif upload_started and not uploading_elements:
                        self.log("✅ '上传中'状态消失，弹窗已关闭")
                        return True
                    elif not upload_started:
                        # 如果没有检测到"上传中"，可能上传很快完成了，检查弹窗是否还存在
                        confirm_buttons = self.driver.find_elements(By.XPATH, "//*[contains(text(), '确定') or contains(text(), '确认')]")
                        if len(confirm_buttons) < 2:  # 如果确定按钮少于2个，说明弹窗可能已经关闭
                            self.log("✅ 弹窗已关闭（未检测到上传中状态）")
                            return True

                except Exception as e:
                    self.log(f"⏳ 弹窗状态检测异常: {str(e)}")

                time.sleep(0.5)  # 更频繁的检查

            self.log("⚠️ 等待弹窗消失超时")
            return False

        except Exception as e:
            self.log(f"❌ 等待弹窗消失失败: {str(e)}")
            return False
