"""
UI主题模块 - 包含主题和样式相关代码
"""

import tkinter as tk
from tkinter import ttk, font


def configure_fonts(self):
    """配置字体大小和字体族 - 完全匹配Cursor的字体"""
    # Cursor使用的主要字体是JetBrains Mono或Menlo
    # 定义首选字体和备选字体列表
    cursor_fonts = ["JetBrains Mono", "Menlo", "SF Mono", "Consolas", "Roboto Mono", "Source Code Pro", "Monaco", "Courier New"]

    # 检查系统中是否有Cursor使用的字体
    available_fonts = list(font.families())
    font_family = "Courier New"  # 默认备选

    for cursor_font in cursor_fonts:
        if cursor_font in available_fonts:
            font_family = cursor_font
            break

    # 使用配置的字体大小，而不是硬编码
    current_font_size = self.default_font_size if hasattr(self, 'default_font_size') else 9

    # 设置默认字体
    default_font = font.nametofont("TkDefaultFont")
    default_font.configure(family=font_family, size=current_font_size, weight="normal")
    self.root.option_add("*Font", default_font)

    # 设置文本字体 - 使用等宽字体提高可读性
    text_font = font.nametofont("TkTextFont")
    text_font.configure(family=font_family, size=current_font_size, weight="normal")

    # 设置固定宽度字体 - 用于代码和日志显示
    fixed_font = font.nametofont("TkFixedFont")
    fixed_font.configure(family=font_family, size=current_font_size, weight="normal")

    # 其他字体设置
    self.root.option_add("*TCombobox*Listbox.Font", default_font)

    # 创建粗体版本的字体用于标题
    self.bold_font = font.Font(family=font_family, size=current_font_size, weight="bold")

    # 记录使用的字体
    print(f"使用Cursor风格字体: {font_family}, 当前字体大小: {current_font_size}")

    # 返回选择的字体族，以便其他地方使用
    return font_family


def increase_font_size(self):
    """增加字体大小"""
    if self.default_font_size < 24:
        self.default_font_size += 1
        self.font_size_var.set(self.default_font_size)
        self.configure_fonts()
        self.log(f"字体大小已增加到: {self.default_font_size}")
        self.config_manager.save_config(show_message=False)


def decrease_font_size(self):
    """减小字体大小"""
    if self.default_font_size > 8:
        self.default_font_size -= 1
        self.font_size_var.set(self.default_font_size)
        self.configure_fonts()
        self.log(f"字体大小已减小到: {self.default_font_size}")
        self.config_manager.save_config(show_message=False)


def _update_font_size(self):
    """从字体大小变量更新字体大小"""
    try:
        new_size = int(self.font_size_var.get())
        if 8 <= new_size <= 24:
            self.default_font_size = new_size
            self.configure_fonts()
            if hasattr(self, 'font_size_label'):
                self.font_size_label.config(text=f"当前大小: {new_size}")
            self.log(f"字体大小已更新为: {new_size}")
            self.config_manager.save_config(show_message=False)
        else:
            # 如果值超出范围，重置为默认值
            self.default_font_size = 9
            self.font_size_var.set(self.default_font_size)
            if hasattr(self, 'font_size_label'):
                self.font_size_label.config(text=f"当前大小: {self.default_font_size}")
            self.log(f"⚠️ 字体大小超出范围，已重置为默认值: {self.default_font_size}")
            self.config_manager.save_config(show_message=False)
    except (ValueError, TypeError) as e:
        # 如果转换失败，重置为默认值
        self.default_font_size = 9
        self.font_size_var.set(self.default_font_size)
        if hasattr(self, 'font_size_label'):
            self.font_size_label.config(text=f"当前大小: {self.default_font_size}")
        self.log(f"⚠️ 字体大小设置无效，已重置为默认值: {str(e)}")
        self.config_manager.save_config(show_message=False)


def toggle_dark_mode(self):
    """切换黑暗模式"""
    # 切换黑暗模式状态
    new_state = not self.enable_dark_mode.get()
    self.enable_dark_mode.set(new_state)

    # 应用主题
    self.apply_theme()

    # 记录日志
    mode_name = "黑暗模式" if new_state else "浅色模式"
    self.log(f"已切换到{mode_name}")

    # 保存配置
    self.config_manager.save_config(show_message=False)


def apply_theme(self):
    """应用当前主题 - 现代化设计风格"""
    # 获取当前主题
    theme_key = "dark" if self.enable_dark_mode.get() else "light"
    theme = self.theme_colors[theme_key]

    # 创建或获取ttk样式
    style = ttk.Style()

    # 尝试设置主题基础
    try:
        if theme_key == "dark":
            style.theme_use("clam")  # 使用clam主题作为黑暗模式的基础
        else:
            style.theme_use("default")  # 使用默认主题作为浅色模式的基础
    except:
        pass  # 如果主题不可用，忽略错误

    # 配置全局颜色
    style.configure(".",
                   background=theme["bg"],
                   foreground=theme["fg"],
                   troughcolor=theme["border"],
                   selectbackground=theme["highlight_bg"],
                   selectforeground=theme["highlight_fg"],
                   fieldbackground=theme["entry_bg"],
                   font=("TkDefaultFont", self.default_font_size))

    # 框架样式 - 添加细微的边框和圆角
    style.configure("TFrame",
                   background=theme["bg"])

    # 标签框架样式 - 添加更明显的边框和标题
    style.configure("TLabelframe",
                   background=theme["bg"],
                   foreground=theme["fg"],
                   borderwidth=1,
                   relief="solid")
    style.configure("TLabelframe.Label",
                   background=theme["bg"],
                   foreground=theme["fg"],
                   font=("TkDefaultFont", self.default_font_size, "bold"))

    # 标签样式 - 确保文本清晰可见
    style.configure("TLabel",
                   background=theme["bg"],
                   foreground=theme["fg"])

    # 按钮样式 - 现代风格按钮，添加圆角和悬停效果
    style.configure("TButton",
                   background=theme["button_bg"],
                   foreground=theme["fg"],
                   borderwidth=1,
                   relief="flat",
                   padding=(10, 5))
    style.map("TButton",
             background=[("active", theme["button_hover"]),
                        ("pressed", theme["button_active"])],
             foreground=[("active", theme["fg"]),
                        ("pressed", theme["fg"])])

    # 复选框和单选按钮 - 现代风格
    style.configure("TCheckbutton",
                   background=theme["bg"],
                   foreground=theme["fg"])
    style.map("TCheckbutton",
             background=[("active", theme["bg"])],
             foreground=[("active", theme["fg"])])

    style.configure("TRadiobutton",
                   background=theme["bg"],
                   foreground=theme["fg"])
    style.map("TRadiobutton",
             background=[("active", theme["bg"])],
             foreground=[("active", theme["fg"])])

    # 输入框样式 - 添加内边距和圆角
    style.configure("TEntry",
                   fieldbackground=theme["entry_bg"],
                   foreground=theme["entry_fg"],
                   selectbackground=theme["highlight_bg"],
                   selectforeground=theme["highlight_fg"],
                   borderwidth=1,
                   padding=5)

    # 配置特殊样式 - 更明显的开始按钮，添加渐变效果
    style.configure("Start.TButton",
                   background=theme["highlight_bg"],
                   foreground=theme["highlight_fg"],
                   font=("TkDefaultFont", self.default_font_size + 2, "bold"),
                   borderwidth=1,
                   relief="flat",
                   padding=(15, 8))
    style.map("Start.TButton",
             background=[("active", theme["highlight_bg"]),
                        ("pressed", theme["highlight_bg"])],
             foreground=[("active", theme["highlight_fg"]),
                        ("pressed", theme["highlight_fg"])])

    # 更新所有窗口部件的颜色
    self._update_all_widgets_theme(self.root, theme)

    # 更新日志文本颜色
    if hasattr(self, 'log_text'):
        self.log_text.config(
            bg=theme["log_bg"],
            fg=theme["log_fg"],
            insertbackground=theme["fg"],
            selectbackground=theme["highlight_bg"],
            selectforeground=theme["highlight_fg"]
        )

        # 更新日志文本标签
        self.log_text.tag_configure("success", foreground=theme["success"])
        self.log_text.tag_configure("error", foreground=theme["error"])
        self.log_text.tag_configure("warning", foreground=theme["warning"])
        self.log_text.tag_configure("info", foreground=theme["info"])
        self.log_text.tag_configure("section", foreground=theme["highlight_bg"], font=self.bold_font)

    # 更新颜色预览
    if hasattr(self, 'color_preview_top'):
        self._update_color_preview(self.color_preview_top, self.cover_color_top.get())
    if hasattr(self, 'color_preview_bottom'):
        self._update_color_preview(self.color_preview_bottom, self.cover_color_bottom.get())

    # 更新字体大小预览
    if hasattr(self, 'font_preview_canvas'):
        self._update_font_size_preview()


def _update_all_widgets_theme(self, parent, theme):
    """递归更新所有窗口部件的主题"""
    try:
        # 更新当前窗口部件的颜色
        if isinstance(parent, tk.Widget):
            # 根据窗口部件类型设置颜色
            if isinstance(parent, (tk.Frame, ttk.Frame, ttk.LabelFrame)):
                parent.configure(background=theme["bg"])
            elif isinstance(parent, tk.Label):
                parent.configure(background=theme["bg"], foreground=theme["fg"])
            elif isinstance(parent, tk.Entry):
                parent.configure(background=theme["entry_bg"], foreground=theme["entry_fg"],
                               insertbackground=theme["fg"],
                               selectbackground=theme["highlight_bg"],
                               selectforeground=theme["highlight_fg"])
            elif isinstance(parent, tk.Text):
                parent.configure(background=theme["log_bg"], foreground=theme["log_fg"],
                               insertbackground=theme["fg"],
                               selectbackground=theme["highlight_bg"],
                               selectforeground=theme["highlight_fg"])
            elif isinstance(parent, tk.Canvas):
                # 检查是否是颜色预览画布
                is_color_preview = False
                for attr_name in ['color_preview_top', 'color_preview_bottom']:
                    if hasattr(self, attr_name) and parent == getattr(self, attr_name):
                        is_color_preview = True
                        break

                # 如果不是颜色预览画布，则更新背景色
                if not is_color_preview:
                    parent.configure(background=theme["canvas_bg"])

            # 对于ttk部件，不需要手动设置颜色，因为它们会使用ttk样式

        # 递归处理所有子窗口部件
        for child in parent.winfo_children():
            self._update_all_widgets_theme(child, theme)
    except Exception as e:
        print(f"更新窗口部件主题时出错: {str(e)}")
