"""
常量定义模块 - 包含所有URL和XPath常量
"""

import os

# 账号状态常量定义 - 确保跨平台一致性
class AccountStatus:
    """账号状态常量 - 遵循MECE原则"""

    # 正常状态
    NORMAL = "正常"
    SUCCESS = "成功"
    QUERIED = "已查询"

    # 失败状态 - 按失败类型分类
    # 认证相关失败
    LOGIN_FAILED = "登录失败"
    COOKIE_NOT_FOUND = "Cookie文件不存在"
    COOKIE_EXPIRED = "Cookie已过期"
    SESSION_EXPIRED = "会话过期"

    # 查询相关失败
    QUERY_FAILED = "查询失败"
    QUERY_TIMEOUT = "查询超时"
    QUERY_EXCEPTION = "查询异常"
    THREAD_EXCEPTION = "线程异常"

    # 网络相关失败
    NETWORK_ERROR = "网络错误"
    CONNECTION_TIMEOUT = "连接超时"

    # 数据相关失败
    DATA_ERROR = "数据错误"
    NO_RESULT = "查询无结果"

    # 系统相关失败
    SYSTEM_ERROR = "系统错误"
    UNKNOWN_ERROR = "未知错误"

    # 默认状态
    UNKNOWN = "未知"
    NOT_QUERIED = "未查询"

# 状态颜色映射
STATUS_COLORS = {
    # 正常状态 - 绿色系
    AccountStatus.NORMAL: "#28A745",
    AccountStatus.SUCCESS: "#28A745",
    AccountStatus.QUERIED: "#28A745",

    # 失败状态 - 红色系
    AccountStatus.LOGIN_FAILED: "#DC3545",
    AccountStatus.COOKIE_NOT_FOUND: "#DC3545",
    AccountStatus.COOKIE_EXPIRED: "#DC3545",
    AccountStatus.SESSION_EXPIRED: "#DC3545",
    AccountStatus.QUERY_FAILED: "#DC3545",
    AccountStatus.QUERY_TIMEOUT: "#DC3545",
    AccountStatus.QUERY_EXCEPTION: "#DC3545",
    AccountStatus.THREAD_EXCEPTION: "#DC3545",
    AccountStatus.NETWORK_ERROR: "#DC3545",
    AccountStatus.CONNECTION_TIMEOUT: "#DC3545",
    AccountStatus.DATA_ERROR: "#DC3545",
    AccountStatus.NO_RESULT: "#DC3545",
    AccountStatus.SYSTEM_ERROR: "#DC3545",
    AccountStatus.UNKNOWN_ERROR: "#DC3545",

    # 默认状态 - 灰色系
    AccountStatus.UNKNOWN: "#6C757D",
    AccountStatus.NOT_QUERIED: "#6C757D"
}

# 状态图标映射 - 使用实心圆点指示灯
STATUS_ICONS = {
    # 正常状态 - 绿色圆点
    AccountStatus.NORMAL: "●",
    AccountStatus.SUCCESS: "●",
    AccountStatus.QUERIED: "●",

    # 失败状态 - 红色圆点
    AccountStatus.LOGIN_FAILED: "●",
    AccountStatus.COOKIE_NOT_FOUND: "●",
    AccountStatus.COOKIE_EXPIRED: "●",
    AccountStatus.SESSION_EXPIRED: "●",
    AccountStatus.QUERY_FAILED: "●",
    AccountStatus.QUERY_TIMEOUT: "●",
    AccountStatus.QUERY_EXCEPTION: "●",
    AccountStatus.THREAD_EXCEPTION: "●",
    AccountStatus.NETWORK_ERROR: "●",
    AccountStatus.CONNECTION_TIMEOUT: "●",
    AccountStatus.DATA_ERROR: "●",
    AccountStatus.NO_RESULT: "●",
    AccountStatus.SYSTEM_ERROR: "●",
    AccountStatus.UNKNOWN_ERROR: "●",

    # 默认状态 - 灰色圆点
    AccountStatus.UNKNOWN: "●",
    AccountStatus.NOT_QUERIED: "●"
}

# 平台目录结构配置
PLATFORM_ROOT = "D:\\网易号全自动"

# 共用目录路径
SHARED_DIRECTORIES = {
    "screenshots": os.path.join(PLATFORM_ROOT, "截图"),
    "data": os.path.join(PLATFORM_ROOT, "数据"),
    "violations": os.path.join(PLATFORM_ROOT, "违规"),
    "pending": os.path.join(PLATFORM_ROOT, "未处理"),
    "processed": os.path.join(PLATFORM_ROOT, "已处理"),
    "completed": os.path.join(PLATFORM_ROOT, "已完成")
}

# 账号目录路径
ACCOUNT_DIRECTORIES = {
    "netease": os.path.join(PLATFORM_ROOT, "网易号账号"),
    "toutiao": os.path.join(PLATFORM_ROOT, "头条号账号"),
    "dayu": os.path.join(PLATFORM_ROOT, "大鱼号账号")
}

# 子目录配置
SUBDIRECTORIES = {
    "screenshots": {
        "operation": "操作截图",
        "error": "错误截图",
        "success": "成功截图",
        "debug": "调试截图"
    },
    "data": {
        "config": "配置",
        "logs": "日志",
        "cache": "缓存",
        "statistics": "统计"
    },
    "violations": {
        "content": "违规内容",
        "records": "处理记录",
        "appeals": "申诉材料"
    },
    "pending": {
        "articles": "待发布文章",
        "videos": "待上传视频",
        "images": "待处理图片",
        "tasks": "待执行任务"
    },
    "processed": {
        "articles": "已编辑文章",
        "videos": "已处理视频",
        "images": "已优化图片",
        "tasks": "处理中任务"
    },
    "completed": {
        "published": "已发布内容",
        "uploaded": "已上传内容",
        "finished": "完成任务",
        "archive": "历史归档"
    },
    "accounts": {
        "accounts": "账号信息",
        "publish_history": "发布记录",
        "statistics": "数据统计",
        "settings": "账号设置"
    }
}

# 网易号平台URL
NETEASE_PUBLISH_URL = "https://mp.163.com/subscribe_v4/index.html#/video-publish"
VIDEO_UPLOAD_URL = "https://mp.163.com/article/videoedit"  # 视频上传页面URL
NETEASE_BACKEND_URL = "https://mp.163.com/subscribe_v4/index.html"  # 网易号后台首页URL

# 网易号数据统计页面URL
NETEASE_HOME_URL = "https://mp.163.com/subscribe_v4/index.html#/home"  # 首页数据
NETEASE_CONTENT_URL = "https://mp.163.com/subscribe_v4/index.html#/content-data"  # 内容数据
NETEASE_SUBSCRIBE_URL = "https://mp.163.com/subscribe_v4/index.html#/subscribe-data"  # 订阅数据
NETEASE_PROFIT_URL = "https://mp.163.com/subscribe_v4/index.html#/profit-data"  # 收益数据

# XPath 定义
VIDEO_UPLOAD_AREA = '//*[@id="root"]/div/div[2]/div/div[1]/div/div/div[2]/div'
VIDEO_UPLOAD_SUCCESS = '//*[@id="root"]/div/div[2]/div/div[2]/div/div/div[1]/div/div[2]/div[1]/div[2]/div[2]/ul/li[4]'
# 新的视频上传成功检测XPath - 检测"推荐封面"文本
VIDEO_UPLOAD_SUCCESS_NEW = '//*[@id="root"]/div/div[2]/div/div[2]/div/div/div[1]/div/div[2]/div[1]/div[2]/div[2]/div'
UPLOAD_COVER_BUTTON = '//*[@id="root"]/div/div[2]/div/div[2]/div/div/div[1]/div/div[2]/div[1]/div[2]/div[1]'
LOCAL_UPLOAD_BUTTON = '/html/body/div[9]/div[3]/div/div[1]/div/div[1]/div/div/div[3]/div'
COVER_UPLOAD_INPUT = '//*[@id="cropper-input"]'
COVER_CONFIRM_BUTTON = '//*[contains(text(), "确定") or contains(text(), "确认")]'
TITLE_INPUT = '//*[@id="easy-form-title"]'
DECLARATION_BUTTON = '//*[@id="root"]/div/div[2]/div/div[2]/div/div/div[2]/div[2]/div[2]/div[1]/div/div/button'
DECLARATION_CONTENT = '//*[@id="root"]/div/div[2]/div/div[2]/div/div/div[2]/div[2]/div[2]/div[1]/div[1]/span[1]/div/div/div'
INTERNET_SOURCE = '/html/body/div[11]/div/div[2]/div/ul/li[3]/div'  # 取材网络选项，注意此XPath可能因网站更新而变化
CATEGORY_INPUT = '//*[@id="root"]/div/div[2]/div/div[2]/div/div/div[2]/div[3]/div[2]/div[1]/span/div/input' # 分类输入框/点击框
CATEGORY_FIRST_LEVEL = '/html/body/div[7]/div/div[2]/div/div/ul[1]/li[1]' # 第一级分类第一个选项
CATEGORY_SECOND_LEVEL = '/html/body/div[7]/div/div[2]/div/div/ul[2]/li[1]' # 第二级分类第一个选项
TAG_INPUT = '//*[@id="root"]/div/div[2]/div/div[2]/div/div/div[2]/div[4]/div[2]/div[1]/div[1]/div[1]/span'
TAG_CONTAINER = '//*[@id="root"]/div/div[2]/div/div[2]/div/div/div[2]/div[4]/div[2]/div[1]/div[1]/div[1]/span'  # 标签容器元素，用于检测标签是否成功设置
SAVE_DRAFT_BUTTON = '//button[contains(text(), "存草稿") or contains(text(), "保存草稿")]'  # 存草稿按钮

# 网易号数据统计元素 - 首页数据
NETEASE_USERNAME = '//*[@id="root"]/div/div[2]/div[2]/div[2]/div[2]/div[1]/div[1]/div[2]/div/div'  # 用户名
NETEASE_TOTAL_INCOME = '//*[@id="root"]/div/div[2]/div[2]/div[2]/div[1]/div[1]/div[1]/div[3]/div[1]/span'  # 总收益
NETEASE_TOTAL_PLAY = '//*[@id="root"]/div/div[2]/div[2]/div[2]/div[1]/div[1]/div[1]/div[2]/div[1]/span'  # 总阅读/播放
NETEASE_TOTAL_FANS = '//*[@id="root"]/div/div[2]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/span'  # 总粉丝

# 网易号数据统计元素 - 内容数据
NETEASE_YESTERDAY_PLAY = '//*[@id="root"]/div/div[2]/div[2]/div[3]/div[1]/div[2]/div[2]'  # 昨日播放
NETEASE_YESTERDAY_PLAY_TAB = '//*[@id="root"]/div/div[2]/div[2]/div[1]/div/div/div[2]/div'  # 昨日播放标签

# 网易号数据统计元素 - 订阅数据
NETEASE_YESTERDAY_FANS = '//*[@id="root"]/div/div[2]/div[2]/div[1]/div[2]/div[2]'  # 昨日粉丝

# 网易号数据统计元素 - 收益数据
NETEASE_YESTERDAY_INCOME = '//*[@id="root"]/div/div[2]/div[2]/div[3]/div[1]/div[2]/div[2]'  # 昨日收益
NETEASE_YESTERDAY_INCOME_TAB = '//*[@id="root"]/div/div[2]/div[2]/div[1]/div/div/div[3]/div'  # 昨日收益标签

# 视频文件扩展名
VIDEO_EXTENSIONS = ('.mp4', '.mov', '.avi', '.wmv', '.flv')
