"""
任务执行器模块
负责执行定时任务，包括错误处理、重试机制等
"""

import time
import threading
import traceback
from typing import Dict, Any, Optional, Callable
from concurrent.futures import ThreadPoolExecutor, Future, TimeoutError

from .task_model import ScheduledTask, TaskType, TaskStatus
from .time_utils import TimeUtils


class TaskExecutor:
    """任务执行器"""
    
    def __init__(self, max_workers: int = 3, logger: Optional[Callable] = None):
        """
        初始化任务执行器

        Args:
            max_workers: 最大并发执行任务数
            logger: 日志记录函数
        """
        self.max_workers = max_workers
        self.logger = logger or print
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.running_tasks: Dict[str, Future] = {}
        self.platform_integration = None  # 将在manager中设置
        self.monitor = None  # 将在manager中设置
    
    def set_platform_integration(self, platform_integration):
        """设置平台集成器"""
        self.platform_integration = platform_integration

    def set_monitor(self, monitor):
        """设置任务监控器"""
        self.monitor = monitor
    
    def execute_task(self, task: ScheduledTask) -> bool:
        """
        执行任务
        
        Args:
            task: 要执行的任务
            
        Returns:
            是否成功提交执行
        """
        if task.id in self.running_tasks:
            self.logger(f"任务 {task.name} 已在执行中，跳过")
            return False
        
        # 提交任务到线程池
        future = self.executor.submit(self._execute_task_with_timeout, task)
        self.running_tasks[task.id] = future
        
        # 设置完成回调
        future.add_done_callback(lambda f: self._task_completed(task.id, f))
        
        self.logger(f"✅ 任务 {task.name} 已提交执行")
        return True
    
    def _execute_task_with_timeout(self, task: ScheduledTask) -> Dict[str, Any]:
        """
        带超时的任务执行
        
        Args:
            task: 要执行的任务
            
        Returns:
            执行结果
        """
        start_time = time.time()
        
        try:
            # 更新任务状态
            task.status = TaskStatus.RUNNING
            self.logger(f"🚀 开始执行任务: {task.name}")
            
            # 执行任务
            result = self._execute_task_logic(task)
            
            # 计算执行时间
            duration = time.time() - start_time
            
            # 更新任务状态
            success = result.get('success', False)
            error_message = result.get('error_message')
            result_data = result.get('data')

            task.update_after_execution(success, duration, error_message, result_data)

            # 通知监控器任务完成
            if self.monitor:
                self.monitor.task_completed(task, success, duration, error_message, result_data)

            if success:
                self.logger(f"✅ 任务执行成功: {task.name} (耗时: {duration:.2f}秒)")
            else:
                self.logger(f"❌ 任务执行失败: {task.name} - {error_message}")

            return result
            
        except Exception as e:
            duration = time.time() - start_time
            error_message = f"任务执行异常: {str(e)}"
            
            # 记录详细错误信息
            self.logger(f"❌ {error_message}")
            self.logger(f"错误详情: {traceback.format_exc()}")
            
            # 更新任务状态
            task.update_after_execution(False, duration, error_message)

            # 通知监控器任务失败
            if self.monitor:
                self.monitor.task_completed(task, False, duration, error_message)

            return {
                'success': False,
                'error_message': error_message,
                'data': None
            }
    
    def _execute_task_logic(self, task: ScheduledTask) -> Dict[str, Any]:
        """
        执行任务的具体逻辑
        
        Args:
            task: 要执行的任务
            
        Returns:
            执行结果
        """
        if not self.platform_integration:
            return {
                'success': False,
                'error_message': '平台集成器未初始化',
                'data': None
            }
        
        try:
            if task.task_type == TaskType.DATA_QUERY_ALL:
                return self._execute_data_query_all(task)
            elif task.task_type == TaskType.DATA_QUERY_SINGLE:
                return self._execute_data_query_single(task)
            elif task.task_type == TaskType.DRAFT_UPLOAD:
                return self._execute_draft_upload(task)
            elif task.task_type == TaskType.COMBINED:
                return self._execute_combined_task(task)
            else:
                return {
                    'success': False,
                    'error_message': f'不支持的任务类型: {task.task_type}',
                    'data': None
                }
        except Exception as e:
            return {
                'success': False,
                'error_message': f'任务执行异常: {str(e)}',
                'data': None
            }
    
    def _execute_data_query_all(self, task: ScheduledTask) -> Dict[str, Any]:
        """执行全部账号数据查询任务"""
        try:
            result = self.platform_integration.query_all_accounts_data(task.platform)
            
            if result.get('success', False):
                return {
                    'success': True,
                    'error_message': None,
                    'data': {
                        'platform': task.platform,
                        'accounts_processed': result.get('accounts_processed', 0),
                        'success_count': result.get('success_count', 0),
                        'failure_count': result.get('failure_count', 0)
                    }
                }
            else:
                return {
                    'success': False,
                    'error_message': result.get('error_message', '数据查询失败'),
                    'data': result.get('data')
                }
        except Exception as e:
            return {
                'success': False,
                'error_message': f'数据查询异常: {str(e)}',
                'data': None
            }
    
    def _execute_data_query_single(self, task: ScheduledTask) -> Dict[str, Any]:
        """执行单个账号数据查询任务"""
        try:
            if not task.target_accounts or len(task.target_accounts) == 0:
                return {
                    'success': False,
                    'error_message': '未指定目标账号',
                    'data': None
                }
            
            account = task.target_accounts[0]  # 取第一个账号
            result = self.platform_integration.query_single_account_data(task.platform, account)
            
            if result.get('success', False):
                return {
                    'success': True,
                    'error_message': None,
                    'data': {
                        'platform': task.platform,
                        'account': account,
                        'data': result.get('data')
                    }
                }
            else:
                return {
                    'success': False,
                    'error_message': result.get('error_message', '单账号数据查询失败'),
                    'data': result.get('data')
                }
        except Exception as e:
            return {
                'success': False,
                'error_message': f'单账号数据查询异常: {str(e)}',
                'data': None
            }
    
    def _execute_draft_upload(self, task: ScheduledTask) -> Dict[str, Any]:
        """执行存稿任务"""
        try:
            result = self.platform_integration.execute_draft_upload(task.platform, task.target_accounts)
            
            if result.get('success', False):
                return {
                    'success': True,
                    'error_message': None,
                    'data': {
                        'platform': task.platform,
                        'accounts': task.target_accounts,
                        'upload_count': result.get('upload_count', 0)
                    }
                }
            else:
                return {
                    'success': False,
                    'error_message': result.get('error_message', '存稿任务失败'),
                    'data': result.get('data')
                }
        except Exception as e:
            return {
                'success': False,
                'error_message': f'存稿任务异常: {str(e)}',
                'data': None
            }
    
    def _execute_combined_task(self, task: ScheduledTask) -> Dict[str, Any]:
        """执行组合任务（先查询后存稿）"""
        try:
            # 先执行数据查询
            query_result = self.platform_integration.query_all_accounts_data(task.platform)
            
            if not query_result.get('success', False):
                return {
                    'success': False,
                    'error_message': f'数据查询阶段失败: {query_result.get("error_message", "未知错误")}',
                    'data': {'stage': 'query', 'result': query_result}
                }
            
            # 等待一段时间再执行存稿
            time.sleep(5)
            
            # 执行存稿任务
            upload_result = self.platform_integration.execute_draft_upload(task.platform, task.target_accounts)
            
            combined_success = query_result.get('success', False) and upload_result.get('success', False)
            
            return {
                'success': combined_success,
                'error_message': upload_result.get('error_message') if not combined_success else None,
                'data': {
                    'platform': task.platform,
                    'query_result': query_result,
                    'upload_result': upload_result
                }
            }
        except Exception as e:
            return {
                'success': False,
                'error_message': f'组合任务异常: {str(e)}',
                'data': None
            }
    
    def _task_completed(self, task_id: str, future: Future):
        """任务完成回调"""
        try:
            # 从运行任务列表中移除
            self.running_tasks.pop(task_id, None)
            
            # 获取执行结果
            result = future.result()
            
            if result.get('success', False):
                self.logger(f"✅ 任务 {task_id} 执行完成")
            else:
                self.logger(f"❌ 任务 {task_id} 执行失败: {result.get('error_message', '未知错误')}")
                
        except Exception as e:
            self.logger(f"❌ 任务 {task_id} 完成回调异常: {str(e)}")
    
    def cancel_task(self, task_id: str) -> bool:
        """
        取消正在执行的任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否成功取消
        """
        if task_id in self.running_tasks:
            future = self.running_tasks[task_id]
            success = future.cancel()
            
            if success:
                self.running_tasks.pop(task_id, None)
                self.logger(f"✅ 任务 {task_id} 已取消")
            else:
                self.logger(f"⚠️ 任务 {task_id} 无法取消（可能已在执行中）")
            
            return success
        
        return False
    
    def get_running_tasks(self) -> Dict[str, str]:
        """
        获取正在运行的任务列表
        
        Returns:
            任务ID到状态的映射
        """
        running_status = {}
        
        for task_id, future in self.running_tasks.items():
            if future.done():
                if future.cancelled():
                    status = "已取消"
                elif future.exception():
                    status = "执行失败"
                else:
                    status = "执行完成"
            else:
                status = "执行中"
            
            running_status[task_id] = status
        
        return running_status
    
    def shutdown(self, wait: bool = True):
        """
        关闭任务执行器
        
        Args:
            wait: 是否等待正在执行的任务完成
        """
        self.logger("正在关闭任务执行器...")
        
        if not wait:
            # 取消所有正在执行的任务
            for task_id in list(self.running_tasks.keys()):
                self.cancel_task(task_id)
        
        # 关闭线程池
        self.executor.shutdown(wait=wait)
        self.logger("任务执行器已关闭")
