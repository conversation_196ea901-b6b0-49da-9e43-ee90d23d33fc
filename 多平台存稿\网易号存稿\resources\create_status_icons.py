"""
创建状态指示灯PNG图片
用于表格中的状态显示
"""

import os
from PIL import Image, ImageDraw

def create_status_icon(color, size=16, filename=None):
    """
    创建状态指示灯图片
    
    Args:
        color: 颜色 (RGB元组)
        size: 图片大小 (像素)
        filename: 保存文件名
    """
    # 创建透明背景的图片
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 绘制实心圆
    margin = 2  # 边距
    draw.ellipse([margin, margin, size-margin, size-margin], fill=color)
    
    # 保存图片
    if filename:
        img.save(filename, 'PNG')
    
    return img

def main():
    """创建所有状态指示灯图片"""
    # 确保目录存在
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 定义状态颜色 (RGB)
    colors = {
        'success': (40, 167, 69),    # 绿色 - 成功状态
        'failed': (220, 53, 69),     # 红色 - 失败状态  
        'unknown': (108, 117, 125)   # 灰色 - 未查询状态
    }
    
    # 创建图片
    for status, color in colors.items():
        filename = os.path.join(current_dir, f'status_{status}.png')
        create_status_icon(color, size=16, filename=filename)
        print(f"已创建: {filename}")

if __name__ == "__main__":
    main()
