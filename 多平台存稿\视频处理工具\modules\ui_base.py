"""
UI基础模块 - 包含基本UI类和初始化代码
"""

import os
import tkinter as tk
from typing import Callable

from .config_manager import ConfigManager
from .utils import get_color_options, get_color_names, center_window


class VideoPreprocessUI:
    """视频预处理工具UI类"""

    def create_ui(self):
        """创建用户界面 - 这个方法将在子类中实现"""
        raise NotImplementedError("此方法应在子类中实现")

    def center_window(self):
        """将窗口居中显示在屏幕上"""
        # 使用通用的窗口居中函数
        try:
            print("UI基类: 正在居中窗口...")
            center_window(self.root)
            print("UI基类: 窗口居中完成")
        except Exception as e:
            print(f"UI基类: 居中窗口时出错: {str(e)}")

    def on_close(self):
        """窗口关闭时的处理 - 这个方法可能会在子类中被覆盖"""
        # 保存配置并关闭窗口
        if hasattr(self, 'config_manager'):
            self.config_manager.save_config(show_message=False)  # 保存配置但不显示消息
        self.root.destroy()  # 关闭窗口

    def __init__(self, root: tk.Tk, process_callback: Callable = None):
        """
        初始化UI

        Args:
            root: Tkinter根窗口
            process_callback: 处理视频的回调函数
        """
        import sys

        self.root = root
        self.root.title("网易视频预处理工具 - 模块化版")

        # 根据是否在打包环境中调整窗口大小，统一使用系统默认字体大小
        if getattr(sys, 'frozen', False):
            # 在打包环境中使用较小的窗口
            self.root.geometry("1400x1200")  # 打包环境使用较小窗口
            self.root.minsize(1200, 1000)   # 相应调整最小窗口大小
            self.default_font_size = 9      # 统一使用系统默认字体大小
            print("检测到打包环境，使用适配的窗口大小: 1400x1200，系统默认字体大小: 9")
        else:
            # 在开发环境中使用原始大小
            self.root.geometry("1800x1600")  # 开发环境使用原始大小
            self.root.minsize(1280, 1200)   # 原始最小窗口大小
            self.default_font_size = 9      # 统一使用系统默认字体大小
            print("检测到开发环境，使用原始窗口大小: 1800x1600，系统默认字体大小: 9")

        # 确保窗口在屏幕中央显示
        self.center_window()

        # 注意：configure_fonts方法将在ui.py中被添加

        # 创建变量
        self.is_processing = False
        self.video_dir = tk.StringVar()
        self.processed_videos_dir = tk.StringVar()
        self.processed_covers_dir = tk.StringVar()

        # 添加变量修改跟踪
        self.video_dir.trace_add("write", self.update_output_dirs)

        # 视频处理相关设置
        self.video_min_duration = tk.IntVar(value=30)  # 最小视频时长(秒)
        self.video_max_duration = tk.IntVar(value=300)  # 最大视频时长(秒)
        self.video_ratio = tk.StringVar(value="16:9")  # 视频比例
        self.thread_num = tk.IntVar(value=4)  # 处理线程数

        # 封面文字设置 - 上方文字
        self.cover_text_top = tk.StringVar(value="")  # 左上角文字内容
        self.cover_color_top = tk.StringVar(value="#FFFFFF")  # 左上角文字颜色

        # 封面文字设置 - 下方文字
        self.cover_text_bottom = tk.StringVar(value="")  # 右下角文字内容
        self.cover_color_bottom = tk.StringVar(value="#FFFFFF")  # 右下角文字颜色

        # 共用的封面文字设置
        self.cover_size = tk.IntVar(value=60)  # 封面文字大小

        # 水印设置
        self.enable_watermark = tk.BooleanVar(value=False)  # 是否启用水印
        self.watermark_color = tk.StringVar(value="#FFFFFF")  # 水印颜色
        self.watermark_opacity = tk.DoubleVar(value=0.1)  # 水印透明度(0.0-1.0)
        self.watermark_position = tk.StringVar(value="全屏")  # 水印位置
        self.watermark_quantity = tk.IntVar(value=1)  # 水印数量

        # 视频去重设置
        self.enable_deduplication = tk.BooleanVar(value=True)  # 启用视频去重
        self.auto_use_filename = tk.BooleanVar(value=True)  # 自动使用文件名作为标题
        self.dedup_method = tk.StringVar(value="content")  # 去重方式：content或filename
        self.sample_frames = tk.IntVar(value=10)  # 视频去重采样帧数

        # 新增功能 - 封面分辨率设置
        self.enable_custom_cover_resolution = tk.BooleanVar(value=False)  # 是否启用自定义封面分辨率
        self.cover_width = tk.IntVar(value=1280)  # 封面宽度
        self.cover_height = tk.IntVar(value=720)  # 封面高度

        # 新增功能 - 视频格式设置
        self.enable_format_conversion = tk.BooleanVar(value=False)  # 是否启用格式转换
        self.output_format = tk.StringVar(value="mp4")  # 输出格式
        self.video_codec = tk.StringVar(value="h264")  # 视频编码器

        # 新增功能 - 黑暗模式
        self.enable_dark_mode = tk.BooleanVar(value=False)  # 是否启用黑暗模式

        # 新增功能 - GPU加速
        self.enable_gpu_acceleration = tk.BooleanVar(value=False)  # 是否启用GPU加速
        self.gpu_device = tk.StringVar(value="auto")  # GPU设备选择

        # 新增功能 - 智能资源分配
        self.enable_smart_resource = tk.BooleanVar(value=True)  # 是否启用智能资源分配
        self.memory_limit = tk.IntVar(value=0)  # 内存限制(MB)，0表示自动

        # 新增功能 - 文件名过滤
        self.enable_filename_filter = tk.BooleanVar(value=False)  # 是否启用文件名过滤
        self.filename_filters = tk.StringVar(value="")  # 文件名过滤关键词，用逗号分隔

        # 定义颜色选项和颜色名称映射
        self.color_options = get_color_options()
        self.color_names = get_color_names()

        # 定义主题颜色 - 更现代化的配色方案
        self.theme_colors = {
            "light": {
                "bg": "#f8f9fa",           # 更柔和的背景色
                "fg": "#212529",           # 更深的文字颜色，提高可读性
                "frame_bg": "#ffffff",     # 纯白色框架背景
                "entry_bg": "#ffffff",     # 输入框背景
                "entry_fg": "#212529",     # 输入框文字颜色
                "button_bg": "#e9ecef",    # 更柔和的按钮背景
                "button_hover": "#dee2e6", # 按钮悬停颜色
                "button_active": "#ced4da", # 按钮激活颜色
                "highlight_bg": "#4dabf7", # 更鲜明的高亮色
                "highlight_fg": "#ffffff", # 高亮文字颜色
                "canvas_bg": "#ffffff",    # 画布背景
                "log_bg": "#ffffff",       # 日志背景
                "log_fg": "#212529",       # 日志文字颜色
                "border": "#dee2e6",       # 边框颜色
                "success": "#40c057",      # 成功色
                "warning": "#fab005",      # 警告色
                "error": "#fa5252",        # 错误色
                "info": "#15aabf"          # 信息色
            },
            "dark": {
                "bg": "#121212",           # 深灰色背景，比纯黑更护眼
                "fg": "#e9ecef",           # 浅灰色文字，提高可读性
                "frame_bg": "#1e1e1e",     # 稍亮的框架背景，增加层次感
                "entry_bg": "#2d2d2d",     # 更深的灰色输入框背景
                "entry_fg": "#e9ecef",     # 输入框文字颜色
                "button_bg": "#2d2d2d",    # 按钮背景
                "button_hover": "#3d3d3d", # 按钮悬停颜色
                "button_active": "#4d4d4d", # 按钮激活颜色
                "highlight_bg": "#228be6", # 更鲜明的蓝色高亮
                "highlight_fg": "#ffffff", # 高亮文字颜色
                "canvas_bg": "#2d2d2d",    # 画布背景
                "log_bg": "#1e1e1e",       # 日志背景
                "log_fg": "#e9ecef",       # 日志文字颜色
                "border": "#3d3d3d",       # 边框颜色
                "success": "#40c057",      # 成功色
                "warning": "#fab005",      # 警告色
                "error": "#fa5252",        # 错误色
                "info": "#15aabf"          # 信息色
            }
        }

        # 初始化配置文件路径
        script_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        config_filename = "preprocess_config.ini"
        self.config_file = os.path.join(script_dir, config_filename)

        # 定时任务相关变量
        self.enable_scheduler = tk.BooleanVar(value=True)
        self.schedule_hour = tk.StringVar(value="00")
        self.schedule_minute = tk.StringVar(value="00")

        # 记录要跟踪保存的变量 - 确保与var_names顺序完全一致
        self.tracked_vars = [
            self.video_dir, self.processed_videos_dir, self.processed_covers_dir,
            self.video_min_duration, self.video_max_duration, self.video_ratio,
            self.thread_num, self.cover_text_top, self.cover_color_top,
            self.cover_text_bottom, self.cover_color_bottom, self.cover_size,
            self.enable_watermark, self.watermark_color, self.watermark_opacity, self.watermark_position, self.watermark_quantity,
            self.enable_deduplication, self.auto_use_filename, self.dedup_method, self.sample_frames,
            self.enable_custom_cover_resolution, self.cover_width, self.cover_height,
            self.enable_format_conversion, self.output_format, self.video_codec,
            self.enable_dark_mode, self.enable_gpu_acceleration, self.gpu_device,
            self.enable_smart_resource, self.memory_limit,
            self.enable_filename_filter, self.filename_filters,
            self.enable_scheduler, self.schedule_hour, self.schedule_minute
        ]

        # 添加界面字体大小到tracked_vars
        self.font_size_var = tk.IntVar(value=self.default_font_size)
        self.tracked_vars.append(self.font_size_var)

        # 添加自动保存间隔变量
        self.auto_save_interval_var = tk.IntVar(value=60)  # 默认60秒
        self.tracked_vars.append(self.auto_save_interval_var)

        # 创建变量名列表（与tracked_vars对应）
        self.var_names = [
            "video_dir", "processed_videos_dir", "processed_covers_dir",
            "video_min_duration", "video_max_duration", "video_ratio", "thread_num",
            "cover_text_top", "cover_color_top", "cover_text_bottom", "cover_color_bottom", "cover_size",
            "enable_watermark", "watermark_color", "watermark_opacity", "watermark_position", "watermark_quantity",
            "enable_deduplication", "auto_use_filename", "dedup_method", "sample_frames",
            "enable_custom_cover_resolution", "cover_width", "cover_height",
            "enable_format_conversion", "output_format", "video_codec",
            "enable_dark_mode", "enable_gpu_acceleration", "gpu_device",
            "enable_smart_resource", "memory_limit",
            "enable_filename_filter", "filename_filters",
            "enable_scheduler", "schedule_hour", "schedule_minute",
            "font_size_var", "auto_save_interval_var"
        ]

        # 创建配置管理器
        self.config_manager = ConfigManager(self.config_file, self.tracked_vars, self.log, self.var_names)

        # 注意：create_ui方法将在子类中实现

        # 加载配置
        self.config_manager.load_config()

        # 更新字体大小
        try:
            loaded_font_size = int(self.font_size_var.get())
            if 8 <= loaded_font_size <= 24:
                self.default_font_size = loaded_font_size
                self.font_size_var.set(loaded_font_size)  # 确保是整数
                self.configure_fonts()
                print(f"已加载字体大小设置: {loaded_font_size}")
            else:
                # 如果加载的值无效，使用系统默认值
                self.default_font_size = 9  # 系统默认字体大小
                self.font_size_var.set(self.default_font_size)
                print(f"字体大小超出范围，使用系统默认值: {self.default_font_size}")
        except (ValueError, TypeError):
            # 如果转换失败，使用系统默认值
            self.default_font_size = 9  # 系统默认字体大小
            self.font_size_var.set(self.default_font_size)
            print(f"字体大小设置无效，使用系统默认值: {self.default_font_size}")

        # 注意：_update_color_preview和apply_theme方法将在ui.py中被添加

        # 绑定关闭窗口事件，自动保存配置
        self.root.protocol("WM_DELETE_WINDOW", self.on_close)

        # 自动保存定时器
        self.auto_save_interval = 60 * 1000  # 60秒
        self.config_manager.schedule_auto_save(self.root, self.auto_save_interval)

        # 处理计数器
        self.success_count = 0
        self.failed_count = 0
        self.total_processed = 0

        # 处理回调
        self.process_callback = process_callback

    def update_output_dirs(self, *args):
        """
        当视频目录改变时，自动更新输出目录

        Args:
            *args: trace_add回调的参数
        """
        try:
            video_dir = self.video_dir.get()
            if video_dir and os.path.exists(video_dir):
                # 自动设置输出目录
                parent_dir = os.path.dirname(video_dir)

                # 设置处理后视频目录
                processed_videos_dir = os.path.join(parent_dir, "已处理视频")
                self.processed_videos_dir.set(processed_videos_dir)

                # 设置处理后封面目录
                processed_covers_dir = os.path.join(parent_dir, "已处理封面")
                self.processed_covers_dir.set(processed_covers_dir)

        except Exception as e:
            # 静默处理错误，避免影响UI初始化
            pass

    def log(self, message: str):
        """
        日志记录方法

        Args:
            message: 日志消息
        """
        print(message)

    def configure_fonts(self):
        """
        配置字体 - 基础实现
        子类应该重写此方法以提供完整的字体配置
        """
        pass
