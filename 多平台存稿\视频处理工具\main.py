"""
网易视频预处理工具  (Python 3.11.9适配版)

此程序是网易预处理功能的进一步模块化版本，兼容新版MoviePy，适配Python 3.11.9。
主要功能：
1. 视频目录选择和输出目录设置
2. 视频时长、比例筛选
3. 视频封面提取
4. 封面文字添加功能
5. 多线程并行处理
6. 视频水印添加功能（支持隐形水印）
7. 自定义封面分辨率
8. 视频格式转换
"""

import sys
import os
import time
import traceback
import tkinter as tk
from tkinter import messagebox
from ctypes import windll
import argparse

# 导入自定义模块
from modules.common.system import (
    print_system_info,
    ensure_tesseract_available,
    ensure_ffmpeg_available,
    ensure_moviepy_available
)
# 使用新的模块化UI类
from modules.ui.main import VideoPreprocessUI
from modules.video_processor import VideoProcessor



def main():
    """主程序入口"""
    try:
        # 解析命令行参数
        parser = argparse.ArgumentParser(description='网易视频预处理工具')
        parser.add_argument('--auto-mode', action='store_true', help='自动模式，无需用户交互')
        parser.add_argument('--status-file', type=str, help='状态文件路径，用于与主程序通信')
        args = parser.parse_args()

        auto_mode = args.auto_mode
        status_file = args.status_file

        print("正在启动网易视频预处理工具...")
        if auto_mode:
            print("以自动模式运行")
        print_system_info()

        # 检查依赖库
        print("\n=== 依赖库诊断信息 ===")

        # 检查Tesseract
        ensure_tesseract_available()

        # 检查FFmpeg
        ensure_ffmpeg_available()

        # 检查MoviePy
        ensure_moviepy_available()

        print("=== 诊断信息结束 ===\n")

        # 添加调试输出
        print("正在导入UI模块...")
        sys.stdout.flush()  # 确保输出立即显示

        # 延迟50毫秒以避免某些环境下的窗口显示问题
        time.sleep(0.05)

        # 创建主窗口
        print("创建主窗口...")
        sys.stdout.flush()
        root = tk.Tk()
        root.withdraw()  # 先隐藏窗口，等配置完成后再显示

        # 应用DPI感知
        try:
            print("设置DPI感知...")
            sys.stdout.flush()
            windll.shcore.SetProcessDpiAwareness(1)
            # 修复可能的高DPI缩放问题
            windll.user32.SetProcessDPIAware()
        except Exception as dpi_err:
            print(f"DPI设置跳过: {str(dpi_err)}")
            sys.stdout.flush()

        # 创建视频处理器
        print("创建视频处理器...")
        sys.stdout.flush()
        video_processor = VideoProcessor()

        # 创建应用程序实例
        print("创建UI实例...")
        sys.stdout.flush()
        try:
            # 保存UI实例的引用，防止被垃圾回收
            ui = VideoPreprocessUI(root, process_callback=video_processor.process_video,
                                  auto_mode=auto_mode, video_processor=video_processor)
            # 确保UI实例被保存为全局变量
            global app_ui
            app_ui = ui
            print("UI实例创建成功")
            sys.stdout.flush()
        except Exception as ui_err:
            print(f"UI创建失败: {str(ui_err)}")
            print(traceback.format_exc())
            sys.stdout.flush()
            raise

        # 确保窗口居中显示 - 在显示窗口前设置位置
        try:
            print("正在居中主窗口...")

            # 导入center_window函数
            from modules.utils import center_window

            # 直接使用改进的center_window函数
            center_window(root)

            # 强制更新
            root.update_idletasks()

            # 再次检查窗口位置
            actual_geometry = root.geometry()
            print(f"主窗口最终geometry: {actual_geometry}")
        except Exception as e:
            print(f"居中主窗口时出错: {str(e)}")

        # 显示窗口并强制更新
        root.deiconify()  # 显示窗口
        root.update_idletasks()
        root.update()

        # 将窗口置顶一次，确保可见性
        root.attributes('-topmost', True)
        root.after(100, lambda: root.attributes('-topmost', False))

        # 如果是自动模式，设置自动处理
        if auto_mode:
            print("=== 自动模式启动 ===")
            print("开始处理视频...")

            # 如果提供了状态文件，更新状态
            if status_file:
                try:
                    with open(status_file, 'w', encoding='utf-8') as f:
                        f.write("开始处理")
                    print(f"✅ 状态文件已更新: {status_file}")
                except Exception as e:
                    print(f"❌ 更新状态文件失败: {e}")

            # 设置进度回调函数
            def progress_callback(progress, message):
                print(f"📊 处理进度: {progress}% - {message}")
                # 更新状态文件
                if status_file:
                    try:
                        with open(status_file, 'w', encoding='utf-8') as f:
                            f.write(f"{progress}% - {message}")
                    except Exception as e:
                        print(f"❌ 更新状态文件失败: {e}")

            # 设置进度回调
            print("🔧 设置进度回调...")
            ui.set_progress_callback(progress_callback)

            # 延迟一下确保UI完全初始化
            print("⏳ 等待UI初始化完成...")
            root.update()
            time.sleep(1)

            # 检查目录设置
            print("🔍 检查目录设置...")
            video_dir = ui.video_dir.get()
            processed_videos_dir = ui.processed_videos_dir.get()
            processed_covers_dir = ui.processed_covers_dir.get()

            print(f"📂 视频源目录: {video_dir}")
            print(f"📂 处理后视频目录: {processed_videos_dir}")
            print(f"📂 处理后封面目录: {processed_covers_dir}")

            # 检查目录是否存在
            if not video_dir or not os.path.exists(video_dir):
                print(f"❌ 视频源目录不存在或未设置: {video_dir}")
                if status_file:
                    try:
                        with open(status_file, 'w', encoding='utf-8') as f:
                            f.write("失败: 视频源目录不存在")
                    except:
                        pass
                return

            # 检查视频文件
            video_extensions = ('.mp4', '.avi', '.mkv', '.mov', '.flv', '.wmv', '.webm')
            try:
                files = os.listdir(video_dir)
                video_files = [f for f in files if any(f.lower().endswith(ext) for ext in video_extensions)]
                print(f"🎬 找到 {len(video_files)} 个视频文件")

                if len(video_files) == 0:
                    print("⚠️ 没有找到视频文件，无法开始处理")
                    if status_file:
                        try:
                            with open(status_file, 'w', encoding='utf-8') as f:
                                f.write("失败: 没有找到视频文件")
                        except:
                            pass
                    return
            except Exception as e:
                print(f"❌ 检查视频文件失败: {e}")
                if status_file:
                    try:
                        with open(status_file, 'w', encoding='utf-8') as f:
                            f.write(f"失败: 检查视频文件失败 - {e}")
                    except:
                        pass
                return

            # 直接调用start_preprocess方法开始处理
            print("🚀 直接调用start_preprocess方法开始处理...")
            try:
                ui.start_preprocess()
                print("✅ start_preprocess方法调用成功")
            except Exception as e:
                print(f"❌ 调用start_preprocess方法失败: {e}")
                import traceback
                traceback.print_exc()
                if status_file:
                    try:
                        with open(status_file, 'w', encoding='utf-8') as f:
                            f.write(f"失败: 启动处理失败 - {e}")
                    except:
                        pass

            # 设置处理完成后自动退出的检查
            def check_completion():
                if not ui.is_processing:
                    # 处理已完成
                    if status_file:
                        try:
                            with open(status_file, 'w', encoding='utf-8') as f:
                                f.write("完成")
                        except Exception as e:
                            print(f"更新状态文件失败: {e}")
                    print("自动处理完成，程序将在3秒后退出")
                    root.after(3000, root.quit)  # 3秒后退出，让用户看到完成界面
                else:
                    # 继续检查
                    root.after(500, check_completion)

            # 启动完成检查
            root.after(1000, check_completion)

        print("窗口已创建，启动主循环...")
        # 启动主循环
        root.mainloop()
        print("程序正常退出")
    except Exception as e:
        print(f"程序启动失败: {str(e)}")
        print(traceback.format_exc())
        try:
            messagebox.showerror("错误", f"程序启动失败: {str(e)}")
        except:
            pass
        # 保持控制台窗口开着，让用户看到错误
        input("按Enter键退出...")

if __name__ == "__main__":
    # 创建全局变量存储UI实例
    app_ui = None

    # 重定向输出到文件和控制台

    class TeeOutput:
        """同时输出到文件和控制台的类"""
        def __init__(self, file_stream, console_stream):
            self.file_stream = file_stream
            self.console_stream = console_stream

        def write(self, message):
            # 写入文件（UTF-8编码）
            self.file_stream.write(message)

            # 写入控制台（处理编码问题）
            try:
                self.console_stream.write(message)
            except UnicodeEncodeError:
                # 如果控制台不支持某些字符，替换为安全字符
                safe_message = message.encode('ascii', 'replace').decode('ascii')
                self.console_stream.write(safe_message)

            # 确保文件和控制台都立即显示
            self.file_stream.flush()
            try:
                self.console_stream.flush()
            except:
                pass

        def flush(self):
            self.file_stream.flush()
            try:
                self.console_stream.flush()
            except:
                pass

    # 打开日志文件
    with open("debug_log.txt", "w", encoding="utf-8") as f:
        # 保存原始stdout
        original_stdout = sys.stdout
        # 设置stdout为同时输出到文件和控制台
        sys.stdout = TeeOutput(f, original_stdout)
        try:
            main()
        except Exception as e:
            print(f"主程序异常: {str(e)}")
            print(traceback.format_exc())
        finally:
            # 恢复原始stdout
            sys.stdout = original_stdout
