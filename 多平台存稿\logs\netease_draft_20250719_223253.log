[2025-07-19 22:32:58] [INFO] 🚀 启动多平台存稿工具...
[2025-07-19 22:32:58] [INFO] 🔍 [调试] 当前平台: toutiao
[2025-07-19 22:32:58] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-07-19 22:32:58] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-07-19 22:32:58] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-19 22:32:58] [INFO] 🔄 发现旧数据文件: C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\toutiao_account_data.json
[2025-07-19 22:32:58] [INFO] ℹ️ 新数据文件已存在且更新，跳过迁移: C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\toutiao_account_data.json
[2025-07-19 22:32:58] [INFO] 🔄 发现旧数据文件: C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\account_data.json
[2025-07-19 22:32:58] [INFO] ℹ️ 新数据文件已存在且更新，跳过迁移: C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\account_data.json
[2025-07-19 22:32:58] [INFO] 已从toutiao平台加载存稿详情数据，共 27 个账号
[2025-07-19 22:32:59] [INFO] 已确保所有必要目录存在
[2025-07-19 22:32:59] [INFO] 🔍 [调试] 当前平台: toutiao
[2025-07-19 22:32:59] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-07-19 22:32:59] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-07-19 22:32:59] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-19 22:32:59] [INFO] 🔍 [调试] 更新账号树 - 当前平台: toutiao
[2025-07-19 22:32:59] [INFO] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-19 22:32:59] [INFO] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-07-19 22:32:59] [INFO] 已加载账号数据: 79 条记录
[2025-07-19 22:32:59] [INFO] 🔍 [调试] 账号数据为空或未加载
[2025-07-19 22:32:59] [INFO] 🔍 [调试] 更新账号树 - 当前平台: toutiao
[2025-07-19 22:32:59] [INFO] 🔍 [调试] 更新账号树 - existing_accounts数量: 59
[2025-07-19 22:32:59] [INFO] 🔍 [调试] 使用toutiao平台账号过滤，获取到 56 条数据
[2025-07-19 22:32:59] [INFO] ❌ 初始UI更新失败: cannot access local variable 'default_data' where it is not associated with a value
[2025-07-19 22:33:15] [INFO] 平台切换前保存 toutiao 的设置和存稿详情数据
[2025-07-19 22:33:15] [INFO] 🔍 [调试] 更新账号树 - 当前平台: toutiao
[2025-07-19 22:33:15] [INFO] 🔍 [调试] 更新账号树 - existing_accounts数量: 59
[2025-07-19 22:33:15] [INFO] 🔍 [调试] 使用toutiao平台账号过滤，获取到 56 条数据
[2025-07-19 22:33:15] [INFO] ✅ 设置已静默保存
[2025-07-19 22:33:15] [INFO] 已确保所有必要目录存在
[2025-07-19 22:33:15] [INFO] 正在更新账号管理器，当前平台: netease
[2025-07-19 22:33:15] [INFO] 账号目录: D:/网易号全自动/网易号账号
[2025-07-19 22:33:15] [INFO] 已加载 19 个netease平台账号
[2025-07-19 22:33:15] [INFO] 🔍 [调试] 当前平台: netease
[2025-07-19 22:33:15] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/网易号账号
[2025-07-19 22:33:15] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\网易号账号
[2025-07-19 22:33:15] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\网易号账号\netease_account_data.json
[2025-07-19 22:33:15] [INFO] 已加载账号数据: 25 条记录
[2025-07-19 22:33:15] [INFO] 账号数据文件路径: D:\网易号全自动\网易号账号\netease_account_data.json
[2025-07-19 22:33:15] [INFO] 已更新数据查询管理器的AccountData引用
[2025-07-19 22:33:15] [INFO] 已更新Excel导出管理器的AccountData引用
[2025-07-19 22:33:15] [INFO] 已从netease平台加载存稿详情数据，共 18 个账号
[2025-07-19 22:33:15] [INFO] [netease] ✅ 视频分配跟踪器初始化成功
[2025-07-19 22:33:15] [INFO] 已切换到平台: 网易号平台
[2025-07-19 22:33:15] [INFO] [netease] ✅ 视频分配跟踪器初始化成功
[2025-07-19 22:33:15] [INFO] 🔍 [调试] 当前平台: netease
[2025-07-19 22:33:15] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/网易号账号
[2025-07-19 22:33:15] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\网易号账号
[2025-07-19 22:33:15] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\网易号账号\netease_account_data.json
[2025-07-19 22:33:15] [INFO] 🔍 [调试] 更新账号树 - 当前平台: netease
[2025-07-19 22:33:15] [INFO] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\网易号账号\netease_account_data.json
[2025-07-19 22:33:15] [INFO] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-07-19 22:33:15] [INFO] 已加载账号数据: 25 条记录
[2025-07-19 22:33:15] [INFO] 🔍 [调试] 账号数据为空或未加载
[2025-07-19 22:33:15] [INFO] 🔍 [调试] 更新账号树 - 当前平台: netease
[2025-07-19 22:33:15] [INFO] 🔍 [调试] 更新账号树 - existing_accounts数量: 19
[2025-07-19 22:33:15] [INFO] 🔍 [调试] 使用netease平台账号过滤，获取到 17 条数据
[2025-07-19 22:33:19] [INFO] 正在关闭程序，清理后台资源...
[2025-07-19 22:33:19] [INFO] 🧹 开始清理所有资源...
[2025-07-19 22:33:19] [INFO] 正在停止并发管理器...
[2025-07-19 22:33:19] [INFO] ✅ 运行中的任务已停止
[2025-07-19 22:33:19] [INFO] ✅ 定时器线程已清理
[2025-07-19 22:33:19] [INFO] ✅ Chrome进程强制清理完成
[2025-07-19 22:33:19] [INFO] ✅ 浏览器进程已清理
[2025-07-19 22:33:20] [INFO] ✅ 日志处理线程已清理
[2025-07-19 22:33:20] [INFO] ✅ 存稿处理器已清理
[2025-07-19 22:33:20] [INFO] ✅ 线程池已清理
[2025-07-19 22:33:20] [INFO] 正在停止串行刷新工作线程...
[2025-07-19 22:33:23] [INFO] ⚠️ 仍有 2 个线程未结束
[2025-07-19 22:33:23] [INFO] ✅ 临时文件已清理
[2025-07-19 22:33:24] [INFO] ✅ 资源清理完成
[2025-07-19 22:33:24] [INFO] 🔍 [调试] 更新账号树 - 当前平台: netease
[2025-07-19 22:33:24] [INFO] 🔍 [调试] 更新账号树 - existing_accounts数量: 19
[2025-07-19 22:33:24] [INFO] 🔍 [调试] 使用netease平台账号过滤，获取到 17 条数据
[2025-07-19 22:33:24] [INFO] ✅ 设置已静默保存
[2025-07-19 22:33:24] [INFO] 程序清理完成，正在退出...
