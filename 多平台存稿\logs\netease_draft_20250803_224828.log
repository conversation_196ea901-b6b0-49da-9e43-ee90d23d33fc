[2025-08-03 22:48:33] [INFO] 🚀 启动多平台存稿工具...
[2025-08-03 22:48:33] [INFO] ✅ 定时任务管理器UI初始化完成
[2025-08-03 22:48:33] [INFO] ✅ 定时任务管理标签页创建完成
[SUCCESS] ✅ 升级版定时任务调度管理器初始化完成
[2025-08-03 22:48:33] [INFO] 已从toutiao平台加载存稿详情数据，共 83 个账号
[2025-08-03 22:48:33] [DEBUG] 🔄 调度器主循环开始
[2025-08-03 22:48:33] [INFO] 🚀 定时任务调度器已启动
[2025-08-03 22:48:34] [INFO] 已确保所有必要目录存在
[2025-08-03 22:48:34] [DEBUG] 🔍 [调试] 当前平台: toutiao
[2025-08-03 22:48:34] [DEBUG] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-08-03 22:48:34] [DEBUG] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-08-03 22:48:34] [SUCCESS] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-08-03 22:48:34] [DEBUG] 🔍 [调试] 更新账号树 - 当前平台: toutiao
[2025-08-03 22:48:34] [DEBUG] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-08-03 22:48:34] [DEBUG] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-08-03 22:48:34] [INFO] 已加载账号数据: 85 条记录
[2025-08-03 22:48:34] [DEBUG] 🔍 [调试] 账号数据为空或未加载
[2025-08-03 22:48:34] [SUCCESS] ✅ 初始UI更新完成
[2025-08-03 22:48:47] [INFO] 已选择账号: ***********
[2025-08-03 22:48:49] [DEBUG] 🚀 开始为账号 *********** 存稿
[2025-08-03 22:48:49] [INFO] 已确保所有必要目录存在
[2025-08-03 22:48:49] [INFO] 使用单线程模式处理账号
[2025-08-03 22:48:49] [DEBUG] 🔍 [DEBUG] 准备启动单线程任务，参数: ***********
[2025-08-03 22:48:49] [DEBUG] 🔍 [DEBUG] 单线程任务已启动
[2025-08-03 22:48:49] [DEBUG] 🔍 [DEBUG] _single_task_thread 开始执行，参数: ***********
[2025-08-03 22:48:49] [DEBUG] 📋 开始处理单个账号: ***********
[2025-08-03 22:48:49] [INFO] 视频分配方式: 随机分配
[2025-08-03 22:48:49] [DEBUG] 开始处理账号: ***********
[2025-08-03 22:48:49] [SUCCESS] 正在使用Cookie登录账号: ***********
[2025-08-03 22:48:49] [SUCCESS] ✅ Cookie预检测通过，启动浏览器验证...
[2025-08-03 22:48:55] [SUCCESS] 开始查询账号数据: *********** [toutiao] 浏览器驱动创建成功，使用端口: 9515
[2025-08-03 22:49:01] [SUCCESS] 正在加载Cookie文件: D:/网易号全自动/头条号账号\***********.txt
[2025-08-03 22:49:24] [SUCCESS] ✅ Cookie添加完成
[2025-08-03 22:49:26] [SUCCESS] ✅ 头条号快速登录成功 (等待0.5秒)
[2025-08-03 22:49:26] [SUCCESS] 登录成功，开始处理视频: ***********
[2025-08-03 22:49:26] [INFO] 找到 3 个视频文件
[2025-08-03 22:49:26] [INFO] 已随机打乱 3 个视频文件的顺序
[2025-08-03 22:49:26] [DEBUG] 开始处理视频: 2005年，李讷母亲贴身护士回忆：那些都是瞎说的，她被抓时我.mp4
[2025-08-03 22:49:26] [DEBUG] 🎬 开始头条号视频存稿流程
[2025-08-03 22:49:26] [DEBUG] 🔍 正在定位视频菜单...
[2025-08-03 22:49:26] [SUCCESS] ✅ 已点击视频菜单
[2025-08-03 22:49:29] [INFO] 🌐 当前不在上传页面，导航到视频上传页面...
[2025-08-03 22:49:33] [SUCCESS] ✅ 已导航到视频上传页面
[2025-08-03 22:49:33] [INFO] 📁 准备上传视频: 2005年，李讷母亲贴身护士回忆：那些都是瞎说的，她被抓时我.mp4
[2025-08-03 22:49:38] [DEBUG] 🔍 找到上传按钮，准备点击...
[2025-08-03 22:49:38] [SUCCESS] ✅ 已点击上传视频按钮
[2025-08-03 22:49:40] [SUCCESS] ✅ 视频文件已选择并开始上传
[2025-08-03 22:49:40] [DEBUG] ⏳ 开始监控视频上传状态...
[2025-08-03 22:49:40] [INFO] 📊 当前上传状态: 上传中… 0%
[2025-08-03 22:49:40] [DEBUG] ⏳ 视频正在上传中...
[2025-08-03 22:49:45] [INFO] 📊 当前上传状态: 上传中… 62.79%
[2025-08-03 22:49:45] [DEBUG] ⏳ 视频正在上传中...
[2025-08-03 22:49:50] [INFO] 📊 当前上传状态: 上传中… 100%
[2025-08-03 22:49:50] [DEBUG] ⏳ 视频正在上传中...
[2025-08-03 22:49:55] [SUCCESS] 📊 当前上传状态: 上传成功
[2025-08-03 22:49:55] [SUCCESS] ✅ 视频上传成功！
[2025-08-03 22:49:55] [DEBUG] 📷 开始上传视频封面...
[2025-08-03 22:49:55] [SUCCESS] ✅ 已点击上传封面按钮
[2025-08-03 22:49:57] [SUCCESS] ✅ 已通过文字查找点击本地上传
[2025-08-03 22:50:09] [ERROR] ❌ 点击上传封面区域失败: Message: 
Stacktrace:
	GetHandleVerifier [0x0x7ff7b7a26f75+76917]
	GetHandleVerifier [0x0x7ff7b7a26fd0+77008]
	(No symbol) [0x0x7ff7b77d9dea]
	(No symbol) [0x0x7ff7b7830256]
	(No symbol) [0x0x7ff7b783050c]
	(No symbol) [0x0x7ff7b7883887]
	(No symbol) [0x0x7ff7b78584af]
	(No symbol) [0x0x7ff7b788065c]
	(No symbol) [0x0x7ff7b7858243]
	(No symbol) [0x0x7ff7b7821431]
	(No symbol) [0x0x7ff7b78221c3]
	GetHandleVerifier [0x0x7ff7b7cfd2ad+3051437]
	GetHandleVerifier [0x0x7ff7b7cf7903+3028483]
	GetHandleVerifier [0x0x7ff7b7d1589d+3151261]
	GetHandleVerifier [0x0x7ff7b7a4183e+185662]
	GetHandleVerifier [0x0x7ff7b7a496ff+218111]
	GetHandleVerifier [0x0x7ff7b7a2faf4+112628]
	GetHandleVerifier [0x0x7ff7b7a2fca9+113065]
	GetHandleVerifier [0x0x7ff7b7a16c78+10616]
	BaseThreadInitThunk [0x0x7ffcc2f7e8d7+23]
	RtlUserThreadStart [0x0x7ffcc4e5c34c+44]

[2025-08-03 22:50:09] [WARNING] ⚠️ 上传封面失败，继续存稿流程
[2025-08-03 22:50:09] [DEBUG] 💾 开始保存草稿...
[2025-08-03 22:50:10] [SUCCESS] ✅ 已点击存草稿按钮
[2025-08-03 22:50:13] [WARNING] ✅ 草稿保存成功！检测到'全部保存成功'提示
[2025-08-03 22:50:13] [SUCCESS] ✅ 头条号视频存稿成功！
[2025-08-03 22:50:13] [SUCCESS] ✅ 视频存稿成功: 2005年，李讷母亲贴身护士回忆：那些都是瞎说的，她被抓时我.mp4
[2025-08-03 22:50:13] [DEBUG] 开始处理视频: 19岁少年错付千元轻生，司机否认：他要套现！死无对证引哗然！.mp4
[2025-08-03 22:50:13] [DEBUG] 🎬 开始头条号视频存稿流程
[2025-08-03 22:50:13] [DEBUG] 🔍 正在定位视频菜单...
[2025-08-03 22:50:13] [SUCCESS] ✅ 已点击视频菜单
[2025-08-03 22:50:16] [INFO] 🌐 当前不在上传页面，导航到视频上传页面...
[2025-08-03 22:50:19] [SUCCESS] ✅ 已导航到视频上传页面
[2025-08-03 22:50:19] [INFO] 📁 准备上传视频: 19岁少年错付千元轻生，司机否认：他要套现！死无对证引哗然！.mp4
[2025-08-03 22:50:19] [ERROR] ❌ 上传视频文件失败: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff7b7a26f75+76917]
	GetHandleVerifier [0x0x7ff7b7a26fd0+77008]
	(No symbol) [0x0x7ff7b77d9dea]
	(No symbol) [0x0x7ff7b77c5f15]
	(No symbol) [0x0x7ff7b77eabf4]
	(No symbol) [0x0x7ff7b785fa85]
	(No symbol) [0x0x7ff7b787ff72]
	(No symbol) [0x0x7ff7b7858243]
	(No symbol) [0x0x7ff7b7821431]
	(No symbol) [0x0x7ff7b78221c3]
	GetHandleVerifier [0x0x7ff7b7cfd2ad+3051437]
	GetHandleVerifier [0x0x7ff7b7cf7903+3028483]
	GetHandleVerifier [0x0x7ff7b7d1589d+3151261]
	GetHandleVerifier [0x0x7ff7b7a4183e+185662]
	GetHandleVerifier [0x0x7ff7b7a496ff+218111]
	GetHandleVerifier [0x0x7ff7b7a2faf4+112628]
	GetHandleVerifier [0x0x7ff7b7a2fca9+113065]
	GetHandleVerifier [0x0x7ff7b7a16c78+10616]
	BaseThreadInitThunk [0x0x7ffcc2f7e8d7+23]
	RtlUserThreadStart [0x0x7ffcc4e5c34c+44]

[2025-08-03 22:50:19] [WARNING] ⚠️ 第1次尝试失败，2次重试机会剩余
[2025-08-03 22:50:19] [INFO] ⏳ 等待2秒后重试...
[2025-08-03 22:50:21] [INFO] 📁 准备上传视频: 19岁少年错付千元轻生，司机否认：他要套现！死无对证引哗然！.mp4
[2025-08-03 22:50:21] [ERROR] ❌ 上传视频文件失败: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff7b7a26f75+76917]
	GetHandleVerifier [0x0x7ff7b7a26fd0+77008]
	(No symbol) [0x0x7ff7b77d9c1c]
	(No symbol) [0x0x7ff7b782055f]
	(No symbol) [0x0x7ff7b7858332]
	(No symbol) [0x0x7ff7b7852e53]
	(No symbol) [0x0x7ff7b7851f19]
	(No symbol) [0x0x7ff7b77a4b05]
	GetHandleVerifier [0x0x7ff7b7cfd2ad+3051437]
	GetHandleVerifier [0x0x7ff7b7cf7903+3028483]
	GetHandleVerifier [0x0x7ff7b7d1589d+3151261]
	GetHandleVerifier [0x0x7ff7b7a4183e+185662]
	GetHandleVerifier [0x0x7ff7b7a496ff+218111]
	(No symbol) [0x0x7ff7b77a3b00]
	GetHandleVerifier [0x0x7ff7b7e15f18+4201496]
	BaseThreadInitThunk [0x0x7ffcc2f7e8d7+23]
	RtlUserThreadStart [0x0x7ffcc4e5c34c+44]

[2025-08-03 22:50:21] [WARNING] ⚠️ 第2次尝试失败，1次重试机会剩余
[2025-08-03 22:50:21] [INFO] ⏳ 等待2秒后重试...
