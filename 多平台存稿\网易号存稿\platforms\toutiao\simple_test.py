"""
头条号并发功能简单测试
"""

import os
import sys

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(current_dir))))
sys.path.insert(0, project_root)

def test_imports():
    """测试导入功能"""
    print("🔍 测试模块导入...")
    
    try:
        # 测试共享并发池导入
        from 多平台存稿.网易号存稿.common.concurrent_pool import get_shared_pool
        print("✅ 共享并发池导入成功")
        
        # 测试头条号并发管理器导入
        from 多平台存稿.网易号存稿.platforms.toutiao.concurrency.manager import ToutiaoConcurrentManager
        print("✅ 头条号并发管理器导入成功")
        
        # 测试头条号工作线程导入
        from 多平台存稿.网易号存稿.platforms.toutiao.concurrency.worker import ToutiaoWorker
        print("✅ 头条号工作线程导入成功")
        
        # 测试头条号处理器导入
        from 多平台存稿.网易号存稿.platforms.toutiao.processor import ToutiaoDraftProcessor
        print("✅ 头条号处理器导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {str(e)}")
        return False

def test_shared_pool():
    """测试共享并发池"""
    print("\n🔍 测试共享并发池...")
    
    try:
        from 多平台存稿.网易号存稿.common.concurrent_pool import get_shared_pool
        
        # 获取共享池实例
        pool = get_shared_pool()
        print("✅ 获取共享池实例成功")
        
        # 初始化池
        success = pool.initialize_pool(max_workers=3)
        if success:
            print("✅ 共享池初始化成功")
        else:
            print("❌ 共享池初始化失败")
            return False
        
        # 检查池状态
        status = pool.get_pool_status()
        print(f"✅ 池状态: {status}")
        
        return True
        
    except Exception as e:
        print(f"❌ 共享池测试失败: {str(e)}")
        return False

def test_concurrent_manager():
    """测试并发管理器"""
    print("\n🔍 测试头条号并发管理器...")
    
    try:
        from 多平台存稿.网易号存稿.platforms.toutiao.concurrency.manager import ToutiaoConcurrentManager
        
        # 创建管理器实例
        manager = ToutiaoConcurrentManager(
            account_dir="./test_accounts",
            processed_dir="./test_videos",
            processed_covers_dir="./test_covers",
            max_workers=2,
            log_callback=lambda msg: print(f"[管理器] {msg}")
        )
        print("✅ 并发管理器创建成功")
        
        # 检查初始状态
        assert manager.is_running == False
        print("✅ 初始状态检查通过")
        
        # 检查进度获取
        progress = manager.get_progress()
        assert isinstance(progress, dict)
        print("✅ 进度获取功能正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 并发管理器测试失败: {str(e)}")
        return False

def test_processor_integration():
    """测试处理器集成"""
    print("\n🔍 测试头条号处理器集成...")
    
    try:
        from 多平台存稿.网易号存稿.platforms.toutiao.processor import ToutiaoDraftProcessor
        
        # 创建处理器实例
        processor = ToutiaoDraftProcessor(
            account_dir="./test_accounts",
            processed_dir="./test_videos",
            processed_covers_dir="./test_covers",
            log_callback=lambda msg: print(f"[处理器] {msg}")
        )
        print("✅ 处理器创建成功")
        
        # 检查并发方法
        methods = ['start_concurrent', 'stop_concurrent', 'get_concurrent_progress', 'is_concurrent_running']
        for method in methods:
            if hasattr(processor, method):
                print(f"✅ 方法 {method} 存在")
            else:
                print(f"❌ 方法 {method} 不存在")
                return False
        
        # 检查初始状态
        assert processor.is_concurrent_running() == False
        print("✅ 并发运行状态检查通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 处理器集成测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 开始头条号并发功能简单测试\n")
    
    tests = [
        ("模块导入", test_imports),
        ("共享并发池", test_shared_pool),
        ("并发管理器", test_concurrent_manager),
        ("处理器集成", test_processor_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"--- 执行测试: {test_name} ---")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过\n")
            else:
                print(f"❌ {test_name} 测试失败\n")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}\n")
    
    # 输出总结
    print("🎯 测试总结:")
    print(f"总测试数: {total}")
    print(f"通过测试: {passed}")
    print(f"失败测试: {total - passed}")
    print(f"通过率: {passed / total * 100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！头条号并发功能基础架构正常")
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
