"""
应用程序启动器 - 处理打包后的环境初始化
"""

import os
import sys
import traceback

def setup_environment():
    """设置运行环境"""
    print("设置运行环境...")
    
    # 获取应用程序目录
    if getattr(sys, 'frozen', False):
        # 打包后的可执行文件
        app_dir = os.path.dirname(sys.executable)
        print(f"检测到打包环境，应用目录: {app_dir}")
    else:
        # 开发环境
        app_dir = os.path.dirname(os.path.abspath(__file__))
        print(f"开发环境，应用目录: {app_dir}")
    
    # 设置工作目录
    os.chdir(app_dir)
    print(f"设置工作目录: {app_dir}")
    
    # 添加到Python路径
    if app_dir not in sys.path:
        sys.path.insert(0, app_dir)
    
    # 添加子目录到Python路径
    subdirs = ['网易号存稿', '视频处理工具']
    for subdir in subdirs:
        subdir_path = os.path.join(app_dir, subdir)
        if os.path.exists(subdir_path) and subdir_path not in sys.path:
            sys.path.insert(0, subdir_path)
            print(f"添加到Python路径: {subdir_path}")
    
    return app_dir

def check_dependencies():
    """检查依赖"""
    print("检查依赖...")
    
    required_modules = [
        'tkinter',
        'selenium',
        'PIL',
        'requests',
        'jieba',
        'moviepy',
        'pyautogui'
    ]
    
    missing_modules = []
    
    for module_name in required_modules:
        try:
            __import__(module_name)
            print(f"✅ {module_name}")
        except ImportError:
            missing_modules.append(module_name)
            print(f"❌ {module_name}")
    
    if missing_modules:
        print(f"缺少依赖: {missing_modules}")
        return False
    
    print("所有依赖检查通过")
    return True

def create_required_directories(app_dir):
    """创建必要的目录"""
    print("创建必要的目录...")
    
    required_dirs = [
        'logs',
        'screenshots', 
        'config',
        '待存稿',
        '待存稿封面',
        '数据目录',
        '视频目录'
    ]
    
    for dir_name in required_dirs:
        dir_path = os.path.join(app_dir, dir_name)
        if not os.path.exists(dir_path):
            try:
                os.makedirs(dir_path, exist_ok=True)
                print(f"✅ 创建目录: {dir_path}")
            except Exception as e:
                print(f"❌ 创建目录失败: {dir_path} - {e}")
        else:
            print(f"✅ 目录已存在: {dir_path}")

def create_default_configs(app_dir):
    """创建默认配置文件"""
    print("创建默认配置文件...")
    
    default_configs = {
        'config.json': {
            "theme": "dark",
            "language": "zh_CN",
            "auto_save": True
        },
        'directory_config.json': {
            "video_directory": "D:/头条全自动/视频搬运/待处理视频",
            "cover_directory": "D:/头条全自动/视频搬运/待处理封面",
            "output_directory": "D:/网易号全自动/未处理/待上传视频"
        },
        'timer_config.json': {
            "enabled": False,
            "interval": 60,
            "auto_start": False
        },
        'account_data.json': {},
        'draft_details.json': {},
        'netease_draft_details.json': {},
        'toutiao_draft_details.json': {}
    }
    
    for config_file, default_content in default_configs.items():
        config_path = os.path.join(app_dir, config_file)
        if not os.path.exists(config_path):
            try:
                import json
                with open(config_path, 'w', encoding='utf-8') as f:
                    json.dump(default_content, f, ensure_ascii=False, indent=2)
                print(f"✅ 创建配置文件: {config_path}")
            except Exception as e:
                print(f"❌ 创建配置文件失败: {config_path} - {e}")
        else:
            print(f"✅ 配置文件已存在: {config_path}")

def main():
    """主函数"""
    print("=" * 60)
    print("多平台存稿工具启动器")
    print("=" * 60)
    
    try:
        # 设置环境
        app_dir = setup_environment()
        
        # 检查依赖
        if not check_dependencies():
            print("依赖检查失败，程序可能无法正常运行")
            input("按Enter键继续...")
        
        # 创建必要目录
        create_required_directories(app_dir)
        
        # 创建默认配置
        create_default_configs(app_dir)
        
        print("=" * 60)
        print("环境初始化完成，启动主程序...")
        print("=" * 60)
        
        # 导入并运行主程序
        try:
            # 修复导入问题
            import fix_imports
            fix_imports.main()
            
            # 启动主程序
            import run_app
            run_app.main() if hasattr(run_app, 'main') else None
            
        except ImportError as e:
            print(f"导入主程序失败: {e}")
            print("尝试直接执行...")
            
            # 直接执行主程序文件
            main_script = os.path.join(app_dir, 'run_app.py')
            if os.path.exists(main_script):
                exec(open(main_script, encoding='utf-8').read())
            else:
                print(f"找不到主程序文件: {main_script}")
                input("按Enter键退出...")
                
    except Exception as e:
        print(f"启动过程中出错: {e}")
        traceback.print_exc()
        input("按Enter键退出...")

if __name__ == "__main__":
    main()
