[2025-07-15 08:50:09] [INFO] 🚀 启动多平台存稿工具...
[2025-07-15 08:50:10] [ERROR] 程序发生未捕获的异常: 'LogManager' object has no attribute 'show_progress_dialog'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\main.py", line 77, in main
    app = NeteaseDraftUI(root, config_manager)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\ui\main.py", line 174, in __init__
    self.create_ui()
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\ui\main.py", line 303, in create_ui
    self.create_modular_layout(content_container)
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\ui\main.py", line 355, in create_modular_layout
    self.create_content_panel(right_panel)
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\ui\main.py", line 941, in create_content_panel
    self.create_modern_notebook(content_frame)
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\ui\main.py", line 966, in create_modern_notebook
    self.log_manager.create_log_tab(log_tab)
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\ui\managers\log_manager.py", line 136, in create_log_tab
    self._create_log_toolbar(main_container)
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\ui\managers\log_manager.py", line 185, in _create_log_toolbar
    command=self.show_progress_dialog
            ^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'LogManager' object has no attribute 'show_progress_dialog'

[2025-07-15 08:50:11] [INFO] 🔍 [调试] 当前平台: toutiao
[2025-07-15 08:50:12] [INFO] 🔍 [调试] 配置的账号目录: D:\网易号全自动\头条号账号
