"""
主题定义模块 - 定义UI主题
现代化设计风格，支持深色和浅色模式
"""

import tkinter as tk
from tkinter import ttk
import platform

class Theme:
    """主题类，定义UI主题"""

    # 浅色主题颜色定义
    LIGHT = {
        "background": "#f8f9fa",      # 背景色
        "foreground": "#212529",      # 前景色
        "accent": "#0d6efd",          # 强调色
        "accent_hover": "#0b5ed7",    # 强调色悬停
        "success": "#198754",         # 成功色
        "warning": "#ffc107",         # 警告色
        "error": "#dc3545",           # 错误色
        "info": "#0dcaf0",            # 信息色
        "border": "#dee2e6",          # 边框色
        "input_bg": "#ffffff",        # 输入框背景
        "hover_bg": "#e9ecef",        # 悬停背景
        "active_bg": "#ced4da",       # 激活背景
        "disabled_bg": "#e9ecef",     # 禁用背景
        "disabled_fg": "#6c757d",     # 禁用前景
        "highlight_bg": "#e9ecef",    # 高亮背景
        "highlight_fg": "#212529",    # 高亮前景
        "selected_bg": "#0d6efd",     # 选中背景
        "selected_fg": "#ffffff",     # 选中前景
        "progress_bg": "#e9ecef",     # 进度条背景
        "progress_fg": "#0d6efd",     # 进度条前景
        "header_bg": "#f1f3f5",       # 表头背景
        "alternate_row": "#f8f9fa",   # 表格交替行
        "card_bg": "#ffffff",         # 卡片背景
        "shadow": "#00000022"         # 阴影颜色
    }

    # 深色主题颜色定义
    DARK = {
        "background": "#212529",      # 背景色
        "foreground": "#f8f9fa",      # 前景色
        "accent": "#0d6efd",          # 强调色
        "accent_hover": "#0b5ed7",    # 强调色悬停
        "success": "#20c997",         # 成功色
        "warning": "#ffc107",         # 警告色
        "error": "#dc3545",           # 错误色
        "info": "#0dcaf0",            # 信息色
        "border": "#495057",          # 边框色
        "input_bg": "#343a40",        # 输入框背景
        "hover_bg": "#343a40",        # 悬停背景
        "active_bg": "#495057",       # 激活背景
        "disabled_bg": "#343a40",     # 禁用背景
        "disabled_fg": "#6c757d",     # 禁用前景
        "highlight_bg": "#343a40",    # 高亮背景
        "highlight_fg": "#f8f9fa",    # 高亮前景
        "selected_bg": "#0d6efd",     # 选中背景
        "selected_fg": "#ffffff",     # 选中前景
        "progress_bg": "#343a40",     # 进度条背景
        "progress_fg": "#0d6efd",     # 进度条前景
        "header_bg": "#343a40",       # 表头背景
        "alternate_row": "#2b3035",   # 表格交替行
        "card_bg": "#343a40",         # 卡片背景
        "shadow": "#00000055"         # 阴影颜色
    }

    # 字体定义 - 根据操作系统选择合适的字体
    if platform.system() == "Windows":
        FONT_FAMILY = "微软雅黑"
    elif platform.system() == "Darwin":  # macOS
        FONT_FAMILY = "PingFang SC"
    else:  # Linux和其他系统
        FONT_FAMILY = "Noto Sans CJK SC"

    # 字体大小 - 使用系统默认字体大小
    FONT_SIZE_SMALL = 8    # 系统默认小字体
    FONT_SIZE_NORMAL = 9   # 系统默认标准字体
    FONT_SIZE_LARGE = 11   # 系统默认大字体
    FONT_SIZE_XLARGE = 12  # 系统默认超大字体

    # 账号列表和表格专用字体大小（保持较大以提高可读性）
    TABLE_FONT_SIZE = 13   # 表格字体大小
    ACCOUNT_LIST_FONT_SIZE = 13  # 账号列表字体大小

    # 边距定义 - 增加空间感
    PADDING_SMALL = 4
    PADDING_NORMAL = 8
    PADDING_LARGE = 12

    # 圆角定义
    CORNER_RADIUS = 6

    @classmethod
    def apply(cls, root, theme_mode="light"):
        """
        应用主题到根窗口

        Args:
            root: Tkinter根窗口
            theme_mode: 主题模式，可选值为"light"或"dark"
        """
        # 选择主题颜色
        theme = cls.LIGHT if theme_mode == "light" else cls.DARK

        # 创建样式对象
        style = ttk.Style(root)

        # 设置主题基础
        try:
            style.theme_use("clam")  # clam主题支持更多自定义选项
        except tk.TclError:
            pass  # 如果主题不可用，忽略错误

        # 配置根窗口
        root.configure(background=theme["background"])

        # 配置通用样式 - 使用系统默认字体大小
        style.configure(".",
                        background=theme["background"],
                        foreground=theme["foreground"],
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_NORMAL),
                        borderwidth=1)

        # 配置标签样式
        style.configure("TLabel",
                        background=theme["background"],
                        foreground=theme["foreground"],
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_NORMAL))

        # 配置按钮样式 - 原生外观风格按钮
        # 获取系统默认按钮颜色
        default_button_bg = style.lookup('TButton', 'background')
        default_button_fg = style.lookup('TButton', 'foreground')

        style.configure("TButton",
                        background=default_button_bg or theme["background"],
                        foreground=default_button_fg or theme["foreground"],
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_NORMAL),
                        padding=(cls.PADDING_NORMAL, cls.PADDING_SMALL),
                        relief="raised",
                        borderwidth=1)

        # 按钮悬停和激活状态
        style.map("TButton",
                  background=[("active", theme["hover_bg"]),
                              ("pressed", theme["active_bg"]),
                              ("disabled", theme["disabled_bg"])],
                  foreground=[("disabled", theme["disabled_fg"])],
                  relief=[("pressed", "sunken"),
                         ("active", "raised")])

        # 配置标题标签样式
        style.configure("Title.TLabel",
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_LARGE, "bold"))

        # 配置强调按钮样式 - 使用原生外观但增加视觉重量
        style.configure("Accent.TButton",
                        background=default_button_bg or theme["background"],
                        foreground=default_button_fg or theme["foreground"],
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_NORMAL, "bold"),
                        relief="raised",
                        borderwidth=2)
        style.map("Accent.TButton",
                 background=[("active", theme["hover_bg"]),
                            ("pressed", theme["active_bg"])],
                 relief=[("pressed", "sunken"),
                        ("active", "raised")])

        # 配置成功按钮样式 - 保持原生外观
        style.configure("Success.TButton",
                        background=default_button_bg or theme["background"],
                        foreground=default_button_fg or theme["foreground"],
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_NORMAL, "bold"),
                        relief="raised",
                        borderwidth=1)

        # 配置警告按钮样式 - 保持原生外观
        style.configure("Warning.TButton",
                        background=default_button_bg or theme["background"],
                        foreground=default_button_fg or theme["foreground"],
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_NORMAL, "bold"),
                        relief="raised",
                        borderwidth=1)

        # 配置错误按钮样式
        style.configure("Error.TButton",
                        background=theme["error"],
                        foreground="white")

        # 配置信息按钮样式
        style.configure("Info.TButton",
                        background=theme["info"],
                        foreground="white")

        # 配置树状视图样式 - 现代化表格，账号列表和表格使用较大字体
        style.configure("Treeview",
                        background=theme["input_bg"],
                        foreground=theme["foreground"],
                        fieldbackground=theme["input_bg"],
                        font=(cls.FONT_FAMILY, cls.TABLE_FONT_SIZE),  # 使用表格专用字体大小
                        rowheight=int(cls.TABLE_FONT_SIZE * 2.2),  # 根据表格字体调整行高
                        borderwidth=0)

        # 树状视图选中状态
        style.map("Treeview",
                  background=[("selected", theme["selected_bg"])],
                  foreground=[("selected", theme["selected_fg"])])

        # 树状视图表头 - 账号列表和表格使用较大字体
        style.configure("Treeview.Heading",
                        background=theme["header_bg"],
                        foreground=theme["foreground"],
                        font=(cls.FONT_FAMILY, cls.TABLE_FONT_SIZE, "bold"),  # 使用表格专用字体大小
                        relief="flat",
                        borderwidth=1)

        # 配置进度条样式 - 现代风格进度条
        style.configure("TProgressbar",
                        background=theme["progress_fg"],
                        troughcolor=theme["progress_bg"],
                        borderwidth=0,
                        thickness=10)  # 增加厚度

        # 配置彩色进度条样式
        style.configure("green.Horizontal.TProgressbar",
                        background=theme["success"],
                        troughcolor=theme["progress_bg"])

        style.configure("blue.Horizontal.TProgressbar",
                        background=theme["info"],
                        troughcolor=theme["progress_bg"])

        style.configure("orange.Horizontal.TProgressbar",
                        background=theme["warning"],
                        troughcolor=theme["progress_bg"])

        style.configure("red.Horizontal.TProgressbar",
                        background=theme["error"],
                        troughcolor=theme["progress_bg"])

        # 配置标签框架样式 - 添加圆角效果
        style.configure("TLabelframe",
                        background=theme["background"],
                        foreground=theme["foreground"],
                        borderwidth=1,
                        relief="solid")

        style.configure("TLabelframe.Label",
                        background=theme["background"],
                        foreground=theme["foreground"],
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_NORMAL, "bold"))

        # 配置标签页样式 - 现代标签页
        style.configure("TNotebook",
                        background=theme["background"],
                        foreground=theme["foreground"],
                        tabmargins=[2, 5, 2, 0],
                        borderwidth=0)

        style.configure("TNotebook.Tab",
                        background=theme["background"],
                        foreground=theme["foreground"],
                        padding=[cls.PADDING_LARGE, cls.PADDING_SMALL],
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_NORMAL),
                        borderwidth=0)

        # 配置选中的标签页样式
        style.map("TNotebook.Tab",
                  background=[("selected", theme["accent"])],
                  foreground=[("selected", "white")],
                  expand=[("selected", [1, 1, 1, 0])])

        # 配置状态栏样式
        style.configure("StatusBar.TLabel",
                        background=theme["border"],
                        foreground=theme["foreground"],
                        relief=tk.SUNKEN,
                        padding=cls.PADDING_SMALL)

        # 配置标题栏样式
        style.configure("TitleBar.TFrame",
                        background=theme["accent"])

        style.configure("TitleBar.TLabel",
                        background=theme["accent"],
                        foreground="white",
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_LARGE, "bold"))

        # 配置工具栏样式
        style.configure("Toolbar.TFrame",
                        background=theme["background"],
                        relief="flat")

        # 配置分隔线样式
        style.configure("TSeparator",
                        background=theme["border"])

        # 配置输入框样式
        style.configure("TEntry",
                        fieldbackground=theme["input_bg"],
                        foreground=theme["foreground"],
                        padding=cls.PADDING_SMALL,
                        borderwidth=1)

        # 配置下拉框样式
        style.configure("TCombobox",
                        fieldbackground=theme["input_bg"],
                        foreground=theme["foreground"],
                        padding=cls.PADDING_SMALL,
                        arrowsize=cls.FONT_SIZE_NORMAL)

        style.map("TCombobox",
                  fieldbackground=[("readonly", theme["input_bg"])],
                  foreground=[("readonly", theme["foreground"])])

        # 配置复选框样式
        style.configure("TCheckbutton",
                        background=theme["background"],
                        foreground=theme["foreground"],
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_NORMAL))

        # 配置单选框样式
        style.configure("TRadiobutton",
                        background=theme["background"],
                        foreground=theme["foreground"],
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_NORMAL))

        # 配置卡片样式 - 用于创建带阴影的面板
        style.configure("Card.TFrame",
                        background=theme["card_bg"],
                        relief="solid",
                        borderwidth=1)

        # 配置标题样式
        style.configure("Heading.TLabel",
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_LARGE, "bold"),
                        background=theme["background"],
                        foreground=theme["foreground"],
                        padding=(0, cls.PADDING_NORMAL))

        # 配置副标题样式
        style.configure("Subheading.TLabel",
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_NORMAL, "bold"),
                        background=theme["background"],
                        foreground=theme["foreground"],
                        padding=(0, cls.PADDING_SMALL))

        # 配置强调文本样式
        style.configure("Accent.TLabel",
                        foreground=theme["accent"],
                        background=theme["background"],
                        font=(cls.FONT_FAMILY, cls.FONT_SIZE_NORMAL))

        # 配置成功文本样式
        style.configure("Success.TLabel",
                        foreground=theme["success"],
                        background=theme["background"])

        # 配置警告文本样式
        style.configure("Warning.TLabel",
                        foreground=theme["warning"],
                        background=theme["background"])

        # 配置错误文本样式
        style.configure("Error.TLabel",
                        foreground=theme["error"],
                        background=theme["background"])

    @staticmethod
    def create_rounded_rectangle(canvas, x1, y1, x2, y2, radius=10, **kwargs):
        """
        在画布上创建圆角矩形

        Args:
            canvas: 画布对象
            x1, y1: 左上角坐标
            x2, y2: 右下角坐标
            radius: 圆角半径
            **kwargs: 传递给canvas.create_polygon的其他参数

        Returns:
            创建的多边形ID
        """
        # 确保半径不超过矩形的一半
        radius = min(radius, (x2-x1)//2, (y2-y1)//2)

        # 创建圆角矩形的点
        points = [
            x1+radius, y1,
            x2-radius, y1,
            x2, y1,
            x2, y1+radius,
            x2, y2-radius,
            x2, y2,
            x2-radius, y2,
            x1+radius, y2,
            x1, y2,
            x1, y2-radius,
            x1, y1+radius,
            x1, y1
        ]

        return canvas.create_polygon(points, **kwargs, smooth=True)

    @classmethod
    def create_theme_switch(cls, parent, variable, command=None):
        """
        创建主题切换开关

        Args:
            parent: 父容器
            variable: 绑定的变量，True表示深色模式，False表示浅色模式
            command: 切换时的回调函数

        Returns:
            创建的开关框架
        """
        # 创建框架
        switch_frame = ttk.Frame(parent)

        # 创建简单的按钮切换
        theme_frame = ttk.Frame(switch_frame)
        theme_frame.pack(pady=5)

        # 浅色模式按钮
        light_button = ttk.Button(
            theme_frame,
            text="浅色模式",
            command=lambda: cls._set_theme_mode(variable, False, command),
            style="Accent.TButton" if not variable.get() else "TButton"
        )
        light_button.pack(side=tk.LEFT, padx=5)

        # 深色模式按钮
        dark_button = ttk.Button(
            theme_frame,
            text="深色模式",
            command=lambda: cls._set_theme_mode(variable, True, command),
            style="Accent.TButton" if variable.get() else "TButton"
        )
        dark_button.pack(side=tk.LEFT, padx=5)

        # 更新按钮状态的函数
        def update_buttons(*_):
            if variable.get():  # 深色模式
                dark_button.configure(style="Accent.TButton")
                light_button.configure(style="TButton")
            else:  # 浅色模式
                light_button.configure(style="Accent.TButton")
                dark_button.configure(style="TButton")

        # 初始更新按钮状态
        update_buttons()

        # 绑定变量变化事件
        variable.trace_add("write", update_buttons)

        return switch_frame

    @staticmethod
    def _set_theme_mode(variable, value, command=None):
        """
        设置主题模式

        Args:
            variable: 主题变量
            value: 设置的值
            command: 回调函数
        """
        variable.set(value)
        if command:
            command()

    @classmethod
    def apply_scrollbar_style(cls, scrollbar, theme_mode="light"):
        """
        应用滚动条样式

        Args:
            scrollbar: 滚动条对象
            theme_mode: 主题模式，可选值为"light"或"dark"
        """
        theme = cls.LIGHT if theme_mode == "light" else cls.DARK

        scrollbar.configure(
            background=theme["background"],
            troughcolor=theme["border"],
            activebackground=theme["accent"],
            highlightbackground=theme["background"],
            highlightcolor=theme["background"],
            relief="flat",
            borderwidth=0,
            width=10
        )


