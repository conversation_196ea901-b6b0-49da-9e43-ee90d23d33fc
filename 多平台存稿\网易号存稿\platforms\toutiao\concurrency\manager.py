"""
头条号并发管理器模块 - 负责管理多账号并发处理
基于网易号的并发实现模式，适配头条号平台特性
"""

import os
import time
import queue
import random
import threading
import traceback
from typing import List, Dict, Any, Optional, Callable
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime

from ..processor import ToutiaoDraftProcessor
from ....common.concurrent_pool import get_shared_pool


class ToutiaoConcurrentManager:
    """头条号并发管理器类，负责管理多账号并发处理"""

    def __init__(self,
                 account_dir: str,
                 processed_dir: str,
                 processed_covers_dir: str,
                 archive_completed: bool = True,
                 headless_mode: bool = False,
                 draft_limit: int = 0,
                 loop_limit: int = 0,
                 log_callback: Callable = None,
                 screenshots_dir: str = None,
                 max_workers: int = 5,
                 random_video_allocation: bool = True):
        """
        初始化头条号并发管理器

        Args:
            account_dir: 账号目录
            processed_dir: 已处理视频目录
            processed_covers_dir: 已处理封面目录
            archive_completed: 是否归档已完成视频
            headless_mode: 是否使用无头模式
            draft_limit: 存稿数量限制，0表示不限制
            loop_limit: 循环次数限制，0表示不限制
            log_callback: 日志回调函数
            screenshots_dir: 截图保存目录
            max_workers: 最大工作线程数
            random_video_allocation: 是否随机分配视频，True为随机分配，False为顺序分配
        """
        self.account_dir = account_dir
        self.processed_dir = processed_dir
        self.processed_covers_dir = processed_covers_dir
        self.archive_completed = archive_completed
        self.headless_mode = headless_mode
        self.draft_limit = draft_limit
        self.loop_limit = loop_limit
        self.max_workers = max_workers
        self.log_callback = log_callback
        self.screenshots_dir = screenshots_dir
        self.progress_callback = None
        self.random_video_allocation = random_video_allocation

        # 初始化状态
        self.is_running = False
        self.executor = None
        self.futures = {}
        self.video_queue = queue.Queue()
        self.account_progress = {}

    def log(self, message: str) -> None:
        """
        记录日志

        Args:
            message: 日志消息
        """
        if self.log_callback:
            self.log_callback(f"[头条号并发] {message}")
        else:
            print(f"[头条号并发] {message}")

    def start(self, accounts: List[str]) -> bool:
        """
        开始并发处理

        Args:
            accounts: 账号列表

        Returns:
            是否成功启动
        """
        if self.is_running:
            self.log("任务已在运行中")
            return False

        if not accounts:
            self.log("没有指定要处理的账号")
            return False

        self.is_running = True

        try:
            # 初始化线程池
            self.executor = ThreadPoolExecutor(max_workers=min(self.max_workers, len(accounts)))

            # 初始化账号进度
            for account in accounts:
                self.account_progress[account] = {
                    "progress": 0,
                    "status": "等待中",
                    "last_update": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "details": "准备处理",
                    "successful_drafts": 0
                }

            # 准备视频队列
            self._prepare_video_queue()

            # 提交任务
            self.futures = {}
            for account in accounts:
                future = self.executor.submit(self._process_account, account)
                self.futures[future] = account

            # 启动监控线程
            threading.Thread(target=self._monitor_progress, daemon=True).start()

            self.log(f"已启动 {len(accounts)} 个账号的头条号并发处理")
            return True

        except Exception as e:
            self.log(f"启动并发处理失败: {str(e)}")
            self.is_running = False
            return False

    def stop(self) -> None:
        """停止并发处理"""
        if not self.is_running:
            return

        self.is_running = False
        self.log("正在停止所有头条号任务...")

        # 关闭线程池
        if self.executor:
            self.executor.shutdown(wait=False)
            self.executor = None

        # 清空队列
        while not self.video_queue.empty():
            try:
                self.video_queue.get_nowait()
            except:
                pass

        self.log("所有头条号任务已停止")

    def get_progress(self) -> Dict[str, Dict[str, Any]]:
        """
        获取处理进度

        Returns:
            账号进度字典
        """
        return self.account_progress

    def set_progress_callback(self, callback: Callable) -> None:
        """
        设置进度回调函数

        Args:
            callback: 进度回调函数，接收账号名称、进度百分比和状态
        """
        self.progress_callback = callback

    def _prepare_video_queue(self) -> None:
        """准备视频队列"""
        try:
            # 清空队列
            while not self.video_queue.empty():
                try:
                    self.video_queue.get_nowait()
                except queue.Empty:
                    break

            # 检查目录是否存在
            if not os.path.exists(self.processed_dir):
                os.makedirs(self.processed_dir, exist_ok=True)
                self.log(f"创建视频目录: {self.processed_dir}")
                return

            # 获取所有视频文件
            video_files = []
            for file in os.listdir(self.processed_dir):
                file_path = os.path.join(self.processed_dir, file)
                # 跳过目录和非视频文件
                if os.path.isdir(file_path):
                    continue

                # 检查文件扩展名
                ext = os.path.splitext(file)[1].lower()
                if ext in ['.mp4', '.mov', '.avi', '.wmv', '.flv', '.mkv']:
                    # 检查文件是否可以访问
                    try:
                        # 尝试打开文件以确认它是可访问的
                        with open(file_path, 'rb'):
                            # 不需要读取内容，只是检查文件是否可以打开
                            pass
                        video_files.append(file_path)
                    except Exception as e:
                        self.log(f"无法访问文件 {file}: {str(e)}")
                        continue

            # 根据配置决定是否随机打乱文件顺序
            if self.random_video_allocation:
                random.shuffle(video_files)
                sort_method = "随机排序"
            else:
                video_files.sort()
                sort_method = "顺序排序"

            # 将视频文件放入队列
            for video_file in video_files:
                self.video_queue.put(video_file)

            self.log(f"已准备 {len(video_files)} 个视频文件到队列中（{sort_method}）")

        except Exception as e:
            self.log(f"准备视频队列失败: {str(e)}")

    def _process_account(self, account: str) -> bool:
        """
        处理单个账号

        Args:
            account: 账号名称

        Returns:
            是否处理成功
        """
        try:
            self.log(f"开始处理账号: {account}")
            self._update_account_progress(account, 5, "初始化", "正在初始化处理器...")

            # 创建头条号存稿处理器
            processor = ToutiaoDraftProcessor(
                account_dir=self.account_dir,
                processed_dir=self.processed_dir,
                processed_covers_dir=self.processed_covers_dir,
                archive_completed=self.archive_completed,
                headless_mode=self.headless_mode,
                draft_limit=self.draft_limit,
                loop_limit=self.loop_limit,
                log_callback=lambda msg: self._account_log(account, msg),
                screenshots_dir=self.screenshots_dir,
                random_video_allocation=self.random_video_allocation,
                add_draft_detail_callback=lambda acc, video_info: 
                    self._update_draft_detail(acc, video_info)
            )

            self._update_account_progress(account, 10, "已初始化", "处理器初始化完成")

            # 使用共享视频队列处理视频
            success = self._process_videos_from_queue(account, processor)

            if success:
                self._update_account_progress(account, 100, "完成", f"账号 {account} 处理完成")
                self.log(f"账号 {account} 处理完成")
            else:
                self._update_account_progress(account, 0, "失败", f"账号 {account} 处理失败")
                self.log(f"账号 {account} 处理失败")

            return success

        except Exception as e:
            error_msg = f"处理账号 {account} 时发生错误: {str(e)}"
            self.log(error_msg)
            self._update_account_progress(account, 0, "错误", error_msg)
            return False

    def _process_videos_from_queue(self, account: str, processor: ToutiaoDraftProcessor) -> bool:
        """
        从共享队列处理视频

        Args:
            account: 账号名称
            processor: 头条号处理器

        Returns:
            是否处理成功
        """
        try:
            draft_count = 0
            processed_count = 0

            while self.is_running:
                # 检查存稿限制
                if self.draft_limit > 0 and draft_count >= self.draft_limit:
                    self.log(f"账号 {account} 已达到存稿限制: {self.draft_limit}")
                    break

                # 从队列获取视频
                try:
                    video_path = self.video_queue.get(timeout=5.0)
                except queue.Empty:
                    self.log(f"账号 {account} 视频队列为空，处理完成")
                    break

                processed_count += 1
                video_file = os.path.basename(video_path)
                self.log(f"账号 {account} 正在处理视频: {video_file}")

                # 更新进度
                progress = min(20 + (processed_count * 70 // max(1, self.draft_limit or 10)), 90)
                self._update_account_progress(account, progress, "处理中", f"正在处理: {video_file}")

                # 查找对应的封面文件
                cover_path = self._find_cover_for_video(video_path)

                # 处理单个视频
                success = processor._process_single_video(video_path, cover_path)

                if success:
                    draft_count += 1
                    self.log(f"账号 {account} 视频存稿成功: {video_file}")

                    # 更新成功存稿数量
                    self._update_account_progress(
                        account, progress, "处理中",
                        f"已成功存稿 {draft_count} 个视频",
                        {"successful_drafts": draft_count}
                    )

                    # 归档成功的视频
                    if self.archive_completed:
                        self._archive_video(video_path, cover_path, success=True)
                else:
                    self.log(f"账号 {account} 视频存稿失败: {video_file}")

                    # 归档失败的视频
                    if self.archive_completed:
                        self._archive_video(video_path, cover_path, success=False)

                # 标记任务完成
                self.video_queue.task_done()

                # 等待一段时间再处理下一个视频
                if self.is_running:
                    wait_time = random.uniform(2.0, 5.0)
                    time.sleep(wait_time)

            # 最终更新进度
            final_progress = {
                "progress": 100,
                "status": f"完成 ({draft_count})",
                "details": f"共成功存稿 {draft_count} 个视频",
                "successful_drafts": draft_count
            }
            self._update_account_progress(account, 100, f"完成 ({draft_count})",
                                        f"共成功存稿 {draft_count} 个视频", final_progress)

            return True

        except Exception as e:
            self.log(f"账号 {account} 处理视频队列时发生错误: {str(e)}")
            return False

    def _find_cover_for_video(self, video_path: str) -> Optional[str]:
        """
        查找视频对应的封面文件

        Args:
            video_path: 视频文件路径

        Returns:
            封面文件路径，如果没有找到则返回None
        """
        try:
            video_name = os.path.splitext(os.path.basename(video_path))[0]
            cover_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.gif']

            for ext in cover_extensions:
                cover_path = os.path.join(self.processed_covers_dir, video_name + ext)
                if os.path.exists(cover_path):
                    return cover_path

            return None

        except Exception as e:
            self.log(f"查找封面文件失败: {str(e)}")
            return None

    def _archive_video(self, video_path: str, cover_path: Optional[str], success: bool) -> None:
        """
        归档视频文件

        Args:
            video_path: 视频文件路径
            cover_path: 封面文件路径
            success: 是否成功处理
        """
        try:
            import shutil

            # 确定归档目录
            archive_dir = os.path.join(
                os.path.dirname(video_path),
                "completed" if success else "failed"
            )
            os.makedirs(archive_dir, exist_ok=True)

            # 归档视频文件
            video_name = os.path.basename(video_path)
            archive_video_path = os.path.join(archive_dir, video_name)
            shutil.move(video_path, archive_video_path)

            # 归档封面文件（如果存在）
            if cover_path and os.path.exists(cover_path):
                cover_name = os.path.basename(cover_path)
                archive_cover_path = os.path.join(archive_dir, cover_name)
                shutil.move(cover_path, archive_cover_path)

        except Exception as e:
            self.log(f"归档文件失败: {str(e)}")

    def _update_account_progress(self, account: str, progress: int, status: str,
                               details: str, progress_data: Optional[Dict] = None) -> None:
        """
        更新账号进度

        Args:
            account: 账号名称
            progress: 进度百分比
            status: 状态
            details: 详细信息
            progress_data: 额外的进度数据
        """
        if account in self.account_progress:
            # 获取旧的成功存稿数量
            old_successful_drafts = self.account_progress[account].get("successful_drafts", 0)
            old_status = self.account_progress[account].get("status", "")

            # 更新进度信息
            self.account_progress[account].update({
                "progress": progress,
                "status": status,
                "last_update": time.strftime("%Y-%m-%d %H:%M:%S"),
                "details": details
            })

            # 如果提供了额外的进度数据，合并进去
            if progress_data:
                self.account_progress[account].update(progress_data)

            # 获取新的成功存稿数量
            successful_drafts = self.account_progress[account].get("successful_drafts", 0)

            # 只在存稿成功数量变化或状态变化时记录日志，减少日志输出
            if successful_drafts != old_successful_drafts or status != old_status:
                self.log(f"账号 {account} 进度更新: 进度={progress}%, 状态={status}, 存稿成功={successful_drafts}")
        else:
            # 账号不在进度字典中，创建新的进度记录
            self.account_progress[account] = {
                "progress": progress,
                "status": status,
                "last_update": time.strftime("%Y-%m-%d %H:%M:%S"),
                "details": details,
                "successful_drafts": progress_data.get("successful_drafts", 0) if progress_data else 0
            }

            self.log(f"账号 {account} 首次进度记录: 进度={progress}%, 状态={status}")

        # 调用进度回调函数
        if self.progress_callback:
            self.progress_callback(account, progress, status, details, self.account_progress[account])

    def _account_log(self, account: str, message: str) -> None:
        """
        账号日志

        Args:
            account: 账号名称
            message: 日志消息
        """
        self.log(f"[{account}] {message}")

    def _update_draft_detail(self, account: str, video_info: Dict[str, Any]) -> None:
        """
        更新存稿详情

        Args:
            account: 账号名称
            video_info: 视频信息
        """
        # 这里可以添加存稿详情的更新逻辑
        # 例如保存到文件或数据库
        pass

    def _monitor_progress(self) -> None:
        """监控处理进度"""
        while self.is_running:
            try:
                # 检查所有任务是否完成
                if self.futures:
                    completed_futures = []
                    for future in self.futures:
                        if future.done():
                            completed_futures.append(future)

                    # 移除已完成的任务
                    for future in completed_futures:
                        account = self.futures.pop(future)
                        try:
                            result = future.result()
                            if result:
                                self.log(f"账号 {account} 处理完成")
                            else:
                                self.log(f"账号 {account} 处理失败")
                        except Exception as e:
                            self.log(f"账号 {account} 处理异常: {str(e)}")

                    # 如果所有任务都完成了，停止监控
                    if not self.futures:
                        self.log("所有头条号账号处理完成")
                        self.is_running = False
                        break

                # 等待一段时间再检查
                time.sleep(2)

            except Exception as e:
                self.log(f"监控进度时发生错误: {str(e)}")
                time.sleep(5)
