{"timestamp": "2025-07-16T18:57:30.782847", "diagnosis_results": {"1. 主程序UI初始化检查": {"status": "success", "details": ["✅ 主UI初始化成功", "   主窗口标题: 🚀 多平台存稿工具 - 现代化版本", "   主窗口几何: 1400x900+153+83", "   主窗口状态: normal", "   主窗口子组件数量: 1", "     子组件0: <PERSON><PERSON> (可见: 0)"]}, "2. 组件创建状态检查": {"status": "warning", "details": ["   ✅ 顶部标题栏 (header_bar): 已创建", "     类型: <PERSON><PERSON><PERSON><PERSON>", "     ℹ️ 非tkinter组件", "   ✅ 标签页容器 (notebook): 已创建", "     类型: Notebook", "     存在状态: 1", "     父容器: .!frame.!frame2.!frame2.!frame.!frame", "     几何信息: 1x1 at (0,0)", "     可见性: viewable=0, mapped=0", "   ✅ 账号表格 (accounts_table): 已创建", "     类型: AccountsTable", "     ℹ️ 非tkinter组件", "   ✅ 开始按钮 (start_button): 已创建", "     类型: <PERSON><PERSON>", "     存在状态: 1", "     父容器: .!frame.!frame2.!frame2.!frame.!frame.!notebook.!frame.!frame.!frame.!frame.!frame", "     几何信息: 1x1 at (0,0)", "     可见性: viewable=0, mapped=0", "   ✅ 停止按钮 (stop_button): 已创建", "     类型: <PERSON><PERSON>", "     存在状态: 1", "     父容器: .!frame.!frame2.!frame2.!frame.!frame.!notebook.!frame.!frame.!frame.!frame.!frame", "     几何信息: 1x1 at (0,0)", "     可见性: viewable=0, mapped=0", "   ✅ 状态文本 (status_text): 已创建", "     类型: Label", "     存在状态: 1", "     父容器: .!frame.!frame3.!frame.!frame", "     几何信息: 1x1 at (0,0)", "     可见性: viewable=0, mapped=0", "   ✅ 日志管理器 (log_manager): 已创建", "     类型: LogManager", "     ℹ️ 非tkinter组件", "   ✅ 设置管理器 (settings_manager): 已创建", "     类型: SettingsManager", "     ℹ️ 非tkinter组件", "   ❌ 平台侧边栏 (platform_sidebar): 不存在", "   ❌ 统计卡片 (stats_card): 不存在"]}, "3. 组件布局管理器检查": {"status": "warning", "details": ["   检查到 108 个容器", "   📦 root: 管理器=wm", "   📦 root.child0(Frame): 管理器=pack", "     pack选项: {'in': <tkinter.Tk object .>, 'anchor': 'center', 'expand': 1, 'fill': 'both', 'ipadx': 0, 'ipady': 0, 'padx': 0, 'pady': 0, 'side': 'top'}", "     ⚠️ 组件不可见但已设置管理器", "   📦 root.child0.child0(Frame): 管理器=pack", "     pack选项: {'in': <tkinter.ttk.Frame object .!frame>, 'anchor': 'center', 'expand': 0, 'fill': 'x', 'ipadx': 0, 'ipady': 0, 'padx': 15, 'pady': 15, 'side': 'top'}", "     ⚠️ 组件不可见但已设置管理器", "   📦 root.child0.child0.child0(Frame): 管理器=pack", "     pack选项: {'in': <tkinter.ttk.Frame object .!frame.!frame>, 'anchor': 'center', 'expand': 1, 'fill': 'x', 'ipadx': 0, 'ipady': 0, 'padx': (20, 10), 'pady': 10, 'side': 'left'}", "     ⚠️ 组件不可见但已设置管理器", "   📦 root.child0.child0.child0.child0(Label): 管理器=pack", "     pack选项: {'in': <tkinter.ttk.Frame object .!frame.!frame.!frame>, 'anchor': 'center', 'expand': 0, 'fill': 'none', 'ipadx': 0, 'ipady': 0, 'padx': 0, 'pady': 0, 'side': 'left'}", "     ⚠️ 组件不可见但已设置管理器", "   📦 root.child0.child0.child0.child1(Label): 管理器=pack", "     pack选项: {'in': <tkinter.ttk.Frame object .!frame.!frame.!frame>, 'anchor': 'center', 'expand': 0, 'fill': 'none', 'ipadx': 0, 'ipady': 0, 'padx': (10, 0), 'pady': 0, 'side': 'left'}", "     ⚠️ 组件不可见但已设置管理器", "   📦 root.child0.child0.child1(Frame): 管理器=pack", "     pack选项: {'in': <tkinter.ttk.Frame object .!frame.!frame>, 'anchor': 'center', 'expand': 0, 'fill': 'none', 'ipadx': 0, 'ipady': 0, 'padx': (10, 20), 'pady': 10, 'side': 'right'}", "     ⚠️ 组件不可见但已设置管理器", "   📦 root.child0.child0.child1.child0(Frame): 管理器=pack", "     pack选项: {'in': <tkinter.ttk.Frame object .!frame.!frame.!frame2>, 'anchor': 'center', 'expand': 0, 'fill': 'none', 'ipadx': 0, 'ipady': 0, 'padx': 10, 'pady': 5, 'side': 'right'}", "     ⚠️ 组件不可见但已设置管理器", "   📦 root.child0.child0.child1.child0.child0(Frame): 管理器=pack", "     pack选项: {'in': <tkinter.ttk.Frame object .!frame.!frame.!frame2.!frame>, 'anchor': 'center', 'expand': 0, 'fill': 'x', 'ipadx': 0, 'ipady': 0, 'padx': 0, 'pady': (0, 5), 'side': 'top'}", "     ⚠️ 组件不可见但已设置管理器", "   📦 root.child0.child0.child1.child0.child0.child0(Label): 管理器=pack", "     pack选项: {'in': <tkinter.ttk.Frame object .!frame.!frame.!frame2.!frame.!frame>, 'anchor': 'center', 'expand': 0, 'fill': 'none', 'ipadx': 0, 'ipady': 0, 'padx': (0, 10), 'pady': 0, 'side': 'left'}", "     ⚠️ 组件不可见但已设置管理器"]}, "4. 组件可见性属性检查": {"status": "success", "details": ["   检查到 170 个tkinter组件", "     ❌ 检查 root 可见性失败: '_tkinter.tkapp' object has no attribute 'winfo_visible'", "     ❌ 检查 root.Frame0 可见性失败: 'Frame' object has no attribute 'winfo_visible'", "     ❌ 检查 root.Frame0.Frame0 可见性失败: 'Frame' object has no attribute 'winfo_visible'", "     ❌ 检查 root.Frame0.Frame0.Frame0 可见性失败: 'Frame' object has no attribute 'winfo_visible'", "     ❌ 检查 root.Frame0.Frame0.Frame0.Label0 可见性失败: 'Label' object has no attribute 'winfo_visible'", "     ❌ 检查 root.Frame0.Frame0.Frame0.Label1 可见性失败: 'Label' object has no attribute 'winfo_visible'", "     ❌ 检查 root.Frame0.Frame0.Frame1 可见性失败: 'Frame' object has no attribute 'winfo_visible'", "     ❌ 检查 root.Frame0.Frame0.Frame1.Frame0 可见性失败: 'Frame' object has no attribute 'winfo_visible'", "     ❌ 检查 root.Frame0.Frame0.Frame1.Frame0.Frame0 可见性失败: 'Frame' object has no attribute 'winfo_visible'", "     ❌ 检查 root.Frame0.Frame0.Frame1.Frame0.Frame0.Label0 可见性失败: 'Label' object has no attribute 'winfo_visible'", "     ❌ 检查 root.Frame0.Frame0.Frame1.Frame0.Frame0.Label1 可见性失败: 'Label' object has no attribute 'winfo_visible'", "     ❌ 检查 root.Frame0.Frame0.Frame1.Frame0.Frame0.Label2 可见性失败: 'Label' object has no attribute 'winfo_visible'", "     ❌ 检查 root.Frame0.Frame1 可见性失败: 'Frame' object has no attribute 'winfo_visible'", "     ❌ 检查 root.Frame0.Frame1.Frame0 可见性失败: 'Frame' object has no attribute 'winfo_visible'", "     ❌ 检查 root.Frame0.Frame1.Frame0.Frame0 可见性失败: 'Frame' object has no attribute 'winfo_visible'", "     ❌ 检查 root.Frame0.Frame1.Frame0.Frame0.Frame0 可见性失败: 'Frame' object has no attribute 'winfo_visible'", "     ❌ 检查 root.Frame0.Frame1.Frame0.Frame0.Frame0.Canvas0 可见性失败: 'Canvas' object has no attribute 'winfo_visible'", "     ❌ 检查 root.Frame0.Frame1.Frame0.Frame0.Frame0.Canvas0.Frame0 可见性失败: 'Frame' object has no attribute 'winfo_visible'", "     ❌ 检查 root.Frame0.Frame1.Frame0.Frame0.Frame0.Canvas0.Frame0.Labelframe0 可见性失败: 'Labelframe' object has no attribute 'winfo_visible'", "     ❌ 检查 root.Frame0.Frame1.Frame0.Frame0.Frame0.Canvas0.Frame0.Labelframe0.Frame0 可见性失败: 'Frame' object has no attribute 'winfo_visible'", "   📊 可见性统计: 可见=0, 不可见=0"]}, "5. 容器嵌套结构检查": {"status": "warning", "details": ["   🏗️ 容器嵌套结构分析:", "📦 Tk (管理器:wm, 可见:1, 尺寸:1400x900)", "  📦 Frame (管理器:pack, 可见:0, 尺寸:1x1)", "    📦 Frame (管理器:pack, 可见:0, 尺寸:1x1)", "      📦 Frame (管理器:pack, 可见:0, 尺寸:1x1)", "        📦 Label (管理器:pack, 可见:0, 尺寸:1x1)", "        📦 Label (管理器:pack, 可见:0, 尺寸:1x1)", "      📦 Frame (管理器:pack, 可见:0, 尺寸:1x1)", "        📦 Frame (管理器:pack, 可见:0, 尺寸:1x1)", "          📦 Frame (管理器:pack, 可见:0, 尺寸:1x1)", "    📦 Frame (管理器:pack, 可见:0, 尺寸:1x1)", "      📦 Frame (管理器:pack, 可见:0, 尺寸:1x1)", "        📦 Frame (管理器:pack, 可见:0, 尺寸:1x1)", "          📦 Frame (管理器:pack, 可见:0, 尺寸:1x1)", "      📦 Frame (管理器:pack, 可见:0, 尺寸:1x1)", "        📦 Frame (管理器:pack, 可见:0, 尺寸:1x1)", "          📦 Frame (管理器:pack, 可见:0, 尺寸:1x1)", "    📦 Frame (管理器:pack, 可见:0, 尺寸:1x1)", "      📦 Frame (管理器:pack, 可见:0, 尺寸:1x1)", "        📦 Frame (管理器:pack, 可见:0, 尺寸:1x1)", "          📦 Label (管理器:pack, 可见:0, 尺寸:1x1)", "        📦 Frame (管理器:pack, 可见:0, 尺寸:1x1)", "          📦 Label (管理器:pack, 可见:0, 尺寸:1x1)", "   📏 最大嵌套深度: 15", "   ⚠️ 嵌套层次过深，可能影响性能"]}, "6. 几何管理器冲突检查": {"status": "warning", "details": ["   🔍 检查几何管理器冲突:", "   ⚠️ root.Frame0.Frame1.Frame1.Frame0.Frame0.Notebook0.Frame0.Frame0 中发现几何管理器冲突: {'wm', 'pack'}", "     - Frame: pack", "     - Frame: pack", "     - Menu: wm", "   📊 发现冲突数量: 1"]}, "7. 组件尺寸和位置检查": {"status": "success", "details": ["   📐 notebook: 尺寸=1x1, 位置=(0,0)", "   📐 start_button: 尺寸=1x1, 位置=(0,0)", "   📐 stop_button: 尺寸=1x1, 位置=(0,0)", "   📐 status_text: 尺寸=1x1, 位置=(0,0)", "   📊 异常统计: 零尺寸=0, 负位置=0"]}, "8. 实时可见性测试": {"status": "success", "details": ["   🧪 执行实时可见性测试...", "     ✅ notebook: 实时可见", "     ✅ start_button: 实时可见", "     ✅ stop_button: 实时可见", "   📊 实时可见性率: 100.0% (3/3)", "   ✅ 实时可见性率良好", "   ✅ 可见性测试界面已创建"]}}, "missing_components": ["平台侧边栏 (platform_sidebar)", "统计卡片 (stats_card)"], "invisible_components": ["标签页容器 (notebook)", "开始按钮 (start_button)", "停止按钮 (stop_button)", "状态文本 (status_text)"], "layout_issues": ["root.child0(Frame): 不可见但已设置管理器", "root.child0.child0(<PERSON><PERSON>): 不可见但已设置管理器", "root.child0.child0.child0(Frame): 不可见但已设置管理器", "root.child0.child0.child0.child0(Label): 不可见但已设置管理器", "root.child0.child0.child0.child1(Label): 不可见但已设置管理器", "root.child0.child0.child1(<PERSON><PERSON>): 不可见但已设置管理器", "root.child0.child0.child1.child0(Frame): 不可见但已设置管理器", "root.child0.child0.child1.child0.child0(Frame): 不可见但已设置管理器", "root.child0.child0.child1.child0.child0.child0(Label): 不可见但已设置管理器", "嵌套深度过深: 15", "root.Frame0.Frame1.Frame1.Frame0.Frame0.Notebook0.Frame0.Frame0: 几何管理器冲突 {'wm', 'pack'}"], "summary": {"total_issues": 17, "missing_components_count": 2, "invisible_components_count": 4, "layout_issues_count": 11}}