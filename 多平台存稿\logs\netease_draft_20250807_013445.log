[2025-08-07 01:34:50] [INFO] 🚀 启动多平台存稿工具...
[2025-08-07 01:34:50] [INFO] ✅ 定时任务管理器UI初始化完成
[2025-08-07 01:34:51] [INFO] ✅ 定时任务管理标签页创建完成
[2025-08-07 01:38:13] [INFO] ✅ 定时任务管理器UI清理完成
25-08-07 01:34:50] [INFO] 已从toutiao平台加载存稿详情数据，共 84 个账号
[2025-08-07 01:34:51] [INFO] 🚀 定时任务调度器已启动
[2025-08-07 01:34:51] [DEBUG] 🔄 调度器主循环开始
[2025-08-07 01:34:51] [INFO] 已确保所有必要目录存在
[2025-08-07 01:34:51] [DEBUG] 🔍 [调试] 当前平台: toutiao
[2025-08-07 01:34:51] [DEBUG] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-08-07 01:34:51] [DEBUG] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-08-07 01:34:51] [SUCCESS] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-08-07 01:34:51] [DEBUG] 🔍 [调试] 更新账号树 - 当前平台: toutiao
[2025-08-07 01:34:51] [DEBUG] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-08-07 01:34:51] [DEBUG] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-08-07 01:34:51] [INFO] 已加载账号数据: 85 条记录
[2025-08-07 01:34:51] [DEBUG] 🔍 [调试] 账号数据为空或未加载
[2025-08-07 01:34:51] [SUCCESS] ✅ 初始UI更新完成
[2025-08-07 01:35:13] [INFO] 已选择账号: ***********
[2025-08-07 01:36:00] [INFO] 已选择账号: ***********
[2025-08-07 01:36:02] [DEBUG] 🚀 开始为账号 *********** 存稿
[2025-08-07 01:36:02] [INFO] 已确保所有必要目录存在
[2025-08-07 01:36:02] [INFO] 使用单线程模式处理账号
[2025-08-07 01:36:02] [DEBUG] 🔍 [DEBUG] 准备启动单线程任务，参数: ***********
[2025-08-07 01:36:02] [DEBUG] 🔍 [DEBUG] _single_task_thread 开始执行，参数: ***********
[2025-08-07 01:36:02] [DEBUG] 🔍 [DEBUG] 单线程任务已启动
[2025-08-07 01:36:02] [DEBUG] 📋 开始处理单个账号: ***********
[2025-08-07 01:36:02] [INFO] 视频分配方式: 随机分配
[2025-08-07 01:36:02] [DEBUG] 开始处理账号: ***********
[2025-08-07 01:36:02] [SUCCESS] 正在使用Cookie登录账号: ***********
[2025-08-07 01:36:02] [SUCCESS] ✅ Cookie预检测通过，启动浏览器验证...
[2025-08-07 01:36:08] [SUCCESS] 开始查询账号数据: *********** [toutiao] 浏览器驱动创建成功，使用端口: 9515
[2025-08-07 01:36:12] [SUCCESS] 正在加载Cookie文件: D:/网易号全自动/头条号账号\***********.txt
[2025-08-07 01:36:26] [SUCCESS] ✅ Cookie添加完成
[2025-08-07 01:36:27] [SUCCESS] ✅ 头条号快速登录成功 (等待0.5秒)
[2025-08-07 01:36:27] [SUCCESS] 登录成功，开始处理视频: ***********
[2025-08-07 01:36:27] [INFO] 找到 652 个视频文件
[2025-08-07 01:36:27] [INFO] 已随机打乱 652 个视频文件的顺序
[2025-08-07 01:36:27] [DEBUG] 开始处理视频: 听风的蚕：直接干翻“大boss”？.mp4
[2025-08-07 01:36:27] [DEBUG] 🎬 开始头条号视频存稿流程
[2025-08-07 01:36:27] [DEBUG] 🔍 正在定位视频菜单...
[2025-08-07 01:36:27] [SUCCESS] ✅ 已点击视频菜单
[2025-08-07 01:36:31] [INFO] 🌐 当前不在上传页面，导航到视频上传页面...
[2025-08-07 01:36:34] [SUCCESS] ✅ 已导航到视频上传页面
[2025-08-07 01:36:34] [INFO] 📁 准备上传视频: 听风的蚕：直接干翻“大boss”？.mp4
[2025-08-07 01:36:34] [DEBUG] 🔍 找到上传按钮，准备点击...
[2025-08-07 01:36:34] [SUCCESS] ✅ 已点击上传视频按钮
[2025-08-07 01:36:36] [SUCCESS] ✅ 视频文件已选择并开始上传
[2025-08-07 01:36:36] [DEBUG] ⏳ 开始监控视频上传状态...
[2025-08-07 01:36:36] [INFO] 📊 当前上传状态: 上传中… 0%
[2025-08-07 01:36:36] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 01:36:42] [INFO] 📊 当前上传状态: 上传中… 17.14%
[2025-08-07 01:36:42] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 01:36:47] [INFO] 📊 当前上传状态: 上传中… 35.41%
[2025-08-07 01:36:47] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 01:36:52] [INFO] 📊 当前上传状态: 上传中… 53.68%
[2025-08-07 01:36:52] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 01:36:57] [INFO] 📊 当前上传状态: 上传中… 72.06%
[2025-08-07 01:36:57] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 01:37:02] [INFO] 📊 当前上传状态: 上传中… 93.31%
[2025-08-07 01:37:02] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 01:37:07] [INFO] 📊 当前上传状态: 上传中… 100%
[2025-08-07 01:37:07] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 01:37:12] [SUCCESS] 📊 当前上传状态: 上传成功
[2025-08-07 01:37:12] [SUCCESS] ✅ 视频上传成功！
[2025-08-07 01:37:12] [DEBUG] 📷 开始上传视频封面...
[2025-08-07 01:37:12] [SUCCESS] ✅ 已点击上传封面按钮
[2025-08-07 01:37:14] [SUCCESS] ✅ 已选择本地上传
[2025-08-07 01:37:15] [DEBUG] 🔍 查找文件输入框...
[2025-08-07 01:37:15] [SUCCESS] ✅ 找到 3 个文件输入框，使用最后一个作为封面输入框
[2025-08-07 01:37:15] [SUCCESS] ✅ 封面文件已选择
[2025-08-07 01:37:15] [DEBUG] 🔍 开始检测封面canvas加载状态...
[2025-08-07 01:37:15] [SUCCESS] ✅ Canvas已加载并有内容 (888x499)
[2025-08-07 01:37:15] [SUCCESS] ✅ 封面canvas已加载完成
[2025-08-07 01:37:15] [SUCCESS] ✅ 已点击第一个确定按钮
[2025-08-07 01:37:17] [SUCCESS] ✅ 已点击第二个确定按钮（通过文字查找）
[2025-08-07 01:37:17] [INFO] ⏳ 等待上传弹窗消失...
[2025-08-07 01:37:17] [SUCCESS] ⏳ 检测到'上传中'状态，等待上传完成...
[2025-08-07 01:37:20] [SUCCESS] ✅ '上传中'状态消失，弹窗已关闭
[2025-08-07 01:37:22] [SUCCESS] ✅ 视频封面上传完成
[2025-08-07 01:37:22] [DEBUG] 💾 开始保存草稿...
[2025-08-07 01:37:22] [SUCCESS] ✅ 已点击存草稿按钮
[2025-08-07 01:37:25] [WARNING] ✅ 草稿保存成功！检测到'全部保存成功'提示
[2025-08-07 01:37:25] [SUCCESS] ✅ 头条号视频存稿成功！
[2025-08-07 01:37:25] [SUCCESS] ✅ 视频存稿成功: 听风的蚕：直接干翻“大boss”？.mp4
[2025-08-07 01:37:25] [DEBUG] 开始处理视频: 过独木桥的节奏盒子.mp4
[2025-08-07 01:37:25] [DEBUG] 🎬 开始头条号视频存稿流程
[2025-08-07 01:37:25] [DEBUG] 🔍 正在定位视频菜单...
[2025-08-07 01:37:25] [SUCCESS] ✅ 已点击视频菜单
[2025-08-07 01:37:28] [INFO] 🌐 当前不在上传页面，导航到视频上传页面...
[2025-08-07 01:37:32] [SUCCESS] ✅ 已导航到视频上传页面
[2025-08-07 01:37:32] [INFO] 📁 准备上传视频: 过独木桥的节奏盒子.mp4
[2025-08-07 01:37:32] [DEBUG] 🔍 找到上传按钮，准备点击...
[2025-08-07 01:37:32] [SUCCESS] ✅ 已点击上传视频按钮
[2025-08-07 01:37:34] [SUCCESS] ✅ 视频文件已选择并开始上传
[2025-08-07 01:37:34] [DEBUG] ⏳ 开始监控视频上传状态...
[2025-08-07 01:37:35] [INFO] 📊 当前上传状态: 上传中… 0%
[2025-08-07 01:37:35] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 01:37:40] [SUCCESS] 📊 当前上传状态: 上传成功
[2025-08-07 01:37:40] [SUCCESS] ✅ 视频上传成功！
[2025-08-07 01:37:40] [DEBUG] 📷 开始上传视频封面...
[2025-08-07 01:37:40] [SUCCESS] ✅ 已点击上传封面按钮
[2025-08-07 01:37:42] [SUCCESS] ✅ 已选择本地上传
[2025-08-07 01:37:43] [DEBUG] 🔍 查找文件输入框...
[2025-08-07 01:37:43] [SUCCESS] ✅ 找到 3 个文件输入框，使用最后一个作为封面输入框
[2025-08-07 01:37:43] [SUCCESS] ✅ 封面文件已选择
[2025-08-07 01:37:43] [DEBUG] 🔍 开始检测封面canvas加载状态...
[2025-08-07 01:37:43] [SUCCESS] ✅ Canvas已加载并有内容 (888x499)
[2025-08-07 01:37:43] [SUCCESS] ✅ 封面canvas已加载完成
[2025-08-07 01:37:43] [SUCCESS] ✅ 已点击第一个确定按钮
[2025-08-07 01:37:45] [SUCCESS] ✅ 已点击第二个确定按钮（通过文字查找）
[2025-08-07 01:37:45] [INFO] ⏳ 等待上传弹窗消失...
[2025-08-07 01:37:45] [SUCCESS] ⏳ 检测到'上传中'状态，等待上传完成...
[2025-08-07 01:37:47] [SUCCESS] ✅ '上传中'状态消失，弹窗已关闭
[2025-08-07 01:37:49] [SUCCESS] ✅ 视频封面上传完成
[2025-08-07 01:37:49] [DEBUG] 💾 开始保存草稿...
[2025-08-07 01:37:50] [SUCCESS] ✅ 已点击存草稿按钮
[2025-08-07 01:37:53] [WARNING] ✅ 草稿保存成功！检测到'全部保存成功'提示
[2025-08-07 01:37:53] [SUCCESS] ✅ 头条号视频存稿成功！
[2025-08-07 01:37:53] [SUCCESS] ✅ 视频存稿成功: 过独木桥的节奏盒子.mp4
[2025-08-07 01:37:53] [INFO] 已达到存稿限制 2，停止处理
[2025-08-07 01:37:53] [INFO] [toutiao] 已释放端口: 9515
[2025-08-07 01:37:55] [SUCCESS] 账号 *********** 处理完成
[2025-08-07 01:37:55] [SUCCESS] 账号 *********** 处理完成
[2025-08-07 01:37:55] [SUCCESS] 已更新账号 *********** 的进度信息: 存稿成功数=0, 状态=
[2025-08-07 01:37:55] [SUCCESS] 已更新账号 *********** 状态到账号管理器: 
[2025-08-07 01:37:55] [SUCCESS] 已更新账号 *********** 的进度信息: 存稿成功数=0, 状态=
[2025-08-07 01:37:55] [SUCCESS] 已更新账号 *********** 状态到账号管理器: 
[2025-08-07 01:37:55] [SUCCESS] ✅ 账号 *********** 处理成功
[2025-08-07 01:37:55] [SUCCESS] 📊 任务进度完成: 1/1
[2025-08-07 01:37:55] [SUCCESS] ✅ 存稿任务已完成
[2025-08-07 01:37:57] [INFO] 打开存稿设置对话框
[2025-08-07 01:38:09] [SUCCESS] ✅ 存稿设置已成功保存
[2025-08-07 01:38:12] [DEBUG] 正在关闭程序，清理后台资源...
[2025-08-07 01:38:13] [DEBUG] 🔄 调度器主循环结束
[2025-08-07 01:38:13] [DEBUG] 正在关闭任务执行器...
[2025-08-07 01:38:13] [INFO] 任务执行器已关闭
[2025-08-07 01:38:13] [INFO] ⏹️ 定时任务调度器已停止
[2025-08-07 01:38:13] [SUCCESS] ✅ 定时任务管理器UI清理完成
[2025-08-07 01:38:13] [SUCCESS] ✅ 调度器管理器清理完成
[2025-08-07 01:38:13] [DEBUG] 🧹 开始清理所有资源...
[2025-08-07 01:38:13] [DEBUG] 正在停止并发管理器...
[2025-08-07 01:38:13] [SUCCESS] ✅ 运行中的任务已停止
[2025-08-07 01:38:13] [SUCCESS] ✅ 定时器线程已清理（升级版系统自动管理）
[2025-08-07 01:38:14] [SUCCESS] ✅ Chrome进程强制清理完成
[2025-08-07 01:38:14] [SUCCESS] ✅ 浏览器进程已清理
[2025-08-07 01:38:15] [SUCCESS] ✅ 日志处理线程已清理
[2025-08-07 01:38:15] [SUCCESS] ✅ 存稿处理器已清理
[2025-08-07 01:38:15] [SUCCESS] ✅ 线程池已清理
[2025-08-07 01:38:15] [DEBUG] 正在停止串行刷新工作线程...
[2025-08-07 01:38:18] [WARNING] ⚠️ 仍有 2 个线程未结束
[2025-08-07 01:38:18] [SUCCESS] ✅ 临时文件已清理
[2025-08-07 01:38:18] [SUCCESS] ✅ 资源清理完成
[2025-08-07 01:38:18] [SUCCESS] ✅ 设置已静默保存
[2025-08-07 01:38:18] [SUCCESS] 程序清理完成，正在退出...
