"""
头条号工作线程模块 - 负责执行具体的存稿任务
基于网易号的Worker模式，适配头条号平台特性
"""

import os
import time
import queue
import random
import threading
from typing import Dict, Any, Optional, Callable, List

from ..processor import ToutiaoDraftProcessor
from ....browser.driver import DriverManager
from ....browser.actions import BrowserActions
from ..login import ToutiaoLogin


class ToutiaoWorker:
    """头条号工作线程类，负责执行具体的存稿任务"""

    def __init__(self,
                 account: str,
                 account_dir: str,
                 processed_dir: str,
                 processed_covers_dir: str,
                 video_queue: queue.Queue,
                 archive_completed: bool = True,
                 headless_mode: bool = False,
                 draft_limit: int = 0,
                 loop_limit: int = 0,
                 log_callback: Callable = None,
                 progress_callback: Callable = None,
                 screenshots_dir: str = None):
        """
        初始化头条号工作线程

        Args:
            account: 账号名称
            account_dir: 账号目录
            processed_dir: 已处理视频目录
            processed_covers_dir: 已处理封面目录
            video_queue: 视频队列
            archive_completed: 是否归档已完成视频
            headless_mode: 是否使用无头模式
            draft_limit: 存稿数量限制，0表示不限制
            loop_limit: 循环次数限制，0表示不限制
            log_callback: 日志回调函数
            progress_callback: 进度回调函数
            screenshots_dir: 截图保存目录
        """
        self.account = account
        self.account_dir = account_dir
        self.processed_dir = processed_dir
        self.processed_covers_dir = processed_covers_dir
        self.video_queue = video_queue
        self.archive_completed = archive_completed
        self.headless_mode = headless_mode
        self.draft_limit = draft_limit
        self.loop_limit = loop_limit
        self.log_callback = log_callback
        self.progress_callback = progress_callback
        self.screenshots_dir = screenshots_dir

        # 初始化状态
        self.is_running = False
        self.thread = None
        self.driver_manager = None
        self.browser_actions = None
        self.driver = None

        # 创建截图目录
        if not self.screenshots_dir:
            self.screenshots_dir = os.path.join(
                os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 
                "screenshots"
            )
        os.makedirs(self.screenshots_dir, exist_ok=True)

    def log(self, message: str) -> None:
        """
        记录日志

        Args:
            message: 日志消息
        """
        if self.log_callback:
            self.log_callback(f"[头条号-{self.account}] {message}")
        else:
            print(f"[头条号-{self.account}] {message}")

    def update_progress(self, progress: int, status: str, details: str, progress_data=None) -> None:
        """
        更新进度

        Args:
            progress: 进度百分比
            status: 状态
            details: 详细信息
            progress_data: 完整的进度数据字典（可选）
        """
        if self.progress_callback:
            if progress_data and isinstance(progress_data, dict):
                # 如果提供了完整的进度数据，直接传递
                self.progress_callback(self.account, progress_data)
            else:
                # 否则使用单独的参数
                self.progress_callback(self.account, progress, status, details)

    def start(self) -> None:
        """启动工作线程"""
        if self.is_running:
            self.log("工作线程已在运行中")
            return

        self.is_running = True
        self.thread = threading.Thread(target=self._run, daemon=True)
        self.thread.start()
        self.log("工作线程已启动")

    def stop(self) -> None:
        """停止工作线程"""
        if not self.is_running:
            return

        self.is_running = False
        self.log("正在停止工作线程...")

        # 清理浏览器资源
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass
            self.driver = None

        if self.driver_manager:
            try:
                self.driver_manager.quit_driver()
            except:
                pass
            self.driver_manager = None

        self.log("工作线程已停止")

    def _run(self) -> None:
        """工作线程主循环"""
        try:
            self.log(f"开始处理账号: {self.account}")
            self.update_progress(5, "初始化", "正在初始化浏览器...")

            # 初始化浏览器驱动
            if not self._initialize_browser():
                self.log("初始化浏览器失败")
                self.update_progress(0, "失败", "初始化浏览器失败")
                return

            # 登录账号
            if not self._login_account():
                self.log("登录账号失败")
                self.update_progress(0, "失败", "登录账号失败")
                return

            # 更新进度
            self.update_progress(10, "已登录", "登录成功")

            # 创建浏览器操作对象
            self.browser_actions = BrowserActions(self.driver, lambda msg: self.log(msg))

            # 初始化计数器
            draft_count = 0  # 已成功存稿数量
            loop_count = 0   # 已循环次数

            # 显示限制设置
            if self.draft_limit > 0:
                self.log(f"已设置存稿数量限制: {self.draft_limit} 个视频")
            if self.loop_limit > 0:
                self.log(f"已设置循环次数限制: {self.loop_limit} 次")

            # 循环处理视频
            while self.is_running:
                # 检查循环次数限制
                if self.loop_limit > 0 and loop_count >= self.loop_limit:
                    self.log(f"已达到循环次数限制: {self.loop_limit} 次")
                    break

                # 检查存稿数量限制
                if self.draft_limit > 0 and draft_count >= self.draft_limit:
                    self.log(f"已达到存稿数量限制: {self.draft_limit} 个视频")
                    break

                # 从队列获取视频
                try:
                    video_path = self.video_queue.get(timeout=5.0)
                except queue.Empty:
                    self.log("视频队列为空，等待中...")
                    time.sleep(5)
                    continue

                # 处理视频
                video_file = os.path.basename(video_path)
                self.log(f"正在处理视频: {video_file}")

                # 更新进度
                self.update_progress(20, "处理中", f"正在处理视频: {video_file}")

                # 查找对应的封面文件
                cover_path = self._find_cover_for_video(video_file)

                # 创建头条号存稿处理器
                processor = ToutiaoDraftProcessor(
                    account_dir=self.account_dir,
                    processed_dir=self.processed_dir,
                    processed_covers_dir=self.processed_covers_dir,
                    archive_completed=self.archive_completed,
                    headless_mode=self.headless_mode,
                    draft_limit=1,  # 每次只处理一个视频
                    loop_limit=1,   # 每次只循环一次
                    log_callback=lambda msg: self.log(msg)
                )

                # 设置处理器的驱动和浏览器操作对象
                processor.driver_manager = self.driver_manager
                processor.driver = self.driver
                processor.browser_actions = self.browser_actions

                # 执行存稿
                result = processor._process_single_video(video_path, cover_path)

                if result:
                    draft_count += 1
                    self.log(f"视频 {video_file} 存稿成功")

                    # 更新进度
                    progress = min(10 + (draft_count * 80 // max(1, self.draft_limit or 10)), 90)
                    self.update_progress(
                        progress, "处理中", 
                        f"已成功存稿 {draft_count} 个视频",
                        {"successful_drafts": draft_count}
                    )

                    # 归档成功的视频
                    if self.archive_completed:
                        self._archive_video(video_path, cover_path, success=True)
                else:
                    self.log(f"视频 {video_file} 存稿失败")

                    # 更新进度
                    self.update_progress(
                        min(10 + (draft_count * 90 // max(1, self.draft_limit or 10)), 100),
                        "警告",
                        f"视频 {video_file} 存稿失败"
                    )

                    # 将视频放回队列
                    self.video_queue.put(video_path)

                # 标记任务完成
                self.video_queue.task_done()

                loop_count += 1

            # 任务完成
            self.log(f"账号 {self.account} 存稿任务完成，共存稿 {draft_count} 个视频")

            # 创建包含成功存稿数量的进度数据
            progress_data = {
                "progress": 100,
                "status": "完成",
                "details": f"共存稿 {draft_count} 个视频",
                "successful_drafts": draft_count
            }

            # 更新进度，包含成功存稿数量
            self.update_progress(100, f"完成 ({draft_count})", f"共存稿 {draft_count} 个视频", progress_data)

            # 确保进度回调函数被调用，实时更新UI
            if hasattr(self, 'progress_callback') and self.progress_callback:
                # 直接调用回调函数，确保UI立即更新
                self.progress_callback(self.account, 100, f"完成 ({draft_count})",
                                      f"共存稿 {draft_count} 个视频", progress_data)

        except Exception as e:
            self.log(f"工作线程发生错误: {str(e)}")
            self.update_progress(0, "错误", f"发生错误: {str(e)}")

        finally:
            # 清理资源
            self.stop()

    def _initialize_browser(self) -> bool:
        """
        初始化浏览器驱动

        Returns:
            是否初始化成功
        """
        try:
            # 创建驱动管理器
            self.driver_manager = DriverManager(
                log_callback=lambda msg: self.log(msg)
            )

            # 创建驱动
            self.driver = self.driver_manager.create_driver(
                headless=self.headless_mode,
                account=self.account
            )
            if not self.driver:
                self.log("创建浏览器驱动失败")
                return False

            self.log("浏览器驱动初始化成功")
            return True

        except Exception as e:
            self.log(f"初始化浏览器驱动失败: {str(e)}")
            return False

    def _login_account(self) -> bool:
        """
        登录账号

        Returns:
            是否登录成功
        """
        try:
            # 创建头条号登录对象
            login = ToutiaoLogin(
                log_callback=lambda msg: self.log(msg)
            )

            # 执行登录 - 使用Cookie登录
            cookie_path = os.path.join(self.account_dir, f"{self.account}.txt")
            success, driver = login.login_with_cookies(
                cookie_path=cookie_path,
                headless=self.headless_mode,
                account=self.account
            )

            if success and driver:
                self.driver = driver

            if success:
                self.log(f"账号 {self.account} 登录成功")
                return True
            else:
                self.log(f"账号 {self.account} 登录失败")
                return False

        except Exception as e:
            self.log(f"登录账号时发生错误: {str(e)}")
            return False

    def _find_cover_for_video(self, video_file: str) -> Optional[str]:
        """
        查找视频对应的封面文件

        Args:
            video_file: 视频文件名

        Returns:
            封面文件路径，如果没有找到则返回None
        """
        try:
            video_name = os.path.splitext(video_file)[0]
            cover_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.gif']

            for ext in cover_extensions:
                cover_path = os.path.join(self.processed_covers_dir, video_name + ext)
                if os.path.exists(cover_path):
                    self.log(f"找到封面文件: {os.path.basename(cover_path)}")
                    return cover_path

            self.log(f"未找到视频 {video_file} 的封面文件")
            return None

        except Exception as e:
            self.log(f"查找封面文件失败: {str(e)}")
            return None

    def _archive_video(self, video_path: str, cover_path: Optional[str], success: bool) -> None:
        """
        归档视频文件

        Args:
            video_path: 视频文件路径
            cover_path: 封面文件路径
            success: 是否成功处理
        """
        try:
            import shutil

            # 确定归档目录
            archive_dir = os.path.join(
                os.path.dirname(video_path),
                "completed" if success else "failed"
            )
            os.makedirs(archive_dir, exist_ok=True)

            # 归档视频文件
            video_name = os.path.basename(video_path)
            archive_video_path = os.path.join(archive_dir, video_name)

            # 使用复制而不是移动，以防其他线程仍在使用原始文件
            shutil.copy2(video_path, archive_video_path)
            self.log(f"已归档视频: {video_name}")

            # 检查是否有其他线程正在使用此视频
            # 这里使用一个简单的延迟删除策略，给其他线程留出时间
            time.sleep(2)  # 等待2秒，让其他可能的线程有时间处理
            try:
                os.remove(video_path)
                self.log(f"已删除原始视频: {video_name}")
            except Exception as e:
                self.log(f"删除原始视频失败(可能被其他线程使用): {str(e)}")

            # 归档封面文件（如果存在）
            if cover_path and os.path.exists(cover_path):
                cover_name = os.path.basename(cover_path)
                archive_cover_path = os.path.join(archive_dir, cover_name)

                try:
                    # 使用复制而不是移动，以防其他线程仍在使用原始文件
                    shutil.copy2(cover_path, archive_cover_path)
                    self.log(f"已归档封面: {cover_name}")

                    # 检查是否有其他线程正在使用此封面
                    # 这里使用一个简单的延迟删除策略，给其他线程留出时间
                    time.sleep(2)  # 等待2秒，让其他可能的线程有时间处理
                    try:
                        os.remove(cover_path)
                        self.log(f"已删除原始封面: {cover_name}")
                    except Exception as e:
                        self.log(f"删除原始封面失败(可能被其他线程使用): {str(e)}")
                except Exception as e:
                    self.log(f"归档封面失败: {str(e)}")

        except Exception as e:
            self.log(f"归档文件失败: {str(e)}")

    def is_alive(self) -> bool:
        """
        检查工作线程是否存活

        Returns:
            线程是否存活
        """
        return self.thread and self.thread.is_alive()

    def join(self, timeout: Optional[float] = None) -> None:
        """
        等待工作线程结束

        Args:
            timeout: 超时时间
        """
        if self.thread:
            self.thread.join(timeout)
