[2025-08-04 00:46:50] [INFO] 🚀 启动多平台存稿工具...
[2025-08-04 00:46:50] [INFO] ✅ 定时任务管理器UI初始化完成
[2025-08-04 00:46:51] [INFO] ✅ 定时任务管理标签页创建完成
[2025-08-04 00:48:18] [INFO] ✅ 定时任务管理器UI清理完成
25-08-04 00:46:50] [INFO] 已从toutiao平台加载存稿详情数据，共 83 个账号
[2025-08-04 00:46:51] [INFO] 🚀 定时任务调度器已启动
[2025-08-04 00:46:51] [DEBUG] 🔄 调度器主循环开始
[2025-08-04 00:46:51] [INFO] 已确保所有必要目录存在
[2025-08-04 00:46:52] [DEBUG] 🔍 [调试] 当前平台: toutiao
[2025-08-04 00:46:52] [DEBUG] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-08-04 00:46:52] [DEBUG] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-08-04 00:46:52] [SUCCESS] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-08-04 00:46:52] [DEBUG] 🔍 [调试] 更新账号树 - 当前平台: toutiao
[2025-08-04 00:46:52] [DEBUG] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-08-04 00:46:52] [DEBUG] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-08-04 00:46:52] [INFO] 已加载账号数据: 85 条记录
[2025-08-04 00:46:52] [DEBUG] 🔍 [调试] 账号数据为空或未加载
[2025-08-04 00:46:52] [SUCCESS] ✅ 初始UI更新完成
[2025-08-04 00:46:58] [INFO] 已选择账号: ***********
[2025-08-04 00:46:59] [DEBUG] 🚀 开始为账号 *********** 存稿
[2025-08-04 00:46:59] [INFO] 已确保所有必要目录存在
[2025-08-04 00:46:59] [INFO] 使用单线程模式处理账号
[2025-08-04 00:46:59] [DEBUG] 🔍 [DEBUG] 准备启动单线程任务，参数: ***********
[2025-08-04 00:46:59] [DEBUG] 🔍 [DEBUG] 单线程任务已启动
[2025-08-04 00:46:59] [DEBUG] 🔍 [DEBUG] _single_task_thread 开始执行，参数: ***********
[2025-08-04 00:46:59] [DEBUG] 📋 开始处理单个账号: ***********
[2025-08-04 00:46:59] [INFO] 视频分配方式: 随机分配
[2025-08-04 00:46:59] [DEBUG] 开始处理账号: ***********
[2025-08-04 00:46:59] [SUCCESS] 正在使用Cookie登录账号: ***********
[2025-08-04 00:46:59] [SUCCESS] ✅ Cookie预检测通过，启动浏览器验证...
[2025-08-04 00:47:06] [SUCCESS] 开始查询账号数据: *********** [toutiao] 浏览器驱动创建成功，使用端口: 9515
[2025-08-04 00:47:09] [SUCCESS] 正在加载Cookie文件: D:/网易号全自动/头条号账号\***********.txt
[2025-08-04 00:47:11] [SUCCESS] ✅ Cookie添加完成
[2025-08-04 00:47:13] [SUCCESS] ✅ 头条号快速登录成功 (等待0.5秒)
[2025-08-04 00:47:13] [SUCCESS] 登录成功，开始处理视频: ***********
[2025-08-04 00:47:13] [INFO] 找到 3 个视频文件
[2025-08-04 00:47:13] [INFO] 已随机打乱 3 个视频文件的顺序
[2025-08-04 00:47:13] [DEBUG] 开始处理视频: 19岁少年错付千元轻生，司机否认：他要套现！死无对证引哗然！.mp4
[2025-08-04 00:47:13] [DEBUG] 🎬 开始头条号视频存稿流程
[2025-08-04 00:47:13] [DEBUG] 🔍 正在定位视频菜单...
[2025-08-04 00:47:14] [SUCCESS] ✅ 已点击视频菜单
[2025-08-04 00:47:17] [INFO] 🌐 当前不在上传页面，导航到视频上传页面...
[2025-08-04 00:47:21] [SUCCESS] ✅ 已导航到视频上传页面
[2025-08-04 00:47:21] [INFO] 📁 准备上传视频: 19岁少年错付千元轻生，司机否认：他要套现！死无对证引哗然！.mp4
[2025-08-04 00:47:21] [DEBUG] 🔍 找到上传按钮，准备点击...
[2025-08-04 00:47:21] [SUCCESS] ✅ 已点击上传视频按钮
[2025-08-04 00:47:23] [SUCCESS] ✅ 视频文件已选择并开始上传
[2025-08-04 00:47:23] [DEBUG] ⏳ 开始监控视频上传状态...
[2025-08-04 00:47:23] [INFO] 📊 当前上传状态: 上传中… 0%
[2025-08-04 00:47:23] [DEBUG] ⏳ 视频正在上传中...
[2025-08-04 00:47:28] [INFO] 📊 当前上传状态: 上传中… 61.89%
[2025-08-04 00:47:28] [DEBUG] ⏳ 视频正在上传中...
[2025-08-04 00:47:33] [SUCCESS] 📊 当前上传状态: 上传成功
[2025-08-04 00:47:33] [SUCCESS] ✅ 视频上传成功！
[2025-08-04 00:47:33] [DEBUG] 📷 开始上传视频封面...
[2025-08-04 00:47:33] [INFO] 📁 封面文件信息: 19岁少年错付千元轻生，司机否认：他要套现！死无对证引哗然！.jpg (508308 bytes)
[2025-08-04 00:47:34] [SUCCESS] ✅ 已点击上传封面按钮
[2025-08-04 00:47:36] [SUCCESS] ✅ 已选择本地上传
[2025-08-04 00:47:37] [DEBUG] 🔍 尝试方案1: 点击指定上传区域
[2025-08-04 00:47:45] [WARNING] ⚠️ 方案1失败: Message: 
Stacktrace:
	GetHandleVerifier [0x0x7ff7b7a26f75+76917]
	GetHandleVerifier [0x0x7ff7b7a26fd0+77008]
	(No symbol) [0x0x7ff7b77d9dea]
	(No symbol) [0x0x7ff7b7830256]
	(No symbol) [0x0x7ff7b783050c]
	(No symbol) [0x0x7ff7b7883887]
	(No symbol) [0x0x7ff7b78584af]
	(No symbol) [0x0x7ff7b788065c]
	(No symbol) [0x0x7ff7b7858243]
	(No symbol) [0x0x7ff7b7821431]
	(No symbol) [0x0x7ff7b78221c3]
	GetHandleVerifier [0x0x7ff7b7cfd2ad+3051437]
	GetHandleVerifier [0x0x7ff7b7cf7903+3028483]
	GetHandleVerifier [0x0x7ff7b7d1589d+3151261]
	GetHandleVerifier [0x0x7ff7b7a4183e+185662]
	GetHandleVerifier [0x0x7ff7b7a496ff+218111]
	GetHandleVerifier [0x0x7ff7b7a2faf4+112628]
	GetHandleVerifier [0x0x7ff7b7a2fca9+113065]
	GetHandleVerifier [0x0x7ff7b7a16c78+10616]
	BaseThreadInitThunk [0x0x7ffcc2f7e8d7+23]
	RtlUserThreadStart [0x0x7ffcc4e5c34c+44]

[2025-08-04 00:47:45] [DEBUG] 🔍 尝试方案2: 直接查找文件输入
[2025-08-04 00:47:45] [SUCCESS] ✅ 封面文件已选择（方案2）
[2025-08-04 00:47:45] [INFO] ⏳ 等待文件上传处理...
[2025-08-04 00:47:48] [INFO] 📍 当前页面URL: https://mp.toutiao.com/profile_v4/xigua/upload-video
[2025-08-04 00:47:48] [WARNING] ⚠️ 发现错误提示: 
[2025-08-04 00:47:48] [DEBUG] 🔍 开始检测封面canvas加载状态...
[2025-08-04 00:47:48] [ERROR] ❌ 未找到Canvas元素
[2025-08-04 00:47:48] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-04 00:47:49] [ERROR] ❌ 未找到Canvas元素
[2025-08-04 00:47:49] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-04 00:47:50] [ERROR] ❌ 未找到Canvas元素
[2025-08-04 00:47:50] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-04 00:47:51] [ERROR] ❌ 未找到Canvas元素
[2025-08-04 00:47:51] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-04 00:47:52] [ERROR] ❌ 未找到Canvas元素
[2025-08-04 00:47:52] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-04 00:47:53] [ERROR] ❌ 未找到Canvas元素
[2025-08-04 00:47:53] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-04 00:47:54] [ERROR] ❌ 未找到Canvas元素
[2025-08-04 00:47:54] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-04 00:47:55] [ERROR] ❌ 未找到Canvas元素
[2025-08-04 00:47:55] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-04 00:47:56] [ERROR] ❌ 未找到Canvas元素
[2025-08-04 00:47:56] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-04 00:47:57] [ERROR] ❌ 未找到Canvas元素
[2025-08-04 00:47:57] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-04 00:47:58] [ERROR] ❌ 未找到Canvas元素
[2025-08-04 00:47:58] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-04 00:47:59] [ERROR] ❌ 未找到Canvas元素
[2025-08-04 00:47:59] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-04 00:48:00] [ERROR] ❌ 未找到Canvas元素
[2025-08-04 00:48:00] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-04 00:48:01] [ERROR] ❌ 未找到Canvas元素
[2025-08-04 00:48:01] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-04 00:48:02] [ERROR] ❌ 未找到Canvas元素
[2025-08-04 00:48:02] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-04 00:48:03] [ERROR] ❌ canvas加载检测超时（15秒）
[2025-08-04 00:48:03] [WARNING] ⚠️ 封面canvas可能未完全加载，但继续后续操作
[2025-08-04 00:48:06] [WARNING] ⚠️ 未找到确定按钮，封面可能已自动确认
[2025-08-04 00:48:08] [SUCCESS] ✅ 视频封面上传完成
[2025-08-04 00:48:08] [DEBUG] 💾 开始保存草稿...
[2025-08-04 00:48:08] [SUCCESS] ✅ 已点击存草稿按钮
[2025-08-04 00:48:11] [WARNING] ✅ 草稿保存成功！检测到'全部保存成功'提示
[2025-08-04 00:48:11] [SUCCESS] ✅ 头条号视频存稿成功！
[2025-08-04 00:48:11] [SUCCESS] ✅ 视频存稿成功: 19岁少年错付千元轻生，司机否认：他要套现！死无对证引哗然！.mp4
[2025-08-04 00:48:11] [DEBUG] 开始处理视频: 2005年，李讷母亲贴身护士回忆：那些都是瞎说的，她被抓时我.mp4
[2025-08-04 00:48:11] [DEBUG] 🎬 开始头条号视频存稿流程
[2025-08-04 00:48:11] [DEBUG] 🔍 正在定位视频菜单...
[2025-08-04 00:48:11] [SUCCESS] ✅ 已点击视频菜单
[2025-08-04 00:48:14] [INFO] 🌐 当前不在上传页面，导航到视频上传页面...
[2025-08-04 00:48:18] [DEBUG] 正在关闭程序，清理后台资源...
[2025-08-04 00:48:18] [DEBUG] 🔄 调度器主循环结束
[2025-08-04 00:48:18] [DEBUG] 正在关闭任务执行器...
[2025-08-04 00:48:18] [INFO] 任务执行器已关闭
[2025-08-04 00:48:18] [INFO] ⏹️ 定时任务调度器已停止
[2025-08-04 00:48:18] [SUCCESS] ✅ 调度器管理器清理完成
[2025-08-04 00:48:18] [SUCCESS] ✅ 定时任务管理器UI清理完成
[2025-08-04 00:48:18] [DEBUG] 🧹 开始清理所有资源...
[2025-08-04 00:48:18] [DEBUG] ⏹️ 正在停止存稿任务...
[2025-08-04 00:48:18] [INFO] ⏹️ 存稿任务已停止
[2025-08-04 00:48:18] [DEBUG] 正在停止并发管理器...
[2025-08-04 00:48:18] [SUCCESS] ✅ 运行中的任务已停止
[2025-08-04 00:48:18] [SUCCESS] ✅ 定时器线程已清理（升级版系统自动管理）
[2025-08-04 00:48:18] [SUCCESS] ✅ 已导航到视频上传页面
[2025-08-04 00:48:18] [INFO] 📁 准备上传视频: 2005年，李讷母亲贴身护士回忆：那些都是瞎说的，她被抓时我.mp4
[2025-08-04 00:48:18] [DEBUG] 🔍 找到上传按钮，准备点击...
[2025-08-04 00:48:18] [SUCCESS] ✅ 已点击上传视频按钮
[2025-08-04 00:48:19] [SUCCESS] ✅ Chrome进程强制清理完成
[2025-08-04 00:48:19] [SUCCESS] ✅ 浏览器进程已清理
[2025-08-04 00:48:20] [SUCCESS] ✅ 日志处理线程已清理
[2025-08-04 00:48:20] [SUCCESS] ✅ 存稿处理器已清理
[2025-08-04 00:48:20] [SUCCESS] ✅ 线程池已清理
[2025-08-04 00:48:20] [DEBUG] 正在停止串行刷新工作线程...
[2025-08-04 00:48:23] [WARNING] ⚠️ 仍有 3 个线程未结束
[2025-08-04 00:48:23] [SUCCESS] ✅ 临时文件已清理
[2025-08-04 00:48:23] [SUCCESS] ✅ 资源清理完成
[2025-08-04 00:48:23] [SUCCESS] ✅ 设置已静默保存
[2025-08-04 00:48:23] [SUCCESS] 程序清理完成，正在退出...
