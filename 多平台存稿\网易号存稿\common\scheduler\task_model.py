"""
定时任务模型定义
包含任务类型、状态、重复规则等核心数据模型
"""

import uuid
import datetime
from enum import Enum
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
import json


class TaskType(Enum):
    """任务类型枚举"""
    DATA_QUERY_ALL = "data_query_all"          # 查询所有账号数据
    DATA_QUERY_SINGLE = "data_query_single"    # 查询单个账号数据
    DRAFT_UPLOAD = "draft_upload"              # 存稿任务
    COMBINED = "combined"                      # 组合任务（先查询后存稿）


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"        # 等待执行
    RUNNING = "running"        # 正在执行
    COMPLETED = "completed"    # 执行完成
    FAILED = "failed"          # 执行失败
    CANCELLED = "cancelled"    # 已取消
    DISABLED = "disabled"      # 已禁用


class RepeatType(Enum):
    """重复类型枚举"""
    ONCE = "once"              # 一次性任务
    DAILY = "daily"            # 每日重复
    WEEKLY = "weekly"          # 每周重复
    MONTHLY = "monthly"        # 每月重复
    CUSTOM_CRON = "custom_cron"  # 自定义cron表达式


@dataclass
class RepeatRule:
    """重复规则配置"""
    type: RepeatType = RepeatType.ONCE
    interval: int = 1                    # 间隔数量（如每2天、每3周）
    days_of_week: Optional[List[int]] = None  # 周几执行（0=周一，6=周日）
    day_of_month: Optional[int] = None   # 月份中的第几天
    cron_expression: Optional[str] = None # 自定义cron表达式
    end_date: Optional[datetime.datetime] = None  # 结束日期
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {
            'type': self.type.value,
            'interval': self.interval
        }
        if self.days_of_week:
            result['days_of_week'] = self.days_of_week
        if self.day_of_month:
            result['day_of_month'] = self.day_of_month
        if self.cron_expression:
            result['cron_expression'] = self.cron_expression
        if self.end_date:
            result['end_date'] = self.end_date.isoformat()
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RepeatRule':
        """从字典创建"""
        rule = cls()
        rule.type = RepeatType(data.get('type', 'once'))
        rule.interval = data.get('interval', 1)
        rule.days_of_week = data.get('days_of_week')
        rule.day_of_month = data.get('day_of_month')
        rule.cron_expression = data.get('cron_expression')
        if data.get('end_date'):
            rule.end_date = datetime.datetime.fromisoformat(data['end_date'])
        return rule


@dataclass
class TaskExecutionRecord:
    """任务执行记录"""
    execution_time: datetime.datetime
    status: TaskStatus
    duration: float  # 执行时长（秒）
    error_message: Optional[str] = None
    result_data: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'execution_time': self.execution_time.isoformat(),
            'status': self.status.value,
            'duration': self.duration,
            'error_message': self.error_message,
            'result_data': self.result_data
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TaskExecutionRecord':
        """从字典创建"""
        return cls(
            execution_time=datetime.datetime.fromisoformat(data['execution_time']),
            status=TaskStatus(data['status']),
            duration=data['duration'],
            error_message=data.get('error_message'),
            result_data=data.get('result_data')
        )


class ScheduledTask:
    """定时任务模型类"""
    
    def __init__(self, 
                 name: str,
                 task_type: TaskType,
                 schedule_time: datetime.datetime,
                 repeat_rule: Optional[RepeatRule] = None,
                 description: str = "",
                 platform: str = "netease",
                 target_accounts: Optional[List[str]] = None,
                 task_id: Optional[str] = None):
        """
        初始化定时任务
        
        Args:
            name: 任务名称
            task_type: 任务类型
            schedule_time: 首次执行时间
            repeat_rule: 重复规则
            description: 任务描述
            platform: 目标平台
            target_accounts: 目标账号列表
            task_id: 任务ID（可选，自动生成）
        """
        self.id = task_id or str(uuid.uuid4())
        self.name = name
        self.description = description
        self.task_type = task_type
        self.platform = platform
        self.target_accounts = target_accounts or ["all"]
        
        # 时间相关
        self.schedule_time = schedule_time
        self.repeat_rule = repeat_rule or RepeatRule()
        self.next_execution = schedule_time
        
        # 状态相关
        self.status = TaskStatus.PENDING
        self.enabled = True
        
        # 统计信息
        from .time_utils import TimeUtils
        current_time = TimeUtils.get_beijing_now()
        self.created_at = current_time
        self.updated_at = current_time
        self.last_execution: Optional[datetime.datetime] = None
        self.execution_count = 0
        self.success_count = 0
        self.failure_count = 0
        
        # 执行记录（最近10次）
        self.execution_history: List[TaskExecutionRecord] = []
        
        # 配置选项
        self.timeout = 3600  # 任务超时时间（秒）
        self.retry_count = 3  # 失败重试次数
        self.retry_interval = 300  # 重试间隔（秒）
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典用于序列化"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'task_type': self.task_type.value,
            'platform': self.platform,
            'target_accounts': self.target_accounts,
            'schedule_time': self.schedule_time.isoformat(),
            'repeat_rule': self.repeat_rule.to_dict(),
            'next_execution': self.next_execution.isoformat() if self.next_execution else None,
            'status': self.status.value,
            'enabled': self.enabled,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'last_execution': self.last_execution.isoformat() if self.last_execution else None,
            'execution_count': self.execution_count,
            'success_count': self.success_count,
            'failure_count': self.failure_count,
            'execution_history': [record.to_dict() for record in self.execution_history],
            'timeout': self.timeout,
            'retry_count': self.retry_count,
            'retry_interval': self.retry_interval
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ScheduledTask':
        """从字典创建任务对象"""
        task = cls(
            name=data['name'],
            task_type=TaskType(data['task_type']),
            schedule_time=datetime.datetime.fromisoformat(data['schedule_time']),
            repeat_rule=RepeatRule.from_dict(data.get('repeat_rule', {})),
            description=data.get('description', ''),
            platform=data.get('platform', 'netease'),
            target_accounts=data.get('target_accounts', ['all']),
            task_id=data['id']
        )
        
        # 恢复状态信息
        if data.get('next_execution'):
            task.next_execution = datetime.datetime.fromisoformat(data['next_execution'])
        task.status = TaskStatus(data.get('status', 'pending'))
        task.enabled = data.get('enabled', True)
        task.created_at = datetime.datetime.fromisoformat(data['created_at'])
        task.updated_at = datetime.datetime.fromisoformat(data['updated_at'])
        if data.get('last_execution'):
            task.last_execution = datetime.datetime.fromisoformat(data['last_execution'])
        
        # 恢复统计信息
        task.execution_count = data.get('execution_count', 0)
        task.success_count = data.get('success_count', 0)
        task.failure_count = data.get('failure_count', 0)
        
        # 恢复执行历史
        task.execution_history = [
            TaskExecutionRecord.from_dict(record) 
            for record in data.get('execution_history', [])
        ]
        
        # 恢复配置
        task.timeout = data.get('timeout', 3600)
        task.retry_count = data.get('retry_count', 3)
        task.retry_interval = data.get('retry_interval', 300)
        
        return task
    
    def should_execute(self, current_time: datetime.datetime) -> bool:
        """判断任务是否应该执行"""
        if not self.enabled or self.status in [TaskStatus.RUNNING, TaskStatus.DISABLED]:
            return False
        
        if not self.next_execution:
            return False
            
        return current_time >= self.next_execution
    
    def calculate_next_execution(self) -> Optional[datetime.datetime]:
        """计算下次执行时间"""
        if self.repeat_rule.type == RepeatType.ONCE:
            return None  # 一次性任务不需要下次执行时间
        
        if not self.last_execution:
            base_time = self.schedule_time
        else:
            base_time = self.last_execution
        
        if self.repeat_rule.type == RepeatType.DAILY:
            return base_time + datetime.timedelta(days=self.repeat_rule.interval)
        elif self.repeat_rule.type == RepeatType.WEEKLY:
            return base_time + datetime.timedelta(weeks=self.repeat_rule.interval)
        elif self.repeat_rule.type == RepeatType.MONTHLY:
            # 简化的月份计算，实际应用中可能需要更复杂的逻辑
            next_month = base_time.month + self.repeat_rule.interval
            next_year = base_time.year + (next_month - 1) // 12
            next_month = ((next_month - 1) % 12) + 1
            return base_time.replace(year=next_year, month=next_month)
        
        return None
    
    def update_after_execution(self, success: bool, duration: float, error_message: str = None, result_data: Dict[str, Any] = None):
        """执行后更新任务状态"""
        from .time_utils import TimeUtils
        current_time = TimeUtils.get_beijing_now()
        self.last_execution = current_time
        self.execution_count += 1
        self.updated_at = current_time
        
        if success:
            self.success_count += 1
            self.status = TaskStatus.COMPLETED if self.repeat_rule.type == RepeatType.ONCE else TaskStatus.PENDING
        else:
            self.failure_count += 1
            self.status = TaskStatus.FAILED
        
        # 添加执行记录
        record = TaskExecutionRecord(
            execution_time=self.last_execution,
            status=TaskStatus.COMPLETED if success else TaskStatus.FAILED,
            duration=duration,
            error_message=error_message,
            result_data=result_data
        )
        self.execution_history.append(record)
        
        # 只保留最近10次记录
        if len(self.execution_history) > 10:
            self.execution_history = self.execution_history[-10:]
        
        # 计算下次执行时间
        if success and self.repeat_rule.type != RepeatType.ONCE:
            self.next_execution = self.calculate_next_execution()
            if self.next_execution:
                self.status = TaskStatus.PENDING
        elif self.repeat_rule.type == RepeatType.ONCE:
            self.next_execution = None
            self.enabled = False  # 一次性任务执行后自动禁用
