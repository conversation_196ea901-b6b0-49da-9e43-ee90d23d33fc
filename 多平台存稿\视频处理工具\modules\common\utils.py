"""
通用工具模块 - 包含通用的功能函数
"""

import re
from typing import Dict, List, Tuple, Optional, Any, Union

def clean_filename(filename: str) -> str:
    """清理文件名，移除特殊字符和随机后缀"""
    # 移除前缀特殊符号
    while filename and filename[0] in '#@*_-+.,:;!?':
        filename = filename[1:]
    
    # 移除末尾的随机字符串格式如 _4f38e739
    filename = re.sub(r'_[a-zA-Z0-9]{6,10}$', '', filename)
    
    # 去除多余空格，修复其他潜在问题
    filename = filename.strip()
    
    return filename

def smart_split_filename(filename: str, max_total_chars: int = 30) -> Tuple[str, str]:
    """智能拆分文件名为两部分，用于封面文字"""
    # 清理文件名
    filename = clean_filename(filename)
    
    # 首先确保文件名总长度不超过限制
    if len(filename) > max_total_chars:
        filename = filename[:max_total_chars]
    
    # 如果文件名很短，直接返回
    if len(filename) <= 15:  # 小于15个字符时不拆分
        return filename, ""
    
    # 查找拆分点 - 优先在标点符号处拆分
    punctuation = ['.', '_', '-', ' ', '、', '，', '。', '：', '；', '!', '?', '！', '？',
                  '(', ')', '[', ']', '{', '}', '（', '）', '【', '】', '「', '」',
                  '《', '》', '〈', '〉', '"', '"', ''', ''', ',', ';', ':', '/', '\\']
    
    # 寻找中间点附近的分隔符
    mid_point = len(filename) // 2
    best_split_point = mid_point
    min_distance = len(filename)
    
    # 在文件名中寻找最接近中点的标点符号
    for i, char in enumerate(filename):
        if char in punctuation:
            distance = abs(i - mid_point)
            if distance < min_distance:
                min_distance = distance
                best_split_point = i + 1  # 在标点符号后拆分
    
    # 如果没有找到合适的标点符号，尝试寻找中文与非中文字符的分界点
    if best_split_point == mid_point:
        for i in range(mid_point - 5, mid_point + 5):
            if 0 < i < len(filename) - 1:
                # 检查是否是中文与非中文的分界
                curr_is_chinese = '\u4e00' <= filename[i] <= '\u9fff'
                next_is_chinese = '\u4e00' <= filename[i+1] <= '\u9fff'
                if curr_is_chinese != next_is_chinese:
                    best_split_point = i + 1
                    break
    
    # 拆分文件名
    first_part = filename[:best_split_point]
    second_part = filename[best_split_point:]
    
    return first_part, second_part

def get_color_map() -> Dict[str, Tuple[int, int, int]]:
    """获取颜色映射表"""
    return {
        "#FFFFFF": (255, 255, 255),  # 白色
        "#000000": (0, 0, 0),        # 黑色
        "#FF0000": (255, 0, 0),      # 红色
        "#0000FF": (0, 0, 255),      # 蓝色
        "#00FF00": (0, 255, 0),      # 绿色
        "#FFFF00": (255, 255, 0),    # 黄色
        "#FFA500": (255, 165, 0),    # 橙色
        "#800080": (128, 0, 128),    # 紫色
        "#FFC0CB": (255, 192, 203),  # 粉色
        "#A52A2A": (165, 42, 42),    # 棕色
        "#808080": (128, 128, 128),  # 灰色
        "#00FFFF": (0, 255, 255)     # 青色
    }

def get_color_names() -> Dict[str, str]:
    """获取颜色名称映射表"""
    return {
        "#FFFFFF": "白色", 
        "#000000": "黑色", 
        "#FF0000": "红色", 
        "#0000FF": "蓝色", 
        "#00FF00": "绿色", 
        "#FFFF00": "黄色",
        "#FFA500": "橙色", 
        "#800080": "紫色", 
        "#FFC0CB": "粉色",
        "#A52A2A": "棕色", 
        "#808080": "灰色", 
        "#00FFFF": "青色"
    }

def get_color_options() -> List[str]:
    """获取颜色选项列表"""
    return [
        "#FFFFFF", "#000000", "#FF0000", "#0000FF", "#00FF00", "#FFFF00",
        "#FFA500", "#800080", "#FFC0CB", "#A52A2A", "#808080", "#00FFFF"
    ]

def format_time(seconds: float) -> str:
    """将秒数格式化为时:分:秒格式"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    
    if hours > 0:
        return f"{hours}:{minutes:02d}:{secs:02d}"
    else:
        return f"{minutes}:{secs:02d}"

def format_file_size(size_bytes: int) -> str:
    """将字节数格式化为人类可读的文件大小"""
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} KB"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes / (1024 * 1024):.1f} MB"
    else:
        return f"{size_bytes / (1024 * 1024 * 1024):.2f} GB"

def get_aspect_ratio(width: int, height: int) -> str:
    """根据宽高计算视频比例"""
    # 计算最大公约数
    def gcd(a, b):
        while b:
            a, b = b, a % b
        return a
    
    # 计算宽高比
    divisor = gcd(width, height)
    w = width // divisor
    h = height // divisor
    
    # 检查是否是常见比例
    if abs(w/h - 16/9) < 0.01:
        return "16:9"
    elif abs(w/h - 4/3) < 0.01:
        return "4:3"
    elif abs(w/h - 1) < 0.01:
        return "1:1"
    elif abs(w/h - 9/16) < 0.01:
        return "9:16"
    else:
        return f"{w}:{h}"

def get_video_formats() -> List[str]:
    """获取支持的视频格式列表"""
    return ["mp4", "mov", "avi", "mkv", "wmv", "flv", "webm"]

def get_cover_resolutions() -> Dict[str, Tuple[int, int]]:
    """获取预设的封面分辨率"""
    return {
        "720p (1280x720)": (1280, 720),
        "1080p (1920x1080)": (1920, 1080),
        "4K (3840x2160)": (3840, 2160),
        "方形 (1080x1080)": (1080, 1080),
        "竖屏 (720x1280)": (720, 1280),
        "竖屏 (1080x1920)": (1080, 1920)
    }
