"""
PyInstaller hook for pkg_resources
"""

from PyInstaller.utils.hooks import collect_all

# 收集pkg_resources包的所有内容
datas, binaries, hiddenimports = collect_all('pkg_resources')

# 添加特定的pkg_resources子模块
hiddenimports += [
    'pkg_resources._vendor',
    'pkg_resources._vendor.packaging',
    'pkg_resources._vendor.packaging.version',
    'pkg_resources._vendor.packaging.specifiers',
    'pkg_resources._vendor.packaging.requirements',
    'pkg_resources._vendor.packaging.markers',
    'pkg_resources.extern',
    'pkg_resources.extern.packaging',
]
