{"platform_config": {"root_directory": "D:\\网易号全自动", "description": "多平台存稿系统根目录配置", "version": "1.0.0", "last_updated": "2024-12-27"}, "shared_directories": {"description": "所有平台共用的目录", "directories": {"screenshots": {"path": "截图", "full_path": "D:\\网易号全自动\\截图", "description": "存放系统运行时的截图文件", "subdirectories": {"operation": "操作截图", "error": "错误截图", "success": "成功截图", "debug": "调试截图"}, "file_types": ["png", "jpg", "jpeg", "bmp"], "auto_cleanup": {"enabled": true, "retention_days": 30}}, "data": {"path": "数据", "full_path": "D:\\网易号全自动\\数据", "description": "存放各种数据文件和配置", "subdirectories": {"config": "配置", "logs": "日志", "cache": "缓存", "statistics": "统计"}, "file_types": ["json", "xml", "csv", "txt", "log"], "backup": {"enabled": true, "frequency": "daily"}}, "violations": {"path": "违规", "full_path": "D:\\网易号全自动\\违规", "description": "存放违规相关的记录和处理文件", "subdirectories": {"content": "违规内容", "records": "处理记录", "appeals": "申诉材料"}, "file_types": ["txt", "pdf", "doc", "docx", "json"], "retention": {"enabled": true, "retention_years": 2}}, "pending": {"path": "未处理", "full_path": "D:\\网易号全自动\\未处理", "description": "存放待处理的内容和任务", "subdirectories": {"articles": "待发布文章", "videos": "待上传视频", "images": "待处理图片", "tasks": "待执行任务"}, "file_types": ["txt", "md", "mp4", "avi", "mov", "png", "jpg", "json"], "processing": {"auto_scan": true, "scan_interval": 300}}, "processed": {"path": "已处理", "full_path": "D:\\网易号全自动\\已处理", "description": "存放已处理但未最终完成的内容", "subdirectories": {"articles": "已编辑文章", "videos": "已处理视频", "images": "已优化图片", "tasks": "处理中任务"}, "file_types": ["txt", "md", "mp4", "avi", "mov", "png", "jpg", "json"], "workflow": {"next_stage": "completed", "auto_move": false}}, "completed": {"path": "已完成", "full_path": "D:\\网易号全自动\\已完成", "description": "存放最终完成的内容和结果", "subdirectories": {"published": "已发布内容", "uploaded": "已上传内容", "finished": "完成任务", "archive": "历史归档"}, "file_types": ["txt", "md", "mp4", "avi", "mov", "png", "jpg", "json"], "archive": {"enabled": true, "archive_after_days": 90}}}}, "account_directories": {"description": "各平台特定的账号管理目录", "platforms": {"netease": {"name": "网易号账号", "path": "网易号账号", "full_path": "D:\\网易号全自动\\网易号账号", "description": "网易号平台的账号管理", "subdirectories": {"accounts": "账号信息", "publish_history": "发布记录", "statistics": "数据统计", "settings": "账号设置"}, "features": ["内容发布", "数据统计", "账号管理", "违规监控"], "file_types": ["json", "txt", "csv", "log"]}, "toutiao": {"name": "头条号账号", "path": "头条号账号", "full_path": "D:\\网易号全自动\\头条号账号", "description": "头条号平台的账号管理", "subdirectories": {"accounts": "账号信息", "publish_history": "发布记录", "statistics": "数据统计", "recommendations": "推荐数据"}, "features": ["内容发布", "推荐机制", "阅读量统计", "粉丝管理"], "file_types": ["json", "txt", "csv", "log"]}, "dayu": {"name": "大鱼号账号", "path": "大鱼号账号", "full_path": "D:\\网易号全自动\\大鱼号账号", "description": "大鱼号平台的账号管理", "subdirectories": {"accounts": "账号信息", "publish_history": "发布记录", "statistics": "数据统计", "revenue": "收益数据"}, "features": ["内容发布", "收益统计", "质量评分", "平台推荐"], "file_types": ["json", "txt", "csv", "log"]}}}, "directory_rules": {"naming_convention": {"use_chinese": true, "no_spaces": false, "case_sensitive": true}, "permissions": {"system_process": {"read": true, "write": true, "delete": true}, "user_operation": {"read": true, "write": true, "delete": false}, "backup_process": {"read": true, "write": false, "delete": false}}, "security": {"encrypt_account_data": true, "access_control": true, "audit_log": true}, "maintenance": {"auto_cleanup": true, "backup_schedule": "daily", "health_check": true}}, "workflow": {"content_flow": ["pending", "processed", "completed"], "status_tracking": {"enabled": true, "log_transitions": true}, "automation": {"auto_move_files": false, "notification": true, "error_handling": true}}, "monitoring": {"disk_usage": {"enabled": true, "warning_threshold": 80, "critical_threshold": 90}, "file_count": {"enabled": true, "max_files_per_directory": 10000}, "access_log": {"enabled": true, "retention_days": 30}}, "backup": {"enabled": true, "schedule": "0 2 * * *", "retention": {"daily": 7, "weekly": 4, "monthly": 12}, "exclude_patterns": ["*.tmp", "*.cache", "*.log"]}}