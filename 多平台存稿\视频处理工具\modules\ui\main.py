"""
UI主模块 - 主UI类，组装各个UI组件
"""

import tkinter as tk
from tkinter import ttk
from typing import Callable

# 导入图标模块
from ..icons import get_icon

# 导入UI基础类
from ..ui_base import VideoPreprocessUI as BaseUI
from ..utils import center_window

# 导入UI主题和样式
from .styles.theme import (
    configure_fonts,
    increase_font_size,
    decrease_font_size,
    _update_font_size,
    toggle_dark_mode,
    apply_theme,
    _update_all_widgets_theme
)

# 不再导入UI工具函数，改为直接实现所需功能

# 导入UI组件
from .components.log_panel import create_log_panel
from .components.progress_panel import create_progress_panel
from .components.toolbar import create_toolbar
from .components.video_settings import create_video_settings
from .components.cover_settings import create_cover_settings
from .components.gpu_settings import create_gpu_settings
from .components.resource_settings import create_resource_settings
from .components.scheduler_panel import create_scheduler_panel

# 导入旧模块，保持向后兼容
# 这些将在未来版本中逐步替换为新的模块化组件
from ..ui_utils import (
    update_output_dirs,
    select_video_dir,
    select_directory,
    bind_keyboard_shortcuts,
    _update_all_previews,
    _on_color_change,
    _update_color_preview,
    _update_font_size_preview,
    _update_auto_save_interval
)
from ..ui_settings import (
    open_settings_dialog,
    _search_settings,
    _create_interface_settings,
    _create_video_settings,
    _create_performance_settings,
    _create_cover_settings,
    _create_status_settings,
    _update_status_info
)
from ..ui_processing import (
    start_preprocess,
    stop_processing,
    preprocess_task
)

class VideoPreprocessUI(BaseUI):
    """视频预处理工具UI类 - 重构版本"""

    # 添加从ui_theme模块导入的方法
    configure_fonts = configure_fonts
    increase_font_size = increase_font_size
    decrease_font_size = decrease_font_size
    _update_font_size = _update_font_size
    toggle_dark_mode = toggle_dark_mode
    apply_theme = apply_theme
    _update_all_widgets_theme = _update_all_widgets_theme

    # 添加从ui_utils模块导入的方法
    update_output_dirs = update_output_dirs
    select_video_dir = select_video_dir
    select_directory = select_directory
    bind_keyboard_shortcuts = bind_keyboard_shortcuts
    _update_all_previews = _update_all_previews
    _on_color_change = _on_color_change
    _update_color_preview = _update_color_preview
    _update_font_size_preview = _update_font_size_preview
    _update_auto_save_interval = _update_auto_save_interval

    # 禁用工具提示功能
    def create_tooltip(self, *args, **kwargs):
        """禁用的工具提示方法，不执行任何操作"""
        pass

    # 颜色预览更新方法已从ui_utils模块导入

    # 实现字体大小预览方法
    def _update_font_size_preview(self):
        """更新字体大小预览"""
        try:
            # 获取当前字体大小
            font_size = self.cover_size.get()

            # 创建预览对话框
            preview_dialog = tk.Toplevel(self.root)
            preview_dialog.title("字体大小预览")
            preview_dialog.geometry("600x400")
            preview_dialog.transient(self.root)
            preview_dialog.grab_set()

            # 创建预览画布
            preview_canvas = tk.Canvas(preview_dialog, bg="black", width=600, height=400)
            preview_canvas.pack(fill=tk.BOTH, expand=True)

            # 创建示例文本
            preview_canvas.create_text(300, 200, text="示例文字", fill="white",
                                     font=("Arial", font_size), anchor="center")

            # 添加说明标签
            ttk.Label(preview_dialog, text=f"当前字体大小: {font_size}").pack(pady=10)

            # 添加关闭按钮
            ttk.Button(preview_dialog, text="关闭", command=preview_dialog.destroy).pack(pady=10)

            # 居中显示对话框
            center_window(preview_dialog)

        except Exception as e:
            self.log(f"❌ 预览字体大小失败: {str(e)}", "error")

    # 实现基本的日志方法
    def log(self, message, tag=None):
        """添加日志消息"""
        # 首先打印到控制台，确保始终有输出
        print(message)

        # 检查log_text是否存在
        if hasattr(self, 'log_text'):
            try:
                self.log_text.config(state=tk.NORMAL)
                self.log_text.insert(tk.END, message + "\n", tag)
                self.log_text.see(tk.END)  # 滚动到最新内容
                self.log_text.config(state=tk.DISABLED)

                # 更新UI
                self.root.update_idletasks()

                # 更新状态标签
                if hasattr(self, 'status_label'):
                    if tag == "error":
                        self.status_label.config(text="错误")
                    elif tag == "warning":
                        self.status_label.config(text="警告")
                    elif tag == "success":
                        self.status_label.config(text="成功")
                    elif tag == "info":
                        self.status_label.config(text="信息")
            except Exception as e:
                print(f"日志记录失败: {message} - {str(e)}")

    def clear_log(self):
        """清空日志"""
        if hasattr(self, 'log_text'):
            self.log_text.config(state=tk.NORMAL)
            self.log_text.delete(1.0, tk.END)
            self.log_text.config(state=tk.DISABLED)

    # 添加从ui_settings模块导入的方法
    open_settings_dialog = open_settings_dialog
    _search_settings = _search_settings
    _create_interface_settings = _create_interface_settings
    _create_video_settings = _create_video_settings
    _create_performance_settings = _create_performance_settings
    _create_cover_settings = _create_cover_settings
    _create_status_settings = _create_status_settings
    _update_status_info = _update_status_info

    def _create_scrollable_frame(self, parent):
        """创建通用的滚动框架"""
        # 创建滚动框架（删除滚动条）
        canvas = tk.Canvas(parent, highlightthickness=0)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")

        # 添加鼠标滚轮支持
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        def _bind_to_mousewheel(event):
            canvas.bind_all("<MouseWheel>", _on_mousewheel)

        def _unbind_from_mousewheel(event):
            canvas.unbind_all("<MouseWheel>")

        canvas.bind('<Enter>', _bind_to_mousewheel)
        canvas.bind('<Leave>', _unbind_from_mousewheel)

        canvas.pack(fill="both", expand=True)

        return scrollable_frame

    # 添加从ui_processing模块导入的方法
    start_preprocess = start_preprocess
    stop_processing = stop_processing
    preprocess_task = preprocess_task

    # 使用progress_panel模块的方法
    def reset_progress_ui(self):
        """重置进度UI"""
        from .components.progress_panel import reset_progress
        reset_progress(self)

    def update_progress_ui(self, success=None, fail=None, total=None, percent=None):
        """更新进度UI"""
        from .components.progress_panel import update_progress
        update_progress(self, success, fail, total, percent)

    # 使用gpu_settings模块的方法
    def check_gpu_availability(self):
        """检查GPU可用性并更新UI"""
        from .components.gpu_settings import check_gpu_availability
        check_gpu_availability(self)

    def detect_gpus(self):
        """检测可用的GPU设备"""
        from .components.gpu_settings import detect_gpus
        detect_gpus(self)

    # 使用resource_settings模块的方法
    def toggle_smart_resource(self):
        """切换智能资源分配状态"""
        from .components.resource_settings import toggle_smart_resource
        toggle_smart_resource(self)

    def detect_system_resources(self):
        """检测系统资源并更新UI"""
        try:
            self.log("🔍 正在检测系统资源...", "info")

            # 获取CPU核心数
            import multiprocessing
            cpu_count = multiprocessing.cpu_count()

            # 尝试获取内存信息
            try:
                import psutil
                mem = psutil.virtual_memory()
                total_mem_gb = mem.total / (1024**3)  # 转换为GB
                available_mem_gb = mem.available / (1024**3)  # 可用内存

                self.log(f"✅ 系统信息: CPU {cpu_count}核, 总内存 {total_mem_gb:.1f}GB, 可用内存 {available_mem_gb:.1f}GB", "success")

                # 设置推荐的线程数
                recommended_threads = max(1, min(cpu_count - 1, 8))
                self.thread_num.set(recommended_threads)

                # 设置推荐的内存限制
                recommended_memory = int(min(available_mem_gb * 0.8, total_mem_gb * 0.5) * 1024)  # 转换为MB
                self.memory_limit.set(recommended_memory)

                self.log(f"✅ 已设置推荐线程数: {recommended_threads}, 内存限制: {recommended_memory}MB", "success")

            except ImportError:
                # 如果没有psutil，使用简单的估计
                self.log(f"⚠️ 无法获取详细内存信息，仅检测到CPU {cpu_count}核", "warning")

                # 设置推荐的线程数
                recommended_threads = max(1, min(cpu_count - 1, 4))
                self.thread_num.set(recommended_threads)

                self.log(f"✅ 已设置推荐线程数: {recommended_threads}", "success")

        except Exception as e:
            self.log(f"❌ 检测系统资源失败: {str(e)}", "error")

    def set_progress_callback(self, callback):
        """设置进度回调函数"""
        self.progress_callback = callback

        # 如果已经初始化了自动处理器，也设置它的回调
        if hasattr(self, 'auto_processor'):
            self.auto_processor.set_progress_callback(callback)

    def start_auto_processing(self):
        """开始自动处理"""
        if not hasattr(self, 'auto_processor'):
            self.log("❌ 自动处理器未初始化", "error")
            return

        self.log("🚀 开始自动处理视频...", "info")
        # 不要在这里设置 is_processing = True，让 start_preprocess 方法来设置
        self.auto_processor.start_processing()

    def on_closing(self):
        """窗口关闭时的处理函数"""
        try:
            # 保存配置
            self.config_manager.save_config()

            # 停止调度器
            if hasattr(self, 'scheduler') and self.scheduler:
                self.scheduler.stop()
                self.log("✅ 调度器已停止", "info")

            # 停止自动处理
            if hasattr(self, 'auto_processor'):
                self.is_processing = False
                self.log("✅ 自动处理已停止", "info")

            # 销毁窗口
            self.root.destroy()

        except Exception as e:
            self.log(f"❌ 关闭窗口时出错: {str(e)}", "error")
            # 确保窗口被销毁
            self.root.destroy()

    # 使用cover_settings模块的方法
    def set_resolution(self, width, height):
        """设置封面分辨率"""
        from .components.cover_settings import set_resolution
        set_resolution(self, width, height)

    # _update_auto_save_interval 方法已从ui_utils模块导入



    def __init__(self, root: tk.Tk, process_callback: Callable = None, auto_mode: bool = False, video_processor = None):
        """
        初始化UI

        Args:
            root: Tkinter根窗口
            process_callback: 处理视频的回调函数
            auto_mode: 是否为自动模式
            video_processor: 视频处理器实例
        """
        # 设置自动模式标志
        self.auto_mode = auto_mode
        self.is_processing = False

        # 保存视频处理器实例
        self.video_processor = video_processor

        # 调用基类的初始化方法
        super().__init__(root, process_callback)

        # 加载过滤关键词
        if hasattr(self, 'filename_filters') and hasattr(self.video_processor, 'load_filters_from_string'):
            self.video_processor.load_filters_from_string(self.filename_filters.get())

        # 配置字体
        self.configure_fonts()

        # 创建UI - 这会初始化log_text和其他UI元素
        self.create_ui()

        # 确保窗口居中显示
        self.center_window()

        # 设置配置管理器的log方法
        self.config_manager.log_callback = self.log

        # 应用主题
        self.apply_theme()

        # 绑定键盘快捷键
        self.bind_keyboard_shortcuts()

        # 重置进度UI
        self.reset_progress_ui()

        # 初始化调度器
        from ..common.scheduler import Scheduler
        self.scheduler = Scheduler(logger=self.log)
        self.scheduler.start()

        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 初始化日志 - 根据用户要求简化日志显示
        if auto_mode:
            self.log("网易视频预处理工具 - 自动模式", "info")
        else:
            # 只显示用户要求的几条信息
            self.log("欢迎使用网易视频预处理工具", "info")
            self.log("✅ 界面初始化完成", "success")

        # 检查GPU可用性
        try:
            self.check_gpu_availability()
        except Exception as e:
            self.log(f"⚠️ GPU检测失败: {str(e)}", "warning")

        # 如果是自动模式，初始化自动处理器
        if auto_mode:
            from .auto_processor import AutoProcessor
            self.auto_processor = AutoProcessor(self, process_callback)
            self.log("✅ 自动处理器初始化完成", "success")

    # 注意：这是一个临时解决方案，未来应该将create_ui方法完全重写

    def create_ui(self):
        """创建用户界面 - 使用模块化组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # 创建顶部工具栏
        create_toolbar(main_frame, self)

        # 创建设置区域框架 - 包含左右两侧的设置面板
        settings_frame = ttk.Frame(main_frame)
        settings_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建左侧设置面板 - 减少垂直padding以节省空间
        left_frame = ttk.Frame(settings_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=2)

        # 创建右侧设置面板 - 减少垂直padding以节省空间
        right_frame = ttk.Frame(settings_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=2)

        # 创建视频设置面板（左侧）
        create_video_settings(left_frame, self)

        # 创建封面设置面板（左侧）
        create_cover_settings(left_frame, self)

        # 创建GPU设置面板（右侧）
        create_gpu_settings(right_frame, self)

        # 创建资源设置面板（右侧）
        create_resource_settings(right_frame, self)

        # 创建定时任务面板（右侧）
        create_scheduler_panel(right_frame, self)

        # 创建按钮区域 - 在设置区域之后，使用LabelFrame使其更加明显
        button_frame = ttk.LabelFrame(main_frame, text="操作控制")
        button_frame.pack(fill=tk.X, padx=10, pady=(5, 10))  # 减少顶部padding

        # 创建内部按钮框架 - 减少垂直padding以节省空间
        inner_button_frame = ttk.Frame(button_frame)
        inner_button_frame.pack(fill=tk.X, padx=10, pady=5)

        # 创建按钮图标
        play_icon = get_icon("play", (24, 24))
        stop_icon = get_icon("stop", (24, 24))

        # 保存图标引用
        self.play_icon = play_icon
        self.stop_icon = stop_icon

        # 创建开始按钮 - 使用大一点的按钮
        self.start_button = ttk.Button(inner_button_frame, text="开始处理", image=play_icon, compound=tk.LEFT,
                                     command=self.start_preprocess, width=15)
        self.start_button.pack(side=tk.RIGHT, padx=5)

        # 创建停止按钮
        self.stop_button = ttk.Button(inner_button_frame, text="停止处理", image=stop_icon, compound=tk.LEFT,
                                    command=self.stop_processing, width=15)
        self.stop_button.pack(side=tk.RIGHT, padx=5)

        # 添加一个状态标签
        self.status_label = ttk.Label(inner_button_frame, text="就绪")
        self.status_label.pack(side=tk.LEFT, padx=5)

        # 不再使用工具提示和快捷键
        self.start_button.config(text="开始处理")
        self.stop_button.config(text="停止处理")

        # 创建进度面板 - 确保它在按钮区域之后
        self.progress_frame = create_progress_panel(main_frame, self)

        # 创建底部区域框架 - 包含日志面板，减少垂直padding
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=(2, 5))

        # 创建日志面板框架 - 使用LabelFrame使其更加明显，减少垂直padding
        log_container = ttk.LabelFrame(bottom_frame, text="处理日志")
        log_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=2)

        # 创建日志面板
        self.log_frame = create_log_panel(log_container, self)

        # 确保日志面板可见 - 使用fill和expand确保它占用所有可用空间
        self.log_frame.pack(fill=tk.BOTH, expand=True)

        # 记录日志
        print("UI创建完成")
        self.log("✅ UI创建完成", "success")
