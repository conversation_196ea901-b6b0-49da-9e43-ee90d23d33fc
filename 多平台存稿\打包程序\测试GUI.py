#!/usr/bin/env python3
"""
测试GUI功能
"""

def test_tkinter():
    """测试tkinter是否可用"""
    try:
        import tkinter as tk
        from tkinter import ttk, messagebox
        print("✅ tkinter导入成功")
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("GUI测试")
        root.geometry("300x200")
        
        label = tk.Label(root, text="GUI测试成功！", font=("微软雅黑", 14))
        label.pack(pady=50)
        
        button = tk.Button(root, text="关闭", command=root.destroy)
        button.pack(pady=10)
        
        print("✅ 测试窗口创建成功")
        print("请关闭测试窗口...")
        
        root.mainloop()
        return True
        
    except ImportError as e:
        print(f"❌ tkinter导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        return False

def test_unified_packager_gui():
    """测试统一打包器的GUI"""
    try:
        import sys
        from pathlib import Path
        
        # 添加当前目录到路径
        sys.path.insert(0, str(Path(__file__).parent))
        
        from 统一自动打包 import GUI_AVAILABLE, PackageGUI
        
        if not GUI_AVAILABLE:
            print("❌ 统一打包器中GUI不可用")
            return False
        
        print("✅ 统一打包器GUI模块可用")
        
        # 测试创建GUI
        import tkinter as tk
        root = tk.Tk()
        
        try:
            app = PackageGUI(root)
            print("✅ PackageGUI创建成功")
            
            # 显示窗口
            root.after(2000, root.destroy)  # 2秒后自动关闭
            root.mainloop()
            return True
            
        except Exception as e:
            print(f"❌ PackageGUI创建失败: {e}")
            root.destroy()
            return False
            
    except ImportError as e:
        print(f"❌ 统一打包器导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 统一打包器GUI测试失败: {e}")
        return False

def main():
    print("=" * 50)
    print("GUI功能诊断测试")
    print("=" * 50)
    
    print("\n1. 测试基础tkinter功能:")
    tkinter_ok = test_tkinter()
    
    print("\n2. 测试统一打包器GUI:")
    if tkinter_ok:
        unified_gui_ok = test_unified_packager_gui()
    else:
        print("跳过统一打包器GUI测试（tkinter不可用）")
        unified_gui_ok = False
    
    print("\n" + "=" * 50)
    print("诊断结果:")
    print(f"基础tkinter: {'✅ 正常' if tkinter_ok else '❌ 异常'}")
    print(f"统一打包器GUI: {'✅ 正常' if unified_gui_ok else '❌ 异常'}")
    
    if not tkinter_ok:
        print("\n建议:")
        print("- 检查Python是否包含tkinter模块")
        print("- 在某些Linux发行版中需要安装python3-tk")
        print("- 使用命令行模式: python 统一自动打包.py")
    elif not unified_gui_ok:
        print("\n建议:")
        print("- 检查统一自动打包.py文件是否完整")
        print("- 查看错误信息进行调试")
    else:
        print("\n🎉 GUI功能正常！")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
