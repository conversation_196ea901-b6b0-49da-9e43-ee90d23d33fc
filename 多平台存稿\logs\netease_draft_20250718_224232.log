[2025-07-18 22:42:33] [INFO] 🚀 启动多平台存稿工具...
[2025-07-18 22:42:33] [INFO] 🔍 [调试] 当前平台: toutiao
[2025-07-18 22:42:33] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-07-18 22:42:33] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-07-18 22:42:33] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-18 22:42:33] [INFO] 🔄 发现旧数据文件: C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\toutiao_account_data.json
[2025-07-18 22:42:33] [INFO] ℹ️ 新数据文件已存在且更新，跳过迁移: C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\toutiao_account_data.json
[2025-07-18 22:42:33] [INFO] 🔄 发现旧数据文件: C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\account_data.json
[2025-07-18 22:42:33] [INFO] ℹ️ 新数据文件已存在且更新，跳过迁移: C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\account_data.json
[2025-07-18 22:42:33] [INFO] 已从toutiao平台加载存稿详情数据，共 27 个账号
[2025-07-18 22:42:34] [INFO] 已确保所有必要目录存在
[2025-07-18 22:42:35] [INFO] 🔍 [调试] 当前平台: toutiao
[2025-07-18 22:42:35] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-07-18 22:42:35] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-07-18 22:42:35] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-18 22:42:35] [INFO] 🔍 [调试] 更新账号树 - 当前平台: toutiao
[2025-07-18 22:42:35] [INFO] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-18 22:42:35] [INFO] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-07-18 22:42:35] [INFO] 已加载账号数据: 79 条记录
[2025-07-18 22:42:35] [INFO] 🔍 [调试] 账号数据为空或未加载
[2025-07-18 22:42:35] [INFO] ✅ 初始UI更新完成
[2025-07-18 22:42:42] [INFO] 显示右键菜单时出错: 'TextTable' object has no attribute 'identify_row'
[2025-07-18 22:42:43] [INFO] 显示右键菜单时出错: 'TextTable' object has no attribute 'identify_row'
[2025-07-18 22:42:43] [INFO] 显示右键菜单时出错: 'TextTable' object has no attribute 'identify_row'
[2025-07-18 22:42:57] [INFO] 显示右键菜单时出错: 'TextTable' object has no attribute 'identify_row'
[2025-07-18 22:42:58] [INFO] 显示右键菜单时出错: 'TextTable' object has no attribute 'identify_row'
[2025-07-18 22:42:58] [INFO] 显示右键菜单时出错: 'TextTable' object has no attribute 'identify_row'
[2025-07-18 22:42:58] [INFO] 显示右键菜单时出错: 'TextTable' object has no attribute 'identify_row'
[2025-07-18 22:42:58] [INFO] 显示右键菜单时出错: 'TextTable' object has no attribute 'identify_row'
[2025-07-18 22:42:59] [INFO] 显示右键菜单时出错: 'TextTable' object has no attribute 'identify_row'
[2025-07-18 22:42:59] [INFO] 显示右键菜单时出错: 'TextTable' object has no attribute 'identify_row'
[2025-07-18 22:42:59] [INFO] 显示右键菜单时出错: 'TextTable' object has no attribute 'identify_row'
[2025-07-18 22:42:59] [INFO] 显示右键菜单时出错: 'TextTable' object has no attribute 'identify_row'
[2025-07-18 22:42:59] [INFO] 显示右键菜单时出错: 'TextTable' object has no attribute 'identify_row'
[2025-07-18 22:43:02] [INFO] 开始查询所有头条账号数据
[2025-07-18 22:43:02] [INFO] 开始查询 59 个头条账号的数据
[2025-07-18 22:43:02] [INFO] 📊 并发设置: 最大线程数=10, 批大小=10
[2025-07-18 22:43:02] [INFO] 📊 开始任务进度跟踪: 0/59
[2025-07-18 22:43:02] [INFO] [头条查询管理器] 🚀 开始并发查询 59 个账号，最大并发数: 10
[2025-07-18 22:43:02] [INFO] ✅ Cookie预检测通过，启动浏览器验证...
[2025-07-18 22:43:02] [INFO] ✅ Cookie预检测通过，启动浏览器验证...
[2025-07-18 22:43:02] [INFO] ✅ Cookie预检测通过，启动浏览器验证...
[2025-07-18 22:43:02] [INFO] ✅ Cookie预检测通过，启动浏览器验证...
[2025-07-18 22:43:02] [INFO] ✅ Cookie预检测通过，启动浏览器验证...
[2025-07-18 22:43:02] [INFO] ✅ Cookie预检测通过，启动浏览器验证...
[2025-07-18 22:43:02] [INFO] ✅ Cookie预检测通过，启动浏览器验证...
[2025-07-18 22:43:02] [INFO] ✅ Cookie预检测通过，启动浏览器验证...
[2025-07-18 22:43:02] [INFO] ✅ Cookie预检测通过，启动浏览器验证...
[2025-07-18 22:43:08] [INFO] 正在关闭程序，清理后台资源...
[2025-07-18 22:43:08] [INFO] 🧹 开始清理所有资源...
[2025-07-18 22:43:08] [INFO] 正在停止数据查询...
[2025-07-18 22:43:08] [INFO] 正在停止并发管理器...
[2025-07-18 22:43:08] [INFO] ✅ 运行中的任务已停止
[2025-07-18 22:43:08] [INFO] ✅ 定时器线程已清理
[2025-07-18 22:43:08] [INFO] ✅ Chrome进程强制清理完成
[2025-07-18 22:43:08] [INFO] ✅ 浏览器进程已清理
[2025-07-18 22:43:09] [INFO] ✅ 日志处理线程已清理
[2025-07-18 22:43:09] [INFO] ✅ 存稿处理器已清理
[2025-07-18 22:43:09] [INFO] ✅ 线程池已清理
[2025-07-18 22:43:09] [INFO] 正在停止串行刷新工作线程...
[2025-07-18 22:43:12] [INFO] 开始查询账号数据: 13268553663 [toutiao] 浏览器驱动创建成功，使用端口: 9517
[2025-07-18 22:43:12] [INFO] 开始查询账号数据: 13125217359 [toutiao] 浏览器驱动创建成功，使用端口: 9516
[2025-07-18 22:43:12] [INFO] 开始查询账号数据: 13018516447 [toutiao] 浏览器驱动创建成功，使用端口: 9515
[2025-07-18 22:43:12] [INFO] 开始查询账号数据: 13469508102 [toutiao] 浏览器驱动创建成功，使用端口: 9522
[2025-07-18 22:43:12] [INFO] ⚠️ 仍有 21 个线程未结束
[2025-07-18 22:43:12] [INFO] ✅ 临时文件已清理
[2025-07-18 22:43:13] [INFO] Cookie登录异常: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
[2025-07-18 22:43:13] [INFO] Cookie登录异常: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
[2025-07-18 22:43:13] [INFO] Cookie登录异常: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
[2025-07-18 22:43:13] [INFO] [toutiao] 创建驱动失败，已释放端口: 9523
[2025-07-18 22:43:13] [INFO] [toutiao] 创建驱动失败，已释放端口: 9521
[2025-07-18 22:43:13] [INFO] [toutiao] 创建驱动失败，已释放端口: 9519
[2025-07-18 22:43:13] [INFO] Cookie登录异常: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
[2025-07-18 22:43:13] [INFO] [toutiao] 创建驱动失败，已释放端口: 9520
[2025-07-18 22:43:13] [INFO] [toutiao] 创建驱动失败，已释放端口: 9524
[2025-07-18 22:43:13] [INFO] ✅ 已终止 52 个子进程
[2025-07-18 22:43:13] [INFO] Cookie登录异常: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
[2025-07-18 22:43:13] [INFO] Cookie登录异常: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
[2025-07-18 22:43:13] [INFO] Cookie登录异常: Message: Unable to obtain driver for chrome; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

[2025-07-18 22:43:13] [INFO] Cookie登录异常: Message: Unable to obtain driver for chrome; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

[2025-07-18 22:43:13] [INFO] Cookie登录异常: Message: Unable to obtain driver for chrome; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

[2025-07-18 22:43:13] [INFO] ✅ 资源清理完成
[2025-07-18 22:43:13] [INFO] Cookie登录异常: Message: Unable to obtain driver for chrome; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors/driver_location

[2025-07-18 22:43:13] [INFO] ✅ Cookie预检测通过，启动浏览器验证...
[2025-07-18 22:43:13] [INFO] ✅ Cookie预检测通过，启动浏览器验证...
[2025-07-18 22:43:13] [INFO] ❌ 13537289985 数据查询失败: 登录失败
[2025-07-18 22:43:13] [INFO] ✅ Cookie预检测通过，启动浏览器验证...
[2025-07-18 22:43:13] [INFO] ✅ Cookie预检测通过，启动浏览器验证...
[2025-07-18 22:43:13] [INFO] ✅ Cookie预检测通过，启动浏览器验证...
[2025-07-18 22:43:13] [INFO] ✅ Cookie预检测通过，启动浏览器验证...
[2025-07-18 22:43:13] [INFO] ✅ 设置已静默保存
[2025-07-18 22:43:13] [INFO] 程序清理完成，正在退出...
