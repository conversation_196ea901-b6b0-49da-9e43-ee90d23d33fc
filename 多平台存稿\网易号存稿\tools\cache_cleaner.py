#!/usr/bin/env python3
"""
缓存清理工具 - 清理软件自身产生的缓存文件
"""

import os
import sys
import json
import shutil
import glob
import time
from datetime import datetime, timedelta
from pathlib import Path
import tkinter as tk
from tkinter import ttk, messagebox, filedialog

class CacheCleaner:
    """缓存清理器类"""
    
    def __init__(self, root_dir=None):
        """
        初始化缓存清理器
        
        Args:
            root_dir: 项目根目录，如果为None则自动检测
        """
        self.root_dir = root_dir or self._detect_root_dir()
        self.cache_categories = self._init_cache_categories()
        self.scan_results = {}
        
    def _detect_root_dir(self):
        """自动检测项目根目录"""
        current_dir = os.path.dirname(os.path.abspath(__file__))
        # 向上查找包含config.json的目录
        while current_dir != os.path.dirname(current_dir):
            if os.path.exists(os.path.join(current_dir, "config.json")):
                return current_dir
            current_dir = os.path.dirname(current_dir)
        
        # 如果没找到，使用当前目录的上级目录
        return os.path.dirname(os.path.dirname(current_dir))
    
    def _init_cache_categories(self):
        """初始化缓存分类"""
        return {
            "logs": {
                "name": "日志文件",
                "description": "程序运行产生的日志文件",
                "patterns": [
                    "logs/*.log",
                    "*.log",
                    "网易号存稿/*.log"
                ],
                "keep_days": 7,  # 保留最近7天的日志
                "icon": "📄"
            },
            "screenshots": {
                "name": "截图文件",
                "description": "存稿过程中产生的截图文件",
                "patterns": [
                    "screenshots/*.png",
                    "screenshots/*.jpg",
                    "screenshots/*.jpeg",
                    "网易号存稿/screenshots/*.png"
                ],
                "keep_days": 3,  # 保留最近3天的截图
                "icon": "📸"
            },
            "python_cache": {
                "name": "Python缓存",
                "description": "Python字节码缓存文件",
                "patterns": [
                    "__pycache__/*",
                    "*/__pycache__/*",
                    "**/__pycache__/*",
                    "*.pyc",
                    "*.pyo"
                ],
                "keep_days": 0,  # 不保留
                "icon": "🐍"
            },
            "temp_files": {
                "name": "临时文件",
                "description": "各种临时文件和调试文件",
                "patterns": [
                    "*.tmp",
                    "*.temp",
                    "*_temp*",
                    "debug_*.txt",
                    "test_*.json",
                    "*_debug_*.json"
                ],
                "keep_days": 1,  # 保留最近1天
                "icon": "🗂️"
            },
            "browser_cache": {
                "name": "浏览器缓存",
                "description": "WebDriver和浏览器产生的缓存",
                "patterns": [
                    "网易号存稿/drivers/chrome_*",
                    "网易号存稿/drivers/temp_*",
                    "chromedriver_*",
                    "geckodriver_*"
                ],
                "keep_days": 0,  # 不保留
                "icon": "🌐"
            },
            "data_backups": {
                "name": "数据备份",
                "description": "自动生成的数据备份文件",
                "patterns": [
                    "*_backup_*.json",
                    "*.bak",
                    "*_bak.*"
                ],
                "keep_days": 30,  # 保留最近30天的备份
                "icon": "💾"
            },
            "config_cache": {
                "name": "配置缓存",
                "description": "配置文件的缓存和临时文件",
                "patterns": [
                    "config/*.tmp",
                    "*_config_*.tmp",
                    "timer_config_*.json"
                ],
                "keep_days": 1,  # 保留最近1天
                "icon": "⚙️"
            }
        }
    
    def scan_cache_files(self, categories=None):
        """
        扫描缓存文件
        
        Args:
            categories: 要扫描的分类列表，如果为None则扫描所有分类
        
        Returns:
            dict: 扫描结果
        """
        if categories is None:
            categories = list(self.cache_categories.keys())
        
        self.scan_results = {}
        total_size = 0
        total_count = 0
        
        for category in categories:
            if category not in self.cache_categories:
                continue
                
            category_info = self.cache_categories[category]
            files = []
            category_size = 0
            
            # 扫描匹配的文件
            for pattern in category_info["patterns"]:
                pattern_path = os.path.join(self.root_dir, pattern)
                matched_files = glob.glob(pattern_path, recursive=True)
                
                for file_path in matched_files:
                    if os.path.isfile(file_path):
                        try:
                            file_size = os.path.getsize(file_path)
                            file_mtime = os.path.getmtime(file_path)
                            
                            # 检查文件是否应该被清理
                            should_clean = self._should_clean_file(
                                file_path, file_mtime, category_info["keep_days"]
                            )
                            
                            files.append({
                                "path": file_path,
                                "size": file_size,
                                "mtime": file_mtime,
                                "should_clean": should_clean,
                                "relative_path": os.path.relpath(file_path, self.root_dir)
                            })
                            
                            if should_clean:
                                category_size += file_size
                                
                        except (OSError, IOError):
                            # 忽略无法访问的文件
                            continue
            
            # 统计结果
            cleanable_files = [f for f in files if f["should_clean"]]
            
            self.scan_results[category] = {
                "info": category_info,
                "files": files,
                "cleanable_files": cleanable_files,
                "total_count": len(files),
                "cleanable_count": len(cleanable_files),
                "total_size": sum(f["size"] for f in files),
                "cleanable_size": category_size
            }
            
            total_size += category_size
            total_count += len(cleanable_files)
        
        # 添加总计信息
        self.scan_results["_summary"] = {
            "total_cleanable_size": total_size,
            "total_cleanable_count": total_count,
            "scan_time": datetime.now()
        }
        
        return self.scan_results
    
    def _should_clean_file(self, file_path, file_mtime, keep_days):
        """
        判断文件是否应该被清理
        
        Args:
            file_path: 文件路径
            file_mtime: 文件修改时间
            keep_days: 保留天数
        
        Returns:
            bool: 是否应该清理
        """
        if keep_days == 0:
            return True
        
        # 计算文件年龄
        file_age = time.time() - file_mtime
        keep_seconds = keep_days * 24 * 3600
        
        return file_age > keep_seconds
    
    def clean_cache(self, categories=None, dry_run=False):
        """
        清理缓存文件
        
        Args:
            categories: 要清理的分类列表
            dry_run: 是否为试运行（不实际删除文件）
        
        Returns:
            dict: 清理结果
        """
        if not self.scan_results:
            self.scan_cache_files(categories)
        
        results = {
            "cleaned_files": [],
            "failed_files": [],
            "cleaned_size": 0,
            "cleaned_count": 0,
            "failed_count": 0,
            "dry_run": dry_run
        }
        
        categories_to_clean = categories or list(self.cache_categories.keys())
        
        for category in categories_to_clean:
            if category not in self.scan_results or category.startswith("_"):
                continue
            
            category_result = self.scan_results[category]
            
            for file_info in category_result["cleanable_files"]:
                file_path = file_info["path"]
                
                try:
                    if not dry_run:
                        if os.path.isfile(file_path):
                            os.remove(file_path)
                        elif os.path.isdir(file_path):
                            shutil.rmtree(file_path)
                    
                    results["cleaned_files"].append({
                        "path": file_info["relative_path"],
                        "size": file_info["size"],
                        "category": category
                    })
                    results["cleaned_size"] += file_info["size"]
                    results["cleaned_count"] += 1
                    
                except Exception as e:
                    results["failed_files"].append({
                        "path": file_info["relative_path"],
                        "error": str(e),
                        "category": category
                    })
                    results["failed_count"] += 1
        
        return results
    
    def format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        units = ["B", "KB", "MB", "GB", "TB"]
        unit_index = 0
        size = float(size_bytes)
        
        while size >= 1024 and unit_index < len(units) - 1:
            size /= 1024
            unit_index += 1
        
        if unit_index == 0:
            return f"{int(size)} {units[unit_index]}"
        else:
            return f"{size:.1f} {units[unit_index]}"
    
    def get_cache_summary(self):
        """获取缓存摘要信息"""
        if not self.scan_results:
            return None
        
        summary = self.scan_results.get("_summary", {})
        
        return {
            "total_size": self.format_size(summary.get("total_cleanable_size", 0)),
            "total_count": summary.get("total_cleanable_count", 0),
            "categories": len([k for k in self.scan_results.keys() if not k.startswith("_")]),
            "scan_time": summary.get("scan_time")
        }


class CacheCleanerGUI:
    """缓存清理器图形界面"""

    def __init__(self, root=None):
        """初始化GUI"""
        self.root = root or tk.Tk()
        self.cleaner = CacheCleaner()
        self.category_vars = {}
        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        self.root.title("🧹 缓存清理工具")
        self.root.geometry("800x800")
        self.root.minsize(700, 700)

        # 创建主框架
        main_frame = ttk.Frame(self.root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame,
                               text="🧹 软件缓存清理工具",
                               font=("Microsoft YaHei", 16, "bold"))
        title_label.pack(pady=(0, 10))

        # 说明文字
        desc_label = ttk.Label(main_frame,
                              text="清理软件运行过程中产生的缓存文件，释放磁盘空间",
                              font=("Microsoft YaHei", 10))
        desc_label.pack(pady=(0, 15))

        # 创建笔记本控件
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)

        # 缓存扫描标签页
        self.scan_frame = ttk.Frame(notebook)
        notebook.add(self.scan_frame, text="📊 缓存扫描")
        self.setup_scan_tab()

        # 清理结果标签页
        self.result_frame = ttk.Frame(notebook)
        notebook.add(self.result_frame, text="📋 清理结果")
        self.setup_result_tab()

        # 设置标签页
        self.settings_frame = ttk.Frame(notebook)
        notebook.add(self.settings_frame, text="⚙️ 设置")
        self.setup_settings_tab()

        # 底部按钮栏
        self.setup_bottom_buttons(main_frame)

        # 初始扫描
        self.root.after(500, self.scan_cache)

    def setup_scan_tab(self):
        """设置缓存扫描标签页"""
        # 扫描结果框架
        scan_info_frame = ttk.LabelFrame(self.scan_frame, text="📊 扫描结果", padding=10)
        scan_info_frame.pack(fill=tk.X, pady=(0, 10))

        # 总计信息
        self.summary_label = ttk.Label(scan_info_frame,
                                      text="正在扫描缓存文件...",
                                      font=("Microsoft YaHei", 11))
        self.summary_label.pack(anchor=tk.W)

        # 缓存分类框架
        categories_frame = ttk.LabelFrame(self.scan_frame, text="🗂️ 缓存分类", padding=10)
        categories_frame.pack(fill=tk.BOTH, expand=True)

        # 创建滚动框架
        canvas = tk.Canvas(categories_frame)
        scrollbar = ttk.Scrollbar(categories_frame, orient=tk.VERTICAL, command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 布局
        canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.categories_container = scrollable_frame

    def setup_result_tab(self):
        """设置清理结果标签页"""
        # 结果信息
        result_info_frame = ttk.LabelFrame(self.result_frame, text="📋 清理统计", padding=10)
        result_info_frame.pack(fill=tk.X, pady=(0, 10))

        self.result_summary_label = ttk.Label(result_info_frame,
                                             text="尚未执行清理操作",
                                             font=("Microsoft YaHei", 11))
        self.result_summary_label.pack(anchor=tk.W)

        # 详细结果
        details_frame = ttk.LabelFrame(self.result_frame, text="📄 详细结果", padding=10)
        details_frame.pack(fill=tk.BOTH, expand=True)

        # 创建文本框显示详细结果
        self.result_text = tk.Text(details_frame, wrap=tk.WORD, font=("Consolas", 9))
        result_scrollbar = ttk.Scrollbar(details_frame, orient=tk.VERTICAL, command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=result_scrollbar.set)

        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        result_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def setup_settings_tab(self):
        """设置设置标签页"""
        # 清理策略
        strategy_frame = ttk.LabelFrame(self.settings_frame, text="🎯 清理策略", padding=10)
        strategy_frame.pack(fill=tk.X, pady=(0, 10))

        # 安全模式
        self.safe_mode_var = tk.BooleanVar(value=True)
        safe_mode_cb = ttk.Checkbutton(strategy_frame,
                                      text="🛡️ 安全模式（保留重要文件）",
                                      variable=self.safe_mode_var)
        safe_mode_cb.pack(anchor=tk.W, pady=2)

        # 确认删除
        self.confirm_delete_var = tk.BooleanVar(value=True)
        confirm_cb = ttk.Checkbutton(strategy_frame,
                                    text="❓ 删除前确认",
                                    variable=self.confirm_delete_var)
        confirm_cb.pack(anchor=tk.W, pady=2)

        # 路径信息
        path_frame = ttk.LabelFrame(self.settings_frame, text="📁 路径信息", padding=10)
        path_frame.pack(fill=tk.X, pady=(0, 10))

        # 项目根目录
        root_dir_label = ttk.Label(path_frame, text=f"项目根目录: {self.cleaner.root_dir}")
        root_dir_label.pack(anchor=tk.W, pady=2)

        # 更改目录按钮
        change_dir_btn = ttk.Button(path_frame, text="📂 更改目录", command=self.change_root_dir)
        change_dir_btn.pack(anchor=tk.W, pady=5)

        # 关于信息
        about_frame = ttk.LabelFrame(self.settings_frame, text="ℹ️ 关于", padding=10)
        about_frame.pack(fill=tk.BOTH, expand=True)

        about_text = """
缓存清理工具 v1.0

功能特点：
• 智能识别多种类型的缓存文件
• 按文件类型和时间进行分类清理
• 安全的清理策略，避免误删重要文件
• 支持试运行模式，预览清理效果
• 详细的清理报告和统计信息

支持清理的文件类型：
📄 日志文件 - 程序运行日志
📸 截图文件 - 存稿过程截图
🐍 Python缓存 - 字节码缓存
🗂️ 临时文件 - 各种临时文件
🌐 浏览器缓存 - WebDriver缓存
💾 数据备份 - 自动备份文件
⚙️ 配置缓存 - 配置临时文件
        """

        about_label = ttk.Label(about_frame, text=about_text, justify=tk.LEFT)
        about_label.pack(anchor=tk.W)

    def setup_bottom_buttons(self, parent):
        """设置底部按钮"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        # 左侧按钮
        left_buttons = ttk.Frame(button_frame)
        left_buttons.pack(side=tk.LEFT)

        ttk.Button(left_buttons, text="🔄 重新扫描", command=self.scan_cache).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(left_buttons, text="👁️ 试运行", command=self.dry_run_clean).pack(side=tk.LEFT, padx=5)

        # 右侧按钮
        right_buttons = ttk.Frame(button_frame)
        right_buttons.pack(side=tk.RIGHT)

        ttk.Button(right_buttons, text="❌ 关闭", command=self.root.destroy).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(right_buttons, text="🧹 开始清理", command=self.start_clean).pack(side=tk.RIGHT, padx=5)

    def scan_cache(self):
        """扫描缓存文件"""
        try:
            # 更新状态
            self.summary_label.config(text="正在扫描缓存文件...")
            self.root.update()

            # 执行扫描
            self.cleaner.scan_cache_files()

            # 更新界面
            self.update_scan_results()

        except Exception as e:
            messagebox.showerror("扫描错误", f"扫描缓存文件时发生错误:\n{str(e)}")

    def update_scan_results(self):
        """更新扫描结果显示"""
        # 更新总计信息
        summary = self.cleaner.get_cache_summary()
        if summary:
            summary_text = (f"📊 扫描完成！发现 {summary['total_count']} 个可清理文件，"
                          f"总大小 {summary['total_size']} ({summary['categories']} 个分类)")
            self.summary_label.config(text=summary_text)

        # 清空现有分类显示
        for widget in self.categories_container.winfo_children():
            widget.destroy()

        self.category_vars.clear()

        # 显示各分类
        for category, result in self.cleaner.scan_results.items():
            if category.startswith("_"):
                continue

            self.create_category_widget(category, result)

    def create_category_widget(self, category, result):
        """创建分类显示组件"""
        info = result["info"]

        # 创建分类框架
        category_frame = ttk.LabelFrame(self.categories_container,
                                       text=f"{info['icon']} {info['name']}",
                                       padding=5)
        category_frame.pack(fill=tk.X, pady=5)

        # 创建复选框变量
        var = tk.BooleanVar(value=True)
        self.category_vars[category] = var

        # 顶部信息行
        top_frame = ttk.Frame(category_frame)
        top_frame.pack(fill=tk.X)

        # 导入美化复选框
        try:
            from ..ui.styles.checkbox_styles import create_beautiful_checkbox
            cb = create_beautiful_checkbox(top_frame, text="", variable=var, style_type="modern")
        except ImportError:
            # 如果导入失败，使用默认复选框
            cb = ttk.Checkbutton(top_frame, variable=var)
        cb.pack(side=tk.LEFT)

        # 统计信息
        stats_text = (f"可清理: {result['cleanable_count']} 个文件 "
                     f"({self.cleaner.format_size(result['cleanable_size'])})")
        if result['total_count'] > result['cleanable_count']:
            stats_text += f" | 总计: {result['total_count']} 个文件"

        stats_label = ttk.Label(top_frame, text=stats_text)
        stats_label.pack(side=tk.LEFT, padx=(10, 0))

        # 描述信息
        desc_label = ttk.Label(category_frame, text=info['description'],
                              font=("Microsoft YaHei", 9), foreground="gray")
        desc_label.pack(anchor=tk.W, pady=(2, 0))

        # 保留策略信息
        if info['keep_days'] > 0:
            policy_text = f"保留策略: 保留最近 {info['keep_days']} 天的文件"
        else:
            policy_text = "保留策略: 清理所有文件"

        policy_label = ttk.Label(category_frame, text=policy_text,
                                font=("Microsoft YaHei", 8), foreground="blue")
        policy_label.pack(anchor=tk.W)

    def get_selected_categories(self):
        """获取选中的分类"""
        return [category for category, var in self.category_vars.items() if var.get()]

    def dry_run_clean(self):
        """试运行清理"""
        selected = self.get_selected_categories()
        if not selected:
            messagebox.showwarning("提示", "请至少选择一个要清理的分类")
            return

        try:
            # 执行试运行
            result = self.cleaner.clean_cache(selected, dry_run=True)

            # 显示结果
            self.show_clean_result(result, dry_run=True)

        except Exception as e:
            messagebox.showerror("试运行错误", f"试运行时发生错误:\n{str(e)}")

    def start_clean(self):
        """开始清理"""
        selected = self.get_selected_categories()
        if not selected:
            messagebox.showwarning("提示", "请至少选择一个要清理的分类")
            return

        # 确认对话框
        if self.confirm_delete_var.get():
            summary = self.cleaner.get_cache_summary()
            if summary:
                message = (f"确定要清理选中的缓存文件吗？\n\n"
                          f"将清理约 {summary['total_count']} 个文件，"
                          f"释放约 {summary['total_size']} 的空间。\n\n"
                          f"此操作不可撤销！")

                if not messagebox.askyesno("确认清理", message):
                    return

        try:
            # 执行清理
            result = self.cleaner.clean_cache(selected, dry_run=False)

            # 显示结果
            self.show_clean_result(result, dry_run=False)

            # 重新扫描
            self.root.after(1000, self.scan_cache)

        except Exception as e:
            messagebox.showerror("清理错误", f"清理时发生错误:\n{str(e)}")

    def show_clean_result(self, result, dry_run=False):
        """显示清理结果"""
        # 更新统计信息
        if dry_run:
            summary_text = (f"🔍 试运行完成！预计清理 {result['cleaned_count']} 个文件，"
                          f"释放 {self.cleaner.format_size(result['cleaned_size'])} 空间")
        else:
            summary_text = (f"✅ 清理完成！已清理 {result['cleaned_count']} 个文件，"
                          f"释放 {self.cleaner.format_size(result['cleaned_size'])} 空间")

        if result['failed_count'] > 0:
            summary_text += f"，{result['failed_count']} 个文件清理失败"

        self.result_summary_label.config(text=summary_text)

        # 更新详细结果
        self.result_text.delete(1.0, tk.END)

        # 添加标题
        title = "🔍 试运行结果\n" if dry_run else "✅ 清理结果\n"
        self.result_text.insert(tk.END, title)
        self.result_text.insert(tk.END, "=" * 50 + "\n\n")

        # 添加统计信息
        self.result_text.insert(tk.END, f"清理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        self.result_text.insert(tk.END, f"处理文件: {result['cleaned_count']} 个\n")
        self.result_text.insert(tk.END, f"释放空间: {self.cleaner.format_size(result['cleaned_size'])}\n")
        self.result_text.insert(tk.END, f"失败文件: {result['failed_count']} 个\n\n")

        # 按分类显示清理的文件
        if result['cleaned_files']:
            self.result_text.insert(tk.END, "📁 清理的文件:\n")
            self.result_text.insert(tk.END, "-" * 30 + "\n")

            # 按分类分组
            by_category = {}
            for file_info in result['cleaned_files']:
                category = file_info['category']
                if category not in by_category:
                    by_category[category] = []
                by_category[category].append(file_info)

            for category, files in by_category.items():
                category_info = self.cleaner.cache_categories.get(category, {})
                category_name = category_info.get('name', category)
                category_icon = category_info.get('icon', '📄')

                self.result_text.insert(tk.END, f"\n{category_icon} {category_name}:\n")

                total_size = sum(f['size'] for f in files)
                self.result_text.insert(tk.END, f"  文件数: {len(files)} 个，大小: {self.cleaner.format_size(total_size)}\n")

                for file_info in files[:10]:  # 只显示前10个文件
                    size_str = self.cleaner.format_size(file_info['size'])
                    self.result_text.insert(tk.END, f"  • {file_info['path']} ({size_str})\n")

                if len(files) > 10:
                    self.result_text.insert(tk.END, f"  ... 还有 {len(files) - 10} 个文件\n")

        # 显示失败的文件
        if result['failed_files']:
            self.result_text.insert(tk.END, "\n❌ 清理失败的文件:\n")
            self.result_text.insert(tk.END, "-" * 30 + "\n")

            for file_info in result['failed_files']:
                self.result_text.insert(tk.END, f"• {file_info['path']}: {file_info['error']}\n")

        # 滚动到顶部
        self.result_text.see(1.0)

    def change_root_dir(self):
        """更改根目录"""
        new_dir = filedialog.askdirectory(title="选择项目根目录", initialdir=self.cleaner.root_dir)
        if new_dir:
            self.cleaner.root_dir = new_dir
            self.scan_cache()
            messagebox.showinfo("提示", f"已更改根目录为: {new_dir}")


def main():
    """主函数"""
    try:
        # 创建GUI
        app = CacheCleanerGUI()
        app.root.mainloop()

    except Exception as e:
        print(f"启动缓存清理工具失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
