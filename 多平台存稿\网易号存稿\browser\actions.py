"""
浏览器操作模块 - 封装常用的浏览器操作
"""

import time
import traceback
from typing import Optional, Dict, Any, Callable, List, Tuple
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import (
    TimeoutException,
    NoSuchElementException,
    ElementNotInteractableException,
    StaleElementReferenceException
)

from ..common.utils import random_sleep

class BrowserActions:
    """浏览器操作类，封装常用的浏览器操作"""
    
    def __init__(self, driver: webdriver.Chrome, log_callback: Callable = None):
        """
        初始化浏览器操作
        
        Args:
            driver: Chrome浏览器驱动
            log_callback: 日志回调函数
        """
        self.driver = driver
        self.log_callback = log_callback
    
    def log(self, message: str) -> None:
        """
        记录日志
        
        Args:
            message: 日志消息
        """
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)
    
    def navigate_to(self, url: str, timeout: int = 30) -> bool:
        """
        导航到指定URL
        
        Args:
            url: 目标URL
            timeout: 超时时间（秒）
        
        Returns:
            是否成功导航
        """
        try:
            self.log(f"正在导航到: {url}")
            self.driver.get(url)
            return True
        except TimeoutException:
            self.log(f"导航超时: {url}")
            return False
        except Exception as e:
            self.log(f"导航失败: {str(e)}")
            return False
    
    def wait_for_element(self, by: By, value: str, timeout: int = 30) -> Optional[Any]:
        """
        等待元素出现
        
        Args:
            by: 定位方式
            value: 定位值
            timeout: 超时时间（秒）
        
        Returns:
            元素对象，如果未找到则返回None
        """
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((by, value))
            )
            return element
        except TimeoutException:
            self.log(f"等待元素超时: {by}={value}")
            return None
        except Exception as e:
            self.log(f"等待元素失败: {str(e)}")
            return None
    
    def wait_for_element_clickable(self, by: By, value: str, timeout: int = 30) -> Optional[Any]:
        """
        等待元素可点击
        
        Args:
            by: 定位方式
            value: 定位值
            timeout: 超时时间（秒）
        
        Returns:
            元素对象，如果未找到则返回None
        """
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.element_to_be_clickable((by, value))
            )
            return element
        except TimeoutException:
            self.log(f"等待元素可点击超时: {by}={value}")
            return None
        except Exception as e:
            self.log(f"等待元素可点击失败: {str(e)}")
            return None
    
    def click_element(self, by: By, value: str, timeout: int = 30, retry: int = 3) -> bool:
        """
        点击元素
        
        Args:
            by: 定位方式
            value: 定位值
            timeout: 超时时间（秒）
            retry: 重试次数
        
        Returns:
            是否成功点击
        """
        for attempt in range(retry):
            try:
                element = self.wait_for_element_clickable(by, value, timeout)
                if not element:
                    continue
                
                # 尝试常规点击
                element.click()
                random_sleep(0.5, 1.0)
                return True
            
            except (ElementNotInteractableException, StaleElementReferenceException):
                # 如果常规点击失败，尝试JavaScript点击
                try:
                    element = self.wait_for_element(by, value, timeout)
                    if element:
                        self.driver.execute_script("arguments[0].click();", element)
                        random_sleep(0.5, 1.0)
                        return True
                except:
                    pass
            
            except Exception as e:
                self.log(f"点击元素失败 (尝试 {attempt+1}/{retry}): {str(e)}")
            
            # 如果不是最后一次尝试，等待一段时间再重试
            if attempt < retry - 1:
                random_sleep(1.0, 2.0)
        
        self.log(f"点击元素失败，已达到最大重试次数: {by}={value}")
        return False
    
    def input_text(self, by: By, value: str, text: str, timeout: int = 30, clear_first: bool = True) -> bool:
        """
        在输入框中输入文本
        
        Args:
            by: 定位方式
            value: 定位值
            text: 要输入的文本
            timeout: 超时时间（秒）
            clear_first: 是否先清空输入框
        
        Returns:
            是否成功输入
        """
        try:
            element = self.wait_for_element(by, value, timeout)
            if not element:
                return False
            
            # 先清空输入框
            if clear_first:
                element.clear()
                random_sleep(0.2, 0.5)
            
            # 输入文本
            element.send_keys(text)
            random_sleep(0.5, 1.0)
            return True
        
        except Exception as e:
            self.log(f"输入文本失败: {str(e)}")
            return False
    
    def upload_file(self, by: By, value: str, file_path: str, timeout: int = 30) -> bool:
        """
        上传文件
        
        Args:
            by: 定位方式
            value: 定位值
            file_path: 文件路径
            timeout: 超时时间（秒）
        
        Returns:
            是否成功上传
        """
        try:
            element = self.wait_for_element(by, value, timeout)
            if not element:
                return False
            
            # 输入文件路径
            element.send_keys(file_path)
            self.log(f"已选择文件: {file_path}")
            return True
        
        except Exception as e:
            self.log(f"上传文件失败: {str(e)}")
            return False
    
    def execute_script(self, script: str, *args) -> Any:
        """
        执行JavaScript脚本
        
        Args:
            script: JavaScript脚本
            *args: 脚本参数
        
        Returns:
            脚本执行结果
        """
        try:
            return self.driver.execute_script(script, *args)
        except Exception as e:
            self.log(f"执行脚本失败: {str(e)}")
            return None
    
    def switch_to_frame(self, by: By, value: str, timeout: int = 30) -> bool:
        """
        切换到iframe
        
        Args:
            by: 定位方式
            value: 定位值
            timeout: 超时时间（秒）
        
        Returns:
            是否成功切换
        """
        try:
            frame = self.wait_for_element(by, value, timeout)
            if not frame:
                return False
            
            self.driver.switch_to.frame(frame)
            return True
        
        except Exception as e:
            self.log(f"切换到iframe失败: {str(e)}")
            return False
    
    def switch_to_default_content(self) -> None:
        """切换回主文档"""
        try:
            self.driver.switch_to.default_content()
        except Exception as e:
            self.log(f"切换回主文档失败: {str(e)}")
    
    def get_element_text(self, by: By, value: str, timeout: int = 30) -> Optional[str]:
        """
        获取元素文本
        
        Args:
            by: 定位方式
            value: 定位值
            timeout: 超时时间（秒）
        
        Returns:
            元素文本，如果未找到则返回None
        """
        try:
            element = self.wait_for_element(by, value, timeout)
            if not element:
                return None
            
            return element.text
        
        except Exception as e:
            self.log(f"获取元素文本失败: {str(e)}")
            return None
