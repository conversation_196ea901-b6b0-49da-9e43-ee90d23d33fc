"""
账号表格组件 - 遵循MECE原则的独立UI组件

职责：
- 创建和管理账号表格
- 提供搜索和排序功能
- 管理账号操作按钮
- 处理账号选择事件

独立性：不依赖其他UI组件，只依赖配置管理器和回调函数
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Optional, Callable, Dict, Any, List
import threading


# 不再使用tksheet，改用纯Treeview方案


class AccountsTable:
    """账号表格组件"""

    # 表格列定义 - 包含状态列和所有16个数据列
    COLUMNS = ("状态", "账号", "用户名", "七日收益", "总收益", "昨日收益", "总提现", "待提现",
              "最近提现日", "最近提现金额", "更新时间", "总播放", "昨日播放", "总粉丝", "昨日粉丝", "草稿箱")
    NUMERIC_COLUMNS = ["七日收益", "总收益", "昨日收益", "总提现", "待提现", "最近提现金额", "总播放", "昨日播放", "总粉丝", "昨日粉丝", "草稿箱"]

    def __init__(self, parent: tk.Widget, config_manager, callbacks: Dict[str, Callable] = None,
                 data_query_manager=None, current_platform=None, account_data=None, log_callback=None):
        """
        初始化账号表格

        Args:
            parent: 父容器
            config_manager: 配置管理器
            callbacks: 回调函数字典
            data_query_manager: 数据查询管理器
            current_platform: 当前平台
            account_data: 账号数据管理器
            log_callback: 日志回调函数
        """
        self.parent = parent
        self.config_manager = config_manager
        self.callbacks = callbacks or {}
        self.data_query_manager = data_query_manager
        self.current_platform = current_platform
        self.account_data = account_data
        self.log_callback = log_callback
        
        # 创建主容器
        self.container = ttk.Frame(parent)

        # 表格组件引用
        self.account_tree = None
        self.main_search_var = None

        # 排序状态
        self.main_sort_reverse = {}
        self.last_sort_column = None  # 最后排序的列
        self.last_sort_reverse = False  # 最后排序的方向
        
        # 创建组件
        self._create_accounts_table()

        # 创建右键菜单
        self._create_context_menu()





    def _set_status_image(self, item_id: str, status: str):
        """设置状态列的彩色文本指示灯"""
        try:
            # 获取状态对应的显示文本和标签
            status_text, status_tag = self._get_status_display(status)

            # 获取当前行的值
            current_values = list(self.account_tree.item(item_id, "values"))
            if current_values:
                # 更新状态列（第一列）
                current_values[0] = status_text
                self.account_tree.item(item_id, values=current_values, tags=(status_tag,))

        except Exception as e:
            self.log(f"设置状态指示灯失败: {str(e)}")

    def _get_status_display(self, status: str) -> tuple:
        """根据状态获取显示文本和标签"""
        if status in ["成功", "正常", "已查询", "登录验证成功"]:
            return "●", "status_success"  # 绿色圆点
        elif status in ["失败", "错误", "登录失败", "Cookie过期", "Cookie缺失",
                       "Cookie失效", "查询失败", "查询超时", "查询异常", "网络错误"]:
            return "●", "status_failed"   # 红色圆点
        else:
            return "●", "status_unknown"  # 灰色圆点

    def clear_data(self):
        """清空表格数据"""
        try:
            # 清空主表格
            for item in self.account_tree.get_children():
                self.account_tree.delete(item)
        except Exception as e:
            self.log(f"清空数据失败: {str(e)}")

    def insert_row(self, row_data: List[str], status: str = "未知") -> str:
        """插入数据行"""
        try:
            # 获取状态显示文本
            status_text, status_tag = self._get_status_display(status)

            # 在行数据前面插入状态列
            full_row_data = [status_text] + row_data

            # 在主表格中插入数据
            item_id = self.account_tree.insert("", "end", values=full_row_data, tags=(status_tag,))

            return item_id
        except Exception as e:
            self.log(f"插入行数据失败: {str(e)}")
            return ""

    def log(self, message: str) -> None:
        """
        记录日志

        Args:
            message: 日志消息
        """
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)
    
    def _create_accounts_table(self):
        """创建现代化账号表格"""
        # 表格容器
        table_container = ttk.Frame(self.container)
        table_container.pack(fill=tk.BOTH, expand=True)
        
        # 表格标题和功能区域
        table_title_frame = ttk.Frame(table_container)
        table_title_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 左侧：标题和搜索
        self._create_left_section(table_title_frame)
        
        # 创建表格框架
        self._create_table_frame(table_container)
    
    def _create_left_section(self, parent):
        """创建左侧标题和功能区域"""
        left_section = ttk.Frame(parent)
        left_section.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 标题行
        title_row = ttk.Frame(left_section)
        title_row.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(
            title_row,
            text="📋 账号列表",
            font=("微软雅黑", 12, "bold"),
            foreground="#495057"
        ).pack(side=tk.LEFT)
        
        # 账号目录按钮行
        directory_row = ttk.Frame(left_section)
        directory_row.pack(fill=tk.X, pady=(5, 5))
        
        ttk.Button(
            directory_row,
            text="📁 账号目录",
            command=self._get_callback('open_account_directory'),
            style="Tiny.TButton"
        ).pack(side=tk.LEFT, padx=2)
        
        # 搜索和功能按钮行
        function_row = ttk.Frame(left_section)
        function_row.pack(fill=tk.X)
        
        # 搜索框
        self._create_search_section(function_row)
        
        # 功能按钮
        self._create_function_buttons(function_row)
    
    def _create_search_section(self, parent):
        """创建搜索区域"""
        search_frame = ttk.Frame(parent)
        search_frame.pack(side=tk.LEFT)
        
        ttk.Label(search_frame, text="搜索:", font=("微软雅黑", 9)).pack(side=tk.LEFT, padx=(0, 3))
        self.main_search_var = tk.StringVar()
        self.main_search_var.trace_add("write", self._on_search_change)
        ttk.Entry(search_frame, textvariable=self.main_search_var, width=20, font=("微软雅黑", 9)).pack(side=tk.LEFT, padx=(0, 10))
    
    def _create_function_buttons(self, parent):
        """创建功能按钮"""
        # 创建小尺寸按钮样式
        style = ttk.Style()
        style.configure("Tiny.TButton", font=("微软雅黑", 8), padding=(3, 1))
        
        # 第一列功能按钮
        buttons_col1 = ttk.Frame(parent)
        buttons_col1.pack(side=tk.LEFT, padx=(0, 10))
        
        button_configs_col1 = [
            ("➕ 添加账号", "add_account"),
            ("🔍 查询数据", "start_data_query")
        ]
        
        for text, callback_key in button_configs_col1:
            ttk.Button(
                buttons_col1,
                text=text,
                command=self._get_callback(callback_key),
                style="Tiny.TButton"
            ).pack(side=tk.LEFT, padx=2)
        
        # 第二列功能按钮
        buttons_col2 = ttk.Frame(parent)
        buttons_col2.pack(side=tk.LEFT, padx=(0, 10))
        
        button_configs_col2 = [
            ("📊 导出Excel", "export_accounts_to_excel"),
            ("💾 备份数据", "backup_account_data"),
            ("📈 详细数据", "show_collected_data")
        ]
        
        for text, callback_key in button_configs_col2:
            ttk.Button(
                buttons_col2,
                text=text,
                command=self._get_callback(callback_key),
                style="Tiny.TButton"
            ).pack(side=tk.LEFT, padx=2)
        
        # 第三列功能按钮（账号和系统操作）
        buttons_col3 = ttk.Frame(parent)
        buttons_col3.pack(side=tk.LEFT, padx=(0, 10))
        
        button_configs_col3 = [
            ("🗑️ 批量删除", "show_batch_delete_dialog"),
            ("🗑️ 删除选中", "delete_selected_accounts"),
            ("🔄 刷新列表", "refresh_account_list"),
            ("🧹 清理重复", "show_duplicate_cleaner_dialog")
        ]
        
        for text, callback_key in button_configs_col3:
            ttk.Button(
                buttons_col3,
                text=text,
                command=self._get_callback(callback_key),
                style="Tiny.TButton"
            ).pack(side=tk.LEFT, padx=2)
    


    def _create_table_frame(self, parent):
        """创建表格框架"""
        # 创建表格框架
        table_frame = ttk.Frame(parent)
        table_frame.pack(fill=tk.BOTH, expand=True)

        # 创建主表格（包含状态列）
        self._create_main_table_with_status(table_frame)



    def _create_main_table_with_status(self, parent):
        """创建包含状态列的主表格"""
        # 创建表格容器
        tree_frame = ttk.Frame(parent)
        tree_frame.pack(fill=tk.BOTH, expand=True)

        # 定义列 - 使用中文列名，包含状态列和所有16个数据列
        columns = ("状态", "账号", "用户名", "七日收益", "总收益", "昨日收益", "总提现", "待提现",
                  "最近提现日", "最近提现金额", "更新时间", "总播放", "昨日播放", "总粉丝", "昨日粉丝", "草稿箱")

        # 创建Treeview - 删除滚动条但保留滚轮功能
        self.account_tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=15)

        # 设置列标题和宽度 - 16个列的紧凑布局，使用中文列名作为键
        headers = {
            "状态": ("状态", 20),                        # 状态指示灯
            "账号": ("账号", 75),                       # 11位手机号
            "用户名": ("用户名", 60),                    # 用户名
            "七日收益": ("七日收益", 50),           # 七日收益
            "总收益": ("总收益", 50),                # 总收益
            "昨日收益": ("昨日收益", 50),           # 昨日收益
            "总提现": ("总提现", 50),              # 总提现
            "待提现": ("待提现", 50),            # 待提现
            "最近提现日": ("最近提现日", 60),     # 最近提现日期
            "最近提现金额": ("最近提现金额", 60), # 最近提现金额
            "更新时间": ("更新时间", 80),               # 更新时间
            "总播放": ("总播放", 50),                 # 总播放
            "昨日播放": ("昨日播放", 50),            # 昨日播放
            "总粉丝": ("总粉丝", 50),                  # 总粉丝
            "昨日粉丝": ("昨日粉丝", 50),             # 昨日粉丝
            "草稿箱": ("草稿箱", 35)                       # 草稿箱
        }

        for col, (text, width) in headers.items():
            # 设置列标题和排序功能
            self.account_tree.heading(col, text=text, command=lambda c=col: self._on_column_sort(c))

            # 根据列类型设置对齐方式
            if col in ["状态"]:
                # 状态列居中对齐
                anchor = "center"
            elif col in ["账号", "用户名", "最近提现日", "更新时间"]:
                # 账号、用户名、日期时间左对齐
                anchor = "w"
            elif col in ["草稿箱"]:
                # 草稿箱数量居中对齐（通常是小数字）
                anchor = "center"
            else:
                # 数值类数据居中对齐（更好的视觉效果）
                anchor = "center"

            self.account_tree.column(col, width=width, anchor=anchor)

        # 配置状态列的标签样式
        self._configure_status_tags()

        # 不创建滚动条，但保留滚轮功能
        # 直接布局表格，占满整个框架
        self.account_tree.pack(fill=tk.BOTH, expand=True)

        # 绑定事件
        self._bind_table_events()

    def _bind_table_events(self):
        """绑定表格事件"""
        # 绑定鼠标滚轮事件以支持滚动
        def _on_mousewheel(event):
            self.account_tree.yview_scroll(int(-1*(event.delta/120)), "units")

        def _on_horizontal_mousewheel(event):
            self.account_tree.xview_scroll(int(-1*(event.delta/120)), "units")

        def _bind_mousewheel(event):
            self.account_tree.bind_all("<MouseWheel>", _on_mousewheel)
            # 绑定Shift+滚轮进行水平滚动
            self.account_tree.bind_all("<Shift-MouseWheel>", _on_horizontal_mousewheel)

        def _unbind_mousewheel(event):
            self.account_tree.unbind_all("<MouseWheel>")
            self.account_tree.unbind_all("<Shift-MouseWheel>")

        self.account_tree.bind('<Enter>', _bind_mousewheel)
        self.account_tree.bind('<Leave>', _unbind_mousewheel)

        # 绑定事件
        self.account_tree.bind("<Double-1>", self._on_account_select)  # 双击选择账号
        self.account_tree.bind("<Button-3>", self._show_context_menu)  # 右键菜单



    def _configure_status_tags(self):
        """配置状态列的标签样式"""
        # 成功状态 - 绿色
        self.account_tree.tag_configure("status_success", foreground="#006400", font=("Arial", 12, "bold"))

        # 失败状态 - 红色
        self.account_tree.tag_configure("status_failed", foreground="#DC143C", font=("Arial", 12, "bold"))

        # 未知状态 - 灰色
        self.account_tree.tag_configure("status_unknown", foreground="#696969", font=("Arial", 12, "bold"))



    def _load_sort_config(self):
        """加载排序配置"""
        try:
            sort_config = self.config_manager.get("main_table_sort", {}, platform="common")
            if sort_config:
                # 加载最后排序的列和方向
                last_column = sort_config.get("last_column")
                last_reverse = sort_config.get("last_reverse", False)
                if last_column:
                    self.main_sort_reverse[last_column] = last_reverse
                    # 应用已保存的排序配置
                    self._apply_saved_sort_config()
        except Exception:
            pass  # 忽略配置加载错误

    def _apply_saved_sort_config(self):
        """应用已保存的排序配置"""
        callback = self._get_callback('apply_saved_main_sort_config')
        if callback:
            callback()
    
    def _get_callback(self, callback_key: str) -> Callable:
        """获取回调函数"""
        return self.callbacks.get(callback_key, lambda: None)
    
    def _on_search_change(self, *args):
        """搜索变化事件处理"""
        callback = self._get_callback('filter_main_accounts')
        if callback:
            callback(*args)
    
    def _on_account_select(self, event):
        """账号选择事件处理"""
        callback = self._get_callback('on_account_select')
        if callback:
            callback(event)


    def _on_column_sort(self, column):
        """列排序事件处理"""
        try:
            # 直接在组件内处理排序
            self._sort_table_by_column(column)
        except Exception as e:
            # 如果组件内排序失败，尝试回调
            callback = self._get_callback('sort_main_table_by_column')
            if callback:
                callback(column)

    def _sort_table_by_column(self, column):
        """表格按列排序"""
        # 切换排序顺序
        if column not in self.main_sort_reverse:
            self.main_sort_reverse[column] = False
        else:
            self.main_sort_reverse[column] = not self.main_sort_reverse[column]

        reverse = self.main_sort_reverse[column]

        # 定义数值列，用于数值排序
        numeric_columns = self.NUMERIC_COLUMNS

        # 获取所有数据，分离总计行和普通数据行
        all_items = []
        total_item = None

        for child in self.account_tree.get_children():
            values = self.account_tree.item(child, "values")
            # 检查是否是总计行（账号列为"总计"）
            if len(values) > 1 and values[1] == "总计":
                total_item = (child, values)
            else:
                all_items.append((child, values))

        # 排序函数
        def parse_number(value):
            """解析数值，处理各种格式"""
            if not value or value == "" or value == "未查询":
                return float('-inf') if reverse else float('inf')  # 空值排序处理
            try:
                # 转换为字符串并清理
                cleaned = str(value).replace(",", "").replace("¥", "").replace("元", "").strip()
                if cleaned == "" or cleaned == "-" or cleaned == "未查询":
                    return float('-inf') if reverse else float('inf')

                # 处理"万"单位
                if "万" in cleaned:
                    # 移除"万"字符并转换为实际数值
                    num_str = cleaned.replace("万", "")
                    if num_str:
                        return float(num_str) * 10000
                    else:
                        return 0

                return float(cleaned)
            except (ValueError, TypeError):
                return 0

        # 根据列类型选择排序方式
        col_index = self.COLUMNS.index(column)

        if column in numeric_columns:
            # 数值排序
            all_items.sort(key=lambda x: parse_number(x[1][col_index]), reverse=reverse)
        else:
            # 文本排序，处理空值
            def text_sort_key(x):
                value = x[1][col_index]
                if value == "" or value == "未查询":
                    return "zzz" if not reverse else ""  # 空值排在最后
                return str(value).lower()
            all_items.sort(key=text_sort_key, reverse=reverse)

        # 重新排列表格项：先排列普通数据行，总计行始终在最后
        for index, (child, values) in enumerate(all_items):
            self.account_tree.move(child, "", index)

        # 总计行移到最后
        if total_item:
            self.account_tree.move(total_item[0], "", len(all_items))

        # 更新列标题显示排序方向
        for col in self.COLUMNS:
            if col == column:
                direction = " ↓" if reverse else " ↑"
                self.account_tree.heading(col, text=f"{col}{direction}", command=lambda c=col: self._on_column_sort(c))
            else:
                self.account_tree.heading(col, text=col, command=lambda c=col: self._on_column_sort(c))

        # 保存排序配置
        self._save_sort_config(column, reverse)

    def _save_sort_config(self, column, reverse):
        """保存排序配置"""
        try:
            # 保存到实例变量，供串行刷新机制使用
            self.last_sort_column = column
            self.last_sort_reverse = reverse

            # 保存到配置文件
            sort_config = {
                "last_column": column,
                "last_reverse": reverse
            }
            self.config_manager.set("main_table_sort", sort_config, platform="common")
        except Exception:
            pass  # 忽略保存错误
    
    def update_account_count(self, count: int):
        """更新账号数量显示 - 现在账号数量显示在侧边栏中"""
        # 账号数量现在显示在侧边栏的统计信息面板中
        # 这个方法保留以保持接口兼容性，但不再更新表格右上角的显示
        pass

    def refresh_accounts(self, accounts: list):
        """刷新账号列表显示"""
        # 触发账号过滤更新，这会重新加载账号列表
        if hasattr(self, 'callbacks') and 'filter_main_accounts' in self.callbacks:
            callback = self.callbacks['filter_main_accounts']
            if callback:
                callback()

        # 更新账号数量
        self.update_account_count(len(accounts))
    
    def get_search_text(self) -> str:
        """获取搜索文本"""
        if self.main_search_var:
            return self.main_search_var.get()
        return ""
    
    def get_selected_items(self) -> List:
        """获取选中的项目"""
        if self.account_tree:
            return self.account_tree.selection()
        return []
    
    def get_item_values(self, item_id) -> List:
        """获取项目值"""
        if self.account_tree:
            return self.account_tree.item(item_id)['values']
        return []
    
    def clear_selection(self):
        """清除选择"""
        if self.account_tree:
            self.account_tree.selection_remove(self.account_tree.selection())
    
    def pack(self, **kwargs):
        """打包容器"""
        self.container.pack(**kwargs)
    
    def grid(self, **kwargs):
        """网格布局容器"""
        self.container.grid(**kwargs)
    
    def destroy(self):
        """销毁组件"""
        if hasattr(self, 'container'):
            self.container.destroy()

    def _create_context_menu(self):
        """创建右键菜单 - 完全复制原版"""
        self.context_menu = tk.Menu(self.parent, tearoff=0)
        self.context_menu.add_command(label="复制账号", command=self._copy_account_name)
        self.context_menu.add_command(label="复制用户名", command=self._copy_username)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="查看详情", command=lambda: self._show_account_details_from_menu())
        # 添加查询数据选项
        self.context_menu.add_separator()
        self.context_menu.add_command(label="查询数据", command=self._query_selected_account_data)
        # 添加登录账号选项（头条、网易和大鱼平台）
        if self.current_platform in ["toutiao", "netease", "dayu"]:
            self.context_menu.add_separator()
            self.context_menu.add_command(label="登录账号", command=self._login_selected_account_from_menu)
        # 添加删除账号选项
        self.context_menu.add_separator()
        self.context_menu.add_command(label="删除账号", command=self._delete_selected_account)

    def _show_context_menu(self, event):
        """显示右键菜单 - 完全复制原版"""
        try:
            # 获取点击位置的项目
            item = self.account_tree.identify_row(event.y)
            if not item:
                return

            # 选中该项目
            self.account_tree.selection_set(item)

            # 显示菜单
            self.context_menu.post(event.x_root, event.y_root)
        except Exception as e:
            # 静默处理菜单显示错误，避免影响用户体验
            self.log(f"显示右键菜单时出错: {str(e)}")
        finally:
            # 确保菜单在异常情况下也能正确释放
            try:
                self.context_menu.grab_release()
            except:
                pass

    def _copy_account_name(self):
        """复制账号名称到剪贴板 - 完全复制原版"""
        # 获取选中的项目
        selected_items = self.account_tree.selection()
        if not selected_items:
            return

        # 获取选中的账号
        item = selected_items[0]
        values = self.account_tree.item(item, "values")
        if len(values) < 2:
            return

        account = values[1]  # 账号列的索引为1（状态列是索引0）

        if account == "总计":
            return

        # 复制到剪贴板
        try:
            self.parent.clipboard_clear()
            self.parent.clipboard_append(account)
            self.log(f"已复制账号: {account}")
        except Exception as e:
            self.log(f"复制账号到剪贴板失败: {str(e)}")
            messagebox.showerror("复制失败", f"复制账号到剪贴板失败:\n{str(e)}")

    def _copy_username(self):
        """复制用户名到剪贴板 - 完全复制原版"""
        # 获取选中的项目
        selected_items = self.account_tree.selection()
        if not selected_items:
            return

        # 获取选中的用户名
        item = selected_items[0]
        values = self.account_tree.item(item, "values")
        if len(values) < 3:
            return

        # 检查是否是总计行
        account = values[1]  # 账号列的索引为1（状态列是索引0）
        if account == "总计":
            return

        username = values[2]  # 用户名列的索引为2

        if not username or username == "":
            self.log("该账号没有用户名信息")
            return

        # 复制到剪贴板
        try:
            self.parent.clipboard_clear()
            self.parent.clipboard_append(username)
            self.log(f"已复制用户名: {username}")
        except Exception as e:
            self.log(f"复制用户名到剪贴板失败: {str(e)}")
            messagebox.showerror("复制失败", f"复制用户名到剪贴板失败:\n{str(e)}")

    def _query_selected_account_data(self):
        """查询选中账号的数据（右键菜单）"""
        # 获取选中的项目
        selected_items = self.account_tree.selection()
        if not selected_items:
            messagebox.showwarning("提示", "请先选择要查询的账号")
            return

        # 获取选中的账号
        item = selected_items[0]
        values = self.account_tree.item(item, "values")
        if len(values) < 2:
            return

        account = values[1]  # 账号列的索引为1（状态列是索引0）
        if account == "总计":
            messagebox.showwarning("提示", "无法查询总计行数据")
            return

        # 检查是否有数据查询管理器
        if not self.data_query_manager:
            messagebox.showerror("错误", "数据查询管理器未初始化")
            return

        # 启动单账号查询
        try:
            self.data_query_manager.start_single_account_query(account)
            self.log(f"🔍 已启动账号 {account} 的数据查询")
        except Exception as e:
            self.log(f"❌ 启动账号 {account} 数据查询失败: {str(e)}")
            messagebox.showerror("查询失败", f"启动账号 {account} 数据查询失败:\n{str(e)}")

    def _login_selected_account_from_menu(self):
        """登录选中的账号（右键菜单）"""
        # 获取选中的项目
        selected_items = self.account_tree.selection()
        if not selected_items:
            return

        # 获取选中的账号
        item = selected_items[0]
        values = self.account_tree.item(item, "values")
        if len(values) < 2:
            return

        account = values[1]  # 账号列的索引为1（状态列是索引0）
        if account == "总计":
            return

        self._login_account(account)

    def _delete_selected_account(self):
        """删除选中的账号（右键菜单） - 完全复制原版"""
        # 获取选中的项目
        selected_items = self.account_tree.selection()
        if not selected_items:
            messagebox.showwarning("警告", "请先选择要删除的账号")
            return

        # 获取选中的账号
        item = selected_items[0]
        values = self.account_tree.item(item, "values")
        if len(values) < 2:
            return

        account = values[1]  # 账号列的索引为1（状态列是索引0）

        if account == "总计":
            messagebox.showwarning("警告", "无法删除总计行")
            return

        # 确认删除
        if not messagebox.askyesno("确认删除", f"确定要删除账号 {account} 吗？"):
            return

        # 使用账号管理器删除账号
        try:
            if hasattr(self, 'account_manager') and self.account_manager:
                # 使用账号管理器的删除方法
                success = self.account_manager.delete_account(account)
                if success:
                    self.log(f"✅ 已删除账号: {account}")

                    # 刷新账号列表
                    refresh_callback = self.callbacks.get('refresh_account_list')
                    if refresh_callback:
                        refresh_callback()
                    else:
                        self.log(f"刷新账号列表功能未实现")
                else:
                    self.log(f"❌ 删除账号失败: {account}")
                    messagebox.showerror("删除失败", f"删除账号 {account} 失败")
            else:
                messagebox.showerror("错误", "账号管理器未初始化")

        except Exception as e:
            self.log(f"❌ 删除账号失败: {str(e)}")
            messagebox.showerror("删除失败", f"删除账号失败:\n{str(e)}")

    def _login_account(self, account):
        """登录指定账号"""
        if self.current_platform not in ["toutiao", "netease", "dayu"]:
            messagebox.showwarning("警告", "当前平台不支持登录功能")
            return

        # 根据平台显示不同的确认信息
        if self.current_platform == "toutiao":
            platform_name = "头条"
            login_url = "https://mp.toutiao.com/auth/page/login"
        elif self.current_platform == "dayu":
            platform_name = "大鱼号"
            login_url = "https://mp.dayu.com/"
        else:  # netease
            platform_name = "网易号"
            login_url = "https://mp.163.com/"

        # 确认登录
        if not messagebox.askyesno("确认登录", f"确定要登录{platform_name}账号 {account} 吗？\n\n登录地址：{login_url}\n\n登录后浏览器将保持打开状态，您可以随时手动关闭。"):
            return

        self.log(f"正在登录{platform_name}账号: {account}")

        # 在后台线程中执行登录，避免阻塞UI
        threading.Thread(target=self._login_account_thread, args=(account,), daemon=True).start()

    def _login_account_thread(self, account):
        """在后台线程中执行登录"""
        if self.current_platform == "toutiao":
            self._login_toutiao_account_thread(account)
        elif self.current_platform == "dayu":
            self._login_dayu_account_thread(account)
        elif self.current_platform == "netease":
            self._login_netease_account_thread(account)

    def _login_toutiao_account_thread(self, account):
        """在后台线程中执行头条账号登录"""
        try:
            # 导入必要的模块
            from 网易号存稿.platforms.toutiao.login import ToutiaoLogin

            # 获取账号目录 - 与存稿和查询功能保持一致
            import os
            if hasattr(self, 'account_manager') and self.account_manager and hasattr(self.account_manager, 'account_dir'):
                base_account_dir = self.account_manager.account_dir
            else:
                # 兼容旧的硬编码路径
                base_account_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), "账号")
            cookie_path = None

            # 只查找账号名.txt格式
            cookie_path = os.path.join(base_account_dir, f"{account}.txt")
            if not os.path.exists(cookie_path):
                cookie_path = None

            if not cookie_path:
                self.log(f"❌ 头条账号 {account} Cookie文件不存在")
                return

            self.log(f"找到头条账号 {account} 的Cookie文件: {cookie_path}")

            # 创建头条登录对象
            toutiao_login = ToutiaoLogin(self.log)

            # 使用Cookie登录
            self.log(f"正在使用Cookie登录头条账号: {account}")
            self.log(f"Cookie文件路径: {cookie_path}")

            success, driver = toutiao_login.login_with_cookies(cookie_path, headless=False)

            if success:
                self.log(f"✅ 头条账号 {account} 登录成功")

                # 在主线程中询问是否立即获取账号数据
                self.parent.after(0, lambda: self._ask_for_data_query_after_login(account, driver))
            else:
                self.log(f"❌ 头条账号 {account} 登录失败")

                # 在主线程中询问是否尝试手机号登录
                self.parent.after(0, lambda: self._ask_for_toutiao_phone_login(account, toutiao_login, cookie_path))

        except Exception as e:
            self.log(f"❌ 头条账号 {account} 登录异常: {str(e)}")
            import traceback
            traceback.print_exc()

    def _login_dayu_account_thread(self, account):
        """在后台线程中执行大鱼号账号登录"""
        try:
            # 导入必要的模块
            from 网易号存稿.platforms.dayu.login import DayuLogin

            # 获取账号目录 - 与存稿和查询功能保持一致
            import os
            if hasattr(self, 'account_manager') and self.account_manager and hasattr(self.account_manager, 'account_dir'):
                base_account_dir = self.account_manager.account_dir
            else:
                # 兼容旧的硬编码路径
                base_account_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), "账号")
            cookie_path = None

            # 只查找账号名.txt格式
            cookie_path = os.path.join(base_account_dir, f"{account}.txt")
            if not os.path.exists(cookie_path):
                cookie_path = None

            if not cookie_path:
                self.log(f"❌ 大鱼号账号 {account} Cookie文件不存在")
                return

            self.log(f"找到大鱼号账号 {account} 的Cookie文件: {cookie_path}")

            # 创建大鱼号登录对象
            dayu_login = DayuLogin(self.log)

            # 使用Cookie登录
            self.log(f"正在使用Cookie登录大鱼号账号: {account}")
            self.log(f"Cookie文件路径: {cookie_path}")

            success, driver = dayu_login.login_with_cookies(cookie_path, headless=False)

            if success:
                self.log(f"✅ 大鱼号账号 {account} 登录成功")

                # 在主线程中询问是否立即获取账号数据
                self.parent.after(0, lambda: self._ask_for_dayu_data_query_after_login(account, driver))
            else:
                self.log(f"❌ 大鱼号账号 {account} 登录失败")

                # 在主线程中询问是否尝试手机号登录
                self.parent.after(0, lambda: self._ask_for_dayu_phone_login(account, dayu_login, cookie_path))

        except Exception as e:
            self.log(f"❌ 大鱼号账号 {account} 登录异常: {str(e)}")
            import traceback
            traceback.print_exc()

    def _login_netease_account_thread(self, account):
        """在后台线程中执行网易号账号登录"""
        try:
            # 导入必要的模块
            from 网易号存稿.account.login import AccountLogin
            from 网易号存稿.browser.driver import DriverManager

            # 获取账号目录 - 与存稿和查询功能保持一致
            import os
            if hasattr(self, 'account_manager') and self.account_manager and hasattr(self.account_manager, 'account_dir'):
                base_account_dir = self.account_manager.account_dir
            else:
                # 兼容旧的硬编码路径
                base_account_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), "账号")
            cookie_path = None

            # 只查找账号名.txt格式
            cookie_path = os.path.join(base_account_dir, f"{account}.txt")
            if not os.path.exists(cookie_path):
                cookie_path = None

            if not cookie_path:
                self.log(f"❌ 网易号账号 {account} Cookie文件不存在")
                return

            self.log(f"找到网易号账号 {account} 的Cookie文件: {cookie_path}")

            # 创建网易号登录对象
            account_login = AccountLogin(self.log)

            # 使用Cookie登录
            self.log(f"正在使用Cookie登录网易号账号: {account}")
            self.log(f"Cookie文件路径: {cookie_path}")

            success, driver = account_login.login_with_cookies(cookie_path, headless=False, account=account)

            if success:
                self.log(f"✅ 网易号账号 {account} 登录成功")

                # 在主线程中询问是否立即获取账号数据
                self.parent.after(0, lambda: self._ask_for_netease_data_query_after_login(account, driver))
            else:
                self.log(f"❌ 网易号账号 {account} 登录失败")

        except Exception as e:
            self.log(f"❌ 网易号账号 {account} 登录异常: {str(e)}")
            import traceback
            traceback.print_exc()

    def _show_account_details_from_menu(self):
        """从右键菜单显示账号详情"""
        # 获取选中的项目
        selected_items = self.account_tree.selection()
        if not selected_items:
            return

        # 获取选中的账号
        item = selected_items[0]
        values = self.account_tree.item(item, "values")
        if len(values) < 2:
            return

        account = values[1]  # 账号列的索引为1（状态列是索引0）
        if account == "总计":
            return

        # 显示账号详情窗口
        self._show_account_details_window(account)

    def _show_account_details_window(self, account):
        """
        显示独立的账号详情窗口 - 完全复制原版布局

        【作废计划 目前仍在使用 但不更新】
        注意：此方法为账号详情窗口显示的实现，虽然标记为作废计划，但目前仍在使用中。
        该方法不再进行功能更新和维护，仅保持现有详情窗口功能的稳定运行。
        """
        # 查找账号数据
        account_data = None
        if self.account_data:
            all_accounts_data = self.account_data.get_all_accounts_data()

            for data in all_accounts_data:
                if data.get("账号") == account:
                    account_data = data
                    break

        if not account_data:
            messagebox.showwarning("警告", f"未找到账号 {account} 的数据")
            return

        # 创建详情窗口
        details_window = tk.Toplevel(self.parent)
        details_window.title(f"账号详情 - {account}")
        details_window.geometry("1100x800")  # 调整窗口尺寸以适应统一布局
        details_window.minsize(1000, 700)
        details_window.configure(bg="#f8f9fa")

        # 设置窗口图标
        try:
            details_window.iconbitmap(self.parent.iconbitmap())
        except:
            pass

        # 创建主框架
        main_frame = ttk.Frame(details_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 创建标题框架
        title_frame = tk.Frame(main_frame, bg="#ffffff", relief=tk.RAISED, bd=2)
        title_frame.pack(fill=tk.X, pady=(0, 20))

        # 标题内部框架
        title_inner = tk.Frame(title_frame, bg="#ffffff")
        title_inner.pack(fill=tk.X, padx=20, pady=15)

        # 左侧：账号信息
        left_title = tk.Frame(title_inner, bg="#ffffff")
        left_title.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 账号标题
        title_label = tk.Label(left_title, text=f"📱 {account}",
                              font=("Microsoft YaHei", 18, "bold"),
                              bg="#ffffff", fg="#2c3e50")
        title_label.pack(side=tk.LEFT, anchor=tk.W)

        # 用户名（如果有）
        username = account_data.get("用户名", "")
        if username and username != "未设置":
            username_label = tk.Label(left_title, text=f"({username})",
                                    font=("Microsoft YaHei", 12),
                                    bg="#ffffff", fg="#7f8c8d")
            username_label.pack(side=tk.LEFT, padx=(10, 0), anchor=tk.W)

        # 右侧：状态和更新时间
        right_title = tk.Frame(title_inner, bg="#ffffff")
        right_title.pack(side=tk.RIGHT)

        # 状态标签
        status = account_data.get("状态", "未知")
        status_color = self._get_status_color(status)
        status_frame = tk.Frame(right_title, bg="#ffffff")
        status_frame.pack(side=tk.TOP, anchor=tk.E)

        status_label = tk.Label(status_frame, text=f"● {status}",
                               font=("Microsoft YaHei", 12, "bold"),
                               bg="#ffffff", fg=status_color)
        status_label.pack()

        # 更新时间
        update_time = account_data.get("更新时间", "未知")
        if update_time != "未知":
            try:
                from datetime import datetime
                if len(str(update_time)) > 10:
                    dt = datetime.strptime(str(update_time)[:19], "%Y-%m-%d %H:%M:%S")
                    update_time_display = dt.strftime("%Y-%m-%d %H:%M")  # 显示完整日期格式
                else:
                    update_time_display = str(update_time)
            except:
                update_time_display = str(update_time)

            time_label = tk.Label(right_title, text=f"🕒 {update_time_display}",
                                font=("Microsoft YaHei", 10),
                                bg="#ffffff", fg="#6c757d")
            time_label.pack(side=tk.TOP, anchor=tk.E, pady=(5, 0))

        # 创建统一的内容区域
        content_frame = tk.Frame(main_frame, bg="#f8f9fa")
        content_frame.pack(fill=tk.BOTH, expand=True)

        # 创建滚动区域
        canvas = tk.Canvas(content_frame, bg="#f8f9fa")
        canvas.pack(fill=tk.BOTH, expand=True)

        scrollable_frame = tk.Frame(canvas, bg="#f8f9fa")
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")

        # 绑定鼠标滚轮事件 - 修复卡死问题
        def _on_mousewheel(event):
            try:
                # 检查canvas是否仍然有效
                if canvas.winfo_exists():
                    canvas.yview_scroll(int(-1*(event.delta/120)), "units")
            except tk.TclError:
                # canvas已销毁，解绑事件
                try:
                    canvas.unbind_all("<MouseWheel>")
                except:
                    pass

        def _bind_mousewheel(event):
            canvas.bind_all("<MouseWheel>", _on_mousewheel)

        def _unbind_mousewheel(event):
            canvas.unbind_all("<MouseWheel>")

        # 只在鼠标进入canvas区域时绑定滚轮事件
        canvas.bind('<Enter>', _bind_mousewheel)
        canvas.bind('<Leave>', _unbind_mousewheel)

        # 窗口关闭时确保解绑滚轮事件
        def on_window_close():
            try:
                canvas.unbind_all("<MouseWheel>")
            except:
                pass
            details_window.destroy()

        details_window.protocol("WM_DELETE_WINDOW", on_window_close)

        # 创建统一的账号详情内容
        self.create_unified_account_details(scrollable_frame, account_data)

        # 底部按钮框架
        button_frame = tk.Frame(main_frame, bg="#ffffff", relief=tk.RAISED, bd=1)
        button_frame.pack(fill=tk.X, pady=(20, 0))

        # 按钮内部框架
        button_inner = tk.Frame(button_frame, bg="#ffffff")
        button_inner.pack(fill=tk.X, padx=20, pady=15)

        # 左侧按钮组
        left_buttons = tk.Frame(button_inner, bg="#ffffff")
        left_buttons.pack(side=tk.LEFT)

        # 复制账号按钮
        copy_account_btn = ttk.Button(left_buttons, text="📋 复制账号",
                                     command=lambda: self.copy_text_to_clipboard(account))
        copy_account_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 复制用户名按钮
        username = account_data.get("用户名", "")
        if username and username != "未设置":
            copy_username_btn = ttk.Button(left_buttons, text="👤 复制用户名",
                                          command=lambda: self.copy_text_to_clipboard(username))
            copy_username_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 复制收益信息按钮
        total_income = account_data.get("总收益", "0")  # 统一标准字段
        copy_income_btn = ttk.Button(left_buttons, text="💰 复制收益",
                                   command=lambda: self.copy_text_to_clipboard(f"账号: {account}, 总收益: {total_income}"))
        copy_income_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 中间按钮组
        center_buttons = tk.Frame(button_inner, bg="#ffffff")
        center_buttons.pack(side=tk.LEFT, expand=True)

        # 导出详情按钮
        export_btn = ttk.Button(center_buttons, text="📤 导出详情",
                               command=lambda: self.export_account_details(account, account_data))
        export_btn.pack(side=tk.LEFT, padx=10)

        # 右侧按钮组
        right_buttons = tk.Frame(button_inner, bg="#ffffff")
        right_buttons.pack(side=tk.RIGHT)

        # 刷新按钮
        refresh_btn = ttk.Button(right_buttons, text="🔄 刷新",
                                command=lambda: self.refresh_account_details(details_window, account))
        refresh_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 关闭按钮
        close_btn = ttk.Button(right_buttons, text="❌ 关闭", command=on_window_close)
        close_btn.pack(side=tk.LEFT)

        # 居中显示窗口
        self._center_window(details_window)

    def create_unified_account_details(self, parent, account_data):
        """创建统一的账号详情内容 - 完全复制原版布局"""
        # 使用网格布局来更好地控制空间分配
        parent.grid_columnconfigure(0, weight=1)
        parent.grid_columnconfigure(1, weight=1)
        parent.grid_columnconfigure(2, weight=1)
        parent.grid_rowconfigure(0, weight=1)
        parent.grid_rowconfigure(1, weight=1)

        # 第一行：基本信息、收益概览、设置卡片（包含七日收益）
        # 左侧：基本信息卡片
        basic_card = tk.Frame(parent, bg="#ffffff", relief=tk.RAISED, bd=2)
        basic_card.grid(row=0, column=0, padx=(15, 5), pady=15, sticky="nsew")
        self.create_basic_info_card(basic_card, account_data)

        # 中间：收益概览卡片
        income_card = tk.Frame(parent, bg="#ffffff", relief=tk.RAISED, bd=2)
        income_card.grid(row=0, column=1, padx=5, pady=15, sticky="nsew")
        self.create_main_income_card(income_card, account_data)

        # 右侧：设置卡片（包含七日收益数据）
        settings_card = tk.Frame(parent, bg="#ffffff", relief=tk.RAISED, bd=2)
        settings_card.grid(row=0, column=2, padx=(5, 15), pady=15, sticky="nsew")
        self.create_settings_card_with_seven_day(settings_card, account_data)

        # 第二行：提现信息、播放粉丝数据、装饰性卡片
        # 左侧：提现信息
        withdraw_card = tk.Frame(parent, bg="#ffffff", relief=tk.RAISED, bd=2)
        withdraw_card.grid(row=1, column=0, padx=(15, 5), pady=(0, 15), sticky="nsew")
        self.create_withdraw_info_card(withdraw_card, account_data)

        # 中间：播放和粉丝数据
        stats_card = tk.Frame(parent, bg="#ffffff", relief=tk.RAISED, bd=2)
        stats_card.grid(row=1, column=1, padx=5, pady=(0, 15), sticky="nsew")
        self.create_stats_card(stats_card, account_data)

        # 右侧：创建不规则形状的装饰性卡片
        decorative_card = tk.Frame(parent, bg="#ffffff", relief=tk.FLAT, bd=0)
        decorative_card.grid(row=1, column=2, padx=(5, 15), pady=(0, 15), sticky="nsew")
        self.create_irregular_decorative_card(decorative_card, account_data)

    def create_basic_info_card(self, parent, account_data):
        """创建基本信息卡片"""
        # 标题
        title_frame = tk.Frame(parent, bg="#ffffff")
        title_frame.pack(fill=tk.X, padx=20, pady=(15, 10))

        title_label = tk.Label(title_frame, text="📋 基本信息",
                              font=("Microsoft YaHei", 14, "bold"),
                              bg="#ffffff", fg="#2c3e50")
        title_label.pack(side=tk.LEFT)

        # 内容区域
        content_frame = tk.Frame(parent, bg="#ffffff")
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 15))

        # 格式化更新时间
        update_time = account_data.get("更新时间", "未知")
        if update_time != "未知" and update_time:
            try:
                from datetime import datetime
                if isinstance(update_time, str) and len(update_time) > 10:
                    dt = datetime.strptime(update_time[:19], "%Y-%m-%d %H:%M:%S")
                    update_time = dt.strftime("%Y-%m-%d %H:%M")
            except:
                pass

        # 格式化草稿箱数量
        draft_count = account_data.get("草稿箱", "0")  # 统一标准字段
        if str(draft_count).isdigit():
            draft_count = f"{draft_count} 篇"

        basic_info = [
            ("账号", "📱", account_data.get("账号", ""), "#007bff"),
            ("用户名", "👤", account_data.get("用户名", "未设置"), "#28a745"),
            ("状态", "🔄", account_data.get("状态", "未知"), self._get_status_color(account_data.get("状态", "未知"))),
            ("更新时间", "🕒", update_time, "#6c757d"),
            ("草稿箱", "📝", draft_count, "#17a2b8"),
        ]

        for i, (label, icon, value, color) in enumerate(basic_info):
            row_frame = tk.Frame(content_frame, bg="#f8f9fa", relief=tk.FLAT, bd=1)
            row_frame.pack(fill=tk.X, pady=3, padx=5)

            inner_frame = tk.Frame(row_frame, bg="#f8f9fa")
            inner_frame.pack(fill=tk.X, padx=10, pady=8)

            # 左侧图标和标签
            left_frame = tk.Frame(inner_frame, bg="#f8f9fa")
            left_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

            icon_label = tk.Label(left_frame, text=f"{icon} {label}",
                                font=("Microsoft YaHei", 10, "bold"),
                                bg="#f8f9fa", fg=color)
            icon_label.pack(side=tk.LEFT, anchor=tk.W)

            # 右侧值
            value_label = tk.Label(inner_frame, text=str(value),
                                 font=("Microsoft YaHei", 10),
                                 bg="#f8f9fa", fg="#212529")
            value_label.pack(side=tk.RIGHT, anchor=tk.E)

    def _get_status_color(self, status):
        """根据状态获取颜色 - 完全复制原版"""
        status_colors = {
            "成功": "#28a745",
            "失败": "#dc3545",
            "错误": "#dc3545",
            "警告": "#ffc107",
            "进行中": "#17a2b8",
            "未知": "#6c757d"
        }
        return status_colors.get(status, "#6c757d")

    def create_main_income_card(self, parent, account_data):
        """创建主要收益指标卡片 - 完全复制原版"""
        # 标题
        title_frame = tk.Frame(parent, bg="#ffffff")
        title_frame.pack(fill=tk.X, padx=20, pady=(15, 10))

        title_label = tk.Label(title_frame, text="💰 收益概览",
                              font=("Microsoft YaHei", 14, "bold"),
                              bg="#ffffff", fg="#2c3e50")
        title_label.pack(side=tk.LEFT)

        # 内容区域
        content_frame = tk.Frame(parent, bg="#ffffff")
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 15))

        # 计算七日收益总和
        seven_day_total = 0
        if "七日收益" in account_data and isinstance(account_data["七日收益"], list):
            for day_data in account_data["七日收益"]:
                if isinstance(day_data, dict):
                    income = day_data.get("收益", 0)
                    try:
                        seven_day_total += float(income) if income else 0
                    except:
                        pass

        # 格式化金额函数
        def format_money(value):
            try:
                if isinstance(value, str) and value.replace('.', '').replace('-', '').isdigit():
                    num = float(value)
                elif isinstance(value, (int, float)):
                    num = float(value)
                else:
                    return str(value)

                if num == 0:
                    return "0.00 元"
                elif num >= 1000:
                    return f"{num:,.2f} 元"
                else:
                    return f"{num:.2f} 元"
            except:
                return str(value)

        income_info = [
            ("总收益", "💰", format_money(account_data.get("总收益", "0")), "#28a745"),  # 统一标准字段
            ("昨日收益", "📈", format_money(account_data.get("昨日收益", "0")), "#17a2b8"),
            ("七日收益", "📊", format_money(seven_day_total), "#6f42c1"),
        ]

        for i, (label, icon, value, color) in enumerate(income_info):
            row_frame = tk.Frame(content_frame, bg="#f8f9fa", relief=tk.FLAT, bd=1)
            row_frame.pack(fill=tk.X, pady=3, padx=5)

            inner_frame = tk.Frame(row_frame, bg="#f8f9fa")
            inner_frame.pack(fill=tk.X, padx=10, pady=8)

            # 左侧图标和标签
            left_frame = tk.Frame(inner_frame, bg="#f8f9fa")
            left_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

            icon_label = tk.Label(left_frame, text=f"{icon} {label}",
                                font=("Microsoft YaHei", 10, "bold"),
                                bg="#f8f9fa", fg=color)
            icon_label.pack(side=tk.LEFT, anchor=tk.W)

            # 右侧值
            value_label = tk.Label(inner_frame, text=str(value),
                                 font=("Microsoft YaHei", 11, "bold"),
                                 bg="#f8f9fa", fg=color)
            value_label.pack(side=tk.RIGHT, anchor=tk.E)

    def create_settings_card_with_seven_day(self, parent, account_data):
        """创建设置卡片，包含七日收益数据 - 完全复制原版"""
        # 标题
        title_frame = tk.Frame(parent, bg="#ffffff")
        title_frame.pack(fill=tk.X, padx=20, pady=(15, 10))

        title_label = tk.Label(title_frame, text="📊 七日收益数据",
                              font=("Microsoft YaHei", 14, "bold"),
                              bg="#ffffff", fg="#2c3e50")
        title_label.pack(side=tk.LEFT)

        # 内容区域
        content_frame = tk.Frame(parent, bg="#ffffff")
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 15))

        # 检查是否有七日收益数据
        seven_day_data = account_data.get("七日收益", [])
        if isinstance(seven_day_data, list) and seven_day_data:
            # 计算统计信息
            total_income = 0
            for day_data in seven_day_data:
                if isinstance(day_data, dict):
                    income = day_data.get("收益", 0)
                    try:
                        total_income += float(income) if income else 0
                    except:
                        pass

            # 统计信息
            stats_frame = tk.Frame(content_frame, bg="#e8f4fd", relief=tk.FLAT, bd=1)
            stats_frame.pack(fill=tk.X, pady=(0, 15), padx=5)

            stats_inner = tk.Frame(stats_frame, bg="#e8f4fd")
            stats_inner.pack(fill=tk.X, padx=15, pady=10)

            stats_text = f"📊 七日总收益: {total_income:.2f}元  📅 数据天数: {len(seven_day_data)}天"
            stats_label = tk.Label(stats_inner, text=stats_text,
                                  font=("Microsoft YaHei", 11, "bold"),
                                  bg="#e8f4fd", fg="#6f42c1")
            stats_label.pack()

            # 创建七日收益数据的网格布局 - 使用更多列来填充空间
            seven_day_frame = tk.Frame(content_frame, bg="#ffffff")
            seven_day_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

            # 根据数据量决定列数，最多4列
            data_count = len(seven_day_data)
            if data_count <= 4:
                cols = 2
            elif data_count <= 8:
                cols = 3
            else:
                cols = 4

            # 配置网格权重
            for i in range(cols):
                seven_day_frame.grid_columnconfigure(i, weight=1)

            # 显示七日收益数据（原来的格式，但使用更多列）
            row = 0
            col = 0
            for day_data in seven_day_data:
                if isinstance(day_data, dict):
                    date = day_data.get("日期", "")
                    income = day_data.get("收益", "0")

                    # 格式化日期显示（只显示月-日）
                    try:
                        from datetime import datetime
                        if len(date) >= 10:
                            dt = datetime.strptime(date[:10], "%Y-%m-%d")
                            date_display = dt.strftime("%m-%d")
                        else:
                            date_display = date
                    except:
                        date_display = date

                    # 格式化收益显示
                    try:
                        income_display = f"{float(income):.2f}元" if income else "0.00元"
                    except:
                        income_display = str(income)

                    # 创建数据项 - 更大的卡片
                    item_frame = tk.Frame(seven_day_frame, bg="#f8f9fa", relief=tk.RAISED, bd=1)
                    item_frame.grid(row=row, column=col, padx=5, pady=4, sticky="ew")

                    inner_frame = tk.Frame(item_frame, bg="#f8f9fa")
                    inner_frame.pack(fill=tk.X, padx=12, pady=10)

                    # 日期 - 居中显示
                    date_label = tk.Label(inner_frame, text=date_display,
                                        font=("Microsoft YaHei", 10, "bold"),
                                        bg="#f8f9fa", fg="#495057")
                    date_label.pack()

                    # 收益 - 居中显示，更突出
                    income_label = tk.Label(inner_frame, text=income_display,
                                          font=("Microsoft YaHei", 11, "bold"),
                                          bg="#f8f9fa", fg="#6f42c1")
                    income_label.pack(pady=(3, 0))

                    # 更新行列位置
                    col += 1
                    if col >= cols:
                        col = 0
                        row += 1
        else:
            # 如果没有七日收益数据，显示提示信息并填充空间
            no_data_frame = tk.Frame(content_frame, bg="#f8f9fa", relief=tk.FLAT, bd=1)
            no_data_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=10)

            no_data_inner = tk.Frame(no_data_frame, bg="#f8f9fa")
            no_data_inner.pack(expand=True)

            icon_label = tk.Label(no_data_inner, text="📊",
                                font=("Microsoft YaHei", 24),
                                bg="#f8f9fa", fg="#6c757d")
            icon_label.pack(pady=(20, 10))

            no_data_label = tk.Label(no_data_inner, text="暂无七日收益数据",
                                   font=("Microsoft YaHei", 12),
                                   bg="#f8f9fa", fg="#6c757d")
            no_data_label.pack()

            tip_label = tk.Label(no_data_inner, text="数据查询完成后将在此显示",
                               font=("Microsoft YaHei", 10),
                               bg="#f8f9fa", fg="#adb5bd")
            tip_label.pack(pady=(5, 20))

    def create_irregular_decorative_card(self, parent, account_data):
        """创建不规则形状的装饰性卡片 - 完全复制原版"""
        import tkinter as tk
        from tkinter import Canvas
        import math

        # 创建画布
        canvas = Canvas(parent, bg="#ffffff", highlightthickness=0)
        canvas.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        def draw_shapes(event=None):
            canvas.delete("all")
            width = canvas.winfo_width()
            height = canvas.winfo_height()

            if width <= 1 or height <= 1:
                return

            # 渐变色系
            colors = ["#667eea", "#764ba2", "#f093fb", "#f5576c", "#4facfe", "#00f2fe"]
            light_colors = ["#e8f0fe", "#f3e8ff", "#fef0ff", "#ffe8ec", "#e8f8ff", "#e0fffe"]

            # 背景渐变效果（用多个矩形模拟）
            for i in range(10):
                alpha = i / 10
                y = height * alpha / 10
                canvas.create_rectangle(0, y, width, y + height/10,
                                      fill="#f8f9fa", outline="")

            # 形状1：流体形状（左上）
            points = []
            for i in range(20):
                angle = i * 18 * math.pi / 180
                r = width * 0.15 + width * 0.05 * math.sin(angle * 3)
                x = width * 0.25 + r * math.cos(angle)
                y = height * 0.25 + r * math.sin(angle) * 0.7
                points.extend([x, y])
            canvas.create_polygon(points, fill=light_colors[0], outline=colors[0], width=2, smooth=True)

            # 形状2：六边形（右上）
            center_x, center_y = width * 0.75, height * 0.25
            radius = min(width, height) * 0.12
            hex_points = []
            for i in range(6):
                angle = i * 60 * math.pi / 180
                x = center_x + radius * math.cos(angle)
                y = center_y + radius * math.sin(angle)
                hex_points.extend([x, y])
            canvas.create_polygon(hex_points, fill=light_colors[1], outline=colors[1], width=2)

            # 形状3：波浪圆（左下）
            center_x, center_y = width * 0.3, height * 0.7
            points = []
            for i in range(30):
                angle = i * 12 * math.pi / 180
                r = width * 0.1 + width * 0.03 * math.sin(angle * 4)
                x = center_x + r * math.cos(angle)
                y = center_y + r * math.sin(angle)
                points.extend([x, y])
            canvas.create_polygon(points, fill=light_colors[2], outline=colors[2], width=2, smooth=True)

            # 形状4：螺旋形（右下）
            center_x, center_y = width * 0.7, height * 0.7
            points = []
            for i in range(50):
                angle = i * 7.2 * math.pi / 180
                r = i * 0.8
                x = center_x + r * math.cos(angle)
                y = center_y + r * math.sin(angle)
                if x < width and y < height and x > 0 and y > 0:
                    points.extend([x, y])
            if len(points) >= 4:
                canvas.create_line(points, fill=colors[3], width=3, smooth=True)

            # 形状5：三角形组合（中心）
            triangles = [
                [width*0.5, height*0.4, width*0.45, height*0.5, width*0.55, height*0.5],
                [width*0.5, height*0.6, width*0.45, height*0.5, width*0.55, height*0.5],
                [width*0.4, height*0.5, width*0.5, height*0.45, width*0.5, height*0.55],
                [width*0.6, height*0.5, width*0.5, height*0.45, width*0.5, height*0.55]
            ]
            for i, triangle in enumerate(triangles):
                canvas.create_polygon(triangle, fill=light_colors[4], outline=colors[4], width=1)

            # 添加小圆点装饰
            for i in range(15):
                x = width * (0.1 + 0.8 * (i * 7 % 13) / 13)
                y = height * (0.1 + 0.8 * (i * 11 % 17) / 17)
                r = 3 + (i % 3)
                canvas.create_oval(x-r, y-r, x+r, y+r,
                                 fill=colors[i % len(colors)], outline="")

            # 装饰性文字
            canvas.create_text(width * 0.5, height * 0.85,
                             text="🎨 创意设计",
                             font=("Microsoft YaHei", 14, "bold"),
                             fill="#495057")

            canvas.create_text(width * 0.5, height * 0.92,
                             text="Art & Data",
                             font=("Arial", 10, "italic"),
                             fill="#6c757d")

        # 绑定重绘事件
        canvas.bind("<Configure>", draw_shapes)
        parent.after(100, draw_shapes)  # 延迟绘制确保尺寸正确

    def create_withdraw_info_card(self, parent, account_data):
        """创建提现信息卡片 - 完全复制原版"""
        # 标题
        title_frame = tk.Frame(parent, bg="#ffffff")
        title_frame.pack(fill=tk.X, padx=20, pady=(15, 10))

        title_label = tk.Label(title_frame, text="💸 提现信息",
                              font=("Microsoft YaHei", 14, "bold"),
                              bg="#ffffff", fg="#2c3e50")
        title_label.pack(side=tk.LEFT)

        # 内容区域
        content_frame = tk.Frame(parent, bg="#ffffff")
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 15))

        # 格式化金额函数
        def format_money(value):
            try:
                if isinstance(value, str) and value.replace('.', '').replace('-', '').isdigit():
                    num = float(value)
                elif isinstance(value, (int, float)):
                    num = float(value)
                else:
                    return str(value)
                return f"{num:.2f} 元" if num != 0 else "0.00 元"
            except:
                return str(value)

        # 格式化最近提现日期
        recent_withdraw = account_data.get("最近提现日期", account_data.get("最近提现日", account_data.get("最近提现时间", "未知")))
        if recent_withdraw and recent_withdraw != "未知":
            try:
                from datetime import datetime
                if len(str(recent_withdraw)) >= 10:
                    dt = datetime.strptime(str(recent_withdraw)[:10], "%Y-%m-%d")
                    recent_withdraw = dt.strftime("%Y年%m月%d日")
            except:
                pass

        # 获取最近提现金额
        recent_withdraw_amount = account_data.get("最近提现", account_data.get("最近提现金额", "0"))

        withdraw_info = [
            ("总提现", "💸", format_money(account_data.get("总提现", "0")), "#fd7e14"),
            ("待提现", "💳", format_money(account_data.get("待提现", "0")), "#20c997"),  # 统一标准字段
            ("最近提现金额", "💰", format_money(recent_withdraw_amount), "#28a745"),
            ("最近提现日期", "📅", recent_withdraw, "#6c757d"),
        ]

        for label, icon, value, color in withdraw_info:
            row_frame = tk.Frame(content_frame, bg="#f8f9fa", relief=tk.FLAT, bd=1)
            row_frame.pack(fill=tk.X, pady=3, padx=5)

            inner_frame = tk.Frame(row_frame, bg="#f8f9fa")
            inner_frame.pack(fill=tk.X, padx=10, pady=8)

            # 左侧图标和标签
            left_frame = tk.Frame(inner_frame, bg="#f8f9fa")
            left_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

            icon_label = tk.Label(left_frame, text=f"{icon} {label}",
                                font=("Microsoft YaHei", 10, "bold"),
                                bg="#f8f9fa", fg=color)
            icon_label.pack(side=tk.LEFT, anchor=tk.W)

            # 右侧值
            value_label = tk.Label(inner_frame, text=str(value),
                                 font=("Microsoft YaHei", 10),
                                 bg="#f8f9fa", fg="#212529")
            value_label.pack(side=tk.RIGHT, anchor=tk.E)

    def create_stats_card(self, parent, account_data):
        """创建播放和粉丝数据卡片 - 完全复制原版"""
        # 标题
        title_frame = tk.Frame(parent, bg="#ffffff")
        title_frame.pack(fill=tk.X, padx=20, pady=(15, 10))

        title_label = tk.Label(title_frame, text="📊 数据统计",
                              font=("Microsoft YaHei", 14, "bold"),
                              bg="#ffffff", fg="#2c3e50")
        title_label.pack(side=tk.LEFT)

        # 内容区域
        content_frame = tk.Frame(parent, bg="#ffffff")
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 15))

        # 格式化数字函数
        def format_number(value):
            try:
                if isinstance(value, str) and value.replace(',', '').replace('.', '').isdigit():
                    num = float(value.replace(',', ''))
                elif isinstance(value, (int, float)):
                    num = float(value)
                else:
                    return str(value)

                if num >= 10000:
                    return f"{num:,.0f}"
                elif num >= 1000:
                    return f"{num:,.1f}"
                else:
                    return f"{num:.0f}" if num == int(num) else f"{num:.1f}"
            except:
                return str(value)

        stats_info = [
            ("总播放", "📺", format_number(account_data.get("总播放", "0")), "#e74c3c"),
            ("昨日播放", "📈", format_number(account_data.get("昨日播放", "0")), "#3498db"),
            ("总粉丝", "👥", format_number(account_data.get("总粉丝", "0")), "#9b59b6"),
            ("昨日粉丝", "📊", format_number(account_data.get("昨日粉丝", "0")), "#1abc9c"),
        ]

        for label, icon, value, color in stats_info:
            row_frame = tk.Frame(content_frame, bg="#f8f9fa", relief=tk.FLAT, bd=1)
            row_frame.pack(fill=tk.X, pady=3, padx=5)

            inner_frame = tk.Frame(row_frame, bg="#f8f9fa")
            inner_frame.pack(fill=tk.X, padx=10, pady=8)

            # 左侧图标和标签
            left_frame = tk.Frame(inner_frame, bg="#f8f9fa")
            left_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

            icon_label = tk.Label(left_frame, text=f"{icon} {label}",
                                font=("Microsoft YaHei", 10, "bold"),
                                bg="#f8f9fa", fg=color)
            icon_label.pack(side=tk.LEFT, anchor=tk.W)

            # 右侧值
            value_label = tk.Label(inner_frame, text=str(value),
                                 font=("Microsoft YaHei", 10),
                                 bg="#f8f9fa", fg="#212529")
            value_label.pack(side=tk.RIGHT, anchor=tk.E)



    def copy_text_to_clipboard(self, text):
        """复制文本到剪贴板"""
        self.parent.clipboard_clear()
        self.parent.clipboard_append(text)
        self.log(f"已复制: {text}")

    def export_account_details(self, account, account_data):
        """导出账号详情"""
        try:
            import json
            from tkinter import filedialog

            # 选择保存位置
            filename = filedialog.asksaveasfilename(
                title="导出账号详情",
                defaultextension=".json",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")],
                initialvalue=f"{account}_详情.json"
            )

            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(account_data, f, ensure_ascii=False, indent=2)
                self.log(f"账号详情已导出到: {filename}")
                messagebox.showinfo("导出成功", f"账号详情已导出到:\n{filename}")
        except Exception as e:
            self.log(f"❌ 导出账号详情失败: {str(e)}")
            messagebox.showerror("导出失败", f"导出账号详情失败:\n{str(e)}")

    def refresh_account_details(self, window, account):
        """刷新账号详情"""
        # 先刷新账号数据
        if self.account_data:
            self.account_data.load_data()
        # 确保解绑滚轮事件后关闭窗口
        try:
            # 尝试解绑可能存在的全局滚轮事件
            window.unbind_all("<MouseWheel>")
        except:
            pass
        # 关闭当前窗口
        window.destroy()
        # 重新打开详情窗口
        self._show_account_details_window(account)

    def _ask_for_data_query_after_login(self, account, driver):
        """询问是否在登录后获取数据（头条）"""
        if messagebox.askyesno("获取数据", f"头条账号 {account} 登录成功！\n\n是否立即获取账号数据？\n\n注意：获取数据需要访问多个页面，大约需要1-2分钟。"):
            self.log(f"开始获取头条账号 {account} 的数据...")
            # 这里可以调用数据查询功能
            messagebox.showinfo("提示", "请使用主界面的数据查询功能获取详细数据")
        else:
            self.log(f"跳过数据获取，浏览器将保持打开状态")
            messagebox.showinfo("登录成功", f"头条账号 {account} 登录成功\n\n浏览器将保持打开状态，您可以随时手动关闭。")

    def _ask_for_netease_data_query_after_login(self, account, driver):
        """询问是否在登录后获取数据（网易）"""
        if messagebox.askyesno("获取数据", f"网易号账号 {account} 登录成功！\n\n是否立即获取账号数据？\n\n注意：获取数据需要访问多个页面，大约需要1-2分钟。"):
            self.log(f"开始获取网易号账号 {account} 的数据...")
            # 这里可以调用数据查询功能
            messagebox.showinfo("提示", "请使用主界面的数据查询功能获取详细数据")
        else:
            self.log(f"跳过数据获取，浏览器将保持打开状态")
            messagebox.showinfo("登录成功", f"网易号账号 {account} 登录成功\n\n浏览器将保持打开状态，您可以随时手动关闭。")

    def _ask_for_dayu_data_query_after_login(self, account, driver):
        """询问是否在登录后获取数据（大鱼号）"""
        if messagebox.askyesno("获取数据", f"大鱼号账号 {account} 登录成功！\n\n是否立即获取账号数据？\n\n注意：获取数据需要访问多个页面，大约需要1-2分钟。"):
            self.log(f"开始获取大鱼号账号 {account} 的数据...")
            # 这里可以调用数据查询功能
            messagebox.showinfo("提示", "请使用主界面的数据查询功能获取详细数据")
        else:
            self.log(f"跳过数据获取，浏览器将保持打开状态")
            messagebox.showinfo("登录成功", f"大鱼号账号 {account} 登录成功\n\n浏览器将保持打开状态，您可以随时手动关闭。")

    def _center_window(self, window):
        """居中显示窗口"""
        window.update_idletasks()
        width = window.winfo_width()
        height = window.winfo_height()
        x = (window.winfo_screenwidth() // 2) - (width // 2)
        y = (window.winfo_screenheight() // 2) - (height // 2)
        window.geometry(f"{width}x{height}+{x}+{y}")

    def _ask_for_toutiao_phone_login(self, account, toutiao_login, cookie_path):
        """询问是否尝试头条号手机号登录"""
        from tkinter import messagebox

        if not messagebox.askyesno("登录失败", f"头条账号 {account} Cookie登录失败，可能Cookie已过期。\n\n是否尝试使用手机号登录？"):
            messagebox.showerror("登录失败", f"头条账号 {account} 登录失败")
            return

        self.log(f"正在尝试使用手机号登录头条账号: {account}")

        # 显示提示对话框
        messagebox.showinfo("操作提示", "请在打开的浏览器中完成头条账号登录操作。\n\n登录地址：https://mp.toutiao.com/auth/page/login\n\n浏览器将保持打开状态，您可以随时手动关闭。")

        # 在后台线程中执行手机号登录
        import threading
        threading.Thread(target=self._toutiao_phone_login_thread, args=(account, toutiao_login, cookie_path), daemon=True).start()

    def _toutiao_phone_login_thread(self, account, toutiao_login, cookie_path):
        """在后台线程中执行头条号手机号登录"""
        try:
            # 使用手机号登录
            login_success, driver, cookies = toutiao_login.login_with_phone(False)

            if login_success and cookies:
                self.log(f"✅ 头条账号 {account} 手机号登录成功")

                # 在主线程中询问是否更新Cookie
                self.parent.after(0, lambda: self._ask_for_toutiao_cookie_update(account, cookies, cookie_path, driver))
            else:
                self.log(f"❌ 头条账号 {account} 手机号登录失败")
                self.parent.after(0, lambda: messagebox.showerror("登录失败", f"头条账号 {account} 手机号登录失败"))

                # 如果浏览器仍然活动，提示用户可以手动关闭
                if driver:
                    self.log(f"浏览器仍然处于打开状态，您可以随时手动关闭")
                    self.parent.after(0, lambda: messagebox.showinfo("提示", "浏览器仍然处于打开状态，您可以随时手动关闭。"))

        except Exception as e:
            self.log(f"❌ 头条账号 {account} 手机号登录过程中发生错误: {str(e)}")
            self.parent.after(0, lambda: messagebox.showerror("错误", f"头条账号 {account} 手机号登录过程中发生错误: {str(e)}"))
            import traceback
            traceback.print_exc()

    def _ask_for_toutiao_cookie_update(self, account, cookies, cookie_path, driver):
        """询问是否更新头条号Cookie"""
        from tkinter import messagebox

        if messagebox.askyesno("更新Cookie", f"头条账号 {account} 手机号登录成功！\n\n是否将新的Cookie保存到文件中？\n\n这将覆盖现有的Cookie文件。"):
            try:
                # 保存Cookie到文件
                import json
                with open(cookie_path, 'w', encoding='utf-8') as f:
                    json.dump(cookies, f, ensure_ascii=False, indent=2)

                self.log(f"✅ 头条账号 {account} Cookie已更新保存")
                messagebox.showinfo("保存成功", f"头条账号 {account} 的Cookie已成功保存到文件中。")

            except Exception as e:
                self.log(f"❌ 保存Cookie失败: {str(e)}")
                messagebox.showerror("保存失败", f"保存Cookie时发生错误: {str(e)}")
        else:
            self.log(f"跳过Cookie保存")

        # 询问是否立即获取账号数据
        if messagebox.askyesno("获取数据", f"是否立即获取头条账号 {account} 的数据？"):
            self.log(f"开始获取头条账号 {account} 的数据...")
            # 这里可以调用数据查询功能
            messagebox.showinfo("提示", "请使用主界面的数据查询功能获取详细数据")
        else:
            self.log(f"跳过数据获取，浏览器将保持打开状态")
            messagebox.showinfo("登录成功", f"头条账号 {account} 登录成功\n\n浏览器将保持打开状态，您可以随时手动关闭。")

        # 关闭浏览器
        if driver:
            try:
                driver.quit()
            except:
                pass

    def _ask_for_dayu_phone_login(self, account, dayu_login, cookie_path):
        """询问是否尝试大鱼号手机号登录"""
        from tkinter import messagebox

        if not messagebox.askyesno("登录失败", f"大鱼号账号 {account} Cookie登录失败，可能Cookie已过期。\n\n是否尝试使用手机号登录？"):
            messagebox.showerror("登录失败", f"大鱼号账号 {account} 登录失败")
            return

        self.log(f"正在尝试使用手机号登录大鱼号账号: {account}")

        # 显示提示对话框
        messagebox.showinfo("操作提示", "请在打开的浏览器中完成大鱼号账号登录操作。\n\n登录地址：https://mp.dayu.com/\n\n浏览器将保持打开状态，您可以随时手动关闭。")

        # 在后台线程中执行手机号登录
        import threading
        threading.Thread(target=self._dayu_phone_login_thread, args=(account, dayu_login, cookie_path), daemon=True).start()

    def _dayu_phone_login_thread(self, account, dayu_login, cookie_path):
        """在后台线程中执行大鱼号手机号登录"""
        try:
            # 使用手机号登录
            login_success, driver, cookies = dayu_login.login_with_phone(False)

            if login_success and cookies:
                self.log(f"✅ 大鱼号账号 {account} 手机号登录成功")

                # 在主线程中询问是否更新Cookie
                self.parent.after(0, lambda: self._ask_for_dayu_cookie_update(account, cookies, cookie_path, driver))
            else:
                self.log(f"❌ 大鱼号账号 {account} 手机号登录失败")
                self.parent.after(0, lambda: messagebox.showerror("登录失败", f"大鱼号账号 {account} 手机号登录失败"))

                # 如果浏览器仍然活动，提示用户可以手动关闭
                if driver:
                    self.log(f"浏览器仍然处于打开状态，您可以随时手动关闭")
                    self.parent.after(0, lambda: messagebox.showinfo("提示", "浏览器仍然处于打开状态，您可以随时手动关闭。"))

        except Exception as e:
            self.log(f"❌ 大鱼号账号 {account} 手机号登录过程中发生错误: {str(e)}")
            self.parent.after(0, lambda: messagebox.showerror("错误", f"大鱼号账号 {account} 手机号登录过程中发生错误: {str(e)}"))
            import traceback
            traceback.print_exc()

    def _ask_for_dayu_cookie_update(self, account, cookies, cookie_path, driver):
        """询问是否更新大鱼号Cookie"""
        from tkinter import messagebox

        if messagebox.askyesno("更新Cookie", f"大鱼号账号 {account} 手机号登录成功！\n\n是否将新的Cookie保存到文件中？\n\n这将覆盖现有的Cookie文件。"):
            try:
                # 保存Cookie到文件
                import json
                with open(cookie_path, 'w', encoding='utf-8') as f:
                    json.dump(cookies, f, ensure_ascii=False, indent=2)

                self.log(f"✅ 大鱼号账号 {account} Cookie已更新保存")
                messagebox.showinfo("保存成功", f"大鱼号账号 {account} 的Cookie已成功保存到文件中。")

            except Exception as e:
                self.log(f"❌ 保存Cookie失败: {str(e)}")
                messagebox.showerror("保存失败", f"保存Cookie时发生错误: {str(e)}")
        else:
            self.log(f"跳过Cookie保存")

        # 询问是否立即获取账号数据
        if messagebox.askyesno("获取数据", f"是否立即获取大鱼号账号 {account} 的数据？"):
            self.log(f"开始获取大鱼号账号 {account} 的数据...")
            # 这里可以调用数据查询功能
            messagebox.showinfo("提示", "请使用主界面的数据查询功能获取详细数据")
        else:
            self.log(f"跳过数据获取，浏览器将保持打开状态")
            messagebox.showinfo("登录成功", f"大鱼号账号 {account} 登录成功\n\n浏览器将保持打开状态，您可以随时手动关闭。")

        # 关闭浏览器
        if driver:
            try:
                driver.quit()
            except:
                pass
