[2025-07-20 12:49:37] [INFO] 🚀 启动多平台存稿工具...
[2025-07-20 12:49:38] [ERROR] 程序发生未捕获的异常: unexpected indent (settings_manager.py, line 242)
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\main.py", line 472, in main
    return create_and_run_main_app()
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\main.py", line 376, in create_and_run_main_app
    app = NeteaseDraftUI(root, config_manager)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\ui\main.py", line 179, in __init__
    self._init_managers()
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\ui\main.py", line 254, in _init_managers
    from 网易号存稿.ui.managers.settings_manager import SettingsManager
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\ui\managers\settings_manager.py", line 242
    error_label = ttk.Label(timer_frame, text=f"❌ 定时任务功能不可用: {e}",
IndentationError: unexpected indent

