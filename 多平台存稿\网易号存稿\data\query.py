"""
网易号数据查询模块
"""

import os
import time
import json
import datetime
import traceback
import threading
from queue import Queue
from typing import Dict, Any, Optional, List, Callable
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

from 网易号存稿.account.login import AccountLogin
from 网易号存稿.browser.driver import DriverManager
from 网易号存稿.common.constants import AccountStatus


class DataQuery:
    """网易号数据查询类"""

    def __init__(self, config_manager=None, account_manager=None, log_callback=None):
        """
        初始化数据查询器

        Args:
            config_manager: 配置管理器
            account_manager: 账号管理器
            log_callback: 日志回调函数
        """
        self.config_manager = config_manager
        self.account_manager = account_manager
        self.log_callback = log_callback
        
    def query_account_data(self, account: str) -> Dict[str, Any]:
        """
        查询单个账号的数据
        
        Args:
            account: 账号名称
            
        Returns:
            账号数据字典
        """
        result = {
            "账号": account,
            "平台": "网易号",
            "用户名": "",
            "累计收益": "0.00",  # 使用与头条号一致的原始字段名
            "昨日收益": "0.00",
            "七日收益": [],      # 使用与头条号一致的列表格式
            "总播放": "0",
            "昨日播放": "0",
            "总粉丝": "0",
            "昨日粉丝": "0",
            "草稿箱数量": "0",   # 使用与头条号一致的原始字段名
            "总提现": "0.00",
            "可提现": "0.00",    # 使用与头条号一致的原始字段名
            "查询时间": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "状态": AccountStatus.QUERY_FAILED,
            "错误信息": ""
        }
        
        driver_manager = None
        try:
            # 创建驱动管理器
            driver_manager = DriverManager(lambda msg: print(f"[DataQuery] {msg}"))
            
            # 创建账号登录对象
            account_login = AccountLogin(driver_manager, lambda msg: print(f"[DataQuery] {msg}"))
            
            # 获取账号Cookie文件路径
            if self.account_manager:
                account_dir = self.account_manager.account_dir
            else:
                account_dir = "accounts"
                
            cookie_path = os.path.join(account_dir, f"{account}.txt")
            
            if not os.path.exists(cookie_path):
                result["错误信息"] = f"Cookie文件不存在: {cookie_path}"
                result["状态"] = AccountStatus.COOKIE_NOT_FOUND
                return result
            
            # 使用Cookie登录
            success, driver = account_login.login_with_cookies(cookie_path, headless=True, account=account)
            
            if not success or not driver:
                result["错误信息"] = "登录失败"
                result["状态"] = AccountStatus.LOGIN_FAILED
                return result
            
            # 查询数据
            data = self._extract_account_data(driver, account)
            result.update(data)
            result["状态"] = AccountStatus.SUCCESS
            
        except Exception as e:
            result["错误信息"] = str(e)
            result["状态"] = AccountStatus.QUERY_EXCEPTION
            traceback.print_exc()
            
        finally:
            # 关闭浏览器
            if driver_manager and driver_manager.is_driver_alive():
                driver_manager.close_driver()
                
        return result
    
    def _extract_account_data(self, driver: webdriver.Chrome, account: str) -> Dict[str, Any]:
        """
        从页面提取账号数据 - 基于备份文件的完整实现

        Args:
            driver: 浏览器驱动
            account: 账号名称

        Returns:
            提取的数据字典
        """
        data = {}

        try:
            # 1. 获取首页数据
            self.log("正在获取首页数据...")
            driver.get("https://mp.163.com/subscribe_v4/index.html#/home")
            time.sleep(3)  # 等待页面加载

            # 获取用户名 - 使用网易号专用的XPath选择器
            try:
                username_element = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, '//*[@id="root"]/div/div[2]/div[2]/div[2]/div[2]/div[1]/div[1]/div[2]/div/div'))
                )
                data["用户名"] = username_element.text.strip()
                self.log(f"用户名: {data['用户名']}")
            except:
                # 备用选择器
                try:
                    username_element = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, "//span[@class='username']"))
                    )
                    data["用户名"] = username_element.text.strip()
                    self.log(f"用户名(备用): {data['用户名']}")
                except:
                    data["用户名"] = account  # 使用账号名作为备用
                    self.log(f"用户名(默认): {data['用户名']}")

            # 获取总粉丝数 - 网易号专用XPath
            try:
                total_fans_element = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, '//*[@id="root"]/div/div[2]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/span'))
                )
                data["总粉丝"] = total_fans_element.text.strip()
                self.log(f"总粉丝: {data['总粉丝']}")
            except:
                # 备用选择器
                try:
                    total_fans_element = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'fans-num')]"))
                    )
                    data["总粉丝"] = total_fans_element.text.strip()
                    self.log(f"总粉丝(备用): {data['总粉丝']}")
                except:
                    data["总粉丝"] = "0"  # 默认值
                    self.log("总粉丝(默认): 0")

            # 获取总播放量 - 网易号专用XPath
            try:
                total_play_element = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, '//*[@id="root"]/div/div[2]/div[2]/div[2]/div[1]/div[1]/div[1]/div[2]/div[1]/span'))
                )
                data["总播放"] = total_play_element.text.strip()
                self.log(f"总播放: {data['总播放']}")
            except:
                # 备用选择器
                try:
                    total_play_element = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'play-num')]"))
                    )
                    data["总播放"] = total_play_element.text.strip()
                    self.log(f"总播放(备用): {data['总播放']}")
                except:
                    data["总播放"] = "0"  # 默认值
                    self.log("总播放(默认): 0")

            # 2. 获取总收益数据 - 使用网易号专用的收益页面
            self.log("正在获取总收益数据...")
            try:
                # 访问网易号收益页面
                driver.get("https://mp.163.com/subscribe_v4/index.html#/profit-data")
                time.sleep(3)  # 等待页面加载

                # 点击总收益标签
                try:
                    total_income_tab = WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable((By.XPATH, '//*[@id="root"]/div/div[2]/div[2]/div[1]/div/div/div[3]/div'))
                    )
                    driver.execute_script("arguments[0].click();", total_income_tab)
                    self.log("已点击总收益标签")
                    time.sleep(2)  # 等待数据加载
                except Exception as e:
                    self.log(f"点击总收益标签失败: {str(e)}，尝试直接获取数据")

                # 获取累计收益数据
                total_income_element = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, '//*[@id="root"]/div/div[2]/div[2]/div[3]/div[1]/div[1]/div[2]'))
                )
                data["累计收益"] = total_income_element.text.strip()  # 使用与头条号一致的字段名
                self.log(f"累计收益: {data['累计收益']}")
            except Exception as e:
                self.log(f"获取累计收益失败: {str(e)}")
                # 备用选择器
                try:
                    total_income_element = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'income-num')]"))
                    )
                    data["累计收益"] = total_income_element.text.strip()
                    self.log(f"累计收益(备用): {data['累计收益']}")
                except:
                    data["累计收益"] = "0.00"  # 默认值
                    self.log("累计收益(默认): 0.00")

            # 3. 获取草稿箱数据
            self.log("正在获取草稿箱数据...")
            draft_data = self._get_draft_data(driver)
            data.update(draft_data)

            # 4. 获取内容数据（昨日播放）
            self.log("正在获取内容数据...")
            content_data = self._get_content_data(driver)
            data.update(content_data)

            # 5. 获取订阅数据（昨日粉丝）
            self.log("正在获取订阅数据...")
            subscribe_data = self._get_subscribe_data(driver)
            data.update(subscribe_data)

            # 6. 获取昨日收益数据
            self.log("正在获取昨日收益数据...")
            yesterday_income_data = self._get_yesterday_income(driver)
            data.update(yesterday_income_data)

            # 7. 获取提现数据
            self.log("正在获取提现数据...")
            withdraw_data = self._get_withdraw_data(driver)
            data.update(withdraw_data)

            # 8. 获取七日收益数据
            self.log("正在获取七日收益数据...")
            seven_day_data = self._get_seven_day_income(driver)
            data["七日收益"] = seven_day_data  # 使用与头条号一致的字段名和格式

        except Exception as e:
            self.log(f"提取数据时发生错误: {e}")

        # 统一字段映射标准：草稿箱、待提现、七日收益、查询时间、总收益（与头条号保持一致）
        data["总收益"] = data.get("累计收益", "0.00")    # 统一标准字段
        data["草稿箱"] = data.get("草稿箱数量", "0")      # 统一标准字段
        data["待提现"] = data.get("可提现", "0.00")       # 统一标准字段

        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        data["查询时间"] = current_time                  # 统一标准字段
        data["更新时间"] = current_time                  # 保持兼容

        # 设置默认值确保字段完整性
        if "昨日收益" not in data:
            data["昨日收益"] = "0.00"
        if "昨日播放" not in data:
            data["昨日播放"] = "0"
        if "昨日粉丝" not in data:
            data["昨日粉丝"] = "0"
        if "总提现" not in data:
            data["总提现"] = "0.00"

        return data

    def _get_draft_data(self, driver: webdriver.Chrome) -> Dict[str, Any]:
        """
        获取草稿箱数据 - 网易号专用实现

        Args:
            driver: 浏览器驱动

        Returns:
            草稿箱数据字典
        """
        data = {}

        try:
            # 导航到草稿箱页面
            driver.get("https://mp.163.com/subscribe_v4/index.html#/draft-manage")
            time.sleep(3)  # 等待页面加载

            # 获取草稿箱数量 - 使用源文件中的正确XPath
            try:
                draft_count_element = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, '//*[@id="root"]/div/div[2]/div[2]/div[2]/div[1]/div'))
                )
                draft_count_text = draft_count_element.text.strip()

                # 解析草稿箱数量
                import re
                draft_count_match = re.search(r'\d+', draft_count_text)
                if draft_count_match:
                    data["草稿箱数量"] = draft_count_match.group(0)  # 使用与头条号一致的字段名
                    self.log(f"获取到草稿箱数量: {data['草稿箱数量']}")
                else:
                    data["草稿箱数量"] = "0"
                    self.log("未能从文本中提取草稿箱数量，使用默认值: 0")
            except Exception as e:
                self.log(f"获取草稿箱数量失败: {str(e)}")
                data["草稿箱数量"] = "0"  # 默认值
                self.log("无法获取草稿箱数量，使用默认值: 0")

        except Exception as e:
            self.log(f"获取草稿箱数据时发生错误: {str(e)}")
            data["草稿箱数量"] = "0"

        return data

    def _get_content_data(self, driver: webdriver.Chrome) -> Dict[str, Any]:
        """
        获取内容数据（昨日播放） - 网易号专用实现

        Args:
            driver: 浏览器驱动

        Returns:
            内容数据字典
        """
        data = {}

        try:
            # 导航到内容数据页面
            driver.get("https://mp.163.com/subscribe_v4/index.html#/content-data")
            time.sleep(3)  # 等待页面加载

            # 先点击昨日播放标签 - 使用更健壮的选择器
            try:
                yesterday_play_tab = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, '//*[@id="root"]/div/div[2]/div[2]/div[1]/div/div/div[2]/div'))
                )
                driver.execute_script("arguments[0].click();", yesterday_play_tab)
                self.log("已点击昨日播放标签")
                time.sleep(2)  # 等待数据加载
            except:
                # 备用选择器
                try:
                    yesterday_play_tab = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, "//div[contains(text(), '昨日播放')]"))
                    )
                    driver.execute_script("arguments[0].click();", yesterday_play_tab)
                    self.log("已点击昨日播放标签(备用)")
                    time.sleep(2)
                except:
                    self.log("未找到昨日播放标签，尝试直接获取数据")

            # 获取昨日播放数据 - 使用更健壮的选择器
            try:
                yesterday_play_element = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, '//*[@id="root"]/div/div[2]/div[2]/div[3]/div[1]/div[2]/div[2]'))
                )
                data["昨日播放"] = yesterday_play_element.text.strip()
                self.log(f"昨日播放: {data['昨日播放']}")
            except:
                # 备用选择器
                try:
                    yesterday_play_element = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'yesterday-play-num')]"))
                    )
                    data["昨日播放"] = yesterday_play_element.text.strip()
                    self.log(f"昨日播放(备用): {data['昨日播放']}")
                except:
                    data["昨日播放"] = "0"  # 默认值
                    self.log("昨日播放(默认): 0")

        except Exception as e:
            self.log(f"获取内容数据失败: {str(e)}")
            data["昨日播放"] = "0"

        return data

    def _get_subscribe_data(self, driver: webdriver.Chrome) -> Dict[str, Any]:
        """
        获取订阅数据（昨日粉丝） - 网易号专用实现

        Args:
            driver: 浏览器驱动

        Returns:
            订阅数据字典
        """
        data = {}

        try:
            # 导航到订阅数据页面
            driver.get("https://mp.163.com/subscribe_v4/index.html#/subscribe-data")
            time.sleep(3)  # 等待页面加载

            # 获取昨日粉丝 - 使用更健壮的选择器
            try:
                yesterday_fans_element = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, '//*[@id="root"]/div/div[2]/div[2]/div[1]/div[2]/div[2]'))
                )
                data["昨日粉丝"] = yesterday_fans_element.text.strip()
                self.log(f"昨日粉丝: {data['昨日粉丝']}")
            except:
                # 备用选择器
                try:
                    yesterday_fans_element = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'yesterday-fans-num')]"))
                    )
                    data["昨日粉丝"] = yesterday_fans_element.text.strip()
                    self.log(f"昨日粉丝(备用): {data['昨日粉丝']}")
                except:
                    data["昨日粉丝"] = "0"  # 默认值
                    self.log("昨日粉丝(默认): 0")

        except Exception as e:
            self.log(f"获取订阅数据失败: {str(e)}")
            data["昨日粉丝"] = "0"

        return data

    def _get_yesterday_income(self, driver: webdriver.Chrome) -> Dict[str, Any]:
        """
        获取昨日收益数据 - 网易号专用实现

        Args:
            driver: 浏览器驱动

        Returns:
            昨日收益数据字典
        """
        data = {}

        try:
            # 导航到收益数据页面
            driver.get("https://mp.163.com/subscribe_v4/index.html#/profit-data")
            time.sleep(3)  # 等待页面加载

            # 先点击昨日收益标签 - 使用更健壮的选择器
            try:
                yesterday_income_tab = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, '//*[@id="root"]/div/div[2]/div[2]/div[1]/div/div/div[3]/div'))
                )
                driver.execute_script("arguments[0].click();", yesterday_income_tab)
                self.log("已点击昨日收益标签")
                time.sleep(2)  # 等待数据加载
            except:
                # 备用选择器
                try:
                    yesterday_income_tab = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, "//div[contains(text(), '昨日收益')]"))
                    )
                    driver.execute_script("arguments[0].click();", yesterday_income_tab)
                    self.log("已点击昨日收益标签(备用)")
                    time.sleep(2)
                except:
                    self.log("未找到昨日收益标签，尝试直接获取数据")

            # 获取昨日收益数据 - 使用更健壮的选择器
            try:
                yesterday_income_element = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, '//*[@id="root"]/div/div[2]/div[2]/div[3]/div[1]/div[2]/div[2]'))
                )
                yesterday_income = yesterday_income_element.text.strip()
                data["昨日收益"] = yesterday_income
                self.log(f"昨日收益: {data['昨日收益']}")
            except:
                # 备用选择器
                try:
                    yesterday_income_element = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'yesterday-income-num')]"))
                    )
                    data["昨日收益"] = yesterday_income_element.text.strip()
                    self.log(f"昨日收益(备用): {data['昨日收益']}")
                except:
                    data["昨日收益"] = "0.00"  # 默认值
                    self.log("昨日收益(默认): 0.00")

        except Exception as e:
            self.log(f"获取昨日收益数据失败: {str(e)}")
            data["昨日收益"] = "0.00"

        return data

    def _get_withdraw_data(self, driver: webdriver.Chrome) -> Dict[str, Any]:
        """
        获取提现数据 - 网易号专用实现

        Args:
            driver: 浏览器驱动

        Returns:
            提现数据字典
        """
        data = {}

        try:
            # 导航到提现页面
            driver.get("https://mp.163.com/subscribe_v4/index.html#/profit")
            time.sleep(3)  # 等待页面加载

            # 获取总提现数据
            try:
                total_withdraw_element = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, '//*[@id="root"]/div/div[2]/div[2]/div[3]/div[2]/div[1]/div[2]'))
                )
                data["总提现"] = total_withdraw_element.text.strip()
                self.log(f"总提现: {data['总提现']}")
            except Exception as e:
                self.log(f"获取总提现失败: {str(e)}")
                data["总提现"] = "0"

            # 获取可提现数据
            try:
                pending_withdraw_element = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, '//*[@id="root"]/div/div[2]/div[2]/div[3]/div[2]/div[3]/div[2]'))
                )
                data["可提现"] = pending_withdraw_element.text.strip()  # 使用与头条号一致的字段名
                self.log(f"可提现: {data['可提现']}")
            except Exception as e:
                self.log(f"获取可提现失败: {str(e)}")
                data["可提现"] = "0"

        except Exception as e:
            self.log(f"获取提现数据失败: {str(e)}")
            if "总提现" not in data:
                data["总提现"] = "0"
            if "可提现" not in data:
                data["可提现"] = "0"

        return data

    def _get_seven_day_income(self, driver: webdriver.Chrome) -> List[Dict[str, Any]]:
        """
        获取七日收益数据 - 基于源文件的完整实现，返回与头条号一致的格式

        Args:
            driver: 浏览器驱动

        Returns:
            七日收益数据列表，格式与头条号一致：[{"日期": "xx", "收益": xx, "收益文本": "xx"}]
        """
        try:
            self.log("开始获取七日收益数据...")

            # 1. 首先打开网易号后台首页
            driver.get("https://mp.163.com/subscribe_v4/index.html#/home")
            time.sleep(3)

            # 2. 进入收益数据页面
            driver.get("https://mp.163.com/subscribe_v4/index.html#/profit-data")
            time.sleep(3)

            # 3. 点击视频收益标签
            try:
                video_income_tab = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, '//*[@id="root"]/div/div[2]/div[2]/div[1]/div/div/div[3]/div'))
                )
                driver.execute_script("arguments[0].click();", video_income_tab)
                self.log("已点击视频收益标签")
                time.sleep(2)
            except Exception as e:
                self.log(f"点击视频收益标签失败: {str(e)}")
                return {}

            # 4. 点击数据明细
            try:
                data_detail_tab = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, '//*[@id="root"]/div/div[2]/div[2]/div[3]/div[2]/div[1]/div[1]/div[2]'))
                )
                driver.execute_script("arguments[0].click();", data_detail_tab)
                self.log("已点击数据明细")
                time.sleep(2)
            except Exception as e:
                self.log(f"点击数据明细失败: {str(e)}")
                return {}

            # 5. 获取七天的收益数据
            seven_day_income_data = []

            # 日期和收益的XPath路径 - 基于源文件的完整实现
            date_income_paths = [
                # 第一天
                ('//*[@id="root"]/div/div[2]/div[2]/div[3]/div[2]/div[2]/div/div/div[2]/div[7]/div[1]/div',
                 '//*[@id="root"]/div/div[2]/div[2]/div[3]/div[2]/div[2]/div/div/div[2]/div[7]/div[5]'),
                # 第二天
                ('//*[@id="root"]/div/div[2]/div[2]/div[3]/div[2]/div[2]/div/div/div[2]/div[6]/div[1]/div',
                 '//*[@id="root"]/div/div[2]/div[2]/div[3]/div[2]/div[2]/div/div/div[2]/div[6]/div[5]'),
                # 第三天
                ('//*[@id="root"]/div/div[2]/div[2]/div[3]/div[2]/div[2]/div/div/div[2]/div[5]/div[1]/div',
                 '//*[@id="root"]/div/div[2]/div[2]/div[3]/div[2]/div[2]/div/div/div[2]/div[5]/div[5]'),
                # 第四天
                ('//*[@id="root"]/div/div[2]/div[2]/div[3]/div[2]/div[2]/div/div/div[2]/div[4]/div[1]/div',
                 '//*[@id="root"]/div/div[2]/div[2]/div[3]/div[2]/div[2]/div/div/div[2]/div[4]/div[5]'),
                # 第五天
                ('//*[@id="root"]/div/div[2]/div[2]/div[3]/div[2]/div[2]/div/div/div[2]/div[3]/div[1]/div',
                 '//*[@id="root"]/div/div[2]/div[2]/div[3]/div[2]/div[2]/div/div/div[2]/div[3]/div[5]'),
                # 第六天
                ('//*[@id="root"]/div/div[2]/div[2]/div[3]/div[2]/div[2]/div/div/div[2]/div[2]/div[1]/div',
                 '//*[@id="root"]/div/div[2]/div[2]/div[3]/div[2]/div[2]/div/div/div[2]/div[2]/div[5]'),
                # 第七天
                ('//*[@id="root"]/div/div[2]/div[2]/div[3]/div[2]/div[2]/div/div/div[2]/div[1]/div[1]/div',
                 '//*[@id="root"]/div/div[2]/div[2]/div[3]/div[2]/div[2]/div/div/div[2]/div[1]/div[5]')
            ]

            # 获取每一天的日期和收益
            for i, (date_xpath, income_xpath) in enumerate(date_income_paths):
                try:
                    # 获取日期
                    date_element = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, date_xpath))
                    )
                    date_text = date_element.text.strip()

                    # 获取收益
                    income_element = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, income_xpath))
                    )
                    income_text = income_element.text.strip()

                    # 解析收益值
                    income_value = self.parse_number_with_unit(income_text)

                    if date_text:  # 只有当日期不为空时才添加
                        # 按照与头条号一致的格式构建数据
                        day_data = {
                            "日期": date_text,
                            "收益": income_value,
                            "收益文本": income_text
                        }
                        seven_day_income_data.append(day_data)
                        self.log(f"第{i+1}天收益: {date_text} - {income_text} -> {income_value}")
                    else:
                        self.log(f"第{i+1}行日期为空，跳过")

                except Exception as e:
                    self.log(f"获取第 {i+1} 天的收益数据失败: {str(e)}")
                    # 继续获取下一天的数据

            # 检查是否获取到了数据
            if not seven_day_income_data:
                self.log("❌ 未获取到任何七日收益数据")
                return []
            else:
                self.log(f"✅ 成功获取到 {len(seven_day_income_data)} 天的收益数据")
                return seven_day_income_data

        except Exception as e:
            self.log(f"获取七日收益数据时发生错误: {str(e)}")
            return []

    def safe_get_element_text(self, driver: webdriver.Chrome, xpath: str, timeout: int = 5) -> str:
        """
        安全获取元素文本内容

        Args:
            driver: 浏览器驱动
            xpath: 元素的XPath
            timeout: 超时时间

        Returns:
            元素文本内容，如果获取失败则返回空字符串
        """
        try:
            element = WebDriverWait(driver, timeout).until(
                EC.presence_of_element_located((By.XPATH, xpath))
            )
            return element.text.strip()
        except (TimeoutException, NoSuchElementException):
            return ""
        except Exception:
            return ""

    def parse_number_with_unit(self, text: str) -> float:
        """
        解析带单位的数字文本

        Args:
            text: 包含数字和单位的文本

        Returns:
            解析后的数字值
        """
        if not text:
            return 0.0

        try:
            import re
            # 移除所有非数字、小数点、万、千、亿的字符
            cleaned_text = re.sub(r'[^\d.\w万千亿]', '', text)

            # 提取数字部分
            number_match = re.search(r'(\d+\.?\d*)', cleaned_text)
            if not number_match:
                return 0.0

            number = float(number_match.group(1))

            # 处理单位
            if '亿' in cleaned_text:
                number *= 100000000
            elif '万' in cleaned_text:
                number *= 10000
            elif '千' in cleaned_text:
                number *= 1000

            return number

        except Exception:
            return 0.0

    def log(self, message: str) -> None:
        """
        记录日志

        Args:
            message: 日志消息
        """
        if self.log_callback:
            self.log_callback(f"[网易号数据查询] {message}")
        else:
            print(f"[网易号数据查询] {message}")

    def query_accounts_batch(self, accounts: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        批量查询账号数据
        
        Args:
            accounts: 账号列表
            
        Returns:
            账号数据字典，键为账号名，值为数据字典
        """
        results = {}
        
        for account in accounts:
            print(f"正在查询账号: {account}")
            result = self.query_account_data(account)
            results[account] = result
            
        return results


class NeteaseDataQueryManager:
    """网易号数据查询管理器 - 支持多线程并发查询"""

    def __init__(self, config_manager=None, account_manager=None, log_callback: Callable = None):
        """
        初始化查询管理器

        Args:
            config_manager: 配置管理器
            account_manager: 账号管理器
            log_callback: 日志回调函数
        """
        self.config_manager = config_manager
        self.account_manager = account_manager
        self.log_callback = log_callback
        self.results = {}
        self.lock = threading.Lock()

    def log(self, message: str) -> None:
        """
        记录日志

        Args:
            message: 日志消息
        """
        if self.log_callback:
            self.log_callback(f"[网易号查询管理器] {message}")
        else:
            print(f"[网易号查询管理器] {message}")

    def query_single_account_thread(self, account: str, result_queue: Queue) -> None:
        """
        单个账号查询线程函数

        Args:
            account: 账号名称
            result_queue: 结果队列
        """
        try:
            # 为每个线程创建独立的查询对象
            query = DataQuery(
                config_manager=self.config_manager,
                account_manager=self.account_manager,
                log_callback=self.log_callback
            )

            # 查询账号数据
            result = query.query_account_data(account)

            # 将结果放入队列
            result_queue.put((account, result))

        except Exception as e:
            error_result = {
                "账号": account,
                "状态": "线程异常",
                "错误信息": str(e),
                "查询时间": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            result_queue.put((account, error_result))
            self.log(f"账号 {account} 查询线程异常: {str(e)}")

    def query_accounts_concurrent(self, accounts: List[str], max_workers: int = 3,
                                data_callback=None, progress_callback=None) -> Dict[str, Dict[str, Any]]:
        """
        并发查询多个账号的数据 - 优化版：真正的并发，无批次等待

        Args:
            accounts: 账号列表
            max_workers: 最大并发数
            data_callback: 数据回调函数
            progress_callback: 进度回调函数

        Returns:
            账号数据字典
        """
        if not accounts:
            return {}

        self.log(f"🚀 开始并发查询 {len(accounts)} 个账号，最大并发数: {max_workers}")

        from concurrent.futures import ThreadPoolExecutor, as_completed
        import threading

        # 用于线程安全的结果存储
        results_lock = threading.Lock()
        completed_count = 0

        def query_single_account_wrapper(account):
            """单账号查询包装器"""
            try:
                # 为每个线程创建独立的查询对象
                query = DataQuery(
                    config_manager=self.config_manager,
                    account_manager=self.account_manager,
                    log_callback=self.log_callback
                )

                # 查询账号数据
                result = query.query_account_data(account)
                return account, result
            except Exception as e:
                self.log(f"❌ 账号 {account} 查询异常: {str(e)}")
                error_result = {
                    "账号": account,
                    "状态": "失败",
                    "错误信息": str(e),
                    "查询时间": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                return account, error_result

        # 使用ThreadPoolExecutor实现真正的并发
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务到线程池
            future_to_account = {executor.submit(query_single_account_wrapper, account): account
                               for account in accounts}

            # 处理完成的任务，无需等待批次
            for future in as_completed(future_to_account):
                account = future_to_account[future]

                try:
                    account_name, result = future.result()

                    # 线程安全地更新结果
                    with results_lock:
                        self.results[account_name] = result
                        completed_count += 1

                    # 立即调用数据回调
                    if data_callback:
                        data_callback(account_name, result)

                    # 调用进度回调 - 更新侧边栏任务进度
                    if progress_callback:
                        progress_callback(completed_count, len(accounts), account_name)

                    self.log(f"✅ 账号 {account_name} 查询完成 ({completed_count}/{len(accounts)})")

                except Exception as e:
                    self.log(f"❌ 处理账号 {account} 结果时出错: {str(e)}")

                    # 记录错误结果
                    with results_lock:
                        completed_count += 1
                        self.results[account] = {
                            "账号": account,
                            "状态": "失败",
                            "错误信息": f"结果处理错误: {str(e)}",
                            "查询时间": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        }

        self.log(f"🎉 并发查询完成，共处理 {len(self.results)} 个账号")
        return self.results
