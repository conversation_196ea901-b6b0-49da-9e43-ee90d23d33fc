{"version": "2.0", "last_updated": "2025-07-20T11:43:31.790489", "tasks": [{"id": "ffa90fad-cc34-4205-b248-2996ac02608d", "name": "测试一次性任务", "description": "这是一个测试任务", "task_type": "one_time", "status": "completed", "execution_mode": 1, "target_accounts": "all", "scheduled_time": "2025-07-20T11:37:10.763407", "recurring_type": "daily", "next_execution": "2025-07-20T11:37:10.763407", "created_time": "2025-07-20T11:37:05.763407", "last_execution": "2025-07-20T11:37:10.771032", "execution_count": 1, "last_result": "执行成功", "enabled": false}, {"id": "2730ae86-dede-4216-8148-e37b894bf4c9", "name": "测试常驻任务", "description": "每日重复的测试任务", "task_type": "recurring", "status": "waiting", "execution_mode": 1, "target_accounts": "all", "scheduled_time": "2025-07-20T23:59:59.763407", "recurring_type": "daily", "next_execution": "2025-07-20T23:59:59", "created_time": "2025-07-20T11:37:05.763407", "last_execution": null, "execution_count": 0, "last_result": "", "enabled": true}]}