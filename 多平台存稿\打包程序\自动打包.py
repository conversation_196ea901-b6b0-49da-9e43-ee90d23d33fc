"""
自动打包脚本 - 无需GUI界面，直接执行打包
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import time

def log_message(message, level="INFO"):
    """打印日志消息"""
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] {level}: {message}")

def check_dependencies():
    """检查打包依赖"""
    log_message("检查打包依赖...")
    
    # 检查PyInstaller
    try:
        import PyInstaller
        log_message(f"PyInstaller 版本: {PyInstaller.__version__}")
    except ImportError:
        log_message("PyInstaller 未安装，正在安装...", "WARNING")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            log_message("PyInstaller 安装成功")
        except subprocess.CalledProcessError as e:
            log_message(f"PyInstaller 安装失败: {e}", "ERROR")
            return False
    
    # 检查源文件
    source_dir = Path(r"C:\Users\<USER>\Downloads\网易\多平台存稿")
    if not source_dir.exists():
        log_message(f"源目录不存在: {source_dir}", "ERROR")
        return False
        
    run_app_path = source_dir / "run_app.py"
    if not run_app_path.exists():
        log_message(f"主程序文件不存在: {run_app_path}", "ERROR")
        return False
        
    log_message("依赖检查完成")
    return True

def clean_and_create_directories():
    """清理旧文件并创建目录"""
    log_message("清理旧文件并创建目录...")

    target_dir = Path(r"D:\多平台存稿")

    # 第一步：清理旧的程序文件（如果存在）
    if target_dir.exists():
        log_message("清理现有目录...")
        try:
            shutil.rmtree(target_dir)
            log_message("旧目录已清理")
        except Exception as e:
            log_message(f"清理目录时出错: {e}", "WARNING")

    # 第二步：创建新的目录结构
    target_dir.mkdir(parents=True, exist_ok=True)
    log_message(f"创建程序目录: {target_dir}")

    # 直接在目标目录中打包，不需要临时目录
    return target_dir, target_dir

def generate_spec_file(package_dir):
    """生成PyInstaller spec文件"""
    log_message("生成spec文件...")

    source_dir = Path(r"C:\Users\<USER>\Downloads\网易\多平台存稿")
    hooks_dir = Path(__file__).parent.absolute()  # 获取当前脚本所在目录
    
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

import os
import sys
from pathlib import Path

# 设置基础路径
base_path = Path(r"{source_dir}")
app_path = base_path / "run_app.py"

# 数据文件和目录
datas = []

# 添加配置文件
config_files = ["config.json", "directory_config.json", "timer_config.json"]
for config_file in config_files:
    config_path = base_path / config_file
    if config_path.exists():
        datas.append((str(config_path), "."))

# 添加config目录
config_dir = base_path / "config"
if config_dir.exists():
    datas.append((str(config_dir), "config"))

# 添加网易号存稿目录
netease_dir = base_path / "网易号存稿"
if netease_dir.exists():
    datas.append((str(netease_dir), "网易号存稿"))

# 添加视频处理工具目录
video_dir = base_path / "视频处理工具"
if video_dir.exists():
    datas.append((str(video_dir), "视频处理工具"))

# 添加日志目录
logs_dir = base_path / "logs"
if logs_dir.exists():
    datas.append((str(logs_dir), "logs"))

# 添加Python包的数据文件以解决依赖问题
import site
try:
    site_packages = site.getsitepackages()
    if site_packages:
        site_pkg_path = Path(site_packages[0])

        # 添加jaraco相关包 - 使用collect_all方式
        try:
            from PyInstaller.utils.hooks import collect_all
            jaraco_datas, jaraco_binaries, jaraco_hiddenimports = collect_all('jaraco')
            datas.extend(jaraco_datas)
        except:
            # 回退到手动添加
            jaraco_path = site_pkg_path / "jaraco"
            if jaraco_path.exists():
                datas.append((str(jaraco_path), "jaraco"))

        # 添加pkg_resources相关
        try:
            pkg_datas, pkg_binaries, pkg_hiddenimports = collect_all('pkg_resources')
            datas.extend(pkg_datas)
        except:
            pkg_res_path = site_pkg_path / "pkg_resources"
            if pkg_res_path.exists():
                datas.append((str(pkg_res_path), "pkg_resources"))

        # 添加setuptools相关
        setuptools_path = site_pkg_path / "setuptools"
        if setuptools_path.exists():
            datas.append((str(setuptools_path), "setuptools"))

        # 添加packaging相关
        packaging_path = site_pkg_path / "packaging"
        if packaging_path.exists():
            datas.append((str(packaging_path), "packaging"))
except Exception:
    pass

# 完整的隐藏导入列表
hiddenimports = [
    # tkinter模块
    'tkinter', 'tkinter.ttk', 'tkinter.messagebox', 'tkinter.simpledialog',
    'tkinter.filedialog', 'tkinter.scrolledtext', 'tkinter.font', 'tkinter.constants',

    # selenium模块
    'selenium', 'selenium.webdriver', 'selenium.webdriver.chrome',
    'selenium.webdriver.chrome.service', 'selenium.webdriver.chrome.options',
    'selenium.webdriver.common.by', 'selenium.webdriver.support.ui',
    'selenium.webdriver.support.wait', 'selenium.webdriver.support.expected_conditions',
    'selenium.webdriver.common.keys', 'selenium.webdriver.common.action_chains',
    'selenium.common.exceptions',

    # PIL模块
    'PIL', 'PIL.Image', 'PIL.ImageTk', 'PIL.ImageDraw', 'PIL.ImageFont',

    # 网络和数据处理
    'requests', 'requests.adapters', 'requests.auth', 'requests.cookies',
    'json', 'urllib3', 'certifi',

    # 系统和线程
    'threading', 'queue', 'subprocess', 'multiprocessing', 'concurrent.futures',
    'datetime', 'time', 'pathlib', 'os', 'sys', 'shutil', 'tempfile',

    # 导入和模块
    'importlib', 'importlib.util', 'traceback', 'logging',

    # 应用特定
    'jieba', 'moviepy', 'moviepy.editor', 'pyautogui',

    # 平台和目录相关
    'platformdirs', 'appdirs',

    # 修复缺失的依赖 - jaraco相关
    'jaraco', 'jaraco.text', 'jaraco.functools', 'jaraco.context',
    'jaraco.collections', 'jaraco.itertools', 'jaraco.classes',

    # 元数据和导入相关
    'importlib_metadata', 'zipp', 'more_itertools',

    # pkg_resources相关
    'pkg_resources', 'pkg_resources._vendor', 'pkg_resources._vendor.packaging',
    'pkg_resources._vendor.packaging.version', 'pkg_resources._vendor.packaging.specifiers',
    'pkg_resources._vendor.packaging.requirements', 'pkg_resources._vendor.packaging.markers',
    'pkg_resources.extern', 'pkg_resources.extern.packaging',

    # setuptools相关
    'setuptools', 'setuptools.dist', 'setuptools.command', 'setuptools._distutils',
    'setuptools.extern', 'setuptools.extern.packaging', 'setuptools.extern.jaraco',

    # packaging相关
    'packaging', 'packaging.version', 'packaging.requirements', 'packaging.specifiers',
    'packaging.markers', 'packaging.utils', 'packaging.tags',

    # distutils相关
    'distutils', 'distutils.util', 'distutils.version', 'distutils.core',
    'distutils.command', 'distutils.command.build', 'distutils.command.install',

    # 其他系统模块
    'email', 'email.mime', 'email.mime.text', 'email.mime.multipart',
    'html', 'html.parser', 'xml', 'xml.etree', 'xml.etree.ElementTree',
    'collections', 'collections.abc', 'functools', 'itertools',
    'inspect', 'types', 'weakref', 'copy', 'pickle',

    # 额外的依赖修复
    'zipp', 'importlib_metadata', 'more_itertools',
    'configparser', 'platform', 'socket', 'ssl', 'hashlib',
    'base64', 'uuid', 'string', 'math', 'random', 'typing',
    'ast', 'operator', 'keyword', 'token', 'tokenize',

    # 网络相关
    'http', 'http.client', 'http.server', 'urllib', 'urllib.parse',
    'urllib.request', 'urllib.error',

    # 文件和IO
    'io', 'codecs', 'locale', 'encodings', 'encodings.utf_8',
    'encodings.cp1252', 'encodings.ascii',

    # 正则表达式和文本处理
    're', 'textwrap', 'difflib', 'unicodedata',
]

excludes = ['matplotlib', 'scipy', 'jupyter', 'IPython', 'notebook', 'pytest', 'setuptools']

block_cipher = None

a = Analysis(
    [str(app_path)],
    pathex=[str(base_path)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[r"{hooks_dir}"],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='多平台存稿工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name="多平台存稿工具",
)
'''
    
    spec_file_path = package_dir / "auto_app.spec"
    with open(spec_file_path, 'w', encoding='utf-8') as f:
        f.write(spec_content)
        
    log_message(f"spec文件已生成: {spec_file_path}")
    return spec_file_path

def run_pyinstaller(spec_file_path, package_dir):
    """运行PyInstaller打包"""
    log_message("开始PyInstaller打包...")
    
    try:
        # 构建PyInstaller命令
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm",
            "--log-level=INFO",
            str(spec_file_path)
        ]

        log_message(f"执行命令: {' '.join(cmd)}")

        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        env['PYTHONLEGACYWINDOWSSTDIO'] = '1'

        # 在打包目录中执行命令，使用更好的错误处理
        result = subprocess.run(
            cmd,
            cwd=str(package_dir),
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace',
            env=env
        )
        
        # 显示输出
        if result.stdout:
            for line in result.stdout.split('\n'):
                if line.strip():
                    log_message(line.strip(), "PYINSTALLER")
        
        if result.stderr:
            for line in result.stderr.split('\n'):
                if line.strip():
                    log_message(line.strip(), "PYINSTALLER")
        
        if result.returncode == 0:
            log_message("PyInstaller打包完成")
            return True
        else:
            log_message(f"PyInstaller打包失败，返回码: {result.returncode}", "ERROR")
            return False
            
    except Exception as e:
        log_message(f"PyInstaller执行出错: {e}", "ERROR")
        return False

def finalize_package(target_dir):
    """整理打包结果"""
    log_message("整理打包结果...")

    try:
        # 查找打包输出目录
        dist_dir = target_dir / "dist"
        if not dist_dir.exists():
            log_message("找不到dist目录", "ERROR")
            return False

        # 查找应用程序目录
        app_name = "多平台存稿工具"
        app_dir = dist_dir / app_name

        if app_dir.exists():
            log_message(f"找到应用程序目录: {app_dir}")

            # 将应用程序内容移动到目标目录根部
            log_message("移动程序文件到根目录...")
            for item in app_dir.iterdir():
                target_path = target_dir / item.name
                if target_path.exists():
                    if target_path.is_dir():
                        shutil.rmtree(target_path)
                    else:
                        target_path.unlink()
                shutil.move(str(item), str(target_path))

            # 删除空的dist目录结构
            shutil.rmtree(dist_dir)
            log_message("清理临时目录结构")

        else:
            log_message("找不到打包输出目录", "ERROR")
            return False

        # 复制配置文件
        copy_config_files(target_dir)

        # 创建启动脚本
        create_launch_script(target_dir)

        log_message("整理完成")
        return True

    except Exception as e:
        log_message(f"整理过程出错: {e}", "ERROR")
        return False

def copy_config_files(target_dir):
    """复制配置文件到目标目录"""
    log_message("复制配置文件...")

    try:
        source_dir = Path(r"C:\Users\<USER>\Downloads\网易\多平台存稿")

        # 复制主配置文件
        config_files = [
            "config.json",
            "directory_config.json",
            "timer_config.json"
        ]

        for config_file in config_files:
            source_file = source_dir / config_file
            if source_file.exists():
                target_file = target_dir / config_file
                shutil.copy2(source_file, target_file)
                log_message(f"复制配置文件: {config_file}")

        # 复制config目录
        source_config_dir = source_dir / "config"
        if source_config_dir.exists():
            target_config_dir = target_dir / "config"
            if target_config_dir.exists():
                shutil.rmtree(target_config_dir)
            shutil.copytree(source_config_dir, target_config_dir)
            log_message("复制config目录")

    except Exception as e:
        log_message(f"复制配置文件出错: {e}", "WARNING")

def create_launch_script(target_dir):
    """创建启动脚本"""
    log_message("创建启动脚本...")

    try:
        bat_content = '''@echo off
chcp 65001 >nul
title 多平台存稿工具
cd /d "%~dp0"
start "" "多平台存稿工具.exe"
'''

        bat_file = target_dir / "启动多平台存稿工具.bat"
        with open(bat_file, 'w', encoding='gbk') as f:
            f.write(bat_content)

        log_message("创建启动脚本: 启动多平台存稿工具.bat")

    except Exception as e:
        log_message(f"创建启动脚本出错: {e}", "WARNING")



def main():
    """主函数"""
    print("=" * 60)
    print("多平台存稿工具自动打包器")
    print("=" * 60)

    try:
        # 步骤1: 检查依赖
        log_message("步骤1: 检查依赖")
        if not check_dependencies():
            log_message("依赖检查失败", "ERROR")
            return False

        # 步骤2: 清理并创建目录
        log_message("步骤2: 清理并创建目录")
        package_dir, target_dir = clean_and_create_directories()

        # 步骤3: 生成spec文件
        log_message("步骤3: 生成spec文件")
        spec_file_path = generate_spec_file(package_dir)

        # 步骤4: 运行PyInstaller
        log_message("步骤4: 执行PyInstaller打包")
        if not run_pyinstaller(spec_file_path, package_dir):
            log_message("PyInstaller打包失败", "ERROR")
            return False

        # 步骤5: 整理打包结果
        log_message("步骤5: 整理打包结果")
        if not finalize_package(target_dir):
            log_message("整理失败", "ERROR")
            return False

        # 完成
        print("=" * 60)
        log_message("打包完成！")
        log_message(f"程序已部署到: {target_dir}")
        log_message("所有文件都集中在一个目录中，无需复制")
        print("=" * 60)

        return True

    except Exception as e:
        log_message(f"打包过程出错: {e}", "ERROR")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 打包成功完成！")
        print(f"📁 程序位置: D:\\多平台存稿")
        print("🚀 您现在可以运行打包好的程序了！")
    else:
        print("\n❌ 打包失败，请检查错误信息")

    input("\n按回车键退出...")

def deploy_package(package_dir, target_dir):
    """部署打包结果到目标目录"""
    log_message("开始部署打包结果...")

    try:
        # 查找打包输出目录
        dist_dir = package_dir / "dist"
        if not dist_dir.exists():
            log_message("找不到dist目录", "ERROR")
            return False

        # 查找应用程序目录
        app_name = "多平台存稿工具"
        app_dir = dist_dir / app_name

        if app_dir.exists():
            log_message(f"找到应用程序目录: {app_dir}")

            # 清理目标目录
            if target_dir.exists():
                log_message("清理现有目标目录...")
                shutil.rmtree(target_dir)

            # 复制应用程序目录到目标位置
            log_message(f"复制应用程序到: {target_dir}")
            shutil.copytree(app_dir, target_dir)

        else:
            log_message("找不到打包输出目录", "ERROR")
            return False

        # 复制配置文件
        copy_config_files(target_dir)

        # 创建启动脚本
        create_launch_script(target_dir)

        log_message("部署完成")
        return True

    except Exception as e:
        log_message(f"部署过程出错: {e}", "ERROR")
        return False

def copy_config_files(target_dir):
    """复制配置文件到目标目录"""
    log_message("复制配置文件...")

    try:
        source_dir = Path(r"C:\Users\<USER>\Downloads\网易\多平台存稿")

        # 复制主配置文件
        config_files = [
            "config.json",
            "directory_config.json",
            "timer_config.json"
        ]

        for config_file in config_files:
            source_file = source_dir / config_file
            if source_file.exists():
                target_file = target_dir / config_file
                shutil.copy2(source_file, target_file)
                log_message(f"复制配置文件: {config_file}")

        # 复制config目录
        source_config_dir = source_dir / "config"
        if source_config_dir.exists():
            target_config_dir = target_dir / "config"
            if target_config_dir.exists():
                shutil.rmtree(target_config_dir)
            shutil.copytree(source_config_dir, target_config_dir)
            log_message("复制config目录")

    except Exception as e:
        log_message(f"复制配置文件出错: {e}", "WARNING")

def create_launch_script(target_dir):
    """创建启动脚本"""
    log_message("创建启动脚本...")

    try:
        # 创建批处理启动脚本
        bat_content = '''@echo off
chcp 65001 >nul
title 多平台存稿工具
cd /d "%~dp0"
start "" "多平台存稿工具.exe"
'''

        bat_file = target_dir / "启动多平台存稿工具.bat"
        with open(bat_file, 'w', encoding='gbk') as f:
            f.write(bat_content)

        log_message("创建启动脚本: 启动多平台存稿工具.bat")

    except Exception as e:
        log_message(f"创建启动脚本出错: {e}", "WARNING")

def main():
    """主函数"""
    print("=" * 60)
    print("多平台存稿工具自动打包器")
    print("=" * 60)

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 打包成功完成！")
        print(f"📁 程序位置: D:\\多平台存稿")
        print("🚀 您现在可以运行打包好的程序了！")
    else:
        print("\n❌ 打包失败，请检查错误信息")

    input("\n按回车键退出...")
