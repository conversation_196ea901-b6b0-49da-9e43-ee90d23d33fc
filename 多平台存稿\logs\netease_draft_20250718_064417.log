[2025-07-18 06:44:30] [INFO] 🚀 启动多平台存稿工具...
[2025-07-18 06:44:30] [ERROR] 程序发生未捕获的异常: unexpected indent (data_query_manager.py, line 231)
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\main.py", line 77, in main
    app = NeteaseDraftUI(root, config_manager)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\ui\main.py", line 180, in __init__
    self._init_managers()
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\ui\main.py", line 255, in _init_managers
    from 网易号存稿.ui.managers.data_query_manager import DataQueryManager
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\ui\managers\data_query_manager.py", line 231
    self.parent_ui.account_manager.update_account_status(account, AccountStatus.QUERY_FAILED, f"返回无效结果类型: {type(result)}")
IndentationError: unexpected indent

