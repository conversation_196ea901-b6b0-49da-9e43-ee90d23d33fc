
import tkinter as tk
from modules.ui_base import VideoPreprocessUI

def test_font_sizes():
    """测试字体大小设置"""
    root = tk.Tk()
    root.withdraw()
    
    try:
        ui = VideoPreprocessUI(root)
        
        print(f"默认字体大小: {ui.default_font_size}")
        print(f"字体大小变量: {ui.font_size_var.get()}")
        print(f"封面字体大小: {ui.cover_size.get()}")
        
        # 测试字体大小设置
        test_sizes = [10, 12, 14, 16, 18]
        for size in test_sizes:
            ui.default_font_size = size
            ui.font_size_var.set(size)
            print(f"设置字体大小为 {size}: 默认={ui.default_font_size}, 变量={ui.font_size_var.get()}")
        
        return True
    except Exception as e:
        print(f"测试失败: {e}")
        return False
    finally:
        root.destroy()

if __name__ == "__main__":
    test_font_sizes()
