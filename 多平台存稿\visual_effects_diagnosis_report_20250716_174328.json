{"timestamp": "2025-07-16T17:43:28.961247", "diagnosis_results": {"1. 基础组件存在性检查": {"status": "success", "details": ["✅ 主UI初始化成功", "✅ AnimationEngine: 存在且已初始化", "   ✅ fade_in方法: 存在", "   ✅ fade_out方法: 存在", "   ✅ slide_in_from_left方法: 存在", "   ✅ scale_in方法: 存在", "✅ StatusFeedbackManager: 存在且已初始化", "   ✅ success方法: 存在", "   ✅ error方法: 存在", "   ✅ warning方法: 存在", "   ✅ info方法: 存在", "✅ VisualEffectsManager: 存在且已初始化", "✅ ResponsiveLayoutManager: 存在且已初始化", "✅ LogManager: 存在且已初始化", "✅ 便捷方法 show_success_message: 存在", "✅ 便捷方法 show_error_message: 存在", "✅ 便捷方法 show_warning_message: 存在", "✅ 便捷方法 show_info_message: 存在", "✅ 便捷方法 animate_widget: 存在"]}, "2. 组件几何属性检查": {"status": "warning", "details": ["⚠️ Toast创建测试失败: 'StatusFeedbackManager' object has no attribute 'create_toast'", "✅ 动画目标几何属性: 1x1"]}, "3. 组件可见性状态检查": {"status": "warning", "details": ["✅ visual_effects_enabled: 已启用", "✅ animations_enabled: 已启用", "✅ feedback_enabled: 已启用", "✅ 当前主题模式: light", "⚠️ 主窗口: 不可见", "✅ 主窗口大小: 1400x900"]}, "4. 事件触发机制检查": {"status": "success", "details": ["✅ show_success_message: 调用成功 (22.36ms)", "✅ show_error_message: 调用成功 (6.10ms)", "✅ show_warning_message: 调用成功 (24.62ms)", "✅ show_info_message: 调用成功 (7.65ms)", "✅ animate_widget: 调用成功 (0.00ms)", "✅ animate_widget: 返回了动画对象", "✅ 直接调用success: 成功 (5.99ms)", "✅ 直接调用error: 成功 (0.00ms)", "✅ 直接调用warning: 成功 (0.00ms)", "✅ 直接调用info: 成功 (0.00ms)"]}, "5. 样式和层级检查": {"status": "warning", "details": ["✅ 浅色主题应用: 成功", "✅ 增强浅色主题应用: 成功", "⚠️ 层级检查失败: 'StatusFeedbackManager' object has no attribute 'create_toast'", "✅ 窗口属性: ('-alpha', 1.0, '-transparentcolor', '', '-disabled', 0, '-fullscreen', 0, '-toolwindow', 0, '-topmost', 0)", "✅ 窗口层级: 检测到topmost设置"]}, "6. 配置状态检查": {"status": "success", "details": ["✅ visual_effects_enabled: True (符合预期)", "✅ animations_enabled: True (符合预期)", "✅ transitions_enabled: True (符合预期)", "✅ feedback_enabled: True (符合预期)", "✅ enhanced_log_display: True (符合预期)", "✅ dark_mode: False (符合预期)", "✅ animation_duration: 300 (符合预期)", "✅ animation_easing: ease_out (符合预期)", "✅ performance_mode: balanced (符合预期)", "✅ max_animations: 10 (符合预期)", "✅ 配置文件: 可读写"]}, "7. 实时显示测试": {"status": "success", "details": ["✅ 创建实时测试窗口", "✅ 实时显示测试设置完成"]}, "8. 交互式修复测试": {"status": "success", "details": ["✅ 修复1: 配置重置完成", "✅ 修复2: 强制重新初始化完成", "✅ 修复3: 组件位置修复完成", "✅ 应用的修复措施: 配置重置, 强制重新初始化, 组件位置修复", "✅ 修复后测试: 成功消息显示正常", "✅ 修复后测试: 动画功能正常"]}}, "system_info": {"python_version": "3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]", "tkinter_version": 8.6, "platform": "win32"}}