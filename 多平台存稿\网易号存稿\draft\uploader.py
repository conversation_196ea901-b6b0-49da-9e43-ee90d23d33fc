"""
视频上传模块 - 负责处理视频上传和存稿
"""

import os
import time
import random
import traceback
from typing import Optional, Callable, List
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException

from 网易号存稿.common.constants import (
    VIDEO_UPLOAD_AREA,
    VIDEO_UPLOAD_SUCCESS,
    UPLOAD_COVER_BUTTON,
    LOCAL_UPLOAD_BUTTON,
    COVER_UPLOAD_INPUT,
    COVER_CONFIRM_BUTTON,
    TITLE_INPUT,
    DECLARATION_BUTTON,
    DECLARATION_CONTENT,
    INTERNET_SOURCE,
    CATEGORY_INPUT,
    CATEGORY_FIRST_LEVEL,
    CATEGORY_SECOND_LEVEL,
    TAG_INPUT,
    SAVE_DRAFT_BUTTON
)
from 网易号存稿.draft.tagger import generate_tags

def random_sleep(min_seconds: float, max_seconds: float) -> None:
    """
    随机睡眠一段时间

    Args:
        min_seconds: 最小睡眠时间（秒）
        max_seconds: 最大睡眠时间（秒）
    """
    sleep_time = min_seconds + random.random() * (max_seconds - min_seconds)
    time.sleep(sleep_time)

class VideoUploader:
    """视频上传类，负责处理视频上传和存稿"""

    def __init__(self, driver: webdriver.Chrome, log_callback: Callable = None):
        """
        初始化视频上传器

        Args:
            driver: 浏览器驱动
            log_callback: 日志回调函数
        """
        self.driver = driver
        self.log_callback = log_callback

    def log(self, message: str) -> None:
        """
        记录日志

        Args:
            message: 日志消息
        """
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def upload_and_draft(self, video_path: str, cover_path: Optional[str] = None) -> bool:
        """
        上传视频并存稿

        Args:
            video_path: 视频文件路径
            cover_path: 封面文件路径

        Returns:
            是否成功存稿
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(video_path):
                self.log(f"视频文件不存在: {video_path}")
                return False

            if cover_path and not os.path.exists(cover_path):
                self.log(f"封面文件不存在: {cover_path}")
                cover_path = None

            # 上传视频
            if not self.upload_video(video_path):
                self.log("上传视频失败")
                return False

            # 等待视频上传成功
            if not self.wait_for_video_upload():
                self.log("等待视频上传超时")
                return False

            # 获取视频标题
            video_title = os.path.splitext(os.path.basename(video_path))[0]

            # 设置视频标题
            if not self.set_title(video_title):
                self.log("设置视频标题失败")
                return False

            # 上传封面（如果有）
            if cover_path:
                if not self.upload_cover(cover_path):
                    self.log("上传封面失败")
                    # 继续处理，封面不是必须的

            # 设置声明
            if not self.set_declaration():
                self.log("设置声明失败")
                # 继续处理，可能已经有默认声明

            # 设置分类
            if not self.set_category():
                self.log("设置分类失败")
                # 继续处理，可能已经有默认分类

            # 设置标签
            if not self.set_tags(video_title):
                self.log("设置标签失败")
                # 继续处理，可能已经有默认标签

            # 保存为草稿
            if not self.save_draft():
                self.log("保存草稿失败")
                return False

            self.log("视频存稿成功")
            return True

        except Exception as e:
            self.log(f"视频存稿过程中发生错误: {str(e)}")
            traceback.print_exc()
            return False

    def upload_video(self, video_path: str) -> bool:
        """
        上传视频文件 - 优化版，直接使用file input元素，避免打开文件窗口

        Args:
            video_path: 视频文件路径

        Returns:
            是否成功上传
        """
        try:
            # 格式化视频路径 - 处理Windows路径斜杠
            video_path_formatted = video_path.replace('\\', '/')

            # 点击上传区域
            element = self.driver.find_element(By.XPATH, VIDEO_UPLOAD_AREA)
            self.driver.execute_script("arguments[0].click();", element)
            time.sleep(1)

            # 查找文件输入框并上传
            file_inputs = self.driver.find_elements(By.CSS_SELECTOR, 'input[type="file"]')
            if file_inputs:
                for input_elem in file_inputs:
                    try:
                        # 确保元素可见和可交互
                        self.driver.execute_script("arguments[0].style.display = 'block'; arguments[0].style.opacity = '1';", input_elem)
                        input_elem.send_keys(video_path_formatted)
                        self.log("视频文件已通过找到的输入框上传")
                        return True
                    except Exception as e:
                        continue

                # 如果循环结束但没有成功，尝试使用第一个输入框
                try:
                    file_inputs[0].send_keys(video_path_formatted)
                    self.log("视频文件已通过第一个输入框上传")
                    return True
                except Exception as e:
                    self.log(f"使用第一个输入框上传视频失败: {str(e)}")
                    raise
            else:
                # 如果没有找到文件输入框，创建一个
                self.log("未找到文件输入框，尝试创建新的输入框")
                js_code = """
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.accept = 'video/*';
                    input.style.position = 'absolute';
                    input.style.left = '-9999px';
                    input.id = 'custom-file-input';
                    document.body.appendChild(input);
                    return input;
                """
                file_input = self.driver.execute_script(js_code)
                file_input.send_keys(video_path_formatted)
                self.driver.execute_script("arguments[0].dispatchEvent(new Event('change'));", file_input)
                self.log("已通过创建的输入框上传视频")
                return True
        except Exception as e:
            self.log(f"视频上传失败: {str(e)}")
            traceback.print_exc()
            return False

    def wait_for_video_upload(self, timeout: int = 1200) -> bool:
        """
        等待视频上传完成 - 使用改进的检测方法

        Args:
            timeout: 超时时间（秒）

        Returns:
            是否成功上传
        """
        try:
            self.log(f"⏳ 等待视频上传完成，超时时间: {timeout/60} 分钟")

            # 使用改进的检测方法
            success = self._wait_for_upload_completion_improved(timeout)

            if success:
                self.log("✅ 视频上传成功")
                return True
            else:
                self.log("❌ 视频上传失败或超时")
                return False

        except Exception as e:
            self.log(f"❌ 等待视频上传时出错: {str(e)}")
            return False

    def upload_cover(self, cover_path: str) -> bool:
        """
        上传封面图片

        Args:
            cover_path: 封面图片路径

        Returns:
            是否成功上传
        """
        try:
            # 点击上传封面按钮
            cover_button = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, UPLOAD_COVER_BUTTON))
            )
            self.driver.execute_script("arguments[0].click();", cover_button)
            time.sleep(1)

            # 点击本地上传按钮
            local_button = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, LOCAL_UPLOAD_BUTTON))
            )
            self.driver.execute_script("arguments[0].click();", local_button)
            time.sleep(1)

            # 上传封面文件
            cover_input = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, COVER_UPLOAD_INPUT))
            )
            cover_path_formatted = cover_path.replace('\\', '/')
            cover_input.send_keys(cover_path_formatted)
            self.log(f"已选择封面文件: {os.path.basename(cover_path)}")

            # 点击确认按钮
            confirm_button = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, COVER_CONFIRM_BUTTON))
            )
            self.driver.execute_script("arguments[0].click();", confirm_button)
            time.sleep(2)

            self.log("✅ 封面上传成功")
            return True
        except Exception as e:
            self.log(f"❌ 封面上传失败: {str(e)}")
            return False

    def set_title(self, title: str) -> bool:
        """
        设置视频标题

        Args:
            title: 视频标题

        Returns:
            是否成功设置
        """
        try:
            # 等待标题输入框出现
            title_input = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, TITLE_INPUT))
            )

            # 清空输入框
            title_input.clear()

            # 输入标题
            title_input.send_keys(title)
            self.log(f"已设置视频标题: {title}")

            return True
        except Exception as e:
            self.log(f"❌ 设置标题失败: {str(e)}")
            return False

    def set_declaration(self) -> bool:
        """
        设置声明（取材网络）

        Returns:
            是否成功设置
        """
        try:
            # 点击声明按钮
            declaration_button = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, DECLARATION_BUTTON))
            )
            self.driver.execute_script("arguments[0].click();", declaration_button)
            time.sleep(1)

            # 选择"取材网络"选项
            internet_option = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, INTERNET_SOURCE))
            )
            self.driver.execute_script("arguments[0].click();", internet_option)
            time.sleep(1)

            self.log("已设置声明: 取材网络")
            return True
        except Exception as e:
            self.log(f"❌ 设置声明失败: {str(e)}")
            return False

    def set_category(self) -> bool:
        """
        设置分类

        Returns:
            是否成功设置
        """
        try:
            # 点击分类输入框
            category_input = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, CATEGORY_INPUT))
            )
            self.driver.execute_script("arguments[0].click();", category_input)
            time.sleep(1)

            # 选择第一级分类
            first_level = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, CATEGORY_FIRST_LEVEL))
            )
            self.driver.execute_script("arguments[0].click();", first_level)
            time.sleep(1)

            # 选择第二级分类
            second_level = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, CATEGORY_SECOND_LEVEL))
            )
            self.driver.execute_script("arguments[0].click();", second_level)
            time.sleep(1)

            self.log("已设置分类")
            return True
        except Exception as e:
            self.log(f"❌ 设置分类失败: {str(e)}")
            return False

    def set_tags(self, title: str) -> bool:
        """
        设置标签

        Args:
            title: 视频标题，用于生成标签

        Returns:
            是否成功设置
        """
        try:
            # 标签处理
            tags = generate_tags(title)

            # 确保标签数量至少为3个，特别是在并发模式下
            if len(tags) < 3:
                self.log(f"警告: 生成的标签数量不足3个，当前只有 {len(tags)} 个")
                # 添加默认标签补充到3个
                default_tags = ["视频", "精彩", "推荐"]
                for tag in default_tags:
                    if tag not in tags and len(tags) < 3:
                        tags.append(tag)
                self.log(f"已补充默认标签，现在有 {len(tags)} 个标签")

            # 滚动到标签区域
            self.driver.execute_script("window.scrollBy(0, 300);")  # 向下滚动一点以确保元素可见
            time.sleep(0.5)

            # 查找标签输入框元素
            tag_input = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, TAG_INPUT))
            )
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", tag_input)
            time.sleep(0.5)

            # 点击标签输入框，确保聚焦
            tag_input.click()
            time.sleep(0.8)

            # 清空可能存在的内容
            ActionChains(self.driver).key_down(Keys.CONTROL).send_keys('a').key_up(Keys.CONTROL).send_keys(Keys.DELETE).perform()
            time.sleep(0.5)

            # 逐个标签输入，模拟人工手动输入
            for tag in tags:
                # 一个字符一个字符地输入，模拟真人输入速度
                for char in tag:
                    ActionChains(self.driver).send_keys(char).perform()
                    # 较慢的随机打字速度，模拟人工输入
                    time.sleep(0.1 + random.random() * 0.2)

                # 输入空格分隔标签 - 按一次空格，根据平台要求
                ActionChains(self.driver).send_keys(Keys.SPACE).perform()
                time.sleep(0.3)
                self.log(f"已输入标签 '{tag}' 并按空格键")

            # 最后按回车键确认
            ActionChains(self.driver).send_keys(Keys.ENTER).perform()
            time.sleep(0.5)

            # 验证标签是否成功设置 - 直接使用JavaScript获取标签框内的文本
            try:

                # 使用JavaScript直接获取标签容器的文本内容，避免Selenium的stale element问题
                from 网易号存稿.common.constants import TAG_CONTAINER

                # 使用JavaScript获取标签容器的文本内容
                js_script = """
                    var container = document.evaluate(arguments[0], document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                    if (!container) return '';
                    return container.textContent || container.innerText || '';
                """
                tag_container_text = self.driver.execute_script(js_script, TAG_CONTAINER)

                # 如果获取到文本，分析标签
                if tag_container_text and tag_container_text.strip():
                    # 清理文本，去除多余空格
                    cleaned_text = ' '.join(tag_container_text.strip().split())
                    self.log(f"标签容器文本内容: {cleaned_text}")

                    # 尝试从文本中提取标签
                    # 标签通常由空格、逗号或其他分隔符分隔
                    possible_tags = []
                    for separator in [' ', ',', '，', ';', '；', '|']:
                        if separator in cleaned_text:
                            split_tags = [t.strip() for t in cleaned_text.split(separator) if t.strip()]
                            if len(split_tags) > len(possible_tags):
                                possible_tags = split_tags

                    # 如果没有找到分隔的标签，将整个文本作为一个标签
                    if not possible_tags and cleaned_text:
                        possible_tags = [cleaned_text]

                    # 过滤掉可能的非标签文本（如"添加标签"等提示文字）
                    filtered_tags = []
                    for tag in possible_tags:
                        # 排除常见的非标签文本
                        if tag not in ["添加标签", "请输入标签", "标签", "tag", "tags", "添加", "输入"]:
                            filtered_tags.append(tag)

                    # 检查是否找到了足够的标签
                    if len(filtered_tags) >= 3:
                        self.log(f"✅ 成功检测到 {len(filtered_tags)} 个标签: {', '.join(filtered_tags)}")
                    else:
                        self.log(f"⚠️ 检测到的标签数量不足3个，只有 {len(filtered_tags)} 个: {', '.join(filtered_tags)}")

                        # 获取标签容器的HTML内容，用于调试
                        js_html_script = """
                            var container = document.evaluate(arguments[0], document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                            if (!container) return '';
                            return container.innerHTML || '';
                        """
                        container_html = self.driver.execute_script(js_html_script, TAG_CONTAINER)
                        self.log(f"标签容器HTML内容: {container_html[:100]}...")  # 只显示前100个字符，避免日志过长

                        # 如果标签数量不足，尝试重新输入
                        self.log("尝试重新输入标签...")

                        # 使用JavaScript点击标签输入框
                        js_click_script = """
                            var input = document.evaluate(arguments[0], document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                            if (input) {
                                input.click();
                                return true;
                            }
                            return false;
                        """
                        click_success = self.driver.execute_script(js_click_script, TAG_INPUT)

                        if click_success:
                            time.sleep(0.8)

                            # 使用JavaScript清空输入框
                            js_clear_script = """
                                var input = document.evaluate(arguments[0], document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                                if (input) {
                                    input.value = '';
                                    return true;
                                }
                                return false;
                            """
                            self.driver.execute_script(js_clear_script, TAG_INPUT)
                            time.sleep(0.5)

                            # 使用ActionChains输入标签
                            # 直接输入完整标签字符串，用空格分隔
                            tag_string = " ".join(tags)
                            ActionChains(self.driver).send_keys(tag_string).perform()
                            time.sleep(0.5)

                            # 按一次空格和回车确认
                            ActionChains(self.driver).send_keys(Keys.SPACE).perform()
                            time.sleep(0.3)
                            self.log("已在备用方法中输入标签并按空格键")
                            ActionChains(self.driver).send_keys(Keys.ENTER).perform()
                            time.sleep(1)

                            # 再次检查标签
                            tag_container_text = self.driver.execute_script(js_script, TAG_CONTAINER)
                            if tag_container_text and tag_container_text.strip():
                                cleaned_text = ' '.join(tag_container_text.strip().split())
                                self.log(f"重新输入后标签容器文本内容: {cleaned_text}")

                                # 再次尝试提取标签
                                new_possible_tags = []
                                for separator in [' ', ',', '，', ';', '；', '|']:
                                    if separator in cleaned_text:
                                        split_tags = [t.strip() for t in cleaned_text.split(separator) if t.strip()]
                                        if len(split_tags) > len(new_possible_tags):
                                            new_possible_tags = split_tags

                                if not new_possible_tags and cleaned_text:
                                    new_possible_tags = [cleaned_text]

                                # 过滤非标签文本
                                new_filtered_tags = []
                                for tag in new_possible_tags:
                                    if tag not in ["添加标签", "请输入标签", "标签", "tag", "tags", "添加", "输入"]:
                                        new_filtered_tags.append(tag)

                                self.log(f"重新输入后检测到 {len(new_filtered_tags)} 个标签: {', '.join(new_filtered_tags)}")
                else:
                    self.log("⚠️ 未能获取到标签容器的文本内容")

                    # 如果无法获取标签容器文本，尝试重新输入标签
                    self.log("尝试重新输入标签...")

                    # 使用JavaScript点击标签输入框
                    js_click_script = """
                        var input = document.evaluate(arguments[0], document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                        if (input) {
                            input.click();
                            return true;
                        }
                        return false;
                    """
                    click_success = self.driver.execute_script(js_click_script, TAG_INPUT)

                    if click_success:
                        time.sleep(0.8)

                        # 使用ActionChains输入标签
                        # 清空可能存在的内容
                        ActionChains(self.driver).key_down(Keys.CONTROL).send_keys('a').key_up(Keys.CONTROL).send_keys(Keys.DELETE).perform()
                        time.sleep(0.5)

                        # 直接输入完整标签字符串，用空格分隔
                        tag_string = " ".join(tags)
                        ActionChains(self.driver).send_keys(tag_string).perform()
                        time.sleep(0.5)

                        # 按一次空格和回车确认
                        ActionChains(self.driver).send_keys(Keys.SPACE).perform()
                        time.sleep(0.3)
                        self.log("已在备用方法中输入标签并按空格键")
                        ActionChains(self.driver).send_keys(Keys.ENTER).perform()
                        time.sleep(0.5)

                        self.log("已尝试重新设置标签")

            except Exception as verify_error:
                self.log(f"验证标签设置时出错: {str(verify_error)}")
                # 继续执行，不要因为验证失败而中断流程

            self.log(f"已设置标签: {', '.join(tags)}")
            return True
        except Exception as e:
            self.log(f"❌ 设置标签失败: {str(e)}")
            return False

    def save_draft(self) -> bool:
        """
        保存为草稿

        Returns:
            是否成功保存
        """
        try:

            # 保存草稿
            self.log("保存草稿")
            try:
                # 查找并点击保存草稿按钮
                save_draft_button = WebDriverWait(self.driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, SAVE_DRAFT_BUTTON))
                )
                self.driver.execute_script("arguments[0].click();", save_draft_button)
                self.log("已点击保存草稿按钮")

                # 等待保存成功提示
                time.sleep(3)  # 等待保存操作完成

                # 检查是否有成功提示
                success_messages = self.driver.find_elements(By.XPATH, "//*[contains(text(), '成功') or contains(text(), '已保存')]")
                if success_messages:
                    self.log("✅ 草稿保存成功")
                    return True
                else:
                    # 即使没有明确的成功提示，也认为可能已经保存成功
                    self.log("⚠️ 未检测到明确的保存成功提示，但草稿可能已保存")
                    return True
            except Exception as e:
                self.log(f"❌ 保存草稿时发生错误: {str(e)}")
                return False
        except Exception as e:
            self.log(f"❌ 保存草稿失败: {str(e)}")
            return False

    def _wait_for_upload_completion_improved(self, timeout: int = 180) -> bool:
        """
        改进的视频上传完成检测方法 - 专注于文字检测

        检测成功判定：元素中的文字变更为"推荐封面"即为成功

        特性:
        1. 浏览器隔离检测 - 确保每个浏览器实例独立检测
        2. 二次检测机制 - 每3秒检测一次，并进行二次验证
        3. 纯文字检测 - 专注于检测元素文字内容，不依赖JavaScript
        4. 多种文字检测方式 - 使用XPath和备用方法确保检测准确性

        Args:
            timeout: 超时时间（秒）

        Returns:
            是否检测到上传完成（文字变为"推荐封面"）
        """
        try:
            self.log(f"🔍 开始改进的视频上传完成检测，超时时间: {timeout}秒")

            # 目标XPath - 用户提供的检测路径
            target_xpath = '//*[@id="root"]/div/div[2]/div/div[2]/div/div/div[1]/div/div[2]/div[1]/div[2]/div[2]/div'
            target_text = "推荐封面"

            # 检测间隔为3秒
            check_interval = 3
            max_checks = timeout // check_interval

            self.log(f"🔍 检测配置: 每{check_interval}秒检测一次，最多检测{max_checks}次")

            for check_count in range(max_checks):
                try:
                    current_time = (check_count + 1) * check_interval
                    self.log(f"🔍 第{check_count + 1}次检测 ({current_time}秒)")

                    # 方法1: 使用精确文字检测
                    success_detected = self._check_upload_success_by_text(target_xpath, target_text)

                    if success_detected:
                        self.log(f"✅ 第{check_count + 1}次检测: 通过文字检测到上传完成")

                        # 进行二次验证 - 等待3秒后再次检测
                        self.log("🔍 进行二次验证...")
                        time.sleep(3)

                        # 二次检测
                        second_check = self._check_upload_success_by_text(target_xpath, target_text)
                        if second_check:
                            self.log("✅ 二次验证成功: 确认视频上传完成")
                            return True
                        else:
                            self.log("⚠️ 二次验证失败: 可能是临时状态，继续检测")

                    # 方法2: 备用文字检测方法
                    backup_success = self._check_upload_success_backup_text()
                    if backup_success:
                        self.log(f"✅ 第{check_count + 1}次检测: 通过备用文字检测到上传完成")

                        # 进行二次验证
                        self.log("🔍 进行二次验证...")
                        time.sleep(3)

                        second_backup_check = self._check_upload_success_backup_text()
                        if second_backup_check:
                            self.log("✅ 二次验证成功: 确认视频上传完成")
                            return True
                        else:
                            self.log("⚠️ 二次验证失败: 可能是临时状态，继续检测")

                    # 如果都没有检测到，等待下一次检测
                    if check_count < max_checks - 1:  # 不是最后一次检测
                        self.log(f"⏳ 第{check_count + 1}次检测未发现上传完成，等待{check_interval}秒后继续...")
                        time.sleep(check_interval)

                except Exception as e:
                    self.log(f"⚠️ 第{check_count + 1}次检测时发生错误: {str(e)}")
                    if check_count < max_checks - 1:
                        time.sleep(check_interval)
                    continue

            self.log(f"❌ 检测超时: 在{timeout}秒内未检测到视频上传完成")
            return False

        except Exception as e:
            self.log(f"❌ 改进检测方法执行失败: {str(e)}")
            return False

    def _check_upload_success_by_text(self, xpath: str, target_text: str) -> bool:
        """
        使用文字检测上传成功状态（浏览器隔离）
        专注于检测元素中的文字是否变更为目标文字

        Args:
            xpath: 目标元素的XPath
            target_text: 目标文本内容

        Returns:
            是否检测到成功状态
        """
        try:
            self.log(f"🔍 文字检测开始: 查找路径 {xpath}")
            self.log(f"🔍 目标文字: '{target_text}'")

            # 确保只在当前浏览器窗口中查找，避免多浏览器干扰
            elements = self.driver.find_elements(By.XPATH, xpath)
            self.log(f"🔍 文字检测: 找到 {len(elements)} 个匹配元素")

            if not elements:
                self.log(f"🔍 文字检测: 未找到匹配的元素")
                return False

            for i, element in enumerate(elements):
                try:
                    # 获取元素文字信息
                    element_tag = element.tag_name
                    element_text = element.text.strip()
                    is_displayed = element.is_displayed()

                    self.log(f"🔍 文字检测: 元素 {i+1} - 标签: {element_tag}, 可见: {is_displayed}")
                    self.log(f"🔍 文字检测: 元素 {i+1} - 实际文字: '{element_text}'")

                    # 检查元素是否可见且文字完全匹配目标文字
                    if is_displayed and element_text == target_text:
                        self.log(f"✅ 文字检测成功: 元素文字完全匹配目标文字 '{target_text}'")
                        return True
                    elif is_displayed and target_text in element_text:
                        self.log(f"✅ 文字检测成功: 元素文字包含目标文字 '{target_text}'")
                        return True
                    elif target_text in element_text:
                        self.log(f"⚠️ 文字检测: 找到包含目标文字的元素但不可见")
                    else:
                        self.log(f"🔍 文字检测: 元素文字不匹配目标文字")

                except Exception as element_error:
                    self.log(f"⚠️ 文字检测: 处理元素 {i+1} 时出错: {str(element_error)}")
                    continue

            return False

        except Exception as e:
            self.log(f"❌ 文字检测失败: {str(e)}")
            return False



    def _check_upload_success_backup_text(self) -> bool:
        """
        备用文字检测方法 - 使用多种方式检测"推荐封面"文字
        专注于文字内容检测，不依赖JavaScript

        Returns:
            是否检测到成功状态
        """
        try:
            target_text = "推荐封面"

            # 方法1: 检测页面源码中是否包含"推荐封面"文字（静默检测）
            try:
                page_source = self.driver.page_source
                if target_text in page_source:
                    self.log(f"✅ 视频上传完成检测成功")
                    return True
            except Exception as e:
                pass

            # 方法2: 查找所有包含"推荐封面"的元素
            try:
                elements_with_text = self.driver.find_elements(
                    By.XPATH,
                    f"//*[contains(text(), '{target_text}')]"
                )

                self.log(f"🔍 备用文字检测方法2: 找到 {len(elements_with_text)} 个包含'{target_text}'的元素")

                for i, element in enumerate(elements_with_text):
                    try:
                        element_text = element.text.strip()
                        is_displayed = element.is_displayed()

                        self.log(f"🔍 备用文字检测方法2: 元素 {i+1} - 可见: {is_displayed}, 文字: '{element_text}'")

                        if is_displayed and target_text in element_text:
                            self.log(f"✅ 备用文字检测方法2成功: 找到可见的'{target_text}'元素")
                            return True
                    except Exception as element_error:
                        self.log(f"⚠️ 备用文字检测方法2: 处理元素 {i+1} 时出错: {str(element_error)}")
                        continue

            except Exception as e:
                self.log(f"⚠️ 备用文字检测方法2失败: {str(e)}")

            # 方法3: 使用更宽泛的XPath查找包含目标文字的元素
            try:
                xpath_patterns = [
                    f"//div[contains(text(), '{target_text}')]",
                    f"//span[contains(text(), '{target_text}')]",
                    f"//*[text()='{target_text}']",  # 完全匹配
                ]

                for j, xpath_pattern in enumerate(xpath_patterns):
                    try:
                        elements = self.driver.find_elements(By.XPATH, xpath_pattern)
                        for element in elements:
                            if element.is_displayed():
                                element_text = element.text.strip()
                                if element_text == target_text or target_text in element_text:
                                    self.log(f"✅ 备用文字检测方法3成功: 找到'{target_text}'文字")
                                    return True
                    except:
                        continue

            except Exception as e:
                self.log(f"⚠️ 备用文字检测方法3失败: {str(e)}")

            self.log(f"🔍 备用文字检测: 所有方法都未检测到'{target_text}'文字")
            return False

        except Exception as e:
            self.log(f"❌ 备用文字检测失败: {str(e)}")
            return False
