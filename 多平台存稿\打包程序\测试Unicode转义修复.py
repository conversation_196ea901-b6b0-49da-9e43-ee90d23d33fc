#!/usr/bin/env python3
"""
测试Unicode转义问题修复
"""

import sys
import tempfile
from pathlib import Path

def test_spec_generation():
    """测试spec文件生成"""
    print("🔍 测试spec文件生成...")
    
    try:
        # 添加当前目录到路径
        sys.path.insert(0, str(Path(__file__).parent))
        
        from 统一自动打包 import UnifiedPackager
        
        # 创建临时目录进行测试
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # 创建打包器实例
            packager = UnifiedPackager()
            
            # 生成spec文件
            spec_file = packager.generate_spec_file(temp_path)
            
            print(f"✅ spec文件生成成功: {spec_file}")
            
            # 检查spec文件内容
            if spec_file.exists():
                with open(spec_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否包含问题字符
                if '\\U' in content or '\\u' in content:
                    print("⚠️  spec文件可能仍包含Unicode转义问题")
                    # 显示问题行
                    lines = content.split('\n')
                    for i, line in enumerate(lines, 1):
                        if '\\U' in line or '\\u' in line:
                            print(f"  第{i}行: {line}")
                    return False
                else:
                    print("✅ spec文件无Unicode转义问题")
                    return True
            else:
                print("❌ spec文件未生成")
                return False
                
    except Exception as e:
        print(f"❌ spec文件生成测试失败: {e}")
        return False

def test_path_handling():
    """测试路径处理"""
    print("🔍 测试路径处理...")
    
    try:
        # 测试Windows路径转换
        test_paths = [
            r"C:\Users\<USER>\Downloads\网易\多平台存稿",
            r"D:\多平台存稿",
            r"C:\Program Files\Python\Scripts"
        ]
        
        for path in test_paths:
            # 转换为正斜杠
            converted = path.replace('\\', '/')
            print(f"  {path} → {converted}")
            
            # 检查是否还有反斜杠
            if '\\' in converted:
                print(f"❌ 路径转换失败: {converted}")
                return False
        
        print("✅ 路径处理正常")
        return True
        
    except Exception as e:
        print(f"❌ 路径处理测试失败: {e}")
        return False

def test_spec_syntax():
    """测试spec文件语法"""
    print("🔍 测试spec文件语法...")
    
    try:
        sys.path.insert(0, str(Path(__file__).parent))
        from 统一自动打包 import UnifiedPackager
        
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            packager = UnifiedPackager()
            spec_file = packager.generate_spec_file(temp_path)
            
            if spec_file.exists():
                # 尝试编译spec文件检查语法
                with open(spec_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                try:
                    compile(content, str(spec_file), 'exec')
                    print("✅ spec文件语法正确")
                    return True
                except SyntaxError as e:
                    print(f"❌ spec文件语法错误: {e}")
                    print(f"  错误位置: 第{e.lineno}行")
                    if e.text:
                        print(f"  错误内容: {e.text.strip()}")
                    return False
            else:
                print("❌ spec文件不存在")
                return False
                
    except Exception as e:
        print(f"❌ spec文件语法测试失败: {e}")
        return False

def show_fix_summary():
    """显示修复总结"""
    print("\n💡 Unicode转义问题修复总结:")
    print("=" * 50)
    print("问题: Windows路径中的反斜杠在spec文件中被错误解释为Unicode转义")
    print("修复: 将所有路径中的反斜杠转换为正斜杠")
    print("方法: path.replace('\\\\', '/')")
    print("影响: datas列表、hookspath、base_path等所有路径")
    print("结果: 避免SyntaxError: unicode error")
    print("=" * 50)

def main():
    """主测试函数"""
    print("=" * 60)
    print("Unicode转义问题修复验证")
    print("=" * 60)
    
    tests = [
        ("路径处理", test_path_handling),
        ("spec文件生成", test_spec_generation),
        ("spec文件语法", test_spec_syntax),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*15} {test_name} {'='*15}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 出错: {e}")
    
    show_fix_summary()
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 Unicode转义问题修复成功！")
        print("✅ 统一自动打包器现在可以正常生成spec文件")
    else:
        print("⚠️  仍有问题需要解决")
    
    print("=" * 60)
    
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n修复{'成功' if success else '失败'}")
    input("按回车键退出...")
