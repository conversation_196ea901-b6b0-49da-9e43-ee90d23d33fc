"""
大鱼号平台存稿处理模块 - 负责处理大鱼号平台的视频存稿
"""

import os
import time
import random
import glob
import shutil
import pickle
import json
import datetime
import traceback
import re
import threading
import concurrent.futures
from queue import Queue
from typing import List, Dict, Any, Optional, Tuple, Callable

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, ElementClickInterceptedException
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains

from .login import DayuLogin

# 大鱼号平台常量
DAYU_PUBLISH_URL = "https://mp.dayu.com/dashboard/content/publish"  # 大鱼号视频发布页面
DAYU_DRAFT_URL = "https://mp.dayu.com/dashboard/content/draft"      # 大鱼号草稿箱页面
DAYU_HOME_URL = "https://mp.dayu.com/dashboard/index"               # 大鱼号首页

# 大鱼号存稿操作XPath常量
DAYU_MENU_HOVER = '//*[@id="menu"]/div/div[1]/a'                                    # 悬停菜单元素
DAYU_VIDEO_UPLOAD_MENU = '//*[@id="menu"]/div/div[2]/div/div[2]/a'                  # 视频上传菜单
DAYU_UPLOAD_VIDEO_BUTTON = '/html/body/div[1]/div[3]/div/div[2]/div/div/div/div/div/div[2]/div'  # 上传视频按钮
DAYU_CATEGORY_SELECT = '/html/body/div[1]/div[3]/div/div[2]/div/div/div[1]/div[5]/div[1]/div/div/div[1]'  # 选择视频分类
DAYU_CATEGORY_OPTION = '/html/body/div[1]/div[3]/div/div[2]/div/div/div[1]/div[5]/div[1]/div/div/div[2]/a[2]'  # 分类选项
DAYU_COVER_HOVER = '//*[@id="coverImg"]/div[1]'                                     # 封面悬停区域
DAYU_COVER_SAVE = '/html/body/div[4]/div/div[2]/div/div[2]/div[3]/div[2]/button'    # 保存封面按钮
DAYU_SOURCE_FICTION = '/html/body/div[1]/div[3]/div/div[2]/div/div/div[1]/div[8]/div[1]/div[1]/label[4]/span[1]/input'  # 虚构演绎选项
DAYU_SAVE_DRAFT = '/html/body/div[1]/div[3]/div/div[2]/div/div/div[2]/div/div[3]/button[4]'  # 保存草稿按钮

class DayuDraftProcessor:
    """大鱼号平台存稿处理类"""

    # 平台标识符 - 用于模块识别和配置加载
    PLATFORM_ID = "dayu"
    PLATFORM_NAME = "大鱼号平台"

    def __init__(self, account_dir: str, processed_dir: str, processed_covers_dir: str,
                 archive_completed: bool = True, headless_mode: bool = False,
                 draft_limit: int = 0, loop_limit: int = 0, log_callback: Callable = None,
                 screenshots_dir: str = "", random_video_allocation: bool = True,
                 add_draft_detail_callback: Callable = None):
        """
        初始化大鱼号存稿处理类

        Args:
            account_dir: 账号目录
            processed_dir: 已处理视频目录
            processed_covers_dir: 已处理封面目录
            archive_completed: 是否归档已完成的视频
            headless_mode: 是否使用无头模式
            draft_limit: 每个账号的存稿限制数量，0表示不限制
            loop_limit: 循环次数限制，0表示不限制
            log_callback: 日志回调函数
            screenshots_dir: 截图保存目录
            random_video_allocation: 是否随机分配视频
            add_draft_detail_callback: 添加存稿详情的回调函数
        """
        self.account_dir = account_dir
        self.processed_dir = processed_dir
        self.processed_covers_dir = processed_covers_dir
        self.screenshots_dir = screenshots_dir
        self.log_callback = log_callback
        self.add_draft_detail_callback = add_draft_detail_callback

        # 运行标志
        self.is_running = False

        # 运行选项
        self.headless_mode = headless_mode
        self.archive_completed = archive_completed
        self.draft_limit = draft_limit
        self.loop_limit = loop_limit
        self.random_video_allocation = random_video_allocation

        # 存稿成功数量
        self.successful_drafts = 0

        # 设置平台标识
        self.platform_id = self.PLATFORM_ID
        self.platform_name = self.PLATFORM_NAME

        # 创建登录实例
        self.login = DayuLogin(log_callback)

        # 浏览器驱动
        self.driver = None

    def log(self, message: str, internal: bool = False) -> None:
        """
        记录日志

        Args:
            message: 日志消息
            internal: 是否为内部日志（内部日志会添加平台标记，但不显示给用户）
        """
        if internal:
            # 内部日志添加平台标记，用于调试和模块识别
            formatted_message = f"[{self.platform_id}] {message}"
        else:
            # 用户日志不显示平台标记
            formatted_message = message

        if self.log_callback:
            self.log_callback(formatted_message)
        else:
            print(formatted_message)

    def _close_driver_with_port_release(self, driver):
        """关闭驱动并释放端口"""
        if driver:
            try:
                # 释放端口
                if hasattr(driver, '_assigned_port'):
                    try:
                        from 网易号存稿.browser.driver import PortManager
                    except ImportError:
                        # 如果相对导入失败，尝试绝对导入
                        import sys
                        import os
                        sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
                        from browser.driver import PortManager
                    PortManager.release_port(driver._assigned_port)
                    self.log(f"已释放端口: {driver._assigned_port}", internal=True)

                driver.quit()
            except Exception as e:
                self.log(f"关闭驱动时发生错误: {str(e)}", internal=True)

    def start(self, account: str = None, accounts: List[str] = None) -> Tuple[bool, List[Dict[str, Any]]]:
        """
        开始存稿处理

        Args:
            account: 单个账号名称
            accounts: 多个账号名称列表

        Returns:
            处理结果和处理的视频信息列表
        """
        # 设置运行标志
        self.is_running = True

        # 重置存稿成功数量
        self.successful_drafts = 0

        # 处理的视频信息列表
        processed_videos = []

        try:
            # 确定要处理的账号
            accounts_to_process = []
            if account:
                accounts_to_process = [account]
            elif accounts:
                accounts_to_process = accounts

            if not accounts_to_process:
                self.log("没有指定要处理的账号")
                self.is_running = False
                return False, processed_videos

            # 处理每个账号
            for account in accounts_to_process:
                if not self.is_running:
                    self.log("任务已被停止，中断处理")
                    break

                self.log(f"开始处理账号: {account}")

                # 处理单个账号
                success, account_videos = self.process_account(account)

                # 添加到处理过的视频信息列表
                processed_videos.extend(account_videos)

                if success:
                    self.log(f"账号 {account} 处理完成")
                else:
                    self.log(f"账号 {account} 处理失败")

            return True, processed_videos

        except Exception as e:
            self.log(f"存稿处理异常: {str(e)}")
            traceback.print_exc()
            return False, processed_videos

        finally:
            # 重置运行标志
            self.is_running = False

    def process_account(self, account: str) -> Tuple[bool, List[Dict[str, Any]]]:
        """
        处理单个账号

        Args:
            account: 账号名称

        Returns:
            处理结果和处理的视频信息列表
        """
        # 处理的视频信息列表
        processed_videos = []

        try:
            # 获取账号Cookie路径 - 支持多种格式
            cookie_path = self._find_cookie_file(account)
            if not cookie_path:
                self.log(f"未找到账号 {account} 的Cookie文件")
                return False, processed_videos

            # 使用Cookie登录
            self.log(f"正在使用Cookie登录账号: {account}")
            success, driver = self.login.login_with_cookies(cookie_path, self.headless_mode)

            # 如果Cookie登录失败，尝试使用手机号登录
            if not success:
                self.log(f"Cookie登录失败，尝试使用手机号登录: {account}")
                login_success, driver, cookies = self.login.login_with_phone(self.headless_mode)

                if login_success and cookies:
                    self.log(f"手机号登录成功，保存Cookie: {account}")

                    # 保存Cookie为账号名.txt格式
                    cookie_path = os.path.join(self.account_dir, f"{account}.txt")
                    try:
                        import json
                        with open(cookie_path, "w", encoding='utf-8') as f:
                            json.dump(cookies, f, ensure_ascii=False, indent=4)
                        self.log(f"已保存Cookie到: {cookie_path}")
                    except Exception as e:
                        self.log(f"保存Cookie失败: {str(e)}")
                        self.log(f"已保存Cookie到: {cookie_path}")

                    success = True
                else:
                    self.log(f"手机号登录失败: {account}")
                    return False, processed_videos

            # 如果登录成功，开始处理视频
            if success and driver:
                self.driver = driver
                self.log(f"登录成功，开始处理视频: {account}")

                # 获取视频文件列表
                video_files = self.get_video_files()

                if not video_files:
                    self.log("未找到视频文件，请检查视频目录")
                    # 释放端口并关闭驱动
                    self._close_driver_with_port_release(driver)
                    return False, processed_videos

                self.log(f"找到 {len(video_files)} 个视频文件")

                # 如果启用随机分配，随机打乱视频文件顺序
                if self.random_video_allocation:
                    random.shuffle(video_files)
                    self.log(f"已随机打乱 {len(video_files)} 个视频文件的顺序")

                # 处理视频 - 实现大鱼号平台的具体存稿流程
                # 循环处理视频
                loop_count = 0
                while self.is_running:
                    # 检查循环限制
                    if self.loop_limit > 0 and loop_count >= self.loop_limit:
                        self.log(f"已达到循环限制 {self.loop_limit}，停止处理")
                        break

                    # 检查存稿限制
                    if self.draft_limit > 0 and self.successful_drafts >= self.draft_limit:
                        self.log(f"已达到存稿限制 {self.draft_limit}，停止处理")
                        break

                    # 处理一个视频
                    if video_files:
                        video_path = video_files.pop(0)
                        self.log(f"开始处理视频: {os.path.basename(video_path)}")

                        # 获取对应的封面文件
                        cover_path = self.get_cover_file(video_path)
                        if cover_path:
                            self.log(f"找到对应封面: {os.path.basename(cover_path)}")
                        else:
                            self.log("未找到对应封面文件")

                        # 执行大鱼号存稿流程
                        success = self.draft_video(video_path, cover_path)

                        # 添加到处理过的视频信息列表
                        video_info = {
                            "视频": os.path.basename(video_path),
                            "账号": account,
                            "平台": "大鱼号",
                            "状态": "成功" if success else "失败",
                            "时间": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        }
                        processed_videos.append(video_info)

                        if success:
                            # 增加存稿成功数量
                            self.successful_drafts += 1
                            self.log(f"✅ 视频存稿成功: {os.path.basename(video_path)}")

                            # 归档成功的视频
                            if self.archive_completed:
                                self.archive_video(video_path, cover_path, True)
                        else:
                            self.log(f"❌ 视频存稿失败: {os.path.basename(video_path)}")

                            # 归档失败的视频
                            if self.archive_completed:
                                self.archive_video(video_path, cover_path, False)

                        # 添加存稿详情
                        if self.add_draft_detail_callback:
                            self.add_draft_detail_callback(account, video_info)

                        # 等待一段时间再处理下一个视频
                        time.sleep(2)
                    else:
                        self.log("所有视频已处理完毕")
                        break

                    # 增加循环计数
                    loop_count += 1

                # 关闭浏览器并释放端口
                self._close_driver_with_port_release(driver)
                self.driver = None

                return True, processed_videos
            else:
                self.log(f"登录失败: {account}")
                return False, processed_videos

        except Exception as e:
            self.log(f"账号 {account} 处理异常: {str(e)}")
            traceback.print_exc()

            # 关闭浏览器并释放端口
            if self.driver:
                try:
                    self._close_driver_with_port_release(self.driver)
                except:
                    pass
                self.driver = None

            return False, processed_videos

    def stop(self) -> None:
        """停止存稿处理"""
        self.is_running = False

        # 关闭浏览器并释放端口
        if self.driver:
            try:
                self._close_driver_with_port_release(self.driver)
            except:
                pass
            self.driver = None

    def get_video_files(self) -> List[str]:
        """
        获取视频文件列表

        Returns:
            视频文件路径列表
        """
        # 支持的视频格式
        video_extensions = [".mp4", ".avi", ".mov", ".flv", ".wmv", ".mkv"]

        # 获取视频目录下的所有文件
        video_files = []
        for ext in video_extensions:
            video_files.extend(glob.glob(os.path.join(self.processed_dir, f"*{ext}")))

        # 按文件名排序
        video_files.sort()

        return video_files

    def get_cover_file(self, video_path: str) -> Optional[str]:
        """
        获取视频对应的封面文件

        Args:
            video_path: 视频文件路径

        Returns:
            封面文件路径，如果不存在则返回None
        """
        # 获取视频文件名（不含扩展名）
        video_name = os.path.splitext(os.path.basename(video_path))[0]

        # 支持的图片格式
        image_extensions = [".jpg", ".jpeg", ".png", ".bmp"]

        # 查找对应的封面文件
        for ext in image_extensions:
            cover_path = os.path.join(self.processed_covers_dir, f"{video_name}{ext}")
            if os.path.exists(cover_path):
                return cover_path

        # 如果没有找到对应的封面文件，返回None
        return None

    def archive_video(self, video_path: str, cover_path: Optional[str] = None, success: bool = True) -> None:
        """
        归档视频和封面文件

        Args:
            video_path: 视频文件路径
            cover_path: 封面文件路径
            success: 是否成功存稿
        """
        if not self.archive_completed:
            return

        try:
            # 确定目标目录
            target_dir = os.path.join(self.processed_dir, "已处理")
            if not success:
                target_dir = os.path.join(self.processed_dir, "失败")

            # 确保目标目录存在
            os.makedirs(target_dir, exist_ok=True)

            # 移动视频文件
            video_name = os.path.basename(video_path)
            target_video_path = os.path.join(target_dir, video_name)
            shutil.move(video_path, target_video_path)

            # 如果有封面文件，也移动封面文件
            if cover_path and os.path.exists(cover_path):
                cover_name = os.path.basename(cover_path)
                target_cover_dir = os.path.join(target_dir, "封面")
                os.makedirs(target_cover_dir, exist_ok=True)
                target_cover_path = os.path.join(target_cover_dir, cover_name)
                shutil.move(cover_path, target_cover_path)

            self.log(f"已归档{'成功' if success else '失败'}的视频: {video_name}")

        except Exception as e:
            self.log(f"归档视频时出错: {str(e)}")
            traceback.print_exc()

    def _find_cookie_file(self, account: str) -> Optional[str]:
        """
        查找账号的Cookie文件，只支持账号名.txt格式

        Args:
            account: 账号名称

        Returns:
            Cookie文件路径，如果未找到则返回None
        """
        # 只查找账号名.txt格式
        cookie_path = os.path.join(self.account_dir, f"{account}.txt")
        if os.path.exists(cookie_path):
            return cookie_path

        self.log(f"未找到账号 {account} 的Cookie文件")
        return None

    def draft_video(self, video_path: str, cover_path: Optional[str] = None) -> bool:
        """
        执行大鱼号视频存稿流程

        Args:
            video_path: 视频文件路径
            cover_path: 封面文件路径（可选）

        Returns:
            是否存稿成功
        """
        try:
            if not self.driver:
                self.log("浏览器驱动未初始化")
                return False

            # 验证登录状态
            if not self._verify_login():
                self.log("登录状态验证失败")
                return False

            # 导航到视频上传页面
            if not self._navigate_to_upload_page():
                self.log("导航到上传页面失败")
                return False

            # 上传视频文件
            if not self._upload_video_file(video_path):
                self.log("上传视频文件失败")
                return False

            # 等待视频上传完成
            if not self._wait_for_video_upload():
                self.log("等待视频上传超时")
                return False

            # 设置视频分类
            if not self._set_video_category():
                self.log("设置视频分类失败")
                # 继续处理，分类不是必须的

            # 上传封面（如果有）
            if cover_path and os.path.exists(cover_path):
                if not self._upload_cover(cover_path):
                    self.log("上传封面失败")
                    # 继续处理，封面不是必须的

            # 设置信息来源为虚构演绎
            if not self._set_source_fiction():
                self.log("设置信息来源失败")
                # 继续处理，可能已有默认设置

            # 保存草稿
            if not self._save_draft():
                self.log("保存草稿失败")
                return False

            self.log("大鱼号视频存稿成功")
            return True

        except Exception as e:
            self.log(f"大鱼号视频存稿过程中发生错误: {str(e)}")
            traceback.print_exc()
            return False

    def _verify_login(self) -> bool:
        """
        验证登录状态 - 检查URL是否改变进入到后台

        Returns:
            是否已登录
        """
        try:
            # 先获取当前URL
            current_url = self.driver.current_url
            self.log(f"当前页面URL: {current_url}", internal=True)

            # 如果当前不在登录页面，说明可能已经登录
            if "login" not in current_url.lower():
                self.log("当前不在登录页面，可能已登录", internal=True)

                # 尝试访问后台页面进一步验证
                try:
                    self.driver.get(DAYU_HOME_URL)
                    time.sleep(3)

                    # 检查访问后台页面后的URL
                    final_url = self.driver.current_url
                    self.log(f"访问后台页面后的URL: {final_url}", internal=True)

                    # 检查是否成功进入后台（URL改变且不是登录页面）
                    if ("dashboard" in final_url or
                        ("mp.dayu.com" in final_url and "login" not in final_url.lower())):
                        self.log("登录状态验证成功", internal=True)
                        return True
                    else:
                        self.log(f"无法访问后台页面，登录验证失败: {final_url}", internal=True)
                        return False

                except Exception as e:
                    self.log(f"访问后台页面时出错: {str(e)}", internal=True)
                    return False
            else:
                # 如果仍在登录页面，尝试访问后台看是否会自动跳转
                try:
                    self.driver.get(DAYU_HOME_URL)
                    time.sleep(3)

                    final_url = self.driver.current_url
                    self.log(f"从登录页面访问后台后的URL: {final_url}", internal=True)

                    # 检查是否成功进入后台
                    if ("dashboard" in final_url or
                        ("mp.dayu.com" in final_url and "login" not in final_url.lower())):
                        self.log("登录状态验证成功", internal=True)
                        return True
                    else:
                        self.log(f"登录状态验证失败，仍无法访问后台: {final_url}", internal=True)
                        return False

                except Exception as e:
                    self.log(f"验证登录状态时发生错误: {str(e)}", internal=True)
                    return False

        except Exception as e:
            self.log(f"验证登录状态时发生错误: {str(e)}", internal=True)
            return False

    def _navigate_to_upload_page(self) -> bool:
        """
        导航到视频上传页面

        Returns:
            是否成功导航
        """
        try:
            self.log("开始导航到视频上传页面")

            # 悬停在菜单元素上1秒
            self.log("悬停在菜单元素上...")
            menu_element = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, DAYU_MENU_HOVER))
            )

            # 使用ActionChains悬停
            actions = ActionChains(self.driver)
            actions.move_to_element(menu_element).perform()
            time.sleep(1)  # 悬停1秒

            # 点击视频上传菜单
            self.log("点击视频上传菜单...")
            upload_menu = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, DAYU_VIDEO_UPLOAD_MENU))
            )
            upload_menu.click()
            time.sleep(3)  # 等待页面加载

            self.log("成功导航到视频上传页面")
            return True

        except Exception as e:
            self.log(f"导航到上传页面时发生错误: {str(e)}")
            traceback.print_exc()
            return False

    def _upload_video_file(self, video_path: str) -> bool:
        """
        上传视频文件

        Args:
            video_path: 视频文件路径

        Returns:
            是否上传成功
        """
        try:
            self.log(f"开始上传视频文件: {os.path.basename(video_path)}")

            # 点击上传视频按钮
            upload_button = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, DAYU_UPLOAD_VIDEO_BUTTON))
            )
            upload_button.click()
            time.sleep(2)

            # 查找文件输入元素并上传文件
            # 尝试多种可能的文件输入元素
            file_input_selectors = [
                'input[type="file"]',
                'input[accept*="video"]',
                'input[accept*=".mp4"]'
            ]

            file_uploaded = False
            for selector in file_input_selectors:
                try:
                    file_input = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if file_input.is_displayed() or True:  # 即使不可见也尝试上传
                        file_input.send_keys(video_path)
                        self.log(f"通过选择器 {selector} 成功上传文件")
                        file_uploaded = True
                        break
                except:
                    continue

            if not file_uploaded:
                self.log("未找到合适的文件输入元素")
                return False

            self.log("视频文件上传请求已发送")
            return True

        except Exception as e:
            self.log(f"上传视频文件时发生错误: {str(e)}")
            traceback.print_exc()
            return False

    def _wait_for_video_upload(self) -> bool:
        """
        等待视频上传完成

        Returns:
            是否上传完成
        """
        try:
            self.log("等待视频上传完成...")

            # 等待最多5分钟
            max_wait_time = 300
            start_time = time.time()

            while time.time() - start_time < max_wait_time:
                try:
                    # 检查是否有上传进度或完成标识
                    # 这里可能需要根据实际页面调整检测逻辑

                    # 简单等待策略：等待一定时间后认为上传完成
                    # 实际使用时可能需要检查特定的元素或状态
                    time.sleep(10)  # 等待10秒

                    self.log("视频上传完成")
                    return True

                except Exception as e:
                    self.log(f"检查上传状态时出错: {str(e)}")
                    time.sleep(5)
                    continue

            self.log("等待视频上传超时")
            return False

        except Exception as e:
            self.log(f"等待视频上传时发生错误: {str(e)}")
            return False

    def _set_video_category(self) -> bool:
        """
        设置视频分类

        Returns:
            是否设置成功
        """
        try:
            self.log("开始设置视频分类...")

            # 点击选择视频分类
            category_select = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, DAYU_CATEGORY_SELECT))
            )
            category_select.click()
            time.sleep(1)

            # 选择具体分类选项
            category_option = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, DAYU_CATEGORY_OPTION))
            )
            category_option.click()
            time.sleep(1)

            self.log("视频分类设置完成")
            return True

        except Exception as e:
            self.log(f"设置视频分类时发生错误: {str(e)}")
            traceback.print_exc()
            return False

    def _upload_cover(self, cover_path: str) -> bool:
        """
        上传视频封面

        Args:
            cover_path: 封面文件路径

        Returns:
            是否上传成功
        """
        try:
            self.log(f"开始上传视频封面: {os.path.basename(cover_path)}")

            # 滚动到页面底部
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(1)

            # 悬停在封面区域
            cover_hover_element = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, DAYU_COVER_HOVER))
            )

            actions = ActionChains(self.driver)
            actions.move_to_element(cover_hover_element).perform()
            time.sleep(1)

            # 通过文本查找"从本地选择"并点击
            local_select_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '从本地选择')]")
            if local_select_elements:
                local_select_elements[0].click()
                time.sleep(2)

                # 查找文件输入元素并上传封面
                file_input_selectors = [
                    'input[type="file"]',
                    'input[accept*="image"]',
                    'input[accept*=".jpg"]',
                    'input[accept*=".png"]'
                ]

                file_uploaded = False
                for selector in file_input_selectors:
                    try:
                        file_inputs = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        for file_input in file_inputs:
                            try:
                                file_input.send_keys(cover_path)
                                self.log(f"通过选择器 {selector} 成功上传封面")
                                file_uploaded = True
                                break
                            except:
                                continue
                        if file_uploaded:
                            break
                    except:
                        continue

                if file_uploaded:
                    # 等待封面处理完成，然后点击保存
                    time.sleep(3)

                    try:
                        save_button = WebDriverWait(self.driver, 10).until(
                            EC.element_to_be_clickable((By.XPATH, DAYU_COVER_SAVE))
                        )
                        save_button.click()
                        time.sleep(2)
                        self.log("封面保存成功")
                        return True
                    except:
                        self.log("点击封面保存按钮失败")
                        return False
                else:
                    self.log("未找到合适的封面文件输入元素")
                    return False
            else:
                self.log("未找到'从本地选择'按钮")
                return False

        except Exception as e:
            self.log(f"上传封面时发生错误: {str(e)}")
            traceback.print_exc()
            return False

    def _set_source_fiction(self) -> bool:
        """
        设置信息来源为虚构演绎

        Returns:
            是否设置成功
        """
        try:
            self.log("设置信息来源为虚构演绎...")

            # 点击虚构演绎选项
            fiction_option = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, DAYU_SOURCE_FICTION))
            )
            fiction_option.click()
            time.sleep(1)

            self.log("信息来源设置完成")
            return True

        except Exception as e:
            self.log(f"设置信息来源时发生错误: {str(e)}")
            traceback.print_exc()
            return False

    def _save_draft(self) -> bool:
        """
        保存草稿

        Returns:
            是否保存成功
        """
        try:
            self.log("保存草稿...")

            # 点击保存草稿按钮
            save_draft_button = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, DAYU_SAVE_DRAFT))
            )
            save_draft_button.click()
            time.sleep(3)  # 等待保存完成

            self.log("草稿保存成功")
            return True

        except Exception as e:
            self.log(f"保存草稿时发生错误: {str(e)}")
            traceback.print_exc()
            return False


class DayuDataQuery:
    """大鱼号平台数据查询类"""

    # 平台标识符 - 用于模块识别和配置加载
    PLATFORM_ID = "dayu"
    PLATFORM_NAME = "大鱼号平台"

    def __init__(self, account_dir: str, log_callback: Callable = None, headless: bool = True):
        """
        初始化大鱼号数据查询类

        Args:
            account_dir: 账号目录
            log_callback: 日志回调函数
            headless: 是否使用无头模式，默认为True
        """
        self.account_dir = account_dir
        self.log_callback = log_callback
        self.headless = headless

        # 设置平台标识
        self.platform_id = self.PLATFORM_ID
        self.platform_name = self.PLATFORM_NAME

        # 创建登录实例
        self.login = DayuLogin(log_callback)

    def log(self, message: str, internal: bool = False) -> None:
        """
        记录日志

        Args:
            message: 日志消息
            internal: 是否为内部日志（内部日志会添加平台标记，但不显示给用户）
        """
        if internal:
            # 内部日志添加平台标记，用于调试和模块识别
            formatted_message = f"[{self.platform_id}] {message}"
        else:
            # 用户日志不显示平台标记
            formatted_message = message

        if self.log_callback:
            self.log_callback(formatted_message)
        else:
            print(formatted_message)

    def query_account_data(self, account: str) -> Dict[str, Any]:
        """
        查询账号数据

        Args:
            account: 账号名称

        Returns:
            账号数据字典
        """
        # TODO: 实现大鱼号平台的数据查询逻辑
        # 这里只是一个基本框架，实际实现需要根据大鱼号平台的具体页面结构来编写

        self.log(f"开始查询大鱼号账号数据: {account}")

        # 返回示例数据结构
        return {
            "账号": account,
            "平台": "大鱼号",
            "用户名": "",
            "总收益": "0.00",
            "昨日收益": "0.00",
            "七日收益": "0.00",
            "总播放": "0",
            "昨日播放": "0",
            "总粉丝": "0",
            "昨日粉丝": "0",
            "草稿数": "0",
            "总提现": "0.00",
            "待提现": "0.00",
            "更新时间": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "状态": "待实现"
        }
