{"timestamp": "2025-07-16T18:08:07.253930", "diagnosis_results": {"1. 日志模块导入检查": {"status": "success", "details": ["✅ logger模块导入成功", "✅ logger全局实例存在", "   logger类型: <PERSON><PERSON>", "   运行状态: 运行中", "✅ 日志级别定义: ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']", "✅ 日志颜色定义: 9种颜色"]}, "2. 日志系统初始化检查": {"status": "warning", "details": ["✅ 日志文件输出: 启用", "✅ 日志目录: logs", "✅ 最小日志级别: 20", "✅ 级别过滤: 禁用", "✅ 关键词过滤: 禁用", "✅ 日志线程: 运行中", "✅ 日志文件已打开", "⚠️ UI回调未设置"]}, "3. 日志管理器集成检查": {"status": "success", "details": ["✅ 主UI初始化成功", "✅ 日志管理器已初始化", "   类型: LogManager", "   ✅ log_text: NoneType", "   ✅ enhanced_log_display: EnhancedLogDisplay", "   ✅ log_queue: {'message': '🎨 主题切换到: light', 'level': 'INFO', 'timestamp': '18:08:05'}", "   ✅ enhanced_display: True", "   ✅ auto_scroll: True", "   ✅ 方法 log: 存在", "   ✅ 方法 create_log_tab: 存在", "   ✅ 方法 start_log_handler: 存在"]}, "4. 日志显示界面检查": {"status": "success", "details": ["✅ 日志标签页创建成功", "✅ 使用增强日志显示模式", "✅ 增强日志显示组件已创建", "✅ 日志容器子组件数量: 1"]}, "5. 日志回调机制检查": {"status": "warning", "details": ["✅ logger UI回调已设置", "   回调函数: <bound method LogManager._enhanced_log_callback of <网易号存稿.ui.managers.log_manager.LogManager object at 0x0000022C23A832D0>>", "⚠️ UI回调未指向日志管理器", "✅ INFO级别消息发送成功", "✅ WARNING级别消息发送成功", "✅ ERROR级别消息发送成功"]}, "6. 日志输出功能检查": {"status": "warning", "details": ["✅ DEBUG级别日志输出成功", "✅ INFO级别日志输出成功", "✅ WARNING级别日志输出成功", "✅ ERROR级别日志输出成功", "✅ SUCCESS级别日志输出成功", "✅ 时间戳格式化测试成功", "✅ 日志队列大小: 19", "⚠️ 日志处理线程未运行"]}, "7. 日志配置状态检查": {"status": "success", "details": ["✅ enable_level_filter: False", "✅ enable_keyword_filter: False", "✅ min_log_level: INFO", "✅ simple_log_mode: False", "✅ auto_scroll: True", "✅ enhanced_log_display: True", "✅ 过滤关键词: 0个", "   ✅ 管理器enable_level_filter: False", "   ✅ 管理器enable_keyword_filter: False", "   ✅ 管理器min_log_level: INFO", "   ✅ 管理器simple_log_mode: False", "   ✅ 管理器auto_scroll: True", "   ✅ 管理器enhanced_display: True"]}, "8. 增强日志显示检查": {"status": "warning", "details": ["✅ EnhancedLogDisplay模块导入成功", "✅ 增强日志显示模式已启用", "✅ 增强日志显示组件已创建", "   组件类型: EnhancedLogDisplay", "   ✅ 方法 add_log: 存在", "   ✅ 方法 clear_logs: 存在", "   ✅ 方法 set_theme: 存在", "⚠️ 增强日志显示功能测试失败: invalid command name \".!notebook.!frame.!frame.!frame.!frame2.!text\""]}}, "system_info": {"python_version": "3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]", "tkinter_version": 8.6, "platform": "win32"}}