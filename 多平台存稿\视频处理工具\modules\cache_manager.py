"""
缓存管理器 - 用于缓存视频元数据和哈希值，提高处理性能
"""

import os
import json
import hashlib
import time
from typing import Dict, Any, Optional
from pathlib import Path


class VideoCache:
    """视频处理缓存管理器"""
    
    def __init__(self, cache_dir: str = None):
        """
        初始化缓存管理器
        
        Args:
            cache_dir: 缓存目录路径，默认为程序目录下的.cache
        """
        if cache_dir is None:
            cache_dir = os.path.join(os.path.dirname(__file__), '..', '.cache')
        
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        
        # 缓存文件路径
        self.metadata_cache_file = self.cache_dir / 'video_metadata.json'
        self.hash_cache_file = self.cache_dir / 'video_hashes.json'
        
        # 内存缓存
        self._metadata_cache = {}
        self._hash_cache = {}
        
        # 加载现有缓存
        self._load_caches()
    
    def _load_caches(self):
        """从磁盘加载缓存"""
        try:
            # 加载元数据缓存
            if self.metadata_cache_file.exists():
                with open(self.metadata_cache_file, 'r', encoding='utf-8') as f:
                    self._metadata_cache = json.load(f)
            
            # 加载哈希缓存
            if self.hash_cache_file.exists():
                with open(self.hash_cache_file, 'r', encoding='utf-8') as f:
                    self._hash_cache = json.load(f)
                    
        except Exception as e:
            print(f"加载缓存失败: {e}")
            self._metadata_cache = {}
            self._hash_cache = {}
    
    def _save_caches(self):
        """保存缓存到磁盘"""
        try:
            # 保存元数据缓存
            with open(self.metadata_cache_file, 'w', encoding='utf-8') as f:
                json.dump(self._metadata_cache, f, ensure_ascii=False, indent=2)
            
            # 保存哈希缓存
            with open(self.hash_cache_file, 'w', encoding='utf-8') as f:
                json.dump(self._hash_cache, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存缓存失败: {e}")
    
    def _get_file_key(self, video_path: str) -> str:
        """
        生成文件的缓存键
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            str: 缓存键
        """
        try:
            stat = os.stat(video_path)
            # 使用文件路径、大小和修改时间生成唯一键
            key_data = f"{video_path}_{stat.st_size}_{stat.st_mtime}"
            return hashlib.md5(key_data.encode()).hexdigest()
        except:
            # 如果获取文件信息失败，使用文件路径作为键
            return hashlib.md5(video_path.encode()).hexdigest()
    
    def get_video_metadata(self, video_path: str) -> Optional[Dict[str, Any]]:
        """
        获取视频元数据缓存
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            Dict: 视频元数据，如果缓存不存在返回None
        """
        file_key = self._get_file_key(video_path)
        return self._metadata_cache.get(file_key)
    
    def set_video_metadata(self, video_path: str, metadata: Dict[str, Any]):
        """
        设置视频元数据缓存
        
        Args:
            video_path: 视频文件路径
            metadata: 视频元数据
        """
        file_key = self._get_file_key(video_path)
        metadata['cached_time'] = time.time()
        self._metadata_cache[file_key] = metadata
        
        # 定期保存缓存（每10个条目保存一次）
        if len(self._metadata_cache) % 10 == 0:
            self._save_caches()
    
    def get_video_hash(self, video_path: str) -> Optional[str]:
        """
        获取视频哈希缓存
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            str: 视频哈希值，如果缓存不存在返回None
        """
        file_key = self._get_file_key(video_path)
        cache_entry = self._hash_cache.get(file_key)
        if cache_entry:
            return cache_entry.get('hash')
        return None
    
    def set_video_hash(self, video_path: str, video_hash: str):
        """
        设置视频哈希缓存
        
        Args:
            video_path: 视频文件路径
            video_hash: 视频哈希值
        """
        file_key = self._get_file_key(video_path)
        self._hash_cache[file_key] = {
            'hash': video_hash,
            'cached_time': time.time(),
            'file_path': video_path
        }
        
        # 定期保存缓存
        if len(self._hash_cache) % 10 == 0:
            self._save_caches()
    
    def cleanup_old_cache(self, max_age_days: int = 30):
        """
        清理过期的缓存条目
        
        Args:
            max_age_days: 最大缓存天数
        """
        current_time = time.time()
        max_age_seconds = max_age_days * 24 * 3600
        
        # 清理元数据缓存
        expired_keys = []
        for key, metadata in self._metadata_cache.items():
            cached_time = metadata.get('cached_time', 0)
            if current_time - cached_time > max_age_seconds:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self._metadata_cache[key]
        
        # 清理哈希缓存
        expired_keys = []
        for key, cache_entry in self._hash_cache.items():
            cached_time = cache_entry.get('cached_time', 0)
            if current_time - cached_time > max_age_seconds:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self._hash_cache[key]
        
        if expired_keys:
            print(f"清理了 {len(expired_keys)} 个过期缓存条目")
            self._save_caches()
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns:
            Dict: 缓存统计信息
        """
        return {
            'metadata_entries': len(self._metadata_cache),
            'hash_entries': len(self._hash_cache),
            'cache_dir': str(self.cache_dir),
            'metadata_file_size': self.metadata_cache_file.stat().st_size if self.metadata_cache_file.exists() else 0,
            'hash_file_size': self.hash_cache_file.stat().st_size if self.hash_cache_file.exists() else 0
        }
    
    def clear_cache(self):
        """清空所有缓存"""
        self._metadata_cache.clear()
        self._hash_cache.clear()
        
        # 删除缓存文件
        try:
            if self.metadata_cache_file.exists():
                self.metadata_cache_file.unlink()
            if self.hash_cache_file.exists():
                self.hash_cache_file.unlink()
        except Exception as e:
            print(f"删除缓存文件失败: {e}")
    
    def save(self):
        """手动保存缓存"""
        self._save_caches()
    
    def __del__(self):
        """析构函数，确保缓存被保存"""
        try:
            # 在析构时，某些内置函数可能已经不可用，所以需要检查
            if hasattr(__builtins__, 'open') or 'open' in dir(__builtins__):
                self._save_caches()
        except:
            # 静默处理所有异常，避免析构时出错
            pass


# 全局缓存实例
_global_cache = None

def get_video_cache() -> VideoCache:
    """获取全局视频缓存实例"""
    global _global_cache
    if _global_cache is None:
        _global_cache = VideoCache()
    return _global_cache
