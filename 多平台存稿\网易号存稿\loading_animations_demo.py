""" 动画演示程序
展示所有类型的动画效果，包括基础动画、创意动画和高级创意动画
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox
import traceback
import threading
import time

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入动画模块
try:
    # 高级创意动画
    from 网易号存稿.ui.components.advanced_animations import (
        DNAHelixAnimation,
        GalaxyAnimation,
        MatrixRainAnimation,
        EnergyPulseAnimation,
        AudioWaveAnimation
    )
    
    # 创意动画
    from 网易号存稿.ui.components.creative_animations import (
        ParticleFlowAnimation,
        LiquidWaveAnimation,
        NeonGlowAnimation,
        GeometricMorphAnimation,
        TypewriterAnimation
    )
    
    # 基础动画
    from 网易号存稿.ui.components.loading_animations import (
        SpinnerAnimation,
        PulseAnimation,
        ProgressBarAnimation,
        TextFadeAnimation,
        BouncingDotsAnimation
    )
    
    ANIMATIONS_AVAILABLE = True
    print("✅ 所有动画模块导入成功")
    
except ImportError as e:
    print(f"❌ 动画模块导入失败: {e}")
    print("请确保项目结构正确，动画模块文件存在")
    ANIMATIONS_AVAILABLE = False


class AnimationDemoApp:
    """动画演示应用程序"""
    
    def __init__(self):
        """初始化演示应用"""
        self.root = tk.Tk()
        self.root.title("🎬 网易号存稿工具 - 动画演示程序")
        self.root.geometry("1400x900")
        self.root.configure(bg="#1a1a2e")
        self.root.resizable(True, True)

        # 设置窗口图标和样式
        self._setup_window_style()

        # 当前动画实例
        self.current_animation = None
        self.animation_thread = None

        # 动画配置
        self.animation_configs = self._init_animation_configs()

        # 创建现代化界面
        self._create_modern_ui()

        # 居中窗口
        self._center_window()

    def _setup_window_style(self):
        """设置窗口样式"""
        # 设置窗口最小尺寸
        self.root.minsize(1200, 800)

        # 配置ttk样式
        self.style = ttk.Style()
        self.style.theme_use('clam')

        # 自定义样式
        self.style.configure('Modern.TFrame', background='#16213e')
        self.style.configure('Dark.TFrame', background='#0f1419')
        self.style.configure('Card.TFrame', background='#ffffff', relief='flat', borderwidth=1)

        self.style.configure('Modern.TLabel',
                           background='#16213e',
                           foreground='#ffffff',
                           font=('Segoe UI', 10))

        self.style.configure('Title.TLabel',
                           background='#1a1a2e',
                           foreground='#ffffff',
                           font=('Segoe UI', 24, 'bold'))

        self.style.configure('Subtitle.TLabel',
                           background='#16213e',
                           foreground='#a8b2d1',
                           font=('Segoe UI', 12))

        self.style.configure('Modern.TCombobox',
                           fieldbackground='#16213e',
                           background='#16213e',
                           foreground='#ffffff',
                           borderwidth=1,
                           relief='solid')

        self.style.configure('Modern.TButton',
                           background='#4c6ef5',
                           foreground='#ffffff',
                           borderwidth=0,
                           focuscolor='none',
                           font=('Segoe UI', 10, 'bold'))

        self.style.map('Modern.TButton',
                      background=[('active', '#364fc7'),
                                ('pressed', '#2b3990')])

        # 配置Listbox样式
        self.root.option_add('*TCombobox*Listbox.selectBackground', '#4c6ef5')
    
    def _init_animation_configs(self):
        """初始化动画配置"""
        return {
            # 高级创意动画
            "高级创意动画": {
                "DNA螺旋": {
                    "class": DNAHelixAnimation if ANIMATIONS_AVAILABLE else None,
                    "params": {
                        "width": 300, "height": 200,
                        "bg_color": "#212529", "fg_color": "#0d6efd",
                        "text": "DNA螺旋动画", "show_text": True,
                        "rotation_speed": 1.2,
                        "strand_colors": ["#0d6efd", "#dc3545"],
                        "base_pair_colors": ["#fd7e14", "#20c997"]
                    },
                    "description": "模拟DNA双螺旋结构旋转的动画，科技感十足"
                },
                "星系旋转": {
                    "class": GalaxyAnimation if ANIMATIONS_AVAILABLE else None,
                    "params": {
                        "width": 300, "height": 200,
                        "bg_color": "#121212", "fg_color": "#6f42c1",
                        "text": "星系旋转动画", "show_text": True,
                        "rotation_speed": 0.5, "star_count": 150,
                        "galaxy_colors": ["#ffffff", "#00ffff", "#ff00ff", "#ffff00"],
                        "has_black_hole": True
                    },
                    "description": "模拟星系旋转的动画，宇宙主题，包含黑洞效果"
                },
                "矩阵雨": {
                    "class": MatrixRainAnimation if ANIMATIONS_AVAILABLE else None,
                    "params": {
                        "width": 300, "height": 200,
                        "bg_color": "#000000", "fg_color": "#00ff00",
                        "text": "矩阵雨动画", "show_text": True,
                        "drop_speed": 1.0, "density": 0.8
                    },
                    "description": "经典矩阵雨效果，黑客风格的数字瀑布"
                },
                "能量脉冲": {
                    "class": EnergyPulseAnimation if ANIMATIONS_AVAILABLE else None,
                    "params": {
                        "width": 300, "height": 200,
                        "bg_color": "#1a1a2e", "fg_color": "#16213e",
                        "text": "能量脉冲动画", "show_text": True,
                        "pulse_speed": 1.5, "pulse_count": 3,
                        "pulse_colors": ["#0d6efd", "#6610f2", "#6f42c1"],
                        "glow_effect": True
                    },
                    "description": "能量波动效果，动感十足的脉冲动画"
                },
                "音频波形": {
                    "class": AudioWaveAnimation if ANIMATIONS_AVAILABLE else None,
                    "params": {
                        "width": 300, "height": 200,
                        "bg_color": "#212529", "fg_color": "#fd7e14",
                        "text": "音频波形动画", "show_text": True,
                        "bar_count": 15, "wave_speed": 1.2,
                        "color_gradient": True,
                        "gradient_colors": ["#0d6efd", "#6f42c1", "#d63384", "#dc3545"]
                    },
                    "description": "音频波形可视化，现代感强的频谱动画"
                }
            },
            
            # 创意动画
            "创意动画": {
                "粒子流": {
                    "class": ParticleFlowAnimation if ANIMATIONS_AVAILABLE else None,
                    "params": {
                        "width": 300, "height": 200,
                        "bg_color": "#1a1a2e", "fg_color": "#0d6efd",
                        "text": "粒子流动画", "show_text": True,
                        "particle_count": 50, "flow_speed": 1.0,
                        "color_scheme": ["#0d6efd", "#6610f2", "#6f42c1"]
                    },
                    "description": "粒子流动效果，流畅自然的粒子运动"
                },
                "液体波浪": {
                    "class": LiquidWaveAnimation if ANIMATIONS_AVAILABLE else None,
                    "params": {
                        "width": 300, "height": 200,
                        "bg_color": "#212529", "fg_color": "#6f42c1",
                        "text": "液体波浪动画", "show_text": True,
                        "wave_count": 3, "wave_height": 12,
                        "wave_speed": 1.5, "fill_level": 0.6,
                        "color_gradient": True
                    },
                    "description": "液体波浪效果，柔和优雅的波动动画"
                },
                "霓虹发光": {
                    "class": NeonGlowAnimation if ANIMATIONS_AVAILABLE else None,
                    "params": {
                        "width": 300, "height": 200,
                        "bg_color": "#0a0a0a", "fg_color": "#ff006e",
                        "text": "霓虹发光动画", "show_text": True,
                        "glow_intensity": 1.5, "rotation_speed": 1.0,
                        "shape": "circle"
                    },
                    "description": "霓虹灯发光效果，炫酷时尚的发光动画"
                },
                "几何变形": {
                    "class": GeometricMorphAnimation if ANIMATIONS_AVAILABLE else None,
                    "params": {
                        "width": 300, "height": 200,
                        "bg_color": "#212529", "fg_color": "#0d6efd",
                        "text": "几何变形动画", "show_text": True,
                        "morph_speed": 1.0, "rotation_speed": 1.0,
                        "shape_count": 3,
                        "color_cycle": ["#0d6efd", "#6610f2", "#6f42c1", "#d63384"]
                    },
                    "description": "几何图形变形和旋转的动画效果"
                },
                "打字机效果": {
                    "class": TypewriterAnimation if ANIMATIONS_AVAILABLE else None,
                    "params": {
                        "width": 300, "height": 200,
                        "bg_color": "#0a0a0a", "fg_color": "#00ff00",
                        "text": "正在加载系统...",
                        "font": ("Courier New", 14, "bold"),
                        "typing_speed": 1.0, "cursor_blink": True,
                        "random_delay": True, "tech_style": True
                    },
                    "description": "模拟打字机逐字显示文本的效果"
                }
            },
            
            # 基础动画
            "基础动画": {
                "旋转圆点": {
                    "class": SpinnerAnimation if ANIMATIONS_AVAILABLE else None,
                    "params": {
                        "width": 300, "height": 200,
                        "bg_color": "#f8f9fa", "fg_color": "#0d6efd",
                        "text": "旋转圆点动画", "show_text": True,
                        "dot_count": 8, "dot_radius": 6,
                        "circle_radius": 30, "speed": 1.0
                    },
                    "description": "经典旋转加载动画，简洁实用"
                },
                "脉冲波动": {
                    "class": PulseAnimation if ANIMATIONS_AVAILABLE else None,
                    "params": {
                        "width": 300, "height": 200,
                        "bg_color": "#f8f9fa", "fg_color": "#0d6efd",
                        "text": "脉冲波动动画", "show_text": True,
                        "min_radius": 10, "max_radius": 50,
                        "pulse_speed": 1.0, "wave_count": 3
                    },
                    "description": "脉冲波动效果，简约现代"
                },
                "进度条": {
                    "class": ProgressBarAnimation if ANIMATIONS_AVAILABLE else None,
                    "params": {
                        "width": 300, "height": 200,
                        "bg_color": "#f8f9fa", "fg_color": "#0d6efd",
                        "text": "进度条动画", "show_text": True,
                        "bar_height": 8, "indeterminate": True,
                        "animation_speed": 1.0
                    },
                    "description": "进度条动画，直观明了"
                },
                "文字渐变": {
                    "class": TextFadeAnimation if ANIMATIONS_AVAILABLE else None,
                    "params": {
                        "width": 300, "height": 200,
                        "bg_color": "#f8f9fa", "fg_color": "#0d6efd",
                        "text": "文字渐变动画",
                        "font": ("微软雅黑", 16, "bold"),
                        "color_cycle": ["#0d6efd", "#6610f2", "#6f42c1", "#d63384"],
                        "fade_speed": 1.0
                    },
                    "description": "文字颜色渐变效果"
                },
                "弹跳圆点": {
                    "class": BouncingDotsAnimation if ANIMATIONS_AVAILABLE else None,
                    "params": {
                        "width": 300, "height": 200,
                        "bg_color": "#f8f9fa", "fg_color": "#0d6efd",
                        "text": "弹跳圆点动画", "show_text": True,
                        "dot_count": 5, "dot_radius": 8,
                        "bounce_height": 20, "bounce_speed": 1.0
                    },
                    "description": "圆点弹跳效果，活泼有趣"
                }
            }
        }
    
    def _create_modern_ui(self):
        """创建现代化用户界面"""
        # 顶部标题栏
        self._create_header()

        # 主内容区域
        main_container = tk.Frame(self.root, bg="#1a1a2e")
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))

        # 左侧控制面板
        self._create_modern_control_panel(main_container)

        # 右侧动画显示区域
        self._create_modern_animation_area(main_container)

        # 底部状态栏
        self._create_modern_status_bar()

    def _create_header(self):
        """创建顶部标题栏"""
        header_frame = tk.Frame(self.root, bg="#16213e", height=100)
        header_frame.pack(fill=tk.X, padx=0, pady=0)
        header_frame.pack_propagate(False)

        # 渐变背景效果（使用Canvas模拟）
        header_canvas = tk.Canvas(header_frame, bg="#16213e", highlightthickness=0)
        header_canvas.pack(fill=tk.BOTH, expand=True)

        # 创建渐变背景
        for i in range(100):
            color_intensity = int(22 + (33-22) * (i/100))  # 从#16213e到#21304e的渐变
            color = f"#{color_intensity:02x}{color_intensity+10:02x}{color_intensity+20:02x}"
            header_canvas.create_rectangle(0, i, 1400, i+1, fill=color, outline=color)

        # 主标题
        header_canvas.create_text(
            700, 35,
            text="🎬 Animation Studio",
            font=("Segoe UI", 28, "bold"),
            fill="#ffffff",
            anchor="center"
        )

        # 副标题
        header_canvas.create_text(
            700, 65,
            text="网易号存稿工具 - 专业动画演示平台",
            font=("Segoe UI", 12),
            fill="#a8b2d1",
            anchor="center"
        )

        # 装饰性元素
        header_canvas.create_oval(50, 25, 90, 65, outline="#4c6ef5", width=2)
        header_canvas.create_text(70, 45, text="🎭", font=("Segoe UI", 20), fill="#4c6ef5")

        header_canvas.create_oval(1310, 25, 1350, 65, outline="#4c6ef5", width=2)
        header_canvas.create_text(1330, 45, text="✨", font=("Segoe UI", 20), fill="#4c6ef5")
    
    def _create_modern_control_panel(self, parent):
        """创建现代化左侧控制面板"""
        # 控制面板容器
        control_container = tk.Frame(parent, bg="#1a1a2e")
        control_container.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 15))
        control_container.configure(width=400)
        control_container.pack_propagate(False)

        # 控制面板卡片
        control_card = tk.Frame(control_container, bg="#16213e", relief=tk.FLAT, bd=0)
        control_card.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

        # 添加圆角效果（使用Canvas模拟）
        self._add_rounded_corners(control_card, "#16213e")

        # 控制面板标题
        title_frame = tk.Frame(control_card, bg="#16213e")
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 15))

        title_icon = tk.Label(
            title_frame,
            text="🎛️",
            font=("Segoe UI", 20),
            fg="#4c6ef5",
            bg="#16213e"
        )
        title_icon.pack(side=tk.LEFT)

        title_label = tk.Label(
            title_frame,
            text="Animation Control",
            font=("Segoe UI", 16, "bold"),
            fg="#ffffff",
            bg="#16213e"
        )
        title_label.pack(side=tk.LEFT, padx=(10, 0))

        # 分隔线
        separator = tk.Frame(control_card, bg="#2a3441", height=1)
        separator.pack(fill=tk.X, padx=20, pady=(0, 15))

        # 创建现代化组件
        self._create_modern_category_selection(control_card)
        self._create_modern_animation_list(control_card)
        self._create_modern_control_buttons(control_card)

    def _add_rounded_corners(self, widget, color):
        """为组件添加圆角效果（简化版）"""
        # 这里可以添加更复杂的圆角实现
        widget.configure(relief=tk.FLAT, bd=1, highlightbackground="#2a3441", highlightthickness=1)
    
    def _create_modern_animation_area(self, parent):
        """创建现代化右侧动画显示区域"""
        # 动画区域容器
        animation_container = tk.Frame(parent, bg="#1a1a2e")
        animation_container.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 动画显示卡片
        self.animation_frame = tk.Frame(animation_container, bg="#0f1419", relief=tk.FLAT, bd=0)
        self.animation_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

        # 添加边框效果
        self.animation_frame.configure(highlightbackground="#2a3441", highlightthickness=1)

        # 动画区域标题栏
        title_bar = tk.Frame(self.animation_frame, bg="#16213e", height=50)
        title_bar.pack(fill=tk.X)
        title_bar.pack_propagate(False)

        title_icon = tk.Label(
            title_bar,
            text="🎭",
            font=("Segoe UI", 16),
            fg="#4c6ef5",
            bg="#16213e"
        )
        title_icon.pack(side=tk.LEFT, padx=(20, 10), pady=15)

        title_label = tk.Label(
            title_bar,
            text="Animation Preview",
            font=("Segoe UI", 14, "bold"),
            fg="#ffffff",
            bg="#16213e"
        )
        title_label.pack(side=tk.LEFT, pady=15)

        # 状态指示器
        self.preview_status = tk.Label(
            title_bar,
            text="● Ready",
            font=("Segoe UI", 10),
            fg="#28a745",
            bg="#16213e"
        )
        self.preview_status.pack(side=tk.RIGHT, padx=(0, 20), pady=15)

        # 动画容器
        self.animation_container = tk.Frame(self.animation_frame, bg="#0f1419")
        self.animation_container.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)

        # 默认提示
        self._create_default_preview()

    def _create_default_preview(self):
        """创建默认预览界面"""
        # 清空容器
        for widget in self.animation_container.winfo_children():
            widget.destroy()

        # 创建居中的提示内容
        preview_frame = tk.Frame(self.animation_container, bg="#0f1419")
        preview_frame.pack(expand=True)

        # 大图标
        icon_label = tk.Label(
            preview_frame,
            text="🎬",
            font=("Segoe UI", 64),
            fg="#4c6ef5",
            bg="#0f1419"
        )
        icon_label.pack(pady=(0, 20))

        # 主提示文字
        main_text = tk.Label(
            preview_frame,
            text="Select an Animation",
            font=("Segoe UI", 20, "bold"),
            fg="#ffffff",
            bg="#0f1419"
        )
        main_text.pack(pady=(0, 10))

        # 副提示文字
        sub_text = tk.Label(
            preview_frame,
            text="Choose from the control panel to preview animations",
            font=("Segoe UI", 12),
            fg="#a8b2d1",
            bg="#0f1419"
        )
        sub_text.pack()

        # 装饰性元素
        dots_frame = tk.Frame(preview_frame, bg="#0f1419")
        dots_frame.pack(pady=(30, 0))

        for i in range(3):
            dot = tk.Label(
                dots_frame,
                text="●",
                font=("Segoe UI", 12),
                fg="#4c6ef5" if i == 1 else "#2a3441",
                bg="#0f1419"
            )
            dot.pack(side=tk.LEFT, padx=5)

    def _create_modern_category_selection(self, parent):
        """创建现代化动画分类选择"""
        category_section = tk.Frame(parent, bg="#16213e")
        category_section.pack(fill=tk.X, padx=20, pady=(0, 20))

        # 分类标签
        category_label = tk.Label(
            category_section,
            text="Category",
            font=("Segoe UI", 12, "bold"),
            fg="#ffffff",
            bg="#16213e"
        )
        category_label.pack(anchor=tk.W, pady=(0, 8))

        # 分类选择框
        self.category_var = tk.StringVar(value="高级创意动画")

        # 创建自定义样式的下拉框
        combo_frame = tk.Frame(category_section, bg="#2a3441", relief=tk.FLAT, bd=1)
        combo_frame.pack(fill=tk.X, pady=(0, 5))

        self.category_combo = ttk.Combobox(
            combo_frame,
            textvariable=self.category_var,
            values=list(self.animation_configs.keys()),
            state="readonly",
            font=("Segoe UI", 10),
            style="Modern.TCombobox"
        )
        self.category_combo.pack(fill=tk.X, padx=2, pady=2)
        self.category_combo.bind("<<ComboboxSelected>>", self._on_category_changed)

        # 分类描述
        category_descriptions = {
            "高级创意动画": "Complex animations with advanced visual effects",
            "创意动画": "Creative animations with artistic flair",
            "基础动画": "Simple and lightweight loading animations"
        }

        self.category_desc = tk.Label(
            category_section,
            text=category_descriptions.get(self.category_var.get(), ""),
            font=("Segoe UI", 9),
            fg="#a8b2d1",
            bg="#16213e",
            wraplength=350,
            justify=tk.LEFT
        )
        self.category_desc.pack(anchor=tk.W, pady=(5, 0))

    def _create_modern_animation_list(self, parent):
        """创建现代化动画列表"""
        list_section = tk.Frame(parent, bg="#16213e")
        list_section.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))

        # 列表标签
        list_label = tk.Label(
            list_section,
            text="Available Animations",
            font=("Segoe UI", 12, "bold"),
            fg="#ffffff",
            bg="#16213e"
        )
        list_label.pack(anchor=tk.W, pady=(0, 10))

        # 列表容器
        list_container = tk.Frame(list_section, bg="#2a3441", relief=tk.FLAT, bd=1)
        list_container.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # 创建自定义样式的列表框
        list_inner = tk.Frame(list_container, bg="#2a3441")
        list_inner.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)

        # 滚动条
        scrollbar = tk.Scrollbar(list_inner, bg="#2a3441", troughcolor="#16213e")
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 列表框
        self.animation_listbox = tk.Listbox(
            list_inner,
            yscrollcommand=scrollbar.set,
            font=("Segoe UI", 10),
            selectmode=tk.SINGLE,
            bg="#2a3441",
            fg="#ffffff",
            selectbackground="#4c6ef5",
            selectforeground="#ffffff",
            activestyle="none",
            relief=tk.FLAT,
            bd=0,
            highlightthickness=0
        )
        self.animation_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=self.animation_listbox.yview)

        # 绑定事件
        self.animation_listbox.bind("<<ListboxSelect>>", self._on_animation_selected)
        self.animation_listbox.bind("<Double-Button-1>", self._on_animation_double_click)

        # 初始化列表
        self._update_animation_list()

        # 动画描述区域
        desc_container = tk.Frame(list_section, bg="#1a1a2e", relief=tk.FLAT, bd=1)
        desc_container.pack(fill=tk.X)

        desc_inner = tk.Frame(desc_container, bg="#1a1a2e")
        desc_inner.pack(fill=tk.X, padx=15, pady=15)

        desc_title = tk.Label(
            desc_inner,
            text="Description",
            font=("Segoe UI", 10, "bold"),
            fg="#ffffff",
            bg="#1a1a2e"
        )
        desc_title.pack(anchor=tk.W, pady=(0, 5))

        self.description_label = tk.Label(
            desc_inner,
            text="Select an animation to view its description",
            font=("Segoe UI", 9),
            fg="#a8b2d1",
            bg="#1a1a2e",
            wraplength=350,
            justify=tk.LEFT
        )
        self.description_label.pack(anchor=tk.W)

    def _create_modern_control_buttons(self, parent):
        """创建现代化控制按钮"""
        button_section = tk.Frame(parent, bg="#16213e")
        button_section.pack(fill=tk.X, padx=20, pady=(0, 20))

        # 按钮容器
        button_container = tk.Frame(button_section, bg="#16213e")
        button_container.pack(fill=tk.X)

        # 播放按钮
        play_frame = tk.Frame(button_container, bg="#28a745", relief=tk.FLAT, bd=0)
        play_frame.pack(fill=tk.X, pady=(0, 8))

        self.play_button = tk.Button(
            play_frame,
            text="▶  Play Animation",
            font=("Segoe UI", 11, "bold"),
            bg="#28a745",
            fg="white",
            relief=tk.FLAT,
            bd=0,
            command=self._play_animation,
            cursor="hand2",
            activebackground="#218838",
            activeforeground="white"
        )
        self.play_button.pack(fill=tk.X, padx=2, pady=2)

        # 停止按钮
        stop_frame = tk.Frame(button_container, bg="#dc3545", relief=tk.FLAT, bd=0)
        stop_frame.pack(fill=tk.X, pady=(0, 8))

        self.stop_button = tk.Button(
            stop_frame,
            text="⏹  Stop Animation",
            font=("Segoe UI", 11, "bold"),
            bg="#dc3545",
            fg="white",
            relief=tk.FLAT,
            bd=0,
            command=self._stop_animation,
            state=tk.DISABLED,
            cursor="hand2",
            activebackground="#c82333",
            activeforeground="white"
        )
        self.stop_button.pack(fill=tk.X, padx=2, pady=2)

        # 重新播放按钮
        restart_frame = tk.Frame(button_container, bg="#17a2b8", relief=tk.FLAT, bd=0)
        restart_frame.pack(fill=tk.X)

        self.restart_button = tk.Button(
            restart_frame,
            text="🔄  Restart Animation",
            font=("Segoe UI", 11, "bold"),
            bg="#17a2b8",
            fg="white",
            relief=tk.FLAT,
            bd=0,
            command=self._restart_animation,
            state=tk.DISABLED,
            cursor="hand2",
            activebackground="#138496",
            activeforeground="white"
        )
        self.restart_button.pack(fill=tk.X, padx=2, pady=2)

    def _create_modern_status_bar(self):
        """创建现代化底部状态栏"""
        self.status_frame = tk.Frame(self.root, bg="#0f1419", height=40)
        self.status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        self.status_frame.pack_propagate(False)

        # 添加顶部边框
        border = tk.Frame(self.status_frame, bg="#2a3441", height=1)
        border.pack(fill=tk.X)

        # 状态内容容器
        status_content = tk.Frame(self.status_frame, bg="#0f1419")
        status_content.pack(fill=tk.BOTH, expand=True, padx=20, pady=8)

        # 左侧状态信息
        status_left = tk.Frame(status_content, bg="#0f1419")
        status_left.pack(side=tk.LEFT, fill=tk.Y)

        self.status_label = tk.Label(
            status_left,
            text="🚀 Animation Studio Ready | Select an animation to preview",
            font=("Segoe UI", 9),
            fg="#a8b2d1",
            bg="#0f1419"
        )
        self.status_label.pack(side=tk.LEFT, pady=2)

        # 右侧状态指示器
        status_right = tk.Frame(status_content, bg="#0f1419")
        status_right.pack(side=tk.RIGHT, fill=tk.Y)

        # 动画状态
        self.status_indicator = tk.Label(
            status_right,
            text="● Standby",
            font=("Segoe UI", 9, "bold"),
            fg="#28a745",
            bg="#0f1419"
        )
        self.status_indicator.pack(side=tk.RIGHT, padx=(20, 0), pady=2)

        # 分隔符
        separator = tk.Label(
            status_right,
            text="|",
            font=("Segoe UI", 9),
            fg="#2a3441",
            bg="#0f1419"
        )
        separator.pack(side=tk.RIGHT, padx=10, pady=2)

        # 动画计数
        self.animation_count = tk.Label(
            status_right,
            text=f"Total: {sum(len(anims) for anims in self.animation_configs.values())} animations",
            font=("Segoe UI", 9),
            fg="#6c757d",
            bg="#0f1419"
        )
        self.animation_count.pack(side=tk.RIGHT, pady=2)

    def _center_window(self):
        """居中窗口"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def _on_category_changed(self, _event=None):
        """分类改变事件"""
        self._update_animation_list()
        self._clear_animation_display()

        # 更新分类描述
        category_descriptions = {
            "高级创意动画": "Complex animations with advanced visual effects",
            "创意动画": "Creative animations with artistic flair",
            "基础动画": "Simple and lightweight loading animations"
        }
        self.category_desc.config(text=category_descriptions.get(self.category_var.get(), ""))

        self._update_status(f"Category switched to: {self.category_var.get()}")

    def _update_animation_list(self):
        """更新动画列表"""
        self.animation_listbox.delete(0, tk.END)
        category = self.category_var.get()

        if category in self.animation_configs:
            for animation_name in self.animation_configs[category].keys():
                self.animation_listbox.insert(tk.END, animation_name)

    def _on_animation_selected(self, _event=None):
        """动画选择事件"""
        selection = self.animation_listbox.curselection()
        if selection:
            animation_name = self.animation_listbox.get(selection[0])
            category = self.category_var.get()

            if category in self.animation_configs and animation_name in self.animation_configs[category]:
                config = self.animation_configs[category][animation_name]
                self.description_label.config(text=config["description"])
                self._update_status(f"Selected: {animation_name}")

    def _on_animation_double_click(self, _event=None):
        """动画双击事件 - 直接播放"""
        self._play_animation()

    def _play_animation(self):
        """播放动画"""
        if not ANIMATIONS_AVAILABLE:
            messagebox.showerror("错误", "动画模块不可用，请检查项目结构和依赖")
            return

        selection = self.animation_listbox.curselection()
        if not selection:
            messagebox.showwarning("提示", "请先选择要播放的动画")
            return

        animation_name = self.animation_listbox.get(selection[0])
        category = self.category_var.get()

        try:
            # 停止当前动画
            self._stop_animation()

            # 获取动画配置
            config = self.animation_configs[category][animation_name]
            animation_class = config["class"]

            if animation_class is None:
                messagebox.showerror("错误", f"动画类 {animation_name} 不可用")
                return

            # 清除默认预览界面
            for widget in self.animation_container.winfo_children():
                widget.destroy()

            # 创建动画实例
            self.current_animation = animation_class(
                self.animation_container,
                **config["params"]
            )

            # 显示动画
            self.current_animation.pack(fill=tk.BOTH, expand=True)

            # 启动动画
            self.current_animation.start()

            # 更新按钮状态
            self.play_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            self.restart_button.config(state=tk.NORMAL)

            # 更新状态
            self._update_status(f"Playing: {animation_name}")
            self.status_indicator.config(text="● Playing", fg="#dc3545")
            self.preview_status.config(text="● Playing", fg="#dc3545")

        except Exception as e:
            error_msg = f"播放动画失败: {str(e)}"
            print(f"错误详情: {traceback.format_exc()}")
            messagebox.showerror("错误", error_msg)
            self._update_status(f"Failed to play: {animation_name}")

    def _stop_animation(self):
        """停止动画"""
        if self.current_animation:
            try:
                self.current_animation.stop()
                self.current_animation.destroy()
            except Exception as e:
                print(f"停止动画时出错: {e}")
            finally:
                self.current_animation = None

        # 更新按钮状态
        self.play_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.restart_button.config(state=tk.DISABLED)

        # 更新状态
        self._update_status("Animation stopped")
        self.status_indicator.config(text="● Standby", fg="#28a745")
        self.preview_status.config(text="● Ready", fg="#28a745")

    def _restart_animation(self):
        """重新播放动画"""
        self._stop_animation()
        time.sleep(0.1)  # 短暂延迟确保清理完成
        self._play_animation()

    def _clear_animation_display(self):
        """清除动画显示区域"""
        self._stop_animation()

        # 重新显示默认预览界面
        self._create_default_preview()

    def _update_status(self, message):
        """更新状态栏"""
        self.status_label.config(text=f"🚀 {message}")
        self.root.update_idletasks()

    def run(self):
        """运行演示程序"""
        if not ANIMATIONS_AVAILABLE:
            messagebox.showerror(
                "错误",
                "动画模块导入失败！\n\n请检查：\n"
                "1. 项目结构是否正确\n"
                "2. 动画模块文件是否存在\n"
                "3. Python路径是否正确设置"
            )
            return

        try:
            self._update_status("Animation Studio launched successfully")
            self.root.mainloop()
        except Exception as e:
            print(f"程序运行出错: {e}")
            print(traceback.format_exc())
        finally:
            # 清理资源
            if self.current_animation:
                try:
                    self.current_animation.stop()
                    self.current_animation.destroy()
                except:
                    pass


def main():
    """主函数"""
    print("🚀 启动动画演示程序...")

    try:
        app = AnimationDemoApp()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {e}")
        print(traceback.format_exc())
        messagebox.showerror("启动失败", f"程序启动失败:\n{str(e)}")


if __name__ == "__main__":
    main()
