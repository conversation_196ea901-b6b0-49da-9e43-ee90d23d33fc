"""
存稿设置对话框模块 - 美化版本
"""

import tkinter as tk
from tkinter import ttk
from tkinter import messagebox

class DraftSettingsDialog:
    """存稿设置对话框类"""

    def __init__(self, parent, draft_limit, loop_limit, concurrent_accounts, random_video_allocation, headless_mode, archive_completed, query_headless_mode=None, query_max_threads=None, auto_close=None, concurrent_mode=None, max_workers=None, config_manager=None, current_platform=None):
        """
        初始化存稿设置对话框

        Args:
            parent: 父窗口
            draft_limit: 存稿数量限制变量
            loop_limit: 循环次数限制变量
            concurrent_accounts: 并发账号数量变量
            random_video_allocation: 视频分配方式变量
            headless_mode: 无头模式变量
            archive_completed: 归档视频变量
            query_headless_mode: 数据查询无头模式变量
            query_max_threads: 数据查询最大线程数变量
            config_manager: 配置管理器
            current_platform: 当前平台
        """
        self.parent = parent
        self.draft_limit = draft_limit
        self.loop_limit = loop_limit
        self.concurrent_accounts = concurrent_accounts
        self.random_video_allocation = random_video_allocation
        self.headless_mode = headless_mode
        self.archive_completed = archive_completed
        self.query_headless_mode = query_headless_mode
        self.query_max_threads = query_max_threads
        self.auto_close = auto_close
        self.concurrent_mode = concurrent_mode
        self.max_workers = max_workers
        self.config_manager = config_manager
        self.current_platform = current_platform

        # 创建临时变量，用于保存对话框中的设置
        self.temp_draft_limit = tk.IntVar(value=draft_limit.get())
        self.temp_loop_limit = tk.IntVar(value=loop_limit.get())
        self.temp_concurrent_accounts = tk.IntVar(value=concurrent_accounts.get())
        self.temp_random_video_allocation = tk.BooleanVar(value=random_video_allocation.get())
        self.temp_headless_mode = tk.BooleanVar(value=headless_mode.get())
        self.temp_archive_completed = tk.BooleanVar(value=archive_completed.get())

        # 数据查询相关的临时变量
        if query_headless_mode is not None:
            self.temp_query_headless_mode = tk.BooleanVar(value=query_headless_mode.get())
        else:
            self.temp_query_headless_mode = tk.BooleanVar(value=False)

        if query_max_threads is not None:
            self.temp_query_max_threads = tk.IntVar(value=query_max_threads.get())
        else:
            self.temp_query_max_threads = tk.IntVar(value=10)

        # 浏览器运行相关的临时变量
        if auto_close is not None:
            self.temp_auto_close = tk.BooleanVar(value=auto_close.get())
        else:
            self.temp_auto_close = tk.BooleanVar(value=True)

        if concurrent_mode is not None:
            self.temp_concurrent_mode = tk.BooleanVar(value=concurrent_mode.get())
        else:
            self.temp_concurrent_mode = tk.BooleanVar(value=False)

        if max_workers is not None:
            self.temp_max_workers = tk.IntVar(value=max_workers.get())
        else:
            self.temp_max_workers = tk.IntVar(value=3)

        # 添加变量跟踪标志
        self.has_changes = False

        # 计算居中位置
        width = 800
        height = 700
        screen_width = parent.winfo_screenwidth()
        screen_height = parent.winfo_screenheight()
        x = (screen_width // 2) - (width // 2)
        y = (screen_height // 2) - (height // 2)

        # 创建对话框并直接设置位置（避免先显示在左上角）
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("存稿设置")
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")  # 直接设置大小和位置
        self.dialog.resizable(True, True)  # 允许调整大小
        self.dialog.minsize(800, 700)  # 设置最小大小
        self.dialog.transient(parent)  # 设置为模态窗口
        self.dialog.grab_set()  # 阻止用户与其他窗口交互

        # 设置对话框背景色
        self.dialog.configure(bg='#f0f0f0')

        # 设置窗口图标
        try:
            self.dialog.iconbitmap(parent.iconbitmap())
        except:
            pass

        # 配置样式
        self.setup_styles()

        # 创建UI
        self.create_ui()

        # 确保窗口在最前面并获得焦点
        self.dialog.lift()
        self.dialog.focus_set()

    def setup_styles(self):
        """设置界面样式"""
        self.style = ttk.Style()

        # 配置标题样式
        self.style.configure("Title.TLabel",
                           font=("微软雅黑", 18, "bold"),
                           foreground="#2c3e50",
                           background="#f0f0f0")

        # 配置分组标题样式
        self.style.configure("GroupTitle.TLabel",
                           font=("微软雅黑", 12, "bold"),
                           foreground="#34495e",
                           background="#ffffff")

        # 配置普通标签样式
        self.style.configure("Normal.TLabel",
                           font=("微软雅黑", 10),
                           foreground="#2c3e50",
                           background="#ffffff")

        # 配置提示标签样式
        self.style.configure("Hint.TLabel",
                           font=("微软雅黑", 9),
                           foreground="#7f8c8d",
                           background="#ffffff")

        # 配置主要按钮样式
        self.style.configure("Primary.TButton",
                           font=("微软雅黑", 11, "bold"),
                           padding=(20, 10))

        # 配置次要按钮样式
        self.style.configure("Secondary.TButton",
                           font=("微软雅黑", 11),
                           padding=(20, 10))

        # 配置框架样式
        self.style.configure("Card.TFrame",
                           background="#ffffff",
                           relief="flat",
                           borderwidth=0)

        # 配置LabelFrame样式
        self.style.configure("Card.TLabelframe",
                           background="#ffffff",
                           relief="solid",
                           borderwidth=1,
                           lightcolor="#e8e8e8",
                           darkcolor="#e8e8e8")

        self.style.configure("Card.TLabelframe.Label",
                           font=("微软雅黑", 9, "bold"),  # 统一使用系统默认字体大小
                           foreground="#2c3e50",
                           background="#ffffff")

        # 配置Spinbox样式 - 统一使用系统默认字体大小
        self.style.configure("Custom.TSpinbox",
                           font=("微软雅黑", 9),  # 统一使用系统默认字体大小
                           fieldbackground="#ffffff",
                           borderwidth=1,
                           relief="solid")

        # 配置Checkbutton样式 - 统一使用系统默认字体大小
        self.style.configure("Custom.TCheckbutton",
                           font=("微软雅黑", 9),  # 统一使用系统默认字体大小
                           background="#ffffff",
                           focuscolor="none")

        # 配置Radiobutton样式 - 统一使用系统默认字体大小
        self.style.configure("Custom.TRadiobutton",
                           font=("微软雅黑", 9),  # 统一使用系统默认字体大小
                           background="#ffffff",
                           focuscolor="none")

    def create_ui(self):
        """创建用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建标题
        title_label = ttk.Label(main_frame, text="📝 存稿任务设置", style="Title.TLabel")
        title_label.pack(pady=(0, 20))

        # 创建内容区域框架（用于滚动内容和按钮的分离）
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)

        # 创建滚动区域
        scroll_container = ttk.Frame(content_frame)
        scroll_container.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # 创建Canvas（删除Scrollbar）
        canvas = tk.Canvas(scroll_container, bg='#f0f0f0', highlightthickness=0, height=480)
        scrollable_frame = ttk.Frame(canvas)

        # 配置滚动区域
        def configure_scroll_region(event=None):
            canvas.configure(scrollregion=canvas.bbox("all"))
            # 确保canvas宽度与内容匹配
            canvas_width = canvas.winfo_width()
            canvas.itemconfig(canvas_window, width=canvas_width)

        scrollable_frame.bind("<Configure>", configure_scroll_region)
        canvas.bind("<Configure>", configure_scroll_region)

        # 创建滚动窗口
        canvas_window = canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")

        # 布局Canvas（删除Scrollbar布局）
        canvas.pack(fill="both", expand=True)

        # 绑定鼠标滚轮事件
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        def bind_mousewheel(event):
            canvas.bind_all("<MouseWheel>", _on_mousewheel)

        def unbind_mousewheel(event):
            canvas.unbind_all("<MouseWheel>")

        canvas.bind('<Enter>', bind_mousewheel)
        canvas.bind('<Leave>', unbind_mousewheel)

        # 创建设置卡片
        self.create_basic_settings_card(scrollable_frame)
        self.create_runtime_settings_card(scrollable_frame)
        self.create_query_settings_card(scrollable_frame)

        # 创建按钮区域（固定在底部）
        self.create_button_area(content_frame)

    def create_basic_settings_card(self, parent):
        """创建基本设置卡片"""
        # 创建区域容器
        card_container = ttk.Frame(parent)
        card_container.pack(fill=tk.X, pady=(0, 25), padx=10)

        # 区域标题
        title_label = ttk.Label(
            card_container,
            text="⚙️ 基本设置",
            font=("微软雅黑", 14, "bold"),
            foreground="#2E86AB"
        )
        title_label.pack(anchor=tk.W, pady=(0, 15))

        # 基本设置内容容器
        basic_card = ttk.Frame(card_container)
        basic_card.pack(fill=tk.BOTH, expand=True, padx=(20, 0))

        # 存稿数量设置
        draft_frame = ttk.Frame(basic_card)
        draft_frame.pack(fill=tk.X, pady=8)

        ttk.Label(draft_frame, text="📝 存稿数量:", style="Normal.TLabel", width=18).pack(side=tk.LEFT)
        draft_spinbox = ttk.Spinbox(draft_frame, from_=0, to=1000, textvariable=self.temp_draft_limit, width=12, style="Custom.TSpinbox")
        draft_spinbox.pack(side=tk.LEFT, padx=(0, 10))
        ttk.Label(draft_frame, text="(0表示不限制)", style="Hint.TLabel").pack(side=tk.LEFT)

        # 循环次数设置
        loop_frame = ttk.Frame(basic_card)
        loop_frame.pack(fill=tk.X, pady=8)

        ttk.Label(loop_frame, text="🔄 循环次数:", style="Normal.TLabel", width=18).pack(side=tk.LEFT)
        loop_spinbox = ttk.Spinbox(loop_frame, from_=0, to=1000, textvariable=self.temp_loop_limit, width=12, style="Custom.TSpinbox")
        loop_spinbox.pack(side=tk.LEFT, padx=(0, 10))
        ttk.Label(loop_frame, text="(0表示不限制)", style="Hint.TLabel").pack(side=tk.LEFT)

        # 并发账号数量设置
        concurrent_frame = ttk.Frame(basic_card)
        concurrent_frame.pack(fill=tk.X, pady=8)

        ttk.Label(concurrent_frame, text="⚡ 并发账号数量:", style="Normal.TLabel", width=18).pack(side=tk.LEFT)
        concurrent_spinbox = ttk.Spinbox(concurrent_frame, from_=1, to=20, textvariable=self.temp_concurrent_accounts, width=12, style="Custom.TSpinbox")
        concurrent_spinbox.pack(side=tk.LEFT, padx=(0, 10))
        ttk.Label(concurrent_frame, text="(推荐1-5)", style="Hint.TLabel").pack(side=tk.LEFT)

        # 视频分配方式设置
        allocation_frame = ttk.Frame(basic_card)
        allocation_frame.pack(fill=tk.X, pady=8)

        ttk.Label(allocation_frame, text="🎯 视频分配方式:", style="Normal.TLabel", width=18).pack(side=tk.LEFT)

        radio_frame = ttk.Frame(allocation_frame)
        radio_frame.pack(side=tk.LEFT)

        ttk.Radiobutton(radio_frame, text="🎲 随机分配", variable=self.temp_random_video_allocation, value=True, style="Custom.TRadiobutton").pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(radio_frame, text="📋 顺序分配", variable=self.temp_random_video_allocation, value=False, style="Custom.TRadiobutton").pack(side=tk.LEFT)

    def create_runtime_settings_card(self, parent):
        """创建运行模式设置卡片"""
        # 创建区域容器
        card_container = ttk.Frame(parent)
        card_container.pack(fill=tk.X, pady=(0, 25), padx=10)

        # 区域标题
        title_label = ttk.Label(
            card_container,
            text="🚀 运行模式",
            font=("微软雅黑", 14, "bold"),
            foreground="#2E86AB"
        )
        title_label.pack(anchor=tk.W, pady=(0, 15))

        # 运行模式内容容器
        runtime_card = ttk.Frame(card_container)
        runtime_card.pack(fill=tk.BOTH, expand=True, padx=(20, 0))

        # 无头模式设置
        headless_frame = ttk.Frame(runtime_card)
        headless_frame.pack(fill=tk.X, pady=8)

        # 导入美化复选框
        from ..styles.checkbox_styles import create_beautiful_checkbox

        headless_check = create_beautiful_checkbox(headless_frame, text="🔒 无头模式（不显示浏览器界面）", variable=self.temp_headless_mode, style_type="modern")
        headless_check.pack(side=tk.LEFT)

        # 归档模式设置
        archive_frame = ttk.Frame(runtime_card)
        archive_frame.pack(fill=tk.X, pady=8)

        archive_check = create_beautiful_checkbox(archive_frame, text="📁 归档视频（不删除原文件）", variable=self.temp_archive_completed, style_type="success")
        archive_check.pack(side=tk.LEFT)

        # 自动关闭浏览器设置
        auto_close_frame = ttk.Frame(runtime_card)
        auto_close_frame.pack(fill=tk.X, pady=8)

        auto_close_check = create_beautiful_checkbox(auto_close_frame, text="🔄 任务完成后自动关闭浏览器", variable=self.temp_auto_close, style_type="info")
        auto_close_check.pack(side=tk.LEFT)

        # 并发模式设置
        concurrent_frame = ttk.Frame(runtime_card)
        concurrent_frame.pack(fill=tk.X, pady=8)

        concurrent_check = create_beautiful_checkbox(concurrent_frame, text="⚡ 启用并发模式（同时处理多个账号）", variable=self.temp_concurrent_mode, style_type="warning")
        concurrent_check.pack(side=tk.LEFT)

        # 最大并发数设置
        workers_frame = ttk.Frame(runtime_card)
        workers_frame.pack(fill=tk.X, pady=8)

        workers_label = ttk.Label(workers_frame, text="🔢 最大并发数:", font=("微软雅黑", 10))
        workers_label.pack(side=tk.LEFT, padx=(0, 10))

        workers_spinbox = ttk.Spinbox(
            workers_frame,
            from_=1,
            to=10,
            textvariable=self.temp_max_workers,
            width=8,
            font=("微软雅黑", 10)
        )
        workers_spinbox.pack(side=tk.LEFT, padx=(0, 10))

        workers_tip = ttk.Label(workers_frame, text="(建议1-5，过高可能导致系统不稳定)", font=("微软雅黑", 9), foreground="#666666")
        workers_tip.pack(side=tk.LEFT)

    def create_query_settings_card(self, parent):
        """创建数据查询设置卡片"""
        # 获取平台名称
        platform_name = "当前平台"
        if self.config_manager and self.current_platform:
            platform_name = self.config_manager.get_platform_name(self.current_platform)

        # 创建区域容器
        card_container = ttk.Frame(parent)
        card_container.pack(fill=tk.X, pady=(0, 25), padx=10)

        # 区域标题
        title_label = ttk.Label(
            card_container,
            text=f"📊 数据查询设置 ({platform_name})",
            font=("微软雅黑", 14, "bold"),
            foreground="#2E86AB"
        )
        title_label.pack(anchor=tk.W, pady=(0, 15))

        # 数据查询设置内容容器
        query_card = ttk.Frame(card_container)
        query_card.pack(fill=tk.BOTH, expand=True, padx=(20, 0))

        # 查询并发数设置
        threads_frame = ttk.Frame(query_card)
        threads_frame.pack(fill=tk.X, pady=8)

        ttk.Label(threads_frame, text="⚡ 查询数据并发数:", style="Normal.TLabel", width=18).pack(side=tk.LEFT)
        threads_spinbox = ttk.Spinbox(threads_frame, from_=1, to=100, textvariable=self.temp_query_max_threads, width=12, style="Custom.TSpinbox")
        threads_spinbox.pack(side=tk.LEFT, padx=(0, 10))
        ttk.Label(threads_frame, text="(建议1-50个并发)", style="Hint.TLabel").pack(side=tk.LEFT)

        # 查询无头模式设置
        query_headless_frame = ttk.Frame(query_card)
        query_headless_frame.pack(fill=tk.X, pady=8)

        query_headless_check = ttk.Checkbutton(query_headless_frame, text="🔒 数据查询使用无头模式（不显示浏览器界面）", variable=self.temp_query_headless_mode, style="Custom.TCheckbutton")
        query_headless_check.pack(side=tk.LEFT)

    def create_button_area(self, parent):
        """创建按钮区域"""
        # 创建按钮框架
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, side=tk.BOTTOM, pady=(20, 0))

        # 按钮容器（居中显示）
        button_container = ttk.Frame(button_frame)
        button_container.pack(expand=True)

        # 取消按钮
        cancel_button = ttk.Button(button_container, text="❌ 取消", command=self.on_cancel, width=15, style="Secondary.TButton")
        cancel_button.pack(side=tk.LEFT, padx=(0, 15))

        # 确定按钮
        ok_button = ttk.Button(button_container, text="✅ 确定", command=self.on_ok, width=15, style="Primary.TButton")
        ok_button.pack(side=tk.LEFT)

        # 设置确定按钮为默认按钮
        self.dialog.bind('<Return>', lambda _: self.on_ok())



    def on_ok(self):
        """确定按钮事件处理"""
        # 验证输入
        try:
            draft_limit = self.temp_draft_limit.get()
            loop_limit = self.temp_loop_limit.get()
            concurrent_accounts = self.temp_concurrent_accounts.get()
            query_max_threads = self.temp_query_max_threads.get()

            if draft_limit < 0:
                self.temp_draft_limit.set(0)

            if loop_limit < 0:
                self.temp_loop_limit.set(0)

            if concurrent_accounts < 1:
                self.temp_concurrent_accounts.set(1)
            elif concurrent_accounts > 20:
                self.temp_concurrent_accounts.set(20)

            if query_max_threads < 1:
                self.temp_query_max_threads.set(1)
            elif query_max_threads > 100:
                self.temp_query_max_threads.set(100)

        except Exception:
            # 如果值不是合法的整数，重置为默认值
            self.temp_draft_limit.set(0)
            self.temp_loop_limit.set(0)
            self.temp_concurrent_accounts.set(1)
            self.temp_query_max_threads.set(10)
            return

        # 记录旧值，用于检测变化
        old_values = {
            "draft_limit": self.draft_limit.get(),
            "loop_limit": self.loop_limit.get(),
            "concurrent_accounts": self.concurrent_accounts.get(),
            "random_video_allocation": self.random_video_allocation.get(),
            "headless_mode": self.headless_mode.get(),
            "archive_completed": self.archive_completed.get()
        }

        # 如果有数据查询配置，也记录旧值
        if self.query_headless_mode is not None:
            old_values["query_headless_mode"] = self.query_headless_mode.get()
        if self.query_max_threads is not None:
            old_values["query_max_threads"] = self.query_max_threads.get()

        # 如果有浏览器运行配置，也记录旧值
        if self.auto_close is not None:
            old_values["auto_close"] = self.auto_close.get()
        if self.concurrent_mode is not None:
            old_values["concurrent_mode"] = self.concurrent_mode.get()
        if self.max_workers is not None:
            old_values["max_workers"] = self.max_workers.get()

        # 更新主窗口中的变量
        self.draft_limit.set(self.temp_draft_limit.get())
        self.loop_limit.set(self.temp_loop_limit.get())
        self.concurrent_accounts.set(self.temp_concurrent_accounts.get())
        self.random_video_allocation.set(self.temp_random_video_allocation.get())
        self.headless_mode.set(self.temp_headless_mode.get())
        self.archive_completed.set(self.temp_archive_completed.get())

        # 更新数据查询配置
        if self.query_headless_mode is not None:
            self.query_headless_mode.set(self.temp_query_headless_mode.get())
        if self.query_max_threads is not None:
            self.query_max_threads.set(self.temp_query_max_threads.get())

        # 更新浏览器运行配置
        if self.auto_close is not None:
            self.auto_close.set(self.temp_auto_close.get())
        if self.concurrent_mode is not None:
            self.concurrent_mode.set(self.temp_concurrent_mode.get())
        if self.max_workers is not None:
            self.max_workers.set(self.temp_max_workers.get())

        # 检测是否有变化
        new_values = {
            "draft_limit": self.draft_limit.get(),
            "loop_limit": self.loop_limit.get(),
            "concurrent_accounts": self.concurrent_accounts.get(),
            "random_video_allocation": self.random_video_allocation.get(),
            "headless_mode": self.headless_mode.get(),
            "archive_completed": self.archive_completed.get()
        }

        # 如果有数据查询配置，也记录新值
        if self.query_headless_mode is not None:
            new_values["query_headless_mode"] = self.query_headless_mode.get()
        if self.query_max_threads is not None:
            new_values["query_max_threads"] = self.query_max_threads.get()

        # 如果有浏览器运行配置，也记录新值
        if self.auto_close is not None:
            new_values["auto_close"] = self.auto_close.get()
        if self.concurrent_mode is not None:
            new_values["concurrent_mode"] = self.concurrent_mode.get()
        if self.max_workers is not None:
            new_values["max_workers"] = self.max_workers.get()

        # 设置对话框结果属性，用于主窗口判断是否有变化
        self.has_changes = any(old_values[key] != new_values[key] for key in old_values)

        # 打印调试信息
        print("存稿设置对话框 - 确定按钮被点击")
        print(f"旧值: {old_values}")
        print(f"新值: {new_values}")
        print(f"是否有变化: {self.has_changes}")

        # 添加确认消息
        if self.has_changes:
            print("设置已更改，将在对话框关闭后保存")
            # 在对话框中显示确认消息
            messagebox.showinfo("设置已更改", "存稿设置已更改，将在关闭对话框后自动保存。")
        else:
            print("设置未更改，但仍将保存配置以确保设置被保存")
            # 设置has_changes为True，确保主窗口会保存配置
            self.has_changes = True

        # 关闭对话框
        self.dialog.destroy()

    def on_cancel(self):
        """取消按钮事件处理"""
        # 关闭对话框
        self.dialog.destroy()
