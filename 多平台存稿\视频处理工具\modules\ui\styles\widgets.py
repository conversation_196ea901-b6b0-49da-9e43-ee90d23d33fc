"""
UI组件样式模块 - 定义自定义组件样式和工具提示
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Any, Callable

def create_tooltip(ui_instance, widget, text):
    """为控件创建现代化工具提示，支持窗口移动时跟随"""
    # 淡入效果
    def fade_in_tooltip(alpha=0.0):
        if hasattr(ui_instance, "tooltip") and ui_instance.tooltip.winfo_exists():
            if alpha < 0.9:
                alpha += 0.1
                ui_instance.tooltip.attributes("-alpha", alpha)
                ui_instance.tooltip.after(20, lambda: fade_in_tooltip(alpha))

    # 淡出效果
    def fade_out_tooltip(alpha=0.9):
        if hasattr(ui_instance, "tooltip") and ui_instance.tooltip.winfo_exists():
            if alpha > 0.1:
                alpha -= 0.1
                ui_instance.tooltip.attributes("-alpha", alpha)
                ui_instance.tooltip.after(20, lambda: fade_out_tooltip(alpha))
            else:
                ui_instance.tooltip.destroy()
                if hasattr(ui_instance, "tooltip"):
                    delattr(ui_instance, "tooltip")

    def update_tooltip_position():
        """更新工具提示位置，使其跟随控件"""
        if hasattr(ui_instance, "tooltip") and ui_instance.tooltip.winfo_exists():
            try:
                # 获取控件当前位置
                x = widget.winfo_rootx() + 25
                y = widget.winfo_rooty() + 25

                # 更新工具提示位置
                ui_instance.tooltip.wm_geometry(f"+{x}+{y}")

                # 继续更新位置，直到工具提示被销毁
                if hasattr(ui_instance, "tooltip") and ui_instance.tooltip.winfo_exists():
                    ui_instance.tooltip.after(100, update_tooltip_position)
            except:
                # 如果出现异常（例如控件已被销毁），则销毁工具提示
                if hasattr(ui_instance, "tooltip"):
                    ui_instance.tooltip.destroy()
                    delattr(ui_instance, "tooltip")

    def enter(_):
        # 如果已经存在工具提示，先销毁它
        if hasattr(ui_instance, "tooltip") and ui_instance.tooltip.winfo_exists():
            ui_instance.tooltip.destroy()

        # 获取控件位置
        x = widget.winfo_rootx() + 25
        y = widget.winfo_rooty() + 25

        # 创建一个顶层窗口
        ui_instance.tooltip = tk.Toplevel(widget)
        ui_instance.tooltip.wm_overrideredirect(True)  # 无边框窗口
        ui_instance.tooltip.wm_geometry(f"+{x}+{y}")

        # 设置工具提示始终在顶层
        ui_instance.tooltip.attributes("-topmost", True)

        # 获取当前主题
        theme_key = "dark" if ui_instance.enable_dark_mode.get() else "light"
        theme = ui_instance.theme_colors[theme_key]

        # 设置工具提示背景色和透明度
        if theme_key == "dark":
            bg_color = "#2d2d2d"
            fg_color = theme["fg"]
        else:
            bg_color = "#f8f9fa"
            fg_color = theme["fg"]

        # 创建框架和标签，添加圆角和阴影效果
        frame = tk.Frame(ui_instance.tooltip,
                       background=bg_color,
                       highlightbackground=theme["border"],
                       highlightthickness=1,
                       padx=2, pady=2)
        frame.pack(fill="both", expand=True)

        # 创建标签
        label = ttk.Label(frame, text=text, justify=tk.LEFT,
                         background=bg_color, foreground=fg_color,
                         padding=(8, 4))
        label.pack()

        # 添加淡入效果
        ui_instance.tooltip.attributes("-alpha", 0.0)
        fade_in_tooltip()

        # 启动位置更新
        update_tooltip_position()

    def leave(_):
        # 销毁工具提示窗口
        if hasattr(ui_instance, "tooltip") and ui_instance.tooltip.winfo_exists():
            fade_out_tooltip()

    # 绑定鼠标进入和离开事件
    widget.bind("<Enter>", enter)
    widget.bind("<Leave>", leave)

    # 绑定窗口移动事件
    ui_instance.root.bind("<Configure>", lambda e: update_tooltip_position() if hasattr(ui_instance, "tooltip") and ui_instance.tooltip.winfo_exists() else None)

def create_modern_button(parent, text, command, icon=None, style="TButton", width=None, tooltip=None, ui_instance=None):
    """创建现代风格按钮"""
    if icon:
        button = ttk.Button(parent, text=text, image=icon, compound=tk.LEFT,
                          command=command, style=style, width=width)
    else:
        button = ttk.Button(parent, text=text, command=command, style=style, width=width)

    if tooltip and ui_instance:
        create_tooltip(ui_instance, button, tooltip)

    return button

def create_modern_entry(parent, textvariable, width=None, tooltip=None, ui_instance=None):
    """创建现代风格输入框"""
    entry = ttk.Entry(parent, textvariable=textvariable, width=width)

    if tooltip and ui_instance:
        create_tooltip(ui_instance, entry, tooltip)

    return entry

def create_modern_combobox(parent, textvariable, values, state="readonly", width=None, tooltip=None, ui_instance=None):
    """创建现代风格下拉框"""
    combo = ttk.Combobox(parent, textvariable=textvariable, values=values, state=state, width=width)

    if tooltip and ui_instance:
        create_tooltip(ui_instance, combo, tooltip)

    return combo

def create_modern_spinbox(parent, from_, to, textvariable, width=None, tooltip=None, ui_instance=None):
    """创建现代风格数字输入框"""
    spinner = ttk.Spinbox(parent, from_=from_, to=to, textvariable=textvariable, width=width)

    if tooltip and ui_instance:
        create_tooltip(ui_instance, spinner, tooltip)

    return spinner

def create_modern_checkbutton(parent, text, variable, tooltip=None, ui_instance=None):
    """创建现代风格复选框"""
    check = ttk.Checkbutton(parent, text=text, variable=variable)

    if tooltip and ui_instance:
        create_tooltip(ui_instance, check, tooltip)

    return check

def create_modern_frame(parent, padding=(10, 10), fill=tk.BOTH, expand=True):
    """创建现代风格框架"""
    frame = ttk.Frame(parent, padding=padding)
    frame.pack(fill=fill, expand=expand)

    return frame

def create_modern_labelframe(parent, text, padding=(10, 10), fill=tk.BOTH, expand=False):
    """创建现代风格标签框架"""
    frame = ttk.LabelFrame(parent, text=text, padding=padding)
    frame.pack(fill=fill, expand=expand, padx=5, pady=5)

    return frame

def create_color_preview(parent, color, size=(20, 20), relief="raised"):
    """创建颜色预览框"""
    canvas = tk.Canvas(parent, width=size[0], height=size[1], bd=1, relief=relief)
    canvas.pack(side=tk.LEFT, padx=5)

    # 绘制颜色
    canvas.create_rectangle(0, 0, size[0], size[1], fill=color, outline="")

    return canvas

def update_color_preview(canvas, color, ui_instance=None):
    """更新颜色预览框"""
    try:
        canvas.delete("all")

        # 确保画布已经被渲染，获取实际大小
        canvas.update_idletasks()
        width = canvas.winfo_width()
        height = canvas.winfo_height()

        # 如果大小为1（未渲染），使用配置的大小
        if width <= 1 or height <= 1:
            width = canvas.winfo_reqwidth()
            height = canvas.winfo_reqheight()

        # 创建颜色矩形
        canvas.create_rectangle(0, 0, width, height, fill=color, outline="")

        # 同时更新画布背景色作为备选
        canvas.configure(bg=color)

        # 如果有颜色名称标签，更新它
        if hasattr(canvas, 'color_name_label'):
            # 使用ui_instance中的color_names
            if ui_instance and hasattr(ui_instance, 'color_names'):
                color_names = ui_instance.color_names
                canvas.color_name_label.config(text=color_names.get(color, ""))
    except Exception as e:
        # 如果更新失败，至少更新背景色
        try:
            canvas.configure(bg=color)
        except:
            pass
