"""
网易号存稿工具 - 主程序入口（带启动动画）
"""

import os
import sys
import tkinter as tk
import traceback
import threading
import time
import json

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入自定义模块
from 网易号存稿.common.config import ConfigManager
from 网易号存稿.common.logger import logger
from 网易号存稿.common.utils import center_window, create_required_dirs
from 网易号存稿.ui.main import NeteaseDraftUI

# 导入启动动画组件
try:
    from 网易号存稿.ui.components.advanced_animations import (
        DNAHelixAnimation,
        GalaxyAnimation,
        MatrixRainAnimation,
        EnergyPulseAnimation,
        AudioWaveAnimation
    )
    from 网易号存稿.ui.components.creative_animations import (
        ParticleFlowAnimation,
        LiquidWaveAnimation,
        NeonGlowAnimation
    )
    from 网易号存稿.ui.components.loading_animations import (
        SpinnerAnimation,
        PulseAnimation,
        ProgressBarAnimation
    )
    ANIMATIONS_AVAILABLE = True
except ImportError as e:
    print(f"警告: 动画组件导入失败: {e}")
    ANIMATIONS_AVAILABLE = False


def load_startup_animation_config():
    """加载启动动画配置"""
    config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "startup_animation_config.json")
    default_config = {
        "enabled": True,
        "animation_type": "audio",
        "duration": 1.5,
        "status_update_interval": 0.4,
        "status_messages": [
            "正在初始化应用程序...",
            "正在加载配置文件...",
            "正在准备用户界面...",
            "正在创建必要目录...",
            "即将完成..."
        ]
    }

    try:
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                # 合并默认配置，确保所有必要的键都存在
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                return config
        else:
            return default_config
    except Exception as e:
        print(f"加载启动动画配置失败: {e}")
        return default_config


class SplashScreen:
    """启动画面类"""

    def __init__(self, animation_type="dna"):
        """
        初始化启动画面

        参数:
            animation_type: 动画类型，可选值为:
                - 高级创意动画: "dna", "galaxy", "matrix", "energy", "audio"
                - 创意动画: "particle", "liquid", "neon"
                - 基础动画: "spinner", "pulse", "progress"
        """
        # 创建根窗口
        self.root = tk.Tk()
        self.root.title("多平台存稿工具")
        self.root.configure(bg="#212529")  # 深色背景
        self.root.overrideredirect(True)  # 无边框窗口

        # 设置窗口大小并居中显示
        window_width = 400
        window_height = 300
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # 创建主框架
        self.main_frame = tk.Frame(self.root, bg="#212529")
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建标题
        tk.Label(
            self.main_frame,
            text="🚀 多平台存稿工具",
            font=("微软雅黑", 18, "bold"),
            fg="#ffffff",
            bg="#212529"
        ).pack(pady=(40, 20))

        # 创建动画容器
        self.animation_container = tk.Frame(self.main_frame, bg="#212529")
        self.animation_container.pack(fill=tk.BOTH, expand=True)

        # 创建状态标签
        self.status_label = tk.Label(
            self.main_frame,
            text="正在启动应用程序...",
            font=("微软雅黑", 10),
            fg="#adb5bd",
            bg="#212529"
        )
        self.status_label.pack(pady=(10, 20))

        # 创建加载动画
        self.animation = None
        if ANIMATIONS_AVAILABLE:
            self.create_animation(animation_type)

        # 应用程序加载标志
        self.app_loaded = False

        # 显示窗口
        self.root.update()

    def create_animation(self, animation_type):
        """创建加载动画"""
        if not ANIMATIONS_AVAILABLE:
            return

        # 清空容器
        for widget in self.animation_container.winfo_children():
            widget.destroy()

        try:
            # 根据类型创建动画
            if animation_type == "dna":
                self.animation = DNAHelixAnimation(
                    self.animation_container,
                    width=200,
                    height=120,
                    bg_color="#212529",
                    fg_color="#0d6efd",
                    text="",
                    show_text=False,
                    rotation_speed=1.2,
                    helix_width=100,
                    helix_height=100,
                    strand_colors=["#0d6efd", "#dc3545"],
                    base_pair_colors=["#fd7e14", "#20c997"]
                )
            elif animation_type == "galaxy":
                self.animation = GalaxyAnimation(
                    self.animation_container,
                    width=200,
                    height=120,
                    bg_color="#212529",
                    fg_color="#6f42c1",
                    text="",
                    show_text=False,
                    rotation_speed=0.5,
                    star_count=150,
                    galaxy_colors=["#ffffff", "#00ffff", "#ff00ff", "#ffff00", "#ff8800"],
                    has_black_hole=True
                )
            elif animation_type == "audio":
                self.animation = AudioWaveAnimation(
                    self.animation_container,
                    width=200,
                    height=120,
                    bg_color="#212529",
                    fg_color="#fd7e14",
                    text="",
                    show_text=False,
                    bar_count=15,
                    wave_speed=1.2,
                    bar_width=6,
                    bar_spacing=3,
                    color_gradient=True,
                    gradient_colors=["#0d6efd", "#6f42c1", "#d63384", "#dc3545"]
                )
            elif animation_type == "liquid":
                self.animation = LiquidWaveAnimation(
                    self.animation_container,
                    width=200,
                    height=120,
                    bg_color="#212529",
                    fg_color="#6f42c1",
                    text="",
                    show_text=False,
                    wave_count=3,
                    wave_height=12,
                    wave_speed=1.5,
                    fill_level=0.6,
                    color_gradient=True
                )
            elif animation_type == "spinner":
                self.animation = SpinnerAnimation(
                    self.animation_container,
                    width=200,
                    height=120,
                    bg_color="#212529",
                    fg_color="#0d6efd",
                    text="",
                    show_text=False
                )
            else:
                # 默认使用DNA螺旋动画
                self.animation = DNAHelixAnimation(
                    self.animation_container,
                    width=200,
                    height=120,
                    bg_color="#212529",
                    fg_color="#0d6efd",
                    text="",
                    show_text=False,
                    rotation_speed=1.2,
                    helix_width=100,
                    helix_height=100,
                    strand_colors=["#0d6efd", "#dc3545"],
                    base_pair_colors=["#fd7e14", "#20c997"]
                )

            if self.animation:
                # 显示动画
                self.animation.pack(fill=tk.BOTH, expand=True)

                # 设置更高的优先级
                self.root.attributes("-topmost", True)
                self.root.update()
                self.root.attributes("-topmost", False)

                # 启动动画
                self.animation.start()

        except Exception as e:
            print(f"创建动画失败: {e}")
            # 如果动画创建失败，显示简单的文本
            tk.Label(
                self.animation_container,
                text="⚡ 正在加载...",
                font=("微软雅黑", 16),
                fg="#0d6efd",
                bg="#212529"
            ).pack(expand=True)

    def update_status(self, text):
        """更新状态文本"""
        if self.status_label.cget("text") != text:
            self.status_label.config(text=text)
            self.root.update_idletasks()

    def close(self):
        """关闭启动画面"""
        try:
            if self.animation and hasattr(self.animation, 'stop'):
                self.animation.stop()
            self.root.quit()
            self.root.destroy()
        except Exception as e:
            print(f"关闭启动画面时出错: {e}")


def load_main_application(duration=1.5):
    """在后台线程中加载主应用程序"""
    global main_app_result, main_app_error

    try:
        # 模拟加载过程，给用户足够时间看到启动动画
        time.sleep(duration)

        # 设置结果
        main_app_result = "ready"
        main_app_error = None

    except Exception as e:
        main_app_result = None
        main_app_error = str(e)


def start_main_application_directly():
    """直接启动主应用程序（无启动动画）"""
    try:
        return create_and_run_main_app()
    except Exception as e:
        # 处理未捕获的异常
        error_message = f"程序发生未捕获的异常: {str(e)}\n{traceback.format_exc()}"
        print(error_message)

        # 显示错误消息框
        try:
            import tkinter.messagebox as messagebox
            messagebox.showerror("错误", f"程序发生错误:\n{str(e)}")
        except:
            pass

        return 1


def create_and_run_main_app():
    """创建并运行主应用程序"""
    # 创建主窗口
    root = tk.Tk()
    root.title("🚀 多平台存稿工具")

    # 设置初始窗口大小
    root.geometry("1330x800")

    # 使用工具函数居中窗口
    center_window(root)

    # 设置窗口图标（如果有）
    icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "icon.ico")
    if os.path.exists(icon_path):
        root.iconbitmap(icon_path)

    # 创建配置管理器 - 使用相对路径
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    config_file = os.path.join(base_dir, "config.json")

    # 确保配置文件目录存在
    config_dir = os.path.dirname(config_file)
    if not os.path.exists(config_dir):
        os.makedirs(config_dir)

    # 创建配置管理器
    config_manager = ConfigManager(config_file, base_dir=base_dir)

    # 设置日志配置文件路径
    log_config_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "log_config.json")

    # 如果日志配置文件存在，则加载配置
    if os.path.exists(log_config_file):
        logger.config_file = log_config_file
        logger.load_config()

    # 启动日志记录器
    logger.start()
    logger.log("🚀 启动多平台存稿工具...")

    # 确保必要目录存在
    create_required_dirs([
        config_manager.get_path("account_dir"),
        config_manager.get_path("video_dir"),
        config_manager.get_path("cover_dir"),
        config_manager.get_path("processed_dir"),
        config_manager.get_path("processed_covers_dir"),
        config_manager.get_path("violation_dir"),
        config_manager.get_path("screenshots_dir")
    ])

    # 创建UI
    app = NeteaseDraftUI(root, config_manager)
    app.debug_mode = config_manager.get("debug_mode", False, platform="common")

    # 设置窗口关闭事件处理 - 直接使用UI类的on_closing方法
    root.protocol("WM_DELETE_WINDOW", app.on_closing)

    # 运行主循环
    root.mainloop()

    # 强制保存所有待保存的配置
    config_manager.flush_pending_changes()

    # 停止日志记录器
    logger.stop()

    return 0


def main():
    """主程序入口函数（带启动动画）"""
    global main_app_result, main_app_error

    # 初始化全局变量
    main_app_result = None
    main_app_error = None

    try:
        # 加载启动动画配置
        animation_config = load_startup_animation_config()

        # 检查是否启用启动动画
        if not animation_config.get("enabled", True) or not ANIMATIONS_AVAILABLE:
            # 如果禁用动画或动画组件不可用，直接启动主程序
            return start_main_application_directly()

        # 获取配置参数
        animation_type = animation_config.get("animation_type", "audio")
        duration = animation_config.get("duration", 1.5)
        status_messages = animation_config.get("status_messages", [
            "正在初始化应用程序...",
            "正在加载配置文件...",
            "正在准备用户界面...",
            "正在创建必要目录...",
            "即将完成..."
        ])
        update_interval = animation_config.get("status_update_interval", 0.4)

        # 创建启动画面
        splash = SplashScreen(animation_type)

        # 启动后台线程加载应用程序
        loading_thread = threading.Thread(target=lambda: load_main_application(duration), daemon=True)
        loading_thread.start()

        # 主循环 - 显示启动动画
        try:
            i = 0
            last_update_time = time.time()

            # 使用事件循环等待加载完成
            while loading_thread.is_alive() and main_app_result is None:
                # 处理所有待处理的事件
                splash.root.update()

                # 只在指定间隔更新状态消息
                current_time = time.time()
                if current_time - last_update_time >= update_interval:
                    splash.update_status(status_messages[i % len(status_messages)])
                    i += 1
                    last_update_time = current_time

                # 短暂休眠，减少CPU使用
                time.sleep(0.01)

            # 等待线程完成
            loading_thread.join(0.5)

            # 显示最终状态
            splash.update_status("启动完成！")
            time.sleep(0.3)  # 让用户看到完成消息

            # 关闭启动画面
            splash.close()

        except Exception as e:
            print(f"启动动画过程中出错: {e}")
            try:
                splash.close()
            except:
                pass

        # 检查是否有加载错误
        if main_app_error:
            raise Exception(main_app_error)

        # 启动动画完成后，创建并运行主应用程序
        return create_and_run_main_app()

    except Exception as e:
        # 处理未捕获的异常
        error_message = f"程序发生未捕获的异常: {str(e)}\n{traceback.format_exc()}"
        print(error_message)

        # 如果日志记录器已启动，记录错误
        try:
            logger.log(error_message, "ERROR")
        except:
            pass

        # 显示错误消息框
        try:
            import tkinter.messagebox as messagebox
            messagebox.showerror("错误", f"程序发生错误:\n{str(e)}")
        except:
            pass

        # 确保日志记录器停止
        try:
            logger.stop()
        except:
            pass

        return 1


# 全局变量，用于线程间通信
main_app_result = None
main_app_error = None

if __name__ == "__main__":
    sys.exit(main())
