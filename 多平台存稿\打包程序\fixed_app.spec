# -*- mode: python ; coding: utf-8 -*-

import os
import sys
from pathlib import Path

# 设置基础路径
base_path = Path(r"C:\Users\<USER>\Downloads\网易\多平台存稿")
app_path = base_path / "run_app.py"

# 数据文件和目录
datas = [
    # 配置文件
    (str(base_path / "config"), "config"),
    (str(base_path / "directory_config.json"), "."),
    
    # 网易号存稿整个目录
    (str(base_path / "网易号存稿"), "网易号存稿"),
    
    # 视频处理工具
    (str(base_path / "视频处理工具"), "视频处理工具"),
    
    # 微动画效果库
    (str(base_path.parent / "微动画效果库"), "微动画效果库"),
    
    # 日志目录
    (str(base_path / "logs"), "logs"),
]

# 完整的隐藏导入列表
hiddenimports = [
    # tkinter完整模块
    'tkinter',
    'tkinter.ttk',
    'tkinter.messagebox',
    'tkinter.simpledialog',
    'tkinter.filedialog',
    'tkinter.scrolledtext',
    'tkinter.font',
    'tkinter.constants',
    'tkinter.dnd',
    'tkinter.colorchooser',
    'tkinter.commondialog',
    
    # selenium完整模块
    'selenium',
    'selenium.webdriver',
    'selenium.webdriver.chrome',
    'selenium.webdriver.chrome.service',
    'selenium.webdriver.chrome.options',
    'selenium.webdriver.common.by',
    'selenium.webdriver.support.ui',
    'selenium.webdriver.support.wait',
    'selenium.webdriver.support.expected_conditions',
    'selenium.webdriver.common.keys',
    'selenium.webdriver.common.action_chains',
    'selenium.common.exceptions',
    
    # PIL完整模块
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
    'PIL.ImageDraw',
    'PIL.ImageFont',
    'PIL.ImageFilter',
    'PIL.ImageEnhance',
    
    # 网络和数据处理
    'requests',
    'requests.adapters',
    'requests.auth',
    'requests.cookies',
    'json',
    'urllib3',
    'certifi',
    
    # 系统和线程
    'threading',
    'queue',
    'subprocess',
    'multiprocessing',
    'concurrent.futures',
    
    # 时间和路径
    'datetime',
    'time',
    'pathlib',
    'os',
    'sys',
    'shutil',
    'tempfile',
    
    # 导入和模块
    'importlib',
    'importlib.util',
    'traceback',
    'logging',
    
    # 应用特定
    'jieba',
    'moviepy',
    'moviepy.editor',
    'pyautogui',
    
    # 数据处理（可选）
    'pandas',
    'openpyxl',
    'numpy',
]

# 排除的模块（减少体积）
excludes = [
    'matplotlib',
    'scipy',
    'jupyter',
    'IPython',
    'notebook',
    'pytest',
    'setuptools',
]

block_cipher = None

a = Analysis(
    [str(app_path)],
    pathex=[str(base_path)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='多平台存稿工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='多平台存稿工具',
)
