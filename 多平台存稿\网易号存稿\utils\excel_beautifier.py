#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel美化器 - 专业的Excel报告生成工具
提供美观的数据展示、图表生成和格式化功能
"""

import os
import sys
import pandas as pd
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
import logging

try:
    from openpyxl import Workbook, load_workbook
    from openpyxl.styles import Font, PatternFill, Border, Side, Alignment, NamedStyle
    from openpyxl.formatting.rule import ColorScaleRule, DataBarRule
    from openpyxl.chart import Bar<PERSON>hart, PieChart, LineChart, Reference
    from openpyxl.chart.series import DataPoint
    from openpyxl.drawing.image import Image
    from openpyxl.utils.dataframe import dataframe_to_rows
    from openpyxl.utils import get_column_letter
    from openpyxl.worksheet.dimensions import ColumnDimension
except ImportError as e:
    print(f"警告: 无法导入openpyxl库: {e}")
    print("请安装openpyxl库: pip install openpyxl")

class ExcelBeautifier:
    """Excel美化器类 - 专业的Excel报告生成工具"""

    def __init__(self):
        """初始化Excel美化器"""
        self.logger = self._setup_logger()

        # 颜色主题配置
        self.colors = {
            'primary': '#1E3A8A',      # 主色调 - 深蓝色
            'secondary': '#3B82F6',    # 次色调 - 蓝色
            'accent': '#93C5FD',       # 强调色 - 浅蓝色
            'success': '#10B981',      # 成功色 - 绿色
            'warning': '#F59E0B',      # 警告色 - 橙色
            'danger': '#EF4444',       # 危险色 - 红色
            'light': '#F8FAFC',        # 浅色背景
            'dark': '#1F2937',         # 深色文字
            'white': '#FFFFFF',        # 白色
            'gray_100': '#F3F4F6',     # 浅灰色
            'gray_200': '#E5E7EB',     # 灰色
            'gray_300': '#D1D5DB',     # 中灰色
        }

        # 字体配置
        self.fonts = {
            'title': {'name': 'Microsoft YaHei UI', 'size': 18, 'bold': True},
            'subtitle': {'name': 'Microsoft YaHei UI', 'size': 14, 'bold': True},
            'header': {'name': 'Microsoft YaHei UI', 'size': 12, 'bold': True},
            'body': {'name': 'Microsoft YaHei UI', 'size': 10, 'bold': False},
            'small': {'name': 'Microsoft YaHei UI', 'size': 9, 'bold': False},
            'chart_title': {'name': 'Microsoft YaHei UI', 'size': 14, 'bold': True},
            'chart_label': {'name': 'Microsoft YaHei UI', 'size': 10, 'bold': False},
        }

        # 边框样式
        self.borders = {
            'thin': Border(
                left=Side(style='thin', color='D1D5DB'),
                right=Side(style='thin', color='D1D5DB'),
                top=Side(style='thin', color='D1D5DB'),
                bottom=Side(style='thin', color='D1D5DB')
            ),
            'medium': Border(
                left=Side(style='medium', color='9CA3AF'),
                right=Side(style='medium', color='9CA3AF'),
                top=Side(style='medium', color='9CA3AF'),
                bottom=Side(style='medium', color='9CA3AF')
            ),
            'thick': Border(
                left=Side(style='thick', color='6B7280'),
                right=Side(style='thick', color='6B7280'),
                top=Side(style='thick', color='6B7280'),
                bottom=Side(style='thick', color='6B7280')
            )
        }

    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('ExcelBeautifier')
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def create_beautiful_excel(self,
                             file_path: str,
                             accounts_data: pd.DataFrame,
                             seven_day_data: Optional[pd.DataFrame] = None,
                             platform: str = "多平台存稿工具",
                             integrated_mode: bool = True,
                             sheet_config: Optional[Dict[str, bool]] = None) -> bool:
        """
        创建美化的Excel报告

        Args:
            file_path: Excel文件保存路径
            accounts_data: 账号数据DataFrame
            seven_day_data: 七天数据DataFrame（可选）
            platform: 平台名称
            integrated_mode: 是否使用集成模式（单页设计）
            sheet_config: 工作表配置

        Returns:
            bool: 是否成功创建
        """
        try:
            self.logger.info(f"开始创建美化Excel报告: {file_path}")

            # 默认配置
            if sheet_config is None:
                sheet_config = {
                    'include_overview': True,
                    'include_details': True,
                    'include_analysis': True,
                    'include_charts': True
                }

            # 创建工作簿
            wb = Workbook()

            if integrated_mode:
                # 集成模式 - 单页设计
                self._create_integrated_report(wb, accounts_data, seven_day_data, platform, sheet_config)
            else:
                # 分页模式 - 多个工作表
                self._create_multi_sheet_report(wb, accounts_data, seven_day_data, platform, sheet_config)

            # 保存文件
            wb.save(file_path)
            self.logger.info(f"Excel报告创建成功: {file_path}")
            return True

        except Exception as e:
            self.logger.error(f"创建Excel报告失败: {e}")
            import traceback
            traceback.print_exc()
            return False

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import logging


class ExcelBeautifier:
    """Excel美化器类，用于生成美化的Excel报告"""

    def __init__(self):
        """初始化Excel美化器"""
        self.logger = logging.getLogger(__name__)

        # 定义现代化颜色主题 - 使用渐变和高对比度配色
        self.colors = {
            # 主色调系列 - 深蓝到浅蓝渐变
            'primary': '#1E3A8A',      # 深蓝色
            'primary_light': '#3B82F6', # 中蓝色
            'primary_lighter': '#93C5FD', # 浅蓝色

            # 次色调系列 - 绿色渐变
            'secondary': '#059669',    # 深绿色
            'secondary_light': '#10B981', # 中绿色
            'secondary_lighter': '#6EE7B7', # 浅绿色

            # 强调色系列 - 橙色渐变
            'accent': '#EA580C',       # 深橙色
            'accent_light': '#F97316', # 中橙色
            'accent_lighter': '#FED7AA', # 浅橙色

            # 状态色系列
            'danger': '#DC2626',       # 红色
            'danger_light': '#EF4444', # 浅红色
            'success': '#16A34A',      # 成功绿
            'success_light': '#22C55E', # 浅成功绿
            'warning': '#D97706',      # 警告橙
            'warning_light': '#F59E0B', # 浅警告橙
            'info': '#0284C7',         # 信息蓝
            'info_light': '#0EA5E9',   # 浅信息蓝

            # 中性色系列
            'light': '#F8FAFC',        # 极浅灰
            'light_gray': '#F1F5F9',   # 浅灰
            'gray': '#64748B',         # 中灰
            'dark_gray': '#334155',    # 深灰
            'dark': '#0F172A',         # 极深色

            # 特殊用途色
            'header': '#1E293B',       # 表头深色
            'border': '#CBD5E1',       # 边框色
            'background': '#FFFFFF',   # 背景白色

            # 图表专用渐变色系
            'chart_gradient_1': ['#1E3A8A', '#3B82F6', '#93C5FD'],  # 蓝色渐变
            'chart_gradient_2': ['#059669', '#10B981', '#6EE7B7'],  # 绿色渐变
            'chart_gradient_3': ['#EA580C', '#F97316', '#FED7AA'],  # 橙色渐变
            'chart_gradient_4': ['#DC2626', '#EF4444', '#FCA5A5'],  # 红色渐变
            'chart_gradient_5': ['#7C3AED', '#A855F7', '#C4B5FD'],  # 紫色渐变
        }

        # 定义现代化字体样式
        self.fonts = {
            'title': {'name': 'Microsoft YaHei UI', 'size': 18, 'bold': True},
            'subtitle': {'name': 'Microsoft YaHei UI', 'size': 14, 'bold': True},
            'header': {'name': 'Microsoft YaHei UI', 'size': 12, 'bold': True},
            'body': {'name': 'Microsoft YaHei UI', 'size': 10, 'bold': False},
            'small': {'name': 'Microsoft YaHei UI', 'size': 9, 'bold': False},
            'chart_title': {'name': 'Microsoft YaHei UI', 'size': 14, 'bold': True},
            'chart_label': {'name': 'Microsoft YaHei UI', 'size': 10, 'bold': False}
        }

        # 图表样式配置
        self.chart_styles = {
            'modern_blue': {
                'colors': ['#1E3A8A', '#3B82F6', '#93C5FD', '#DBEAFE'],
                'style_id': 42
            },
            'modern_green': {
                'colors': ['#059669', '#10B981', '#6EE7B7', '#D1FAE5'],
                'style_id': 43
            },
            'modern_orange': {
                'colors': ['#EA580C', '#F97316', '#FED7AA', '#FEF3C7'],
                'style_id': 44
            },
            'modern_purple': {
                'colors': ['#7C3AED', '#A855F7', '#C4B5FD', '#EDE9FE'],
                'style_id': 45
            }
        }

    def create_beautiful_excel(self, file_path, accounts_data, seven_day_data=None, platform="网易号",
                             integrated_mode=True, sheet_config=None):
        """
        创建美化的Excel报告

        Args:
            file_path: Excel文件保存路径
            accounts_data: 账号数据DataFrame
            seven_day_data: 七天收益数据DataFrame
            platform: 平台名称
            integrated_mode: 是否使用整合模式（单页面），默认True
            sheet_config: 工作表配置字典，包含include_overview, include_details等

        Returns:
            bool: 是否成功创建
        """
        try:
            # 检查必要的库
            try:
                import openpyxl
                from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
                from openpyxl.chart import LineChart, BarChart, PieChart, Reference
                from openpyxl.chart.series import DataPoint
                from openpyxl.utils.dataframe import dataframe_to_rows
                from openpyxl.formatting.rule import ColorScaleRule, DataBarRule
            except ImportError as e:
                self.logger.error(f"缺少必要的库: {e}")
                return False

            # 创建工作簿
            wb = openpyxl.Workbook()

            # 删除默认工作表
            wb.remove(wb.active)

            if integrated_mode:
                # 创建整合的综合报告页面
                self._create_integrated_report_sheet(wb, accounts_data, seven_day_data, platform)
                self.logger.info(f"美化Excel综合报告已保存到: {file_path}")
            else:
                # 创建分别的工作表页面（原来的方式）
                self._create_separate_sheets(wb, accounts_data, seven_day_data, platform, sheet_config)
                self.logger.info(f"美化Excel报告已保存到: {file_path}")

            # 保存文件
            wb.save(file_path)
            return True

        except Exception as e:
            self.logger.error(f"创建美化Excel报告失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _create_separate_sheets(self, wb, accounts_data, seven_day_data, platform, sheet_config):
        """创建分别的工作表页面（原来的方式）"""
        try:
            # 默认配置
            if sheet_config is None:
                sheet_config = {
                    'include_overview': True,
                    'include_details': True,
                    'include_analysis': True,
                    'include_charts': False  # 默认不包含图表分析
                }

            # 创建概览页
            if sheet_config.get('include_overview', True):
                self._create_overview_sheet(wb, accounts_data, platform)

            # 创建账号数据详情页
            if sheet_config.get('include_details', True):
                self._create_accounts_detail_sheet(wb, accounts_data, platform)

            # 如果有七天收益数据，创建收益分析页
            if sheet_config.get('include_analysis', True) and seven_day_data is not None and not seven_day_data.empty:
                self._create_income_analysis_sheet(wb, seven_day_data, platform)

            # 创建图表分析页（如果需要）
            if sheet_config.get('include_charts', False):
                self._create_charts_sheet(wb, accounts_data, seven_day_data, platform)

            return True

        except Exception as e:
            self.logger.error(f"创建分别工作表失败: {e}")
            return False

    def _create_integrated_report_sheet(self, wb, accounts_data, seven_day_data, platform):
        """创建整合的综合报告页面 - 包含所有信息"""
        try:
            from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
            from openpyxl.utils.dataframe import dataframe_to_rows

            ws = wb.create_sheet("📊 综合数据报告", 0)

            # 确保数据是DataFrame格式
            if not isinstance(accounts_data, pd.DataFrame):
                self.logger.error("账号数据不是DataFrame格式")
                return False

            if accounts_data.empty:
                self.logger.warning("账号数据为空")
                ws['A1'] = f"{platform} 综合数据报告"
                ws['A2'] = "暂无数据"
                return True

            current_row = 1

            # 第一部分：页面标题和数据概览（第1-5行）
            current_row = self._create_integrated_header_section(ws, accounts_data, platform, current_row)

            # 第二部分：账号详细数据表格（从第6行开始）
            current_row = self._create_integrated_accounts_section(ws, accounts_data, current_row + 2)

            # 第三部分：七天收益分析（如果有数据）
            if seven_day_data is not None and not seven_day_data.empty:
                current_row = self._create_integrated_income_section(ws, seven_day_data, current_row + 3)

            # 第四部分：数据统计分析
            current_row = self._create_integrated_statistics_section(ws, accounts_data, seven_day_data, current_row + 3)

            return True

        except Exception as e:
            self.logger.error(f"创建整合报告页面失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _create_integrated_header_section(self, ws, accounts_data, platform, start_row):
        """创建整合报告的标题和概览区域"""
        try:
            from openpyxl.styles import Font, PatternFill, Border, Side, Alignment

            # 第1行：主标题
            ws.cell(row=start_row, column=1, value=f"📊 {platform} 综合数据报告")
            title_cell = ws.cell(row=start_row, column=1)
            title_cell.font = Font(**self.fonts['title'], color='FFFFFF', size=16, bold=True)
            title_cell.fill = PatternFill(start_color=self.colors['header'][1:], end_color=self.colors['header'][1:], fill_type='solid')
            title_cell.alignment = Alignment(horizontal='center', vertical='center')

            # 合并标题行（A-P列）
            ws.merge_cells(f'A{start_row}:P{start_row}')
            ws.row_dimensions[start_row].height = 35

            # 第2行：生成时间
            time_row = start_row + 1
            ws.cell(row=time_row, column=1, value=f"📅 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            time_cell = ws.cell(row=time_row, column=1)
            time_cell.font = Font(**self.fonts['small'], color='666666')
            time_cell.alignment = Alignment(horizontal='left', vertical='center')
            ws.merge_cells(f'A{time_row}:P{time_row}')
            ws.row_dimensions[time_row].height = 20

            # 第3-4行：数据概览卡片
            overview_row = time_row + 1
            self._create_integrated_overview_cards(ws, accounts_data, overview_row)

            return overview_row + 1  # 返回下一个可用行

        except Exception as e:
            self.logger.error(f"创建标题区域失败: {e}")
            return start_row + 4

    def _create_integrated_overview_cards(self, ws, accounts_data, start_row):
        """创建数据概览卡片"""
        try:
            from openpyxl.styles import Font, PatternFill, Border, Side, Alignment

            # 计算统计数据
            total_accounts = len(accounts_data)

            def safe_sum(column_name):
                if column_name in accounts_data.columns:
                    try:
                        values = accounts_data[column_name].fillna(0)
                        numeric_values = pd.to_numeric(values, errors='coerce').fillna(0)
                        return numeric_values.sum()
                    except:
                        return 0
                return 0

            total_income = safe_sum('累计收益')
            total_fans = safe_sum('总粉丝')
            total_views = safe_sum('总播放')
            total_drafts = safe_sum('草稿箱数量')
            total_withdrawable = safe_sum('可提现')

            # 创建概览卡片数据
            overview_stats = [
                ("账号总数", f"{total_accounts}", "个"),
                ("累计收益", f"{total_income:.2f}", "元"),
                ("可提现", f"{total_withdrawable:.2f}", "元"),
                ("总粉丝数", f"{int(total_fans):,}", "人"),
                ("总播放量", f"{int(total_views):,}", "次"),
                ("草稿总数", f"{int(total_drafts)}", "个")
            ]

            # 卡片颜色主题
            card_colors = [
                self.colors['primary'], self.colors['success'], self.colors['accent'],
                self.colors['info'], self.colors['secondary'], self.colors['header']
            ]

            # 在第3-4行创建概览卡片（每行3个卡片）
            for i, (label, value, unit) in enumerate(overview_stats):
                if i < 6:  # 最多显示6个统计项
                    row_offset = i // 3  # 0或1，决定在第3行还是第4行
                    col_offset = (i % 3) * 5 + 1  # 1, 6, 11列开始

                    card_row = start_row + row_offset

                    # 标签单元格
                    label_cell = ws.cell(row=card_row, column=col_offset, value=label)
                    label_cell.font = Font(**self.fonts['small'], color='666666', bold=True)
                    label_cell.alignment = Alignment(horizontal='center', vertical='center')
                    label_cell.fill = PatternFill(start_color='F8F9FA', end_color='F8F9FA', fill_type='solid')

                    # 数值单元格
                    value_cell = ws.cell(row=card_row, column=col_offset + 1, value=f"{value}")
                    value_cell.font = Font(**self.fonts['header'], color=card_colors[i][1:], bold=True, size=12)
                    value_cell.alignment = Alignment(horizontal='center', vertical='center')
                    value_cell.fill = PatternFill(start_color=self.colors['light'][1:], end_color=self.colors['light'][1:], fill_type='solid')

                    # 单位单元格
                    unit_cell = ws.cell(row=card_row, column=col_offset + 2, value=unit)
                    unit_cell.font = Font(**self.fonts['small'], color='666666')
                    unit_cell.alignment = Alignment(horizontal='left', vertical='center')
                    unit_cell.fill = PatternFill(start_color=self.colors['light'][1:], end_color=self.colors['light'][1:], fill_type='solid')

                    # 添加边框
                    for col in range(col_offset, col_offset + 3):
                        cell = ws.cell(row=card_row, column=col)
                        cell.border = Border(
                            left=Side(style='thin', color=self.colors['border'][1:]),
                            right=Side(style='thin', color=self.colors['border'][1:]),
                            top=Side(style='thin', color=self.colors['border'][1:]),
                            bottom=Side(style='thin', color=self.colors['border'][1:])
                        )

            # 设置行高
            ws.row_dimensions[start_row].height = 25
            ws.row_dimensions[start_row + 1].height = 25

        except Exception as e:
            self.logger.error(f"创建概览卡片失败: {e}")

    def _create_integrated_accounts_section(self, ws, accounts_data, start_row):
        """创建整合报告的账号详细数据区域"""
        try:
            from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
            from openpyxl.utils.dataframe import dataframe_to_rows

            # 添加分隔标题
            ws.cell(row=start_row, column=1, value="📋 账号详细数据")
            title_cell = ws.cell(row=start_row, column=1)
            title_cell.font = Font(**self.fonts['subtitle'], color=self.colors['primary'][1:], bold=True, size=14)
            title_cell.alignment = Alignment(horizontal='left', vertical='center')
            ws.merge_cells(f'A{start_row}:P{start_row}')
            ws.row_dimensions[start_row].height = 25

            # 数据表格开始行
            table_start_row = start_row + 1

            # 清理数据
            cleaned_data = accounts_data.copy()
            for col in cleaned_data.columns:
                if cleaned_data[col].dtype == 'object':
                    cleaned_data[col] = cleaned_data[col].fillna('')
                else:
                    cleaned_data[col] = cleaned_data[col].fillna(0)

            # 添加表头
            headers = list(cleaned_data.columns)
            for col_idx, header in enumerate(headers):
                cell = ws.cell(row=table_start_row, column=col_idx + 1, value=header)
                cell.font = Font(**self.fonts['header'], color='FFFFFF', bold=True)
                cell.fill = PatternFill(start_color=self.colors['primary'][1:], end_color=self.colors['primary'][1:], fill_type='solid')
                cell.alignment = Alignment(horizontal='center', vertical='center')
                cell.border = Border(
                    left=Side(style='thin', color=self.colors['border'][1:]),
                    right=Side(style='thin', color=self.colors['border'][1:]),
                    top=Side(style='thin', color=self.colors['border'][1:]),
                    bottom=Side(style='thin', color=self.colors['border'][1:])
                )

            # 添加数据行
            row_index = table_start_row + 1
            for data_row_idx, (_, row_data) in enumerate(cleaned_data.iterrows()):
                for col_idx, value in enumerate(row_data):
                    # 处理值
                    if pd.isna(value):
                        clean_value = ''
                    elif isinstance(value, (int, float)):
                        clean_value = value
                    else:
                        clean_value = str(value)

                    cell = ws.cell(row=row_index, column=col_idx + 1, value=clean_value)
                    cell.font = Font(**self.fonts['body'])
                    cell.alignment = Alignment(horizontal='center', vertical='center')

                    # 交替行颜色
                    if data_row_idx % 2 == 0:
                        cell.fill = PatternFill(start_color='F8F9FA', end_color='F8F9FA', fill_type='solid')
                    else:
                        cell.fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')

                    # 添加边框
                    cell.border = Border(
                        left=Side(style='thin', color=self.colors['border'][1:]),
                        right=Side(style='thin', color=self.colors['border'][1:]),
                        top=Side(style='thin', color=self.colors['border'][1:]),
                        bottom=Side(style='thin', color=self.colors['border'][1:])
                    )

                row_index += 1

            # 自动调整列宽
            for col_num in range(1, len(accounts_data.columns) + 1):
                column_letter = chr(64 + col_num) if col_num <= 26 else f"A{chr(65 + (col_num - 27))}"
                max_length = 0

                # 检查该列的所有单元格
                for row_num in range(start_row, row_index):
                    cell = ws.cell(row=row_num, column=col_num)
                    try:
                        if cell.value is not None and len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass

                adjusted_width = min(max(max_length + 2, 10), 25)  # 最小10，最大25
                ws.column_dimensions[column_letter].width = adjusted_width

            # 添加条件格式
            try:
                self._add_integrated_conditional_formatting(ws, accounts_data, table_start_row + 1)
            except Exception as e:
                self.logger.warning(f"添加条件格式失败: {e}")

            return row_index - 1  # 返回最后使用的行

        except Exception as e:
            self.logger.error(f"创建账号详细数据区域失败: {e}")
            return start_row + 10

    def _create_integrated_income_section(self, ws, seven_day_data, start_row):
        """创建整合报告的七天收益分析区域"""
        try:
            from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
            from openpyxl.utils.dataframe import dataframe_to_rows

            # 添加分隔标题
            ws.cell(row=start_row, column=1, value="💰 七天收益分析")
            title_cell = ws.cell(row=start_row, column=1)
            title_cell.font = Font(**self.fonts['subtitle'], color=self.colors['success'][1:], bold=True, size=14)
            title_cell.alignment = Alignment(horizontal='left', vertical='center')
            ws.merge_cells(f'A{start_row}:P{start_row}')
            ws.row_dimensions[start_row].height = 25

            # 数据表格开始行
            table_start_row = start_row + 1

            # 添加表头
            headers = list(seven_day_data.columns)
            for col_idx, header in enumerate(headers):
                cell = ws.cell(row=table_start_row, column=col_idx + 1, value=header)
                cell.font = Font(**self.fonts['header'], color='FFFFFF', bold=True)
                cell.fill = PatternFill(start_color=self.colors['success'][1:], end_color=self.colors['success'][1:], fill_type='solid')
                cell.alignment = Alignment(horizontal='center', vertical='center')
                cell.border = Border(
                    left=Side(style='thin', color=self.colors['border'][1:]),
                    right=Side(style='thin', color=self.colors['border'][1:]),
                    top=Side(style='thin', color=self.colors['border'][1:]),
                    bottom=Side(style='thin', color=self.colors['border'][1:])
                )

            # 添加数据行
            row_index = table_start_row + 1
            for data_row_idx, (_, row_data) in enumerate(seven_day_data.iterrows()):
                for col_idx, value in enumerate(row_data):
                    cell = ws.cell(row=row_index, column=col_idx + 1, value=value)
                    cell.font = Font(**self.fonts['body'])
                    cell.alignment = Alignment(horizontal='center', vertical='center')

                    # 交替行颜色
                    if data_row_idx % 2 == 0:
                        cell.fill = PatternFill(start_color='F0F8F0', end_color='F0F8F0', fill_type='solid')
                    else:
                        cell.fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')

                    # 添加边框
                    cell.border = Border(
                        left=Side(style='thin', color=self.colors['border'][1:]),
                        right=Side(style='thin', color=self.colors['border'][1:]),
                        top=Side(style='thin', color=self.colors['border'][1:]),
                        bottom=Side(style='thin', color=self.colors['border'][1:])
                    )

                row_index += 1

            # 自动调整列宽
            for col_num in range(1, len(seven_day_data.columns) + 1):
                column_letter = chr(64 + col_num) if col_num <= 26 else f"A{chr(65 + (col_num - 27))}"
                max_length = 0

                for row_num in range(start_row, row_index):
                    cell = ws.cell(row=row_num, column=col_num)
                    try:
                        if cell.value is not None and len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass

                adjusted_width = min(max(max_length + 2, 10), 20)
                ws.column_dimensions[column_letter].width = adjusted_width

            # 添加收益数据的条件格式
            try:
                self._add_integrated_income_conditional_formatting(ws, seven_day_data, table_start_row + 1)
            except Exception as e:
                self.logger.warning(f"添加收益条件格式失败: {e}")

            return row_index - 1

        except Exception as e:
            self.logger.error(f"创建七天收益分析区域失败: {e}")
            return start_row + 10

    def _create_integrated_statistics_section(self, ws, accounts_data, seven_day_data, start_row):
        """创建整合报告的数据统计分析区域"""
        try:
            from openpyxl.styles import Font, PatternFill, Border, Side, Alignment

            # 添加分隔标题
            ws.cell(row=start_row, column=1, value="📈 数据统计分析")
            title_cell = ws.cell(row=start_row, column=1)
            title_cell.font = Font(**self.fonts['subtitle'], color=self.colors['info'][1:], bold=True, size=14)
            title_cell.alignment = Alignment(horizontal='left', vertical='center')
            ws.merge_cells(f'A{start_row}:P{start_row}')
            ws.row_dimensions[start_row].height = 25

            current_row = start_row + 2

            # 创建统计卡片区域
            current_row = self._create_integrated_stats_cards(ws, accounts_data, seven_day_data, current_row)

            # 创建排行榜区域
            current_row = self._create_integrated_rankings(ws, accounts_data, current_row + 2)

            return current_row

        except Exception as e:
            self.logger.error(f"创建数据统计分析区域失败: {e}")
            return start_row + 15

    def _create_integrated_stats_cards(self, ws, accounts_data, seven_day_data, start_row):
        """创建统计卡片"""
        try:
            from openpyxl.styles import Font, PatternFill, Border, Side, Alignment

            # 计算高级统计数据
            def safe_sum(column_name):
                if column_name in accounts_data.columns:
                    try:
                        values = accounts_data[column_name].fillna(0)
                        numeric_values = pd.to_numeric(values, errors='coerce').fillna(0)
                        return numeric_values.sum()
                    except:
                        return 0
                return 0

            def safe_mean(column_name):
                if column_name in accounts_data.columns:
                    try:
                        values = accounts_data[column_name].fillna(0)
                        numeric_values = pd.to_numeric(values, errors='coerce').fillna(0)
                        return numeric_values.mean()
                    except:
                        return 0
                return 0

            # 计算统计数据
            avg_income = safe_mean('累计收益')
            avg_fans = safe_mean('总粉丝')
            total_withdrawable = safe_sum('可提现')
            total_withdrawn = safe_sum('总提现')

            # 计算七天收益趋势
            seven_day_trend = "稳定"
            if seven_day_data is not None and not seven_day_data.empty:
                try:
                    # 获取最近两天的收益进行比较
                    date_columns = [col for col in seven_day_data.columns if col not in ['账号', '七天总收益']]
                    if len(date_columns) >= 2:
                        recent_income = seven_day_data[date_columns[-1]].sum()
                        previous_income = seven_day_data[date_columns[-2]].sum()
                        if recent_income > previous_income * 1.1:
                            seven_day_trend = "上升"
                        elif recent_income < previous_income * 0.9:
                            seven_day_trend = "下降"
                except:
                    pass

            # 创建统计卡片数据
            stats_cards = [
                ("平均收益", f"{avg_income:.2f}", "元/账号", self.colors['success']),
                ("平均粉丝", f"{int(avg_fans):,}", "人/账号", self.colors['info']),
                ("可提现总额", f"{total_withdrawable:.2f}", "元", self.colors['accent']),
                ("已提现总额", f"{total_withdrawn:.2f}", "元", self.colors['secondary']),
                ("收益趋势", seven_day_trend, "", self.colors['primary']),
                ("活跃账号", f"{len(accounts_data)}", "个", self.colors['header'])
            ]

            # 创建统计卡片（2行，每行3个）
            for i, (label, value, unit, color) in enumerate(stats_cards):
                if i < 6:
                    row_offset = i // 3  # 0或1
                    col_offset = (i % 3) * 5 + 1  # 1, 6, 11列开始

                    card_row = start_row + row_offset

                    # 标签
                    label_cell = ws.cell(row=card_row, column=col_offset, value=label)
                    label_cell.font = Font(**self.fonts['small'], color='666666', bold=True)
                    label_cell.alignment = Alignment(horizontal='center', vertical='center')
                    label_cell.fill = PatternFill(start_color='F8F9FA', end_color='F8F9FA', fill_type='solid')

                    # 数值
                    value_cell = ws.cell(row=card_row, column=col_offset + 1, value=value)
                    value_cell.font = Font(**self.fonts['header'], color=color[1:], bold=True, size=11)
                    value_cell.alignment = Alignment(horizontal='center', vertical='center')
                    value_cell.fill = PatternFill(start_color=self.colors['light'][1:], end_color=self.colors['light'][1:], fill_type='solid')

                    # 单位
                    if unit:
                        unit_cell = ws.cell(row=card_row, column=col_offset + 2, value=unit)
                        unit_cell.font = Font(**self.fonts['small'], color='666666')
                        unit_cell.alignment = Alignment(horizontal='left', vertical='center')
                        unit_cell.fill = PatternFill(start_color=self.colors['light'][1:], end_color=self.colors['light'][1:], fill_type='solid')

                    # 添加边框
                    for col in range(col_offset, col_offset + 3):
                        cell = ws.cell(row=card_row, column=col)
                        cell.border = Border(
                            left=Side(style='thin', color=self.colors['border'][1:]),
                            right=Side(style='thin', color=self.colors['border'][1:]),
                            top=Side(style='thin', color=self.colors['border'][1:]),
                            bottom=Side(style='thin', color=self.colors['border'][1:])
                        )

            # 设置行高
            ws.row_dimensions[start_row].height = 25
            ws.row_dimensions[start_row + 1].height = 25

            return start_row + 1

        except Exception as e:
            self.logger.error(f"创建统计卡片失败: {e}")
            return start_row + 2

    def _create_integrated_rankings(self, ws, accounts_data, start_row):
        """创建排行榜区域"""
        try:
            from openpyxl.styles import Font, PatternFill, Border, Side, Alignment

            # 添加排行榜标题
            ws.cell(row=start_row, column=1, value="🏆 账号排行榜")
            title_cell = ws.cell(row=start_row, column=1)
            title_cell.font = Font(**self.fonts['subtitle'], color=self.colors['accent'][1:], bold=True, size=12)
            title_cell.alignment = Alignment(horizontal='left', vertical='center')
            ws.merge_cells(f'A{start_row}:P{start_row}')
            ws.row_dimensions[start_row].height = 20

            current_row = start_row + 1

            # 创建三个排行榜：收益、粉丝、播放量
            rankings = [
                ("💰 收益排行", "累计收益", self.colors['success']),
                ("👥 粉丝排行", "总粉丝", self.colors['info']),
                ("📺 播放排行", "总播放", self.colors['accent'])
            ]

            for i, (title, column, color) in enumerate(rankings):
                col_start = i * 5 + 1  # A, F, K列开始

                # 排行榜标题
                rank_title_cell = ws.cell(row=current_row, column=col_start, value=title)
                rank_title_cell.font = Font(**self.fonts['small'], color=color[1:], bold=True)
                rank_title_cell.alignment = Alignment(horizontal='center', vertical='center')
                rank_title_cell.fill = PatternFill(start_color=self.colors['light'][1:], end_color=self.colors['light'][1:], fill_type='solid')
                ws.merge_cells(f'{chr(64 + col_start)}{current_row}:{chr(64 + col_start + 3)}{current_row}')

                # 排行榜数据
                if column in accounts_data.columns:
                    try:
                        # 排序并取前3名
                        sorted_data = accounts_data.nlargest(3, column)

                        for rank, (_, row) in enumerate(sorted_data.iterrows()):
                            rank_row = current_row + rank + 1

                            # 排名
                            rank_cell = ws.cell(row=rank_row, column=col_start, value=f"#{rank + 1}")
                            rank_cell.font = Font(**self.fonts['small'], color='666666', bold=True)
                            rank_cell.alignment = Alignment(horizontal='center', vertical='center')

                            # 账号名
                            account_cell = ws.cell(row=rank_row, column=col_start + 1, value=str(row.get('账号', '未知')))
                            account_cell.font = Font(**self.fonts['small'])
                            account_cell.alignment = Alignment(horizontal='left', vertical='center')

                            # 数值
                            value = row.get(column, 0)
                            if isinstance(value, (int, float)):
                                if column == "累计收益":
                                    display_value = f"{value:.2f}元"
                                else:
                                    display_value = f"{int(value):,}"
                            else:
                                display_value = str(value)

                            value_cell = ws.cell(row=rank_row, column=col_start + 2, value=display_value)
                            value_cell.font = Font(**self.fonts['small'], color=color[1:])
                            value_cell.alignment = Alignment(horizontal='right', vertical='center')

                            # 添加边框
                            for col in range(col_start, col_start + 3):
                                cell = ws.cell(row=rank_row, column=col)
                                cell.border = Border(
                                    left=Side(style='thin', color=self.colors['border'][1:]),
                                    right=Side(style='thin', color=self.colors['border'][1:]),
                                    top=Side(style='thin', color=self.colors['border'][1:]),
                                    bottom=Side(style='thin', color=self.colors['border'][1:])
                                )
                    except Exception as e:
                        self.logger.warning(f"创建{title}失败: {e}")

            return current_row + 4

        except Exception as e:
            self.logger.error(f"创建排行榜区域失败: {e}")
            return start_row + 5

    def _add_integrated_conditional_formatting(self, ws, accounts_data, data_start_row):
        """为整合报告添加条件格式"""
        try:
            from openpyxl.formatting.rule import DataBarRule

            # 为数值列添加数据条
            numeric_columns = ['累计收益', '昨日收益', '总播放', '昨日播放', '总粉丝', '昨日粉丝', '草稿箱数量']

            for col_name in numeric_columns:
                if col_name in accounts_data.columns:
                    col_idx = accounts_data.columns.get_loc(col_name) + 1
                    col_letter = chr(64 + col_idx) if col_idx <= 26 else f"A{chr(65 + (col_idx - 27))}"

                    data_end_row = data_start_row + len(accounts_data) - 1

                    # 数据条
                    data_bar_rule = DataBarRule(
                        start_type='min', start_value=None,
                        end_type='max', end_value=None,
                        color=self.colors['primary'][1:]
                    )
                    ws.conditional_formatting.add(f'{col_letter}{data_start_row}:{col_letter}{data_end_row}', data_bar_rule)

        except Exception as e:
            self.logger.error(f"添加整合条件格式失败: {e}")

    def _add_integrated_income_conditional_formatting(self, ws, seven_day_data, data_start_row):
        """为整合报告的收益数据添加条件格式"""
        try:
            from openpyxl.formatting.rule import DataBarRule

            # 为收益列添加数据条
            date_columns = [col for col in seven_day_data.columns if col not in ['账号', '七天总收益']]

            for col_name in date_columns + ['七天总收益']:
                if col_name in seven_day_data.columns:
                    col_idx = seven_day_data.columns.get_loc(col_name) + 1
                    col_letter = chr(64 + col_idx) if col_idx <= 26 else f"A{chr(65 + (col_idx - 27))}"

                    data_end_row = data_start_row + len(seven_day_data) - 1

                    # 数据条
                    data_bar_rule = DataBarRule(
                        start_type='min', start_value=None,
                        end_type='max', end_value=None,
                        color=self.colors['success'][1:]
                    )
                    ws.conditional_formatting.add(f'{col_letter}{data_start_row}:{col_letter}{data_end_row}', data_bar_rule)

        except Exception as e:
            self.logger.error(f"添加收益条件格式失败: {e}")

    def _create_overview_sheet(self, wb, accounts_data, platform):
        """创建概览页"""
        try:
            import openpyxl
            from openpyxl.styles import Font, PatternFill, Border, Side, Alignment

            ws = wb.create_sheet("📊 数据概览", 0)

            # 设置页面标题
            ws['A1'] = f"{platform} 账号数据概览报告"
            ws['A1'].font = Font(**self.fonts['title'], color='FFFFFF')
            ws['A1'].fill = PatternFill(start_color=self.colors['header'][1:], end_color=self.colors['header'][1:], fill_type='solid')
            ws['A1'].alignment = Alignment(horizontal='center', vertical='center')
            ws.merge_cells('A1:F1')
            ws.row_dimensions[1].height = 30

            # 生成时间
            ws['A2'] = f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            ws['A2'].font = Font(**self.fonts['small'], color='666666')
            ws.merge_cells('A2:F2')

            # 统计信息 - 修复数据访问方式
            total_accounts = len(accounts_data)

            # 安全地获取数值列的总和
            def safe_sum(column_name):
                if column_name in accounts_data.columns:
                    try:
                        # 清理数据并转换为数值
                        values = accounts_data[column_name].fillna(0)
                        numeric_values = pd.to_numeric(values, errors='coerce').fillna(0)
                        return numeric_values.sum()
                    except:
                        return 0
                return 0

            total_income = safe_sum('累计收益')
            total_fans = safe_sum('总粉丝')
            total_views = safe_sum('总播放')

            # 创建统计卡片
            stats = [
                ("账号总数", total_accounts, "个", self.colors['primary']),
                ("累计收益", f"{total_income:.2f}", "元", self.colors['success']),
                ("总粉丝数", f"{int(total_fans):,}", "人", self.colors['info']),
                ("总播放量", f"{int(total_views):,}", "次", self.colors['accent'])
            ]

            row = 4
            for i, (label, value, unit, color) in enumerate(stats):
                col = i * 2 + 1  # A, C, E, G列

                # 标签
                ws.cell(row=row, column=col, value=label)
                ws.cell(row=row, column=col).font = Font(**self.fonts['header'])
                ws.cell(row=row, column=col).alignment = Alignment(horizontal='center')

                # 数值
                ws.cell(row=row+1, column=col, value=f"{value} {unit}")
                ws.cell(row=row+1, column=col).font = Font(**self.fonts['body'], color=color[1:])
                ws.cell(row=row+1, column=col).alignment = Alignment(horizontal='center')

                # 设置背景色
                for r in range(row, row+2):
                    ws.cell(row=r, column=col).fill = PatternFill(start_color='F8F9FA', end_color='F8F9FA', fill_type='solid')
                    ws.cell(row=r, column=col+1).fill = PatternFill(start_color='F8F9FA', end_color='F8F9FA', fill_type='solid')

                # 合并单元格
                ws.merge_cells(f'{chr(64+col)}{row}:{chr(64+col+1)}{row}')
                ws.merge_cells(f'{chr(64+col)}{row+1}:{chr(64+col+1)}{row+1}')

            # 设置列宽
            for col in range(1, 9):
                ws.column_dimensions[chr(64+col)].width = 15

            return True

        except Exception as e:
            self.logger.error(f"创建概览页失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _create_accounts_detail_sheet(self, wb, accounts_data, platform):
        """创建优化的账号详情页 - 数据概览在第一行"""
        try:
            from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
            from openpyxl.utils.dataframe import dataframe_to_rows

            ws = wb.create_sheet("📋 账号详情")

            # 确保数据是DataFrame格式
            if not isinstance(accounts_data, pd.DataFrame):
                self.logger.error("账号数据不是DataFrame格式")
                return False

            if accounts_data.empty:
                self.logger.warning("账号数据为空")
                # 创建空的工作表
                ws['A1'] = f"{platform} 账号详细数据"
                ws['A2'] = "暂无数据"
                return True

            # 第一步：创建数据概览区域（第1-4行）
            current_row = self._create_overview_section_in_details(ws, accounts_data, platform)

            # 第二步：创建账号详细数据表格（从第5行开始）
            self._create_detailed_data_table(ws, accounts_data, platform, current_row + 1)

            return True

        except Exception as e:
            self.logger.error(f"创建账号详情页失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _create_overview_section_in_details(self, ws, accounts_data, platform):
        """在账号详情页创建数据概览区域"""
        try:
            from openpyxl.styles import Font, PatternFill, Border, Side, Alignment

            # 第1行：页面标题
            ws['A1'] = f"📊 {platform} 数据概览与账号详情"
            ws['A1'].font = Font(**self.fonts['title'], color='FFFFFF')
            ws['A1'].fill = PatternFill(start_color=self.colors['header'][1:], end_color=self.colors['header'][1:], fill_type='solid')
            ws['A1'].alignment = Alignment(horizontal='center', vertical='center')

            # 计算合并列数（至少8列以确保美观）
            merge_cols = max(len(accounts_data.columns), 8)
            end_col = chr(64 + merge_cols) if merge_cols <= 26 else f"A{chr(65 + (merge_cols - 27))}"
            ws.merge_cells(f'A1:{end_col}1')
            ws.row_dimensions[1].height = 30

            # 第2行：生成时间和统计信息标题
            ws['A2'] = f"📅 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            ws['A2'].font = Font(**self.fonts['small'], color='666666')
            ws['A2'].alignment = Alignment(horizontal='left', vertical='center')
            ws.merge_cells(f'A2:{end_col}2')
            ws.row_dimensions[2].height = 20

            # 第3行：数据概览卡片
            # 计算统计数据
            total_accounts = len(accounts_data)

            def safe_sum(column_name):
                if column_name in accounts_data.columns:
                    try:
                        values = accounts_data[column_name].fillna(0)
                        numeric_values = pd.to_numeric(values, errors='coerce').fillna(0)
                        return numeric_values.sum()
                    except:
                        return 0
                return 0

            total_income = safe_sum('累计收益')
            total_fans = safe_sum('总粉丝')
            total_views = safe_sum('总播放')
            total_drafts = safe_sum('草稿箱数量')

            # 创建概览卡片数据
            overview_stats = [
                ("账号总数", f"{total_accounts}", "个"),
                ("累计收益", f"{total_income:.2f}", "元"),
                ("总粉丝数", f"{int(total_fans):,}", "人"),
                ("总播放量", f"{int(total_views):,}", "次"),
                ("草稿总数", f"{int(total_drafts)}", "个")
            ]

            # 在第3行创建概览卡片
            card_colors = [self.colors['primary'], self.colors['success'], self.colors['info'], self.colors['accent'], self.colors['secondary']]

            for i, (label, value, unit) in enumerate(overview_stats):
                if i < 5:  # 最多显示5个统计项
                    col_start = i * 2 + 1  # A, C, E, G, I列

                    # 标签单元格
                    label_cell = ws.cell(row=3, column=col_start, value=label)
                    label_cell.font = Font(**self.fonts['small'], color='666666', bold=True)
                    label_cell.alignment = Alignment(horizontal='center', vertical='center')
                    label_cell.fill = PatternFill(start_color='F8F9FA', end_color='F8F9FA', fill_type='solid')
                    label_cell.border = Border(
                        left=Side(style='thin', color=self.colors['border'][1:]),
                        right=Side(style='thin', color=self.colors['border'][1:]),
                        top=Side(style='thin', color=self.colors['border'][1:]),
                        bottom=Side(style='thin', color=self.colors['border'][1:])
                    )

                    # 数值单元格
                    value_cell = ws.cell(row=4, column=col_start, value=f"{value} {unit}")
                    value_cell.font = Font(**self.fonts['header'], color=card_colors[i][1:], bold=True)
                    value_cell.alignment = Alignment(horizontal='center', vertical='center')
                    value_cell.fill = PatternFill(start_color=self.colors['light'][1:], end_color=self.colors['light'][1:], fill_type='solid')
                    value_cell.border = Border(
                        left=Side(style='thin', color=self.colors['border'][1:]),
                        right=Side(style='thin', color=self.colors['border'][1:]),
                        top=Side(style='thin', color=self.colors['border'][1:]),
                        bottom=Side(style='thin', color=self.colors['border'][1:])
                    )

            # 设置行高
            ws.row_dimensions[3].height = 20
            ws.row_dimensions[4].height = 25

            return 4  # 返回当前使用的最后一行

        except Exception as e:
            self.logger.error(f"创建概览区域失败: {e}")
            return 2

    def _create_detailed_data_table(self, ws, accounts_data, platform, start_row):
        """创建详细数据表格"""
        try:
            from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
            from openpyxl.utils.dataframe import dataframe_to_rows

            # 添加分隔行
            separator_row = start_row
            ws.cell(row=separator_row, column=1, value="📋 详细账号数据")
            ws.cell(row=separator_row, column=1).font = Font(**self.fonts['subtitle'], color=self.colors['primary'][1:], bold=True)
            ws.cell(row=separator_row, column=1).alignment = Alignment(horizontal='left', vertical='center')

            # 合并分隔行
            merge_cols = max(len(accounts_data.columns), 8)
            end_col = chr(64 + merge_cols) if merge_cols <= 26 else f"A{chr(65 + (merge_cols - 27))}"
            ws.merge_cells(f'A{separator_row}:{end_col}{separator_row}')
            ws.row_dimensions[separator_row].height = 25

            # 数据表格开始行
            table_start_row = separator_row + 1

            # 清理数据 - 处理NaN值和数据类型
            cleaned_data = accounts_data.copy()
            for col in cleaned_data.columns:
                if cleaned_data[col].dtype == 'object':
                    cleaned_data[col] = cleaned_data[col].fillna('')
                else:
                    cleaned_data[col] = cleaned_data[col].fillna(0)

            # 添加数据
            try:
                row_index = table_start_row

                # 添加表头
                headers = list(cleaned_data.columns)
                for col_idx, header in enumerate(headers):
                    cell = ws.cell(row=row_index, column=col_idx + 1, value=header)
                    cell.font = Font(**self.fonts['header'], color='FFFFFF')
                    cell.fill = PatternFill(start_color=self.colors['primary'][1:], end_color=self.colors['primary'][1:], fill_type='solid')
                    cell.alignment = Alignment(horizontal='center', vertical='center')
                    cell.border = Border(
                        left=Side(style='thin', color=self.colors['border'][1:]),
                        right=Side(style='thin', color=self.colors['border'][1:]),
                        top=Side(style='thin', color=self.colors['border'][1:]),
                        bottom=Side(style='thin', color=self.colors['border'][1:])
                    )

                row_index += 1

                # 添加数据行
                for data_row_idx, (_, row_data) in enumerate(cleaned_data.iterrows()):
                    for col_idx, value in enumerate(row_data):
                        # 处理值，确保可序列化
                        if pd.isna(value):
                            clean_value = ''
                        elif isinstance(value, (int, float)):
                            clean_value = value
                        else:
                            clean_value = str(value)

                        cell = ws.cell(row=row_index, column=col_idx + 1, value=clean_value)
                        cell.font = Font(**self.fonts['body'])
                        cell.alignment = Alignment(horizontal='center', vertical='center')

                        # 交替行颜色
                        if data_row_idx % 2 == 0:
                            cell.fill = PatternFill(start_color='F8F9FA', end_color='F8F9FA', fill_type='solid')
                        else:
                            cell.fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')

                        # 添加边框
                        cell.border = Border(
                            left=Side(style='thin', color=self.colors['border'][1:]),
                            right=Side(style='thin', color=self.colors['border'][1:]),
                            top=Side(style='thin', color=self.colors['border'][1:]),
                            bottom=Side(style='thin', color=self.colors['border'][1:])
                        )

                    row_index += 1

            except Exception as e:
                self.logger.error(f"添加数据到工作表失败: {e}")
                return False

            # 自动调整列宽
            for col_num in range(1, len(accounts_data.columns) + 1):
                column_letter = chr(64 + col_num) if col_num <= 26 else f"A{chr(65 + (col_num - 27))}"
                max_length = 0

                # 检查该列的所有单元格
                for row_num in range(1, row_index):
                    cell = ws.cell(row=row_num, column=col_num)
                    try:
                        if cell.value is not None and len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass

                adjusted_width = min(max(max_length + 2, 10), 30)  # 最小10，最大30
                ws.column_dimensions[column_letter].width = adjusted_width

            # 添加条件格式
            try:
                self._add_conditional_formatting_to_details(ws, accounts_data, table_start_row + 1)
            except Exception as e:
                self.logger.warning(f"添加条件格式失败: {e}")

            return True

        except Exception as e:
            self.logger.error(f"创建详细数据表格失败: {e}")
            return False

    def _add_conditional_formatting_to_details(self, ws, accounts_data, data_start_row):
        """为账号详情页添加条件格式"""
        try:
            from openpyxl.formatting.rule import ColorScaleRule, DataBarRule

            # 为数值列添加条件格式
            numeric_columns = ['累计收益', '昨日收益', '总播放', '昨日播放', '总粉丝', '昨日粉丝', '草稿箱数量']

            for col_name in numeric_columns:
                if col_name in accounts_data.columns:
                    col_idx = accounts_data.columns.get_loc(col_name) + 1
                    col_letter = chr(64 + col_idx) if col_idx <= 26 else f"A{chr(65 + (col_idx - 27))}"

                    data_end_row = data_start_row + len(accounts_data) - 1

                    # 数据条
                    data_bar_rule = DataBarRule(
                        start_type='min', start_value=None,
                        end_type='max', end_value=None,
                        color=self.colors['primary'][1:]
                    )
                    ws.conditional_formatting.add(f'{col_letter}{data_start_row}:{col_letter}{data_end_row}', data_bar_rule)

        except Exception as e:
            self.logger.error(f"添加条件格式失败: {e}")

    def _create_income_analysis_sheet(self, wb, seven_day_data, platform):
        """创建收益分析页"""
        try:
            import openpyxl
            from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
            from openpyxl.utils.dataframe import dataframe_to_rows

            ws = wb.create_sheet("💰 收益分析")

            # 设置标题
            ws['A1'] = f"{platform} 七天收益分析"
            ws['A1'].font = Font(**self.fonts['title'], color='FFFFFF')
            ws['A1'].fill = PatternFill(start_color=self.colors['success'][1:], end_color=self.colors['success'][1:], fill_type='solid')
            ws['A1'].alignment = Alignment(horizontal='center', vertical='center')

            # 计算需要合并的列数
            merge_cols = len(seven_day_data.columns)
            ws.merge_cells(f'A1:{chr(64+merge_cols)}1')
            ws.row_dimensions[1].height = 25

            # 添加数据
            for r in dataframe_to_rows(seven_day_data, index=False, header=True):
                ws.append(r)

            # 设置表头样式
            header_row = 2
            for col in range(1, len(seven_day_data.columns) + 1):
                cell = ws.cell(row=header_row, column=col)
                cell.font = Font(**self.fonts['header'], color='FFFFFF')
                cell.fill = PatternFill(start_color=self.colors['success'][1:], end_color=self.colors['success'][1:], fill_type='solid')
                cell.alignment = Alignment(horizontal='center', vertical='center')
                cell.border = Border(
                    left=Side(style='thin', color=self.colors['border'][1:]),
                    right=Side(style='thin', color=self.colors['border'][1:]),
                    top=Side(style='thin', color=self.colors['border'][1:]),
                    bottom=Side(style='thin', color=self.colors['border'][1:])
                )

            # 设置数据行样式
            for row in range(3, len(seven_day_data) + 3):
                for col in range(1, len(seven_day_data.columns) + 1):
                    cell = ws.cell(row=row, column=col)
                    cell.font = Font(**self.fonts['body'])
                    cell.alignment = Alignment(horizontal='center', vertical='center')

                    # 交替行颜色
                    if row % 2 == 0:
                        cell.fill = PatternFill(start_color='F0F8F0', end_color='F0F8F0', fill_type='solid')

                    # 添加边框
                    cell.border = Border(
                        left=Side(style='thin', color=self.colors['border'][1:]),
                        right=Side(style='thin', color=self.colors['border'][1:]),
                        top=Side(style='thin', color=self.colors['border'][1:]),
                        bottom=Side(style='thin', color=self.colors['border'][1:])
                    )

            # 自动调整列宽
            for col_num in range(1, len(seven_day_data.columns) + 1):
                column_letter = chr(64 + col_num)
                max_length = 0

                # 检查该列的所有单元格
                for row_num in range(1, len(seven_day_data) + 3):
                    cell = ws.cell(row=row_num, column=col_num)
                    try:
                        if cell.value and len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass

                adjusted_width = min(max_length + 2, 25)
                ws.column_dimensions[column_letter].width = adjusted_width

            # 添加收益数据的条件格式
            self._add_income_conditional_formatting(ws, seven_day_data)

            return True

        except Exception as e:
            self.logger.error(f"创建收益分析页失败: {e}")
            return False

    def _create_charts_sheet(self, wb, accounts_data, seven_day_data, platform):
        """创建图表分析页 - 彻底避免重叠的垂直布局"""
        try:
            from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
            from openpyxl.chart import BarChart, PieChart, LineChart, Reference

            ws = wb.create_sheet("📈 图表分析")

            # 设置标题
            ws['A1'] = f"{platform} 数据可视化分析"
            ws['A1'].font = Font(**self.fonts['title'], color='FFFFFF')
            ws['A1'].fill = PatternFill(start_color=self.colors['info'][1:], end_color=self.colors['info'][1:], fill_type='solid')
            ws['A1'].alignment = Alignment(horizontal='center', vertical='center')
            ws.merge_cells('A1:P1')
            ws.row_dimensions[1].height = 30

            # 检查数据是否有效
            if accounts_data.empty:
                ws['A3'] = "暂无账号数据可用于图表分析"
                return True

            # 使用固定的垂直布局，确保每个区域有足够的空间
            # 区域1：收益对比分析 (第3-25行)
            if '累计收益' in accounts_data.columns:
                try:
                    self._create_income_section_vertical(ws, accounts_data, 3)
                except Exception as e:
                    self.logger.warning(f"创建收益分析区域失败: {e}")

            # 区域2：粉丝分布分析 (第26-48行)
            if '总粉丝' in accounts_data.columns:
                try:
                    self._create_fans_section_vertical(ws, accounts_data, 26)
                except Exception as e:
                    self.logger.warning(f"创建粉丝分析区域失败: {e}")

            # 区域3：七天收益趋势 (第49-71行)
            if seven_day_data is not None and not seven_day_data.empty:
                try:
                    self._create_trend_section_vertical(ws, seven_day_data, 49)
                except Exception as e:
                    self.logger.warning(f"创建趋势分析区域失败: {e}")

            # 区域4：KPI仪表盘 (第72-90行)
            try:
                self._create_kpi_dashboard_vertical(ws, accounts_data, seven_day_data, 72)
            except Exception as e:
                self.logger.warning(f"创建KPI仪表盘失败: {e}")

            return True

        except Exception as e:
            self.logger.error(f"创建图表分析页失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _add_conditional_formatting(self, ws, accounts_data):
        """添加条件格式"""
        try:
            from openpyxl.formatting.rule import ColorScaleRule, DataBarRule

            # 为数值列添加条件格式
            numeric_columns = ['累计收益', '昨日收益', '总播放', '昨日播放', '总粉丝', '昨日粉丝']

            for col_name in numeric_columns:
                if col_name in accounts_data.columns:
                    col_idx = accounts_data.columns.get_loc(col_name) + 1
                    col_letter = chr(64 + col_idx)

                    # 数据条
                    data_bar_rule = DataBarRule(
                        start_type='min', start_value=None,
                        end_type='max', end_value=None,
                        color=self.colors['primary'][1:]
                    )
                    ws.conditional_formatting.add(f'{col_letter}3:{col_letter}{len(accounts_data)+2}', data_bar_rule)

        except Exception as e:
            self.logger.error(f"添加条件格式失败: {e}")

    def _add_income_conditional_formatting(self, ws, seven_day_data):
        """为收益数据添加条件格式"""
        try:
            from openpyxl.formatting.rule import ColorScaleRule

            # 为日期列添加颜色渐变
            date_columns = [col for col in seven_day_data.columns if col not in ['账号', '七天总收益']]

            for col_name in date_columns:
                if col_name in seven_day_data.columns:
                    col_idx = seven_day_data.columns.get_loc(col_name) + 1
                    col_letter = chr(64 + col_idx)

                    # 颜色渐变：红色(低) -> 黄色(中) -> 绿色(高)
                    color_scale_rule = ColorScaleRule(
                        start_type='min', start_color='FFEB9C',  # 浅黄
                        mid_type='percentile', mid_value=50, mid_color='FFD966',  # 黄色
                        end_type='max', end_color='C6EFCE'  # 浅绿
                    )
                    ws.conditional_formatting.add(f'{col_letter}3:{col_letter}{len(seven_day_data)+2}', color_scale_rule)

        except Exception as e:
            self.logger.error(f"添加收益条件格式失败: {e}")

    def _create_income_bar_chart(self, ws, accounts_data, start_row):
        """创建美化的收益对比柱状图"""
        try:
            from openpyxl.chart import BarChart, Reference
            from openpyxl.styles import Font, PatternFill, Alignment, Border, Side

            # 添加美化的图表标题
            title_cell = ws.cell(row=start_row, column=1, value="💰 账号收益对比分析")
            title_cell.font = Font(**self.fonts['chart_title'], color=self.colors['primary'][1:])
            title_cell.fill = PatternFill(start_color=self.colors['light_gray'][1:],
                                        end_color=self.colors['light_gray'][1:], fill_type='solid')
            title_cell.alignment = Alignment(horizontal='center', vertical='center')
            title_cell.border = Border(
                bottom=Side(border_style='thin', color=self.colors['border'][1:])
            )
            ws.merge_cells(f'A{start_row}:L{start_row}')
            ws.row_dimensions[start_row].height = 25

            # 准备数据
            data_start_row = start_row + 2

            # 设置表头
            headers = ["账号", "累计收益(元)", "昨日收益(元)", "收益排名"]
            for i, header in enumerate(headers):
                cell = ws.cell(row=data_start_row, column=i+1, value=header)
                cell.font = Font(**self.fonts['header'], color='FFFFFF')
                cell.fill = PatternFill(start_color=self.colors['primary'][1:],
                                      end_color=self.colors['primary_light'][1:], fill_type='solid')
                cell.alignment = Alignment(horizontal='center', vertical='center')
                cell.border = Border(
                    left=Side(border_style='thin', color='FFFFFF'),
                    right=Side(border_style='thin', color='FFFFFF'),
                    top=Side(border_style='thin', color='FFFFFF'),
                    bottom=Side(border_style='thin', color='FFFFFF')
                )

            # 添加数据（取前12个账号以获得更好的视觉效果）
            top_accounts = accounts_data.nlargest(12, '累计收益') if len(accounts_data) > 12 else accounts_data

            # 为数据行添加渐变背景色
            gradient_colors = [self.colors['light'], self.colors['light_gray']]

            for i, (_, row) in enumerate(top_accounts.iterrows()):
                data_row = data_start_row + 1 + i

                # 账号名称
                account_cell = ws.cell(row=data_row, column=1, value=row['账号'])

                # 累计收益
                total_income = float(row['累计收益']) if pd.notna(row['累计收益']) else 0
                income_cell = ws.cell(row=data_row, column=2, value=total_income)

                # 昨日收益
                daily_income = float(row.get('昨日收益', 0)) if pd.notna(row.get('昨日收益', 0)) else 0
                daily_cell = ws.cell(row=data_row, column=3, value=daily_income)

                # 收益排名
                rank_cell = ws.cell(row=data_row, column=4, value=f"第{i+1}名")

                # 设置数据行样式
                for cell in [account_cell, income_cell, daily_cell, rank_cell]:
                    cell.font = Font(**self.fonts['body'])
                    cell.alignment = Alignment(horizontal='center', vertical='center')
                    cell.fill = PatternFill(start_color=gradient_colors[i % 2][1:],
                                          end_color=gradient_colors[i % 2][1:], fill_type='solid')
                    cell.border = Border(
                        left=Side(border_style='thin', color=self.colors['border'][1:]),
                        right=Side(border_style='thin', color=self.colors['border'][1:]),
                        top=Side(border_style='thin', color=self.colors['border'][1:]),
                        bottom=Side(border_style='thin', color=self.colors['border'][1:])
                    )

            # 创建美化的柱状图
            chart = BarChart()
            chart.type = "col"
            chart.style = 42  # 使用现代样式

            # 设置图表标题和轴标签
            chart.title = "账号收益对比分析"
            chart.title.tx.rich.p[0].r.rPr.sz = 1400  # 14pt
            chart.title.tx.rich.p[0].r.rPr.b = True   # 粗体

            chart.y_axis.title = '收益金额 (元)'
            chart.x_axis.title = '账号名称'

            # 设置轴样式
            chart.y_axis.majorGridlines = None  # 移除网格线以获得更清洁的外观
            chart.x_axis.tickLblPos = "low"

            # 设置数据范围
            data_end_row = data_start_row + len(top_accounts)
            data = Reference(ws, min_col=2, min_row=data_start_row, max_row=data_end_row, max_col=2)
            cats = Reference(ws, min_col=1, min_row=data_start_row+1, max_row=data_end_row, max_col=1)

            chart.add_data(data, titles_from_data=True)
            chart.set_categories(cats)

            # 美化数据系列 - 简化版本，避免复杂的图表样式设置
            # 使用内置样式来美化图表
            chart.style = 42  # 现代蓝色样式

            # 设置图表大小和位置
            chart.width = 18  # 增加宽度
            chart.height = 12  # 增加高度
            chart.legend = None  # 移除图例以节省空间

            # 添加图表到工作表
            ws.add_chart(chart, f"F{start_row+1}")

            # 设置列宽以优化显示
            ws.column_dimensions['A'].width = 15
            ws.column_dimensions['B'].width = 12
            ws.column_dimensions['C'].width = 12
            ws.column_dimensions['D'].width = 10

            return data_end_row + 4

        except Exception as e:
            self.logger.error(f"创建美化收益柱状图失败: {e}")
            import traceback
            traceback.print_exc()
            return start_row + 25

    def _create_fans_pie_chart(self, ws, accounts_data, start_row):
        """创建美化的粉丝分布饼图"""
        try:
            from openpyxl.chart import PieChart, Reference
            from openpyxl.chart.series import DataPoint
            from openpyxl.styles import Font, PatternFill, Alignment, Border, Side

            # 添加美化的图表标题
            title_cell = ws.cell(row=start_row, column=1, value="👥 粉丝分布分析")
            title_cell.font = Font(**self.fonts['chart_title'], color=self.colors['secondary'][1:])
            title_cell.fill = PatternFill(start_color=self.colors['light_gray'][1:],
                                        end_color=self.colors['light_gray'][1:], fill_type='solid')
            title_cell.alignment = Alignment(horizontal='center', vertical='center')
            title_cell.border = Border(
                bottom=Side(border_style='thin', color=self.colors['border'][1:])
            )
            ws.merge_cells(f'A{start_row}:L{start_row}')
            ws.row_dimensions[start_row].height = 25

            # 准备数据
            data_start_row = start_row + 2

            # 设置表头
            headers = ["账号", "粉丝数", "占比(%)", "粉丝等级"]
            for i, header in enumerate(headers):
                cell = ws.cell(row=data_start_row, column=i+1, value=header)
                cell.font = Font(**self.fonts['header'], color='FFFFFF')
                cell.fill = PatternFill(start_color=self.colors['secondary'][1:],
                                      end_color=self.colors['secondary_light'][1:], fill_type='solid')
                cell.alignment = Alignment(horizontal='center', vertical='center')
                cell.border = Border(
                    left=Side(border_style='thin', color='FFFFFF'),
                    right=Side(border_style='thin', color='FFFFFF'),
                    top=Side(border_style='thin', color='FFFFFF'),
                    bottom=Side(border_style='thin', color='FFFFFF')
                )

            # 添加数据（取前10个账号）
            top_fans = accounts_data.nlargest(10, '总粉丝') if len(accounts_data) > 10 else accounts_data
            total_fans = top_fans['总粉丝'].sum()

            # 定义粉丝等级
            def get_fans_level(fans_count):
                if fans_count >= 100000:
                    return "🌟 大V"
                elif fans_count >= 50000:
                    return "⭐ 中V"
                elif fans_count >= 10000:
                    return "✨ 小V"
                elif fans_count >= 1000:
                    return "💫 新星"
                else:
                    return "🌱 萌新"

            # 为数据行添加渐变背景色
            gradient_colors = [self.colors['light'], self.colors['light_gray']]

            for i, (_, row) in enumerate(top_fans.iterrows()):
                data_row = data_start_row + 1 + i

                # 账号名称
                account_cell = ws.cell(row=data_row, column=1, value=row['账号'])

                # 粉丝数
                fans_count = float(row['总粉丝']) if pd.notna(row['总粉丝']) else 0
                fans_cell = ws.cell(row=data_row, column=2, value=int(fans_count))

                # 占比
                percentage = (fans_count / total_fans * 100) if total_fans > 0 else 0
                percentage_cell = ws.cell(row=data_row, column=3, value=f"{percentage:.1f}%")

                # 粉丝等级
                level_cell = ws.cell(row=data_row, column=4, value=get_fans_level(fans_count))

                # 设置数据行样式
                for cell in [account_cell, fans_cell, percentage_cell, level_cell]:
                    cell.font = Font(**self.fonts['body'])
                    cell.alignment = Alignment(horizontal='center', vertical='center')
                    cell.fill = PatternFill(start_color=gradient_colors[i % 2][1:],
                                          end_color=gradient_colors[i % 2][1:], fill_type='solid')
                    cell.border = Border(
                        left=Side(border_style='thin', color=self.colors['border'][1:]),
                        right=Side(border_style='thin', color=self.colors['border'][1:]),
                        top=Side(border_style='thin', color=self.colors['border'][1:]),
                        bottom=Side(border_style='thin', color=self.colors['border'][1:])
                    )

            # 创建美化的饼图
            chart = PieChart()
            chart.title = "粉丝分布分析"
            chart.title.tx.rich.p[0].r.rPr.sz = 1400  # 14pt
            chart.title.tx.rich.p[0].r.rPr.b = True   # 粗体

            # 设置数据范围
            data_end_row = data_start_row + len(top_fans)
            data = Reference(ws, min_col=2, min_row=data_start_row+1, max_row=data_end_row, max_col=2)
            cats = Reference(ws, min_col=1, min_row=data_start_row+1, max_row=data_end_row, max_col=1)

            chart.add_data(data)
            chart.set_categories(cats)

            # 美化饼图样式
            if chart.series:
                series = chart.series[0]
                # 设置数据标签显示百分比
                series.dLbls = series.dLbls or series.__class__.dLbls()
                series.dLbls.showPercent = True
                series.dLbls.showVal = False
                series.dLbls.showCatName = True
                series.dLbls.separator = "\n"

                # 为每个扇形设置不同颜色
                colors = self.colors['chart_gradient_2']  # 使用绿色渐变
                for i in range(len(top_fans)):
                    if i < len(series.dPt):
                        point = series.dPt[i]
                        if point is None:
                            point = DataPoint(idx=i)
                            series.dPt[i] = point

                        # 设置颜色
                        color_index = i % len(colors)
                        point.graphicalProperties.solidFill = colors[color_index]

            # 设置图表大小和位置
            chart.width = 18  # 增加宽度
            chart.height = 12  # 增加高度

            # 添加图表到工作表
            ws.add_chart(chart, f"F{start_row+1}")

            # 设置列宽以优化显示
            ws.column_dimensions['A'].width = 15
            ws.column_dimensions['B'].width = 12
            ws.column_dimensions['C'].width = 10
            ws.column_dimensions['D'].width = 12

            return data_end_row + 4

        except Exception as e:
            self.logger.error(f"创建美化粉丝饼图失败: {e}")
            import traceback
            traceback.print_exc()
            return start_row + 25

    def _create_income_trend_chart(self, ws, seven_day_data, start_row):
        """创建美化的七天收益趋势图"""
        try:
            from openpyxl.chart import LineChart, Reference
            from openpyxl.styles import Font, PatternFill, Alignment, Border, Side

            # 添加美化的图表标题
            title_cell = ws.cell(row=start_row, column=1, value="📈 七天收益趋势分析")
            title_cell.font = Font(**self.fonts['chart_title'], color=self.colors['accent'][1:])
            title_cell.fill = PatternFill(start_color=self.colors['light_gray'][1:],
                                        end_color=self.colors['light_gray'][1:], fill_type='solid')
            title_cell.alignment = Alignment(horizontal='center', vertical='center')
            title_cell.border = Border(
                bottom=Side(border_style='thin', color=self.colors['border'][1:])
            )
            ws.merge_cells(f'A{start_row}:P{start_row}')
            ws.row_dimensions[start_row].height = 25

            # 准备数据 - 复制七天收益数据到图表页
            data_start_row = start_row + 2

            # 获取日期列（排除账号和总收益列）
            date_columns = [col for col in seven_day_data.columns if col not in ['账号', '七天总收益']]

            # 添加表头
            headers = ["账号"] + date_columns + ["平均收益", "趋势"]
            for i, header in enumerate(headers):
                cell = ws.cell(row=data_start_row, column=i+1, value=header)
                cell.font = Font(**self.fonts['header'], color='FFFFFF')
                cell.fill = PatternFill(start_color=self.colors['accent'][1:],
                                      end_color=self.colors['accent_light'][1:], fill_type='solid')
                cell.alignment = Alignment(horizontal='center', vertical='center')
                cell.border = Border(
                    left=Side(border_style='thin', color='FFFFFF'),
                    right=Side(border_style='thin', color='FFFFFF'),
                    top=Side(border_style='thin', color='FFFFFF'),
                    bottom=Side(border_style='thin', color='FFFFFF')
                )

            # 添加数据（取前8个账号以获得更好的视觉效果）
            top_accounts = seven_day_data.head(8)

            # 为数据行添加渐变背景色
            gradient_colors = [self.colors['light'], self.colors['light_gray']]

            for i, (_, row) in enumerate(top_accounts.iterrows()):
                data_row = data_start_row + 1 + i

                # 账号名称
                account_cell = ws.cell(row=data_row, column=1, value=row['账号'])

                # 日期数据
                daily_values = []
                for j, date_col in enumerate(date_columns):
                    value = float(row[date_col]) if pd.notna(row[date_col]) else 0
                    daily_values.append(value)
                    value_cell = ws.cell(row=data_row, column=j+2, value=value)
                    value_cell.font = Font(**self.fonts['body'])
                    value_cell.alignment = Alignment(horizontal='center', vertical='center')
                    value_cell.fill = PatternFill(start_color=gradient_colors[i % 2][1:],
                                                end_color=gradient_colors[i % 2][1:], fill_type='solid')
                    value_cell.border = Border(
                        left=Side(border_style='thin', color=self.colors['border'][1:]),
                        right=Side(border_style='thin', color=self.colors['border'][1:]),
                        top=Side(border_style='thin', color=self.colors['border'][1:]),
                        bottom=Side(border_style='thin', color=self.colors['border'][1:])
                    )

                # 计算平均收益
                avg_income = sum(daily_values) / len(daily_values) if daily_values else 0
                avg_cell = ws.cell(row=data_row, column=len(date_columns)+2, value=f"{avg_income:.2f}")

                # 计算趋势（比较最后三天与前三天的平均值）
                if len(daily_values) >= 6:
                    first_half = sum(daily_values[:3]) / 3
                    second_half = sum(daily_values[-3:]) / 3
                    if second_half > first_half * 1.1:
                        trend = "📈 上升"
                    elif second_half < first_half * 0.9:
                        trend = "📉 下降"
                    else:
                        trend = "➡️ 平稳"
                else:
                    trend = "➡️ 平稳"

                trend_cell = ws.cell(row=data_row, column=len(date_columns)+3, value=trend)

                # 设置样式
                for cell in [account_cell, avg_cell, trend_cell]:
                    cell.font = Font(**self.fonts['body'])
                    cell.alignment = Alignment(horizontal='center', vertical='center')
                    cell.fill = PatternFill(start_color=gradient_colors[i % 2][1:],
                                          end_color=gradient_colors[i % 2][1:], fill_type='solid')
                    cell.border = Border(
                        left=Side(border_style='thin', color=self.colors['border'][1:]),
                        right=Side(border_style='thin', color=self.colors['border'][1:]),
                        top=Side(border_style='thin', color=self.colors['border'][1:]),
                        bottom=Side(border_style='thin', color=self.colors['border'][1:])
                    )

            # 创建美化的折线图
            chart = LineChart()
            chart.title = "七天收益趋势分析"
            chart.title.tx.rich.p[0].r.rPr.sz = 1400  # 14pt
            chart.title.tx.rich.p[0].r.rPr.b = True   # 粗体
            chart.style = 13
            chart.y_axis.title = '收益金额 (元)'
            chart.x_axis.title = '日期'

            # 设置网格线样式
            chart.y_axis.majorGridlines.spPr.ln.w = 12700  # 细网格线
            chart.x_axis.majorGridlines = None  # 移除X轴网格线

            # 设置数据范围
            data_end_row = data_start_row + len(top_accounts)

            # 为每个账号创建一条线
            for i in range(len(top_accounts)):
                account_row = data_start_row + 1 + i
                data = Reference(ws, min_col=2, min_row=account_row, max_row=account_row, max_col=len(date_columns)+1)
                chart.add_data(data)

            # 设置类别（日期）
            cats = Reference(ws, min_col=2, min_row=data_start_row, max_row=data_start_row, max_col=len(date_columns)+1)
            chart.set_categories(cats)

            # 设置图表大小和位置
            chart.width = 20  # 增加宽度
            chart.height = 12  # 增加高度
            chart.legend.position = 'r'  # 图例位置在右侧

            # 添加图表到工作表
            ws.add_chart(chart, f"A{start_row+len(top_accounts)+5}")

            # 设置列宽以优化显示
            for i in range(len(headers)):
                col_letter = chr(65 + i)  # A, B, C, ...
                if i == 0:  # 账号列
                    ws.column_dimensions[col_letter].width = 15
                elif i <= len(date_columns):  # 日期列
                    ws.column_dimensions[col_letter].width = 12
                else:  # 平均收益和趋势列
                    ws.column_dimensions[col_letter].width = 10

            return data_end_row + 20  # 为图表留出更多空间

        except Exception as e:
            self.logger.error(f"创建美化收益趋势图失败: {e}")
            import traceback
            traceback.print_exc()
            return start_row + 30

    def _create_combo_chart(self, ws, accounts_data, start_row):
        """创建组合图表（收益与粉丝对比）"""
        try:
            from openpyxl.chart import BarChart, Reference
            from openpyxl.styles import Font, PatternFill, Alignment, Border, Side

            # 添加美化的图表标题
            title_cell = ws.cell(row=start_row, column=1, value="📊 收益与粉丝关联分析")
            title_cell.font = Font(**self.fonts['chart_title'], color=self.colors['info'][1:])
            title_cell.fill = PatternFill(start_color=self.colors['light_gray'][1:],
                                        end_color=self.colors['light_gray'][1:], fill_type='solid')
            title_cell.alignment = Alignment(horizontal='center', vertical='center')
            title_cell.border = Border(
                bottom=Side(border_style='thin', color=self.colors['border'][1:])
            )
            ws.merge_cells(f'A{start_row}:L{start_row}')
            ws.row_dimensions[start_row].height = 25

            # 准备数据
            data_start_row = start_row + 2

            # 设置表头
            headers = ["账号", "累计收益(元)", "总粉丝", "收益/粉丝比", "效率等级"]
            for i, header in enumerate(headers):
                cell = ws.cell(row=data_start_row, column=i+1, value=header)
                cell.font = Font(**self.fonts['header'], color='FFFFFF')
                cell.fill = PatternFill(start_color=self.colors['info'][1:],
                                      end_color=self.colors['info_light'][1:], fill_type='solid')
                cell.alignment = Alignment(horizontal='center', vertical='center')
                cell.border = Border(
                    left=Side(border_style='thin', color='FFFFFF'),
                    right=Side(border_style='thin', color='FFFFFF'),
                    top=Side(border_style='thin', color='FFFFFF'),
                    bottom=Side(border_style='thin', color='FFFFFF')
                )

            # 添加数据（取前10个账号）
            top_accounts = accounts_data.nlargest(10, '累计收益') if len(accounts_data) > 10 else accounts_data

            # 定义效率等级
            def get_efficiency_level(ratio):
                if ratio >= 0.01:
                    return "🏆 超高效"
                elif ratio >= 0.005:
                    return "🥇 高效"
                elif ratio >= 0.001:
                    return "🥈 中效"
                elif ratio >= 0.0005:
                    return "🥉 低效"
                else:
                    return "📈 待提升"

            # 为数据行添加渐变背景色
            gradient_colors = [self.colors['light'], self.colors['light_gray']]

            for i, (_, row) in enumerate(top_accounts.iterrows()):
                data_row = data_start_row + 1 + i

                # 账号名称
                account_cell = ws.cell(row=data_row, column=1, value=row['账号'])

                # 累计收益
                total_income = float(row['累计收益']) if pd.notna(row['累计收益']) else 0
                income_cell = ws.cell(row=data_row, column=2, value=total_income)

                # 总粉丝
                total_fans = float(row['总粉丝']) if pd.notna(row['总粉丝']) else 0
                fans_cell = ws.cell(row=data_row, column=3, value=int(total_fans))

                # 收益/粉丝比
                ratio = total_income / total_fans if total_fans > 0 else 0
                ratio_cell = ws.cell(row=data_row, column=4, value=f"{ratio:.4f}")

                # 效率等级
                efficiency_cell = ws.cell(row=data_row, column=5, value=get_efficiency_level(ratio))

                # 设置数据行样式
                for cell in [account_cell, income_cell, fans_cell, ratio_cell, efficiency_cell]:
                    cell.font = Font(**self.fonts['body'])
                    cell.alignment = Alignment(horizontal='center', vertical='center')
                    cell.fill = PatternFill(start_color=gradient_colors[i % 2][1:],
                                          end_color=gradient_colors[i % 2][1:], fill_type='solid')
                    cell.border = Border(
                        left=Side(border_style='thin', color=self.colors['border'][1:]),
                        right=Side(border_style='thin', color=self.colors['border'][1:]),
                        top=Side(border_style='thin', color=self.colors['border'][1:]),
                        bottom=Side(border_style='thin', color=self.colors['border'][1:])
                    )

            # 创建组合图表（柱状图+折线图）
            chart = BarChart()
            chart.type = "col"
            chart.style = 42
            chart.title = "收益与粉丝关联分析"
            chart.title.tx.rich.p[0].r.rPr.sz = 1400
            chart.title.tx.rich.p[0].r.rPr.b = True

            chart.y_axis.title = '数值'
            chart.x_axis.title = '账号'

            # 设置数据范围
            data_end_row = data_start_row + len(top_accounts)

            # 收益数据（柱状图）
            income_data = Reference(ws, min_col=2, min_row=data_start_row, max_row=data_end_row, max_col=2)
            fans_data = Reference(ws, min_col=3, min_row=data_start_row, max_row=data_end_row, max_col=3)
            cats = Reference(ws, min_col=1, min_row=data_start_row+1, max_row=data_end_row, max_col=1)

            chart.add_data(income_data, titles_from_data=True)
            chart.add_data(fans_data, titles_from_data=True)
            chart.set_categories(cats)

            # 设置图表大小和位置
            chart.width = 18
            chart.height = 12
            chart.legend.position = 'r'

            # 添加图表到工作表
            ws.add_chart(chart, f"G{start_row+1}")

            # 设置列宽
            ws.column_dimensions['A'].width = 15
            ws.column_dimensions['B'].width = 12
            ws.column_dimensions['C'].width = 12
            ws.column_dimensions['D'].width = 12
            ws.column_dimensions['E'].width = 12

            return data_end_row + 4

        except Exception as e:
            self.logger.error(f"创建组合图表失败: {e}")
            import traceback
            traceback.print_exc()
            return start_row + 25

    def _create_kpi_dashboard(self, ws, accounts_data, seven_day_data, start_row):
        """创建KPI仪表盘"""
        try:
            from openpyxl.styles import Font, PatternFill, Alignment, Border, Side

            # 添加美化的仪表盘标题
            title_cell = ws.cell(row=start_row, column=1, value="📊 KPI 数据仪表盘")
            title_cell.font = Font(**self.fonts['chart_title'], color=self.colors['dark'][1:])
            title_cell.fill = PatternFill(start_color=self.colors['accent_lighter'][1:],
                                        end_color=self.colors['accent_lighter'][1:], fill_type='solid')
            title_cell.alignment = Alignment(horizontal='center', vertical='center')
            title_cell.border = Border(
                bottom=Side(border_style='medium', color=self.colors['accent'][1:])
            )
            ws.merge_cells(f'A{start_row}:P{start_row}')
            ws.row_dimensions[start_row].height = 30

            # 计算KPI指标
            total_accounts = len(accounts_data)
            total_income = accounts_data['累计收益'].sum() if '累计收益' in accounts_data.columns else 0
            total_fans = accounts_data['总粉丝'].sum() if '总粉丝' in accounts_data.columns else 0
            avg_income = total_income / total_accounts if total_accounts > 0 else 0
            avg_fans = total_fans / total_accounts if total_accounts > 0 else 0

            # 七天数据统计
            if seven_day_data is not None and not seven_day_data.empty:
                seven_day_total = seven_day_data.select_dtypes(include=[np.number]).sum().sum()
            else:
                seven_day_total = 0

            # KPI卡片数据
            kpi_cards = [
                {
                    'title': '📈 总收益',
                    'value': f'{total_income:.2f}',
                    'unit': '元',
                    'color': self.colors['success'],
                    'icon': '💰'
                },
                {
                    'title': '👥 总粉丝',
                    'value': f'{int(total_fans):,}',
                    'unit': '人',
                    'color': self.colors['info'],
                    'icon': '🌟'
                },
                {
                    'title': '📊 账号数量',
                    'value': str(total_accounts),
                    'unit': '个',
                    'color': self.colors['primary'],
                    'icon': '📱'
                },
                {
                    'title': '💵 平均收益',
                    'value': f'{avg_income:.2f}',
                    'unit': '元/账号',
                    'color': self.colors['warning'],
                    'icon': '📊'
                },
                {
                    'title': '👤 平均粉丝',
                    'value': f'{int(avg_fans):,}',
                    'unit': '人/账号',
                    'color': self.colors['secondary'],
                    'icon': '📈'
                },
                {
                    'title': '📅 七天收益',
                    'value': f'{seven_day_total:.2f}',
                    'unit': '元',
                    'color': self.colors['accent'],
                    'icon': '🗓️'
                }
            ]

            # 创建KPI卡片布局（3列2行）
            card_start_row = start_row + 2
            cards_per_row = 3
            card_width = 5  # 每个卡片占5列
            card_height = 4  # 每个卡片占4行

            for i, kpi in enumerate(kpi_cards):
                row_offset = (i // cards_per_row) * (card_height + 1)
                col_offset = (i % cards_per_row) * card_width

                card_row = card_start_row + row_offset
                card_col = 1 + col_offset

                # 创建卡片背景
                for r in range(card_height):
                    for c in range(card_width):
                        cell = ws.cell(row=card_row + r, column=card_col + c)
                        cell.fill = PatternFill(start_color=kpi['color'][1:],
                                              end_color=kpi['color'][1:], fill_type='solid')
                        cell.border = Border(
                            left=Side(border_style='thin', color='FFFFFF'),
                            right=Side(border_style='thin', color='FFFFFF'),
                            top=Side(border_style='thin', color='FFFFFF'),
                            bottom=Side(border_style='thin', color='FFFFFF')
                        )

                # 卡片标题
                title_cell = ws.cell(row=card_row, column=card_col)
                title_cell.value = f"{kpi['icon']} {kpi['title']}"
                title_cell.font = Font(**self.fonts['header'], color='FFFFFF')
                title_cell.alignment = Alignment(horizontal='center', vertical='center')
                ws.merge_cells(f'{chr(64+card_col)}{card_row}:{chr(64+card_col+card_width-1)}{card_row}')

                # 数值
                value_cell = ws.cell(row=card_row + 1, column=card_col)
                value_cell.value = kpi['value']
                value_cell.font = Font(name='Microsoft YaHei UI', size=20, bold=True, color='FFFFFF')
                value_cell.alignment = Alignment(horizontal='center', vertical='center')
                ws.merge_cells(f'{chr(64+card_col)}{card_row+1}:{chr(64+card_col+card_width-1)}{card_row+1}')

                # 单位
                unit_cell = ws.cell(row=card_row + 2, column=card_col)
                unit_cell.value = kpi['unit']
                unit_cell.font = Font(**self.fonts['body'], color='FFFFFF')
                unit_cell.alignment = Alignment(horizontal='center', vertical='center')
                ws.merge_cells(f'{chr(64+card_col)}{card_row+2}:{chr(64+card_col+card_width-1)}{card_row+2}')

                # 设置行高
                ws.row_dimensions[card_row].height = 20
                ws.row_dimensions[card_row + 1].height = 30
                ws.row_dimensions[card_row + 2].height = 15

            # 添加数据分析摘要
            summary_row = card_start_row + (len(kpi_cards) // cards_per_row + 1) * (card_height + 1) + 2

            summary_cell = ws.cell(row=summary_row, column=1, value="📋 数据分析摘要")
            summary_cell.font = Font(**self.fonts['subtitle'], color=self.colors['dark'][1:])
            summary_cell.fill = PatternFill(start_color=self.colors['light_gray'][1:],
                                          end_color=self.colors['light_gray'][1:], fill_type='solid')
            summary_cell.alignment = Alignment(horizontal='center', vertical='center')
            ws.merge_cells(f'A{summary_row}:P{summary_row}')
            ws.row_dimensions[summary_row].height = 25

            # 生成分析建议
            suggestions = []
            if avg_income > 100:
                suggestions.append("✅ 平均收益表现良好，继续保持优质内容输出")
            else:
                suggestions.append("⚠️ 平均收益偏低，建议优化内容质量和发布策略")

            if avg_fans > 10000:
                suggestions.append("✅ 粉丝基础扎实，可考虑增加互动和变现")
            else:
                suggestions.append("📈 粉丝数量有待提升，建议加强推广和内容优化")

            if total_accounts > 5:
                suggestions.append("✅ 账号矩阵规模良好，注意维护账号质量")
            else:
                suggestions.append("🔄 可考虑适当扩展账号数量以提升整体收益")

            # 添加建议文本
            for i, suggestion in enumerate(suggestions):
                suggestion_row = summary_row + 2 + i
                suggestion_cell = ws.cell(row=suggestion_row, column=1, value=suggestion)
                suggestion_cell.font = Font(**self.fonts['body'])
                suggestion_cell.alignment = Alignment(horizontal='left', vertical='center')
                ws.merge_cells(f'A{suggestion_row}:P{suggestion_row}')
                ws.row_dimensions[suggestion_row].height = 20

            return summary_row + len(suggestions) + 2

        except Exception as e:
            self.logger.error(f"创建KPI仪表盘失败: {e}")
            import traceback
            traceback.print_exc()
            return start_row + 20

    def _create_simple_income_chart(self, ws, accounts_data, start_row):
        """创建简化的收益对比表格"""
        try:
            from openpyxl.styles import Font, PatternFill, Alignment, Border, Side

            # 添加标题
            title_cell = ws.cell(row=start_row, column=1, value="💰 账号收益对比")
            title_cell.font = Font(**self.fonts['chart_title'], color=self.colors['primary'][1:])
            title_cell.fill = PatternFill(start_color=self.colors['light_gray'][1:],
                                        end_color=self.colors['light_gray'][1:], fill_type='solid')
            title_cell.alignment = Alignment(horizontal='center', vertical='center')
            ws.merge_cells(f'A{start_row}:F{start_row}')
            ws.row_dimensions[start_row].height = 25

            # 准备数据
            data_start_row = start_row + 2

            # 设置表头
            headers = ["排名", "账号", "累计收益(元)", "昨日收益(元)", "收益等级"]
            for i, header in enumerate(headers):
                cell = ws.cell(row=data_start_row, column=i+1, value=header)
                cell.font = Font(**self.fonts['header'], color='FFFFFF')
                cell.fill = PatternFill(start_color=self.colors['primary'][1:],
                                      end_color=self.colors['primary'][1:], fill_type='solid')
                cell.alignment = Alignment(horizontal='center', vertical='center')
                cell.border = Border(
                    left=Side(border_style='thin', color='FFFFFF'),
                    right=Side(border_style='thin', color='FFFFFF'),
                    top=Side(border_style='thin', color='FFFFFF'),
                    bottom=Side(border_style='thin', color='FFFFFF')
                )

            # 添加数据（取前10个账号）
            top_accounts = accounts_data.nlargest(10, '累计收益') if len(accounts_data) > 10 else accounts_data

            def get_income_level(income):
                if income >= 1000:
                    return "🏆 高收益"
                elif income >= 500:
                    return "🥇 中收益"
                elif income >= 100:
                    return "🥈 低收益"
                else:
                    return "🥉 起步"

            for i, (_, row) in enumerate(top_accounts.iterrows()):
                data_row = data_start_row + 1 + i

                # 排名
                rank_cell = ws.cell(row=data_row, column=1, value=f"第{i+1}名")

                # 账号名称
                account_cell = ws.cell(row=data_row, column=2, value=row['账号'])

                # 累计收益
                total_income = float(row['累计收益']) if pd.notna(row['累计收益']) else 0
                income_cell = ws.cell(row=data_row, column=3, value=f"{total_income:.2f}")

                # 昨日收益
                daily_income = float(row.get('昨日收益', 0)) if pd.notna(row.get('昨日收益', 0)) else 0
                daily_cell = ws.cell(row=data_row, column=4, value=f"{daily_income:.2f}")

                # 收益等级
                level_cell = ws.cell(row=data_row, column=5, value=get_income_level(total_income))

                # 设置数据行样式
                for cell in [rank_cell, account_cell, income_cell, daily_cell, level_cell]:
                    cell.font = Font(**self.fonts['body'])
                    cell.alignment = Alignment(horizontal='center', vertical='center')
                    if i % 2 == 0:
                        cell.fill = PatternFill(start_color='F8F9FA', end_color='F8F9FA', fill_type='solid')
                    cell.border = Border(
                        left=Side(border_style='thin', color=self.colors['border'][1:]),
                        right=Side(border_style='thin', color=self.colors['border'][1:]),
                        top=Side(border_style='thin', color=self.colors['border'][1:]),
                        bottom=Side(border_style='thin', color=self.colors['border'][1:])
                    )

            # 设置列宽
            ws.column_dimensions['A'].width = 8
            ws.column_dimensions['B'].width = 15
            ws.column_dimensions['C'].width = 12
            ws.column_dimensions['D'].width = 12
            ws.column_dimensions['E'].width = 12

            return data_start_row + len(top_accounts) + 3

        except Exception as e:
            self.logger.error(f"创建简化收益图表失败: {e}")
            return start_row + 15

    def _create_simple_fans_table(self, ws, accounts_data, start_row):
        """创建简化的粉丝分布表格"""
        try:
            from openpyxl.styles import Font, PatternFill, Alignment, Border, Side

            # 添加标题
            title_cell = ws.cell(row=start_row, column=1, value="👥 粉丝分布统计")
            title_cell.font = Font(**self.fonts['chart_title'], color=self.colors['secondary'][1:])
            title_cell.fill = PatternFill(start_color=self.colors['light_gray'][1:],
                                        end_color=self.colors['light_gray'][1:], fill_type='solid')
            title_cell.alignment = Alignment(horizontal='center', vertical='center')
            ws.merge_cells(f'A{start_row}:F{start_row}')
            ws.row_dimensions[start_row].height = 25

            # 准备数据
            data_start_row = start_row + 2

            # 设置表头
            headers = ["排名", "账号", "粉丝数", "占比(%)", "粉丝等级"]
            for i, header in enumerate(headers):
                cell = ws.cell(row=data_start_row, column=i+1, value=header)
                cell.font = Font(**self.fonts['header'], color='FFFFFF')
                cell.fill = PatternFill(start_color=self.colors['secondary'][1:],
                                      end_color=self.colors['secondary'][1:], fill_type='solid')
                cell.alignment = Alignment(horizontal='center', vertical='center')
                cell.border = Border(
                    left=Side(border_style='thin', color='FFFFFF'),
                    right=Side(border_style='thin', color='FFFFFF'),
                    top=Side(border_style='thin', color='FFFFFF'),
                    bottom=Side(border_style='thin', color='FFFFFF')
                )

            # 添加数据（取前10个账号）
            top_fans = accounts_data.nlargest(10, '总粉丝') if len(accounts_data) > 10 else accounts_data
            total_fans = accounts_data['总粉丝'].sum() if '总粉丝' in accounts_data.columns else 1

            def get_fans_level(fans_count):
                if fans_count >= 100000:
                    return "🌟 大V"
                elif fans_count >= 50000:
                    return "⭐ 中V"
                elif fans_count >= 10000:
                    return "✨ 小V"
                elif fans_count >= 1000:
                    return "💫 新星"
                else:
                    return "🌱 萌新"

            for i, (_, row) in enumerate(top_fans.iterrows()):
                data_row = data_start_row + 1 + i

                # 排名
                rank_cell = ws.cell(row=data_row, column=1, value=f"第{i+1}名")

                # 账号名称
                account_cell = ws.cell(row=data_row, column=2, value=row['账号'])

                # 粉丝数
                fans_count = float(row['总粉丝']) if pd.notna(row['总粉丝']) else 0
                fans_cell = ws.cell(row=data_row, column=3, value=f"{int(fans_count):,}")

                # 占比
                percentage = (fans_count / total_fans * 100) if total_fans > 0 else 0
                percentage_cell = ws.cell(row=data_row, column=4, value=f"{percentage:.1f}%")

                # 粉丝等级
                level_cell = ws.cell(row=data_row, column=5, value=get_fans_level(fans_count))

                # 设置数据行样式
                for cell in [rank_cell, account_cell, fans_cell, percentage_cell, level_cell]:
                    cell.font = Font(**self.fonts['body'])
                    cell.alignment = Alignment(horizontal='center', vertical='center')
                    if i % 2 == 0:
                        cell.fill = PatternFill(start_color='F8F9FA', end_color='F8F9FA', fill_type='solid')
                    cell.border = Border(
                        left=Side(border_style='thin', color=self.colors['border'][1:]),
                        right=Side(border_style='thin', color=self.colors['border'][1:]),
                        top=Side(border_style='thin', color=self.colors['border'][1:]),
                        bottom=Side(border_style='thin', color=self.colors['border'][1:])
                    )

            # 设置列宽
            ws.column_dimensions['A'].width = 8
            ws.column_dimensions['B'].width = 15
            ws.column_dimensions['C'].width = 12
            ws.column_dimensions['D'].width = 10
            ws.column_dimensions['E'].width = 12

            return data_start_row + len(top_fans) + 3

        except Exception as e:
            self.logger.error(f"创建简化粉丝表格失败: {e}")
            return start_row + 15

    def _create_simple_seven_day_table(self, ws, seven_day_data, start_row):
        """创建简化的七天收益表格"""
        try:
            from openpyxl.styles import Font, PatternFill, Alignment, Border, Side

            # 添加标题
            title_cell = ws.cell(row=start_row, column=1, value="📈 七天收益趋势")
            title_cell.font = Font(**self.fonts['chart_title'], color=self.colors['accent'][1:])
            title_cell.fill = PatternFill(start_color=self.colors['light_gray'][1:],
                                        end_color=self.colors['light_gray'][1:], fill_type='solid')
            title_cell.alignment = Alignment(horizontal='center', vertical='center')
            ws.merge_cells(f'A{start_row}:H{start_row}')
            ws.row_dimensions[start_row].height = 25

            # 准备数据
            data_start_row = start_row + 2

            # 获取日期列（排除账号和总收益列）
            date_columns = [col for col in seven_day_data.columns if col not in ['账号', '七天总收益']]

            # 设置表头
            headers = ["账号"] + date_columns[:7] + ["总收益", "平均收益"]  # 限制最多7天
            for i, header in enumerate(headers):
                cell = ws.cell(row=data_start_row, column=i+1, value=header)
                cell.font = Font(**self.fonts['header'], color='FFFFFF')
                cell.fill = PatternFill(start_color=self.colors['accent'][1:],
                                      end_color=self.colors['accent'][1:], fill_type='solid')
                cell.alignment = Alignment(horizontal='center', vertical='center')
                cell.border = Border(
                    left=Side(border_style='thin', color='FFFFFF'),
                    right=Side(border_style='thin', color='FFFFFF'),
                    top=Side(border_style='thin', color='FFFFFF'),
                    bottom=Side(border_style='thin', color='FFFFFF')
                )

            # 添加数据（取前8个账号）
            top_accounts = seven_day_data.head(8)

            for i, (_, row) in enumerate(top_accounts.iterrows()):
                data_row = data_start_row + 1 + i

                # 账号名称
                account_cell = ws.cell(row=data_row, column=1, value=row['账号'])

                # 日期数据
                daily_values = []
                for j, date_col in enumerate(date_columns[:7]):  # 限制最多7天
                    value = float(row[date_col]) if pd.notna(row[date_col]) else 0
                    daily_values.append(value)
                    value_cell = ws.cell(row=data_row, column=j+2, value=f"{value:.2f}")
                    value_cell.font = Font(**self.fonts['body'])
                    value_cell.alignment = Alignment(horizontal='center', vertical='center')
                    if i % 2 == 0:
                        value_cell.fill = PatternFill(start_color='F8F9FA', end_color='F8F9FA', fill_type='solid')
                    value_cell.border = Border(
                        left=Side(border_style='thin', color=self.colors['border'][1:]),
                        right=Side(border_style='thin', color=self.colors['border'][1:]),
                        top=Side(border_style='thin', color=self.colors['border'][1:]),
                        bottom=Side(border_style='thin', color=self.colors['border'][1:])
                    )

                # 总收益
                total_income = sum(daily_values)
                total_cell = ws.cell(row=data_row, column=len(date_columns[:7])+2, value=f"{total_income:.2f}")

                # 平均收益
                avg_income = total_income / len(daily_values) if daily_values else 0
                avg_cell = ws.cell(row=data_row, column=len(date_columns[:7])+3, value=f"{avg_income:.2f}")

                # 设置样式
                for cell in [account_cell, total_cell, avg_cell]:
                    cell.font = Font(**self.fonts['body'])
                    cell.alignment = Alignment(horizontal='center', vertical='center')
                    if i % 2 == 0:
                        cell.fill = PatternFill(start_color='F8F9FA', end_color='F8F9FA', fill_type='solid')
                    cell.border = Border(
                        left=Side(border_style='thin', color=self.colors['border'][1:]),
                        right=Side(border_style='thin', color=self.colors['border'][1:]),
                        top=Side(border_style='thin', color=self.colors['border'][1:]),
                        bottom=Side(border_style='thin', color=self.colors['border'][1:])
                    )

            # 设置列宽
            for i in range(len(headers)):
                col_letter = chr(65 + i)  # A, B, C, ...
                if i == 0:  # 账号列
                    ws.column_dimensions[col_letter].width = 15
                else:  # 其他列
                    ws.column_dimensions[col_letter].width = 10

            return data_start_row + len(top_accounts) + 3

        except Exception as e:
            self.logger.error(f"创建简化七天收益表格失败: {e}")
            return start_row + 15

    def _create_income_bar_chart_enhanced(self, ws, accounts_data, start_row):
        """创建增强版收益对比柱状图"""
        try:
            from openpyxl.chart import BarChart, Reference
            from openpyxl.styles import Font, PatternFill, Alignment

            # 添加图表标题
            title_cell = ws.cell(row=start_row, column=1, value="📊 收益对比柱状图")
            title_cell.font = Font(**self.fonts['chart_title'], color=self.colors['primary'][1:])
            title_cell.fill = PatternFill(start_color=self.colors['light_gray'][1:],
                                        end_color=self.colors['light_gray'][1:], fill_type='solid')
            title_cell.alignment = Alignment(horizontal='center', vertical='center')
            ws.merge_cells(f'A{start_row}:H{start_row}')
            ws.row_dimensions[start_row].height = 25

            # 准备图表数据
            data_start_row = start_row + 2

            # 获取前8个账号的数据
            top_accounts = accounts_data.nlargest(8, '累计收益') if len(accounts_data) > 8 else accounts_data

            # 添加图表数据到工作表
            headers = ["账号", "累计收益"]
            for i, header in enumerate(headers):
                cell = ws.cell(row=data_start_row, column=i+1, value=header)
                cell.font = Font(**self.fonts['header'])
                cell.alignment = Alignment(horizontal='center', vertical='center')

            for i, (_, row) in enumerate(top_accounts.iterrows()):
                data_row = data_start_row + 1 + i
                ws.cell(row=data_row, column=1, value=row['账号'])
                income = float(row['累计收益']) if pd.notna(row['累计收益']) else 0
                ws.cell(row=data_row, column=2, value=income)

            # 创建柱状图
            chart = BarChart()
            chart.type = "col"
            chart.style = 10
            chart.title = "账号收益对比"
            chart.y_axis.title = '收益金额 (元)'
            chart.x_axis.title = '账号'

            # 设置数据范围
            data_end_row = data_start_row + len(top_accounts)
            data = Reference(ws, min_col=2, min_row=data_start_row, max_row=data_end_row, max_col=2)
            cats = Reference(ws, min_col=1, min_row=data_start_row+1, max_row=data_end_row, max_col=1)

            chart.add_data(data, titles_from_data=True)
            chart.set_categories(cats)

            # 设置图表大小和位置 - 优化大小避免遮挡
            chart.width = 12  # 减小宽度
            chart.height = 8   # 减小高度
            chart.legend = None

            # 添加图表到工作表 - 修复位置，放在数据表格右侧
            chart_position = f"D{data_start_row}"  # 与数据表格同一行开始，但在右侧列
            ws.add_chart(chart, chart_position)

            # 确保返回值考虑图表高度，避免与下一个内容重叠
            chart_end_row = data_start_row + 12  # 图表大约占用12行
            return max(data_end_row + 3, chart_end_row + 3)

        except Exception as e:
            self.logger.error(f"创建增强版收益柱状图失败: {e}")
            return start_row + 15

    def _create_fans_pie_chart_enhanced(self, ws, accounts_data, start_row):
        """创建增强版粉丝分布饼图"""
        try:
            from openpyxl.chart import PieChart, Reference
            from openpyxl.styles import Font, PatternFill, Alignment

            # 添加图表标题
            title_cell = ws.cell(row=start_row, column=1, value="🥧 粉丝分布饼图")
            title_cell.font = Font(**self.fonts['chart_title'], color=self.colors['secondary'][1:])
            title_cell.fill = PatternFill(start_color=self.colors['light_gray'][1:],
                                        end_color=self.colors['light_gray'][1:], fill_type='solid')
            title_cell.alignment = Alignment(horizontal='center', vertical='center')
            ws.merge_cells(f'A{start_row}:H{start_row}')
            ws.row_dimensions[start_row].height = 25

            # 准备图表数据
            data_start_row = start_row + 2

            # 获取前6个账号的数据
            top_fans = accounts_data.nlargest(6, '总粉丝') if len(accounts_data) > 6 else accounts_data

            # 添加图表数据到工作表
            headers = ["账号", "粉丝数"]
            for i, header in enumerate(headers):
                cell = ws.cell(row=data_start_row, column=i+1, value=header)
                cell.font = Font(**self.fonts['header'])
                cell.alignment = Alignment(horizontal='center', vertical='center')

            for i, (_, row) in enumerate(top_fans.iterrows()):
                data_row = data_start_row + 1 + i
                ws.cell(row=data_row, column=1, value=row['账号'])
                fans = float(row['总粉丝']) if pd.notna(row['总粉丝']) else 0
                ws.cell(row=data_row, column=2, value=int(fans))

            # 创建饼图
            chart = PieChart()
            chart.title = "粉丝分布"

            # 设置数据范围
            data_end_row = data_start_row + len(top_fans)
            data = Reference(ws, min_col=2, min_row=data_start_row+1, max_row=data_end_row, max_col=2)
            cats = Reference(ws, min_col=1, min_row=data_start_row+1, max_row=data_end_row, max_col=1)

            chart.add_data(data)
            chart.set_categories(cats)

            # 设置图表大小和位置 - 优化大小避免遮挡
            chart.width = 12  # 减小宽度
            chart.height = 8   # 减小高度

            # 添加图表到工作表 - 修复位置，放在数据表格右侧
            chart_position = f"D{data_start_row}"  # 与数据表格同一行开始，但在右侧列
            ws.add_chart(chart, chart_position)

            # 确保返回值考虑图表高度，避免与下一个内容重叠
            chart_end_row = data_start_row + 12  # 图表大约占用12行
            return max(data_end_row + 3, chart_end_row + 3)

        except Exception as e:
            self.logger.error(f"创建增强版粉丝饼图失败: {e}")
            return start_row + 15

    def _create_income_trend_chart_enhanced(self, ws, seven_day_data, start_row):
        """创建增强版收益趋势图"""
        try:
            from openpyxl.chart import LineChart, Reference
            from openpyxl.styles import Font, PatternFill, Alignment

            # 添加图表标题
            title_cell = ws.cell(row=start_row, column=1, value="📈 收益趋势图")
            title_cell.font = Font(**self.fonts['chart_title'], color=self.colors['accent'][1:])
            title_cell.fill = PatternFill(start_color=self.colors['light_gray'][1:],
                                        end_color=self.colors['light_gray'][1:], fill_type='solid')
            title_cell.alignment = Alignment(horizontal='center', vertical='center')
            ws.merge_cells(f'A{start_row}:H{start_row}')
            ws.row_dimensions[start_row].height = 25

            # 准备图表数据
            data_start_row = start_row + 2

            # 获取日期列（排除账号和总收益列）
            date_columns = [col for col in seven_day_data.columns if col not in ['账号', '七天总收益']]

            if not date_columns:
                return start_row + 10

            # 取前5个账号
            top_accounts = seven_day_data.head(5)

            # 添加表头
            headers = ["账号"] + date_columns[:7]  # 最多7天
            for i, header in enumerate(headers):
                cell = ws.cell(row=data_start_row, column=i+1, value=header)
                cell.font = Font(**self.fonts['header'])
                cell.alignment = Alignment(horizontal='center', vertical='center')

            # 添加数据
            for i, (_, row) in enumerate(top_accounts.iterrows()):
                data_row = data_start_row + 1 + i
                ws.cell(row=data_row, column=1, value=row['账号'])

                for j, date_col in enumerate(date_columns[:7]):
                    value = float(row[date_col]) if pd.notna(row[date_col]) else 0
                    ws.cell(row=data_row, column=j+2, value=value)

            # 创建折线图
            chart = LineChart()
            chart.title = "七天收益趋势"
            chart.y_axis.title = '收益金额 (元)'
            chart.x_axis.title = '日期'
            chart.style = 13

            # 设置数据范围
            data_end_row = data_start_row + len(top_accounts)

            # 添加数据系列
            for i in range(len(top_accounts)):
                account_row = data_start_row + 1 + i
                data = Reference(ws, min_col=2, min_row=account_row, max_row=account_row, max_col=len(date_columns[:7])+1)
                chart.add_data(data)

            # 设置类别（日期）
            cats = Reference(ws, min_col=2, min_row=data_start_row, max_row=data_start_row, max_col=len(date_columns[:7])+1)
            chart.set_categories(cats)

            # 设置图表大小和位置 - 优化大小避免遮挡
            chart.width = 14  # 适中的宽度
            chart.height = 8   # 减小高度

            # 添加图表到工作表 - 修复位置，放在数据表格下方，留出足够空间
            chart_row = data_end_row + 3  # 在数据表格下方留出3行空间
            chart_position = f"A{chart_row}"
            ws.add_chart(chart, chart_position)

            # 确保返回值考虑图表高度，避免与下一个内容重叠
            chart_end_row = chart_row + 12  # 图表大约占用12行
            return chart_end_row + 3

        except Exception as e:
            self.logger.error(f"创建增强版收益趋势图失败: {e}")
            return start_row + 15

    def _create_income_section_optimized(self, ws, accounts_data, start_row):
        """创建优化的收益分析区域 - 避免重叠"""
        try:
            from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
            from openpyxl.chart import BarChart, Reference

            # 区域标题
            title_cell = ws.cell(row=start_row, column=1, value="💰 收益对比分析区域")
            title_cell.font = Font(**self.fonts['chart_title'], color=self.colors['primary'][1:])
            title_cell.fill = PatternFill(start_color=self.colors['light_gray'][1:],
                                        end_color=self.colors['light_gray'][1:], fill_type='solid')
            title_cell.alignment = Alignment(horizontal='center', vertical='center')
            ws.merge_cells(f'A{start_row}:P{start_row}')
            ws.row_dimensions[start_row].height = 25

            # 数据表格区域 (A列到F列)
            table_start_row = start_row + 2

            # 表头
            headers = ["排名", "账号", "累计收益", "昨日收益", "收益等级"]
            for i, header in enumerate(headers):
                cell = ws.cell(row=table_start_row, column=i+1, value=header)
                cell.font = Font(**self.fonts['header'], color='FFFFFF')
                cell.fill = PatternFill(start_color=self.colors['primary'][1:],
                                      end_color=self.colors['primary'][1:], fill_type='solid')
                cell.alignment = Alignment(horizontal='center', vertical='center')

            # 数据行 - 限制为4行，避免表格过长
            top_accounts = accounts_data.nlargest(4, '累计收益') if len(accounts_data) > 4 else accounts_data

            def get_income_level(income):
                if income >= 1000:
                    return "🏆 高收益"
                elif income >= 500:
                    return "🥇 中收益"
                elif income >= 100:
                    return "🥈 低收益"
                else:
                    return "🥉 起步"

            for i, (_, row) in enumerate(top_accounts.iterrows()):
                data_row = table_start_row + 1 + i

                # 数据
                ws.cell(row=data_row, column=1, value=f"第{i+1}名")
                ws.cell(row=data_row, column=2, value=row['账号'])
                total_income = float(row['累计收益']) if pd.notna(row['累计收益']) else 0
                ws.cell(row=data_row, column=3, value=total_income)
                daily_income = float(row.get('昨日收益', 0)) if pd.notna(row.get('昨日收益', 0)) else 0
                ws.cell(row=data_row, column=4, value=daily_income)
                ws.cell(row=data_row, column=5, value=get_income_level(total_income))

            # 图表区域 (H列到P列) - 确保不与数据重叠
            try:
                chart = BarChart()
                chart.type = "col"
                chart.style = 10
                chart.title = "收益对比"
                chart.y_axis.title = '收益金额 (元)'
                chart.x_axis.title = '账号'

                # 数据范围
                data_end_row = table_start_row + len(top_accounts)
                data = Reference(ws, min_col=3, min_row=table_start_row, max_row=data_end_row, max_col=3)
                cats = Reference(ws, min_col=2, min_row=table_start_row+1, max_row=data_end_row, max_col=2)

                chart.add_data(data, titles_from_data=True)
                chart.set_categories(cats)

                # 图表大小和位置 - 放在数据表格下方，避免重叠
                chart.width = 12
                chart.height = 6
                chart.legend = None

                # 图表位置：数据表格下方，留出2行间距
                chart_row = data_end_row + 2
                ws.add_chart(chart, f"A{chart_row}")

            except Exception as e:
                self.logger.warning(f"创建收益图表失败: {e}")

            # 设置列宽
            ws.column_dimensions['A'].width = 8
            ws.column_dimensions['B'].width = 15
            ws.column_dimensions['C'].width = 12
            ws.column_dimensions['D'].width = 12
            ws.column_dimensions['E'].width = 12

            # 返回下一个区域的起始行，确保足够间距
            # 考虑图表高度，确保下一个区域不会与图表重叠
            chart_end_row = chart_row + 10  # 图表大约占用10行
            return chart_end_row + 3

        except Exception as e:
            self.logger.error(f"创建收益分析区域失败: {e}")
            return start_row + 20

    def _create_fans_section_optimized(self, ws, accounts_data, start_row):
        """创建优化的粉丝分析区域 - 避免重叠"""
        try:
            from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
            from openpyxl.chart import PieChart, Reference

            # 区域标题
            title_cell = ws.cell(row=start_row, column=1, value="👥 粉丝分布分析区域")
            title_cell.font = Font(**self.fonts['chart_title'], color=self.colors['secondary'][1:])
            title_cell.fill = PatternFill(start_color=self.colors['light_gray'][1:],
                                        end_color=self.colors['light_gray'][1:], fill_type='solid')
            title_cell.alignment = Alignment(horizontal='center', vertical='center')
            ws.merge_cells(f'A{start_row}:P{start_row}')
            ws.row_dimensions[start_row].height = 25

            # 数据表格区域 (A列到F列)
            table_start_row = start_row + 2

            # 表头
            headers = ["排名", "账号", "粉丝数", "占比(%)", "粉丝等级"]
            for i, header in enumerate(headers):
                cell = ws.cell(row=table_start_row, column=i+1, value=header)
                cell.font = Font(**self.fonts['header'], color='FFFFFF')
                cell.fill = PatternFill(start_color=self.colors['secondary'][1:],
                                      end_color=self.colors['secondary'][1:], fill_type='solid')
                cell.alignment = Alignment(horizontal='center', vertical='center')

            # 数据行 - 限制为4行，避免表格过长
            top_fans = accounts_data.nlargest(4, '总粉丝') if len(accounts_data) > 4 else accounts_data
            total_fans = accounts_data['总粉丝'].sum() if '总粉丝' in accounts_data.columns else 1

            def get_fans_level(fans_count):
                if fans_count >= 100000:
                    return "🌟 大V"
                elif fans_count >= 50000:
                    return "⭐ 中V"
                elif fans_count >= 10000:
                    return "✨ 小V"
                elif fans_count >= 1000:
                    return "💫 新星"
                else:
                    return "🌱 萌新"

            for i, (_, row) in enumerate(top_fans.iterrows()):
                data_row = table_start_row + 1 + i

                # 数据
                ws.cell(row=data_row, column=1, value=f"第{i+1}名")
                ws.cell(row=data_row, column=2, value=row['账号'])
                fans_count = float(row['总粉丝']) if pd.notna(row['总粉丝']) else 0
                ws.cell(row=data_row, column=3, value=int(fans_count))
                percentage = (fans_count / total_fans * 100) if total_fans > 0 else 0
                ws.cell(row=data_row, column=4, value=f"{percentage:.1f}%")
                ws.cell(row=data_row, column=5, value=get_fans_level(fans_count))

            # 图表区域 (H列到P列) - 确保不与数据重叠
            try:
                chart = PieChart()
                chart.title = "粉丝分布"

                # 数据范围
                data_end_row = table_start_row + len(top_fans)
                data = Reference(ws, min_col=3, min_row=table_start_row+1, max_row=data_end_row, max_col=3)
                cats = Reference(ws, min_col=2, min_row=table_start_row+1, max_row=data_end_row, max_col=2)

                chart.add_data(data)
                chart.set_categories(cats)

                # 图表大小和位置 - 放在数据表格下方，避免重叠
                chart.width = 10
                chart.height = 6

                # 图表位置：数据表格下方，留出2行间距
                chart_row = data_end_row + 2
                ws.add_chart(chart, f"A{chart_row}")

            except Exception as e:
                self.logger.warning(f"创建粉丝图表失败: {e}")

            # 设置列宽
            ws.column_dimensions['A'].width = 8
            ws.column_dimensions['B'].width = 15
            ws.column_dimensions['C'].width = 12
            ws.column_dimensions['D'].width = 10
            ws.column_dimensions['E'].width = 12

            # 返回下一个区域的起始行，确保足够间距
            # 考虑图表高度，确保下一个区域不会与图表重叠
            chart_end_row = chart_row + 10  # 图表大约占用10行
            return chart_end_row + 3

        except Exception as e:
            self.logger.error(f"创建粉丝分析区域失败: {e}")
            return start_row + 20

    def _create_trend_section_optimized(self, ws, seven_day_data, start_row):
        """创建优化的趋势分析区域 - 避免重叠"""
        try:
            from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
            from openpyxl.chart import LineChart, Reference

            # 区域标题
            title_cell = ws.cell(row=start_row, column=1, value="📈 七天收益趋势分析区域")
            title_cell.font = Font(**self.fonts['chart_title'], color=self.colors['accent'][1:])
            title_cell.fill = PatternFill(start_color=self.colors['light_gray'][1:],
                                        end_color=self.colors['light_gray'][1:], fill_type='solid')
            title_cell.alignment = Alignment(horizontal='center', vertical='center')
            ws.merge_cells(f'A{start_row}:P{start_row}')
            ws.row_dimensions[start_row].height = 25

            # 数据表格区域 (A列到P列，上半部分)
            table_start_row = start_row + 2

            # 获取日期列
            date_columns = [col for col in seven_day_data.columns if col not in ['账号', '七天总收益']]

            # 表头
            headers = ["账号"] + date_columns[:7] + ["平均收益", "趋势"]
            for i, header in enumerate(headers):
                cell = ws.cell(row=table_start_row, column=i+1, value=header)
                cell.font = Font(**self.fonts['header'], color='FFFFFF')
                cell.fill = PatternFill(start_color=self.colors['accent'][1:],
                                      end_color=self.colors['accent'][1:], fill_type='solid')
                cell.alignment = Alignment(horizontal='center', vertical='center')

            # 数据行 - 限制为3个账号，避免表格过长和图例过多
            top_accounts = seven_day_data.head(3)  # 只显示前3个账号

            for i, (_, row) in enumerate(top_accounts.iterrows()):
                data_row = table_start_row + 1 + i

                # 账号名称
                ws.cell(row=data_row, column=1, value=row['账号'])

                # 日期数据
                daily_values = []
                for j, date_col in enumerate(date_columns[:7]):
                    value = float(row[date_col]) if pd.notna(row[date_col]) else 0
                    daily_values.append(value)
                    ws.cell(row=data_row, column=j+2, value=value)

                # 平均收益
                avg_income = sum(daily_values) / len(daily_values) if daily_values else 0
                ws.cell(row=data_row, column=len(date_columns[:7])+2, value=f"{avg_income:.2f}")

                # 趋势
                if len(daily_values) >= 6:
                    first_half = sum(daily_values[:3]) / 3
                    second_half = sum(daily_values[-3:]) / 3
                    if second_half > first_half * 1.1:
                        trend = "📈 上升"
                    elif second_half < first_half * 0.9:
                        trend = "📉 下降"
                    else:
                        trend = "➡️ 平稳"
                else:
                    trend = "➡️ 平稳"

                ws.cell(row=data_row, column=len(date_columns[:7])+3, value=trend)

            # 图表区域 (A列到P列，下半部分) - 确保不与数据重叠
            data_end_row = table_start_row + len(top_accounts)
            chart_start_row = data_end_row + 3  # 在数据表格下方留出3行空间

            try:
                chart = LineChart()
                chart.title = "七天收益趋势"
                chart.style = 13
                chart.y_axis.title = '收益金额 (元)'
                chart.x_axis.title = '日期'

                # 为每个账号创建一条线
                for i in range(len(top_accounts)):
                    account_row = table_start_row + 1 + i
                    data = Reference(ws, min_col=2, min_row=account_row, max_row=account_row, max_col=len(date_columns[:7])+1)
                    chart.add_data(data)

                # 设置类别（日期）
                cats = Reference(ws, min_col=2, min_row=table_start_row, max_row=table_start_row, max_col=len(date_columns[:7])+1)
                chart.set_categories(cats)

                # 图表大小和位置 - 放在数据表格下方，减小尺寸
                chart.width = 12
                chart.height = 6
                chart.legend.position = 'r'

                # 图表位置：数据表格下方
                ws.add_chart(chart, f"A{chart_start_row}")

            except Exception as e:
                self.logger.warning(f"创建趋势图表失败: {e}")

            # 设置列宽
            for i in range(len(headers)):
                col_letter = chr(65 + i)
                if i == 0:  # 账号列
                    ws.column_dimensions[col_letter].width = 15
                elif i <= len(date_columns[:7]):  # 日期列
                    ws.column_dimensions[col_letter].width = 10
                else:  # 平均收益和趋势列
                    ws.column_dimensions[col_letter].width = 10

            # 返回下一个区域的起始行，确保足够间距
            return chart_start_row + 10  # 图表高度 + 额外间距

        except Exception as e:
            self.logger.error(f"创建趋势分析区域失败: {e}")
            return start_row + 25

    def _create_kpi_dashboard_optimized(self, ws, accounts_data, seven_day_data, start_row):
        """创建优化的KPI仪表盘 - 避免重叠"""
        try:
            from openpyxl.styles import Font, PatternFill, Alignment, Border, Side

            # 区域标题
            title_cell = ws.cell(row=start_row, column=1, value="📊 KPI数据仪表盘")
            title_cell.font = Font(**self.fonts['chart_title'], color=self.colors['dark'][1:])
            title_cell.fill = PatternFill(start_color=self.colors['accent_lighter'][1:],
                                        end_color=self.colors['accent_lighter'][1:], fill_type='solid')
            title_cell.alignment = Alignment(horizontal='center', vertical='center')
            ws.merge_cells(f'A{start_row}:P{start_row}')
            ws.row_dimensions[start_row].height = 30

            # 计算KPI指标
            total_accounts = len(accounts_data)
            total_income = accounts_data['累计收益'].sum() if '累计收益' in accounts_data.columns else 0
            total_fans = accounts_data['总粉丝'].sum() if '总粉丝' in accounts_data.columns else 0
            avg_income = total_income / total_accounts if total_accounts > 0 else 0
            avg_fans = total_fans / total_accounts if total_accounts > 0 else 0

            # 七天数据统计
            if seven_day_data is not None and not seven_day_data.empty:
                seven_day_total = seven_day_data.select_dtypes(include=[np.number]).sum().sum()
            else:
                seven_day_total = 0

            # 创建简化的KPI表格 (3列2行布局)
            kpi_start_row = start_row + 2

            # KPI数据
            kpi_data = [
                ["📈 总收益", f"{total_income:.2f} 元", "💰 平均收益", f"{avg_income:.2f} 元/账号"],
                ["👥 总粉丝", f"{int(total_fans):,} 人", "👤 平均粉丝", f"{int(avg_fans):,} 人/账号"],
                ["📊 账号数量", f"{total_accounts} 个", "📅 七天收益", f"{seven_day_total:.2f} 元"]
            ]

            # 创建KPI表格
            for i, row_data in enumerate(kpi_data):
                kpi_row = kpi_start_row + i * 2

                for j in range(0, len(row_data), 2):
                    # 标题和数值成对显示
                    title_col = j * 4 + 1  # A, I列
                    value_col = title_col + 1  # B, J列

                    # 标题
                    title_cell = ws.cell(row=kpi_row, column=title_col, value=row_data[j])
                    title_cell.font = Font(**self.fonts['header'], color='FFFFFF')
                    title_cell.fill = PatternFill(start_color=self.colors['primary'][1:],
                                                end_color=self.colors['primary'][1:], fill_type='solid')
                    title_cell.alignment = Alignment(horizontal='center', vertical='center')

                    # 数值
                    value_cell = ws.cell(row=kpi_row, column=value_col, value=row_data[j+1])
                    value_cell.font = Font(**self.fonts['body'], bold=True)
                    value_cell.fill = PatternFill(start_color=self.colors['light'][1:],
                                                end_color=self.colors['light'][1:], fill_type='solid')
                    value_cell.alignment = Alignment(horizontal='center', vertical='center')

                    # 合并单元格
                    ws.merge_cells(f'{chr(64+title_col)}{kpi_row}:{chr(64+title_col+2)}{kpi_row}')
                    ws.merge_cells(f'{chr(64+value_col)}{kpi_row}:{chr(64+value_col+2)}{kpi_row}')

            # 添加数据分析摘要
            summary_row = kpi_start_row + len(kpi_data) * 2 + 2

            summary_cell = ws.cell(row=summary_row, column=1, value="📋 数据分析摘要")
            summary_cell.font = Font(**self.fonts['subtitle'], color=self.colors['dark'][1:])
            summary_cell.fill = PatternFill(start_color=self.colors['light_gray'][1:],
                                          end_color=self.colors['light_gray'][1:], fill_type='solid')
            summary_cell.alignment = Alignment(horizontal='center', vertical='center')
            ws.merge_cells(f'A{summary_row}:P{summary_row}')

            # 生成分析建议
            suggestions = []
            if avg_income > 100:
                suggestions.append("✅ 平均收益表现良好，继续保持优质内容输出")
            else:
                suggestions.append("⚠️ 平均收益偏低，建议优化内容质量和发布策略")

            if avg_fans > 10000:
                suggestions.append("✅ 粉丝基础扎实，可考虑增加互动和变现")
            else:
                suggestions.append("📈 粉丝数量有待提升，建议加强推广和内容优化")

            if total_accounts > 5:
                suggestions.append("✅ 账号矩阵规模良好，注意维护账号质量")
            else:
                suggestions.append("🔄 可考虑适当扩展账号数量以提升整体收益")

            # 添加建议文本
            for i, suggestion in enumerate(suggestions):
                suggestion_row = summary_row + 2 + i
                suggestion_cell = ws.cell(row=suggestion_row, column=1, value=suggestion)
                suggestion_cell.font = Font(**self.fonts['body'])
                suggestion_cell.alignment = Alignment(horizontal='left', vertical='center')
                ws.merge_cells(f'A{suggestion_row}:P{suggestion_row}')

            return summary_row + len(suggestions) + 2

        except Exception as e:
            self.logger.error(f"创建KPI仪表盘失败: {e}")
            return start_row + 20

    def _create_income_section_vertical(self, ws, accounts_data, start_row):
        """创建收益分析区域 - 垂直布局，彻底避免重叠"""
        try:
            from openpyxl.styles import Font, PatternFill, Alignment
            from openpyxl.chart import BarChart, Reference

            # 区域标题
            title_cell = ws.cell(row=start_row, column=1, value="💰 账号收益对比分析")
            title_cell.font = Font(**self.fonts['chart_title'], color=self.colors['primary'][1:])
            title_cell.fill = PatternFill(start_color=self.colors['light_gray'][1:],
                                        end_color=self.colors['light_gray'][1:], fill_type='solid')
            title_cell.alignment = Alignment(horizontal='center', vertical='center')
            ws.merge_cells(f'A{start_row}:P{start_row}')
            ws.row_dimensions[start_row].height = 25

            # 数据表格 (第4-7行)
            table_start_row = start_row + 1
            headers = ["账号", "累计收益", "昨日收益", "排名"]
            for i, header in enumerate(headers):
                cell = ws.cell(row=table_start_row, column=i+1, value=header)
                cell.font = Font(**self.fonts['header'], color='FFFFFF')
                cell.fill = PatternFill(start_color=self.colors['primary'][1:],
                                      end_color=self.colors['primary'][1:], fill_type='solid')
                cell.alignment = Alignment(horizontal='center', vertical='center')

            # 数据行 (限制为3行)
            top_accounts = accounts_data.nlargest(3, '累计收益') if len(accounts_data) > 3 else accounts_data
            for i, (_, row) in enumerate(top_accounts.iterrows()):
                data_row = table_start_row + 1 + i
                ws.cell(row=data_row, column=1, value=row['账号'])
                income = float(row['累计收益']) if pd.notna(row['累计收益']) else 0
                ws.cell(row=data_row, column=2, value=income)
                yesterday_income = float(row['昨日收益']) if pd.notna(row['昨日收益']) else 0
                ws.cell(row=data_row, column=3, value=yesterday_income)
                ws.cell(row=data_row, column=4, value=f"第{i+1}名")

            # 图表 (第8-20行) - 固定位置，确保不重叠
            chart_row = start_row + 7
            try:
                chart = BarChart()
                chart.title = "收益对比"
                chart.type = "col"
                chart.style = 10

                data_end_row = table_start_row + len(top_accounts)
                data = Reference(ws, min_col=2, min_row=table_start_row+1, max_row=data_end_row, max_col=2)
                cats = Reference(ws, min_col=1, min_row=table_start_row+1, max_row=data_end_row, max_col=1)

                chart.add_data(data, titles_from_data=False)
                chart.set_categories(cats)

                chart.width = 10
                chart.height = 8
                chart.legend = None

                ws.add_chart(chart, f"A{chart_row}")
            except Exception as e:
                self.logger.warning(f"创建收益图表失败: {e}")

            # 设置列宽
            ws.column_dimensions['A'].width = 15
            ws.column_dimensions['B'].width = 12
            ws.column_dimensions['C'].width = 12
            ws.column_dimensions['D'].width = 10

        except Exception as e:
            self.logger.error(f"创建收益分析区域失败: {e}")

    def _create_fans_section_vertical(self, ws, accounts_data, start_row):
        """创建粉丝分析区域 - 垂直布局，彻底避免重叠"""
        try:
            from openpyxl.styles import Font, PatternFill, Alignment
            from openpyxl.chart import PieChart, Reference

            # 区域标题
            title_cell = ws.cell(row=start_row, column=1, value="👥 粉丝分布分析")
            title_cell.font = Font(**self.fonts['chart_title'], color=self.colors['secondary'][1:])
            title_cell.fill = PatternFill(start_color=self.colors['light_gray'][1:],
                                        end_color=self.colors['light_gray'][1:], fill_type='solid')
            title_cell.alignment = Alignment(horizontal='center', vertical='center')
            ws.merge_cells(f'A{start_row}:P{start_row}')
            ws.row_dimensions[start_row].height = 25

            # 数据表格 (第27-30行)
            table_start_row = start_row + 1
            headers = ["账号", "粉丝数", "占比", "等级"]
            for i, header in enumerate(headers):
                cell = ws.cell(row=table_start_row, column=i+1, value=header)
                cell.font = Font(**self.fonts['header'], color='FFFFFF')
                cell.fill = PatternFill(start_color=self.colors['secondary'][1:],
                                      end_color=self.colors['secondary'][1:], fill_type='solid')
                cell.alignment = Alignment(horizontal='center', vertical='center')

            # 数据行 (限制为3行)
            top_fans = accounts_data.nlargest(3, '总粉丝') if len(accounts_data) > 3 else accounts_data
            total_fans = accounts_data['总粉丝'].sum() if '总粉丝' in accounts_data.columns else 1

            def get_fans_level(fans_count):
                if fans_count >= 50000:
                    return "🌟 大V"
                elif fans_count >= 10000:
                    return "⭐ 中V"
                elif fans_count >= 1000:
                    return "✨ 小V"
                else:
                    return "🌱 萌新"

            for i, (_, row) in enumerate(top_fans.iterrows()):
                data_row = table_start_row + 1 + i
                ws.cell(row=data_row, column=1, value=row['账号'])
                fans_count = float(row['总粉丝']) if pd.notna(row['总粉丝']) else 0
                ws.cell(row=data_row, column=2, value=int(fans_count))
                percentage = (fans_count / total_fans * 100) if total_fans > 0 else 0
                ws.cell(row=data_row, column=3, value=f"{percentage:.1f}%")
                ws.cell(row=data_row, column=4, value=get_fans_level(fans_count))

            # 图表 (第31-43行) - 固定位置，确保不重叠
            chart_row = start_row + 7
            try:
                chart = PieChart()
                chart.title = "粉丝分布"

                data_end_row = table_start_row + len(top_fans)
                data = Reference(ws, min_col=2, min_row=table_start_row+1, max_row=data_end_row, max_col=2)
                cats = Reference(ws, min_col=1, min_row=table_start_row+1, max_row=data_end_row, max_col=1)

                chart.add_data(data)
                chart.set_categories(cats)

                chart.width = 10
                chart.height = 8

                ws.add_chart(chart, f"A{chart_row}")
            except Exception as e:
                self.logger.warning(f"创建粉丝图表失败: {e}")

            # 设置列宽
            ws.column_dimensions['A'].width = 15
            ws.column_dimensions['B'].width = 12
            ws.column_dimensions['C'].width = 10
            ws.column_dimensions['D'].width = 12

        except Exception as e:
            self.logger.error(f"创建粉丝分析区域失败: {e}")

    def _create_trend_section_vertical(self, ws, seven_day_data, start_row):
        """创建趋势分析区域 - 垂直布局，彻底避免重叠"""
        try:
            from openpyxl.styles import Font, PatternFill, Alignment
            from openpyxl.chart import LineChart, Reference

            # 区域标题
            title_cell = ws.cell(row=start_row, column=1, value="📈 七天收益趋势分析")
            title_cell.font = Font(**self.fonts['chart_title'], color=self.colors['accent'][1:])
            title_cell.fill = PatternFill(start_color=self.colors['light_gray'][1:],
                                        end_color=self.colors['light_gray'][1:], fill_type='solid')
            title_cell.alignment = Alignment(horizontal='center', vertical='center')
            ws.merge_cells(f'A{start_row}:P{start_row}')
            ws.row_dimensions[start_row].height = 25

            # 数据表格 (第50-53行)
            table_start_row = start_row + 1
            date_columns = [col for col in seven_day_data.columns if col not in ['账号', '七天总收益']]
            headers = ["账号"] + date_columns[:5] + ["平均收益"]  # 只显示5天数据
            for i, header in enumerate(headers):
                cell = ws.cell(row=table_start_row, column=i+1, value=header)
                cell.font = Font(**self.fonts['header'], color='FFFFFF')
                cell.fill = PatternFill(start_color=self.colors['accent'][1:],
                                      end_color=self.colors['accent'][1:], fill_type='solid')
                cell.alignment = Alignment(horizontal='center', vertical='center')

            # 数据行 (限制为2行)
            top_accounts = seven_day_data.head(2)
            for i, (_, row) in enumerate(top_accounts.iterrows()):
                data_row = table_start_row + 1 + i
                ws.cell(row=data_row, column=1, value=row['账号'])

                daily_values = []
                for j, date_col in enumerate(date_columns[:5]):
                    value = float(row[date_col]) if pd.notna(row[date_col]) else 0
                    daily_values.append(value)
                    ws.cell(row=data_row, column=j+2, value=value)

                avg_income = sum(daily_values) / len(daily_values) if daily_values else 0
                ws.cell(row=data_row, column=len(date_columns[:5])+2, value=f"{avg_income:.2f}")

            # 图表 (第54-66行) - 固定位置，确保不重叠
            chart_row = start_row + 7
            try:
                chart = LineChart()
                chart.title = "收益趋势"
                chart.style = 13

                # 为每个账号创建一条线
                for i in range(len(top_accounts)):
                    account_row = table_start_row + 1 + i
                    data = Reference(ws, min_col=2, min_row=account_row, max_row=account_row, max_col=len(date_columns[:5])+1)
                    chart.add_data(data)

                # 设置类别（日期）
                cats = Reference(ws, min_col=2, min_row=table_start_row, max_row=table_start_row, max_col=len(date_columns[:5])+1)
                chart.set_categories(cats)

                chart.width = 12
                chart.height = 8
                chart.legend.position = 'r'

                ws.add_chart(chart, f"A{chart_row}")
            except Exception as e:
                self.logger.warning(f"创建趋势图表失败: {e}")

            # 设置列宽
            for i in range(len(headers)):
                col_letter = chr(65 + i)
                ws.column_dimensions[col_letter].width = 10

        except Exception as e:
            self.logger.error(f"创建趋势分析区域失败: {e}")

    def _create_kpi_dashboard_vertical(self, ws, accounts_data, seven_day_data, start_row):
        """创建KPI仪表盘 - 垂直布局，彻底避免重叠"""
        try:
            from openpyxl.styles import Font, PatternFill, Alignment

            # 区域标题
            title_cell = ws.cell(row=start_row, column=1, value="📊 KPI数据仪表盘")
            title_cell.font = Font(**self.fonts['chart_title'], color=self.colors['dark'][1:])
            title_cell.fill = PatternFill(start_color=self.colors['accent_lighter'][1:],
                                        end_color=self.colors['accent_lighter'][1:], fill_type='solid')
            title_cell.alignment = Alignment(horizontal='center', vertical='center')
            ws.merge_cells(f'A{start_row}:P{start_row}')
            ws.row_dimensions[start_row].height = 30

            # 计算KPI指标
            total_accounts = len(accounts_data)
            total_income = accounts_data['累计收益'].sum() if '累计收益' in accounts_data.columns else 0
            total_fans = accounts_data['总粉丝'].sum() if '总粉丝' in accounts_data.columns else 0
            avg_income = total_income / total_accounts if total_accounts > 0 else 0

            # KPI数据表格 (第73-76行)
            kpi_start_row = start_row + 1
            kpi_data = [
                ["指标", "数值"],
                ["账号总数", f"{total_accounts} 个"],
                ["累计收益", f"{total_income:.2f} 元"],
                ["总粉丝数", f"{int(total_fans):,} 人"],
                ["平均收益", f"{avg_income:.2f} 元/账号"]
            ]

            for i, row_data in enumerate(kpi_data):
                kpi_row = kpi_start_row + i
                for j, value in enumerate(row_data):
                    cell = ws.cell(row=kpi_row, column=j+1, value=value)
                    if i == 0:  # 表头
                        cell.font = Font(**self.fonts['header'], color='FFFFFF')
                        cell.fill = PatternFill(start_color=self.colors['primary'][1:],
                                              end_color=self.colors['primary'][1:], fill_type='solid')
                    else:  # 数据行
                        cell.font = Font(**self.fonts['body'])
                        cell.fill = PatternFill(start_color=self.colors['light'][1:],
                                              end_color=self.colors['light'][1:], fill_type='solid')
                    cell.alignment = Alignment(horizontal='center', vertical='center')

            # 设置列宽
            ws.column_dimensions['A'].width = 15
            ws.column_dimensions['B'].width = 20

        except Exception as e:
            self.logger.error(f"创建KPI仪表盘失败: {e}")
