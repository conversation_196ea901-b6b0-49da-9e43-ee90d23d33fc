<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Created by , GNU Texinfo 7.0.1 -->
  <head>
    <meta charset="utf-8">
    <title>
      Libavfilter Documentation
    </title>
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="stylesheet" type="text/css" href="bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="style.min.css">
  </head>
  <body>
    <div class="container">
      <h1>
      Libavfilter Documentation
      </h1>


<div class="top-level-extent" id="SEC_Top">

<div class="element-contents" id="SEC_Contents">
<h2 class="contents-heading">Table of Contents</h2>

<div class="contents">

<ul class="toc-numbered-mark">
  <li><a id="toc-Description" href="#Description">1 Description</a></li>
  <li><a id="toc-See-Also" href="#See-Also">2 See Also</a></li>
  <li><a id="toc-Authors" href="#Authors">3 Authors</a></li>
</ul>
</div>
</div>

<ul class="mini-toc">
<li><a href="#Description" accesskey="1">Description</a></li>
<li><a href="#See-Also" accesskey="2">See Also</a></li>
<li><a href="#Authors" accesskey="3">Authors</a></li>
</ul>
<div class="chapter-level-extent" id="Description">
<h2 class="chapter">1 Description</h2>

<p>The libavfilter library provides a generic audio/video filtering
framework containing several filters, sources and sinks.
</p>

</div>
<div class="chapter-level-extent" id="See-Also">
<h2 class="chapter">2 See Also</h2>

<p><a class="url" href="ffmpeg.html">ffmpeg</a>, <a class="url" href="ffplay.html">ffplay</a>, <a class="url" href="ffprobe.html">ffprobe</a>,
<a class="url" href="ffmpeg-filters.html">ffmpeg-filters</a>,
<a class="url" href="libavutil.html">libavutil</a>, <a class="url" href="libswscale.html">libswscale</a>, <a class="url" href="libswresample.html">libswresample</a>,
<a class="url" href="libavcodec.html">libavcodec</a>, <a class="url" href="libavformat.html">libavformat</a>, <a class="url" href="libavdevice.html">libavdevice</a>
</p>

</div>
<div class="chapter-level-extent" id="Authors">
<h2 class="chapter">3 Authors</h2>

<p>The FFmpeg developers.
</p>
<p>For details about the authorship, see the Git history of the project
(https://git.ffmpeg.org/ffmpeg), e.g. by typing the command
<code class="command">git log</code> in the FFmpeg source directory, or browsing the
online repository at <a class="url" href="https://git.ffmpeg.org/ffmpeg">https://git.ffmpeg.org/ffmpeg</a>.
</p>
<p>Maintainers for the specific components are listed in the file
<samp class="file">MAINTAINERS</samp> in the source code tree.
</p>

</div>
</div>
      <p style="font-size: small;">
        This document was generated using <a class="uref" href="https://www.gnu.org/software/texinfo/"><em class="emph">makeinfo</em></a>.
      </p>
    </div>
  </body>
</html>
