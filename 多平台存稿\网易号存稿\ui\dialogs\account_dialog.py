"""
账号添加对话框模块 - 负责添加账号
"""

import os
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from typing import Dict, Any, Callable, Optional, Tuple

from 网易号存稿.account.login import AccountLogin
from 网易号存稿.browser.driver import DriverManager

class AccountDialog:
    """账号添加对话框类，负责添加账号"""

    def __init__(self, parent: tk.Tk, log_callback: Callable = None):
        """
        初始化账号添加对话框

        Args:
            parent: 父窗口
            log_callback: 日志回调函数
        """
        self.parent = parent
        self.log_callback = log_callback

        # 初始化变量
        self.account_name = tk.StringVar()
        self.login_method = tk.StringVar(value="手机号登录")
        self.cookie_file = tk.StringVar()

        # 计算居中位置
        width = 500
        height = 300
        screen_width = parent.winfo_screenwidth()
        screen_height = parent.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2

        # 创建对话框并直接设置居中位置
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("添加账号")
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")  # 直接设置大小和位置
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)  # 设置为父窗口的临时窗口
        self.dialog.grab_set()  # 模态对话框

        # 设置窗口图标
        try:
            self.dialog.iconbitmap(parent.iconbitmap())
        except:
            pass

        # 创建UI
        self.create_ui()

    def log(self, message: str) -> None:
        """
        记录日志

        Args:
            message: 日志消息
        """
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def create_ui(self) -> None:
        """创建用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.dialog, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 账号名称
        ttk.Label(main_frame, text="账号名称:").grid(row=0, column=0, sticky=tk.W, pady=10)
        ttk.Entry(main_frame, textvariable=self.account_name, width=30).grid(row=0, column=1, sticky=tk.W, pady=10)
        ttk.Label(main_frame, text="(如手机号+备注)").grid(row=0, column=2, sticky=tk.W, pady=10)

        # 登录方式
        ttk.Label(main_frame, text="登录方式:").grid(row=1, column=0, sticky=tk.W, pady=10)
        login_methods = ["手机号登录", "Cookie文件登录"]
        login_method_combobox = ttk.Combobox(main_frame, textvariable=self.login_method, values=login_methods, state="readonly", width=15)
        login_method_combobox.grid(row=1, column=1, sticky=tk.W, pady=10)
        login_method_combobox.bind("<<ComboboxSelected>>", self.on_login_method_changed)

        # Cookie文件选择（初始隐藏）
        self.cookie_frame = ttk.Frame(main_frame)
        self.cookie_frame.grid(row=2, column=0, columnspan=3, sticky=tk.W, pady=10)
        self.cookie_frame.grid_remove()  # 初始隐藏

        ttk.Label(self.cookie_frame, text="Cookie文件:").pack(side=tk.LEFT, padx=(0, 5))
        ttk.Entry(self.cookie_frame, textvariable=self.cookie_file, width=30).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(self.cookie_frame, text="浏览", command=self.select_cookie_file).pack(side=tk.LEFT)

        # 提示信息
        self.phone_info_label = ttk.Label(main_frame, text="将打开浏览器，请在浏览器中完成登录", foreground="blue")
        self.phone_info_label.grid(row=3, column=0, columnspan=3, sticky=tk.W, pady=10)

        self.cookie_info_label = ttk.Label(main_frame, text="请选择包含Cookie数据的JSON文件", foreground="blue")
        self.cookie_info_label.grid(row=3, column=0, columnspan=3, sticky=tk.W, pady=10)
        self.cookie_info_label.grid_remove()  # 初始隐藏

        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=3, pady=20)

        ttk.Button(button_frame, text="确定", command=self.on_ok).pack(side=tk.LEFT, padx=10)
        ttk.Button(button_frame, text="取消", command=self.on_cancel).pack(side=tk.LEFT, padx=10)



    def on_login_method_changed(self, event) -> None:
        """
        登录方式变化事件处理

        Args:
            event: 事件对象
        """
        if self.login_method.get() == "手机号登录":
            self.cookie_frame.grid_remove()
            self.cookie_info_label.grid_remove()
            self.phone_info_label.grid()
        else:
            self.cookie_frame.grid()
            self.cookie_info_label.grid()
            self.phone_info_label.grid_remove()

    def select_cookie_file(self) -> None:
        """选择Cookie文件"""
        file_path = filedialog.askopenfilename(
            title="选择Cookie文件",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if file_path:
            self.cookie_file.set(file_path)

    def on_ok(self) -> None:
        """确定按钮事件处理"""
        # 检查账号名称
        account_name = self.account_name.get().strip()
        if not account_name:
            messagebox.showerror("错误", "请输入账号名称")
            return

        # 根据登录方式处理
        if self.login_method.get() == "手机号登录":
            # 手机号登录
            self.dialog.withdraw()  # 隐藏对话框

            # 创建驱动管理器
            driver_manager = DriverManager(self.log)

            # 创建账号登录对象
            account_login = AccountLogin(driver_manager, self.log)

            # 使用手机号登录
            self.log("正在打开浏览器，请在浏览器中完成登录...")
            login_success, driver, cookies = account_login.login_with_phone(False)

            if login_success and cookies:
                # 返回结果
                self.result = (account_name, cookies, None)
                self.dialog.destroy()
            else:
                self.log("登录失败，无法添加账号")
                messagebox.showerror("登录失败", "登录失败，无法添加账号")
                self.dialog.deiconify()  # 重新显示对话框

            # 关闭浏览器
            driver_manager.close_driver()

        else:
            # Cookie文件登录
            cookie_file = self.cookie_file.get().strip()
            if not cookie_file or not os.path.exists(cookie_file):
                messagebox.showerror("错误", "请选择有效的Cookie文件")
                return

            # 返回结果
            self.result = (account_name, None, cookie_file)
            self.dialog.destroy()

    def on_cancel(self) -> None:
        """取消按钮事件处理"""
        self.result = None
        self.dialog.destroy()

    @staticmethod
    def show_dialog(parent: tk.Tk, log_callback: Callable = None) -> Tuple[Optional[str], Optional[Dict[str, Any]], Optional[str]]:
        """
        显示账号添加对话框

        Args:
            parent: 父窗口
            log_callback: 日志回调函数

        Returns:
            (账号名称, Cookie数据, Cookie文件路径)，如果取消则返回None
        """
        dialog = AccountDialog(parent, log_callback)
        parent.wait_window(dialog.dialog)

        # 返回结果
        if hasattr(dialog, "result"):
            return dialog.result
        else:
            return None
