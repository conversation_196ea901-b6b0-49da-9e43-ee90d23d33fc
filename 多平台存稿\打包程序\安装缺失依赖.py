#!/usr/bin/env python3
"""
安装缺失依赖脚本 - 直接安装所有可能缺失的依赖
"""

import subprocess
import sys

def install_package(package_name):
    """安装Python包"""
    try:
        print(f"正在安装 {package_name}...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', package_name])
        print(f"✅ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败: {e}")
        return False

def main():
    print("=" * 60)
    print("🔧 安装缺失依赖")
    print("=" * 60)
    
    # 关键依赖列表
    packages = [
        'jaraco.text',
        'setuptools',
        'packaging',
        'pyinstaller',
        'selenium',
        'Pillow',  # PIL
        'requests',
        'jieba',
        'moviepy',
        'pyautogui',
        'openpyxl',
        'certifi',
        'urllib3',
        'platformdirs',  # 新增：pkg_resources需要
        'appdirs',       # 备用目录库
        'importlib-metadata',  # 元数据支持
        'zipp',          # 压缩文件支持
        'more-itertools', # 迭代器工具
    ]
    
    print(f"将安装 {len(packages)} 个依赖包...")
    print()
    
    success_count = 0
    failed_packages = []
    
    for package in packages:
        if install_package(package):
            success_count += 1
        else:
            failed_packages.append(package)
        print()
    
    print("=" * 60)
    print(f"安装完成！成功: {success_count}/{len(packages)}")
    
    if failed_packages:
        print(f"失败的包: {', '.join(failed_packages)}")
        print("请手动安装失败的包")
    else:
        print("🎉 所有依赖都安装成功！")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
