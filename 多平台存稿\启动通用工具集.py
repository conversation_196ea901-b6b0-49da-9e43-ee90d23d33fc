#!/usr/bin/env python3
"""
多平台存稿通用工具集启动器
一键启动所有工具的统一入口
"""

import os
import sys
import tkinter as tk
from tkinter import messagebox
import traceback

# 获取当前脚本所在目录
current_dir = os.path.dirname(os.path.abspath(__file__))

# 添加当前目录到Python路径
sys.path.insert(0, current_dir)

def main():
    """主函数"""
    try:
        print("🛠️ 启动多平台存稿通用工具集...")
        
        # 导入通用工具集
        from universal_tools import UniversalToolsGUI
        
        # 创建并运行通用工具集
        app = UniversalToolsGUI()
        app.run()
        
    except ImportError as e:
        error_msg = f"导入模块失败: {str(e)}\n\n请确保所有必要的文件都在正确的位置。"
        print(f"❌ {error_msg}")
        
        # 显示错误对话框
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("启动失败", error_msg)
        root.destroy()
        
    except Exception as e:
        error_msg = f"启动通用工具集失败: {str(e)}"
        print(f"❌ {error_msg}")
        traceback.print_exc()
        
        # 显示错误对话框
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("启动错误", error_msg)
            root.destroy()
        except:
            pass

if __name__ == "__main__":
    main()
