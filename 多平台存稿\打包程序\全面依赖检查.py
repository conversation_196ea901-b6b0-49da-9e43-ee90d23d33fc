#!/usr/bin/env python3
"""
全面依赖检查脚本 - 检查整个项目的依赖、导入和环境配置
"""

import os
import sys
import importlib
import subprocess
import ast
from pathlib import Path
import json

def check_python_version():
    """检查Python版本"""
    print("🐍 Python环境检查:")
    print(f"  版本: {sys.version}")
    print(f"  可执行文件: {sys.executable}")
    print(f"  平台: {sys.platform}")
    
    if sys.version_info < (3, 8):
        print("  ⚠️  建议使用Python 3.8或更高版本")
    else:
        print("  ✅ Python版本符合要求")

def check_virtual_env():
    """检查虚拟环境"""
    print("\n🏠 虚拟环境检查:")
    
    # 检查是否在虚拟环境中
    in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
    
    if in_venv:
        print("  ✅ 当前在虚拟环境中")
        print(f"  环境路径: {sys.prefix}")
    else:
        print("  ⚠️  当前不在虚拟环境中，建议使用虚拟环境")
    
    # 检查site-packages路径
    import site
    site_packages = site.getsitepackages()
    print(f"  site-packages路径: {site_packages}")

def extract_imports_from_file(file_path):
    """从Python文件中提取导入语句"""
    imports = set()
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        tree = ast.parse(content)
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.add(alias.name.split('.')[0])
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    imports.add(node.module.split('.')[0])
    except Exception as e:
        print(f"  ⚠️  解析文件 {file_path} 时出错: {e}")
    
    return imports

def scan_project_imports():
    """扫描项目中的所有导入"""
    print("\n📁 项目导入扫描:")
    
    project_root = Path("C:/Users/<USER>/Downloads/网易/多平台存稿")
    all_imports = set()
    
    # 扫描主要Python文件
    python_files = []
    
    # 主要文件
    main_files = [
        project_root / "run_app.py",
        project_root / "app_launcher.py",
        project_root / "网易号存稿" / "main.py",
        project_root / "视频处理工具" / "main.py",
    ]
    
    # 扫描网易号存稿目录
    netease_dir = project_root / "网易号存稿"
    if netease_dir.exists():
        for py_file in netease_dir.rglob("*.py"):
            python_files.append(py_file)
    
    # 扫描视频处理工具目录
    video_dir = project_root / "视频处理工具"
    if video_dir.exists():
        for py_file in video_dir.rglob("*.py"):
            python_files.append(py_file)
    
    # 添加主要文件
    python_files.extend([f for f in main_files if f.exists()])
    
    print(f"  找到 {len(python_files)} 个Python文件")
    
    # 提取导入
    for py_file in python_files:
        file_imports = extract_imports_from_file(py_file)
        all_imports.update(file_imports)
    
    return all_imports

def check_module_availability(module_name):
    """检查模块是否可用"""
    try:
        importlib.import_module(module_name)
        return True, None
    except ImportError as e:
        return False, str(e)

def categorize_imports(imports):
    """分类导入模块"""
    # 标准库模块
    stdlib_modules = {
        'os', 'sys', 'time', 'datetime', 'json', 'pathlib', 'subprocess',
        'threading', 'queue', 'multiprocessing', 'concurrent', 'traceback',
        'logging', 'importlib', 'tempfile', 'shutil', 'copy', 'pickle',
        'collections', 'functools', 'itertools', 'inspect', 'types',
        'weakref', 'email', 'html', 'xml', 'distutils', 'ast', 'string',
        'math', 'random', 'typing', 'configparser', 'urllib', 'http',
        'socket', 'ssl', 'hashlib', 'base64', 'uuid', 'platform'
    }
    
    # 第三方库模块
    third_party_modules = {
        'tkinter', 'selenium', 'PIL', 'requests', 'jieba', 'moviepy',
        'pyautogui', 'pyinstaller', 'jaraco', 'pkg_resources', 'setuptools',
        'packaging', 'certifi', 'urllib3', 'openpyxl', 'pandas', 'numpy',
        'matplotlib', 'cv2', 'ffmpeg'
    }
    
    # 项目内部模块
    internal_modules = {'网易号存稿', '视频处理工具', 'modules', 'utils', 'common'}
    
    categorized = {
        'stdlib': [],
        'third_party': [],
        'internal': [],
        'unknown': []
    }
    
    for module in imports:
        if module in stdlib_modules:
            categorized['stdlib'].append(module)
        elif module in third_party_modules:
            categorized['third_party'].append(module)
        elif module in internal_modules:
            categorized['internal'].append(module)
        else:
            categorized['unknown'].append(module)
    
    return categorized

def main():
    print("=" * 80)
    print("🔍 多平台存稿工具 - 全面依赖检查")
    print("=" * 80)
    
    # 1. 检查Python环境
    check_python_version()
    
    # 2. 检查虚拟环境
    check_virtual_env()
    
    # 3. 扫描项目导入
    all_imports = scan_project_imports()
    
    # 4. 分类导入
    categorized = categorize_imports(all_imports)
    
    print(f"\n📊 导入统计:")
    print(f"  总计: {len(all_imports)} 个模块")
    print(f"  标准库: {len(categorized['stdlib'])} 个")
    print(f"  第三方库: {len(categorized['third_party'])} 个")
    print(f"  内部模块: {len(categorized['internal'])} 个")
    print(f"  未知模块: {len(categorized['unknown'])} 个")
    
    # 5. 检查第三方库可用性
    print(f"\n🔍 第三方库检查:")
    missing_modules = []
    
    for module in sorted(categorized['third_party']):
        available, error = check_module_availability(module)
        if available:
            print(f"  ✅ {module}")
        else:
            print(f"  ❌ {module} - {error}")
            missing_modules.append(module)
    
    # 6. 检查未知模块
    if categorized['unknown']:
        print(f"\n❓ 未知模块检查:")
        for module in sorted(categorized['unknown']):
            available, error = check_module_availability(module)
            if available:
                print(f"  ✅ {module}")
            else:
                print(f"  ❌ {module} - {error}")
                missing_modules.append(module)
    
    # 7. 生成修复建议
    if missing_modules:
        print(f"\n🔧 修复建议:")
        print(f"发现 {len(missing_modules)} 个缺失模块:")
        
        # 生成安装命令
        install_commands = []
        for module in missing_modules:
            if module == 'PIL':
                install_commands.append('pip install Pillow')
            elif module == 'cv2':
                install_commands.append('pip install opencv-python')
            elif module.startswith('jaraco'):
                install_commands.append('pip install jaraco.text')
            else:
                install_commands.append(f'pip install {module}')
        
        print("\n建议执行以下命令:")
        for cmd in sorted(set(install_commands)):
            print(f"  {cmd}")
    else:
        print(f"\n🎉 所有依赖都已满足！")
    
    print("\n" + "=" * 80)

if __name__ == "__main__":
    main()
