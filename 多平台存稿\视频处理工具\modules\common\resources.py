"""
资源管理模块 - 管理图标和其他资源
"""

import os
import base64
from typing import Dict, <PERSON><PERSON>, Optional
import tkinter as tk
from PIL import Image, ImageTk

# 图标资源字典
ICON_RESOURCES = {
    "settings": """
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAOxAAADsQBlSsOGwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAHDSURBVDiNlZK9TxtBFMV/M7P2goGAMcIWQqRIqUCiQaJMlTJF/oC0+QOoU6ZNRZ0iTcoUKVIgpBSpIoSCEAKBzcW7M1uMd+3FNuRJI83ue/PevHlvRlSVoohF5Bi4BEbAQFXnRXVbQQAi0gO+AiPgGfgBvKjqogjgABH5DHwDToCfwJ2qrkSkBfSBU+ArcCUiT8Ctqk5zABH5AHwHboCxiIyAO1WdAajqHJgDExG5BobAuYhcq+pDGmQBfAJGwEBVXwt+3QKq6lRE7oEz4EJEHlX1V+JBG+gBj6r6VgQOgqwTkXdAH+io6jQGOQRawKQMHIjJOhGZAF3gMPbgAGgAszLwFkxEGkA9BqkBVVXdlIGDqOoGqMQgtdj8XQAHUAXqMcg6+Lk3QES6QJeIB+vA/F8AItIGPgJjYBaDrIEXEWmVgUWkAnwAnoB1nIUV8JZmYRdARGrAOTAFJmkWlsA0zcJOJorIEXALnAK/gftUFhbAc5qFXR4kZL0BjoE/wEOahXcgzcJeFkTkDLgCLoA/wG/gV5qFf8Caqk53WSgiPeAL8Bl4Bh6A5zQLr6r6uM+Vb4ExcA/8UNVFUd0fHPz8Q1RSHPMAAAAASUVORK5CYII=
    """,
    "save": """
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAOxAAADsQBlSsOGwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAADdSURBVDiNY2AYNIARmzgDA8N/BgaG/5T4moWQAoIuYGBgYGBiYGD4z8jI+J+Dg+M/AwMDQ3t7OwMDAwODsrIyAysr638+Pr7/6enp/9va2hgYGRkZmJiYGFhZWRnExMQYGBgYGHJzcxnKysoYWFlZGZgYGBgYiAUgF7CRagALAwMDg5CQEEleYGJgYGAQFxdnYGBgYBAUFCTJABZSFTMwMDAwsbCwMAgICJBkAAsLCwODmJgYAwMDA4OIiAhJBrAwMTExiIqKMjAwMDDw8/OTZMDAAwBuYRqI+YfPDgAAAABJRU5ErkJggg==
    """,
    "dark_mode": """
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAOxAAADsQBlSsOGwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAGISURBVDiNnZK9ThtREIW/mfWCwQ4QO0qBokikSBQpIkVKnoEH4Rl4Ah6AkoKKho6GBtGkQhQgJJAQwlmvd+/MpVjvrjFGiJxqRnPPmXv/RnfvXjKzc+AKOAMWZrYtqUVEG+gBp8AX4FrSE3BnZqsqQABI6gPfgTPgJ/BgZhtJHWAAXABfgWtJz8CdmS1TgKQPwHfgFphLmgD3ZrYBMLMtsJU0B0bAELiU9GhmTwlkBXwCJsDAzF5rfr0FmNlG0iNwCVxJejKzXwmgC/SBZzN7qwPXAGa2lPQIdIFPkuZm9jsBHAEd4KUJXAOtJC2ADnCcAA6BJrCuA9SBJbUBmgkgB3Iz29UB6sCSOkAjARRAYWb7OkANWFIBFAmgBPaSijpAHVhSCZQJYA+8S2rVAWrAkvaJhwSwA16TN9QCzOyPpB1QJIAtsEreUAcogG0CWAOvyRtqAWb2ImkNrBPAEniTlNcBJDWAc2AJLJMsrIHX5A3/ZaKkU+AOuAL+Ag/AImXhHdib2aLJlf8BRwqCxYVDrWIAAAAASUVORK5CYII=
    """,
    "folder": """
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAOxAAADsQBlSsOGwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAADdSURBVDiNY2CgEjAyMjIwMDAwMDEx/WdgYGD4////fwYGBgYWBgYGBmZm5v8MDAwMjIyM/9nY2P4zMDAwdHR0MHBwcPzn4eH5n5yczMDIyMjAxMTEwMrKyiAmJsbAwMDAkJuby1BWVsbAysrKwMTAwMBALAC5gI1UA1gYGBgYhISESPICEwMDA4O4uDgDAwMDg6CgIEkGsJCqmIGBgYGJhYWFQUBAgCQDWFhYGBjExMQYGBgYGERERBgYGRkZmJiYGERFRRkYGBgY+Pn5GRgYGBiYmZkZGBgYGAYeAAAzYRVhOO8BNQAAAABJRU5ErkJggg==
    """,
    "font_increase": """
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAOxAAADsQBlSsOGwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAEISURBVDiNndI9TsNAEAXgbyMXKVKAUqRMkYIjcAXOwBk4AUdJlTNwAIrUKVIgpUgTpAQU2xsvhbNe/wTxpJFnZ957O7uzIYQgJ0mSU0knkk4lHUnamdm2ZI8QQpI0lXQt6ULSQtKDpGdJaxuRNJb0KGkmaS7pTtJC0rZwzySdS7qStLLBQNJE0rOkVQjhIIRwKOlN0lTSuDCwkfQiaZWBbWpmm8Jw3Rl4l/TaBfvJzNZ9Bma2kfTRBfvMwLpvYGbfkr46YF8ZWPUNzOxH0mcH7CsDy76Bmf1K+uiAfWdg0TdIkqQr6b0F9pOBed8gSZJjSW8tsEXOwCRJjiS9tsAWIYRQMvgHpwdVdEm8dGUAAAAASUVORK5CYII=
    """,
    "font_decrease": """
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAOxAAADsQBlSsOGwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAADTSURBVDiNndI9DoJAEAXgB+GgJbGwsLHwCF7BM3gGT+ARPINH8AoWVjYWxsLCmJhQbOYXWHYWfsmEYWbezuzOhhCCnCRJTiWdSTqXdCJpZ2bbkj1CCEnSVNKNpCtJC0mPkl4kbWxE0ljSk6SZpLmke0kLSdvCPZd0IelakjUGkiaSXiRZCOEghHAoaSVpKmlcGNhIepNkGdjWzDaF4boz8CHpvQv2k5mt+wzMbCPpswv2lYF138DMviV9dcC+M7DsG5jZn6TPDthPBhZ9gyRJupI+WmC/GZj3DZIkOZb01gJbhBBC6Rv8A5JnS5T/4kOJAAAAAElFTkSuQmCC
    """,
    "play": """
        iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAOxAAADsQBlSsOGwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAFMSURBVEiJ5ZW/TsJQFMZ/t1eTDsaFwcGYOLjwBD6CT+AL+AQ+gU/gZhwcHI2Jk4mDiQnEgRiTtsdBWmspUNrE+CUnuTnn3O/79/VCRQWAiLSAa6ADXANX7pxXBVgCc2AGTIEXYGpmizIAEWkDD8A9MAQmZjYvC+AAbeBOVe9EpA/0zWxaBOQBHKALPKrqg4h0gb6ZDfOCnAJwgBvgWVUfRaQLPJnZIC/oFIADdFT1RUS6wLOZ9fKCqgIcoKOqfRHpAQMz6+YFlgU4QFtVByLSAwZm1skLLgJwgJaqDkWkBwzNrJ0XfAzgAE1VHYlID3g3s1ZecAzgAA1VHYtID5iYWbMo+BDgAHVVnYhID5iaWaMo+D/AAVqqOhORHvBhZvWi4CzAAW5VdSYiPWBmZrWi4DTAAVqqOheRHjA3s2pR8C/1DfFWQJzL2qZnAAAAAElFTkSuQmCC
    """,
    "stop": """
        iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAOxAAADsQBlSsOGwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAEDSURBVEiJ7ZU9TsNAEIXfrpELRJECKUVSIKVPg5Q7cAWOwBk4AkfgDFQpU6SgQEoRKahQCiIUOzvFOsaRE9tJAQVPWmk8M/u+/bEBzKwGYA3ABsA6gJWQc5kBGAMYARgCOAA4mtlkFoCZrQJ4BbAL4BnAwcwGZQAhVgDsANgzs10AewCezOy+KCgNEGIbwIuZ7ZvZNoAnM7srCsoDCLEJ4M3MDsxsG8C7md0WBWUBQmwAODKzQzPbBnBsZjdFQWmAEOsATmZ2ZGabAE7NbK0oKAsgxBqALzM7NrNNAF9mVi0KygOIWDGzHzM7MbMNAD9mVskLzgKIWDWzXzM7NbN1AL9mVs4L/gOA+Qd+AYL5Ub0TGHbgAAAAAElFTkSuQmCC
    """
}

def get_icon(name: str, size: Optional[Tuple[int, int]] = None) -> ImageTk.PhotoImage:
    """
    获取图标
    
    Args:
        name: 图标名称
        size: 图标大小，如(16, 16)
        
    Returns:
        ImageTk.PhotoImage: 图标对象
    """
    if name not in ICON_RESOURCES:
        raise ValueError(f"未知的图标名称: {name}")
    
    # 解码Base64图标数据
    icon_data = base64.b64decode(ICON_RESOURCES[name])
    
    # 创建图像对象
    from io import BytesIO
    image = Image.open(BytesIO(icon_data))
    
    # 如果指定了大小，调整图像大小
    if size:
        image = image.resize(size, Image.LANCZOS)
    
    # 转换为Tkinter图像对象
    return ImageTk.PhotoImage(image)

def get_all_icons(size: Optional[Tuple[int, int]] = None) -> Dict[str, ImageTk.PhotoImage]:
    """
    获取所有图标
    
    Args:
        size: 图标大小，如(16, 16)
        
    Returns:
        Dict[str, ImageTk.PhotoImage]: 图标字典
    """
    icons = {}
    for name in ICON_RESOURCES:
        icons[name] = get_icon(name, size)
    return icons
