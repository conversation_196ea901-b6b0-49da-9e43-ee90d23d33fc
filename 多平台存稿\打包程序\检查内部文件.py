"""
检查打包结果的内部文件结构
"""

from pathlib import Path

def check_internal_structure():
    """检查_internal目录结构"""
    print("=" * 60)
    print("检查打包结果的内部文件结构")
    print("=" * 60)
    
    target_dir = Path(r"D:\多平台存稿")
    internal_dir = target_dir / "_internal"
    
    if not internal_dir.exists():
        print("❌ _internal目录不存在")
        return
    
    print(f"📁 _internal目录内容:")
    
    try:
        items = list(internal_dir.iterdir())
        items.sort()
        
        for item in items:
            if item.is_file():
                size = item.stat().st_size
                size_mb = size / (1024 * 1024)
                print(f"  📄 {item.name} ({size_mb:.1f} MB)")
            elif item.is_dir():
                try:
                    file_count = len(list(item.iterdir()))
                    print(f"  📁 {item.name}/ ({file_count} 项)")
                    
                    # 如果是我们关心的目录，显示更多详情
                    if item.name in ["网易号存稿", "视频处理工具"]:
                        print(f"    ✅ 找到关键目录: {item.name}")
                        # 显示子目录
                        for subitem in list(item.iterdir())[:5]:  # 只显示前5个
                            if subitem.is_dir():
                                print(f"      📁 {subitem.name}/")
                            else:
                                print(f"      📄 {subitem.name}")
                        if len(list(item.iterdir())) > 5:
                            print(f"      ... 还有 {len(list(item.iterdir())) - 5} 个项目")
                            
                except Exception as e:
                    print(f"  📁 {item.name}/ (无法访问: {e})")
        
        # 检查是否有我们需要的目录
        print("\n🔍 关键目录检查:")
        key_dirs = ["网易号存稿", "视频处理工具", "config", "logs"]
        
        for key_dir in key_dirs:
            dir_path = internal_dir / key_dir
            if dir_path.exists():
                print(f"  ✅ {key_dir}/ (在_internal中)")
            else:
                # 检查是否在主目录中
                main_dir_path = target_dir / key_dir
                if main_dir_path.exists():
                    print(f"  ✅ {key_dir}/ (在主目录中)")
                else:
                    print(f"  ❌ {key_dir}/ (缺失)")
        
    except Exception as e:
        print(f"❌ 检查过程出错: {e}")

if __name__ == "__main__":
    check_internal_structure()
    input("\n按回车键退出...")
