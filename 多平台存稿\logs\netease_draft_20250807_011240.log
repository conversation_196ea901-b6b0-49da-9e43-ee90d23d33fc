[2025-08-07 01:12:44] [INFO] 🚀 启动多平台存稿工具...
[2025-08-07 01:12:44] [INFO] ✅ 定时任务管理器UI初始化完成
[2025-08-07 01:12:45] [INFO] ✅ 定时任务管理标签页创建完成
[SUCCESS] ✅ 升级版定时任务调度管理器初始化完成
[2025-08-07 01:12:44] [INFO] 已从toutiao平台加载存稿详情数据，共 83 个账号
[2025-08-07 01:12:45] [INFO] 🚀 定时任务调度器已启动
[2025-08-07 01:12:45] [DEBUG] 🔄 调度器主循环开始
[2025-08-07 01:12:45] [INFO] 已确保所有必要目录存在
[2025-08-07 01:12:46] [DEBUG] 🔍 [调试] 当前平台: toutiao
[2025-08-07 01:12:46] [DEBUG] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-08-07 01:12:46] [DEBUG] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-08-07 01:12:46] [SUCCESS] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-08-07 01:12:46] [DEBUG] 🔍 [调试] 更新账号树 - 当前平台: toutiao
[2025-08-07 01:12:46] [DEBUG] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-08-07 01:12:46] [DEBUG] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-08-07 01:12:46] [INFO] 已加载账号数据: 85 条记录
[2025-08-07 01:12:46] [DEBUG] 🔍 [调试] 账号数据为空或未加载
[2025-08-07 01:12:46] [SUCCESS] ✅ 初始UI更新完成
[2025-08-07 01:12:54] [INFO] 已选择账号: ***********
[2025-08-07 01:12:55] [DEBUG] 🚀 开始为账号 *********** 存稿
[2025-08-07 01:12:55] [INFO] 已确保所有必要目录存在
[2025-08-07 01:12:55] [INFO] 使用单线程模式处理账号
[2025-08-07 01:12:55] [DEBUG] 🔍 [DEBUG] 准备启动单线程任务，参数: ***********
[2025-08-07 01:12:55] [DEBUG] 🔍 [DEBUG] 单线程任务已启动
[2025-08-07 01:12:55] [DEBUG] 🔍 [DEBUG] _single_task_thread 开始执行，参数: ***********
[2025-08-07 01:12:55] [DEBUG] 📋 开始处理单个账号: ***********
[2025-08-07 01:12:55] [INFO] 视频分配方式: 随机分配
[2025-08-07 01:12:55] [DEBUG] 开始处理账号: ***********
[2025-08-07 01:12:55] [SUCCESS] 正在使用Cookie登录账号: ***********
[2025-08-07 01:12:55] [SUCCESS] ✅ Cookie预检测通过，启动浏览器验证...
[2025-08-07 01:13:02] [SUCCESS] 开始查询账号数据: *********** [toutiao] 浏览器驱动创建成功，使用端口: 9515
[2025-08-07 01:13:06] [SUCCESS] 正在加载Cookie文件: D:/网易号全自动/头条号账号\***********.txt
[2025-08-07 01:13:09] [SUCCESS] ✅ Cookie添加完成
[2025-08-07 01:13:10] [SUCCESS] ✅ 头条号快速登录成功 (等待0.5秒)
[2025-08-07 01:13:10] [SUCCESS] 登录成功，开始处理视频: ***********
[2025-08-07 01:13:10] [INFO] 找到 652 个视频文件
[2025-08-07 01:13:10] [INFO] 已随机打乱 652 个视频文件的顺序
[2025-08-07 01:13:10] [DEBUG] 开始处理视频: 1975张爱萍回京，对面多人审问他态度强硬，华老不忍苛责替他.mp4
[2025-08-07 01:13:10] [DEBUG] 🎬 开始头条号视频存稿流程
[2025-08-07 01:13:10] [DEBUG] 🔍 正在定位视频菜单...
[2025-08-07 01:13:11] [SUCCESS] ✅ 已点击视频菜单
[2025-08-07 01:13:14] [INFO] 🌐 当前不在上传页面，导航到视频上传页面...
[2025-08-07 01:13:18] [SUCCESS] ✅ 已导航到视频上传页面
[2025-08-07 01:13:18] [INFO] 📁 准备上传视频: 1975张爱萍回京，对面多人审问他态度强硬，华老不忍苛责替他.mp4
[2025-08-07 01:13:18] [DEBUG] 🔍 找到上传按钮，准备点击...
[2025-08-07 01:13:18] [SUCCESS] ✅ 已点击上传视频按钮
[2025-08-07 01:13:20] [SUCCESS] ✅ 视频文件已选择并开始上传
[2025-08-07 01:13:20] [DEBUG] ⏳ 开始监控视频上传状态...
[2025-08-07 01:13:20] [INFO] 📊 当前上传状态: 上传中… 0%
[2025-08-07 01:13:20] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 01:13:25] [SUCCESS] 📊 当前上传状态: 上传成功
[2025-08-07 01:13:25] [SUCCESS] ✅ 视频上传成功！
[2025-08-07 01:13:25] [DEBUG] 📷 开始上传视频封面...
[2025-08-07 01:13:25] [SUCCESS] ✅ 已点击上传封面按钮
[2025-08-07 01:13:27] [SUCCESS] ✅ 已选择本地上传
[2025-08-07 01:13:28] [DEBUG] 🔍 查找文件输入框...
[2025-08-07 01:13:28] [SUCCESS] ✅ 找到 3 个文件输入框，使用最后一个作为封面输入框
[2025-08-07 01:13:28] [SUCCESS] ✅ 封面文件已选择
[2025-08-07 01:13:28] [DEBUG] 🔍 开始检测封面canvas加载状态...
[2025-08-07 01:13:28] [INFO] ⏳ Canvas未就绪: canvas_not_ready
[2025-08-07 01:13:28] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-07 01:13:29] [SUCCESS] ✅ Canvas已加载并有内容 (888x499)
[2025-08-07 01:13:29] [SUCCESS] ✅ 封面canvas已加载完成
[2025-08-07 01:13:30] [SUCCESS] ✅ 已点击第一个确定按钮
[2025-08-07 01:13:42] [ERROR] ❌ 封面上传过程中元素定位超时
[2025-08-07 01:13:42] [WARNING] ⚠️ 上传封面失败，继续存稿流程
[2025-08-07 01:13:42] [DEBUG] 💾 开始保存草稿...
[2025-08-07 01:13:42] [SUCCESS] ✅ 已点击存草稿按钮
[2025-08-07 01:13:45] [WARNING] ✅ 草稿保存成功！检测到'全部保存成功'提示
[2025-08-07 01:13:45] [SUCCESS] ✅ 头条号视频存稿成功！
[2025-08-07 01:13:45] [SUCCESS] ✅ 视频存稿成功: 1975张爱萍回京，对面多人审问他态度强硬，华老不忍苛责替他.mp4
[2025-08-07 01:13:45] [DEBUG] 开始处理视频: 杨某媛为何不依为饶？看看诬告朱军的弦子，在国外有怎样的发展.mp4
[2025-08-07 01:13:45] [DEBUG] 🎬 开始头条号视频存稿流程
[2025-08-07 01:13:45] [DEBUG] 🔍 正在定位视频菜单...
[2025-08-07 01:13:45] [SUCCESS] ✅ 已点击视频菜单
[2025-08-07 01:13:48] [ERROR] ❌ 进入视频管理页面失败: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff6c2416f75+76917]
	GetHandleVerifier [0x0x7ff6c2416fd0+77008]
	(No symbol) [0x0x7ff6c21c9dea]
	(No symbol) [0x0x7ff6c21b5f15]
	(No symbol) [0x0x7ff6c21dabf4]
	(No symbol) [0x0x7ff6c224fa85]
	(No symbol) [0x0x7ff6c226ff72]
	(No symbol) [0x0x7ff6c2248243]
	(No symbol) [0x0x7ff6c2211431]
	(No symbol) [0x0x7ff6c22121c3]
	GetHandleVerifier [0x0x7ff6c26ed2ad+3051437]
	GetHandleVerifier [0x0x7ff6c26e7903+3028483]
	GetHandleVerifier [0x0x7ff6c270589d+3151261]
	GetHandleVerifier [0x0x7ff6c243183e+185662]
	GetHandleVerifier [0x0x7ff6c24396ff+218111]
	GetHandleVerifier [0x0x7ff6c241faf4+112628]
	GetHandleVerifier [0x0x7ff6c241fca9+113065]
	GetHandleVerifier [0x0x7ff6c2406c78+10616]
	BaseThreadInitThunk [0x0x7ffcc2f7e8d7+23]
	RtlUserThreadStart [0x0x7ffcc4e5c34c+44]

[2025-08-07 01:13:48] [ERROR] ❌ 进入视频管理页面失败
[2025-08-07 01:13:48] [ERROR] ❌ 视频存稿失败: 杨某媛为何不依为饶？看看诬告朱军的弦子，在国外有怎样的发展.mp4
[2025-08-07 01:13:48] [DEBUG] 开始处理视频: 小学数学17分，进复审当老师，官方称运气好，如此招聘太吓人！.mp4
[2025-08-07 01:13:48] [DEBUG] 🎬 开始头条号视频存稿流程
[2025-08-07 01:13:48] [DEBUG] 🔍 正在定位视频菜单...
[2025-08-07 01:13:48] [ERROR] ❌ 进入视频管理页面失败: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff6c2416f75+76917]
	GetHandleVerifier [0x0x7ff6c2416fd0+77008]
	(No symbol) [0x0x7ff6c21c9c1c]
	(No symbol) [0x0x7ff6c221055f]
	(No symbol) [0x0x7ff6c2248332]
	(No symbol) [0x0x7ff6c2242e53]
	(No symbol) [0x0x7ff6c2241f19]
	(No symbol) [0x0x7ff6c2194b05]
	GetHandleVerifier [0x0x7ff6c26ed2ad+3051437]
	GetHandleVerifier [0x0x7ff6c26e7903+3028483]
	GetHandleVerifier [0x0x7ff6c270589d+3151261]
	GetHandleVerifier [0x0x7ff6c243183e+185662]
	GetHandleVerifier [0x0x7ff6c24396ff+218111]
	(No symbol) [0x0x7ff6c2193b00]
	GetHandleVerifier [0x0x7ff6c2805f18+4201496]
	BaseThreadInitThunk [0x0x7ffcc2f7e8d7+23]
	RtlUserThreadStart [0x0x7ffcc4e5c34c+44]

[2025-08-07 01:13:48] [ERROR] ❌ 进入视频管理页面失败
[2025-08-07 01:13:48] [ERROR] ❌ 视频存稿失败: 小学数学17分，进复审当老师，官方称运气好，如此招聘太吓人！.mp4
[2025-08-07 01:13:48] [INFO] 已达到循环限制 3，停止处理
[2025-08-07 01:13:48] [INFO] [toutiao] 已释放端口: 9515
[2025-08-07 01:13:50] [SUCCESS] 账号 *********** 处理完成
[2025-08-07 01:13:50] [SUCCESS] 账号 *********** 处理完成
[2025-08-07 01:13:50] [SUCCESS] 已更新账号 *********** 的进度信息: 存稿成功数=0, 状态=
[2025-08-07 01:13:50] [SUCCESS] 已更新账号 *********** 状态到账号管理器: 
[2025-08-07 01:13:50] [SUCCESS] 已更新账号 *********** 的进度信息: 存稿成功数=0, 状态=
[2025-08-07 01:13:50] [SUCCESS] 已更新账号 *********** 状态到账号管理器: 
[2025-08-07 01:13:50] [SUCCESS] 已更新账号 *********** 的进度信息: 存稿成功数=0, 状态=
[2025-08-07 01:13:50] [SUCCESS] 已更新账号 *********** 状态到账号管理器: 
[2025-08-07 01:13:50] [SUCCESS] 📊 任务进度完成: 1/1
[2025-08-07 01:13:50] [SUCCESS] ✅ 账号 *********** 处理成功
[2025-08-07 01:13:50] [SUCCESS] ✅ 存稿任务已完成
