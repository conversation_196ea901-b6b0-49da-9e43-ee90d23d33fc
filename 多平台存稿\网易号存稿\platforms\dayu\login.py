"""
大鱼号平台登录模块 - 负责处理大鱼号平台的账号登录
"""

import os
import time
import json
import pickle
import traceback
from typing import Optional, Tuple, List

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.common.action_chains import ActionChains

# 大鱼号平台常量
DAYU_LOGIN_URL = "https://mp.dayu.com/"                    # 大鱼号登录页面
DAYU_BACKEND_URL = "https://mp.dayu.com/dashboard/index"   # 大鱼号后台页面

class DayuLogin:
    """大鱼号平台登录类"""

    # 平台标识符 - 用于模块识别和配置加载
    PLATFORM_ID = "dayu"
    PLATFORM_NAME = "大鱼号平台"

    def __init__(self, log_callback=None):
        """
        初始化大鱼号登录类

        Args:
            log_callback: 日志回调函数
        """
        self.log_callback = log_callback

        # 设置平台标识
        self.platform_id = self.PLATFORM_ID
        self.platform_name = self.PLATFORM_NAME

    def log(self, message: str, internal: bool = False) -> None:
        """
        记录日志

        Args:
            message: 日志消息
            internal: 是否为内部日志（内部日志会添加平台标记，但不显示给用户）
        """
        if internal:
            # 内部日志添加平台标记，用于调试和模块识别
            formatted_message = f"[{self.platform_id}] {message}"
        else:
            # 用户日志不显示平台标记
            formatted_message = message

        if self.log_callback:
            self.log_callback(formatted_message)
        else:
            print(formatted_message)

    def init_driver(self, headless: bool = False, account: str = "") -> Optional[webdriver.Chrome]:
        """
        初始化Chrome浏览器驱动

        Args:
            headless: 是否使用无头模式
            account: 账号名称（用于日志显示，可选）

        Returns:
            Chrome浏览器驱动实例，失败时返回None
        """
        try:
            # 导入驱动管理器
            try:
                from 网易号存稿.browser.driver import DriverManager
            except ImportError:
                # 如果相对导入失败，尝试绝对导入
                import sys
                import os
                sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
                from browser.driver import DriverManager

            # 创建驱动管理器
            driver_manager = DriverManager(self.log_callback)

            # 创建浏览器驱动
            driver = driver_manager.create_driver(headless, None, account)

            if driver:
                # 输出合并的日志信息
                if account:
                    self.log(f"开始查询账号数据: {account} [dayu] 浏览器驱动创建成功，使用端口: {getattr(driver, '_assigned_port', 'unknown')}")
                else:
                    self.log("浏览器驱动初始化成功", internal=True)
                return driver
            else:
                self.log("浏览器驱动初始化失败", internal=True)
                return None

        except Exception as e:
            self.log(f"初始化浏览器驱动时发生错误: {str(e)}", internal=True)
            traceback.print_exc()
            return None

    def login_with_cookies(self, cookie_path: str, headless: bool = False, account: str = "") -> Tuple[bool, Optional[webdriver.Chrome]]:
        """
        使用Cookie登录大鱼号

        Args:
            cookie_path: Cookie文件路径
            headless: 是否使用无头模式
            account: 账号名称（用于日志显示，可选）

        Returns:
            登录结果和浏览器驱动实例
        """
        try:
            # 🚀 闪电预检测 - 0.1秒内快速检测Cookie有效性
            lightning_check = self._lightning_cookie_detection(cookie_path, account)
            if not lightning_check[0]:
                self.log(f"❌ 登录失败: {lightning_check[1]}")
                return False, None

            # 预检测通过，继续浏览器验证
            self.log("✅ Cookie预检测通过，启动浏览器验证...")

            # 检查Cookie文件是否存在（双重保险）
            if not os.path.exists(cookie_path):
                self.log(f"Cookie文件不存在: {cookie_path}", internal=True)
                return False, None

            # 初始化浏览器驱动
            driver = self.init_driver(headless, account)
            if not driver:
                self.log("初始化浏览器驱动失败", internal=True)
                return False, None

            # 先访问大鱼号首页
            self.log("正在访问大鱼号首页...", internal=True)
            driver.get(DAYU_LOGIN_URL)
            time.sleep(3)

            # 加载Cookie
            self.log(f"正在加载Cookie文件: {cookie_path}", internal=True)
            cookies = self._load_cookies(cookie_path)

            if not cookies:
                self.log("加载Cookie失败", internal=True)
                driver.quit()
                return False, None

            # 添加Cookie到浏览器
            for cookie in cookies:
                try:
                    # 确保Cookie格式正确
                    if isinstance(cookie, dict) and 'name' in cookie and 'value' in cookie:
                        # 移除可能导致问题的字段
                        clean_cookie = {
                            'name': cookie['name'],
                            'value': cookie['value'],
                            'domain': cookie.get('domain', '.dayu.com'),
                            'path': cookie.get('path', '/'),
                        }
                        
                        # 只有在有效期内的Cookie才添加
                        if 'expiry' in cookie:
                            clean_cookie['expiry'] = cookie['expiry']
                            
                        driver.add_cookie(clean_cookie)
                except Exception as e:
                    self.log(f"添加Cookie时出错: {str(e)}", internal=True)
                    continue

            # 🚀 大鱼号快速验证优化
            self.log("开始大鱼号快速验证...", internal=True)
            driver.refresh()

            try:
                # 🚀 渐进式等待检测
                for wait_time in [0.5, 1.0, 2.0]:  # 渐进式等待
                    time.sleep(wait_time)
                    current_url = driver.current_url

                    # 检查是否已经跳转到后台
                    if current_url != DAYU_LOGIN_URL and "login" not in current_url.lower():
                        self.log(f"大鱼号快速检测到URL变化 (等待{wait_time}秒): {current_url}", internal=True)

                        # 🚀 快速后台验证
                        try:
                            driver.get(DAYU_BACKEND_URL)

                            # 快速检测后台访问
                            for backend_wait in [1.0, 2.0]:
                                time.sleep(backend_wait)
                                final_url = driver.current_url

                                if ("dashboard" in final_url or
                                    ("mp.dayu.com" in final_url and "login" not in final_url.lower())):
                                    self.log(f"✅ 大鱼号快速登录成功 (总等待{wait_time + backend_wait}秒)", internal=True)
                                    return True, driver

                        except Exception as e:
                            self.log(f"快速后台验证时出错: {str(e)}", internal=True)
                            continue

                # 🚀 最终快速检查
                current_url = driver.current_url
                if current_url != DAYU_LOGIN_URL and "login" not in current_url.lower():
                    # 最后一次后台验证
                    try:
                        driver.get(DAYU_BACKEND_URL)
                        time.sleep(2)  # 减少最终等待时间

                        final_url = driver.current_url
                        if ("dashboard" in final_url or
                            ("mp.dayu.com" in final_url and "login" not in final_url.lower())):
                            self.log("✅ 大鱼号最终检测登录成功", internal=True)
                            return True, driver
                    except Exception:
                        pass

                self.log(f"❌ 大鱼号快速验证失败，最终URL: {driver.current_url}", internal=True)
                driver.quit()
                return False, None

            except Exception as e:
                self.log(f"大鱼号快速验证过程出错: {str(e)}", internal=True)
                driver.quit()
                return False, None

        except Exception as e:
            self.log(f"Cookie登录时发生错误: {str(e)}", internal=True)
            traceback.print_exc()
            if 'driver' in locals():
                try:
                    driver.quit()
                except:
                    pass
            return False, None

    def _load_cookies(self, cookie_path: str) -> Optional[List[dict]]:
        """
        加载Cookie文件

        Args:
            cookie_path: Cookie文件路径

        Returns:
            Cookie列表，失败时返回None
        """
        try:
            # 根据文件扩展名选择加载方式
            if cookie_path.endswith('.json'):
                with open(cookie_path, 'r', encoding='utf-8') as f:
                    cookies = json.load(f)
            elif cookie_path.endswith('.pkl'):
                with open(cookie_path, 'rb') as f:
                    cookies = pickle.load(f)
            elif cookie_path.endswith('.txt'):
                # 处理txt格式的Cookie文件
                cookies = self._parse_txt_cookies(cookie_path)
            else:
                self.log(f"不支持的Cookie文件格式: {cookie_path}", internal=True)
                return None

            if isinstance(cookies, list) and len(cookies) > 0:
                self.log(f"成功加载 {len(cookies)} 个Cookie", internal=True)
                return cookies
            else:
                self.log("Cookie文件格式不正确或为空", internal=True)
                return None

        except Exception as e:
            self.log(f"加载Cookie文件时发生错误: {str(e)}", internal=True)
            traceback.print_exc()
            return None

    def _parse_txt_cookies(self, cookie_path: str) -> Optional[List[dict]]:
        """
        解析txt格式的Cookie文件

        Args:
            cookie_path: Cookie文件路径

        Returns:
            Cookie列表，失败时返回None
        """
        try:
            cookies = []
            with open(cookie_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                
                # 如果是简单的key=value格式
                if '=' in content and ';' in content:
                    cookie_pairs = content.split(';')
                    for pair in cookie_pairs:
                        if '=' in pair:
                            key, value = pair.strip().split('=', 1)
                            cookies.append({
                                'name': key.strip(),
                                'value': value.strip(),
                                'domain': '.dayu.com',
                                'path': '/'
                            })
                else:
                    self.log("txt格式Cookie文件格式不正确", internal=True)
                    return None

            return cookies if cookies else None

        except Exception as e:
            self.log(f"解析txt格式Cookie时发生错误: {str(e)}", internal=True)
            return None

    def login_with_phone(self, headless: bool = False) -> Tuple[bool, Optional[webdriver.Chrome], Optional[list]]:
        """
        使用手机号登录大鱼号

        Args:
            headless: 是否使用无头模式

        Returns:
            登录结果、浏览器驱动实例和Cookie
        """
        try:
            # 初始化浏览器驱动
            driver = self.init_driver(headless)

            # 打开大鱼号登录页面
            driver.get(DAYU_LOGIN_URL)

            # 等待页面加载
            time.sleep(2)

            # 等待用户手动登录
            self.log("请在浏览器中手动完成登录操作...")
            self.log(f"登录地址：{DAYU_LOGIN_URL}")

            # 等待登录成功 - 检查是否跳转到后台页面
            try:
                # 等待页面URL变化或特定元素出现，最多等待5分钟
                max_wait_time = 300
                start_time = time.time()

                while time.time() - start_time < max_wait_time:
                    current_url = driver.current_url

                    # 检查URL是否改变进入到后台（不再是登录页面）
                    if current_url != DAYU_LOGIN_URL and "login" not in current_url.lower():
                        self.log(f"检测到URL改变，可能已登录: {current_url}")

                        # 尝试访问后台页面进一步验证
                        try:
                            driver.get(DAYU_BACKEND_URL)
                            time.sleep(3)

                            final_url = driver.current_url
                            self.log(f"访问后台页面后的URL: {final_url}")

                            # 检查是否成功进入后台
                            if ("dashboard" in final_url or
                                ("mp.dayu.com" in final_url and "login" not in final_url.lower())):
                                # 获取Cookie
                                cookies = driver.get_cookies()
                                self.log("手机号登录成功")
                                return True, driver, cookies
                            else:
                                self.log(f"无法访问后台页面: {final_url}")
                                # 继续等待
                        except Exception as e:
                            self.log(f"验证登录状态时出错: {str(e)}")
                            # 继续等待

                    time.sleep(2)  # 每2秒检查一次

                self.log("等待登录超时，请重试")
                driver.quit()
                return False, None, None

            except TimeoutException:
                self.log("等待登录超时，请重试")
                driver.quit()
                return False, None, None

        except Exception as e:
            self.log(f"手机号登录时发生错误: {str(e)}")
            traceback.print_exc()
            if 'driver' in locals():
                try:
                    driver.quit()
                except:
                    pass
            return False, None, None

    def save_cookies(self, cookies: list, save_path: str) -> bool:
        """
        保存Cookie到文件

        Args:
            cookies: Cookie列表
            save_path: 保存路径

        Returns:
            是否保存成功
        """
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(save_path), exist_ok=True)

            # 根据文件扩展名选择保存格式
            if save_path.endswith('.json'):
                with open(save_path, 'w', encoding='utf-8') as f:
                    json.dump(cookies, f, ensure_ascii=False, indent=4)
            elif save_path.endswith('.pkl'):
                with open(save_path, 'wb') as f:
                    pickle.dump(cookies, f)
            else:
                # 默认保存为JSON格式
                save_path = save_path + '.json'
                with open(save_path, 'w', encoding='utf-8') as f:
                    json.dump(cookies, f, ensure_ascii=False, indent=4)

            self.log(f"Cookie已保存到: {save_path}", internal=True)
            return True

        except Exception as e:
            self.log(f"保存Cookie时发生错误: {str(e)}", internal=True)
            traceback.print_exc()
            return False

    def validate_login(self, driver: webdriver.Chrome) -> bool:
        """
        验证登录状态 - 检查URL是否改变进入到后台

        Args:
            driver: 浏览器驱动实例

        Returns:
            是否已登录
        """
        try:
            # 先获取当前URL
            current_url = driver.current_url
            self.log(f"当前页面URL: {current_url}", internal=True)

            # 如果当前不在登录页面，说明可能已经登录
            if current_url != DAYU_LOGIN_URL and "login" not in current_url.lower():
                self.log("当前不在登录页面，可能已登录", internal=True)

                # 尝试访问后台页面进一步验证
                try:
                    driver.get(DAYU_BACKEND_URL)
                    time.sleep(3)

                    # 检查访问后台页面后的URL
                    final_url = driver.current_url
                    self.log(f"访问后台页面后的URL: {final_url}", internal=True)

                    # 检查是否成功进入后台
                    if ("dashboard" in final_url or
                        ("mp.dayu.com" in final_url and "login" not in final_url.lower())):
                        self.log("登录状态验证成功", internal=True)
                        return True
                    else:
                        self.log(f"无法访问后台页面，登录验证失败: {final_url}", internal=True)
                        return False

                except Exception as e:
                    self.log(f"访问后台页面时出错: {str(e)}", internal=True)
                    return False
            else:
                # 如果仍在登录页面，尝试访问后台看是否会自动跳转
                try:
                    driver.get(DAYU_BACKEND_URL)
                    time.sleep(3)

                    final_url = driver.current_url
                    self.log(f"从登录页面访问后台后的URL: {final_url}", internal=True)

                    # 检查是否成功进入后台
                    if ("dashboard" in final_url or
                        ("mp.dayu.com" in final_url and "login" not in final_url.lower())):
                        self.log("登录状态验证成功", internal=True)
                        return True
                    else:
                        self.log(f"登录状态验证失败，仍无法访问后台: {final_url}", internal=True)
                        return False

                except Exception as e:
                    self.log(f"验证登录状态时发生错误: {str(e)}", internal=True)
                    return False

        except Exception as e:
            self.log(f"验证登录状态时发生错误: {str(e)}", internal=True)
            return False

    def _lightning_cookie_detection(self, cookie_path: str, account: str = "") -> Tuple[bool, str]:
        """
        🚀 闪电Cookie检测 - 0.1秒内快速检测Cookie有效性

        Args:
            cookie_path: Cookie文件路径
            account: 账号名称（用于日志）

        Returns:
            (是否有效, 检测结果描述)
        """
        import time
        import json

        start_time = time.time()

        try:
            # 第1层: 文件存在性检查 (0.001秒)
            if not os.path.exists(cookie_path):
                return False, "Cookie文件不存在"

            # 第2层: 文件内容检查 (0.01秒)
            try:
                with open(cookie_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                if not content:
                    return False, "Cookie文件为空"
            except Exception as e:
                return False, f"Cookie文件读取失败: {str(e)}"

            # 第3层: JSON格式检查 (0.01秒)
            try:
                data = json.loads(content)

                # 检查是否是账号格式 {"accountId": "...", "cookies": {...}}
                if isinstance(data, dict) and 'cookies' in data:
                    cookies_dict = data['cookies']
                    if not isinstance(cookies_dict, dict) or len(cookies_dict) == 0:
                        return False, "Cookie数据为空"

                    # 第4层: 关键Cookie检查 (0.03秒) - 字典格式
                    essential_check = self._check_essential_dayu_cookies_dict(cookies_dict)
                    if not essential_check[0]:
                        return False, essential_check[1]

                    # 第5层: 基本有效性检查 (0.02秒)
                    validity_check = self._check_dayu_cookies_validity(cookies_dict)
                    if not validity_check[0]:
                        return False, validity_check[1]

                # 检查是否是Cookie数组格式 [{"name": "...", "value": "...", ...}]
                elif isinstance(data, list):
                    if len(data) == 0:
                        return False, "Cookie数组为空"

                    # 第4层: 关键Cookie检查 (0.03秒) - 数组格式
                    essential_check = self._check_essential_dayu_cookies(data)
                    if not essential_check[0]:
                        return False, essential_check[1]

                    # 第5层: 过期时间检查 (0.02秒)
                    expiry_check = self._check_cookie_expiry_fast(data)
                    if not expiry_check[0]:
                        return False, expiry_check[1]

                    # 第6层: 域名匹配检查 (0.01秒)
                    domain_check = self._check_dayu_domain_match(data)
                    if not domain_check[0]:
                        return False, domain_check[1]
                else:
                    return False, "Cookie数据格式不支持"

            except json.JSONDecodeError:
                return False, "Cookie文件JSON格式错误"

            elapsed = time.time() - start_time
            return True, f"Cookie预检测通过 (耗时: {elapsed:.3f}秒)"

        except Exception as e:
            elapsed = time.time() - start_time
            return False, f"预检测异常: {str(e)} (耗时: {elapsed:.3f}秒)"

    def _check_essential_dayu_cookies(self, cookies: list) -> Tuple[bool, str]:
        """检查大鱼号关键Cookie"""
        try:
            # 大鱼号关键Cookie名称
            essential_names = [
                'sessionid', 'sid', 'uid', 'user_id', 'csrf_token',
                'dayu_sid', 'dayu_uid', 'uc_login_unique',
                'login_aliyunid_csrf', 'aliyun_choice', 'login_aliyunid_ticket'
            ]

            cookie_names = [cookie.get('name', '').lower() for cookie in cookies if isinstance(cookie, dict)]

            # 检查是否有基本的认证Cookie
            found_essential = []
            for name in essential_names:
                if any(name in cookie_name for cookie_name in cookie_names):
                    found_essential.append(name)

            if len(found_essential) < 1:  # 至少需要1个关键Cookie
                return False, f"缺少关键认证Cookie，仅找到: {found_essential}"

            # 检查Cookie基本格式
            valid_cookies = 0
            for cookie in cookies:
                if isinstance(cookie, dict) and cookie.get('name') and cookie.get('value'):
                    valid_cookies += 1

            if valid_cookies < 2:  # 至少需要2个有效Cookie
                return False, f"有效Cookie数量不足: {valid_cookies}/2"

            return True, f"关键Cookie检查通过，找到 {len(found_essential)} 个关键Cookie"

        except Exception as e:
            return False, f"关键Cookie检查异常: {str(e)}"

    def _check_cookie_expiry_fast(self, cookies: list) -> Tuple[bool, str]:
        """快速检查Cookie过期时间"""
        try:
            current_time = time.time()
            total_cookies = 0
            expired_cookies = 0
            valid_cookies = 0

            for cookie in cookies:
                if not isinstance(cookie, dict):
                    continue

                total_cookies += 1

                if 'expiry' in cookie:
                    try:
                        expiry_time = int(float(cookie['expiry']))
                        if expiry_time < current_time:
                            expired_cookies += 1
                        else:
                            valid_cookies += 1
                    except (ValueError, TypeError):
                        valid_cookies += 1  # 无法解析的当作有效
                else:
                    valid_cookies += 1  # 会话Cookie当作有效

            # 如果所有有过期时间的Cookie都过期了，判定为失效
            if expired_cookies > 0 and valid_cookies == 0:
                return False, f"所有Cookie已过期 ({expired_cookies}个)"

            # 如果过期Cookie太多，也可能有问题
            if total_cookies > 0 and expired_cookies / total_cookies > 0.8:
                return False, f"大部分Cookie已过期 ({expired_cookies}/{total_cookies})"

            return True, f"过期检查通过 (有效: {valid_cookies}, 过期: {expired_cookies})"

        except Exception as e:
            return False, f"过期检查异常: {str(e)}"

    def _check_dayu_domain_match(self, cookies: list) -> Tuple[bool, str]:
        """检查大鱼号域名匹配"""
        try:
            # 大鱼号相关域名
            valid_domains = [
                '.dayu.com', 'dayu.com', '.mp.dayu.com', 'mp.dayu.com',
                '.aliyun.com', 'aliyun.com', '.alibaba.com', 'alibaba.com',
                '.taobao.com', 'taobao.com'
            ]

            dayu_cookies = 0
            total_cookies = 0

            for cookie in cookies:
                if not isinstance(cookie, dict):
                    continue

                total_cookies += 1
                domain = cookie.get('domain', '')

                if any(valid_domain in domain for valid_domain in valid_domains):
                    dayu_cookies += 1

            if total_cookies == 0:
                return False, "没有找到任何Cookie"

            # 至少20%的Cookie应该是大鱼相关的
            if dayu_cookies / total_cookies < 0.1:
                return False, f"大鱼相关Cookie比例过低 ({dayu_cookies}/{total_cookies})"

            return True, f"域名检查通过 (大鱼Cookie: {dayu_cookies}/{total_cookies})"

        except Exception as e:
            return False, f"域名检查异常: {str(e)}"

    def _check_essential_dayu_cookies_dict(self, cookies_dict: dict) -> Tuple[bool, str]:
        """检查大鱼号关键Cookie（字典格式）"""
        try:
            # 大鱼号关键Cookie名称（更宽松的检查）
            essential_names = [
                'sessionid', 'sid', 'uid', 'user_id', 'csrf_token',
                'dayu_sid', 'dayu_uid', 'uc_login_unique',
                'login_aliyunid_csrf', 'aliyun_choice', 'login_aliyunid_ticket',
                'session', 'auth', 'token'  # 更通用的认证Cookie
            ]

            cookie_names = [name.lower() for name in cookies_dict.keys()]

            # 检查是否有基本的认证Cookie
            found_essential = []
            for name in essential_names:
                if any(name in cookie_name for cookie_name in cookie_names):
                    found_essential.append(name)

            # 大鱼号检查更宽松，只要有任何可能的认证Cookie就通过
            if len(found_essential) == 0:
                # 如果没有找到明确的认证Cookie，检查是否有任何长值Cookie（可能是认证相关）
                long_value_cookies = 0
                for name, value in cookies_dict.items():
                    if value and len(str(value)) > 20:  # 长值Cookie可能是认证相关
                        long_value_cookies += 1

                if long_value_cookies == 0:
                    return False, f"未找到任何可能的认证Cookie"
                else:
                    return True, f"找到 {long_value_cookies} 个可能的认证Cookie"

            # 检查Cookie值是否有效
            valid_cookies = 0
            for name, value in cookies_dict.items():
                if name and value and len(str(value)) > 3:  # 基本的值检查
                    valid_cookies += 1

            if valid_cookies < 1:  # 至少需要1个有效Cookie
                return False, f"有效Cookie数量不足: {valid_cookies}/1"

            return True, f"关键Cookie检查通过，找到 {len(found_essential)} 个关键Cookie"

        except Exception as e:
            return False, f"关键Cookie检查异常: {str(e)}"

    def _check_dayu_cookies_validity(self, cookies_dict: dict) -> Tuple[bool, str]:
        """检查大鱼号Cookie有效性（字典格式）"""
        try:
            # 大鱼号的检查比较宽松，主要检查是否有基本的Cookie
            total_cookies = len(cookies_dict)

            if total_cookies == 0:
                return False, "没有任何Cookie"

            # 检查是否有明显的过期标识
            for name, value in cookies_dict.items():
                if 'expired' in str(value).lower() or 'invalid' in str(value).lower():
                    return False, f"发现过期标识: {name}"

            # 检查是否有有效的值
            valid_values = 0
            for name, value in cookies_dict.items():
                if value and len(str(value)) > 3:
                    valid_values += 1

            if valid_values == 0:
                return False, "所有Cookie值都无效"

            return True, f"Cookie有效性检查通过 (总计: {total_cookies}个, 有效值: {valid_values}个)"

        except Exception as e:
            return False, f"有效性检查异常: {str(e)}"
