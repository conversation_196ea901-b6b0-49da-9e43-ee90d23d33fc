# 网易视频处理工具 - 模块化版

这是网易视频预处理工具的模块化版本，提供了更好的代码组织结构和新增功能。

## 新增功能

1. **自定义封面分辨率** - 可以设置输出封面图像的分辨率，支持常见预设（720p、1080p、4K等）
2. **视频格式转换** - 可以将视频转换为指定格式（mp4、mov、avi等）

## 模块结构

- `main.py` - 主程序入口
- `modules/` - 模块目录
  - `__init__.py` - 模块包初始化文件
  - `ui/` - 用户界面模块目录
    - `__init__.py` - UI模块初始化文件
    - `main.py` - 主UI类
    - `components/` - UI组件目录
    - `styles/` - UI样式目录
  - `ui_base.py` - UI基础类
  - `ui_theme.py` - UI主题相关功能
  - `ui_utils.py` - UI工具函数
  - `ui_settings.py` - 设置对话框相关功能
  - `ui_processing.py` - 处理相关UI功能
  - `video_processor.py` - 视频处理模块
  - `cover_processor.py` - 封面处理模块
  - `config_manager.py` - 配置管理模块
  - `utils.py` - 工具函数模块
  - `icons.py` - 图标资源模块

## 系统要求

- Python 3.11.9 或更高版本
- FFmpeg（推荐，但不是必须的）
- Tesseract OCR（可选，用于文字识别）
- 依赖库：
  - moviepy
  - Pillow (PIL)
  - psutil（可选，用于优化线程分配）

## 快速开始

1. 双击 `启动模块化视频处理工具.bat` 启动程序
2. 设置视频源目录和输出目录
3. 配置处理参数（视频时长、比例、封面文字等）
4. 点击"开始处理"按钮

## 新功能使用说明

### 自定义封面分辨率

1. 在基本设置区域勾选"自定义封面分辨率"
2. 手动输入宽度和高度，或点击预设按钮（720p、1080p、4K等）
3. 处理后的封面图像将按照指定分辨率输出

### 视频格式转换

1. 在基本设置区域勾选"转换视频格式"
2. 从下拉菜单中选择目标格式（mp4、mov、avi等）
3. 处理后的视频将转换为指定格式

## 注意事项

- 程序会自动检测并使用当前目录下的FFmpeg
- 如需修改配置，可编辑preprocess_config.ini文件
- 水印功能仅应用于封面图像，不会影响视频文件

## 与原版的区别

- 代码结构更清晰，便于维护和扩展
- 新增自定义封面分辨率功能
- 新增视频格式转换功能
- 界面布局优化
- 更好的错误处理和日志记录
