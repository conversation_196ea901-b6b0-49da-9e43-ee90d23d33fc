"""
多平台存稿工具打包脚本
将 run_app.py 程序打包成可执行文件，并按要求放置在指定目录
"""

import os
import sys
import shutil
import subprocess
import json
from pathlib import Path
import tkinter as tk
from tkinter import messagebox, ttk
import threading
import time

class PackageApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("多平台存稿工具打包器")
        self.root.geometry("600x500")
        self.root.resizable(False, False)
        
        # 设置窗口居中
        self.center_window()
        
        # 基础路径配置
        self.source_dir = Path(r"C:\Users\<USER>\Downloads\网易\多平台存稿")
        self.target_dir = Path(r"D:\多平台存稿")
        self.package_dir = Path(r"D:\多平台存稿\打包临时文件")
        
        # 创建界面
        self.create_widgets()
        
        # 打包状态
        self.is_packaging = False
        
    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        self.root.geometry(f"+{x}+{y}")
        
    def create_widgets(self):
        """创建界面组件"""
        # 标题
        title_label = tk.Label(
            self.root,
            text="多平台存稿工具打包器",
            font=("微软雅黑", 16, "bold"),
            fg="#2c3e50"
        )
        title_label.pack(pady=20)
        
        # 配置框架
        config_frame = tk.LabelFrame(self.root, text="打包配置", font=("微软雅黑", 10))
        config_frame.pack(fill="x", padx=20, pady=10)
        
        # 源目录
        tk.Label(config_frame, text="源程序目录:", font=("微软雅黑", 9)).grid(row=0, column=0, sticky="w", padx=10, pady=5)
        self.source_label = tk.Label(config_frame, text=str(self.source_dir), font=("微软雅黑", 8), fg="#666")
        self.source_label.grid(row=0, column=1, sticky="w", padx=10, pady=5)
        
        # 打包临时目录
        tk.Label(config_frame, text="打包临时目录:", font=("微软雅黑", 9)).grid(row=1, column=0, sticky="w", padx=10, pady=5)
        self.temp_label = tk.Label(config_frame, text=str(self.package_dir), font=("微软雅黑", 8), fg="#666")
        self.temp_label.grid(row=1, column=1, sticky="w", padx=10, pady=5)

        # 最终程序目录
        tk.Label(config_frame, text="最终程序目录:", font=("微软雅黑", 9)).grid(row=2, column=0, sticky="w", padx=10, pady=5)
        self.deploy_label = tk.Label(config_frame, text=str(self.target_dir), font=("微软雅黑", 8), fg="#666")
        self.deploy_label.grid(row=2, column=1, sticky="w", padx=10, pady=5)
        
        # 打包选项框架
        options_frame = tk.LabelFrame(self.root, text="打包选项", font=("微软雅黑", 10))
        options_frame.pack(fill="x", padx=20, pady=10)
        
        # 打包模式选择
        self.package_mode = tk.StringVar(value="onedir")
        tk.Radiobutton(options_frame, text="目录模式 (推荐)", variable=self.package_mode, 
                      value="onedir", font=("微软雅黑", 9)).pack(anchor="w", padx=10, pady=2)
        tk.Radiobutton(options_frame, text="单文件模式", variable=self.package_mode, 
                      value="onefile", font=("微软雅黑", 9)).pack(anchor="w", padx=10, pady=2)
        
        # 其他选项
        self.include_console = tk.BooleanVar(value=False)
        tk.Checkbutton(options_frame, text="包含控制台窗口", variable=self.include_console, 
                      font=("微软雅黑", 9)).pack(anchor="w", padx=10, pady=2)
        
        self.upx_compress = tk.BooleanVar(value=True)
        tk.Checkbutton(options_frame, text="启用UPX压缩", variable=self.upx_compress, 
                      font=("微软雅黑", 9)).pack(anchor="w", padx=10, pady=2)
        
        # 进度框架
        progress_frame = tk.LabelFrame(self.root, text="打包进度", font=("微软雅黑", 10))
        progress_frame.pack(fill="x", padx=20, pady=10)
        
        # 进度条
        self.progress = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.progress.pack(fill="x", padx=10, pady=5)
        
        # 状态标签
        self.status_label = tk.Label(progress_frame, text="准备就绪", font=("微软雅黑", 9), fg="#27ae60")
        self.status_label.pack(pady=5)
        
        # 日志框架
        log_frame = tk.LabelFrame(self.root, text="打包日志", font=("微软雅黑", 10))
        log_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # 日志文本框
        self.log_text = tk.Text(log_frame, height=8, font=("Consolas", 8))
        scrollbar = tk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side="left", fill="both", expand=True, padx=(10, 0), pady=10)
        scrollbar.pack(side="right", fill="y", padx=(0, 10), pady=10)
        
        # 按钮框架
        button_frame = tk.Frame(self.root)
        button_frame.pack(fill="x", padx=20, pady=10)
        
        # 开始打包按钮
        self.package_button = tk.Button(
            button_frame,
            text="开始打包",
            command=self.start_packaging,
            font=("微软雅黑", 10, "bold"),
            bg="#3498db",
            fg="white",
            relief="flat",
            padx=20,
            pady=5
        )
        self.package_button.pack(side="left", padx=(0, 10))
        
        # 清理按钮
        self.clean_button = tk.Button(
            button_frame,
            text="清理临时文件",
            command=self.clean_temp_files,
            font=("微软雅黑", 10),
            bg="#95a5a6",
            fg="white",
            relief="flat",
            padx=20,
            pady=5
        )
        self.clean_button.pack(side="left", padx=(0, 10))
        
        # 退出按钮
        exit_button = tk.Button(
            button_frame,
            text="退出",
            command=self.root.quit,
            font=("微软雅黑", 10),
            bg="#e74c3c",
            fg="white",
            relief="flat",
            padx=20,
            pady=5
        )
        exit_button.pack(side="right")
        
    def log_message(self, message, level="INFO"):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def update_status(self, status, color="#27ae60"):
        """更新状态标签"""
        self.status_label.config(text=status, fg=color)
        self.root.update_idletasks()

    def check_dependencies(self):
        """检查打包依赖"""
        self.log_message("检查打包依赖...")

        # 检查PyInstaller
        try:
            import PyInstaller
            self.log_message(f"PyInstaller 版本: {PyInstaller.__version__}")
        except ImportError:
            self.log_message("PyInstaller 未安装，正在安装...", "WARNING")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
                self.log_message("PyInstaller 安装成功")
            except subprocess.CalledProcessError as e:
                self.log_message(f"PyInstaller 安装失败: {e}", "ERROR")
                return False

        # 检查源文件
        if not self.source_dir.exists():
            self.log_message(f"源目录不存在: {self.source_dir}", "ERROR")
            return False

        run_app_path = self.source_dir / "run_app.py"
        if not run_app_path.exists():
            self.log_message(f"主程序文件不存在: {run_app_path}", "ERROR")
            return False

        self.log_message("依赖检查完成")
        return True

    def create_directories(self):
        """创建必要的目录"""
        self.log_message("创建目录结构...")

        # 创建打包输出目录
        self.package_dir.mkdir(parents=True, exist_ok=True)
        self.log_message(f"创建打包目录: {self.package_dir}")

        # 创建最终部署目录
        self.target_dir.mkdir(parents=True, exist_ok=True)
        self.log_message(f"创建部署目录: {self.target_dir}")

        return True

    def generate_spec_file(self):
        """生成PyInstaller spec文件"""
        self.log_message("生成spec文件...")

        # 根据打包模式生成不同的spec内容
        if self.package_mode.get() == "onedir":
            # 目录模式
            spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

import os
import sys
from pathlib import Path

# 设置基础路径
base_path = Path(r"{self.source_dir}")
app_path = base_path / "run_app.py"

# 数据文件和目录
datas = [
    # 配置文件
    (str(base_path / "config"), "config"),
    (str(base_path / "directory_config.json"), "."),
    (str(base_path / "config.json"), "."),

    # 网易号存稿整个目录
    (str(base_path / "网易号存稿"), "网易号存稿"),

    # 视频处理工具
    (str(base_path / "视频处理工具"), "视频处理工具"),

    # 日志目录
    (str(base_path / "logs"), "logs"),
]

# 完整的隐藏导入列表
hiddenimports = [
    # tkinter模块
    'tkinter', 'tkinter.ttk', 'tkinter.messagebox', 'tkinter.simpledialog',
    'tkinter.filedialog', 'tkinter.scrolledtext', 'tkinter.font', 'tkinter.constants',

    # selenium模块
    'selenium', 'selenium.webdriver', 'selenium.webdriver.chrome',
    'selenium.webdriver.chrome.service', 'selenium.webdriver.chrome.options',
    'selenium.webdriver.common.by', 'selenium.webdriver.support.ui',
    'selenium.webdriver.support.wait', 'selenium.webdriver.support.expected_conditions',
    'selenium.webdriver.common.keys', 'selenium.webdriver.common.action_chains',
    'selenium.common.exceptions',

    # PIL模块
    'PIL', 'PIL.Image', 'PIL.ImageTk', 'PIL.ImageDraw', 'PIL.ImageFont',

    # 网络和数据处理
    'requests', 'requests.adapters', 'requests.auth', 'requests.cookies',
    'json', 'urllib3', 'certifi',

    # 系统和线程
    'threading', 'queue', 'subprocess', 'multiprocessing', 'concurrent.futures',
    'datetime', 'time', 'pathlib', 'os', 'sys', 'shutil', 'tempfile',

    # 导入和模块
    'importlib', 'importlib.util', 'traceback', 'logging',

    # 应用特定
    'jieba', 'moviepy', 'moviepy.editor', 'pyautogui',

    # 修复缺失的依赖
    'jaraco', 'jaraco.text', 'jaraco.functools', 'jaraco.context',
    'pkg_resources', 'pkg_resources._vendor', 'pkg_resources._vendor.packaging',

    # 其他可能缺失的模块
    'packaging', 'packaging.version', 'packaging.requirements',
    'distutils', 'distutils.util', 'distutils.version',
    'email', 'email.mime', 'email.mime.text', 'email.mime.multipart',
]

excludes = ['matplotlib', 'scipy', 'jupyter', 'IPython', 'notebook', 'pytest', 'setuptools']

block_cipher = None

a = Analysis(
    [str(app_path)],
    pathex=[str(base_path)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='多平台存稿工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx={self.upx_compress.get()},
    console={self.include_console.get()},
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx={self.upx_compress.get()},
    upx_exclude=[],
    name="多平台存稿工具",
)
'''
        else:
            # 单文件模式
            spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

import os
import sys
from pathlib import Path

# 设置基础路径
base_path = Path(r"{self.source_dir}")
app_path = base_path / "run_app.py"

# 数据文件和目录
datas = [
    # 配置文件
    (str(base_path / "config"), "config"),
    (str(base_path / "directory_config.json"), "."),
    (str(base_path / "config.json"), "."),

    # 网易号存稿整个目录
    (str(base_path / "网易号存稿"), "网易号存稿"),

    # 视频处理工具
    (str(base_path / "视频处理工具"), "视频处理工具"),

    # 日志目录
    (str(base_path / "logs"), "logs"),
]

# 完整的隐藏导入列表
hiddenimports = [
    # tkinter模块
    'tkinter', 'tkinter.ttk', 'tkinter.messagebox', 'tkinter.simpledialog',
    'tkinter.filedialog', 'tkinter.scrolledtext', 'tkinter.font', 'tkinter.constants',

    # selenium模块
    'selenium', 'selenium.webdriver', 'selenium.webdriver.chrome',
    'selenium.webdriver.chrome.service', 'selenium.webdriver.chrome.options',
    'selenium.webdriver.common.by', 'selenium.webdriver.support.ui',
    'selenium.webdriver.support.wait', 'selenium.webdriver.support.expected_conditions',
    'selenium.webdriver.common.keys', 'selenium.webdriver.common.action_chains',
    'selenium.common.exceptions',

    # PIL模块
    'PIL', 'PIL.Image', 'PIL.ImageTk', 'PIL.ImageDraw', 'PIL.ImageFont',

    # 网络和数据处理
    'requests', 'requests.adapters', 'requests.auth', 'requests.cookies',
    'json', 'urllib3', 'certifi',

    # 系统和线程
    'threading', 'queue', 'subprocess', 'multiprocessing', 'concurrent.futures',
    'datetime', 'time', 'pathlib', 'os', 'sys', 'shutil', 'tempfile',

    # 导入和模块
    'importlib', 'importlib.util', 'traceback', 'logging',

    # 应用特定
    'jieba', 'moviepy', 'moviepy.editor', 'pyautogui',

    # 修复缺失的依赖
    'jaraco', 'jaraco.text', 'jaraco.functools', 'jaraco.context',
    'pkg_resources', 'pkg_resources._vendor', 'pkg_resources._vendor.packaging',

    # 其他可能缺失的模块
    'packaging', 'packaging.version', 'packaging.requirements',
    'distutils', 'distutils.util', 'distutils.version',
    'email', 'email.mime', 'email.mime.text', 'email.mime.multipart',
]

excludes = ['matplotlib', 'scipy', 'jupyter', 'IPython', 'notebook', 'pytest', 'setuptools']

block_cipher = None

a = Analysis(
    [str(app_path)],
    pathex=[str(base_path)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='多平台存稿工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx={self.upx_compress.get()},
    console={self.include_console.get()},
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''

        spec_file_path = self.package_dir / "app.spec"
        with open(spec_file_path, 'w', encoding='utf-8') as f:
            f.write(spec_content)

        self.log_message(f"spec文件已生成: {spec_file_path}")
        return spec_file_path

    def run_pyinstaller(self, spec_file_path):
        """运行PyInstaller打包"""
        self.log_message("开始PyInstaller打包...")

        try:
            # 构建PyInstaller命令
            cmd = [
                sys.executable, "-m", "PyInstaller",
                "--clean",
                "--noconfirm",
                str(spec_file_path)
            ]

            self.log_message(f"执行命令: {' '.join(cmd)}")

            # 在打包目录中执行命令
            process = subprocess.Popen(
                cmd,
                cwd=str(self.package_dir),
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                encoding='utf-8'
            )

            # 实时显示输出
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    self.log_message(output.strip(), "PYINSTALLER")

            # 等待进程完成
            return_code = process.poll()

            if return_code == 0:
                self.log_message("PyInstaller打包完成")
                return True
            else:
                self.log_message(f"PyInstaller打包失败，返回码: {return_code}", "ERROR")
                return False

        except Exception as e:
            self.log_message(f"PyInstaller执行出错: {e}", "ERROR")
            return False

    def deploy_package(self):
        """部署打包结果到目标目录"""
        self.log_message("开始部署打包结果...")

        try:
            # 查找打包输出目录
            dist_dir = self.package_dir / "dist"
            if not dist_dir.exists():
                self.log_message("找不到dist目录", "ERROR")
                return False

            # 查找应用程序目录或文件
            app_name = "多平台存稿工具"
            app_dir = dist_dir / app_name
            app_exe = dist_dir / f"{app_name}.exe"

            if app_dir.exists():
                # 目录模式
                self.log_message(f"找到应用程序目录: {app_dir}")

                # 确保目标目录存在
                self.target_dir.mkdir(parents=True, exist_ok=True)

                # 清理目标目录中的旧程序文件（保留配置文件）
                for item in self.target_dir.iterdir():
                    if item.name not in ['config.json', 'directory_config.json', 'timer_config.json', 'config']:
                        if item.is_dir():
                            shutil.rmtree(item)
                        else:
                            item.unlink()

                # 复制应用程序内容到目标目录
                self.log_message(f"复制应用程序内容到: {self.target_dir}")
                for item in app_dir.iterdir():
                    if item.is_dir():
                        shutil.copytree(item, self.target_dir / item.name)
                    else:
                        shutil.copy2(item, self.target_dir / item.name)

            elif app_exe.exists():
                # 单文件模式
                self.log_message(f"找到应用程序文件: {app_exe}")

                # 确保目标目录存在
                self.target_dir.mkdir(parents=True, exist_ok=True)

                # 复制可执行文件
                target_exe = self.target_dir / f"{app_name}.exe"
                self.log_message(f"复制可执行文件到: {target_exe}")
                shutil.copy2(app_exe, target_exe)

            else:
                self.log_message("找不到打包输出文件", "ERROR")
                return False

            # 复制配置目录
            self.copy_config_files()

            # 创建启动脚本
            self.create_launch_script()

            self.log_message("部署完成")
            return True

        except Exception as e:
            self.log_message(f"部署过程出错: {e}", "ERROR")
            return False

    def copy_config_files(self):
        """复制配置文件到目标目录"""
        self.log_message("复制配置文件...")

        try:
            # 复制主配置文件
            config_files = [
                "config.json",
                "directory_config.json",
                "timer_config.json"
            ]

            for config_file in config_files:
                source_file = self.source_dir / config_file
                if source_file.exists():
                    target_file = self.target_dir / config_file
                    shutil.copy2(source_file, target_file)
                    self.log_message(f"复制配置文件: {config_file}")

            # 复制config目录
            source_config_dir = self.source_dir / "config"
            if source_config_dir.exists():
                target_config_dir = self.target_dir / "config"
                if target_config_dir.exists():
                    shutil.rmtree(target_config_dir)
                shutil.copytree(source_config_dir, target_config_dir)
                self.log_message("复制config目录")

        except Exception as e:
            self.log_message(f"复制配置文件出错: {e}", "WARNING")

    def create_launch_script(self):
        """创建启动脚本"""
        self.log_message("创建启动脚本...")

        try:
            # 创建批处理启动脚本
            bat_content = f'''@echo off
chcp 65001 >nul
title 多平台存稿工具
cd /d "%~dp0"
start "" "多平台存稿工具.exe"
'''

            bat_file = self.target_dir / "启动多平台存稿工具.bat"
            with open(bat_file, 'w', encoding='gbk') as f:
                f.write(bat_content)

            self.log_message("创建启动脚本: 启动多平台存稿工具.bat")

        except Exception as e:
            self.log_message(f"创建启动脚本出错: {e}", "WARNING")

    def clean_temp_files(self):
        """清理临时文件"""
        if self.is_packaging:
            messagebox.showwarning("警告", "打包正在进行中，无法清理临时文件")
            return

        self.log_message("开始清理临时文件...")

        try:
            # 清理PyInstaller生成的临时文件
            temp_dirs = ["build", "dist", "__pycache__"]

            for temp_dir in temp_dirs:
                temp_path = self.package_dir / temp_dir
                if temp_path.exists():
                    shutil.rmtree(temp_path)
                    self.log_message(f"删除临时目录: {temp_dir}")

            # 清理spec文件
            spec_file = self.package_dir / "app.spec"
            if spec_file.exists():
                spec_file.unlink()
                self.log_message("删除spec文件")

            self.log_message("临时文件清理完成")
            messagebox.showinfo("完成", "临时文件清理完成")

        except Exception as e:
            self.log_message(f"清理临时文件出错: {e}", "ERROR")
            messagebox.showerror("错误", f"清理临时文件失败: {e}")

    def start_packaging(self):
        """开始打包流程"""
        if self.is_packaging:
            messagebox.showwarning("警告", "打包正在进行中，请等待完成")
            return

        # 在后台线程中执行打包
        threading.Thread(target=self.package_thread, daemon=True).start()

    def package_thread(self):
        """打包线程"""
        try:
            self.is_packaging = True
            self.package_button.config(state="disabled", text="打包中...")
            self.progress.start()
            self.update_status("正在打包...", "#f39c12")

            # 步骤1: 检查依赖
            self.update_status("检查依赖...", "#f39c12")
            if not self.check_dependencies():
                self.update_status("依赖检查失败", "#e74c3c")
                return

            # 步骤2: 创建目录
            self.update_status("创建目录...", "#f39c12")
            if not self.create_directories():
                self.update_status("目录创建失败", "#e74c3c")
                return

            # 步骤3: 生成spec文件
            self.update_status("生成spec文件...", "#f39c12")
            spec_file_path = self.generate_spec_file()
            if not spec_file_path:
                self.update_status("spec文件生成失败", "#e74c3c")
                return

            # 步骤4: 运行PyInstaller
            self.update_status("执行PyInstaller打包...", "#f39c12")
            if not self.run_pyinstaller(spec_file_path):
                self.update_status("PyInstaller打包失败", "#e74c3c")
                return

            # 步骤5: 部署到目标目录
            self.update_status("部署到目标目录...", "#f39c12")
            if not self.deploy_package():
                self.update_status("部署失败", "#e74c3c")
                return

            # 完成
            self.update_status("打包完成", "#27ae60")
            self.log_message("=" * 50)
            self.log_message("打包完成！")
            self.log_message(f"程序已部署到: {self.target_dir}")
            self.log_message("=" * 50)

            # 显示完成对话框
            self.root.after(0, self.show_completion_dialog)

        except Exception as e:
            self.log_message(f"打包过程出错: {e}", "ERROR")
            self.update_status("打包失败", "#e74c3c")
            self.root.after(0, lambda: messagebox.showerror("错误", f"打包失败: {e}"))

        finally:
            self.is_packaging = False
            self.progress.stop()
            self.package_button.config(state="normal", text="开始打包")

    def show_completion_dialog(self):
        """显示完成对话框"""
        result = messagebox.askyesno(
            "打包完成",
            f"打包完成！\n\n程序已部署到: {self.target_dir}\n\n是否打开目标目录？"
        )

        if result:
            try:
                os.startfile(str(self.target_dir))
            except Exception as e:
                messagebox.showerror("错误", f"无法打开目录: {e}")

    def run(self):
        """运行应用程序"""
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = PackageApp()
        app.run()
    except Exception as e:
        messagebox.showerror("启动错误", f"应用程序启动失败: {e}")

if __name__ == "__main__":
    main()
