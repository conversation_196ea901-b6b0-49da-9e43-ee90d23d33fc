"""
系统工具模块 - 处理系统相关功能
"""

import os
import sys
import subprocess
import re
from typing import Dict, List, Tuple, Optional, Any, Union, Callable

def ensure_tesseract_available() -> bool:
    """确保Tesseract OCR可用"""
    try:
        import pytesseract
        pytesseract.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
        return True
    except Exception as e:
        print(f"Tesseract设置失败: {str(e)}")
        return False

def ensure_ffmpeg_available() -> bool:
    """确保FFmpeg在系统中可用，如果不可用则尝试使用本地FFmpeg"""
    # 首先尝试将当前目录的ffmpeg添加到PATH中
    current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    local_ffmpeg_path = os.path.join(current_dir, "ffmpeg", "bin")
    local_ffmpeg_exe = os.path.join(local_ffmpeg_path, "ffmpeg.exe")

    # 如果本地FFmpeg存在，优先使用
    if os.path.exists(local_ffmpeg_exe):
        print(f"✅ 使用本地FFmpeg: {local_ffmpeg_exe}")
        # 确保添加到环境变量PATH的开头，优先级最高
        os.environ["PATH"] = local_ffmpeg_path + os.pathsep + os.environ.get("PATH", "")
        return True

    # 如果本地找不到，再尝试系统命令
    try:
        result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ 系统已安装FFmpeg: " + result.stdout.splitlines()[0])
            return True
    except subprocess.TimeoutExpired:
        print("警告: 系统FFmpeg检测超时")
    except:
        print("警告: 系统PATH中未找到FFmpeg，正在查找本地安装...")

    # 检查多个可能的位置
    possible_paths = []

    # 1. 当前工作目录下的ffmpeg
    cwd_ffmpeg = os.path.join(os.getcwd(), "ffmpeg", "bin")

    # 2. 网易存稿分支目录下的ffmpeg
    branch_ffmpeg = os.path.join(os.getcwd(), "网易存稿分支", "ffmpeg", "bin")

    # 3. 其他可能的位置
    parent_dir = os.path.dirname(current_dir)
    parent_ffmpeg = os.path.join(parent_dir, "ffmpeg", "bin")

    # 添加所有可能的路径
    possible_paths.extend([
        branch_ffmpeg,   # 网易存稿分支/ffmpeg/bin
        cwd_ffmpeg,      # 当前目录/ffmpeg/bin
        parent_ffmpeg    # 父目录/ffmpeg/bin
    ])

    # 尝试所有可能的路径
    for path in possible_paths:
        ffmpeg_exe = os.path.join(path, "ffmpeg.exe")
        print(f"检查路径: {path}")
        if os.path.exists(ffmpeg_exe):
            print(f"✅ 找到本地FFmpeg: {ffmpeg_exe}")
            # 将FFmpeg路径添加到环境变量PATH中，使得子进程可以找到它
            os.environ["PATH"] = path + os.pathsep + os.environ.get("PATH", "")
            # 测试一下是否能用
            try:
                test_result = subprocess.run([ffmpeg_exe, "-version"], capture_output=True, text=True, timeout=10)
                if test_result.returncode == 0:
                    print("✅ FFmpeg测试成功!")
                    return True
                else:
                    print(f"警告: FFmpeg测试失败: {test_result.stderr}")
            except subprocess.TimeoutExpired:
                print(f"警告: FFmpeg测试超时: {ffmpeg_exe}")
            except Exception as e:
                print(f"警告: FFmpeg测试异常: {str(e)}")
                continue

    print("❌ 未找到FFmpeg，某些功能可能无法正常工作")
    print("搜索的路径包括:")
    for path in possible_paths:
        print(f" - {path}")

    return False

def ensure_moviepy_available() -> bool:
    """确保MoviePy库可用"""
    try:
        import moviepy
        from moviepy.video.io.VideoFileClip import VideoFileClip

        # 获取版本信息
        MOVIEPY_VERSION = getattr(moviepy, '__version__', 'unknown')
        print(f"✅ 成功导入MoviePy (版本: {MOVIEPY_VERSION})")
        print(f"✅ 使用导入路径: moviepy.video.io.VideoFileClip")
        return True
    except ImportError as e:
        print(f"❌ 导入MoviePy失败: {str(e)}")
        print("正在尝试安装MoviePy...")

        try:
            # 尝试安装指定版本
            print("安装MoviePy 2.x版本...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "moviepy>=2.0.0"])

            # 再次尝试导入
            try:
                import moviepy
                from moviepy.video.io.VideoFileClip import VideoFileClip
                MOVIEPY_VERSION = getattr(moviepy, '__version__', 'unknown')
                print(f"✅ 安装并导入MoviePy成功 (版本: {MOVIEPY_VERSION})")
                return True
            except ImportError as e2:
                print(f"❌ 安装后导入仍然失败: {str(e2)}")
                return False
        except Exception as install_err:
            print(f"❌ 安装MoviePy时出错: {str(install_err)}")
            return False

def detect_gpu():
    """检测系统GPU"""
    try:
        # 尝试使用NVIDIA工具检测GPU
        result = subprocess.run(['nvidia-smi', '--query-gpu=name,memory.total', '--format=csv,noheader'],
                               capture_output=True, text=True)

        if result.returncode == 0:
            gpus = []
            for i, line in enumerate(result.stdout.strip().split('\n')):
                if line:
                    name, memory = line.split(',')
                    gpus.append({
                        'index': i,
                        'name': name.strip(),
                        'memory': memory.strip(),
                        'type': 'NVIDIA'
                    })
            return gpus

        # 如果NVIDIA工具不可用，尝试使用其他方法
        # 这里可以添加AMD GPU检测等

        return []
    except Exception as e:
        print(f"GPU检测失败: {str(e)}")
        return []

def get_system_info():
    """获取系统信息"""
    info = {
        'python_version': sys.version,
        'python_path': sys.executable,
        'working_dir': os.getcwd(),
    }

    # 尝试获取CPU和内存信息
    try:
        import psutil
        info['cpu_count'] = psutil.cpu_count()
        info['memory_total'] = psutil.virtual_memory().total / (1024**3)  # GB
        info['memory_available'] = psutil.virtual_memory().available / (1024**3)  # GB
    except ImportError:
        info['cpu_count'] = os.cpu_count()
        info['memory_total'] = 'Unknown'
        info['memory_available'] = 'Unknown'

    # 获取GPU信息
    info['gpus'] = detect_gpu()

    return info

def print_system_info():
    """打印系统信息"""
    info = get_system_info()

    print(f"Python 版本: {info['python_version']}")
    print(f"Python 路径: {info['python_path']}")
    print(f"当前工作目录: {info['working_dir']}")

    if isinstance(info['cpu_count'], int):
        print(f"CPU 核心数: {info['cpu_count']}")

    if isinstance(info['memory_total'], float):
        print(f"总内存: {info['memory_total']:.1f} GB")

    if isinstance(info['memory_available'], float):
        print(f"可用内存: {info['memory_available']:.1f} GB")

    if info['gpus']:
        print("GPU 信息:")
        for gpu in info['gpus']:
            print(f"  - {gpu['name']} ({gpu['memory']})")
    else:
        print("未检测到GPU")
