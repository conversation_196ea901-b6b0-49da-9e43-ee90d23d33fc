"""
头条平台数据查询模块 - 从备份文件恢复
"""

import os
import time
import json
import datetime
import traceback
import threading
from queue import Queue
from typing import Dict, Any, Optional, List, Callable
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

from 网易号存稿.platforms.toutiao.login import ToutiaoLogin
from 网易号存稿.common.constants import AccountStatus


class ToutiaoDataQuery:
    """今日头条平台数据查询类"""

    # 平台标识符 - 用于模块识别和配置加载
    PLATFORM_ID = "toutiao"
    PLATFORM_NAME = "今日头条平台"

    def __init__(self, account_dir: str = None, config_manager=None, account_manager=None, log_callback: Callable = None, headless: bool = True):
        """
        初始化头条数据查询类

        Args:
            account_dir: 账号目录（兼容旧版调用方式）
            config_manager: 配置管理器（新版架构）
            account_manager: 账号管理器（新版架构）
            log_callback: 日志回调函数
            headless: 是否使用无头模式，默认为True
        """
        # 兼容新旧两种调用方式
        self.config_manager = config_manager
        self.account_manager = account_manager
        self.log_callback = log_callback
        self.headless = headless

        # 获取账号目录 - 优先使用account_manager，其次使用直接传入的account_dir
        if account_manager and hasattr(account_manager, 'account_dir'):
            self.account_dir = account_manager.account_dir
        elif account_dir:
            self.account_dir = account_dir
        else:
            self.account_dir = "accounts"

        # 设置平台标识
        self.platform_id = self.PLATFORM_ID
        self.platform_name = self.PLATFORM_NAME

        # 创建登录实例
        self.login = ToutiaoLogin(log_callback)

        # 浏览器驱动
        self.driver = None

    def log(self, message: str, internal: bool = False) -> None:
        """
        记录日志

        Args:
            message: 日志消息
            internal: 是否为内部日志（内部日志会添加平台标记，但不显示给用户）
        """
        if internal:
            # 内部日志添加平台标记，用于调试和模块识别
            formatted_message = f"[{self.platform_id}] {message}"
        else:
            # 用户日志不显示平台标记
            formatted_message = message

        if self.log_callback:
            self.log_callback(formatted_message)
        else:
            print(formatted_message)

    def query_account_data(self, account: str) -> Dict[str, Any]:
        """
        查询单个账号的数据

        Args:
            account: 账号名称

        Returns:
            账号数据字典
        """
        # 初始化结果
        result = {
            "账号": account,
            "用户名": "",
            "累计收益": 0.0,
            "昨日收益": 0.0,
            "总播放": 0.0,
            "昨日播放": 0.0,
            "总粉丝": 0.0,
            "昨日粉丝": 0.0,
            "草稿箱数量": 0,
            "可提现": 0.0,
            "总提现": 0.0,
            "最近提现日期": "",
            "最近提现金额": 0.0,
            "七日收益": [],
            "查询时间": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "状态": AccountStatus.QUERY_FAILED,
            "错误信息": ""
        }

        try:
            # 查找Cookie文件
            cookie_path = self._find_cookie_file(account)
            if not cookie_path:
                result["错误信息"] = f"未找到账号 {account} 的Cookie文件"
                result["状态"] = AccountStatus.COOKIE_NOT_FOUND
                return result

            # 使用Cookie登录 - 传递账号信息用于合并日志
            success, driver = self.login.login_with_cookies(cookie_path, headless=self.headless, account=account)

            if not success or not driver:
                result["错误信息"] = "登录失败"
                result["状态"] = AccountStatus.LOGIN_FAILED
                return result

            self.driver = driver

            # 1. 获取首页数据
            self.log("正在获取首页数据...")
            home_data = self._get_home_data()
            result.update(home_data)

            # 2. 获取草稿箱数据
            self.log("正在获取草稿箱数据...")
            draft_data = self._get_draft_data()
            result.update(draft_data)

            # 3. 获取提现数据
            self.log("正在获取提现数据...")
            withdraw_data = self._get_withdraw_data()
            result.update(withdraw_data)

            # 统一字段映射标准：草稿箱、待提现、七日收益、查询时间、总收益
            result["总收益"] = result.get("累计收益", 0.0)  # 统一标准字段
            result["草稿箱"] = result.get("草稿箱数量", 0)    # 统一标准字段
            result["待提现"] = result.get("可提现", 0.0)     # 统一标准字段
            result["查询时间"] = result.get("查询时间", "")   # 统一标准字段
            result["更新时间"] = result.get("查询时间", "")   # 保持兼容

            result["状态"] = AccountStatus.SUCCESS
            self.log(f"账号 {account} 数据查询完成")

        except Exception as e:
            self.log(f"查询账号 {account} 数据时发生错误: {str(e)}")
            result["错误信息"] = str(e)
            traceback.print_exc()

        finally:
            # 关闭浏览器
            if self.driver:
                try:
                    self.driver.quit()
                except:
                    pass
                self.driver = None

        return result

    def _find_cookie_file(self, account: str) -> Optional[str]:
        """查找账号的Cookie文件，只支持账号名.txt格式"""
        # 只查找账号名.txt格式
        cookie_path = os.path.join(self.account_dir, f"{account}.txt")
        if os.path.exists(cookie_path):
            return cookie_path

        self.log(f"未找到账号 {account} 的Cookie文件")
        return None

    def _get_home_data(self) -> Dict[str, Any]:
        """
        获取首页数据

        Returns:
            首页数据字典
        """
        data = {}

        try:
            # 导航到首页
            self.driver.get("https://mp.toutiao.com/profile_v4/index")
            self.log("正在等待首页加载...")
            time.sleep(5)  # 增加等待时间

            # 等待页面主要内容加载
            try:
                from selenium.webdriver.support.ui import WebDriverWait
                from selenium.webdriver.support import expected_conditions as EC
                from selenium.webdriver.common.by import By
                from selenium.common.exceptions import TimeoutException

                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, '//*[@id="root"]'))
                )
                self.log("首页主要内容已加载")
            except TimeoutException:
                self.log("首页加载超时，继续尝试获取数据")

            # 按照源文件processor.py中的正确顺序获取数据

            # 按照源文件processor.py的完全一致的实现和字段名

            # 获取用户名
            username_xpath = '//*[@id="masterRoot"]/div/div[1]/div/div/div[3]/div[1]/a/span/div/div/div[1]'
            username = self.safe_get_element_text(username_xpath, timeout=8)
            data["用户名"] = username
            self.log(f"用户名: {username}")

            # 获取累计收益
            total_income_xpath = '//*[@id="root"]/div/div/div[1]/div[1]/div[1]/div[3]/div/div[2]/a'
            total_income_text = self.safe_get_element_text(total_income_xpath, timeout=8)
            data["累计收益"] = self.parse_number_with_unit(total_income_text)
            self.log(f"累计收益: {total_income_text} -> {data['累计收益']}")

            # 获取昨日收益
            yesterday_income_xpath = '//*[@id="root"]/div/div/div[1]/div[1]/div[1]/div[3]/div/div[3]/p/span'
            yesterday_income_text = self.safe_get_element_text(yesterday_income_xpath, timeout=8)
            data["昨日收益"] = self.parse_number_with_unit(yesterday_income_text)
            self.log(f"昨日收益: {yesterday_income_text} -> {data['昨日收益']}")

            # 获取总播放
            total_views_xpath = '//*[@id="root"]/div/div/div[1]/div[1]/div[1]/div[2]/div/div[2]'
            total_views_text = self.safe_get_element_text(total_views_xpath, timeout=8)
            data["总播放"] = self.parse_number_with_unit(total_views_text)
            self.log(f"总播放: {total_views_text} -> {data['总播放']}")

            # 获取昨日播放
            yesterday_views_xpath = '//*[@id="root"]/div/div/div[1]/div[1]/div[1]/div[2]/div/div[3]/p/span'
            yesterday_views_text = self.safe_get_element_text(yesterday_views_xpath, timeout=8)
            data["昨日播放"] = self.parse_number_with_unit(yesterday_views_text)
            self.log(f"昨日播放: {yesterday_views_text} -> {data['昨日播放']}")

            # 获取总粉丝
            total_fans_xpath = '//*[@id="root"]/div/div/div[1]/div[1]/div[1]/div[1]/div/div[2]/a'
            total_fans_text = self.safe_get_element_text(total_fans_xpath, timeout=8)
            data["总粉丝"] = self.parse_number_with_unit(total_fans_text)
            self.log(f"总粉丝: {total_fans_text} -> {data['总粉丝']}")

            # 获取昨日粉丝
            yesterday_fans_xpath = '//*[@id="root"]/div/div/div[1]/div[1]/div[1]/div[1]/div/div[3]/p/span'
            yesterday_fans_text = self.safe_get_element_text(yesterday_fans_xpath, timeout=8)
            data["昨日粉丝"] = self.parse_number_with_unit(yesterday_fans_text)
            self.log(f"昨日粉丝: {yesterday_fans_text} -> {data['昨日粉丝']}")

            # 获取七日收益数据
            seven_day_data = self._get_seven_day_income()
            data["七日收益"] = seven_day_data

        except Exception as e:
            self.log(f"获取首页数据时发生错误: {str(e)}")

        return data

    def _get_seven_day_income(self) -> List[Dict[str, Any]]:
        """
        获取七日收益数据

        Returns:
            七日收益数据列表，格式与源文件processor.py一致
        """
        seven_day_data = []

        try:
            # 导航到收益分析页面 - 与源文件processor.py保持一致
            income_url = "https://mp.toutiao.com/profile_v4/analysis/income-overview"
            self.log(f"正在访问收益分析页面: {income_url}")
            self.driver.get(income_url)
            self.log("正在等待收益分析页面加载...")
            time.sleep(5)  # 等待页面加载

            # 等待页面主要内容加载
            try:
                from selenium.webdriver.support.ui import WebDriverWait
                from selenium.webdriver.support import expected_conditions as EC
                from selenium.webdriver.common.by import By
                from selenium.common.exceptions import TimeoutException

                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, '//*[@id="root"]'))
                )
                self.log("收益分析页面主要内容已加载")
            except TimeoutException:
                self.log("收益分析页面加载超时，继续尝试获取数据")

            # 等待表格加载
            self.log("等待七日收益表格加载...")
            time.sleep(3)

            # 首先检查表格是否存在
            table_xpath = '//*[@id="root"]/div/div[4]/div/div/div/div/div/div[1]/div/div/table'
            table_exists = False

            try:
                WebDriverWait(self.driver, 8).until(
                    EC.presence_of_element_located((By.XPATH, table_xpath))
                )
                table_exists = True
                self.log("找到七日收益表格")
            except TimeoutException:
                self.log("未找到七日收益表格，可能页面结构发生变化")
                return seven_day_data

            if not table_exists:
                return seven_day_data

            # 动态检测实际有多少行数据，但限制只获取7天
            max_rows_to_check = 10  # 最多检查10行
            actual_rows = 0

            # 先检测有多少行实际存在
            for row in range(1, max_rows_to_check + 1):
                date_xpath = f'//*[@id="root"]/div/div[4]/div/div/div/div/div/div[1]/div/div/table/tbody/tr[{row}]/td[1]/div'
                try:
                    element = WebDriverWait(self.driver, 1).until(
                        EC.presence_of_element_located((By.XPATH, date_xpath))
                    )
                    if element.text.strip():  # 如果有内容
                        actual_rows = row
                except:
                    break  # 如果找不到元素，说明没有更多行了

            # 限制只获取7天的数据
            actual_rows = min(actual_rows, 7)
            # self.log(f"检测到 {actual_rows} 行数据，限制获取7天收益数据")  # 隐藏调试日志

            # 获取实际存在的行数据
            for row in range(1, actual_rows + 1):
                try:
                    # 获取日期
                    date_xpath = f'//*[@id="root"]/div/div[4]/div/div/div/div/div/div[1]/div/div/table/tbody/tr[{row}]/td[1]/div'
                    date_text = self.safe_get_element_text(date_xpath, timeout=2)

                    # 获取收益
                    income_xpath = f'//*[@id="root"]/div/div[4]/div/div/div/div/div/div[1]/div/div/table/tbody/tr[{row}]/td[2]/div'
                    income_text = self.safe_get_element_text(income_xpath, timeout=2)
                    income_value = self.parse_number_with_unit(income_text)

                    if date_text:  # 只有当日期不为空时才添加
                        # 按照源文件processor.py的格式构建数据
                        day_data = {
                            "日期": date_text,
                            "收益": income_value,
                            "收益文本": income_text
                        }
                        seven_day_data.append(day_data)
                        self.log(f"第{row}天收益: {date_text} - {income_text} -> {income_value}")
                    else:
                        self.log(f"第{row}行日期为空，跳过")

                except Exception as e:
                    self.log(f"获取第{row}天收益数据失败: {type(e).__name__}")
                    continue

        except Exception as e:
            self.log(f"获取七日收益数据时发生错误: {str(e)}")

        # self.log(f"成功获取 {len(seven_day_data)} 天的收益数据: {seven_day_data}")  # 隐藏调试日志
        return seven_day_data

    def _get_draft_data(self) -> Dict[str, Any]:
        """
        获取草稿箱数据

        Returns:
            草稿箱数据字典
        """
        data = {}

        try:
            # 导航到草稿箱页面
            draft_url = "https://mp.toutiao.com/profile_v4/manage/draft"
            self.log(f"正在访问草稿箱页面: {draft_url}")
            self.driver.get(draft_url)
            self.log("正在等待草稿箱页面加载...")
            time.sleep(5)  # 增加等待时间

            # 等待页面主要内容加载
            try:
                from selenium.webdriver.support.ui import WebDriverWait
                from selenium.webdriver.support import expected_conditions as EC
                from selenium.webdriver.common.by import By
                from selenium.common.exceptions import TimeoutException

                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, '//*[@id="masterRoot"]'))
                )
                self.log("草稿箱页面主要内容已加载")
            except TimeoutException:
                self.log("草稿箱页面加载超时，继续尝试获取数据")

            # 获取草稿箱数量 - 尝试多个可能的XPath
            draft_count_xpaths = [
                '//*[@id="masterRoot"]/div/div[3]/section/main/div[2]/div/div[1]/div/div/div[10]/span/span',
                '//*[@id="masterRoot"]//span[contains(text(), "草稿")]/../span',
                '//*[contains(text(), "草稿")]/..//span[contains(@class, "count")]',
                '//*[contains(text(), "草稿")]//following-sibling::span',
                '//*[contains(text(), "草稿")]//span'
            ]

            draft_count_text = ""
            for xpath in draft_count_xpaths:
                draft_count_text = self.safe_get_element_text(xpath, timeout=3)
                if draft_count_text:
                    self.log(f"使用XPath获取到草稿数量: {xpath}")
                    break

            # 解析草稿箱数量
            draft_count = 0
            if draft_count_text:
                try:
                    import re
                    # 提取数字
                    numbers = re.findall(r'\d+', draft_count_text)
                    if numbers:
                        draft_count = int(numbers[0])
                except:
                    draft_count = 0

            data["草稿箱数量"] = draft_count
            self.log(f"草稿箱数量: {draft_count_text} -> {draft_count}")

        except Exception as e:
            self.log(f"获取草稿箱数据时发生错误: {str(e)}")
            data["草稿箱数量"] = 0

        return data

    def _get_withdraw_data(self) -> Dict[str, Any]:
        """
        获取提现数据

        Returns:
            提现数据字典
        """
        data = {}

        try:
            # 导航到提现页面
            withdraw_url = "https://mp.toutiao.com/profile_v4/personal/checkout-center"
            self.log(f"正在访问提现页面: {withdraw_url}")
            self.driver.get(withdraw_url)
            self.log("正在等待提现页面加载...")
            time.sleep(5)  # 增加等待时间

            # 等待页面主要内容加载
            try:
                from selenium.webdriver.support.ui import WebDriverWait
                from selenium.webdriver.support import expected_conditions as EC
                from selenium.webdriver.common.by import By
                from selenium.common.exceptions import TimeoutException

                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, '//*[@id="root"]'))
                )
                self.log("提现页面主要内容已加载")
            except TimeoutException:
                self.log("提现页面加载超时，继续尝试获取数据")

            # 获取可提现金额
            available_xpath = '//*[@id="root"]/div/div[2]/div[1]/div[2]/div/div[2]'
            available_text = self.safe_get_element_text(available_xpath, timeout=8)
            data["可提现"] = self.parse_number_with_unit(available_text)
            self.log(f"可提现: {available_text} -> {data['可提现']}")

            # 获取总提现金额
            total_withdraw_xpath = '//*[@id="root"]/div/div[2]/div[1]/div[3]/div/div[2]'
            total_withdraw_text = self.safe_get_element_text(total_withdraw_xpath, timeout=8)
            data["总提现"] = self.parse_number_with_unit(total_withdraw_text)
            self.log(f"总提现: {total_withdraw_text} -> {data['总提现']}")

            # 获取最近提现记录
            try:
                # 等待提现记录表格加载
                try:
                    WebDriverWait(self.driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, '//*[@id="root"]/div/div[2]/div[3]/div/div/div/div/div/div/div/div/table'))
                    )
                    self.log("找到提现记录表格")

                    # 提现日期
                    recent_date_xpath = '//*[@id="root"]/div/div[2]/div[3]/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[1]/div'
                    recent_date = self.safe_get_element_text(recent_date_xpath, timeout=3)
                    data["最近提现日期"] = recent_date

                    # 提现金额
                    recent_amount_xpath = '//*[@id="root"]/div/div[2]/div[3]/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[5]/div'
                    recent_amount_text = self.safe_get_element_text(recent_amount_xpath, timeout=3)
                    data["最近提现金额"] = self.parse_number_with_unit(recent_amount_text)

                    self.log(f"最近提现: {recent_date} - {recent_amount_text} -> {data['最近提现金额']}")

                except TimeoutException:
                    self.log("未找到提现记录表格，可能没有提现记录")
                    data["最近提现日期"] = ""
                    data["最近提现金额"] = 0.0

            except Exception as e:
                self.log(f"获取最近提现记录失败: {type(e).__name__}")
                data["最近提现日期"] = ""
                data["最近提现金额"] = 0.0

        except Exception as e:
            self.log(f"获取提现数据时发生错误: {str(e)}")
            data["可提现"] = 0.0
            data["总提现"] = 0.0
            data["最近提现日期"] = ""
            data["最近提现金额"] = 0.0

        return data

    def safe_get_element_text(self, xpath: str, timeout: int = 5) -> str:
        """
        安全获取元素文本内容

        Args:
            xpath: 元素的XPath
            timeout: 超时时间

        Returns:
            元素文本内容，如果获取失败则返回空字符串
        """
        try:
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.webdriver.common.by import By
            from selenium.common.exceptions import TimeoutException, NoSuchElementException

            element = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((By.XPATH, xpath))
            )
            return element.text.strip()
        except (TimeoutException, NoSuchElementException):
            return ""
        except Exception:
            return ""

    def parse_number_with_unit(self, text: str) -> float:
        """
        解析带单位的数字文本

        Args:
            text: 包含数字和单位的文本

        Returns:
            解析后的数字值
        """
        if not text:
            return 0.0

        try:
            import re
            # 移除所有非数字、小数点、万、千、亿的字符
            cleaned_text = re.sub(r'[^\d.\w万千亿]', '', text)

            # 提取数字部分
            number_match = re.search(r'(\d+\.?\d*)', cleaned_text)
            if not number_match:
                return 0.0

            number = float(number_match.group(1))

            # 处理单位
            if '亿' in cleaned_text:
                number *= *********
            elif '万' in cleaned_text:
                number *= 10000
            elif '千' in cleaned_text:
                number *= 1000

            return number

        except Exception:
            return 0.0

    def query_accounts_concurrent(self, accounts: List[str], max_workers: int = 3,
                                data_callback=None, progress_callback=None) -> Dict[str, Dict[str, Any]]:
        """
        并发查询多个账号的数据 - 真正的多线程实现

        Args:
            accounts: 账号列表
            max_workers: 最大并发数
            data_callback: 数据回调函数
            progress_callback: 进度回调函数

        Returns:
            账号数据字典
        """
        if not accounts:
            return {}

        self.log(f"开始并发查询 {len(accounts)} 个账号，最大并发数: {max_workers}")

        # 使用ToutiaoDataQueryManager进行真正的多线程查询
        query_manager = ToutiaoDataQueryManager(
            account_dir=self.account_dir,
            log_callback=self.log_callback,
            headless=self.headless
        )

        # 调用真正的多线程查询方法
        return query_manager.query_accounts_concurrent(
            accounts=accounts,
            max_workers=max_workers,
            data_callback=data_callback,
            progress_callback=progress_callback
        )


class ToutiaoDataQueryManager:
    """今日头条数据查询管理器 - 支持多线程并发查询"""

    def __init__(self, account_dir: str, log_callback: Callable = None, headless: bool = True):
        """
        初始化查询管理器

        Args:
            account_dir: 账号目录
            log_callback: 日志回调函数
            headless: 是否使用无头模式，默认为True
        """
        self.account_dir = account_dir
        self.log_callback = log_callback
        self.headless = headless
        self.results = {}
        self.lock = threading.Lock()

    def log(self, message: str) -> None:
        """
        记录日志

        Args:
            message: 日志消息
        """
        if self.log_callback:
            self.log_callback(f"[头条查询管理器] {message}")
        else:
            print(f"[头条查询管理器] {message}")

    def query_single_account_thread(self, account: str, result_queue: Queue) -> None:
        """
        单个账号查询线程函数

        Args:
            account: 账号名称
            result_queue: 结果队列
        """
        try:
            # 为每个线程创建独立的查询对象
            query = ToutiaoDataQuery(log_callback=self.log_callback, headless=self.headless)
            query.account_dir = self.account_dir

            # 查询账号数据
            result = query.query_account_data(account)

            # 将结果放入队列
            result_queue.put((account, result))

        except Exception as e:
            error_result = {
                "账号": account,
                "状态": "线程异常",
                "错误信息": str(e),
                "查询时间": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            result_queue.put((account, error_result))
            self.log(f"账号 {account} 查询线程异常: {str(e)}")

    def query_accounts_concurrent(self, accounts: List[str], max_workers: int = 3,
                                data_callback=None, progress_callback=None) -> Dict[str, Dict[str, Any]]:
        """
        并发查询多个账号的数据 - 优化版：真正的并发，无批次等待

        Args:
            accounts: 账号列表
            max_workers: 最大并发数
            data_callback: 数据回调函数
            progress_callback: 进度回调函数

        Returns:
            账号数据字典
        """
        if not accounts:
            return {}

        self.log(f"🚀 开始并发查询 {len(accounts)} 个账号，最大并发数: {max_workers}")

        from concurrent.futures import ThreadPoolExecutor, as_completed
        import threading

        # 用于线程安全的结果存储
        results_lock = threading.Lock()
        completed_count = 0

        def query_single_account_wrapper(account):
            """单账号查询包装器"""
            try:
                # 为每个线程创建独立的查询对象
                query = ToutiaoDataQuery(log_callback=self.log_callback, headless=self.headless)
                query.account_dir = self.account_dir

                # 查询账号数据
                result = query.query_account_data(account)
                return account, result
            except Exception as e:
                self.log(f"❌ 账号 {account} 查询异常: {str(e)}")
                error_result = {
                    "账号": account,
                    "状态": "失败",
                    "错误信息": str(e),
                    "查询时间": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                return account, error_result

        # 使用ThreadPoolExecutor实现真正的并发
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务到线程池
            future_to_account = {executor.submit(query_single_account_wrapper, account): account
                               for account in accounts}

            # 处理完成的任务，无需等待批次
            for future in as_completed(future_to_account):
                account = future_to_account[future]

                try:
                    account_name, result = future.result()

                    # 线程安全地更新结果
                    with results_lock:
                        self.results[account_name] = result
                        completed_count += 1

                    # 立即调用数据回调
                    if data_callback:
                        data_callback(account_name, result)

                    # 调用进度回调 - 更新侧边栏任务进度
                    if progress_callback:
                        progress_callback(completed_count, len(accounts), account_name)

                    self.log(f"✅ 账号 {account_name} 查询完成 ({completed_count}/{len(accounts)})")

                except Exception as e:
                    self.log(f"❌ 处理账号 {account} 结果时出错: {str(e)}")

                    # 记录错误结果
                    with results_lock:
                        completed_count += 1
                        self.results[account] = {
                            "账号": account,
                            "状态": "失败",
                            "错误信息": f"结果处理错误: {str(e)}",
                            "查询时间": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        }

        self.log(f"🎉 并发查询完成，共处理 {len(self.results)} 个账号")
        return self.results
