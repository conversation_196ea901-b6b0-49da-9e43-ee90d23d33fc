# 多平台存稿系统目录结构使用指南

## 概述

本指南详细说明了多平台存稿系统的目录结构设计、使用方法和最佳实践。

## 目录结构总览

```
D:\网易号全自动\
├── 截图\                    # 共用目录 - 系统截图
│   ├── 操作截图\
│   ├── 错误截图\
│   ├── 成功截图\
│   └── 调试截图\
├── 数据\                    # 共用目录 - 系统数据
│   ├── 配置\
│   ├── 日志\
│   ├── 缓存\
│   └── 统计\
├── 违规\                    # 共用目录 - 违规处理
│   ├── 违规内容\
│   ├── 处理记录\
│   └── 申诉材料\
├── 未处理\                  # 共用目录 - 待处理内容
│   ├── 待发布文章\
│   ├── 待上传视频\
│   ├── 待处理图片\
│   └── 待执行任务\
├── 已处理\                  # 共用目录 - 已处理内容
│   ├── 已编辑文章\
│   ├── 已处理视频\
│   ├── 已优化图片\
│   └── 处理中任务\
├── 已完成\                  # 共用目录 - 已完成内容
│   ├── 已发布内容\
│   ├── 已上传内容\
│   ├── 完成任务\
│   └── 历史归档\
├── 网易号账号\              # 账号目录 - 网易号
│   ├── 账号信息\
│   ├── 发布记录\
│   ├── 数据统计\
│   └── 账号设置\
├── 头条号账号\              # 账号目录 - 头条号
│   ├── 账号信息\
│   ├── 发布记录\
│   ├── 数据统计\
│   └── 推荐数据\
└── 大鱼号账号\              # 账号目录 - 大鱼号
    ├── 账号信息\
    ├── 发布记录\
    ├── 数据统计\
    └── 收益数据\
```

## 目录使用说明

### 1. 共用目录

#### 截图目录 (`截图\`)
**用途**: 存储系统运行过程中的各种截图

- **操作截图**: 正常操作过程的截图记录
- **错误截图**: 出现错误时的截图，用于调试
- **成功截图**: 操作成功时的截图，用于确认
- **调试截图**: 开发调试时的截图

**使用示例**:
```python
from 网易号存稿.common.directory_utils import directory_manager

# 保存操作截图
screenshot_path = directory_manager.save_screenshot(
    "temp_screenshot.png", 
    screenshot_type="operation",
    custom_name="video_upload_success"
)
```

#### 数据目录 (`数据\`)
**用途**: 存储系统配置、日志和统计数据

- **配置**: 系统配置文件、用户设置
- **日志**: 运行日志、错误日志、操作记录
- **缓存**: 临时缓存数据、会话数据
- **统计**: 统计分析数据、报表数据

**使用示例**:
```python
# 获取日志文件路径
log_file = directory_manager.get_log_file_path("upload_log")

# 获取配置文件路径
config_file = directory_manager.get_config_file_path("user_settings")
```

#### 违规目录 (`违规\`)
**用途**: 存储违规相关的内容和处理记录

- **违规内容**: 被标记为违规的原始内容备份
- **处理记录**: 违规处理的详细记录和流程
- **申诉材料**: 申诉相关的文档和证据材料

**使用示例**:
```python
# 备份违规内容
success = directory_manager.backup_violation_content("违规视频.mp4")
```

#### 工作流目录
**内容流转**: `未处理` → `已处理` → `已完成`

- **未处理**: 新上传或待处理的内容
- **已处理**: 已经过初步处理，等待进一步操作
- **已完成**: 最终完成的内容，可以归档

**使用示例**:
```python
# 将文件从待处理移动到已处理
success = directory_manager.move_file_to_stage(
    "video.mp4", 
    from_stage="pending", 
    to_stage="processed",
    content_type="videos"
)
```

### 2. 账号目录

#### 网易号账号 (`网易号账号\`)
**用途**: 管理网易号平台的账号信息和数据

- **账号信息**: 账号基本信息、登录凭证
- **发布记录**: 内容发布历史和状态
- **数据统计**: 播放量、粉丝数等统计数据
- **账号设置**: 账号相关的配置和设置

#### 头条号账号 (`头条号账号\`)
**用途**: 管理头条号平台的账号信息和数据

- **推荐数据**: 头条号特有的推荐机制数据

#### 大鱼号账号 (`大鱼号账号\`)
**用途**: 管理大鱼号平台的账号信息和数据

- **收益数据**: 大鱼号特有的收益统计数据

**使用示例**:
```python
# 获取网易号账号信息目录
account_dir = directory_manager.get_account_dir("netease", "accounts")

# 获取发布记录目录
history_dir = directory_manager.get_account_dir("netease", "publish_history")
```

## 代码集成

### 1. 导入目录管理器

```python
from 网易号存稿.common.directory_utils import directory_manager
```

### 2. 初始化目录结构

```python
# 初始化所有必要的目录
success = directory_manager.initialize_directories()
if success:
    print("目录结构初始化成功")
else:
    print("目录结构初始化失败")
```

### 3. 常用操作示例

```python
# 获取各种目录路径
screenshots_dir = directory_manager.get_screenshots_dir("operation")
pending_videos_dir = directory_manager.get_pending_dir("videos")
netease_accounts_dir = directory_manager.get_account_dir("netease", "accounts")

# 确保目录存在
directory_manager.ensure_directory_exists(screenshots_dir)

# 保存截图
screenshot_path = directory_manager.save_screenshot(
    "screenshot.png", 
    "success", 
    "upload_complete"
)

# 移动文件到不同阶段
directory_manager.move_file_to_stage(
    "video.mp4",
    from_stage="pending",
    to_stage="processed",
    content_type="videos"
)

# 备份违规内容
directory_manager.backup_violation_content("违规内容.txt")
```

## 配置管理

### 1. 目录配置文件

系统使用 `directory_config.json` 文件来管理目录结构配置：

```json
{
  "platform_config": {
    "root_directory": "D:\\网易号全自动",
    "version": "1.0.0"
  },
  "shared_directories": {
    "directories": {
      "screenshots": {
        "path": "截图",
        "subdirectories": {
          "operation": "操作截图",
          "error": "错误截图"
        }
      }
    }
  }
}
```

### 2. 常量定义

在 `constants.py` 中定义了目录路径常量：

```python
SHARED_DIRECTORIES = {
    "screenshots": "D:\\网易号全自动\\截图",
    "data": "D:\\网易号全自动\\数据",
    # ...
}

ACCOUNT_DIRECTORIES = {
    "netease": "D:\\网易号全自动\\网易号账号",
    "toutiao": "D:\\网易号全自动\\头条号账号",
    # ...
}
```

## 最佳实践

### 1. 目录命名规范

- 使用中文目录名，保持与用户需求一致
- 目录名称简洁明了，避免过长
- 保持目录结构的层次清晰

### 2. 文件管理规范

- 按照工作流阶段组织文件
- 及时清理临时文件和缓存
- 重要文件要有备份机制

### 3. 权限管理

- 系统进程有完整的读写权限
- 用户操作有适当的权限限制
- 敏感数据要加密保护

### 4. 维护建议

- 定期清理过期文件
- 监控目录大小和使用情况
- 及时更新配置和文档

## 故障排除

### 1. 目录不存在

```python
# 检查并创建目录
if not os.path.exists(directory_path):
    directory_manager.ensure_directory_exists(directory_path)
```

### 2. 权限问题

- 确保程序有足够的文件系统权限
- 检查目录的访问控制设置
- 以管理员权限运行程序

### 3. 路径问题

- 使用绝对路径避免相对路径问题
- 注意Windows路径分隔符的处理
- 检查路径中的特殊字符

## 扩展和定制

### 1. 添加新平台

```python
# 在constants.py中添加新平台
ACCOUNT_DIRECTORIES["new_platform"] = "D:\\网易号全自动\\新平台账号"

# 在directory_config.json中添加配置
```

### 2. 添加新的共用目录

```python
# 在SHARED_DIRECTORIES中添加新目录
SHARED_DIRECTORIES["new_shared"] = "D:\\网易号全自动\\新共用目录"
```

### 3. 自定义子目录

```python
# 在SUBDIRECTORIES中添加新的子目录配置
SUBDIRECTORIES["new_category"] = {
    "sub1": "子目录1",
    "sub2": "子目录2"
}
```

## 总结

通过这套目录结构设计，可以实现：

1. **清晰的功能分离**: 共用功能和平台特定功能分离
2. **良好的扩展性**: 易于添加新平台和新功能
3. **明确的数据流程**: 从未处理到已完成的清晰流程
4. **有效的数据管理**: 分类存储，便于管理和维护
5. **统一的接口**: 通过directory_manager提供统一的目录操作接口

这种设计既满足了用户的具体需求，又保持了系统的灵活性和可维护性。
