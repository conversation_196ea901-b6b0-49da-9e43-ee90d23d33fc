"""
封面处理模块 - 处理视频封面的提取和编辑
"""

import os
from typing import Dict, Tuple, Optional, Callable, Union
from PIL import Image

from ..utils import get_color_map

class CoverProcessor:
    """封面处理类，负责封面的提取和编辑"""

    def __init__(self, logger: Callable = print):
        """
        初始化封面处理器

        Args:
            logger: 日志记录函数
        """
        self.logger = logger
        self.color_map = get_color_map()

    def add_text_to_cover(self,
                         cover_path: str,
                         base_filename: str,
                         top_text: str = "",
                         bottom_text: str = "",
                         top_color: str = "#FFFFFF",
                         bottom_color: str = "#FFFFFF",
                         font_size: int = 60,
                         auto_use_filename: bool = True,
                         cover_resolution: Tuple[int, int] = None) -> bool:
        """
        添加文字到封面图片

        Args:
            cover_path: 封面图片路径
            base_filename: 基础文件名（用于自动生成文字）
            top_text: 上方文字
            bottom_text: 下方文字
            top_color: 上方文字颜色（十六进制）
            bottom_color: 下方文字颜色（十六进制）
            font_size: 字体大小
            auto_use_filename: 是否自动使用文件名
            cover_resolution: 封面分辨率，如果指定则调整大小

        Returns:
            bool: 是否成功添加文字
        """
        from .text import add_text_to_cover
        return add_text_to_cover(
            self,
            cover_path,
            base_filename,
            top_text,
            bottom_text,
            top_color,
            bottom_color,
            font_size,
            auto_use_filename,
            cover_resolution
        )

    def add_watermark_to_cover(self,
                              cover_path: str,
                              watermark_color: str = "#FFFFFF",
                              opacity: float = 0.1,
                              position: str = "全屏",
                              quantity: int = 1) -> bool:
        """
        为封面添加纯色水印

        Args:
            cover_path: 封面图片路径
            watermark_color: 水印颜色（十六进制）
            opacity: 水印透明度（0.0-1.0）
            position: 水印位置（"全屏", "左上角", "右上角", "左下角", "右下角", "中心"）
            quantity: 水印数量（仅在非全屏模式下有效）

        Returns:
            bool: 是否成功添加水印
        """
        from .watermark import add_watermark_to_cover
        return add_watermark_to_cover(
            self,
            cover_path,
            watermark_color,
            opacity,
            position,
            quantity
        )

    def resize_cover(self,
                    cover_path: str,
                    width: int,
                    height: int) -> bool:
        """
        调整封面大小

        Args:
            cover_path: 封面图片路径
            width: 宽度
            height: 高度

        Returns:
            bool: 是否成功调整大小
        """
        from .resizer import resize_cover
        return resize_cover(
            self,
            cover_path,
            width,
            height
        )
