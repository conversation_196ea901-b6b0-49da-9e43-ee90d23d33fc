"""
任务存储管理模块
负责任务的持久化存储、备份和恢复
"""

import os
import json
import shutil
import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

from .task_model import ScheduledTask, TaskType, TaskStatus
from .time_utils import TimeUtils


class TaskStorage:
    """任务存储管理器"""
    
    def __init__(self, storage_dir: str = None):
        """
        初始化任务存储管理器
        
        Args:
            storage_dir: 存储目录路径，默认为当前目录下的config目录
        """
        if storage_dir is None:
            # 默认存储在项目根目录的config目录下
            current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            storage_dir = os.path.join(current_dir, "config", "scheduler")
        
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置文件路径
        self.config_file = self.storage_dir / "scheduler_config.json"
        self.tasks_file = self.storage_dir / "tasks.json"
        self.backup_dir = self.storage_dir / "backups"
        self.backup_dir.mkdir(exist_ok=True)
        
        # 默认配置
        self.default_config = {
            "scheduler_config": {
                "enabled": True,
                "timezone": "Asia/Shanghai",
                "check_interval": 1,
                "max_concurrent_tasks": 3,
                "task_timeout": 3600,
                "auto_cleanup_completed": True,
                "cleanup_after_days": 30,
                "auto_backup": True,
                "backup_interval_hours": 24,
                "max_backup_files": 10
            },
            "version": "2.0.0",
            "created_at": TimeUtils.get_beijing_now().isoformat(),
            "updated_at": TimeUtils.get_beijing_now().isoformat()
        }
        
        # 初始化配置文件
        self._init_config_file()
    
    def _init_config_file(self):
        """初始化配置文件"""
        if not self.config_file.exists():
            self.save_config(self.default_config)
    
    def save_config(self, config: Dict[str, Any]) -> bool:
        """
        保存配置
        
        Args:
            config: 配置字典
            
        Returns:
            是否保存成功
        """
        try:
            config["updated_at"] = TimeUtils.get_beijing_now().isoformat()
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            return True
        except Exception as e:
            print(f"保存配置失败: {e}")
            return False
    
    def load_config(self) -> Dict[str, Any]:
        """
        加载配置
        
        Returns:
            配置字典
        """
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 合并默认配置（处理新增的配置项）
                merged_config = self.default_config.copy()
                merged_config.update(config)
                
                return merged_config
            else:
                return self.default_config.copy()
        except Exception as e:
            print(f"加载配置失败: {e}")
            return self.default_config.copy()
    
    def save_tasks(self, tasks: List[ScheduledTask]) -> bool:
        """
        保存任务列表
        
        Args:
            tasks: 任务列表
            
        Returns:
            是否保存成功
        """
        try:
            # 创建备份
            if self.tasks_file.exists():
                self._create_backup()
            
            # 转换任务为字典格式
            tasks_data = {
                "tasks": [task.to_dict() for task in tasks],
                "version": "2.0.0",
                "saved_at": TimeUtils.get_beijing_now().isoformat(),
                "task_count": len(tasks)
            }
            
            # 保存到文件
            with open(self.tasks_file, 'w', encoding='utf-8') as f:
                json.dump(tasks_data, f, ensure_ascii=False, indent=2)
            
            return True
        except Exception as e:
            print(f"保存任务失败: {e}")
            return False
    
    def load_tasks(self) -> List[ScheduledTask]:
        """
        加载任务列表
        
        Returns:
            任务列表
        """
        try:
            if not self.tasks_file.exists():
                return []
            
            with open(self.tasks_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            tasks = []
            for task_data in data.get("tasks", []):
                try:
                    task = ScheduledTask.from_dict(task_data)
                    tasks.append(task)
                except Exception as e:
                    print(f"加载任务失败 {task_data.get('id', 'unknown')}: {e}")
                    continue
            
            return tasks
        except Exception as e:
            print(f"加载任务列表失败: {e}")
            return []
    
    def save_task(self, task: ScheduledTask) -> bool:
        """
        保存单个任务
        
        Args:
            task: 要保存的任务
            
        Returns:
            是否保存成功
        """
        tasks = self.load_tasks()
        
        # 查找并更新现有任务，或添加新任务
        updated = False
        for i, existing_task in enumerate(tasks):
            if existing_task.id == task.id:
                tasks[i] = task
                updated = True
                break
        
        if not updated:
            tasks.append(task)
        
        return self.save_tasks(tasks)
    
    def delete_task(self, task_id: str) -> bool:
        """
        删除任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否删除成功
        """
        tasks = self.load_tasks()
        original_count = len(tasks)
        
        tasks = [task for task in tasks if task.id != task_id]
        
        if len(tasks) < original_count:
            return self.save_tasks(tasks)
        
        return False  # 没有找到要删除的任务
    
    def get_task(self, task_id: str) -> Optional[ScheduledTask]:
        """
        获取指定任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务对象，如果不存在返回None
        """
        tasks = self.load_tasks()
        for task in tasks:
            if task.id == task_id:
                return task
        return None
    
    def _create_backup(self) -> bool:
        """
        创建备份文件
        
        Returns:
            是否创建成功
        """
        try:
            if not self.tasks_file.exists():
                return True
            
            # 生成备份文件名
            timestamp = TimeUtils.get_beijing_now().strftime("%Y%m%d_%H%M%S")
            backup_file = self.backup_dir / f"tasks_backup_{timestamp}.json"
            
            # 复制文件
            shutil.copy2(self.tasks_file, backup_file)
            
            # 清理旧备份文件
            self._cleanup_old_backups()
            
            return True
        except Exception as e:
            print(f"创建备份失败: {e}")
            return False
    
    def _cleanup_old_backups(self):
        """清理旧的备份文件"""
        try:
            config = self.load_config()
            max_backups = config.get("scheduler_config", {}).get("max_backup_files", 10)
            
            # 获取所有备份文件
            backup_files = list(self.backup_dir.glob("tasks_backup_*.json"))
            
            # 按修改时间排序
            backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            # 删除多余的备份文件
            for backup_file in backup_files[max_backups:]:
                try:
                    backup_file.unlink()
                except Exception as e:
                    print(f"删除备份文件失败 {backup_file}: {e}")
        except Exception as e:
            print(f"清理备份文件失败: {e}")
    
    def restore_from_backup(self, backup_filename: str = None) -> bool:
        """
        从备份恢复任务
        
        Args:
            backup_filename: 备份文件名，如果为None则使用最新的备份
            
        Returns:
            是否恢复成功
        """
        try:
            if backup_filename:
                backup_file = self.backup_dir / backup_filename
            else:
                # 使用最新的备份文件
                backup_files = list(self.backup_dir.glob("tasks_backup_*.json"))
                if not backup_files:
                    print("没有找到备份文件")
                    return False
                
                backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
                backup_file = backup_files[0]
            
            if not backup_file.exists():
                print(f"备份文件不存在: {backup_file}")
                return False
            
            # 复制备份文件到当前任务文件
            shutil.copy2(backup_file, self.tasks_file)
            
            print(f"从备份恢复成功: {backup_file.name}")
            return True
        except Exception as e:
            print(f"从备份恢复失败: {e}")
            return False
    
    def get_backup_list(self) -> List[Dict[str, Any]]:
        """
        获取备份文件列表
        
        Returns:
            备份文件信息列表
        """
        try:
            backup_files = list(self.backup_dir.glob("tasks_backup_*.json"))
            backup_info = []
            
            for backup_file in backup_files:
                stat = backup_file.stat()
                backup_info.append({
                    "filename": backup_file.name,
                    "size": stat.st_size,
                    "created_at": datetime.datetime.fromtimestamp(stat.st_mtime),
                    "path": str(backup_file)
                })
            
            # 按创建时间排序
            backup_info.sort(key=lambda x: x["created_at"], reverse=True)
            
            return backup_info
        except Exception as e:
            print(f"获取备份列表失败: {e}")
            return []
    
    def cleanup_completed_tasks(self, days: int = None) -> int:
        """
        清理已完成的任务
        
        Args:
            days: 清理多少天前的任务，默认使用配置中的值
            
        Returns:
            清理的任务数量
        """
        try:
            if days is None:
                config = self.load_config()
                days = config.get("scheduler_config", {}).get("cleanup_after_days", 30)
            
            tasks = self.load_tasks()
            cutoff_date = TimeUtils.get_beijing_now() - datetime.timedelta(days=days)
            
            # 筛选要保留的任务
            remaining_tasks = []
            cleaned_count = 0
            
            for task in tasks:
                # 保留未完成的任务和最近完成的任务
                if (task.status not in [TaskStatus.COMPLETED, TaskStatus.FAILED] or
                    task.updated_at > cutoff_date or
                    task.enabled):  # 保留启用的任务
                    remaining_tasks.append(task)
                else:
                    cleaned_count += 1
            
            if cleaned_count > 0:
                self.save_tasks(remaining_tasks)
                print(f"清理了 {cleaned_count} 个已完成的任务")
            
            return cleaned_count
        except Exception as e:
            print(f"清理已完成任务失败: {e}")
            return 0
    
    def export_tasks(self, export_file: str) -> bool:
        """
        导出任务到文件
        
        Args:
            export_file: 导出文件路径
            
        Returns:
            是否导出成功
        """
        try:
            tasks = self.load_tasks()
            export_data = {
                "tasks": [task.to_dict() for task in tasks],
                "exported_at": TimeUtils.get_beijing_now().isoformat(),
                "version": "2.0.0",
                "task_count": len(tasks)
            }
            
            with open(export_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            return True
        except Exception as e:
            print(f"导出任务失败: {e}")
            return False
    
    def import_tasks(self, import_file: str, merge: bool = True) -> int:
        """
        从文件导入任务
        
        Args:
            import_file: 导入文件路径
            merge: 是否与现有任务合并，False则替换所有任务
            
        Returns:
            导入的任务数量
        """
        try:
            with open(import_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            imported_tasks = []
            for task_data in data.get("tasks", []):
                try:
                    task = ScheduledTask.from_dict(task_data)
                    imported_tasks.append(task)
                except Exception as e:
                    print(f"导入任务失败 {task_data.get('id', 'unknown')}: {e}")
                    continue
            
            if merge:
                # 合并模式：与现有任务合并
                existing_tasks = self.load_tasks()
                existing_ids = {task.id for task in existing_tasks}
                
                # 只添加不存在的任务
                new_tasks = [task for task in imported_tasks if task.id not in existing_ids]
                all_tasks = existing_tasks + new_tasks
            else:
                # 替换模式：替换所有任务
                all_tasks = imported_tasks
            
            self.save_tasks(all_tasks)
            return len(imported_tasks)
        except Exception as e:
            print(f"导入任务失败: {e}")
            return 0
