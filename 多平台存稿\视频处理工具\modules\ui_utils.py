"""
UI工具模块 - 包含工具函数和辅助方法
"""

import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from .utils import center_window


class ToolTip:
    """工具提示类"""

    def __init__(self, widget, text):
        self.widget = widget
        self.text = text
        self.tooltip = None
        self.widget.bind("<Enter>", self.show_tooltip)
        self.widget.bind("<Leave>", self.hide_tooltip)

    def show_tooltip(self, event=None):
        """显示工具提示"""
        x, y, _, _ = self.widget.bbox("insert")
        x += self.widget.winfo_rootx() + 25
        y += self.widget.winfo_rooty() + 25

        # 创建工具提示窗口
        self.tooltip = tk.Toplevel(self.widget)
        self.tooltip.wm_overrideredirect(True)

        # 创建工具提示标签
        label = ttk.Label(self.tooltip, text=self.text, justify=tk.LEFT,
                         background="#ffffe0", relief=tk.SOLID, borderwidth=1,
                         font=("TkDefaultFont", 9))
        label.pack(padx=3, pady=3)

        # 更新窗口大小
        self.tooltip.update_idletasks()

        # 设置位置
        self.tooltip.wm_geometry(f"+{x}+{y}")

    def hide_tooltip(self, event=None):
        """隐藏工具提示"""
        if self.tooltip:
            self.tooltip.destroy()
            self.tooltip = None


def create_tooltip(self, widget, text):
    """为窗口部件创建工具提示"""
    ToolTip(widget, text)


def log(self, message):
    """添加日志消息"""
    # 检查log_text是否存在
    if not hasattr(self, 'log_text') or self.log_text is None:
        print(message)
        return

    try:
        self.log_text.config(state=tk.NORMAL)

        # 根据消息类型添加不同颜色
        if message.startswith("✅"):
            self.log_text.insert(tk.END, message + "\n", "success")
        elif message.startswith("❌") or message.startswith("⚠️"):
            self.log_text.insert(tk.END, message + "\n", "error")
        else:
            self.log_text.insert(tk.END, message + "\n")

        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.root.update_idletasks()
    except Exception as e:
        print(f"日志记录失败: {message} - {str(e)}")
        print(message)


def clear_log(self):
    """清除日志"""
    # 检查log_text是否存在
    if not hasattr(self, 'log_text') or self.log_text is None:
        print("日志已清除")
        return

    try:
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.log("日志已清除")
    except Exception as e:
        print(f"清除日志失败: {str(e)}")
        print("日志已清除")


def update_output_dirs(self, *args):
    """根据视频源目录更新输出目录"""
    video_dir = self.video_dir.get()
    if video_dir:
        # 获取父目录
        parent_dir = os.path.dirname(video_dir)

        # 设置处理后视频目录
        processed_videos_dir = os.path.join(parent_dir, "已处理视频")
        self.processed_videos_dir.set(processed_videos_dir)

        # 设置处理后封面目录
        processed_covers_dir = os.path.join(parent_dir, "已处理封面")
        self.processed_covers_dir.set(processed_covers_dir)


def select_video_dir(self):
    """选择视频源目录"""
    directory = filedialog.askdirectory(title="选择视频源目录")
    if directory:
        # 设置新目录
        self.video_dir.set(directory)

        # 自动设置输出目录 - update_output_dirs回调会被触发
        # 但在这里也做一些额外的检查

        # 仅当路径不存在时才额外提示
        if not os.path.exists(self.processed_videos_dir.get()):
            self.log(f"注意: 处理后视频目录 '{self.processed_videos_dir.get()}' 不存在，将在处理时自动创建")

        if not os.path.exists(self.processed_covers_dir.get()):
            self.log(f"注意: 处理后封面目录 '{self.processed_covers_dir.get()}' 不存在，将在处理时自动创建")

        # 立即保存配置，确保路径设置被保存
        self.config_manager.save_config()

        # 记录日志
        self.log(f"已选择视频源目录: {directory}")


def select_directory(self, var, title):
    """通用目录选择方法"""
    directory = filedialog.askdirectory(title=title)
    if directory:
        var.set(directory)
        self.log(f"已选择目录: {directory}")


def bind_keyboard_shortcuts(self):
    """绑定键盘快捷键 - 已禁用"""
    # 键盘快捷键已禁用，此函数不再绑定任何快捷键
    pass


def _update_color_preview(self, canvas, color_hex):
    """更新颜色预览"""
    try:
        # 使用新的update_color_preview函数
        from .ui.styles.widgets import update_color_preview
        update_color_preview(canvas, color_hex, self)
    except Exception as e:
        print(f"更新颜色预览失败: {str(e)}")
        try:
            # 回退到旧方法
            canvas.delete("all")
            canvas.configure(bg=color_hex)
            if hasattr(canvas, 'color_name_label') and hasattr(self, 'color_names'):
                color_name = self.color_names.get(color_hex, "自定义颜色")
                canvas.color_name_label.config(text=color_name)
        except Exception as e2:
            print(f"回退更新颜色预览也失败: {str(e2)}")


def _update_font_size_preview(self):
    """更新字体大小预览 - 现代化设计"""
    try:
        if hasattr(self, 'font_preview_canvas'):
            # 获取当前字体大小
            font_size = self.cover_size.get()

            # 清除画布
            self.font_preview_canvas.delete("all")

            # 设置背景色 - 使用当前主题
            theme_key = "dark" if self.enable_dark_mode.get() else "light"
            theme = self.theme_colors[theme_key]
            self.font_preview_canvas.configure(bg=theme["canvas_bg"])

            # 创建示例文本
            font_family = self.configure_fonts()  # 获取当前字体族
            preview_font = (font_family, font_size)

            # 在画布中心绘制文本
            canvas_width = self.font_preview_canvas.winfo_width()
            canvas_height = self.font_preview_canvas.winfo_height()

            # 如果画布尚未渲染，使用默认尺寸
            if canvas_width <= 1:
                canvas_width = 180
            if canvas_height <= 1:
                canvas_height = 60

            # 绘制示例文本 - 使用当前主题的文字颜色
            text_color = theme["fg"]
            self.font_preview_canvas.create_text(
                canvas_width // 2, canvas_height // 2,
                text="Aa文字",
                font=preview_font,
                fill=text_color
            )
    except Exception as e:
        print(f"更新字体大小预览失败: {str(e)}")


def _update_all_previews(self):
    """更新所有预览元素"""
    try:
        # 更新颜色预览
        if hasattr(self, 'color_preview_top'):
            self._update_color_preview(self.color_preview_top, self.cover_color_top.get())
        if hasattr(self, 'color_preview_bottom'):
            self._update_color_preview(self.color_preview_bottom, self.cover_color_bottom.get())

        # 更新字体大小预览
        if hasattr(self, 'font_preview_canvas'):
            self._update_font_size_preview()

    except Exception as e:
        print(f"更新预览元素失败: {str(e)}")


def _on_color_change(self, canvas, color_hex):
    """颜色变化时的处理函数"""
    # 更新颜色预览
    self._update_color_preview(canvas, color_hex)

    # 保存配置
    self.config_manager.save_config(show_message=False)

    # 记录日志
    color_name = self.color_names.get(color_hex, "未知颜色")
    self.log(f"颜色已更改为: {color_name} ({color_hex})")


def _update_auto_save_interval(self):
    """更新自动保存间隔"""
    try:
        # 获取新的自动保存间隔（秒）
        new_interval = self.auto_save_interval_var.get()

        # 转换为毫秒
        new_interval_ms = new_interval * 1000

        # 更新自动保存间隔
        self.auto_save_interval = new_interval_ms

        # 记录日志
        self.log(f"✅ 自动保存间隔已更新为 {new_interval} 秒")

        # 保存配置
        self.config_manager.save_config(show_message=False)
    except Exception as e:
        self.log(f"❌ 更新自动保存间隔失败: {str(e)}")

def set_resolution(self, width, height):
    """设置封面分辨率"""
    try:
        # 启用自定义分辨率
        self.enable_custom_cover_resolution.set(True)

        # 设置宽度和高度
        self.cover_width.set(width)
        self.cover_height.set(height)

        # 记录日志
        self.log(f"✅ 封面分辨率已设置为 {width}x{height}")

        # 保存配置
        self.config_manager.save_config(show_message=False)
    except Exception as e:
        self.log(f"❌ 设置封面分辨率失败: {str(e)}")

def _export_config(self):
    """导出配置到文件"""
    try:
        # 打开文件对话框
        file_path = filedialog.asksaveasfilename(
            defaultextension=".conf",
            filetypes=[("配置文件", "*.conf"), ("所有文件", "*.*")],
            title="导出配置"
        )

        if not file_path:
            return  # 用户取消了操作

        # 保存当前配置
        self.config_manager.save_config(show_message=False)

        # 复制配置文件
        import shutil
        shutil.copy2(self.config_manager.config_file, file_path)

        self.log(f"✅ 配置已导出到: {file_path}")
    except Exception as e:
        self.log(f"❌ 导出配置失败: {str(e)}")

def _import_config(self):
    """从文件导入配置"""
    try:
        # 打开文件对话框
        file_path = filedialog.askopenfilename(
            filetypes=[("配置文件", "*.conf"), ("所有文件", "*.*")],
            title="导入配置"
        )

        if not file_path:
            return  # 用户取消了操作

        # 复制配置文件
        import shutil
        shutil.copy2(file_path, self.config_manager.config_file)

        # 重新加载配置
        self.config_manager.load_config()

        # 应用设置
        self.toggle_dark_mode()  # 应用主题
        self.configure_fonts()   # 应用字体

        self.log(f"✅ 配置已从 {file_path} 导入")
    except Exception as e:
        self.log(f"❌ 导入配置失败: {str(e)}")

def on_close(self):
    """窗口关闭时的处理"""
    # 直接保存配置并关闭窗口，不显示确认对话框
    self.config_manager.save_config(show_message=False)  # 保存配置但不显示消息
    self.root.destroy()  # 关闭窗口
