"""
平台选择面板模块 - 负责显示和管理多平台切换
"""

import os
import tkinter as tk
from tkinter import ttk
from typing import Callable, Dict, List, Any, Optional

class PlatformPanel:
    """平台选择面板类，负责显示和管理多平台切换"""
    
    def __init__(self, parent: ttk.Frame, config_manager, log_callback: Callable = None, on_platform_change: Callable = None):
        """
        初始化平台选择面板
        
        Args:
            parent: 父容器
            config_manager: 配置管理器
            log_callback: 日志回调函数
            on_platform_change: 平台切换回调函数
        """
        self.parent = parent
        self.config_manager = config_manager
        self.log_callback = log_callback
        self.on_platform_change = on_platform_change
        
        # 初始化变量
        self.current_platform = tk.StringVar(value=self.config_manager.get_current_platform())
        self.platform_buttons = {}
        
        # 创建UI
        self.create_ui()
        
    def log(self, message: str) -> None:
        """
        记录日志
        
        Args:
            message: 日志消息
        """
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)
    
    def create_ui(self) -> None:
        """创建用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.parent)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建标题
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(10, 5))
        
        title_label = ttk.Label(title_frame, text="平台选择", font=("微软雅黑", 12, "bold"))
        title_label.pack(side=tk.LEFT, padx=10)
        
        # 创建平台按钮容器
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 获取可用平台列表
        available_platforms = self.config_manager.get("available_platforms", ["netease"])
        platform_names = self.config_manager.get("platform_names", {})
        
        # 创建平台按钮
        for i, platform in enumerate(available_platforms):
            platform_name = platform_names.get(platform, platform)
            
            # 创建按钮框架
            button_frame = ttk.Frame(buttons_frame)
            button_frame.pack(fill=tk.X, pady=5)
            
            # 创建平台按钮
            button = ttk.Button(
                button_frame, 
                text=platform_name,
                width=20,
                command=lambda p=platform: self.switch_platform(p)
            )
            button.pack(side=tk.LEFT, padx=10, pady=5, fill=tk.X, expand=True)
            
            # 保存按钮引用
            self.platform_buttons[platform] = button
        
        # 更新按钮状态
        self.update_button_states()
    
    def update_button_states(self) -> None:
        """更新按钮状态"""
        current_platform = self.config_manager.get_current_platform()
        
        # 更新所有按钮状态
        for platform, button in self.platform_buttons.items():
            if platform == current_platform:
                button.state(["disabled"])  # 禁用当前平台按钮
                button.configure(text=f"✓ {self.config_manager.get_platform_name(platform)}")
            else:
                button.state(["!disabled"])  # 启用其他平台按钮
                button.configure(text=self.config_manager.get_platform_name(platform))
    
    def switch_platform(self, platform: str) -> None:
        """
        切换平台
        
        Args:
            platform: 平台标识
        """
        # 检查是否已经是当前平台
        current_platform = self.config_manager.get_current_platform()
        if platform == current_platform:
            return
        
        # 设置新平台
        success = self.config_manager.set_current_platform(platform, force_save=True)
        
        if success:
            # 更新按钮状态
            self.update_button_states()
            
            # 调用平台切换回调
            if self.on_platform_change:
                self.on_platform_change(platform)
        else:
            self.log(f"切换到平台 {platform} 失败")
