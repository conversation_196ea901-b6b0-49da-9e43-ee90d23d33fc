"""
视频处理模块 - 处理视频的核心功能
"""

import os
import re
import uuid
import json
import shutil
import subprocess
from typing import Dict, List, Tuple, Optional, Any, Union, Callable
from PIL import Image

from ..cover import CoverProcessor
from ..common.utils import clean_filename

class VideoProcessor:
    """视频处理类，负责视频的处理和转换"""

    def __init__(self, logger: Callable = print):
        """
        初始化视频处理器

        Args:
            logger: 日志记录函数
        """
        self.logger = logger
        self.cover_processor = CoverProcessor(logger)

    def process_video(self,
                     video_file: str,
                     video_dir: str,
                     processed_videos_dir: str,
                     processed_covers_dir: str,
                     min_duration: float,
                     max_duration: float,
                     target_ratio: str,
                     enable_deduplication: bool = True,
                     cover_settings: Dict[str, Any] = None,
                     watermark_settings: Dict[str, Any] = None,
                     output_format: str = None,
                     cover_resolution: Tuple[int, int] = None,
                     gpu_settings: Dict[str, Any] = None,
                     resource_settings: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        处理单个视频文件

        Args:
            video_file: 视频文件名
            video_dir: 视频源目录
            processed_videos_dir: 处理后视频目录
            processed_covers_dir: 处理后封面目录
            min_duration: 最小视频时长（秒）
            max_duration: 最大视频时长（秒）
            target_ratio: 目标视频比例（如"16:9"）
            enable_deduplication: 是否启用视频去重
            cover_settings: 封面设置
            watermark_settings: 水印设置
            output_format: 输出视频格式（如"mp4"、"mov"等），None表示保持原格式
            cover_resolution: 封面分辨率，如(1280, 720)
            gpu_settings: GPU加速设置，包含enable和device等参数
            resource_settings: 智能资源分配设置，包含enable和memory_limit等参数

        Returns:
            Dict: 处理结果，包含success、message等信息
        """
        try:
            # 视频文件完整路径
            video_path = os.path.join(video_dir, video_file)

            # 检查视频是否存在
            if not os.path.exists(video_path):
                return {
                    'success': False,
                    'message': f"视频文件不存在: {video_path}"
                }

            # 检查GPU加速设置
            use_gpu = False
            gpu_device = "auto"
            if gpu_settings and gpu_settings.get('enable', False):
                use_gpu = True
                gpu_device = gpu_settings.get('device', 'auto')
                self.logger(f"GPU加速已启用，设备: {gpu_device}")

            # 检查资源限制设置
            memory_limit = 0  # 0表示不限制
            if resource_settings and resource_settings.get('enable', False):
                memory_limit = resource_settings.get('memory_limit', 0)
                if memory_limit > 0:
                    self.logger(f"内存限制已设置: {memory_limit}MB")

            # 处理文件名（保持原始文件名）
            file_name = os.path.splitext(video_file)[0]  # 文件名（不含扩展名）
            file_ext = os.path.splitext(video_file)[1]   # 扩展名

            # 如果指定了输出格式，更改扩展名
            if output_format:
                if not output_format.startswith('.'):
                    output_format = '.' + output_format
                file_ext = output_format

            # 清理文件名
            file_name = clean_filename(file_name)

            # 如果开启了视频去重，生成唯一文件名
            if enable_deduplication:
                # 使用UUID确保唯一性，但不直接显示在文件名中
                short_uuid = str(uuid.uuid4())[:8]
                base_name = file_name[:30] if len(file_name) > 30 else file_name

                # 隐藏UUID到文件名末尾隐藏属性中，不改变可见文件名
                processed_name = base_name

                # 仅当检测到重名时才添加UUID
                test_path = os.path.join(processed_videos_dir, f"{processed_name}{file_ext}")
                if os.path.exists(test_path):
                    processed_name = f"{base_name}_{short_uuid}"

                truncated_name = f"{processed_name}{file_ext}"
            else:
                # 如果不开启去重，仍然确保文件名不会太长
                if len(file_name) > 30:
                    truncated_name = f"{file_name[:30]}{file_ext}"
                else:
                    truncated_name = f"{file_name}{file_ext}"

            # 设置输出路径
            output_path = os.path.join(processed_videos_dir, truncated_name)
            cover_name = f"{os.path.splitext(truncated_name)[0]}.jpg"
            cover_path = os.path.join(processed_covers_dir, cover_name)

            # 检查输出文件是否已存在 (在非去重模式下可能发生)
            if os.path.exists(output_path):
                # 生成新的文件名，但保持干净的命名方式
                base_name = os.path.splitext(truncated_name)[0]
                count = 1

                # 尝试添加序号直到找到可用的文件名
                while os.path.exists(output_path):
                    new_name = f"{base_name}_{count}"
                    truncated_name = f"{new_name}{file_ext}"
                    output_path = os.path.join(processed_videos_dir, truncated_name)
                    count += 1

                # 更新封面路径
                cover_name = f"{os.path.splitext(truncated_name)[0]}.jpg"
                cover_path = os.path.join(processed_covers_dir, cover_name)

            # 分析视频
            from .analyzer import analyze_video
            video_info = analyze_video(self, video_path, use_gpu, gpu_device, memory_limit)
            
            if not video_info['success']:
                return video_info
            
            # 检查视频时长
            duration_seconds = video_info['duration']
            width = video_info['width']
            height = video_info['height']
            
            if duration_seconds < min_duration:
                return {
                    'success': False,
                    'message': f"视频 {video_file} 时长过短: {duration_seconds:.2f}秒 < {min_duration}秒"
                }

            if duration_seconds > max_duration:
                return {
                    'success': False,
                    'message': f"视频 {video_file} 时长过长: {duration_seconds:.2f}秒 > {max_duration}秒"
                }

            # 检查视频比例
            aspect_ratio = width / height

            if target_ratio == "16:9":
                target_aspect = 16 / 9
            elif target_ratio == "4:3":
                target_aspect = 4 / 3
            elif target_ratio == "1:1":
                target_aspect = 1
            elif target_ratio == "9:16":
                target_aspect = 9 / 16
            else:
                target_aspect = 16 / 9  # 默认16:9

            # 容差为0.1
            if abs(aspect_ratio - target_aspect) > 0.1:
                return {
                    'success': False,
                    'message': f"视频 {video_file} 比例不符合要求: {aspect_ratio:.2f}，期望: {target_aspect:.2f}"
                }

            # 提取封面
            from .extractor import extract_cover
            cover_result = extract_cover(
                self, 
                video_path, 
                cover_path, 
                duration_seconds,
                use_gpu, 
                gpu_device, 
                memory_limit,
                cover_resolution
            )
            
            if not cover_result['success']:
                return cover_result

            # 处理封面文字
            if cover_settings:
                self.cover_processor.add_text_to_cover(
                    cover_path,
                    os.path.splitext(video_file)[0],
                    top_text=cover_settings.get('top_text', ''),
                    bottom_text=cover_settings.get('bottom_text', ''),
                    top_color=cover_settings.get('top_color', '#FFFFFF'),
                    bottom_color=cover_settings.get('bottom_color', '#FFFFFF'),
                    font_size=cover_settings.get('font_size', 60),
                    auto_use_filename=cover_settings.get('auto_use_filename', True),
                    cover_resolution=cover_resolution
                )

            # 添加封面水印
            if watermark_settings and watermark_settings.get('enable', False):
                self.cover_processor.add_watermark_to_cover(
                    cover_path,
                    watermark_color=watermark_settings.get('color', '#FFFFFF'),
                    opacity=watermark_settings.get('opacity', 0.1),
                    position=watermark_settings.get('position', '全屏')
                )

            # 处理视频文件 - 如果需要转换格式
            if output_format and os.path.splitext(video_path)[1].lower() != output_format.lower():
                from .converter import convert_video
                convert_result = convert_video(
                    self,
                    video_path,
                    output_path,
                    use_gpu,
                    gpu_device,
                    memory_limit
                )
                
                if not convert_result['success']:
                    # 如果转换失败，回退到直接复制
                    self.logger(f"格式转换失败，直接复制文件: {convert_result['message']}")
                    shutil.copy2(video_path, output_path)
            else:
                # 直接复制视频文件
                shutil.copy2(video_path, output_path)

            return {
                'success': True,
                'message': f"✅ 视频 {video_file} 处理成功，已保存为: {truncated_name}",
                'file': video_file
            }

        except Exception as e:
            return {
                'success': False,
                'message': f"处理视频 {video_file} 失败: {str(e)}"
            }
