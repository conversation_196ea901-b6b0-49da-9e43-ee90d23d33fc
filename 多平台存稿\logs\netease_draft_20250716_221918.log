[2025-07-16 22:19:20] [INFO] 🚀 启动多平台存稿工具...
[2025-07-16 22:19:20] [ERROR] 程序发生未捕获的异常: expected an indented block after 'if' statement on line 218 (data_query_manager.py, line 220)
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\main.py", line 77, in main
    app = NeteaseDraftUI(root, config_manager)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\ui\main.py", line 156, in __init__
    self._init_managers()
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\ui\main.py", line 231, in _init_managers
    from 网易号存稿.ui.managers.data_query_manager import DataQueryManager
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\ui\managers\data_query_manager.py", line 220
    update_success = self.account_data.update_account_data(account, result)
    ^^^^^^^^^^^^^^
IndentationError: expected an indented block after 'if' statement on line 218

