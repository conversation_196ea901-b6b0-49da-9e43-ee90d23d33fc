"""
UI设置模块 - 包含设置对话框相关代码
"""

import tkinter as tk
from tkinter import ttk, scrolledtext

from .icons import get_icon
from .utils import center_window  # 确保正确导入center_window函数



def open_settings_dialog(self, tab_index=0):
    """打开设置对话框"""
    # 创建设置对话框
    settings_dialog = tk.Toplevel(self.root)
    settings_dialog.title("高级设置")
    settings_dialog.geometry("1200x1200")  # 按要求再增加对话框高度100像素至1200
    settings_dialog.minsize(1000, 1050)    # 相应增加最小高度
    settings_dialog.resizable(True, True) # 允许调整大小
    settings_dialog.transient(self.root)  # 设置为主窗口的子窗口
    settings_dialog.grab_set()  # 模态对话框

    # 应用当前主题 - 使用更现代的主题样式
    theme_key = "dark" if self.enable_dark_mode.get() else "light"
    theme = self.theme_colors[theme_key]

    # 设置对话框背景色
    settings_dialog.configure(bg=theme["bg"])

    # 创建和配置样式
    style = ttk.Style()

    # 配置标签框架样式 - 添加圆角和阴影效果
    style.configure("Card.TLabelframe", borderwidth=1, relief="solid", padding=10)
    style.configure("Card.TLabelframe.Label", font=("微软雅黑", self.default_font_size, "bold"))

    # 配置按钮样式 - 更现代的外观
    style.configure("Modern.TButton", padding=8, font=("微软雅黑", self.default_font_size))

    # 配置标签样式 - 更清晰的文本
    style.configure("Modern.TLabel", font=("微软雅黑", self.default_font_size))

    # 配置复选框样式
    style.configure("Modern.TCheckbutton", font=("微软雅黑", self.default_font_size))

    # 配置输入框样式
    style.configure("Modern.TEntry", padding=5)

    # 配置下拉框样式
    style.configure("Modern.TCombobox", padding=5)

    # 创建主内容框架和按钮框架的容器
    main_container = ttk.Frame(settings_dialog)
    main_container.pack(fill=tk.BOTH, expand=True)

    # 创建搜索框 - 使用更现代的卡片式设计
    search_card = ttk.LabelFrame(main_container, text="搜索设置", style="Card.TLabelframe")
    search_card.pack(fill=tk.X, padx=15, pady=10)

    # 创建内部搜索框架
    search_frame = ttk.Frame(search_card)
    search_frame.pack(fill=tk.X, padx=10, pady=10)

    # 创建搜索图标标签
    search_icon_label = ttk.Label(search_frame, text="🔍", font=("微软雅黑", self.default_font_size + 2))
    search_icon_label.pack(side=tk.LEFT, padx=(0, 5))

    # 创建搜索输入框
    self.settings_search_var = tk.StringVar()
    search_entry = ttk.Entry(search_frame, textvariable=self.settings_search_var,
                           width=40, style="Modern.TEntry")
    search_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

    # 添加搜索按钮 - 使用更现代的样式
    search_button = ttk.Button(search_frame, text="搜索", style="Modern.TButton",
                             command=lambda: self._search_settings(notebook))
    search_button.pack(side=tk.LEFT, padx=10)

    # 添加搜索提示
    search_hint = ttk.Label(search_frame, text="(输入关键词搜索相关设置，如'视频'、'格式'等)",
                          style="Modern.TLabel", foreground="#888888")
    search_hint.pack(side=tk.LEFT, padx=5)

    # 创建选项卡容器 - 使用卡片式设计
    tab_card = ttk.LabelFrame(main_container, text="设置选项", style="Card.TLabelframe")
    tab_card.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

    # 创建选项卡 - 放在卡片容器中
    notebook = ttk.Notebook(tab_card)
    notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=10)

    # 创建各个选项卡 - 使用统一的样式
    interface_tab = ttk.Frame(notebook)
    video_tab = ttk.Frame(notebook)
    performance_tab = ttk.Frame(notebook)
    cover_tab = ttk.Frame(notebook)
    status_tab = ttk.Frame(notebook)

    # 获取图标 - 使用更大的图标尺寸
    icon_size = (24, 24)  # 增大图标尺寸
    interface_icon = get_icon("interface", icon_size)
    video_icon = get_icon("video", icon_size)
    performance_icon = get_icon("performance", icon_size)
    cover_icon = get_icon("cover", icon_size)
    status_icon = get_icon("status", icon_size)

    # 设置选项卡样式 - 更现代的外观
    style.configure("TNotebook", background=theme["bg"])
    style.configure("TNotebook.Tab", padding=[25, 12],
                  font=("微软雅黑", self.default_font_size, "bold"),
                  background=theme["button_bg"], foreground=theme["fg"])

    # 添加选项卡到notebook，带图标 - 使用更明显的文本和间距
    notebook.add(interface_tab, text="  界面设置  ", image=interface_icon, compound=tk.LEFT)
    notebook.add(video_tab, text="  视频设置  ", image=video_icon, compound=tk.LEFT)
    notebook.add(performance_tab, text="  性能设置  ", image=performance_icon, compound=tk.LEFT)
    notebook.add(cover_tab, text="  封面设置  ", image=cover_icon, compound=tk.LEFT)
    notebook.add(status_tab, text="  配置状态  ", image=status_icon, compound=tk.LEFT)

    # 保持图标引用
    notebook.interface_icon = interface_icon
    notebook.video_icon = video_icon
    notebook.performance_icon = performance_icon
    notebook.cover_icon = cover_icon
    notebook.status_icon = status_icon

    # 选择指定的选项卡
    notebook.select(tab_index)

    # 填充界面设置选项卡
    self._create_interface_settings(interface_tab)

    # 填充视频设置选项卡
    self._create_video_settings(video_tab)

    # 填充性能设置选项卡
    self._create_performance_settings(performance_tab)

    # 填充封面设置选项卡
    self._create_cover_settings(cover_tab)

    # 填充配置状态选项卡
    self._create_status_settings(status_tab)

    # 添加分隔线，使底部按钮区域更明显
    separator = ttk.Separator(settings_dialog, orient=tk.HORIZONTAL)
    separator.pack(fill=tk.X, padx=0, pady=0)

    # 创建底部按钮区域 - 使用渐变背景效果
    button_frame = tk.Frame(settings_dialog, relief=tk.RAISED, borderwidth=0)
    button_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=0, pady=0)  # 固定在底部

    # 设置按钮区域背景色 - 使用与主题匹配的颜色
    theme_key = "dark" if self.enable_dark_mode.get() else "light"
    bg_color = self.theme_colors[theme_key]["button_bg"]
    button_frame.configure(bg=bg_color)

    # 添加内部框架，用于放置按钮 - 使用更大的内边距
    button_frame_bg = tk.Frame(button_frame, bg=bg_color)
    button_frame_bg.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)  # 增加内边距

    # 创建按钮样式 - 使用原生外观配色

    # 获取系统默认按钮颜色
    default_button_bg = style.lookup('TButton', 'background') or theme["button_bg"]
    default_button_fg = style.lookup('TButton', 'foreground') or theme["fg"]

    # 创建保存按钮样式 - 使用原生外观
    style.configure("Save.TButton",
                  font=("微软雅黑", 12, "bold"),
                  padding=12,
                  background=default_button_bg,
                  foreground=default_button_fg,
                  relief="raised",
                  borderwidth=2)
    style.map("Save.TButton",
             background=[('active', theme["button_hover"]),
                        ('pressed', theme["button_active"])],
             relief=[('pressed', 'sunken'),
                    ('active', 'raised')])

    # 创建取消按钮样式 - 使用原生外观
    style.configure("Cancel.TButton",
                  font=("微软雅黑", 12, "bold"),
                  padding=12,
                  background=default_button_bg,
                  foreground=default_button_fg,
                  relief="raised",
                  borderwidth=2)
    style.map("Cancel.TButton",
             background=[('active', theme["button_hover"]),
                        ('pressed', theme["button_active"])],
             relief=[('pressed', 'sunken'),
                    ('active', 'raised')])

    # 创建应用按钮样式 - 使用原生外观
    style.configure("Apply.TButton",
                  font=("微软雅黑", 12, "bold"),
                  padding=12,
                  background=default_button_bg,
                  foreground=default_button_fg,
                  relief="raised",
                  borderwidth=2)
    style.map("Apply.TButton",
             background=[('active', theme["button_hover"]),
                        ('pressed', theme["button_active"])],
             relief=[('pressed', 'sunken'),
                    ('active', 'raised')])

    # 创建重置按钮样式 - 使用原生外观
    style.configure("Reset.TButton",
                  font=("微软雅黑", 12, "bold"),
                  padding=12,
                  background=default_button_bg,
                  foreground=default_button_fg,
                  relief="raised",
                  borderwidth=2)
    style.map("Reset.TButton",
             background=[('active', theme["button_hover"]),
                        ('pressed', theme["button_active"])],
             relief=[('pressed', 'sunken'),
                    ('active', 'raised')])

    # 创建按钮容器，确保按钮不被遮挡
    button_container = tk.Frame(button_frame_bg, bg=bg_color)
    button_container.pack(fill=tk.X, expand=True, pady=(0, 10))

    # 左侧按钮区域
    left_button_frame = tk.Frame(button_container, bg=bg_color)
    left_button_frame.pack(side=tk.LEFT, fill=tk.Y)

    # 右侧按钮区域
    right_button_frame = tk.Frame(button_container, bg=bg_color)
    right_button_frame.pack(side=tk.RIGHT, fill=tk.Y)

    # 重置按钮 - 放在左侧
    reset_button = ttk.Button(left_button_frame, text="重置为默认 ↺",
                            command=lambda: [self.config_manager.reset_to_defaults(), settings_dialog.destroy()],
                            width=15, style="Reset.TButton")
    reset_button.pack(side=tk.LEFT, padx=(0, 15))

    # 应用按钮 - 放在右侧
    apply_button = ttk.Button(right_button_frame, text="应用 ↺",
                            command=self.config_manager.save_config,
                            width=15, style="Apply.TButton")
    apply_button.pack(side=tk.RIGHT, padx=(15, 0))

    # 取消按钮 - 放在右侧
    cancel_button = ttk.Button(right_button_frame, text="取消 ✗",
                             command=settings_dialog.destroy,
                             width=15, style="Cancel.TButton")
    cancel_button.pack(side=tk.RIGHT, padx=(15, 0))

    # 保存按钮 - 放在右侧最外侧
    save_button = ttk.Button(right_button_frame, text="保存设置 ✓",
                           command=lambda: [self.config_manager.save_config(), settings_dialog.destroy()],
                           width=15, style="Save.TButton")
    save_button.pack(side=tk.RIGHT, padx=(15, 0))

    # 为所有按钮添加悬停和点击效果
    def add_button_effects(button):
        """为按钮添加悬停和点击效果"""
        def on_enter(event):
            button.configure(cursor="hand2")

        def on_leave(event):
            button.configure(cursor="")

        def on_click(event):
            # ttk.Button不支持relief属性，使用状态变化代替
            try:
                # 尝试设置按钮状态为pressed
                button.state(['pressed'])
                button.after(100, lambda: button.state(['!pressed']))
            except Exception:
                # 如果状态设置失败，忽略错误
                pass

        button.bind("<Enter>", on_enter)
        button.bind("<Leave>", on_leave)
        button.bind("<Button-1>", on_click)

    # 应用效果到所有按钮
    for button in [reset_button, apply_button, cancel_button, save_button]:
        add_button_effects(button)

    # 添加按钮提示 - 放在单独的区域，避免遮挡
    hint_frame = tk.Frame(button_frame_bg, bg=bg_color)
    hint_frame.pack(fill=tk.X, pady=(10, 0))

    button_hint = ttk.Label(hint_frame, text="提示: 保存设置后将立即生效",
                          foreground=theme["fg"], background=bg_color,
                          font=("微软雅黑", 9))
    button_hint.pack(anchor=tk.CENTER)

    # 绑定搜索框回车键
    search_entry.bind("<Return>", lambda event: self._search_settings(notebook))

    # 设置对话框居中 - 使用改进的通用居中函数
    try:
        print("正在居中设置对话框...")

        # 使用改进的通用居中函数
        center_window(settings_dialog)

        # 强制更新
        settings_dialog.update_idletasks()

        # 再次检查窗口位置
        actual_geometry = settings_dialog.geometry()
        print(f"设置对话框最终geometry: {actual_geometry}")
    except Exception as e:
        print(f"居中设置对话框时出错: {str(e)}")

    # 将窗口置顶一次，确保可见性
    settings_dialog.attributes('-topmost', True)
    settings_dialog.after(100, lambda: settings_dialog.attributes('-topmost', False))

def _create_scrollable_frame(self, parent):
    """创建通用的滚动框架（删除滚动条）"""
    # 创建滚动框架
    canvas = tk.Canvas(parent, highlightthickness=0)
    scrollable_frame = ttk.Frame(canvas)

    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )

    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")

    # 添加鼠标滚轮支持
    def _on_mousewheel(event):
        canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    def _bind_to_mousewheel(event):
        canvas.bind_all("<MouseWheel>", _on_mousewheel)

    def _unbind_from_mousewheel(event):
        canvas.unbind_all("<MouseWheel>")

    canvas.bind('<Enter>', _bind_to_mousewheel)
    canvas.bind('<Leave>', _unbind_from_mousewheel)

    canvas.pack(fill="both", expand=True)

    return scrollable_frame

def _create_performance_settings(self, parent):
    """创建性能设置选项卡内容"""
    # 使用通用滚动框架
    scrollable_frame = self._create_scrollable_frame(parent)

    # GPU加速设置 - 使用卡片式设计
    gpu_frame = ttk.LabelFrame(scrollable_frame, text="GPU加速设置", style="Card.TLabelframe")
    gpu_frame.pack(fill=tk.X, padx=10, pady=10, anchor=tk.N)

    # 创建GPU启用选项 - 使用更现代的样式
    gpu_check = ttk.Checkbutton(gpu_frame, text="启用GPU加速", style="Modern.TCheckbutton",
                              variable=self.enable_gpu_acceleration,
                              command=self.check_gpu_availability)
    gpu_check.pack(anchor=tk.W, padx=15, pady=10)

    # 创建GPU设备选择区域
    gpu_device_frame = ttk.Frame(gpu_frame)
    gpu_device_frame.pack(fill=tk.X, padx=15, pady=5)

    # 添加GPU设备标签
    ttk.Label(gpu_device_frame, text="GPU设备:", style="Modern.TLabel").pack(side=tk.LEFT)

    # 创建GPU设备下拉框 - 使用更现代的样式
    # 确保包含所有可能的GPU设备ID
    gpu_values = ["auto"]

    # 获取主界面中的GPU设备列表（包含GPU名称）
    if hasattr(self, 'gpu_device_combo') and len(self.gpu_device_combo['values']) > 0:
        for device in self.gpu_device_combo['values']:
            if device != "auto" and device not in gpu_values:
                gpu_values.append(device)

    # 如果没有检测到GPU，添加所有可能的CUDA设备ID
    if len(gpu_values) <= 1:
        # 检查是否有GPU设备名称信息
        if hasattr(self, 'gpu_device_names') and self.gpu_device_names:
            # 使用已知的GPU设备名称
            for device_id, device_name in self.gpu_device_names.items():
                device_display = f"{device_id} ({device_name})"
                if device_display not in gpu_values:
                    gpu_values.append(device_display)
        else:
            # 添加所有可能的CUDA设备ID（无名称）
            for i in range(8):  # 支持最多8个GPU
                device_id = f"cuda:{i}"
                if device_id not in gpu_values:
                    gpu_values.append(device_id)

    gpu_device_combo = ttk.Combobox(gpu_device_frame, textvariable=self.gpu_device,
                                  values=gpu_values,
                                  state="readonly", width=15, style="Modern.TCombobox")
    gpu_device_combo.pack(side=tk.LEFT, padx=10)

    # 检测GPU按钮 - 使用更现代的样式
    ttk.Button(gpu_device_frame, text="检测GPU", style="Modern.TButton",
              command=self.detect_gpus, width=12).pack(side=tk.LEFT, padx=10)

    # GPU状态标签 - 使用更现代的样式
    gpu_status_label = ttk.Label(gpu_device_frame, text=self.gpu_status_label['text'],
                               style="Modern.TLabel")
    gpu_status_label.pack(side=tk.LEFT, padx=10)

    # 添加GPU说明
    gpu_info = ttk.Label(gpu_frame, text="GPU加速可以显著提高视频处理速度，但需要兼容的NVIDIA显卡。",
                       style="Modern.TLabel", foreground="#888888")
    gpu_info.pack(anchor=tk.W, padx=15, pady=10)

    # 智能资源分配设置 - 使用卡片式设计
    resource_frame = ttk.LabelFrame(scrollable_frame, text="智能资源分配", style="Card.TLabelframe")
    resource_frame.pack(fill=tk.X, padx=10, pady=10)

    # 创建资源分配启用选项
    resource_check = ttk.Checkbutton(resource_frame, text="启用智能资源分配", style="Modern.TCheckbutton",
                                   variable=self.enable_smart_resource,
                                   command=self.toggle_smart_resource)
    resource_check.pack(anchor=tk.W, padx=15, pady=10)

    # 添加资源分配说明
    resource_info = ttk.Label(resource_frame, text="智能资源分配可以根据系统状态自动调整资源使用，提高稳定性。",
                            style="Modern.TLabel", foreground="#888888")
    resource_info.pack(anchor=tk.W, padx=15, pady=(0, 10))

    # 内存限制设置
    memory_frame = ttk.Frame(resource_frame)
    memory_frame.pack(fill=tk.X, padx=15, pady=5)

    ttk.Label(memory_frame, text="内存限制:", style="Modern.TLabel").pack(side=tk.LEFT)

    # 内存限制输入框 - 使用更现代的样式
    memory_spinner = ttk.Spinbox(memory_frame, from_=0, to=32768,
                               textvariable=self.memory_limit, width=8)
    memory_spinner.pack(side=tk.LEFT, padx=10)

    ttk.Label(memory_frame, text="MB (0=自动)", style="Modern.TLabel").pack(side=tk.LEFT)

    # 检测系统资源按钮 - 使用更现代的样式
    ttk.Button(memory_frame, text="检测系统资源", style="Modern.TButton",
              command=self.detect_system_resources, width=15).pack(side=tk.LEFT, padx=15)

    # 线程设置
    thread_frame = ttk.Frame(resource_frame)
    thread_frame.pack(fill=tk.X, padx=15, pady=10)

    ttk.Label(thread_frame, text="处理线程数:", style="Modern.TLabel").pack(side=tk.LEFT)

    # 线程数输入框 - 使用更现代的样式
    thread_spinner = ttk.Spinbox(thread_frame, from_=1, to=32,
                               textvariable=self.thread_num, width=5)
    thread_spinner.pack(side=tk.LEFT, padx=10)

    ttk.Label(thread_frame, text="(建议设置为CPU核心数-1)",
            style="Modern.TLabel", foreground="#888888").pack(side=tk.LEFT, padx=5)




def _search_settings(self, notebook):
    """搜索设置项并跳转到对应选项卡"""
    search_text = self.settings_search_var.get().lower()
    if not search_text:
        return

    # 搜索映射表 - 关键词到选项卡索引的映射
    search_map = {
        # 界面设置关键词
        "界面": 0, "字体": 0, "大小": 0, "黑暗": 0, "暗色": 0, "深色": 0, "主题": 0, "颜色": 0, "自动保存": 0,
        # 视频设置关键词
        "视频": 1, "格式": 1, "转换": 1, "mp4": 1, "mov": 1, "avi": 1, "去重": 1, "时长": 1, "比例": 1,
        # 性能设置关键词
        "性能": 2, "gpu": 2, "加速": 2, "线程": 2, "内存": 2, "资源": 2, "系统": 2, "处理": 2,
        # 封面设置关键词
        "封面": 3, "分辨率": 3, "水印": 3, "文字": 3, "标题": 3, "透明度": 3, "位置": 3, "720p": 3, "1080p": 3
    }

    # 查找匹配的选项卡
    for keyword, tab_index in search_map.items():
        if keyword in search_text:
            notebook.select(tab_index)
            self.log(f"已跳转到匹配的设置选项卡: {notebook.tab(tab_index, 'text')}")
            return


def _create_interface_settings(self, parent):
    """创建界面设置选项卡内容"""
    # 使用通用滚动框架
    scrollable_frame = self._create_scrollable_frame(parent)

    # 主题设置
    theme_frame = ttk.LabelFrame(scrollable_frame, text="主题设置")
    theme_frame.pack(fill=tk.X, padx=10, pady=10)

    # 黑暗模式
    dark_mode_frame = ttk.Frame(theme_frame)
    dark_mode_frame.pack(fill=tk.X, padx=10, pady=5)

    ttk.Checkbutton(dark_mode_frame, text="启用黑暗模式",
                  variable=self.enable_dark_mode,
                  command=self.toggle_dark_mode).pack(side=tk.LEFT)

    ttk.Label(dark_mode_frame, text="(纯黑背景和柔和灰色文字，更护眼)").pack(side=tk.LEFT, padx=5)

    # 字体大小设置
    font_size_frame = ttk.Frame(theme_frame)
    font_size_frame.pack(fill=tk.X, padx=10, pady=5)

    ttk.Label(font_size_frame, text="界面字体大小:").pack(side=tk.LEFT, padx=5)

    font_size_spinner = ttk.Spinbox(font_size_frame, from_=8, to=24,
                                  textvariable=self.font_size_var, width=5)
    font_size_spinner.pack(side=tk.LEFT, padx=5)

    # 应用字体大小按钮
    ttk.Button(font_size_frame, text="应用字体大小",
              command=lambda: [self._update_font_size(), self.configure_fonts()]).pack(side=tk.LEFT, padx=5)

    # 自动保存设置
    auto_save_frame = ttk.LabelFrame(scrollable_frame, text="自动保存设置")
    auto_save_frame.pack(fill=tk.X, padx=10, pady=10)

    auto_save_inner_frame = ttk.Frame(auto_save_frame)
    auto_save_inner_frame.pack(fill=tk.X, padx=10, pady=5)

    ttk.Label(auto_save_inner_frame, text="自动保存间隔:").pack(side=tk.LEFT, padx=5)

    # 确保自动保存间隔变量有正确的值
    if hasattr(self, 'auto_save_interval') and hasattr(self, 'auto_save_interval_var'):
        current_interval_seconds = int(self.auto_save_interval // 1000)
        if self.auto_save_interval_var.get() != current_interval_seconds:
            self.auto_save_interval_var.set(current_interval_seconds)

    auto_save_spinner = ttk.Spinbox(auto_save_inner_frame, from_=10, to=300,
                                  textvariable=self.auto_save_interval_var, width=5)
    auto_save_spinner.pack(side=tk.LEFT, padx=5)

    ttk.Label(auto_save_inner_frame, text="秒").pack(side=tk.LEFT)

    ttk.Button(auto_save_inner_frame, text="应用",
              command=self._update_auto_save_interval).pack(side=tk.LEFT, padx=10)

    ttk.Label(auto_save_inner_frame, text="(建议值: 60秒)").pack(side=tk.LEFT, padx=5)


def _create_video_settings(self, parent):
    """创建视频设置选项卡内容"""
    # 使用通用滚动框架
    scrollable_frame = self._create_scrollable_frame(parent)

    # 视频目录设置
    dir_frame = ttk.LabelFrame(scrollable_frame, text="视频目录设置")
    dir_frame.pack(fill=tk.X, padx=10, pady=10)

    # 获取文件夹图标
    from .icons import get_icon
    folder_icon = get_icon("folder", (16, 16))
    self.folder_icon = folder_icon  # 保存引用

    # 第一行: 视频源目录
    source_frame = ttk.Frame(dir_frame)
    source_frame.pack(fill=tk.X, padx=10, pady=8)

    ttk.Label(source_frame, text="视频源目录:").pack(side=tk.LEFT)
    source_entry = ttk.Entry(source_frame, textvariable=self.video_dir, width=60)
    source_entry.pack(side=tk.LEFT, padx=10)

    source_button = ttk.Button(source_frame, text="浏览", image=folder_icon, compound=tk.LEFT,
                             command=self.select_video_dir, width=8)
    source_button.pack(side=tk.LEFT)

    # 第二行: 处理后视频和封面目录
    output_frame = ttk.Frame(dir_frame)
    output_frame.pack(fill=tk.X, padx=10, pady=8)

    ttk.Label(output_frame, text="处理后视频目录:").pack(side=tk.LEFT)
    videos_entry = ttk.Entry(output_frame, textvariable=self.processed_videos_dir, width=25)
    videos_entry.pack(side=tk.LEFT, padx=10)

    videos_button = ttk.Button(output_frame, text="浏览", image=folder_icon, compound=tk.LEFT,
                             command=lambda: self.select_directory(self.processed_videos_dir, "选择处理后视频目录"), width=6)
    videos_button.pack(side=tk.LEFT, padx=(0, 20))

    ttk.Label(output_frame, text="封面目录:").pack(side=tk.LEFT)
    covers_entry = ttk.Entry(output_frame, textvariable=self.processed_covers_dir, width=25)
    covers_entry.pack(side=tk.LEFT, padx=10)

    covers_button = ttk.Button(output_frame, text="浏览", image=folder_icon, compound=tk.LEFT,
                             command=lambda: self.select_directory(self.processed_covers_dir, "选择处理后封面目录"), width=6)
    covers_button.pack(side=tk.LEFT)

    # 添加目录设置说明
    dir_info = ttk.Label(dir_frame, text="设置视频源目录和处理后的输出目录，处理后的视频和封面将保存到对应目录中。",
                       foreground="#888888")
    dir_info.pack(anchor=tk.W, padx=10, pady=5)

    # 添加工具提示
    if hasattr(self, 'create_tooltip'):
        self.create_tooltip(source_entry, "设置包含待处理视频的源目录")
        self.create_tooltip(source_button, "浏览并选择视频源目录")
        self.create_tooltip(videos_entry, "设置处理后视频的保存目录")
        self.create_tooltip(videos_button, "浏览并选择处理后视频目录")
        self.create_tooltip(covers_entry, "设置生成的封面图像的保存目录")
        self.create_tooltip(covers_button, "浏览并选择处理后封面目录")

    # 视频格式设置
    format_frame = ttk.LabelFrame(scrollable_frame, text="视频格式设置")
    format_frame.pack(fill=tk.X, padx=10, pady=10)

    format_check = ttk.Checkbutton(format_frame, text="启用视频格式转换",
                                 variable=self.enable_format_conversion)
    format_check.pack(anchor=tk.W, padx=10, pady=5)

    format_combo_frame = ttk.Frame(format_frame)
    format_combo_frame.pack(fill=tk.X, padx=10, pady=5)

    ttk.Label(format_combo_frame, text="输出格式:").pack(side=tk.LEFT)

    format_combo = ttk.Combobox(format_combo_frame, textvariable=self.output_format,
                              values=["mp4", "mov", "avi", "mkv", "webm"],
                              state="readonly", width=10)
    format_combo.pack(side=tk.LEFT, padx=10)

    ttk.Label(format_combo_frame, text="(推荐使用mp4格式，兼容性最好)").pack(side=tk.LEFT, padx=5)

    # 添加编码器选择
    codec_frame = ttk.Frame(format_frame)
    codec_frame.pack(fill=tk.X, padx=10, pady=5)

    ttk.Label(codec_frame, text="视频编码器:").pack(side=tk.LEFT)

    # 确保video_codec变量存在
    if not hasattr(self, 'video_codec'):
        self.video_codec = tk.StringVar(value="h264")
        # 将变量添加到tracked_vars列表中，确保它被保存
        if self.video_codec not in self.tracked_vars:
            self.tracked_vars.append(self.video_codec)

    codec_combo = ttk.Combobox(codec_frame, textvariable=self.video_codec,
                             values=["h264", "h265", "vp9", "copy"],
                             state="readonly", width=10)
    codec_combo.pack(side=tk.LEFT, padx=10)

    ttk.Label(codec_frame, text="(h264兼容性最好，h265体积更小，copy直接复制不重编码)").pack(side=tk.LEFT, padx=5)

    # 视频去重设置
    dedup_frame = ttk.LabelFrame(scrollable_frame, text="视频去重设置")
    dedup_frame.pack(fill=tk.X, padx=10, pady=10)

    dedup_check = ttk.Checkbutton(dedup_frame, text="启用视频去重",
                                variable=self.enable_deduplication)
    dedup_check.pack(anchor=tk.W, padx=10, pady=5)

    # 添加去重说明
    ttk.Label(dedup_frame, text="(基于视频内容分析，跳过处理重复的视频文件)").pack(anchor=tk.W, padx=10, pady=2)

    # 添加去重方式选择
    dedup_method_frame = ttk.Frame(dedup_frame)
    dedup_method_frame.pack(fill=tk.X, padx=10, pady=5)

    ttk.Label(dedup_method_frame, text="去重方式:").pack(side=tk.LEFT)

    # 使用已经在ui_base.py中初始化的dedup_method变量

    method_combo = ttk.Combobox(dedup_method_frame, textvariable=self.dedup_method,
                             values=["content", "filename"],
                             state="readonly", width=10)
    method_combo.pack(side=tk.LEFT, padx=10)

    ttk.Label(dedup_method_frame, text="(content: 基于视频内容分析, filename: 仅基于文件名)").pack(side=tk.LEFT, padx=5)

    # 添加采样帧数设置
    sample_frame = ttk.Frame(dedup_frame)
    sample_frame.pack(fill=tk.X, padx=10, pady=5)

    ttk.Label(sample_frame, text="采样帧数:").pack(side=tk.LEFT)

    # 使用已经在ui_base.py中初始化的sample_frames变量

    sample_spinner = ttk.Spinbox(sample_frame, from_=1, to=30,
                               textvariable=self.sample_frames, width=5)
    sample_spinner.pack(side=tk.LEFT, padx=5)

    ttk.Label(sample_frame, text="(越多越准确，但处理速度越慢)").pack(side=tk.LEFT, padx=5)

    # 视频参数设置
    params_frame = ttk.LabelFrame(scrollable_frame, text="视频参数设置")
    params_frame.pack(fill=tk.X, padx=10, pady=10)

    # 视频时长
    duration_frame = ttk.Frame(params_frame)
    duration_frame.pack(fill=tk.X, padx=10, pady=5)

    ttk.Label(duration_frame, text="视频时长范围:").pack(side=tk.LEFT)
    ttk.Spinbox(duration_frame, from_=1, to=3600, textvariable=self.video_min_duration, width=6).pack(side=tk.LEFT, padx=5)
    ttk.Label(duration_frame, text="至").pack(side=tk.LEFT)
    ttk.Spinbox(duration_frame, from_=1, to=3600, textvariable=self.video_max_duration, width=6).pack(side=tk.LEFT, padx=5)
    ttk.Label(duration_frame, text="秒").pack(side=tk.LEFT)

    # 视频比例
    ratio_frame = ttk.Frame(params_frame)
    ratio_frame.pack(fill=tk.X, padx=10, pady=5)

    ttk.Label(ratio_frame, text="视频比例:").pack(side=tk.LEFT)
    ttk.Combobox(ratio_frame, textvariable=self.video_ratio,
               values=["16:9", "9:16", "4:3", "1:1"],
               state="readonly", width=10).pack(side=tk.LEFT, padx=10)

    ttk.Label(ratio_frame, text="(16:9为横屏视频，9:16为竖屏视频)").pack(side=tk.LEFT, padx=5)


def _create_cover_settings(self, parent):
    """创建封面设置选项卡内容"""
    # 使用通用滚动框架
    scrollable_frame = self._create_scrollable_frame(parent)

    # 封面分辨率设置
    resolution_frame = ttk.LabelFrame(scrollable_frame, text="封面分辨率设置")
    resolution_frame.pack(fill=tk.X, padx=10, pady=10, anchor=tk.N)

    resolution_check = ttk.Checkbutton(resolution_frame, text="自定义封面分辨率",
                                     variable=self.enable_custom_cover_resolution)
    resolution_check.pack(anchor=tk.W, padx=10, pady=5)

    size_frame = ttk.Frame(resolution_frame)
    size_frame.pack(fill=tk.X, padx=10, pady=5)

    ttk.Label(size_frame, text="宽度:").pack(side=tk.LEFT)
    ttk.Spinbox(size_frame, from_=320, to=3840,
              textvariable=self.cover_width, width=6).pack(side=tk.LEFT, padx=5)

    ttk.Label(size_frame, text="高度:").pack(side=tk.LEFT, padx=(10, 0))
    ttk.Spinbox(size_frame, from_=240, to=2160,
              textvariable=self.cover_height, width=6).pack(side=tk.LEFT, padx=5)

    # 预设按钮
    preset_frame = ttk.Frame(resolution_frame)
    preset_frame.pack(fill=tk.X, padx=10, pady=5)

    ttk.Label(preset_frame, text="预设:").pack(side=tk.LEFT)
    ttk.Button(preset_frame, text="720p",
              command=lambda: self.set_resolution(1280, 720), width=6).pack(side=tk.LEFT, padx=5)
    ttk.Button(preset_frame, text="1080p",
              command=lambda: self.set_resolution(1920, 1080), width=6).pack(side=tk.LEFT, padx=5)
    ttk.Button(preset_frame, text="4K",
              command=lambda: self.set_resolution(3840, 2160), width=6).pack(side=tk.LEFT, padx=5)
    ttk.Button(preset_frame, text="方形",
              command=lambda: self.set_resolution(1080, 1080), width=6).pack(side=tk.LEFT, padx=5)
    ttk.Button(preset_frame, text="竖屏",
              command=lambda: self.set_resolution(720, 1280), width=6).pack(side=tk.LEFT, padx=5)

    # 封面水印设置
    watermark_frame = ttk.LabelFrame(scrollable_frame, text="封面水印设置")
    watermark_frame.pack(fill=tk.X, padx=10, pady=10)

    watermark_check = ttk.Checkbutton(watermark_frame, text="启用封面隐形水印",
                                    variable=self.enable_watermark)
    watermark_check.pack(anchor=tk.W, padx=10, pady=5)

    # 水印颜色
    color_frame = ttk.Frame(watermark_frame)
    color_frame.pack(fill=tk.X, padx=10, pady=5)

    ttk.Label(color_frame, text="水印颜色:").pack(side=tk.LEFT)

    color_combo = ttk.Combobox(color_frame, textvariable=self.watermark_color,
                             values=self.color_options,
                             state="readonly", width=10)
    color_combo.pack(side=tk.LEFT, padx=10)

    # 添加颜色预览框
    watermark_color_preview = tk.Canvas(color_frame, width=20, height=20, bd=1, relief="raised")
    watermark_color_preview.pack(side=tk.LEFT, padx=5)

    # 添加颜色名称标签
    watermark_color_preview.color_name_label = ttk.Label(color_frame, text=self.color_names.get(self.watermark_color.get(), ""), width=6)
    watermark_color_preview.color_name_label.pack(side=tk.LEFT, padx=2)

    # 更新颜色预览
    self._update_color_preview(watermark_color_preview, self.watermark_color.get())

    # 绑定颜色选择事件
    color_combo.bind("<<ComboboxSelected>>",
                   lambda _: self._update_color_preview(watermark_color_preview, self.watermark_color.get()))

    # 水印透明度
    opacity_frame = ttk.Frame(watermark_frame)
    opacity_frame.pack(fill=tk.X, padx=10, pady=5)

    ttk.Label(opacity_frame, text="透明度:").pack(side=tk.LEFT)

    opacity_spinner = ttk.Spinbox(opacity_frame, from_=0.01, to=0.5, increment=0.01,
                                textvariable=self.watermark_opacity, width=5)
    opacity_spinner.pack(side=tk.LEFT, padx=5)

    ttk.Label(opacity_frame, text="(0.01-0.5，越小越不明显)").pack(side=tk.LEFT, padx=5)

    # 水印位置
    position_frame = ttk.Frame(watermark_frame)
    position_frame.pack(fill=tk.X, padx=10, pady=5)

    ttk.Label(position_frame, text="水印位置:").pack(side=tk.LEFT)

    ttk.Combobox(position_frame, textvariable=self.watermark_position,
               values=["全屏", "左上角", "右上角", "左下角", "右下角", "中心"],
               state="readonly", width=10).pack(side=tk.LEFT, padx=10)

    # 水印数量
    quantity_frame = ttk.Frame(watermark_frame)
    quantity_frame.pack(fill=tk.X, padx=10, pady=5)

    ttk.Label(quantity_frame, text="水印数量:").pack(side=tk.LEFT)

    quantity_spinner = ttk.Spinbox(quantity_frame, from_=1, to=10, increment=1,
                                textvariable=self.watermark_quantity, width=5)
    quantity_spinner.pack(side=tk.LEFT, padx=5)

    ttk.Label(quantity_frame, text="(1-10，仅在非全屏模式下有效)").pack(side=tk.LEFT, padx=5)

    # 封面文字设置
    text_frame = ttk.LabelFrame(scrollable_frame, text="封面文字设置")
    text_frame.pack(fill=tk.X, padx=10, pady=10)

    # 自动使用文件名
    auto_frame = ttk.Frame(text_frame)
    auto_frame.pack(fill=tk.X, padx=10, pady=5)

    ttk.Checkbutton(auto_frame, text="自动使用文件名作为标题文字",
                  variable=self.auto_use_filename).pack(side=tk.LEFT)

    ttk.Label(auto_frame, text="(注意: 最多显示30个字符)").pack(side=tk.LEFT, padx=10)

    # 字体大小
    font_frame = ttk.Frame(text_frame)
    font_frame.pack(fill=tk.X, padx=10, pady=5)

    ttk.Label(font_frame, text="字体大小:").pack(side=tk.LEFT)

    font_spinner = ttk.Spinbox(font_frame, from_=20, to=100,
                             textvariable=self.cover_size, width=5)
    font_spinner.pack(side=tk.LEFT, padx=5)

    # 字体大小预览
    preview_button = ttk.Button(font_frame, text="预览",
                              command=self._update_font_size_preview)
    preview_button.pack(side=tk.LEFT, padx=5)


def _create_status_settings(self, parent):
    """创建配置状态选项卡内容"""
    # 创建滚动文本区域来显示所有配置状态
    status_frame = ttk.LabelFrame(parent, text="当前所有配置状态")
    status_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    # 创建文本区域 - 删除滚动条，只保留滚轮滚动
    status_text = tk.Text(status_frame, wrap=tk.WORD, height=20, width=80)
    status_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    status_text.config(state=tk.NORMAL)

    # 添加滚轮支持
    def _on_mousewheel(event):
        status_text.yview_scroll(int(-1*(event.delta/120)), "units")

    def _bind_mousewheel(event):
        status_text.bind_all("<MouseWheel>", _on_mousewheel)

    def _unbind_mousewheel(event):
        status_text.unbind_all("<MouseWheel>")

    status_text.bind('<Enter>', _bind_mousewheel)
    status_text.bind('<Leave>', _unbind_mousewheel)

    # 保存对status_text的引用，以便刷新时使用
    self.status_text = status_text

    # 更新状态信息
    self._update_status_info()

    # 添加刷新按钮
    refresh_frame = ttk.Frame(status_frame)
    refresh_frame.pack(fill=tk.X, padx=5, pady=5)

    ttk.Button(refresh_frame, text="刷新状态信息",
              command=self._update_status_info).pack(side=tk.RIGHT)

    return status_frame

def _update_status_info(self):
    """更新配置状态信息"""
    # 确保status_text存在
    if not hasattr(self, 'status_text') or self.status_text is None:
        return

    # 清空当前内容
    self.status_text.config(state=tk.NORMAL)
    self.status_text.delete(1.0, tk.END)

    # 添加配置状态信息
    self.status_text.insert(tk.END, "=== 当前配置状态 ===\n\n")

    # 视频源目录
    self.status_text.insert(tk.END, f"视频源目录: {self.video_dir.get() or '未设置'}\n")
    self.status_text.insert(tk.END, f"处理后视频目录: {self.processed_videos_dir.get() or '未设置'}\n")
    self.status_text.insert(tk.END, f"处理后封面目录: {self.processed_covers_dir.get() or '未设置'}\n\n")

    # 视频处理设置
    self.status_text.insert(tk.END, "=== 视频处理设置 ===\n")
    self.status_text.insert(tk.END, f"视频时长范围: {self.video_min_duration.get()}-{self.video_max_duration.get()}秒\n")
    self.status_text.insert(tk.END, f"视频比例: {self.video_ratio.get()}\n")
    self.status_text.insert(tk.END, f"处理线程数: {self.thread_num.get()}\n")
    self.status_text.insert(tk.END, f"视频去重: {'启用' if self.enable_deduplication.get() else '禁用'}\n")
    if self.enable_deduplication.get():
        if hasattr(self, 'dedup_method'):
            self.status_text.insert(tk.END, f"去重方式: {self.dedup_method.get()}\n")
        if hasattr(self, 'sample_frames'):
            self.status_text.insert(tk.END, f"采样帧数: {self.sample_frames.get()}\n")
    self.status_text.insert(tk.END, f"视频格式转换: {'启用' if self.enable_format_conversion.get() else '禁用'}\n")
    if self.enable_format_conversion.get():
        self.status_text.insert(tk.END, f"输出格式: {self.output_format.get()}\n")
        if hasattr(self, 'video_codec'):
            self.status_text.insert(tk.END, f"视频编码器: {self.video_codec.get()}\n")
    self.status_text.insert(tk.END, "\n")

    # 封面设置
    self.status_text.insert(tk.END, "=== 封面设置 ===\n")
    self.status_text.insert(tk.END, f"自定义封面分辨率: {'启用' if self.enable_custom_cover_resolution.get() else '禁用'}\n")
    if self.enable_custom_cover_resolution.get():
        self.status_text.insert(tk.END, f"封面分辨率: {self.cover_width.get()}x{self.cover_height.get()}\n")
    self.status_text.insert(tk.END, f"自动使用文件名作为标题: {'启用' if self.auto_use_filename.get() else '禁用'}\n")
    self.status_text.insert(tk.END, f"左上角文字: {self.cover_text_top.get() or '无'}\n")
    self.status_text.insert(tk.END, f"右下角文字: {self.cover_text_bottom.get() or '无'}\n")
    self.status_text.insert(tk.END, f"字体大小: {self.cover_size.get()}\n")
    self.status_text.insert(tk.END, f"封面水印: {'启用' if self.enable_watermark.get() else '禁用'}\n")
    if self.enable_watermark.get():
        self.status_text.insert(tk.END, f"水印颜色: {self.watermark_color.get()}\n")
        self.status_text.insert(tk.END, f"水印透明度: {self.watermark_opacity.get()}\n")
        self.status_text.insert(tk.END, f"水印位置: {self.watermark_position.get()}\n")
        self.status_text.insert(tk.END, f"水印数量: {self.watermark_quantity.get()} (仅非全屏模式有效)\n")
    self.status_text.insert(tk.END, "\n")

    # 性能设置
    self.status_text.insert(tk.END, "=== 性能设置 ===\n")
    self.status_text.insert(tk.END, f"GPU加速: {'启用' if self.enable_gpu_acceleration.get() else '禁用'}\n")
    if self.enable_gpu_acceleration.get():
        self.status_text.insert(tk.END, f"GPU设备: {self.gpu_device.get()}\n")
    self.status_text.insert(tk.END, f"智能资源分配: {'启用' if self.enable_smart_resource.get() else '禁用'}\n")
    if self.enable_smart_resource.get():
        self.status_text.insert(tk.END, f"内存限制: {self.memory_limit.get()}MB (0=自动)\n")
    self.status_text.insert(tk.END, "\n")

    # 界面设置
    self.status_text.insert(tk.END, "=== 界面设置 ===\n")
    self.status_text.insert(tk.END, f"黑暗模式: {'启用' if self.enable_dark_mode.get() else '禁用'}\n")
    self.status_text.insert(tk.END, f"界面字体大小: {self.font_size_var.get()}\n")
    self.status_text.insert(tk.END, f"自动保存间隔: {self.auto_save_interval//1000}秒\n\n")

    # 设置为只读
    self.status_text.config(state=tk.DISABLED)
