"""
账号登录模块 - 负责处理账号登录
"""

import os
import json
import time
import traceback
from typing import Dict, Any, Optional, Callable, Tuple, List
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

from 网易号存稿.browser.driver import DriverManager
from 网易号存稿.browser.actions import BrowserActions
from 网易号存稿.common.utils import random_sleep
from 网易号存稿.common.constants import NETEASE_BACKEND_URL

class AccountLogin:
    """账号登录类，负责处理账号登录"""

    # 平台标识符 - 用于模块识别和配置加载
    PLATFORM_ID = "netease"
    PLATFORM_NAME = "网易号平台"

    def __init__(self, driver_manager: DriverManager = None, log_callback: Callable = None):
        """
        初始化账号登录

        Args:
            driver_manager: 浏览器驱动管理器（可选，如果不提供则使用独立驱动创建）
            log_callback: 日志回调函数
        """
        self.driver_manager = driver_manager  # 可以为None，使用独立驱动创建
        self.log_callback = log_callback
        self.browser_actions = None
        self.last_screenshot = ""

        # 设置平台标识
        self.platform_id = self.PLATFORM_ID
        self.platform_name = self.PLATFORM_NAME  # 保存最后一次截图的路径

    def log(self, message: str, internal: bool = False) -> None:
        """
        记录日志

        Args:
            message: 日志消息
            internal: 是否为内部日志（内部日志会添加平台标记，但不显示给用户）
        """
        if internal:
            # 内部日志添加平台标记，用于调试和模块识别
            formatted_message = f"[{self.platform_id}] {message}"
        else:
            # 用户日志不显示平台标记
            formatted_message = message

        if self.log_callback:
            self.log_callback(formatted_message)
        else:
            print(formatted_message)

    def init_driver(self, headless: bool = False, account: str = "") -> webdriver.Chrome:
        """
        独立初始化Chrome浏览器驱动，参考头条号的实现

        Args:
            headless: 是否使用无头模式
            account: 账号名称（用于日志显示，可选）

        Returns:
            Chrome浏览器驱动实例
        """
        # 导入端口管理器
        try:
            from 网易号存稿.browser.driver import PortManager
        except ImportError:
            # 如果相对导入失败，尝试绝对导入
            import sys
            import os
            sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
            from browser.driver import PortManager

        # 获取可用端口
        port = PortManager.get_available_port()
        if port is None:
            self.log("无法获取可用端口，所有端口都被占用", internal=True)
            raise Exception("无法获取可用端口")

        try:
            # 设置Chrome选项
            chrome_options = Options()

            # 设置无头模式
            if headless:
                chrome_options.add_argument("--headless")

            # 设置窗口大小
            chrome_options.add_argument("--window-size=1920,1080")

            # 禁用GPU加速
            chrome_options.add_argument("--disable-gpu")

            # 禁用扩展
            chrome_options.add_argument("--disable-extensions")

            # 禁用沙盒
            chrome_options.add_argument("--no-sandbox")

            # 禁用开发者工具
            chrome_options.add_argument("--disable-dev-shm-usage")

            # 设置语言为中文
            chrome_options.add_argument("--lang=zh-CN")

            # 设置编码
            chrome_options.add_argument("--charset=UTF-8")

            # 禁用自动化控制提示
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option("useAutomationExtension", False)

            # 添加多线程相关选项
            chrome_options.add_argument("--disable-background-timer-throttling")
            chrome_options.add_argument("--disable-backgrounding-occluded-windows")
            chrome_options.add_argument("--disable-renderer-backgrounding")

            # 创建Service对象，指定端口
            service = Service(port=port)

            # 创建Chrome浏览器驱动
            driver = webdriver.Chrome(service=service, options=chrome_options)

            # 设置窗口大小
            driver.set_window_size(1920, 1080)

            # 设置页面加载超时
            driver.set_page_load_timeout(60)

            # 设置脚本执行超时
            driver.set_script_timeout(30)

            # 保存端口信息到驱动对象，用于后续释放
            driver._assigned_port = port

            # 输出合并的日志信息
            if account:
                self.log(f"开始查询账号数据: {account} [netease] 浏览器驱动创建成功，使用端口: {port}")
            else:
                self.log(f"浏览器驱动创建成功，使用端口: {port}", internal=True)

            return driver

        except Exception as e:
            # 释放端口
            PortManager.release_port(port)
            raise e

    def close_driver(self, driver: webdriver.Chrome = None) -> None:
        """
        关闭浏览器驱动并释放端口

        Args:
            driver: 要关闭的浏览器驱动（可选，如果不提供则关闭当前驱动）
        """
        if driver:
            try:
                # 释放端口（独立驱动创建方式）
                if hasattr(driver, '_assigned_port'):
                    from 网易号存稿.browser.driver import PortManager
                    PortManager.release_port(driver._assigned_port)

                # 关闭浏览器
                driver.quit()
            except Exception as e:
                self.log(f"关闭浏览器时出错: {str(e)}", internal=True)
        elif self.driver_manager:
            # 使用DriverManager关闭（兼容旧方式）
            try:
                self.driver_manager.close_driver()
            except Exception as e:
                self.log(f"关闭浏览器时出错: {str(e)}", internal=True)

    def _close_current_driver(self) -> None:
        """
        关闭当前驱动的内部方法，兼容两种驱动创建方式
        """
        if self.driver_manager:
            self.driver_manager.close_driver()
        # 如果使用独立驱动创建，driver会在调用处单独处理

    def login_with_cookies(self, cookie_path: str, headless: bool = False, account: str = "") -> Tuple[bool, Optional[webdriver.Chrome]]:
        """
        使用Cookie登录

        Args:
            cookie_path: Cookie文件路径
            headless: 是否使用无头模式
            account: 账号名称（用于日志显示）

        Returns:
            (是否成功登录, 浏览器驱动)
        """
        try:
            # 🚀 闪电预检测 - 0.1秒内快速检测Cookie有效性
            lightning_check = self._lightning_cookie_detection(cookie_path, account)
            if not lightning_check[0]:
                self.log(f"❌ 登录失败: {lightning_check[1]}")
                return False, None

            # 预检测通过，继续浏览器验证
            self.log("✅ Cookie预检测通过，启动浏览器验证...")

            # 检查Cookie文件是否存在（双重保险）
            if not os.path.exists(cookie_path):
                self.log(f"Cookie文件不存在: {cookie_path}")
                return False, None

            # 创建浏览器驱动 - 优先使用独立方式（更可靠）
            try:
                # 使用独立的驱动创建方式（参考头条号）
                driver = self.init_driver(headless, account)
                if account:
                    self.log(f"🔐 {account} 正在登录")
                else:
                    self.log(f"🌐 浏览器已启动")
            except Exception as e:
                # 如果独立方式失败，尝试使用DriverManager（向后兼容）
                if self.driver_manager:
                    self.log(f"独立驱动创建失败，尝试使用DriverManager: {str(e)}", internal=True)
                    driver = self.driver_manager.create_driver(headless, None, account)
                    if not driver:
                        self.log(f"❌ {account} 登录失败：浏览器启动失败")
                        return False, None
                else:
                    self.log(f"❌ {account} 登录失败：浏览器启动失败 - {str(e)}")
                    return False, None

            # 创建浏览器操作对象（静默）
            self.browser_actions = BrowserActions(driver, self.log_callback)

            # 访问登录页面（合并日志）
            try:
                driver.get("https://mp.163.com/login.html")
                random_sleep(2.0, 3.0)
                # 不输出详细的URL信息，保持简洁
            except Exception as e:
                self.log(f"❌ 访问登录页面失败: {str(e)}")
                self.close_driver(driver)
                return False, None

            # 加载Cookie（简化日志输出）
            cookies = None

            # 根据文件扩展名确定cookie格式
            if cookie_path.lower().endswith('.txt'):
                # 读取cookie文件内容
                try:
                    with open(cookie_path, 'r', encoding='utf-8') as f:
                        cookie_content = f.read().strip()

                    # 解析cookie字符串
                    cookies = self._parse_cookie_string(cookie_content)
                except Exception as e:
                    self.log(f"读取TXT cookie文件失败: {str(e)}")
                    cookies = None
            elif cookie_path.lower().endswith('.json'):
                # 直接加载JSON格式的cookie
                try:
                    with open(cookie_path, 'r', encoding='utf-8') as f:
                        cookies = json.load(f)
                except Exception as e:
                    self.log(f"读取JSON cookie文件失败: {str(e)}")
                    cookies = None
            else:
                self.log(f"不支持的cookie文件格式: {cookie_path}")
                cookies = None

            if not cookies:
                self.log("❌ Cookie加载失败或为空")
                self.close_driver(driver)
                return False, None

            # 🚀 快速Cookie添加优化
            try:
                driver.delete_all_cookies()
                successful_cookies = 0

                # 预处理所有Cookie，批量验证格式
                valid_cookies = []
                for cookie in cookies:
                    try:
                        # 快速格式验证
                        if not isinstance(cookie, dict) or 'name' not in cookie or 'value' not in cookie:
                            continue

                        # 快速处理Cookie格式
                        cookie_to_add = {
                            'name': cookie['name'],
                            'value': cookie['value'],
                            'path': cookie.get('path', '/'),
                            'secure': cookie.get('secure', False),
                            'httpOnly': cookie.get('httpOnly', False)
                        }

                        # 智能域名设置（优化版）
                        original_domain = cookie.get('domain', '')
                        if not original_domain or original_domain == 'localhost':
                            cookie_name = cookie['name'].lower()
                            if any(keyword in cookie_name for keyword in ['mp', 'subscribe', 'netease']):
                                cookie_to_add['domain'] = '.mp.163.com'
                            else:
                                cookie_to_add['domain'] = '.163.com'
                        else:
                            if not original_domain.startswith('.') and '163.com' in original_domain:
                                cookie_to_add['domain'] = '.' + original_domain.lstrip('.')
                            else:
                                cookie_to_add['domain'] = original_domain

                        # 确保path正确
                        if 'path' not in cookie_to_add:
                            cookie_to_add['path'] = '/'

                        # 处理expiry字段（简化日志输出）
                        if 'expiry' in cookie_to_add:
                            try:
                                expiry_value = int(float(cookie_to_add['expiry']))
                                cookie_to_add['expiry'] = expiry_value
                                # 检查是否过期（只记录过期的Cookie）
                                current_time = time.time()
                                if expiry_value < current_time:
                                    self.log(f"  ⚠️ Cookie已过期: {cookie_to_add['name']}")
                            except (ValueError, TypeError):
                                # 如果expiry无效，删除它
                                del cookie_to_add['expiry']

                        # 处理expires字段（转换为expiry）
                        if 'expires' in cookie_to_add and 'expiry' not in cookie_to_add:
                            try:
                                cookie_to_add['expiry'] = int(float(cookie_to_add['expires']))
                                del cookie_to_add['expires']
                            except (ValueError, TypeError):
                                del cookie_to_add['expires']

                        # 移除可能导致问题的字段
                        problematic_fields = ['sameSite', 'priority', 'sameParty', 'sourceScheme', 'sourcePort']
                        for field in problematic_fields:
                            if field in cookie_to_add:
                                del cookie_to_add[field]

                        # 添加Cookie（不输出详细过程）
                        driver.add_cookie(cookie_to_add)
                        successful_cookies += 1

                    except Exception as e:
                        # 只记录失败的Cookie
                        continue

                # 输出Cookie添加结果汇总
                self.log(f"✅ Cookie添加完成: 成功 {successful_cookies}/{len(cookies)} 个")

            except Exception as e:
                self.log(f"❌ Cookie处理失败: {str(e)}")
                self.close_driver(driver)
                return False, None

            # 🚀 快速验证优化：智能等待 + 并行检测
            self.log("Cookie已添加，开始快速验证...")

            try:
                # 第一步：快速刷新检测（减少等待时间）
                driver.refresh()

                # 🚀 智能等待：先快速检测，再逐步增加等待时间
                for wait_time in [0.5, 1.0, 2.0]:  # 渐进式等待
                    time.sleep(wait_time)
                    current_url = driver.current_url

                    if self._is_backend_url(current_url):
                        self.log(f"✅ {account} 快速登录成功 (等待{wait_time}秒)")
                        return True, driver

                # 第二步：如果快速检测失败，尝试主动导航
                current_url = driver.current_url
                self.log(f"刷新后URL: {current_url}")

                if "login" in current_url.lower():
                    self.log("仍在登录页面，尝试直接导航到后台...")
                    driver.get("https://mp.163.com/subscribe_v4/index.html")

                    # 🚀 快速导航检测
                    for wait_time in [1.0, 2.0]:  # 减少导航等待时间
                        time.sleep(wait_time)
                        final_url = driver.current_url

                        if self._is_backend_url(final_url):
                            self.log(f"✅ {account} 导航登录成功 (等待{wait_time}秒)")
                            return True, driver

            except Exception as e:
                self.log(f"快速验证时出错: {str(e)}")

            # 第三步：最后的快速URL监控（减少监控时间）
            self.log("进行最后的快速验证...")
            if self._verify_login_fast(driver):
                self.log("✅ 快速验证成功")
                return True, driver
            else:
                self.log("❌ 所有验证方式都失败")

            self.close_driver(driver)
            return False, None

        except Exception as e:
            self.log(f"登录过程中发生错误: {str(e)}")
            traceback.print_exc()

            # 关闭浏览器
            try:
                if 'driver' in locals() and driver:
                    self.close_driver(driver)
                else:
                    self._close_current_driver()
            except:
                pass

            return False, None



    def _parse_cookie_string(self, cookie_str):
        """解析cookie字符串为cookie对象列表 - 支持账号格式"""
        try:
            cookies = []

            # 🚀 首先尝试解析为账号格式 {"accountId": "...", "cookies": {...}}
            try:
                parsed_data = json.loads(cookie_str)

                # 检查是否是账号格式
                if isinstance(parsed_data, dict) and 'cookies' in parsed_data:
                    cookies_dict = parsed_data['cookies']
                    if isinstance(cookies_dict, dict):
                        # 转换字典格式为Cookie列表格式
                        cookie_list = []
                        for name, value in cookies_dict.items():
                            cookie_list.append({
                                'name': name,
                                'value': str(value),
                                'domain': '.163.com',
                                'path': '/'
                            })
                        return cookie_list

                # 检查是否是Cookie数组格式
                elif isinstance(parsed_data, list):
                    return parsed_data

            except json.JSONDecodeError:
                pass

            # 尝试解析为浏览器导出的cookie格式（多行）
            lines = cookie_str.strip().split('\n')
            if len(lines) > 1:

                # Netscape cookie格式
                for i, line in enumerate(lines):
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue

                    # 尝试Tab分隔的Netscape格式
                    parts = line.split('\t')
                    if len(parts) >= 7:
                        try:
                            domain, flag, path, secure, expires, name, value = parts[:7]
                            cookie = {
                                'domain': domain,
                                'path': path,
                                'name': name,
                                'value': value,
                                'secure': secure.lower() == 'true'
                            }
                            # 处理过期时间
                            try:
                                if expires and expires != '0':
                                    cookie['expiry'] = int(float(expires))
                            except:
                                pass
                            cookies.append(cookie)
                            continue
                        except Exception as e:
                            self.log(f"解析第 {i+1} 行Netscape格式失败: {str(e)}")

                    # 尝试解析为键值对格式
                    if '=' in line:
                        try:
                            cookie = self._parse_single_cookie_line(line)
                            if cookie:
                                cookies.append(cookie)
                        except Exception as e:
                            self.log(f"解析第 {i+1} 行键值对格式失败: {str(e)}")

            # 如果多行解析失败，尝试解析为单行cookie字符串
            if not cookies:
                self.log("尝试解析为单行cookie字符串")

                # 检查是否是浏览器复制的cookie字符串格式
                if '; ' in cookie_str or ';' in cookie_str:
                    # 标准的cookie字符串格式：name1=value1; name2=value2; ...
                    cookie_pairs = [pair.strip() for pair in cookie_str.split(';') if pair.strip()]

                    for pair in cookie_pairs:
                        if '=' in pair:
                            try:
                                name, value = pair.split('=', 1)
                                cookie = {
                                    'name': name.strip(),
                                    'value': value.strip(),
                                    'domain': '.163.com',
                                    'path': '/'
                                }
                                cookies.append(cookie)
                            except Exception as e:
                                self.log(f"解析cookie对 '{pair}' 失败: {str(e)}")

                # 如果还是没有解析到，尝试作为单个cookie处理
                elif '=' in cookie_str:
                    try:
                        cookie = self._parse_single_cookie_line(cookie_str)
                        if cookie:
                            cookies.append(cookie)
                    except Exception as e:
                        self.log(f"解析单个cookie失败: {str(e)}")

            self.log(f"Cookie解析完成，共解析到 {len(cookies)} 个cookies")

            # 验证解析结果
            valid_cookies = []
            for i, cookie in enumerate(cookies):
                if self._validate_cookie(cookie):
                    valid_cookies.append(cookie)
                else:
                    self.log(f"Cookie {i+1} 验证失败: {cookie}")

            self.log(f"有效的cookies数量: {len(valid_cookies)}")
            return valid_cookies

        except Exception as e:
            self.log(f"解析cookie字符串时发生异常: {str(e)}")
            import traceback
            traceback.print_exc()
            return []

    def _parse_single_cookie_line(self, line):
        """解析单行cookie"""
        try:
            # 处理包含属性的cookie行
            # 格式：name=value; domain=.example.com; path=/; secure; httponly
            parts = [part.strip() for part in line.split(';')]

            if not parts or '=' not in parts[0]:
                return None

            # 第一部分是name=value
            name, value = parts[0].split('=', 1)
            cookie = {
                'name': name.strip(),
                'value': value.strip(),
                'domain': '.163.com',  # 默认域名
                'path': '/'  # 默认路径
            }

            # 处理其他属性
            for part in parts[1:]:
                part = part.strip().lower()
                if '=' in part:
                    attr_name, attr_value = part.split('=', 1)
                    attr_name = attr_name.strip()
                    attr_value = attr_value.strip()

                    if attr_name == 'domain':
                        cookie['domain'] = attr_value
                    elif attr_name == 'path':
                        cookie['path'] = attr_value
                    elif attr_name == 'expires':
                        # 尝试解析过期时间
                        try:
                            import time
                            from datetime import datetime
                            # 尝试多种时间格式
                            for fmt in ['%a, %d %b %Y %H:%M:%S %Z', '%a, %d-%b-%Y %H:%M:%S %Z']:
                                try:
                                    dt = datetime.strptime(attr_value, fmt)
                                    cookie['expiry'] = int(dt.timestamp())
                                    break
                                except:
                                    continue
                        except:
                            pass
                    elif attr_name == 'max-age':
                        try:
                            cookie['expiry'] = int(time.time()) + int(attr_value)
                        except:
                            pass
                else:
                    # 处理标志属性
                    if part == 'secure':
                        cookie['secure'] = True
                    elif part == 'httponly':
                        cookie['httpOnly'] = True

            return cookie

        except Exception as e:
            self.log(f"解析单行cookie失败: {str(e)}")
            return None

    def _validate_cookie(self, cookie):
        """验证cookie格式"""
        if not isinstance(cookie, dict):
            return False

        # 必须有name和value
        if 'name' not in cookie or 'value' not in cookie:
            return False

        # name和value不能为空
        if not cookie['name'] or cookie['value'] is None:
            return False

        # 确保有domain
        if 'domain' not in cookie:
            cookie['domain'] = '.163.com'

        # 确保有path
        if 'path' not in cookie:
            cookie['path'] = '/'

        return True

    def login_with_phone(self, headless: bool = False) -> Tuple[bool, Optional[webdriver.Chrome], Optional[Dict[str, Any]]]:
        """
        使用手机号登录

        Args:
            headless: 是否使用无头模式

        Returns:
            (是否成功登录, 浏览器驱动, Cookie数据)
        """
        try:
            # 创建浏览器驱动
            driver = self.driver_manager.create_driver(headless)
            if not driver:
                self.log("创建浏览器驱动失败")
                return False, None, None

            # 创建浏览器操作对象
            self.browser_actions = BrowserActions(driver, self.log_callback)

            # 打开网易号后台
            self.log("正在打开网易号后台...")
            if not self.browser_actions.navigate_to(NETEASE_BACKEND_URL):
                self.log("打开网易号后台失败")
                self._close_current_driver()
                return False, None, None

            # 等待用户手动登录
            self.log("请在浏览器中完成登录操作...")
            self.log("系统将监控页面变化，检测登录状态...")

            # 等待登录成功 - 使用改进的验证机制
            max_wait_time = 300  # 最多等待5分钟
            start_time = time.time()
            last_check_time = start_time

            while time.time() - start_time < max_wait_time:
                # 检查浏览器是否仍然活动
                if not self.driver_manager.is_driver_alive():
                    self.log("浏览器已被关闭，无法继续登录流程")
                    return False, None, None

                # 每30秒进行一次完整的登录验证
                current_time = time.time()
                if current_time - last_check_time >= 30:
                    self.log("进行定期登录状态检查...")
                    if self._verify_login(driver):
                        self.log("✅ 登录成功")

                        try:
                            # 获取Cookie
                            cookies = driver.get_cookies()
                            return True, driver, cookies
                        except Exception as cookie_error:
                            self.log(f"获取Cookie失败: {str(cookie_error)}")
                            # 如果获取Cookie失败，可能是浏览器已被关闭
                            return False, None, None

                    last_check_time = current_time
                else:
                    # 简单检查是否已经在后台页面
                    try:
                        current_url = driver.current_url
                        if self._is_backend_url(current_url):
                            self.log("✅ 检测到已进入后台，登录成功")

                            try:
                                # 获取Cookie
                                cookies = driver.get_cookies()
                                return True, driver, cookies
                            except Exception as cookie_error:
                                self.log(f"获取Cookie失败: {str(cookie_error)}")
                                return False, None, None
                    except Exception as e:
                        self.log(f"检查页面状态时出错: {str(e)}")

                # 等待一段时间再检查
                time.sleep(2)

            self.log("登录超时，请重试")
            self._close_current_driver()
            return False, None, None

        except Exception as e:
            self.log(f"登录过程中发生错误: {str(e)}")
            traceback.print_exc()

            # 关闭浏览器
            self._close_current_driver()

            return False, None, None

    def _load_cookies(self, cookie_path: str) -> Optional[List[Dict[str, Any]]]:
        """
        加载Cookie

        Args:
            cookie_path: Cookie文件路径

        Returns:
            Cookie列表，如果加载失败则返回None
        """
        try:
            with open(cookie_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            self.log(f"加载Cookie失败: {str(e)}")
            return None

    def login(self, account: str, driver: Optional[webdriver.Chrome] = None, account_dir: str = None) -> bool:
        """
        登录账号

        Args:
            account: 账号名称
            driver: 浏览器驱动，如果为None则创建新的驱动
            account_dir: 账号目录路径，如果为None则使用默认路径

        Returns:
            是否成功登录
        """
        try:
            # 获取账号根目录 - 支持动态账号目录
            if account_dir:
                base_account_dir = account_dir
            else:
                # 兼容旧的硬编码路径
                base_account_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "账号")

            # 查找Cookie文件 - 只查找账号名.txt格式
            cookie_path = os.path.join(base_account_dir, f"{account}.txt")
            if not os.path.exists(cookie_path):
                cookie_path = None

            if not cookie_path:
                self.log(f"Cookie文件不存在: {account}")
                return False

            # 使用Cookie登录
            success, driver = self.login_with_cookies(cookie_path, headless=False, account=account)
            return success

        except Exception as e:
            self.log(f"登录账号 {account} 失败: {str(e)}")
            traceback.print_exc()
            return False



    def _verify_login_by_url_only(self, driver: webdriver.Chrome) -> bool:
        """
        仅通过URL检测验证登录状态 - 不刷新页面，不主动导航

        Args:
            driver: 浏览器驱动

        Returns:
            是否已登录
        """
        try:
            self.log("开始仅通过URL检测登录状态...")

            # 设置监控参数
            max_wait_time = 30  # 最大等待30秒
            check_interval = 2  # 每2秒检测一次
            checks_count = max_wait_time // check_interval

            # 获取初始URL
            initial_url = driver.current_url
            self.log(f"初始URL: {initial_url}")

            for i in range(checks_count):
                try:
                    # 等待检测间隔
                    time.sleep(check_interval)

                    # 获取当前URL（不进行任何页面操作）
                    current_url = driver.current_url
                    check_num = i + 1

                    self.log(f"第{check_num}次URL检测 ({check_num * check_interval}秒): {current_url}")

                    # 检查是否已经进入后台页面
                    if self._is_backend_url(current_url):
                        self.log(f"✅ 登录成功！已进入网易号后台 (第{check_num}次检测)")
                        return True

                    # 检查URL是否发生了变化
                    if current_url != initial_url:
                        self.log(f"URL已变化: {initial_url} -> {current_url}")

                        # 如果变化后的URL是后台页面，则登录成功
                        if self._is_backend_url(current_url):
                            self.log(f"✅ 登录成功！URL已跳转到后台")
                            return True

                except Exception as e:
                    self.log(f"第{check_num}次URL检测时出错: {str(e)}")
                    continue

            # 最终检测
            final_url = driver.current_url
            self.log(f"URL监控结束，最终URL: {final_url}")

            if self._is_backend_url(final_url):
                self.log("✅ 最终URL检测：登录成功")
                return True
            else:
                self.log("❌ 最终URL检测：登录失败，未能进入后台页面")
                return False

        except Exception as e:
            self.log(f"URL检测验证登录状态时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def _verify_login(self, driver: webdriver.Chrome) -> bool:
        """
        验证登录状态 - 改进版，监控页面变化

        根据用户要求：
        1. 检测登录页面有没有变化
        2. 等待时间为30秒，每2秒检测一次
        3. 当账号进入到 https://mp.163.com/subscribe_v4/index.html#/ 即为登录成功

        Args:
            driver: 浏览器驱动

        Returns:
            是否已登录
        """
        try:
            self.log("正在验证登录状态，监控页面变化...")

            # 等待页面基本加载
            WebDriverWait(driver, 5).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # 获取初始URL作为基准
            initial_url = driver.current_url
            self.log(f"初始URL: {initial_url}")

            # 设置监控参数
            max_wait_time = 30  # 最大等待30秒
            check_interval = 2  # 每2秒检测一次
            checks_count = max_wait_time // check_interval

            self.log(f"开始监控页面变化，最大等待{max_wait_time}秒，每{check_interval}秒检测一次")

            for i in range(checks_count):
                try:
                    # 等待检测间隔
                    time.sleep(check_interval)

                    # 获取当前URL
                    current_url = driver.current_url
                    check_num = i + 1

                    self.log(f"第{check_num}次检测 ({check_num * check_interval}秒): {current_url}")

                    # 检查是否已经进入后台页面
                    if self._is_backend_url(current_url):
                        self.log(f"✅ 登录成功！已进入网易号后台 (第{check_num}次检测)")
                        return True

                    # 检查是否在登录页面
                    if self._is_login_url(current_url):
                        self.log(f"仍在登录页面，继续等待... (第{check_num}次检测)")
                        continue

                    # 检查URL是否发生了变化
                    if current_url != initial_url:
                        self.log(f"页面URL已变化: {initial_url} -> {current_url}")

                        # 如果变化后的URL是后台页面，则登录成功
                        if self._is_backend_url(current_url):
                            self.log(f"✅ 登录成功！页面已跳转到后台")
                            return True

                        # 如果变化后仍然是登录相关页面，继续等待
                        if self._is_login_url(current_url):
                            self.log("页面变化但仍在登录流程中，继续等待...")
                            continue

                    # 不进行主动导航，只监控URL变化

                except Exception as e:
                    self.log(f"第{check_num}次检测时出错: {str(e)}")
                    continue

            # 所有检测都完成后，进行最终判断
            final_url = driver.current_url
            self.log(f"监控结束，最终URL: {final_url}")

            if self._is_backend_url(final_url):
                self.log("✅ 最终检测：登录成功")
                return True
            else:
                self.log("❌ 最终检测：登录失败，未能进入后台页面")
                return False

        except Exception as e:
            self.log(f"验证登录状态时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def _is_backend_url(self, url: str) -> bool:
        """
        检查URL是否为后台页面

        Args:
            url: 要检查的URL

        Returns:
            是否为后台页面
        """
        if not url:
            return False

        # 后台页面的URL模式
        backend_patterns = [
            "mp.163.com/subscribe_v4/index.html",
            "mp.163.com/subscribe_v4",
            "mp.163.com/v2/article",
            "mp.163.com/v2/media",
            "mp.163.com/v2/user"
        ]

        # 检查是否匹配后台模式且不包含登录标识
        for pattern in backend_patterns:
            if pattern in url and "login" not in url.lower():
                return True

        return False

    def _is_login_url(self, url: str) -> bool:
        """
        检查URL是否为登录页面

        Args:
            url: 要检查的URL

        Returns:
            是否为登录页面
        """
        if not url:
            return False

        # 登录页面的标识
        login_indicators = ["login", "auth", "signin", "passport"]

        return any(indicator in url.lower() for indicator in login_indicators)

    def _save_login_screenshot(self, driver: webdriver.Chrome, prefix: str = "login_failed"):
        """保存登录相关的截图"""
        try:
            if not driver:
                return

            # 获取截图目录
            screenshots_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "screenshots")
            os.makedirs(screenshots_dir, exist_ok=True)

            # 生成截图文件名
            timestamp = time.strftime("%Y%m%d%H%M%S")
            screenshot_filename = f"{prefix}_{timestamp}.png"
            screenshot_path = os.path.join(screenshots_dir, screenshot_filename)

            # 保存截图
            driver.save_screenshot(screenshot_path)
            self.log(f"已保存截图: {screenshot_path}")

            # 将截图路径保存为实例变量，以便外部访问
            self.last_screenshot = screenshot_path

        except Exception as e:
            self.log(f"保存截图时出错: {str(e)}")
            self.last_screenshot = ""



    def save_cookies(self, cookies: List[Dict[str, Any]], cookie_path: str) -> bool:
        """
        保存Cookie

        Args:
            cookies: Cookie列表
            cookie_path: Cookie文件路径

        Returns:
            是否成功保存
        """
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(cookie_path), exist_ok=True)

            # 保存Cookie
            with open(cookie_path, 'w', encoding='utf-8') as f:
                json.dump(cookies, f, ensure_ascii=False, indent=4)

            self.log(f"已保存Cookie到: {cookie_path}")
            return True

        except Exception as e:
            self.log(f"保存Cookie失败: {str(e)}")
            return False

    def _lightning_cookie_detection(self, cookie_path: str, account: str = "") -> Tuple[bool, str]:
        """
        🚀 闪电Cookie检测 - 0.1秒内快速检测Cookie有效性

        Args:
            cookie_path: Cookie文件路径
            account: 账号名称（用于日志）

        Returns:
            (是否有效, 检测结果描述)
        """
        import time
        import json

        start_time = time.time()

        try:
            # 第1层: 文件存在性检查 (0.001秒)
            if not os.path.exists(cookie_path):
                return False, "Cookie文件不存在"

            # 第2层: 文件内容检查 (0.01秒)
            try:
                with open(cookie_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                if not content:
                    return False, "Cookie文件为空"
            except Exception as e:
                return False, f"Cookie文件读取失败: {str(e)}"

            # 第3层: 格式检查 (0.01秒) - 支持JSON和TXT格式
            try:
                data = json.loads(content)

                # 检查是否是账号格式 {"accountId": "...", "cookies": {...}}
                if isinstance(data, dict) and 'cookies' in data:
                    cookies_dict = data['cookies']
                    if not isinstance(cookies_dict, dict) or len(cookies_dict) == 0:
                        return False, "Cookie数据为空"

                    # 第4层: 关键Cookie检查 (0.03秒) - 字典格式
                    essential_check = self._check_essential_netease_cookies_dict(cookies_dict)
                    if not essential_check[0]:
                        return False, essential_check[1]

                    # 第5层: 基本有效性检查 (0.02秒)
                    validity_check = self._check_netease_cookies_validity(cookies_dict)
                    if not validity_check[0]:
                        return False, validity_check[1]

                # 检查是否是Cookie数组格式 [{"name": "...", "value": "...", ...}]
                elif isinstance(data, list):
                    if len(data) == 0:
                        return False, "Cookie数组为空"

                    # 第4层: 关键Cookie检查 (0.03秒) - 数组格式
                    essential_check = self._check_essential_netease_cookies(data)
                    if not essential_check[0]:
                        return False, essential_check[1]

                    # 第5层: 过期时间检查 (0.02秒)
                    expiry_check = self._check_cookie_expiry_fast(data)
                    if not expiry_check[0]:
                        return False, expiry_check[1]

                    # 第6层: 域名匹配检查 (0.01秒)
                    domain_check = self._check_netease_domain_match(data)
                    if not domain_check[0]:
                        return False, domain_check[1]

                # 检查是否是Cookie字符串格式
                elif isinstance(data, str) and '=' in data:
                    return True, "Cookie字符串格式，跳过详细检测"
                else:
                    return False, "Cookie数据格式不支持"

            except json.JSONDecodeError:
                # 如果不是JSON，可能是cookie字符串格式
                if '=' in content and len(content) > 10:
                    return True, "TXT格式Cookie字符串，跳过详细检测"
                else:
                    return False, "文件内容格式无效"

            elapsed = time.time() - start_time
            return True, f"Cookie预检测通过 (耗时: {elapsed:.3f}秒)"

        except Exception as e:
            elapsed = time.time() - start_time
            return False, f"预检测异常: {str(e)} (耗时: {elapsed:.3f}秒)"

    def _check_essential_netease_cookies(self, cookies: list) -> Tuple[bool, str]:
        """检查网易号关键Cookie"""
        try:
            # 网易号关键Cookie名称
            essential_names = [
                'ntes_nnid', 'ntes_nuid', 'ntes_kaola_ad', 'ntes_sess',
                'usertrack', 'ntes_utid', 'ntes_mail_user', 'ntes_login_usertrack',
                'ntes_passport', 'ntes_sess', 'ntes_urs_session'
            ]

            cookie_names = [cookie.get('name', '').lower() for cookie in cookies if isinstance(cookie, dict)]

            # 检查是否有基本的认证Cookie
            found_essential = []
            for name in essential_names:
                if any(name in cookie_name for cookie_name in cookie_names):
                    found_essential.append(name)

            if len(found_essential) < 1:  # 至少需要1个关键Cookie
                return False, f"缺少关键认证Cookie，仅找到: {found_essential}"

            # 检查Cookie基本格式
            valid_cookies = 0
            for cookie in cookies:
                if isinstance(cookie, dict) and cookie.get('name') and cookie.get('value'):
                    valid_cookies += 1

            if valid_cookies < 2:  # 至少需要2个有效Cookie
                return False, f"有效Cookie数量不足: {valid_cookies}/2"

            return True, f"关键Cookie检查通过，找到 {len(found_essential)} 个关键Cookie"

        except Exception as e:
            return False, f"关键Cookie检查异常: {str(e)}"

    def _check_cookie_expiry_fast(self, cookies: list) -> Tuple[bool, str]:
        """快速检查Cookie过期时间"""
        try:
            current_time = time.time()
            total_cookies = 0
            expired_cookies = 0
            valid_cookies = 0

            for cookie in cookies:
                if not isinstance(cookie, dict):
                    continue

                total_cookies += 1

                if 'expiry' in cookie:
                    try:
                        expiry_time = int(float(cookie['expiry']))
                        if expiry_time < current_time:
                            expired_cookies += 1
                        else:
                            valid_cookies += 1
                    except (ValueError, TypeError):
                        valid_cookies += 1  # 无法解析的当作有效
                else:
                    valid_cookies += 1  # 会话Cookie当作有效

            # 如果所有有过期时间的Cookie都过期了，判定为失效
            if expired_cookies > 0 and valid_cookies == 0:
                return False, f"所有Cookie已过期 ({expired_cookies}个)"

            # 如果过期Cookie太多，也可能有问题
            if total_cookies > 0 and expired_cookies / total_cookies > 0.8:
                return False, f"大部分Cookie已过期 ({expired_cookies}/{total_cookies})"

            return True, f"过期检查通过 (有效: {valid_cookies}, 过期: {expired_cookies})"

        except Exception as e:
            return False, f"过期检查异常: {str(e)}"

    def _check_netease_domain_match(self, cookies: list) -> Tuple[bool, str]:
        """检查网易号域名匹配"""
        try:
            # 网易相关域名
            valid_domains = [
                '.163.com', '163.com', '.mp.163.com', 'mp.163.com',
                '.netease.com', 'netease.com', '.126.com', '126.com'
            ]

            netease_cookies = 0
            total_cookies = 0

            for cookie in cookies:
                if not isinstance(cookie, dict):
                    continue

                total_cookies += 1
                domain = cookie.get('domain', '')

                if any(valid_domain in domain for valid_domain in valid_domains):
                    netease_cookies += 1

            if total_cookies == 0:
                return False, "没有找到任何Cookie"

            # 至少30%的Cookie应该是网易相关的
            if netease_cookies / total_cookies < 0.2:
                return False, f"网易相关Cookie比例过低 ({netease_cookies}/{total_cookies})"

            return True, f"域名检查通过 (网易Cookie: {netease_cookies}/{total_cookies})"

        except Exception as e:
            return False, f"域名检查异常: {str(e)}"

    def _check_essential_netease_cookies_dict(self, cookies_dict: dict) -> Tuple[bool, str]:
        """检查网易号关键Cookie（字典格式）"""
        try:
            # 网易号关键Cookie名称
            essential_names = [
                'ntes_nnid', 'ntes_nuid', '_ntes_nuid', 'ntes_kaola_ad', 'ntes_sess',
                'usertrack', 'ntes_utid', 'ntes_mail_user', 'ntes_login_usertrack',
                'ntes_passport', 'ntes_urs_session', 'p_info', 's_info',
                'ntes_p_utid', 'ntes_sess'
            ]

            cookie_names = [name.lower() for name in cookies_dict.keys()]

            # 检查是否有基本的认证Cookie
            found_essential = []
            for name in essential_names:
                if any(name in cookie_name for cookie_name in cookie_names):
                    found_essential.append(name)

            if len(found_essential) < 1:  # 至少需要1个关键Cookie
                return False, f"缺少关键认证Cookie，仅找到: {found_essential}"

            # 检查Cookie值是否有效
            valid_cookies = 0
            for name, value in cookies_dict.items():
                if name and value and len(str(value)) > 5:  # 基本的值检查
                    valid_cookies += 1

            if valid_cookies < 2:  # 至少需要2个有效Cookie
                return False, f"有效Cookie数量不足: {valid_cookies}/2"

            return True, f"关键Cookie检查通过，找到 {len(found_essential)} 个关键Cookie"

        except Exception as e:
            return False, f"关键Cookie检查异常: {str(e)}"

    def _check_netease_cookies_validity(self, cookies_dict: dict) -> Tuple[bool, str]:
        """检查网易号Cookie有效性（字典格式）"""
        try:
            # 检查一些关键Cookie的值格式
            critical_cookies = ['ntes_passport', 'ntes_sess', '_ntes_nuid']

            found_critical = 0
            for cookie_name in critical_cookies:
                # 检查完全匹配和部分匹配
                for actual_name in cookies_dict.keys():
                    if cookie_name.lower() in actual_name.lower():
                        value = cookies_dict[actual_name]
                        if value and len(str(value)) >= 10:
                            found_critical += 1
                        break

            if found_critical == 0:
                return False, "未找到有效的关键Cookie"

            # 检查是否有明显的过期标识
            for name, value in cookies_dict.items():
                if 'expired' in str(value).lower() or 'invalid' in str(value).lower():
                    return False, f"发现过期标识: {name}"

            return True, f"Cookie有效性检查通过 (关键Cookie: {found_critical}个)"

        except Exception as e:
            return False, f"有效性检查异常: {str(e)}"

    def _verify_login_fast(self, driver: webdriver.Chrome) -> bool:
        """
        🚀 快速登录验证 - 减少等待时间，提高检测效率

        Args:
            driver: 浏览器驱动

        Returns:
            是否已登录
        """
        try:
            self.log("开始快速登录验证...")

            # 🚀 快速检测参数：减少总等待时间
            max_wait_time = 15  # 从30秒减少到15秒
            check_interval = 1  # 从2秒减少到1秒
            checks_count = max_wait_time // check_interval

            # 获取初始URL
            initial_url = driver.current_url
            self.log(f"初始URL: {initial_url}")

            # 🚀 智能检测：前几次快速检测，后面逐渐放慢
            for i in range(checks_count):
                try:
                    # 🚀 动态等待间隔：前5次快速检测，后面正常间隔
                    if i < 5:
                        time.sleep(0.5)  # 前5次每0.5秒检测一次
                    else:
                        time.sleep(check_interval)  # 后面每1秒检测一次

                    current_url = driver.current_url
                    check_num = i + 1

                    # 🚀 减少日志输出频率，只在关键时刻输出
                    if check_num <= 5 or check_num % 3 == 0:  # 前5次或每3次输出一次
                        elapsed_time = (0.5 * min(i + 1, 5)) + max(0, (i + 1 - 5) * check_interval)
                        self.log(f"快速检测第{check_num}次 ({elapsed_time:.1f}秒): {current_url}")

                    # 检查是否已经进入后台页面
                    if self._is_backend_url(current_url):
                        elapsed_time = (0.5 * min(i + 1, 5)) + max(0, (i + 1 - 5) * check_interval)
                        self.log(f"✅ 快速登录成功！已进入后台 (第{check_num}次检测，耗时{elapsed_time:.1f}秒)")
                        return True

                    # 🚀 早期URL变化检测：如果URL快速变化，可能是在跳转过程中
                    if current_url != initial_url:
                        if self._is_backend_url(current_url):
                            self.log(f"✅ 快速登录成功！URL已跳转到后台")
                            return True
                        elif i < 8:  # 前8次检测中如果URL变化，给更多时间等待跳转完成
                            continue

                    # 🚀 提前失败检测：如果长时间停留在登录页面，提前结束
                    if i > 8 and "login" in current_url.lower():
                        self.log(f"❌ 快速验证失败：长时间停留在登录页面")
                        return False

                except Exception as e:
                    self.log(f"第{check_num}次快速检测时出错: {str(e)}")
                    continue

            # 最终检测
            final_url = driver.current_url
            self.log(f"快速验证结束，最终URL: {final_url}")

            if self._is_backend_url(final_url):
                self.log("✅ 最终快速检测：登录成功")
                return True
            else:
                self.log("❌ 最终快速检测：登录失败")
                return False

        except Exception as e:
            self.log(f"快速验证过程中发生错误: {str(e)}")
            return False
