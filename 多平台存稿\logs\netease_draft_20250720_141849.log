[2025-07-20 14:18:53] [INFO] 🚀 启动多平台存稿工具...
[2025-07-20 14:18:53] [INFO] 已从toutiao平台加载存稿详情数据，共 83 个账号
[2025-07-20 14:18:54] [INFO] 🔍 检测到旧版定时任务配置，开始自动迁移...
[2025-07-20 14:18:54] [INFO] 🚀 开始完整的配置迁移流程...
[2025-07-20 14:18:54] [INFO] ✅ 已备份旧版配置: C:\Users\<USER>\Downloads\网易\多平台存稿\timer_config.json.backup_20250720_141854
[2025-07-20 14:18:54] [INFO] 🔄 开始迁移旧版定时任务配置...
[2025-07-20 14:18:54] [INFO] ✅ 成功加载旧版配置: C:\Users\<USER>\Downloads\网易\多平台存稿\timer_config.json
[2025-07-20 14:18:54] [INFO] ❌ 无法解析旧版时间格式: 2025-07-21T10:28:00
[2025-07-20 14:18:54] [INFO] ℹ️ 没有需要迁移的任务配置
[2025-07-20 14:18:54] [INFO] 🎉 配置迁移完成！
[2025-07-20 14:18:54] [INFO] 💡 请在主界面点击 '⏰ 定时任务' 标签页查看迁移的任务
[2025-07-20 14:18:54] [INFO] 已加载 0 个任务到缓存
[2025-07-20 14:18:54] [INFO] ✅ 已注册 toutiao 平台查询函数
[2025-07-20 14:18:54] [INFO] ✅ 已注册 netease 平台查询函数
[2025-07-20 14:18:54] [INFO] ✅ 已注册 dayu 平台查询函数
[2025-07-20 14:18:54] [INFO] ✅ 已注册所有平台查询函数
[2025-07-20 14:18:54] [INFO] ✅ 已重建任务队列，包含 0 个任务
[2025-07-20 14:18:54] [INFO] 🚀 任务调度器已启动
[2025-07-20 14:18:54] [INFO] 📅 调度器主循环已启动
[2025-07-20 14:18:54] [INFO] ✅ 定时任务管理界面已初始化
[2025-07-20 14:18:54] [INFO] ✅ 定时任务功能说明已加载
[2025-07-20 14:18:54] [INFO] 已确保所有必要目录存在
[2025-07-20 14:18:55] [INFO] 🔍 [调试] 当前平台: toutiao
[2025-07-20 14:18:55] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-07-20 14:18:55] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-07-20 14:18:55] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-20 14:18:55] [INFO] 🔍 [调试] 更新账号树 - 当前平台: toutiao
[2025-07-20 14:18:55] [INFO] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-20 14:18:55] [INFO] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-07-20 14:18:55] [INFO] 已加载账号数据: 85 条记录
[2025-07-20 14:18:55] [INFO] 🔍 [调试] 账号数据为空或未加载
[2025-07-20 14:18:55] [INFO] ✅ 初始UI更新完成
[2025-07-20 14:18:56] [INFO] ⚠️ 获取新版定时任务状态失败: 'TaskManagerUI' object has no attribute 'get_scheduler_status'
[2025-07-20 14:18:56] [INFO] 🔍 检测到旧版定时任务配置文件
[2025-07-20 14:18:56] [INFO] 💡 建议: 点击 '⏰ 定时任务' 标签页，系统将自动迁移您的配置
[2025-07-20 14:18:58] [INFO] ❌ 打开定时任务管理界面失败: TaskManagerUI.__init__() got an unexpected keyword argument 'callback'
[2025-07-20 14:18:58] [INFO] 📋 定时任务管理窗口已置于前台
[2025-07-20 14:19:08] [INFO] ❌ 打开定时任务管理界面失败: TaskManagerUI.__init__() got an unexpected keyword argument 'callback'
