"""
验证打包结果脚本
"""

import os
from pathlib import Path

def check_package_result():
    """检查打包结果"""
    print("=" * 60)
    print("验证多平台存稿工具打包结果")
    print("=" * 60)
    
    target_dir = Path(r"D:\多平台存稿")
    
    if not target_dir.exists():
        print("❌ 目标目录不存在: D:\多平台存稿")
        return False
    
    print(f"✅ 目标目录存在: {target_dir}")
    print("\n📁 目录内容:")
    
    try:
        items = list(target_dir.iterdir())
        items.sort()
        
        for item in items:
            if item.is_file():
                size = item.stat().st_size
                size_mb = size / (1024 * 1024)
                print(f"  📄 {item.name} ({size_mb:.1f} MB)")
            elif item.is_dir():
                try:
                    file_count = len(list(item.iterdir()))
                    print(f"  📁 {item.name}/ ({file_count} 项)")
                except:
                    print(f"  📁 {item.name}/")
        
        # 检查关键文件
        print("\n🔍 关键文件检查:")
        
        key_files = [
            "多平台存稿工具.exe",
            "启动多平台存稿工具.bat",
            "config.json",
            "directory_config.json"
        ]
        
        all_exist = True
        for file_name in key_files:
            file_path = target_dir / file_name
            if file_path.exists():
                print(f"  ✅ {file_name}")
            else:
                print(f"  ❌ {file_name} (缺失)")
                all_exist = False
        
        # 检查关键目录
        print("\n📂 关键目录检查:")
        
        key_dirs = [
            "网易号存稿",
            "视频处理工具",
            "_internal"
        ]
        
        for dir_name in key_dirs:
            dir_path = target_dir / dir_name
            internal_path = target_dir / "_internal" / dir_name

            if dir_path.exists():
                print(f"  ✅ {dir_name}/ (在主目录)")
            elif internal_path.exists():
                print(f"  ✅ {dir_name}/ (在_internal目录)")
            else:
                print(f"  ❌ {dir_name}/ (缺失)")
                all_exist = False

        # 额外检查_internal目录内容
        internal_dir = target_dir / "_internal"
        if internal_dir.exists():
            print(f"\n🔍 _internal目录内容检查:")
            try:
                internal_items = list(internal_dir.iterdir())
                found_important = []
                for item in internal_items:
                    if item.name in ["网易号存稿", "视频处理工具", "config", "logs"]:
                        if item.is_dir():
                            file_count = len(list(item.iterdir()))
                            print(f"  ✅ 找到: {item.name}/ ({file_count} 项)")
                            found_important.append(item.name)
                        else:
                            print(f"  ✅ 找到: {item.name}")
                            found_important.append(item.name)

                if "网易号存稿" in found_important and "视频处理工具" in found_important:
                    print("  🎉 关键程序模块都已正确打包到_internal目录！")
                    all_exist = True  # 重新设置为True，因为文件在_internal中

            except Exception as e:
                print(f"  ❌ 检查_internal目录出错: {e}")
        
        print("\n" + "=" * 60)
        if all_exist:
            print("🎉 打包验证成功！所有关键文件和目录都存在")
            print(f"📍 程序位置: {target_dir}")
            print("🚀 您可以运行以下文件启动程序:")
            print(f"   • {target_dir / '多平台存稿工具.exe'}")
            print(f"   • {target_dir / '启动多平台存稿工具.bat'} (推荐)")
        else:
            print("⚠️ 打包验证发现问题，部分文件或目录缺失")
        
        print("=" * 60)
        return all_exist
        
    except Exception as e:
        print(f"❌ 检查过程出错: {e}")
        return False

if __name__ == "__main__":
    success = check_package_result()
    
    if success:
        print("\n✨ 恭喜！打包完全成功！")
        
        # 询问是否启动程序
        try:
            choice = input("\n是否现在启动打包好的程序？(y/n): ").lower().strip()
            if choice in ['y', 'yes', '是', '']:
                exe_path = Path(r"D:\多平台存稿\多平台存稿工具.exe")
                if exe_path.exists():
                    print("🚀 正在启动程序...")
                    os.startfile(str(exe_path))
                    print("✅ 程序已启动！")
                else:
                    print("❌ 找不到可执行文件")
        except KeyboardInterrupt:
            print("\n👋 再见！")
    else:
        print("\n❌ 打包验证失败，请检查打包过程")
    
    input("\n按回车键退出...")
