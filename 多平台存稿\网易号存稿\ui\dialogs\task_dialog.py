"""
定时任务编辑对话框
用于创建和编辑定时任务
"""

import tkinter as tk
from tkinter import ttk, messagebox
import datetime
from typing import Optional, Callable, List

from 网易号存稿.common.scheduler.manager import AdvancedSchedulerManager
from 网易号存稿.common.scheduler.task_model import ScheduledTask, TaskType, RepeatRule, RepeatType
from 网易号存稿.common.scheduler.time_utils import TimeUtils

# 导入美化工具
try:
    from 网易号存稿.ui.styles.checkbox_styles import create_beautiful_checkbox, get_checkbox_style
    from 网易号存稿.ui.styles.theme import ModernTheme
    BEAUTIFY_AVAILABLE = True
except ImportError:
    BEAUTIFY_AVAILABLE = False


class TaskDialog:
    """定时任务编辑对话框"""
    
    def __init__(self, 
                 parent,
                 scheduler_manager: AdvancedSchedulerManager,
                 task: Optional[ScheduledTask] = None,
                 callback: Optional[Callable] = None):
        """
        初始化任务对话框
        
        Args:
            parent: 父窗口
            scheduler_manager: 调度管理器
            task: 要编辑的任务（None表示新建）
            callback: 完成后的回调函数
        """
        self.parent = parent
        self.scheduler_manager = scheduler_manager
        self.task = task
        self.callback = callback
        self.is_edit_mode = task is not None
        
        # 创建对话框
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("📝 编辑任务" if self.is_edit_mode else "➕ 新建定时任务")
        self.dialog.resizable(True, True)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 应用现代化主题
        if BEAUTIFY_AVAILABLE:
            try:
                ModernTheme.apply(self.dialog, "light")
            except:
                pass

        # 设置窗口大小并居中显示
        self._center_window_with_size(580, 650)
        
        # 初始化变量
        self._init_variables()
        
        # 创建UI
        self._create_ui()
        
        # 如果是编辑模式，加载任务数据
        if self.is_edit_mode:
            self._load_task_data()
    
    def _center_window_with_size(self, width, height):
        """设置窗口大小并居中显示"""
        # 获取屏幕尺寸
        screen_width = self.dialog.winfo_screenwidth()
        screen_height = self.dialog.winfo_screenheight()

        # 计算居中位置
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2

        # 设置窗口大小和位置
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")

        # 确保窗口更新
        self.dialog.update_idletasks()
    
    def _init_variables(self):
        """初始化变量"""
        # 基本信息
        self.name_var = tk.StringVar(value="")
        self.description_var = tk.StringVar(value="")

        # 任务类型（将作为任务名称）
        self.task_type_var = tk.StringVar(value="data_query_all")
        self.task_type_display_var = tk.StringVar(value="数据查询(全部账号)")

        # 平台和账号
        self.platform_var = tk.StringVar(value="netease")
        self.platform_display_var = tk.StringVar(value="网易号")
        self.target_accounts_var = tk.StringVar(value="all")

        # 时间设置
        self.schedule_date_var = tk.StringVar()
        self.schedule_time_var = tk.StringVar(value="08:00:00")

        # 重复设置
        self.repeat_type_var = tk.StringVar(value="once")
        self.repeat_type_display_var = tk.StringVar(value="一次性任务")
        self.repeat_interval_var = tk.IntVar(value=1)

        # 设置默认日期为明天
        tomorrow = TimeUtils.get_beijing_now() + datetime.timedelta(days=1)
        self.schedule_date_var.set(tomorrow.strftime("%Y-%m-%d"))
    
    def _create_ui(self):
        """创建用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 创建可滚动区域（无滚动条）
        canvas = tk.Canvas(main_frame, highlightthickness=0)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")

        # 创建各个部分（删除基本信息部分）
        self._create_task_type_section(scrollable_frame)
        self._create_platform_section(scrollable_frame)
        self._create_time_section(scrollable_frame)
        self._create_repeat_section(scrollable_frame)
        self._create_button_section(scrollable_frame)

        # 布局画布（无滚动条）
        canvas.pack(fill="both", expand=True)

        # 绑定鼠标滚轮（保留滚轮滑动功能）
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        # 绑定滚轮事件到画布和所有子组件
        def bind_mousewheel(widget):
            widget.bind("<MouseWheel>", _on_mousewheel)
            for child in widget.winfo_children():
                bind_mousewheel(child)

        bind_mousewheel(self.dialog)
    

    
    def _create_task_type_section(self, parent):
        """创建任务类型部分"""
        # 创建美化的框架
        frame = ttk.LabelFrame(parent, text="📋 任务类型", padding=15)
        frame.pack(fill=tk.X, pady=(0, 15))

        # 应用美化样式
        if BEAUTIFY_AVAILABLE:
            try:
                ModernTheme.apply_frame_style(frame)
            except:
                pass

        # 创建任务类型映射
        self.task_type_options = {
            "📊 数据查询(全部账号)": "data_query_all",
            "👤 数据查询(单个账号)": "data_query_single",
            "📝 存稿任务": "draft_upload",
            "🔄 组合任务(查询+存稿)": "combined"
        }

        # 使用美化的单选按钮组
        self.task_type_buttons = {}
        selected_var = tk.StringVar(value="📊 数据查询(全部账号)")

        for i, (display_name, value) in enumerate(self.task_type_options.items()):
            if BEAUTIFY_AVAILABLE:
                try:
                    btn = create_beautiful_checkbox(frame, display_name, selected_var,
                                                  value=display_name, mode="radio")
                except:
                    btn = ttk.Radiobutton(frame, text=display_name, variable=selected_var,
                                        value=display_name, style="Modern.TRadiobutton")
            else:
                btn = ttk.Radiobutton(frame, text=display_name, variable=selected_var,
                                    value=display_name)

            btn.grid(row=i//2, column=i%2, sticky=tk.W, padx=10, pady=8)
            self.task_type_buttons[display_name] = btn

        # 保存选择变量
        self.task_type_display_var = selected_var

        # 绑定变化事件
        selected_var.trace('w', self._on_task_type_changed)

        # 配置列权重
        frame.columnconfigure(0, weight=1)
        frame.columnconfigure(1, weight=1)
    
    def _create_platform_section(self, parent):
        """创建平台选择部分"""
        frame = ttk.LabelFrame(parent, text="🌐 平台设置", padding=15)
        frame.pack(fill=tk.X, pady=(0, 15))

        # 应用美化样式
        if BEAUTIFY_AVAILABLE:
            try:
                ModernTheme.apply_frame_style(frame)
            except:
                pass

        # 创建平台映射
        self.platform_options = {
            "📰 网易号": "netease",
            "📱 头条号": "toutiao",
            "🐟 大鱼号": "dayu"
        }

        # 使用美化的单选按钮组
        self.platform_buttons = {}
        platform_var = tk.StringVar(value="📰 网易号")

        for i, (display_name, value) in enumerate(self.platform_options.items()):
            if BEAUTIFY_AVAILABLE:
                try:
                    btn = create_beautiful_checkbox(frame, display_name, platform_var,
                                                  value=display_name, mode="radio")
                except:
                    btn = ttk.Radiobutton(frame, text=display_name, variable=platform_var,
                                        value=display_name, style="Modern.TRadiobutton")
            else:
                btn = ttk.Radiobutton(frame, text=display_name, variable=platform_var,
                                    value=display_name)

            btn.grid(row=0, column=i, sticky=tk.W, padx=15, pady=8)
            self.platform_buttons[display_name] = btn

        # 保存选择变量
        self.platform_display_var = platform_var

        # 目标账号选择
        accounts_label = ttk.Label(frame, text="🎯 目标账号:", font=("微软雅黑", 10, "bold"))
        accounts_label.grid(row=1, column=0, columnspan=3, sticky=tk.W, padx=5, pady=(15, 5))

        # 账号选择框架
        self.accounts_frame = ttk.Frame(frame)
        self.accounts_frame.grid(row=2, column=0, columnspan=3, sticky=tk.W+tk.E, padx=5, pady=5)

        # 全部账号选项（美化复选框）
        self.all_accounts_var = tk.BooleanVar(value=True)
        if BEAUTIFY_AVAILABLE:
            try:
                all_accounts_cb = create_beautiful_checkbox(self.accounts_frame, "📋 全部账号",
                                                          self.all_accounts_var, command=self._on_all_accounts_changed)
            except:
                all_accounts_cb = ttk.Checkbutton(self.accounts_frame, text="📋 全部账号",
                                                variable=self.all_accounts_var, command=self._on_all_accounts_changed,
                                                style="Modern.TCheckbutton")
        else:
            all_accounts_cb = ttk.Checkbutton(self.accounts_frame, text="📋 全部账号",
                                            variable=self.all_accounts_var, command=self._on_all_accounts_changed)
        all_accounts_cb.pack(side=tk.LEFT, padx=(0, 20))

        # 特定账号输入
        specific_label = ttk.Label(self.accounts_frame, text="👤 指定账号:", font=("微软雅黑", 9))
        specific_label.pack(side=tk.LEFT, padx=(0, 5))

        self.specific_accounts_entry = ttk.Entry(self.accounts_frame, textvariable=self.target_accounts_var,
                                               width=25, font=("微软雅黑", 9))
        self.specific_accounts_entry.pack(side=tk.LEFT)
        self.specific_accounts_entry.config(state="disabled")

        # 配置列权重
        frame.columnconfigure(0, weight=1)
        frame.columnconfigure(1, weight=1)
        frame.columnconfigure(2, weight=1)
    
    def _create_time_section(self, parent):
        """创建时间设置部分"""
        frame = ttk.LabelFrame(parent, text="⏰ 时间设置", padding=15)
        frame.pack(fill=tk.X, pady=(0, 15))

        # 应用美化样式
        if BEAUTIFY_AVAILABLE:
            try:
                ModernTheme.apply_frame_style(frame)
            except:
                pass

        # 创建时间输入网格
        time_grid = ttk.Frame(frame)
        time_grid.pack(fill=tk.X, pady=5)

        # 执行日期
        date_label = ttk.Label(time_grid, text="📅 执行日期:", font=("微软雅黑", 10, "bold"))
        date_label.grid(row=0, column=0, sticky=tk.W, padx=5, pady=8)

        date_entry = ttk.Entry(time_grid, textvariable=self.schedule_date_var, width=18,
                              font=("微软雅黑", 10), justify='center')
        date_entry.grid(row=0, column=1, sticky=tk.W, padx=10, pady=8)

        date_hint = ttk.Label(time_grid, text="格式: 2025-07-21", foreground="#666666", font=("微软雅黑", 8))
        date_hint.grid(row=0, column=2, sticky=tk.W, padx=5, pady=8)

        # 执行时间
        time_label = ttk.Label(time_grid, text="🕐 执行时间:", font=("微软雅黑", 10, "bold"))
        time_label.grid(row=1, column=0, sticky=tk.W, padx=5, pady=8)

        time_entry = ttk.Entry(time_grid, textvariable=self.schedule_time_var, width=18,
                              font=("微软雅黑", 10), justify='center')
        time_entry.grid(row=1, column=1, sticky=tk.W, padx=10, pady=8)

        time_hint = ttk.Label(time_grid, text="格式: 08:30:00", foreground="#666666", font=("微软雅黑", 8))
        time_hint.grid(row=1, column=2, sticky=tk.W, padx=5, pady=8)

        # 快捷按钮区域
        button_frame = ttk.Frame(frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        # 当前时间按钮（美化）
        if BEAUTIFY_AVAILABLE:
            try:
                current_time_btn = ttk.Button(button_frame, text="📍 设为当前时间", command=self._set_current_time,
                                            style="Modern.TButton")
            except:
                current_time_btn = ttk.Button(button_frame, text="📍 设为当前时间", command=self._set_current_time)
        else:
            current_time_btn = ttk.Button(button_frame, text="📍 设为当前时间", command=self._set_current_time)

        current_time_btn.pack(side=tk.LEFT, padx=5)

        # 明天按钮
        tomorrow_btn = ttk.Button(button_frame, text="📆 明天同一时间", command=self._set_tomorrow_time)
        tomorrow_btn.pack(side=tk.LEFT, padx=5)
    
    def _create_repeat_section(self, parent):
        """创建重复设置部分"""
        frame = ttk.LabelFrame(parent, text="🔄 重复设置", padding=15)
        frame.pack(fill=tk.X, pady=(0, 15))

        # 应用美化样式
        if BEAUTIFY_AVAILABLE:
            try:
                ModernTheme.apply_frame_style(frame)
            except:
                pass

        # 创建重复类型映射
        self.repeat_type_options = {
            "⚡ 一次性任务": "once",
            "📅 每日重复": "daily",
            "📆 每周重复": "weekly",
            "🗓️ 每月重复": "monthly"
        }

        # 使用美化的单选按钮组
        self.repeat_type_buttons = {}
        repeat_var = tk.StringVar(value="⚡ 一次性任务")

        for i, (display_name, value) in enumerate(self.repeat_type_options.items()):
            if BEAUTIFY_AVAILABLE:
                try:
                    btn = create_beautiful_checkbox(frame, display_name, repeat_var,
                                                  value=display_name, mode="radio")
                except:
                    btn = ttk.Radiobutton(frame, text=display_name, variable=repeat_var,
                                        value=display_name, style="Modern.TRadiobutton")
            else:
                btn = ttk.Radiobutton(frame, text=display_name, variable=repeat_var,
                                    value=display_name)

            btn.grid(row=i//2, column=i%2, sticky=tk.W, padx=15, pady=8)
            self.repeat_type_buttons[display_name] = btn

        # 保存选择变量
        self.repeat_type_display_var = repeat_var

        # 重复间隔设置
        interval_frame = ttk.Frame(frame)
        interval_frame.grid(row=2, column=0, columnspan=2, sticky=tk.W+tk.E, pady=(15, 5))

        interval_label = ttk.Label(interval_frame, text="📊 重复间隔:", font=("微软雅黑", 10, "bold"))
        interval_label.pack(side=tk.LEFT, padx=(0, 10))

        interval_spin = ttk.Spinbox(interval_frame, from_=1, to=365, textvariable=self.repeat_interval_var,
                                   width=8, font=("微软雅黑", 10), justify='center')
        interval_spin.pack(side=tk.LEFT, padx=(0, 10))

        interval_hint = ttk.Label(interval_frame, text="数值越大间隔越长", foreground="#666666", font=("微软雅黑", 8))
        interval_hint.pack(side=tk.LEFT)

        # 添加说明
        desc_label = ttk.Label(frame, text="💡 提示：一次性任务执行完成后会自动删除，重复任务会按设定间隔持续执行",
                              foreground="#666666", font=("微软雅黑", 9), wraplength=500)
        desc_label.grid(row=3, column=0, columnspan=2, sticky=tk.W, padx=5, pady=(10, 0))

        # 绑定重复类型变化事件
        repeat_var.trace('w', self._on_repeat_type_changed)

        # 配置列权重
        frame.columnconfigure(0, weight=1)
        frame.columnconfigure(1, weight=1)
    
    def _create_button_section(self, parent):
        """创建按钮部分"""
        frame = ttk.Frame(parent)
        frame.pack(fill=tk.X, pady=20)

        # 美化分隔线
        separator = ttk.Separator(frame, orient='horizontal')
        separator.pack(fill=tk.X, pady=(0, 15))

        # 按钮容器
        button_container = ttk.Frame(frame)
        button_container.pack(fill=tk.X)

        # 左侧信息
        info_frame = ttk.Frame(button_container)
        info_frame.pack(side=tk.LEFT)

        info_label = ttk.Label(info_frame, text="💡 任务将根据设定时间自动执行",
                              foreground="#666666", font=("微软雅黑", 9))
        info_label.pack(side=tk.LEFT)

        # 右侧按钮
        button_frame = ttk.Frame(button_container)
        button_frame.pack(side=tk.RIGHT)

        # 测试按钮（编辑模式）
        if self.is_edit_mode:
            if BEAUTIFY_AVAILABLE:
                try:
                    test_btn = ttk.Button(button_frame, text="🧪 测试执行", command=self._on_test_execute,
                                        style="Secondary.TButton")
                except:
                    test_btn = ttk.Button(button_frame, text="🧪 测试执行", command=self._on_test_execute)
            else:
                test_btn = ttk.Button(button_frame, text="🧪 测试执行", command=self._on_test_execute)
            test_btn.pack(side=tk.LEFT, padx=8)

        # 取消按钮（美化）
        if BEAUTIFY_AVAILABLE:
            try:
                cancel_btn = ttk.Button(button_frame, text="❌ 取消", command=self._on_cancel,
                                      style="Danger.TButton")
            except:
                cancel_btn = ttk.Button(button_frame, text="❌ 取消", command=self._on_cancel)
        else:
            cancel_btn = ttk.Button(button_frame, text="❌ 取消", command=self._on_cancel)
        cancel_btn.pack(side=tk.LEFT, padx=8)

        # 确定按钮（美化）
        ok_text = "💾 保存修改" if self.is_edit_mode else "✅ 创建任务"
        if BEAUTIFY_AVAILABLE:
            try:
                ok_btn = ttk.Button(button_frame, text=ok_text, command=self._on_ok,
                                  style="Success.TButton")
            except:
                ok_btn = ttk.Button(button_frame, text=ok_text, command=self._on_ok)
        else:
            ok_btn = ttk.Button(button_frame, text=ok_text, command=self._on_ok)
        ok_btn.pack(side=tk.LEFT, padx=8)

        # 设置确定按钮为默认按钮
        self.dialog.bind('<Return>', lambda e: self._on_ok())

        # 设置焦点到确定按钮
        ok_btn.focus_set()

    def _set_current_time(self):
        """设置当前时间"""
        try:
            import datetime
            now = datetime.datetime.now()
            # 设置为5分钟后，给用户一些准备时间
            future_time = now + datetime.timedelta(minutes=5)

            self.schedule_date_var.set(future_time.strftime("%Y-%m-%d"))
            self.schedule_time_var.set(future_time.strftime("%H:%M:%S"))
        except Exception as e:
            print(f"设置当前时间失败: {e}")

    def _set_tomorrow_time(self):
        """设置明天同一时间"""
        try:
            import datetime
            now = datetime.datetime.now()
            # 设置为明天的同一时间
            tomorrow = now + datetime.timedelta(days=1)

            self.schedule_date_var.set(tomorrow.strftime("%Y-%m-%d"))
            self.schedule_time_var.set(tomorrow.strftime("%H:%M:%S"))
        except Exception as e:
            print(f"设置明天时间失败: {e}")

    def _on_task_type_changed(self, *args):
        """任务类型变化事件"""
        display_type = self.task_type_display_var.get()

        # 更新实际的任务类型变量
        if hasattr(self, 'task_type_options'):
            actual_type = self.task_type_options.get(display_type, "data_query_all")
            self.task_type_var.set(actual_type)

        # 根据任务类型调整界面
        if "单个账号" in display_type:
            self.all_accounts_var.set(False)
            self._on_all_accounts_changed()

    def _on_all_accounts_changed(self):
        """全部账号选项变化事件"""
        if self.all_accounts_var.get():
            self.specific_accounts_entry.config(state="disabled")
            self.target_accounts_var.set("all")
        else:
            self.specific_accounts_entry.config(state="normal")
            self.target_accounts_var.set("")

    def _on_repeat_type_changed(self, *args):
        """重复类型变化事件"""
        display_type = self.repeat_type_display_var.get()

        # 更新实际的重复类型变量
        if hasattr(self, 'repeat_type_options'):
            actual_type = self.repeat_type_options.get(display_type, "once")
            self.repeat_type_var.set(actual_type)

    def _load_task_data(self):
        """加载任务数据（编辑模式）"""
        if not self.task:
            return

        # 加载基本信息
        self.name_var.set(self.task.name)
        self.description_var.set(self.task.description)

        # 加载任务类型
        type_display_map = {
            TaskType.DATA_QUERY_ALL: "数据查询(全部账号)",
            TaskType.DATA_QUERY_SINGLE: "数据查询(单个账号)",
            TaskType.DRAFT_UPLOAD: "存稿任务",
            TaskType.COMBINED: "组合任务(查询+存稿)"
        }
        type_value_map = {
            TaskType.DATA_QUERY_ALL: "data_query_all",
            TaskType.DATA_QUERY_SINGLE: "data_query_single",
            TaskType.DRAFT_UPLOAD: "draft_upload",
            TaskType.COMBINED: "combined"
        }

        self.task_type_display_var.set(type_display_map.get(self.task.task_type, "数据查询(全部账号)"))
        self.task_type_var.set(type_value_map.get(self.task.task_type, "data_query_all"))

        # 加载平台
        platform_display_map = {
            "netease": "网易号",
            "toutiao": "头条号",
            "dayu": "大鱼号"
        }
        self.platform_display_var.set(platform_display_map.get(self.task.platform, "网易号"))
        self.platform_var.set(self.task.platform)

        # 加载目标账号
        if self.task.target_accounts == ["all"]:
            self.all_accounts_var.set(True)
            self.target_accounts_var.set("all")
        else:
            self.all_accounts_var.set(False)
            self.target_accounts_var.set(",".join(self.task.target_accounts))
        self._on_all_accounts_changed()

        # 加载时间设置
        if self.task.schedule_time:
            beijing_time = TimeUtils.to_beijing_time(self.task.schedule_time)
            self.schedule_date_var.set(beijing_time.strftime("%Y-%m-%d"))
            self.schedule_time_var.set(beijing_time.strftime("%H:%M:%S"))

        # 加载重复设置
        repeat_display_map = {
            RepeatType.ONCE: "一次性任务",
            RepeatType.DAILY: "每日重复",
            RepeatType.WEEKLY: "每周重复",
            RepeatType.MONTHLY: "每月重复"
        }
        repeat_value_map = {
            RepeatType.ONCE: "once",
            RepeatType.DAILY: "daily",
            RepeatType.WEEKLY: "weekly",
            RepeatType.MONTHLY: "monthly"
        }

        self.repeat_type_display_var.set(repeat_display_map.get(self.task.repeat_rule.type, "一次性任务"))
        self.repeat_type_var.set(repeat_value_map.get(self.task.repeat_rule.type, "once"))
        self.repeat_interval_var.set(self.task.repeat_rule.interval)

    def _validate_input(self) -> tuple[bool, str]:
        """验证输入数据"""
        # 验证任务类型（任务名称会自动生成，无需验证）
        if not self.task_type_display_var.get().strip():
            return False, "请选择任务类型"

        # 验证时间格式
        try:
            date_str = self.schedule_date_var.get()
            time_str = self.schedule_time_var.get()
            datetime_str = f"{date_str} {time_str}"

            schedule_time = TimeUtils.parse_time_string(datetime_str)
            if not schedule_time:
                return False, "时间格式不正确，请使用 YYYY-MM-DD HH:MM:SS 格式"

            # 验证时间范围
            valid, error_msg = TimeUtils.validate_time_range(schedule_time)
            if not valid:
                return False, error_msg

        except Exception as e:
            return False, f"时间格式错误: {str(e)}"

        # 验证目标账号（如果需要单个账号查询）
        task_type = self.task_type_var.get()
        if task_type == "data_query_single":
            if hasattr(self, 'all_accounts_var') and not self.all_accounts_var.get():
                accounts_str = self.target_accounts_var.get().strip()
                if not accounts_str:
                    return False, "单个账号查询需要指定目标账号"

        return True, ""

    def _get_task_data(self) -> dict:
        """获取任务数据"""
        # 解析任务类型 - 使用显示变量
        display_type = self.task_type_display_var.get()
        if hasattr(self, 'task_type_options'):
            task_type_str = self.task_type_options.get(display_type, "data_query_all")
        else:
            task_type_str = self.task_type_var.get()

        # 转换为TaskType枚举
        try:
            type_map = {
                "data_query_all": TaskType.DATA_QUERY_ALL,
                "data_query_single": TaskType.DATA_QUERY_SINGLE,
                "draft_upload": TaskType.DRAFT_UPLOAD,
                "combined": TaskType.COMBINED
            }
            task_type = type_map.get(task_type_str, TaskType.DATA_QUERY_ALL)
        except:
            # 如果转换失败，使用字符串
            task_type = task_type_str

        # 解析平台 - 使用显示变量
        display_platform = self.platform_display_var.get()
        if hasattr(self, 'platform_options'):
            platform = self.platform_options.get(display_platform, "netease")
        else:
            platform = self.platform_var.get()

        # 生成基于任务类型的任务名称
        platform_name_map = {
            "netease": "网易号",
            "toutiao": "头条号",
            "dayu": "大鱼号"
        }
        platform_name = platform_name_map.get(platform, platform)

        # 根据任务类型和平台生成名称
        type_name_map = {
            "data_query_all": f"{platform_name}全部账号数据查询",
            "data_query_single": f"{platform_name}单个账号数据查询",
            "draft_upload": f"{platform_name}存稿任务",
            "combined": f"{platform_name}组合任务"
        }

        task_name = type_name_map.get(task_type_str, f"{platform_name}定时任务")

        # 解析目标账号
        if self.all_accounts_var.get():
            target_accounts = ["all"]
        else:
            accounts_str = self.target_accounts_var.get().strip()
            target_accounts = [acc.strip() for acc in accounts_str.split(",") if acc.strip()]

        # 解析时间
        date_str = self.schedule_date_var.get()
        time_str = self.schedule_time_var.get()
        datetime_str = f"{date_str} {time_str}"
        schedule_time = TimeUtils.parse_time_string(datetime_str)

        # 解析重复规则 - 使用显示变量
        display_repeat = self.repeat_type_display_var.get()
        if hasattr(self, 'repeat_type_options'):
            repeat_type_str = self.repeat_type_options.get(display_repeat, "once")
        else:
            repeat_type_str = self.repeat_type_var.get()

        # 转换为RepeatType枚举
        try:
            repeat_map = {
                "once": RepeatType.ONCE,
                "daily": RepeatType.DAILY,
                "weekly": RepeatType.WEEKLY,
                "monthly": RepeatType.MONTHLY
            }
            repeat_type = repeat_map.get(repeat_type_str, RepeatType.ONCE)
        except:
            # 如果转换失败，使用字符串
            repeat_type = repeat_type_str

        # 创建重复规则
        try:
            repeat_rule = RepeatRule(
                type=repeat_type,
                interval=self.repeat_interval_var.get()
            )
        except:
            # 如果创建失败，创建简单的字典
            repeat_rule = {
                'type': repeat_type,
                'interval': self.repeat_interval_var.get()
            }

        # 生成任务描述
        repeat_display = self.repeat_type_display_var.get()
        schedule_date = self.schedule_date_var.get()
        schedule_time_str = self.schedule_time_var.get()

        if "一次性" in repeat_display:
            description = f"将于 {schedule_date} {schedule_time_str} 执行一次"
        else:
            description = f"从 {schedule_date} {schedule_time_str} 开始{repeat_display}"

        return {
            'name': task_name,  # 使用生成的任务名称
            'description': description,  # 使用生成的任务描述
            'task_type': task_type,
            'platform': platform,
            'target_accounts': target_accounts,
            'schedule_time': schedule_time,
            'repeat_rule': repeat_rule
        }

    def _on_ok(self):
        """确定按钮事件"""
        # 验证输入
        valid, error_msg = self._validate_input()
        if not valid:
            messagebox.showerror("输入错误", error_msg)
            return

        try:
            task_data = self._get_task_data()

            if self.is_edit_mode:
                # 更新任务
                success = self.scheduler_manager.update_task(
                    self.task.id,
                    **task_data
                )
                if success:
                    messagebox.showinfo("成功", "任务更新成功")
                else:
                    messagebox.showerror("错误", "任务更新失败")
                    return
            else:
                # 创建新任务
                task_id = self.scheduler_manager.create_task(**task_data)
                messagebox.showinfo("成功", f"任务创建成功\nID: {task_id}")

            # 调用回调函数
            if self.callback:
                self.callback()

            # 关闭对话框
            self.dialog.destroy()

        except Exception as e:
            messagebox.showerror("错误", f"操作失败: {str(e)}")

    def _on_cancel(self):
        """取消按钮事件"""
        self.dialog.destroy()

    def _on_test_execute(self):
        """测试执行按钮事件"""
        if not self.task:
            return

        result = messagebox.askyesno(
            "确认测试",
            f"确定要立即执行任务 '{self.task.name}' 进行测试吗？"
        )

        if result:
            try:
                success = self.scheduler_manager.execute_task_now(self.task.id)
                if success:
                    messagebox.showinfo("成功", "任务已提交执行，请查看日志了解执行结果")
                else:
                    messagebox.showerror("错误", "任务提交失败")
            except Exception as e:
                messagebox.showerror("错误", f"测试执行失败: {str(e)}")
