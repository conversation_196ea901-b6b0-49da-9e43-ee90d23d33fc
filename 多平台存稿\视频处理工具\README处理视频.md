# 网易视频处理工具集

## 项目概述
本项目是一套用于视频处理的工具集，主要用于网易平台的视频预处理、分析和管理。该工具集包含预处理工具和相关测试工具，帮助用户处理视频文件、提取封面、分析视频内容等。

## 功能特点
- **视频预处理**：过滤、转换和优化视频文件
- **封面提取**：自动从视频中提取封面图片
- **视频分析**：分析视频时长、分辨率等参数
- **批量处理**：支持多线程批量处理视频文件
- **兼容性测试**：检测所需依赖和环境是否正确配置

## 主要工具

### 1. 预处理工具 (`修复版预处理工具.py`)
一个图形界面应用程序，用于：
- 选择和处理视频文件夹
- 根据视频时长进行筛选
- 提取视频封面
- 多线程批量处理
- 自定义处理参数

### 2. FFmpeg集成测试工具 (`test_ffmpeg_integration.py`)
一个命令行测试工具，用于：
- 检查FFmpeg安装和配置情况
- 测试MoviePy对FFmpeg的调用
- 检查其他必要库（NetworkX, Pillow, PyTesseract）
- 验证预处理工具的FFmpeg集成
- 提供FFmpeg安装和配置建议

## 依赖项
- Python 3.11+
- FFmpeg
- MoviePy
- Pillow (PIL)
- NetworkX
- PyTesseract
- Tkinter (用于GUI)

## 安装指南

### 1. 安装Python依赖
```bash
pip install moviepy pillow networkx pytesseract
```

### 2. 安装FFmpeg
FFmpeg是处理视频的核心依赖。有以下几种安装方式：

**Windows：**
1. 下载FFmpeg：https://ffmpeg.org/download.html
2. 解压到 `ffmpeg` 文件夹（最好放在项目目录下）
3. 将FFmpeg的bin目录添加到系统PATH中

**使用测试工具检查安装：**
```bash
python test_ffmpeg_integration.py
```

## 使用方法

### 预处理工具
1. 运行 `修复版预处理工具.py`
2. 在界面中选择视频文件夹
3. 设置所需参数（如最小/最大时长）
4. 点击"开始处理"按钮

### FFmpeg测试工具
```bash
python test_ffmpeg_integration.py
```
该命令将检查：
- FFmpeg是否可用
- MoviePy配置
- 其他必要库的安装情况
- 预处理工具的集成状态

## 故障排除
如果遇到FFmpeg相关错误：
1. 确保FFmpeg已正确安装
2. 确保FFmpeg的bin目录已添加到系统PATH
3. 使用`test_ffmpeg_integration.py`进行诊断
4. 按照测试工具给出的建议修复问题

## 后续开发计划
- 增强视频内容分析功能
- 添加更多视频处理选项
- 优化多线程处理性能
- 改进用户界面体验 