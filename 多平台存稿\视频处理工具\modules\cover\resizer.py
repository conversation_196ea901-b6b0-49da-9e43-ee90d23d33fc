"""
封面调整大小模块 - 处理封面图片的大小调整
"""

import os
from typing import Dict, Tuple, Optional, Callable, Union
from PIL import Image

def resize_cover(processor,
                cover_path: str,
                width: int,
                height: int) -> bool:
    """
    调整封面大小
    
    Args:
        processor: 封面处理器实例
        cover_path: 封面图片路径
        width: 宽度
        height: 高度
        
    Returns:
        bool: 是否成功调整大小
    """
    try:
        # 打开封面图片
        cover_img = Image.open(cover_path)
        
        # 调整大小
        resized_img = cover_img.resize((width, height), Image.LANCZOS)
        
        # 保存封面
        resized_img.save(cover_path, quality=95)
        
        return True
    except Exception as e:
        processor.logger(f"调整封面大小失败: {str(e)}")
        return False
