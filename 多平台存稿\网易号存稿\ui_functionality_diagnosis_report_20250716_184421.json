{"timestamp": "2025-07-16T18:44:21.413727", "diagnosis_results": {"1. 主程序初始化检查": {"status": "success", "details": ["✅ 主UI初始化成功", "   ✅ root: Tk", "   ✅ config_manager: ConfigManager", "   ✅ current_platform: str", "   ✅ is_running: bool", "   ✅ is_querying: bool", "   ✅ data_query_running: bool", "   ✅ log_manager: 已初始化", "   ✅ settings_manager: 已初始化", "   ✅ data_query_manager: 已初始化", "   ✅ account_controller: 已初始化", "   ✅ accounts_table: 已初始化"]}, "2. 日志设置功能检查": {"status": "warning", "details": ["✅ 设置管理器已初始化", "   ⚠️ 方法 create_log_settings_panel: 不存在", "   ⚠️ 方法 create_log_level_settings: 不存在", "   ⚠️ 方法 create_log_filter_settings: 不存在", "   ⚠️ 方法 save_log_settings: 不存在", "   ⚠️ 方法 load_log_settings: 不存在", "   ✅ 变量 enable_level_filter: False", "   ✅ 变量 enable_keyword_filter: False", "   ✅ 变量 min_log_level: INFO", "   ❌ 变量 auto_scroll: 不存在", "   ❌ 变量 enhanced_display: 不存在", "   ⚠️ 无法测试日志设置面板创建"]}, "3. 统计日志功能检查": {"status": "warning", "details": ["   ⚠️ 统计属性 total_accounts: 不存在", "   ⚠️ 统计属性 successful_accounts: 不存在", "   ⚠️ 统计属性 failed_accounts: 不存在", "   ⚠️ 统计属性 total_drafts: 不存在", "   ⚠️ 统计属性 successful_drafts: 不存在", "   ⚠️ 统计属性 failed_drafts: 不存在", "   ⚠️ 统计方法 update_stats_display: 不存在", "   ✅ 统计方法 update_account_count_display: 存在", "   ⚠️ 统计方法 log_statistics: 不存在", "   ⚠️ 统计方法 generate_statistics_report: 不存在", "   ⚠️ 统计面板: 未创建", "   ⚠️ 无法测试统计日志生成"]}, "4. 进度按钮功能检查": {"status": "warning", "details": ["   ✅ 按钮 start_button: 存在", "     状态: normal", "   ✅ 按钮 stop_button: 存在", "     状态: disabled", "   ❌ 按钮 pause_button: 不存在", "   ❌ 按钮 resume_button: 不存在", "   ❌ 按钮 query_button: 不存在", "   ❌ 按钮 export_button: 不存在", "   ❌ 进度组件 progress_bar: 不存在", "   ❌ 进度组件 progress_label: 不存在", "   ✅ 进度组件 status_text: 存在", "   ❌ 进度组件 current_account_label: 不存在", "   ❌ 进度组件 progress_frame: 不存在", "   ⚠️ 进度方法 update_progress: 不存在", "   ⚠️ 进度方法 update_status: 不存在", "   ⚠️ 进度方法 reset_progress: 不存在", "   ⚠️ 进度方法 show_progress: 不存在", "   ⚠️ 进度方法 hide_progress: 不存在", "   ✅ 开始按钮文本: ▶️ 开始存稿", "   ✅ 开始按钮命令已绑定"]}, "5. 设置界面组件检查": {"status": "warning", "details": ["✅ 标签页容器存在", "   标签页数量: 3", "   标签页 0: 👥 账号管理", "   标签页 1: 📝 运行日志", "   标签页 2: ⚙️ 系统设置", "   ✅ 设置标签页已找到", "     设置标签页子组件数量: 1", "   ⚠️ 设置面板方法 create_general_settings: 不存在", "   ⚠️ 设置面板方法 create_log_settings_panel: 不存在", "   ⚠️ 设置面板方法 create_performance_settings: 不存在", "   ⚠️ 设置面板方法 create_appearance_settings: 不存在", "   ⚠️ 设置面板方法 create_advanced_settings: 不存在", "   ✅ 设置变量 dark_mode: False", "   ✅ 设置变量 font_size: 17", "   ✅ 设置变量 window_width: 1400", "   ✅ 设置变量 window_height: 900", "   ⚠️ 设置变量 auto_save: 不存在", "   ⚠️ 设置变量 backup_enabled: 不存在", "   ⚠️ 设置变量 debug_mode: 不存在"]}, "6. 主界面UI组件检查": {"status": "warning", "details": ["   ✅ 主界面组件 notebook: 存在", "     可见性: 0", "   ✅ 主界面组件 accounts_table: 存在", "   ⚠️ 主界面组件 log_text: 为None", "   ❌ 主界面组件 status_bar: 不存在", "   ❌ 主界面组件 toolbar_frame: 不存在", "   ✅ 主界面组件 navigation_panel: 存在", "   ✅ 账号表格组件详细检查:", "     ⚠️ 表格方法 load_accounts: 不存在", "     ⚠️ 表格方法 refresh_table: 不存在", "     ⚠️ 表格方法 add_account: 不存在", "     ⚠️ 表格方法 delete_account: 不存在", "     ⚠️ 表格方法 update_account: 不存在", "     ⚠️ 表格方法 get_selected_accounts: 不存在", "   ⚠️ 平台导航侧边栏: 不存在"]}, "7. 工具栏和菜单检查": {"status": "warning", "details": ["   ❌ 工具栏组件 toolbar_frame: 不存在", "   ✅ 工具栏组件 header_bar: 存在", "   ❌ 工具栏组件 accounts_toolbar: 不存在", "   ⚠️ 菜单栏: 未设置", "   ⚠️ 右键菜单方法 create_context_menu: 不存在", "   ⚠️ 右键菜单方法 show_context_menu: 不存在", "   ⚠️ 右键菜单方法 create_account_context_menu: 不存在", "   ❌ 工具栏按钮 add_account_button: 不存在", "   ❌ 工具栏按钮 delete_account_button: 不存在", "   ❌ 工具栏按钮 refresh_button: 不存在", "   ❌ 工具栏按钮 settings_button: 不存在", "   ❌ 工具栏按钮 help_button: 不存在"]}, "8. 事件绑定和响应检查": {"status": "warning", "details": ["   ✅ 窗口事件 <Configure>: 已绑定", "   ℹ️ 窗口事件 <FocusIn>: 未绑定", "   ℹ️ 窗口事件 <FocusOut>: 未绑定", "   ✅ 窗口事件 WM_DELETE_WINDOW: 已绑定", "   ℹ️ 快捷键 <Control-s>: 未绑定", "   ℹ️ 快捷键 <Control-o>: 未绑定", "   ℹ️ 快捷键 <Control-n>: 未绑定", "   ℹ️ 快捷键 <Control-q>: 未绑定", "   ℹ️ 快捷键 <F5>: 未绑定", "   ℹ️ 快捷键 <F1>: 未绑定", "   ✅ 开始按钮事件: 已绑定", "   ⚠️ 开始按钮响应方法: 不存在"]}}, "missing_components": ["stats_card", "pause_button", "resume_button", "query_button", "export_button", "progress_bar", "progress_label", "current_account_label", "progress_frame", "log_text", "status_bar", "toolbar_frame", "platform_sidebar", "toolbar_frame", "accounts_toolbar", "menu_bar", "add_account_button", "delete_account_button", "refresh_button", "settings_button", "help_button"], "broken_features": ["日志设置-create_log_settings_panel", "日志设置-create_log_level_settings", "日志设置-create_log_filter_settings", "日志设置-save_log_settings", "日志设置-load_log_settings", "日志设置变量-auto_scroll", "日志设置变量-enhanced_display", "统计功能-total_accounts", "统计功能-successful_accounts", "统计功能-failed_accounts", "统计功能-total_drafts", "统计功能-successful_drafts", "统计功能-failed_drafts", "统计功能-update_stats_display", "统计功能-log_statistics", "统计功能-generate_statistics_report", "进度功能-update_progress", "进度功能-update_status", "进度功能-reset_progress", "进度功能-show_progress", "进度功能-hide_progress", "设置面板-create_general_settings", "设置面板-create_log_settings_panel", "设置面板-create_performance_settings", "设置面板-create_appearance_settings", "设置面板-create_advanced_settings", "设置变量-auto_save", "设置变量-backup_enabled", "设置变量-debug_mode", "账号表格-load_accounts", "账号表格-refresh_table", "账号表格-add_account", "账号表格-delete_account", "账号表格-update_account", "账号表格-get_selected_accounts", "右键菜单-create_context_menu", "右键菜单-show_context_menu", "右键菜单-create_account_context_menu", "开始按钮响应方法"], "summary": {"total_issues": 60, "missing_components_count": 21, "broken_features_count": 39}}