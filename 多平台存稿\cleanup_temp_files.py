#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import shutil
from datetime import datetime

def cleanup_temp_files():
    """清理临时文件和调试脚本"""
    
    print("=== 根目录临时文件清理工具 ===")
    print(f"清理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 需要删除的临时文件
    temp_files = [
        "account_data_backup.json",
        "quick_migrate.py", 
        "migrate_toutiao_data.py",
        "find_toutiao_data.py",
        "debug_data_path.py",
        "test_d_drive.py",
        "account_data.json.backup_20250713_184847"
    ]
    
    # 需要保留的重要文件
    important_files = [
        "config.json",
        "timer_config.json", 
        "netease_draft_details.json",
        "toutiao_draft_details.json",
        "dayu_draft_details.json",
        "debug_log.txt"  # 可选保留，用于问题排查
    ]
    
    print(f"\n📋 将要删除的临时文件:")
    for file in temp_files:
        if os.path.exists(file):
            print(f"  ✅ {file} (存在)")
        else:
            print(f"  ❌ {file} (不存在)")
    
    print(f"\n🔒 将要保留的重要文件:")
    for file in important_files:
        if os.path.exists(file):
            print(f"  ✅ {file} (存在)")
        else:
            print(f"  ❌ {file} (不存在)")
    
    # 确认删除
    print(f"\n⚠️  即将删除 {len([f for f in temp_files if os.path.exists(f)])} 个临时文件")
    confirm = input("确认删除吗？(y/N): ").strip().lower()
    
    if confirm != 'y':
        print("❌ 取消删除操作")
        return False
    
    # 执行删除
    deleted_count = 0
    failed_count = 0
    
    print(f"\n🗑️  开始删除临时文件...")
    
    for file in temp_files:
        if os.path.exists(file):
            try:
                os.remove(file)
                print(f"  ✅ 已删除: {file}")
                deleted_count += 1
            except Exception as e:
                print(f"  ❌ 删除失败: {file} - {e}")
                failed_count += 1
        else:
            print(f"  ⏭️  跳过: {file} (文件不存在)")
    
    print(f"\n📊 清理结果:")
    print(f"  ✅ 成功删除: {deleted_count} 个文件")
    print(f"  ❌ 删除失败: {failed_count} 个文件")
    
    if failed_count == 0:
        print(f"\n🎉 清理完成！根目录现在更加整洁。")
    else:
        print(f"\n⚠️  清理部分完成，有 {failed_count} 个文件删除失败。")
    
    # 显示清理后的根目录状态
    print(f"\n📁 清理后的根目录重要文件:")
    for file in important_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"  📄 {file} ({size} 字节)")
    
    return deleted_count > 0

if __name__ == "__main__":
    try:
        cleanup_temp_files()
    except KeyboardInterrupt:
        print(f"\n❌ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 清理过程出错: {e}")
    
    input(f"\n按回车键退出...")
