"""
今日头条平台存稿处理模块 - 负责处理今日头条平台的视频存稿
"""

import os
import time
import random
import glob
import shutil
import pickle
import json
import datetime
import traceback
import re
import threading
import concurrent.futures
from queue import Queue
from typing import List, Dict, Any, Optional, Tuple, Callable

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, ElementClickInterceptedException
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains

try:
    from .login import ToutiaoLogin
except ImportError:
    from login import ToutiaoLogin

# 今日头条平台常量
TOUTIAO_PUBLISH_URL = "https://mp.toutiao.com/profile_v4/xigua/upload-video"  # 今日头条视频上传页面
TOUTIAO_DRAFT_URL = "https://mp.toutiao.com/profile_v4/graphic/draft-list"    # 今日头条草稿箱页面
TOUTIAO_HOME_URL = "https://mp.toutiao.com/profile_v4/index"                  # 今日头条首页

class ToutiaoDraftProcessor:
    """今日头条平台存稿处理类"""

    # 平台标识符 - 用于模块识别和配置加载
    PLATFORM_ID = "toutiao"
    PLATFORM_NAME = "今日头条平台"

    def __init__(self, account_dir: str, processed_dir: str, processed_covers_dir: str,
                 archive_completed: bool = True, headless_mode: bool = False,
                 draft_limit: int = 0, loop_limit: int = 0, log_callback: Callable = None,
                 screenshots_dir: str = "", random_video_allocation: bool = True,
                 add_draft_detail_callback: Callable = None):
        """
        初始化今日头条存稿处理类

        Args:
            account_dir: 账号目录
            processed_dir: 已处理视频目录
            processed_covers_dir: 已处理封面目录
            archive_completed: 是否归档已完成的视频
            headless_mode: 是否使用无头模式
            draft_limit: 每个账号的存稿限制数量，0表示不限制
            loop_limit: 循环次数限制，0表示不限制
            log_callback: 日志回调函数
            screenshots_dir: 截图保存目录
            random_video_allocation: 是否随机分配视频
            add_draft_detail_callback: 添加存稿详情的回调函数
        """
        self.account_dir = account_dir
        self.processed_dir = processed_dir
        self.processed_covers_dir = processed_covers_dir
        self.screenshots_dir = screenshots_dir
        self.log_callback = log_callback
        self.add_draft_detail_callback = add_draft_detail_callback

        # 运行标志
        self.is_running = False

        # 运行选项
        self.headless_mode = headless_mode
        self.archive_completed = archive_completed
        self.draft_limit = draft_limit
        self.loop_limit = loop_limit
        self.random_video_allocation = random_video_allocation

        # 存稿成功数量
        self.successful_drafts = 0

        # 设置平台标识
        self.platform_id = self.PLATFORM_ID
        self.platform_name = self.PLATFORM_NAME

        # 创建登录实例
        self.login = ToutiaoLogin(log_callback)

        # 浏览器驱动
        self.driver = None

    def log(self, message: str, internal: bool = False) -> None:
        """
        记录日志

        Args:
            message: 日志消息
            internal: 是否为内部日志（内部日志会添加平台标记，但不显示给用户）
        """
        if internal:
            # 内部日志添加平台标记，用于调试和模块识别
            formatted_message = f"[{self.platform_id}] {message}"
        else:
            # 用户日志不显示平台标记
            formatted_message = message

        if self.log_callback:
            self.log_callback(formatted_message)
        else:
            print(formatted_message)

    def _close_driver_with_port_release(self, driver):
        """关闭驱动并释放端口"""
        if driver:
            try:
                # 释放端口
                if hasattr(driver, '_assigned_port'):
                    try:
                        from 网易号存稿.browser.driver import PortManager
                    except ImportError:
                        # 如果相对导入失败，尝试绝对导入
                        import sys
                        import os
                        sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
                        from browser.driver import PortManager
                    PortManager.release_port(driver._assigned_port)
                    self.log(f"已释放端口: {driver._assigned_port}", internal=True)

                driver.quit()
            except Exception as e:
                self.log(f"关闭驱动时发生错误: {str(e)}", internal=True)

    def start(self, account: str = None, accounts: List[str] = None) -> Tuple[bool, List[Dict[str, Any]]]:
        """
        开始存稿处理

        Args:
            account: 单个账号名称
            accounts: 多个账号名称列表

        Returns:
            处理结果和处理的视频信息列表
        """
        # 设置运行标志
        self.is_running = True

        # 重置存稿成功数量
        self.successful_drafts = 0

        # 处理的视频信息列表
        processed_videos = []

        try:
            # 确定要处理的账号
            accounts_to_process = []
            if account:
                accounts_to_process = [account]
            elif accounts:
                accounts_to_process = accounts

            if not accounts_to_process:
                self.log("没有指定要处理的账号")
                self.is_running = False
                return False, processed_videos

            # 处理每个账号
            for account in accounts_to_process:
                if not self.is_running:
                    self.log("任务已被停止，中断处理")
                    break

                self.log(f"开始处理账号: {account}")

                # 处理单个账号
                success, account_videos = self.process_account(account)

                # 添加到处理过的视频信息列表
                processed_videos.extend(account_videos)

                if success:
                    self.log(f"账号 {account} 处理完成")
                else:
                    self.log(f"账号 {account} 处理失败")

            return True, processed_videos

        except Exception as e:
            self.log(f"存稿处理异常: {str(e)}")
            traceback.print_exc()
            return False, processed_videos

        finally:
            # 重置运行标志
            self.is_running = False

    def process_account(self, account: str) -> Tuple[bool, List[Dict[str, Any]]]:
        """
        处理单个账号

        Args:
            account: 账号名称

        Returns:
            处理结果和处理的视频信息列表
        """
        # 处理的视频信息列表
        processed_videos = []

        try:
            # 获取账号Cookie路径 - 支持多种格式
            cookie_path = self._find_cookie_file(account)
            if not cookie_path:
                self.log(f"未找到账号 {account} 的Cookie文件")
                return False, processed_videos

            # 使用Cookie登录
            self.log(f"正在使用Cookie登录账号: {account}")
            success, driver = self.login.login_with_cookies(cookie_path, self.headless_mode, account)

            # 如果Cookie登录失败，尝试使用手机号登录
            if not success:
                self.log(f"Cookie登录失败，尝试使用手机号登录: {account}")
                login_success, driver, cookies = self.login.login_with_phone(self.headless_mode)

                if login_success and cookies:
                    self.log(f"手机号登录成功，保存Cookie: {account}")

                    # 保存Cookie为账号名.txt格式
                    cookie_path = os.path.join(self.account_dir, f"{account}.txt")
                    try:
                        import json
                        with open(cookie_path, "w", encoding='utf-8') as f:
                            json.dump(cookies, f, ensure_ascii=False, indent=4)
                        self.log(f"已保存Cookie到: {cookie_path}")
                    except Exception as e:
                        self.log(f"保存Cookie失败: {str(e)}")

                    success = True
                else:
                    self.log(f"手机号登录失败: {account}")
                    return False, processed_videos

            # 如果登录成功，开始处理视频
            if success and driver:
                self.driver = driver
                self.log(f"登录成功，开始处理视频: {account}")

                # 获取视频文件列表
                video_files = self.get_video_files()

                if not video_files:
                    self.log("未找到视频文件，请检查视频目录")
                    # 释放端口并关闭驱动
                    self._close_driver_with_port_release(driver)
                    return False, processed_videos

                self.log(f"找到 {len(video_files)} 个视频文件")

                # 如果启用随机分配，随机打乱视频文件顺序
                if self.random_video_allocation:
                    random.shuffle(video_files)
                    self.log(f"已随机打乱 {len(video_files)} 个视频文件的顺序")

                # 处理视频
                # 注意：这里只是一个示例，实际实现需要根据今日头条平台的具体操作流程来编写
                # 由于今日头条平台的具体操作流程需要实际测试，这里只提供一个基本框架

                # 循环处理视频
                loop_count = 0
                while self.is_running:
                    # 增加循环计数
                    loop_count += 1

                    # 显示当前循环次数
                    if self.loop_limit > 0:
                        self.log(f"🔄 开始第{loop_count}/{self.loop_limit}轮处理")
                    else:
                        self.log(f"🔄 开始第{loop_count}轮处理")

                    # 检查循环限制
                    if self.loop_limit > 0 and loop_count > self.loop_limit:
                        self.log(f"已达到循环限制 {self.loop_limit}，停止处理")
                        break

                    # 每轮循环重新获取视频文件列表
                    if loop_count > 1:  # 第一轮已经获取过了
                        video_files = self.get_video_files()
                        if not video_files:
                            self.log("未找到视频文件，等待10秒后重试...")
                            time.sleep(10)
                            continue

                        # 如果启用随机分配，随机打乱视频文件顺序
                        if self.random_video_allocation:
                            random.shuffle(video_files)
                            self.log(f"📁 循环{loop_count}: {len(video_files)}个视频已随机排序")
                        else:
                            video_files.sort()
                            self.log(f"📁 循环{loop_count}: {len(video_files)}个视频已排序")

                    # 当前循环的存稿计数
                    current_loop_drafts = 0

                    # 处理当前循环的视频
                    while video_files and self.is_running:
                        # 检查存稿限制
                        if self.draft_limit > 0 and current_loop_drafts >= self.draft_limit:
                            self.log(f"当前循环已达到存稿限制 {self.draft_limit}，进入下一轮")
                            break

                        video_path = video_files.pop(0)
                        self.log(f"开始处理视频: {os.path.basename(video_path)}")

                        # 获取对应的封面文件
                        cover_path = self.get_cover_file(video_path)

                        # 处理视频 - 使用头条号专用上传器
                        success = self._process_single_video(video_path, cover_path)

                        # 添加到处理过的视频信息列表
                        video_info = {
                            "视频": os.path.basename(video_path),
                            "账号": account,
                            "平台": "今日头条",
                            "状态": "成功" if success else "失败",
                            "时间": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        }
                        processed_videos.append(video_info)

                        # 如果成功，增加存稿成功数量
                        if success:
                            self.successful_drafts += 1
                            current_loop_drafts += 1
                            self.log(f"✅ 视频存稿成功: {os.path.basename(video_path)}")

                            # 归档成功的视频
                            if self.archive_completed:
                                self.archive_video(video_path, cover_path, success=True)
                        else:
                            self.log(f"❌ 视频存稿失败: {os.path.basename(video_path)}")

                            # 归档失败的视频
                            if self.archive_completed:
                                self.archive_video(video_path, cover_path, success=False)

                        # 添加存稿详情
                        if self.add_draft_detail_callback:
                            self.add_draft_detail_callback(account, video_info)

                        # 等待一段时间再处理下一个视频
                        if video_files:  # 如果还有视频要处理
                            wait_time = random.uniform(2.0, 5.0)
                            self.log(f"等待 {wait_time:.1f} 秒后处理下一个视频...")
                            time.sleep(wait_time)

                    # 当前循环完成
                    if current_loop_drafts > 0:
                        self.log(f"第{loop_count}轮处理完成，本轮成功存稿 {current_loop_drafts} 个视频")
                    else:
                        self.log(f"第{loop_count}轮处理完成，本轮未成功存稿任何视频")

                    # 如果没有更多视频文件，等待一段时间后继续下一轮
                    if not video_files:
                        self.log("当前没有更多待处理视频，等待10秒后进入下一轮循环...")
                        time.sleep(10)

                # 关闭浏览器并释放端口
                self._close_driver_with_port_release(driver)
                self.driver = None

                return True, processed_videos
            else:
                self.log(f"登录失败: {account}")
                return False, processed_videos

        except Exception as e:
            self.log(f"账号 {account} 处理异常: {str(e)}")
            traceback.print_exc()

            # 关闭浏览器并释放端口
            if self.driver:
                try:
                    self._close_driver_with_port_release(self.driver)
                except:
                    pass
                self.driver = None

            return False, processed_videos

    def stop(self) -> None:
        """停止存稿处理"""
        self.is_running = False

        # 关闭浏览器并释放端口
        if self.driver:
            try:
                self._close_driver_with_port_release(self.driver)
            except:
                pass
            self.driver = None

    def get_video_files(self) -> List[str]:
        """
        获取视频文件列表

        Returns:
            视频文件路径列表
        """
        # 支持的视频格式
        video_extensions = [".mp4", ".avi", ".mov", ".flv", ".wmv", ".mkv"]

        # 获取视频目录下的所有文件
        video_files = []
        for ext in video_extensions:
            video_files.extend(glob.glob(os.path.join(self.processed_dir, f"*{ext}")))

        # 按文件名排序
        video_files.sort()

        return video_files

    def get_cover_file(self, video_path: str) -> Optional[str]:
        """
        获取视频对应的封面文件

        Args:
            video_path: 视频文件路径

        Returns:
            封面文件路径，如果不存在则返回None
        """
        # 获取视频文件名（不含扩展名）
        video_name = os.path.splitext(os.path.basename(video_path))[0]

        # 支持的图片格式
        image_extensions = [".jpg", ".jpeg", ".png", ".bmp"]

        # 查找对应的封面文件
        for ext in image_extensions:
            cover_path = os.path.join(self.processed_covers_dir, f"{video_name}{ext}")
            if os.path.exists(cover_path):
                return cover_path

        # 如果没有找到对应的封面文件，返回None
        return None

    def archive_video(self, video_path: str, cover_path: Optional[str] = None, success: bool = True) -> None:
        """
        归档视频和封面文件

        Args:
            video_path: 视频文件路径
            cover_path: 封面文件路径
            success: 是否成功存稿
        """
        if not self.archive_completed:
            return

        try:
            # 确定目标目录
            target_dir = os.path.join(self.processed_dir, "已处理")
            if not success:
                target_dir = os.path.join(self.processed_dir, "失败")

            # 确保目标目录存在
            os.makedirs(target_dir, exist_ok=True)

            # 移动视频文件
            video_name = os.path.basename(video_path)
            target_video_path = os.path.join(target_dir, video_name)
            shutil.move(video_path, target_video_path)

            # 如果有封面文件，也移动封面文件
            if cover_path and os.path.exists(cover_path):
                cover_name = os.path.basename(cover_path)
                target_cover_dir = os.path.join(target_dir, "封面")
                os.makedirs(target_cover_dir, exist_ok=True)
                target_cover_path = os.path.join(target_cover_dir, cover_name)
                shutil.move(cover_path, target_cover_path)

            self.log(f"已归档{'成功' if success else '失败'}的视频: {video_name}")

        except Exception as e:
            self.log(f"归档视频时出错: {str(e)}")
            traceback.print_exc()

    def _find_cookie_file(self, account: str) -> Optional[str]:
        """
        查找账号的Cookie文件，只支持账号名.txt格式

        Args:
            account: 账号名称

        Returns:
            Cookie文件路径，如果未找到则返回None
        """
        # 只查找账号名.txt格式
        cookie_path = os.path.join(self.account_dir, f"{account}.txt")
        if os.path.exists(cookie_path):
            return cookie_path

        self.log(f"未找到账号 {account} 的Cookie文件")
        return None

    def _process_single_video(self, video_path: str, cover_path: Optional[str] = None) -> bool:
        """
        处理单个视频的上传和存稿

        Args:
            video_path: 视频文件路径
            cover_path: 封面文件路径

        Returns:
            是否成功存稿
        """
        try:
            # 导入头条号专用上传器
            try:
                from .uploader import ToutiaoVideoUploader
            except ImportError:
                from uploader import ToutiaoVideoUploader

            # 创建上传器实例
            uploader = ToutiaoVideoUploader(
                driver=self.driver,
                log_callback=self.log_callback,
                headless_mode=self.headless_mode,
                screenshots_dir=self.screenshots_dir
            )

            # 执行视频上传和存稿
            success = uploader.draft_video(video_path, cover_path)

            return success

        except Exception as e:
            self.log(f"❌ 处理视频时发生错误: {str(e)}")
            traceback.print_exc()
            return False


class ToutiaoDataQuery:
    """今日头条平台数据查询类"""

    # 平台标识符 - 用于模块识别和配置加载
    PLATFORM_ID = "toutiao"
    PLATFORM_NAME = "今日头条平台"

    def __init__(self, account_dir: str, log_callback: Callable = None, headless: bool = True):
        """
        初始化头条数据查询类

        Args:
            account_dir: 账号目录
            log_callback: 日志回调函数
            headless: 是否使用无头模式，默认为True
        """
        self.account_dir = account_dir
        self.log_callback = log_callback
        self.headless = headless

        # 设置平台标识
        self.platform_id = self.PLATFORM_ID
        self.platform_name = self.PLATFORM_NAME

        # 创建登录实例
        self.login = ToutiaoLogin(log_callback)

        # 浏览器驱动
        self.driver = None

    def log(self, message: str, internal: bool = False) -> None:
        """
        记录日志

        Args:
            message: 日志消息
            internal: 是否为内部日志（内部日志会添加平台标记，但不显示给用户）
        """
        if internal:
            # 内部日志添加平台标记，用于调试和模块识别
            formatted_message = f"[{self.platform_id}] {message}"
        else:
            # 用户日志不显示平台标记
            formatted_message = message

        if self.log_callback:
            self.log_callback(formatted_message)
        else:
            print(formatted_message)

    def _close_driver_with_port_release(self, driver):
        """关闭驱动并释放端口"""
        if driver:
            try:
                # 释放端口
                if hasattr(driver, '_assigned_port'):
                    try:
                        from 网易号存稿.browser.driver import PortManager
                    except ImportError:
                        # 如果相对导入失败，尝试绝对导入
                        import sys
                        import os
                        sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
                        from browser.driver import PortManager
                    PortManager.release_port(driver._assigned_port)
                    self.log(f"已释放端口: {driver._assigned_port}", internal=True)

                driver.quit()
            except Exception as e:
                self.log(f"关闭驱动时发生错误: {str(e)}", internal=True)

    def parse_number_with_unit(self, text: str) -> float:
        """
        解析包含单位的数字字符串，支持万字符功能

        Args:
            text: 包含数字和单位的字符串，如 "1.2万", "3456", "2.5万"

        Returns:
            解析后的数字
        """
        if not text:
            return 0.0

        # 清理文本，移除空格和其他非数字字符（保留小数点、万、千等）
        text = text.strip()

        # 使用正则表达式提取数字和单位
        pattern = r'([\d,]+\.?\d*)\s*([万千]?)'
        match = re.search(pattern, text)

        if not match:
            # 如果没有匹配到，尝试直接转换为数字
            try:
                return float(re.sub(r'[^\d.]', '', text))
            except:
                return 0.0

        number_str = match.group(1).replace(',', '')  # 移除千位分隔符
        unit = match.group(2)

        try:
            number = float(number_str)

            # 根据单位进行转换
            if unit == '万':
                return number * 10000
            elif unit == '千':
                return number * 1000
            else:
                return number
        except:
            return 0.0

    def safe_get_element_text(self, xpath: str, timeout: int = 5) -> str:
        """
        安全获取元素文本内容

        Args:
            xpath: 元素的XPath
            timeout: 等待超时时间

        Returns:
            元素文本内容，如果获取失败返回空字符串
        """
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((By.XPATH, xpath))
            )
            return element.text.strip()
        except TimeoutException:
            # 超时异常，不打印详细错误信息
            self.log(f"元素未找到（超时）: {xpath}")
            return ""
        except Exception as e:
            # 其他异常，打印简化的错误信息
            self.log(f"获取元素失败 {xpath}: {type(e).__name__}")
            return ""

    def query_account_data(self, account: str) -> Dict[str, Any]:
        """
        查询单个账号的数据

        Args:
            account: 账号名称

        Returns:
            账号数据字典
        """
        # 初始化结果（日志输出已移至浏览器驱动创建时合并显示）
        result = {
            "账号": account,
            "用户名": "",
            "累计收益": 0.0,
            "昨日收益": 0.0,
            "总播放": 0.0,
            "昨日播放": 0.0,
            "总粉丝": 0.0,
            "昨日粉丝": 0.0,
            "草稿箱数量": 0,
            "可提现": 0.0,
            "总提现": 0.0,
            "最近提现日期": "",
            "最近提现金额": 0.0,
            "七日收益": [],
            "查询时间": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "状态": "失败",
            "错误信息": ""
        }

        try:
            # 查找Cookie文件
            cookie_path = self._find_cookie_file(account)
            if not cookie_path:
                result["错误信息"] = "未找到Cookie文件"
                return result

            # 使用Cookie登录
            self.log(f"正在登录账号: {account} (无头模式: {self.headless})")
            success, driver = self.login.login_with_cookies(cookie_path, headless=self.headless, account=account)

            if not success or not driver:
                result["错误信息"] = "登录失败"
                return result

            self.driver = driver

            # 1. 获取首页数据
            self.log("正在获取首页数据...")
            home_data = self._get_home_data()
            result.update(home_data)

            # 2. 获取草稿箱数据
            self.log("正在获取草稿箱数据...")
            draft_data = self._get_draft_data()
            result.update(draft_data)

            # 3. 获取提现数据
            self.log("正在获取提现数据...")
            withdraw_data = self._get_withdraw_data()
            result.update(withdraw_data)

            # 统一字段映射标准：草稿箱、待提现、七日收益、查询时间、总收益
            result["总收益"] = result.get("累计收益", 0.0)  # 统一标准字段
            result["草稿箱"] = result.get("草稿箱数量", 0)    # 统一标准字段
            result["待提现"] = result.get("可提现", 0.0)     # 统一标准字段
            result["查询时间"] = result.get("查询时间", "")   # 统一标准字段

            result["状态"] = "成功"
            self.log(f"账号 {account} 数据查询完成")

        except Exception as e:
            self.log(f"查询账号 {account} 数据时发生错误: {str(e)}")
            result["错误信息"] = str(e)
            traceback.print_exc()

        finally:
            # 关闭浏览器并释放端口
            if self.driver:
                try:
                    self._close_driver_with_port_release(self.driver)
                except:
                    pass
                self.driver = None

        return result

    def _get_home_data(self) -> Dict[str, Any]:
        """
        获取首页数据

        Returns:
            首页数据字典
        """
        data = {}

        try:
            # 导航到首页
            self.driver.get(TOUTIAO_HOME_URL)
            self.log("正在等待首页加载...")
            time.sleep(5)  # 增加等待时间

            # 等待页面主要内容加载
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, '//*[@id="root"]'))
                )
                self.log("首页主要内容已加载")
            except TimeoutException:
                self.log("首页加载超时，继续尝试获取数据")

            # 获取用户名
            username_xpath = '//*[@id="masterRoot"]/div/div[1]/div/div/div[3]/div[1]/a/span/div/div/div[1]'
            username = self.safe_get_element_text(username_xpath, timeout=8)
            data["用户名"] = username
            self.log(f"用户名: {username}")

            # 获取累计收益
            total_income_xpath = '//*[@id="root"]/div/div/div[1]/div[1]/div[1]/div[3]/div/div[2]/a'
            total_income_text = self.safe_get_element_text(total_income_xpath, timeout=8)
            data["累计收益"] = self.parse_number_with_unit(total_income_text)
            self.log(f"累计收益: {total_income_text} -> {data['累计收益']}")

            # 获取昨日收益
            yesterday_income_xpath = '//*[@id="root"]/div/div/div[1]/div[1]/div[1]/div[3]/div/div[3]/p/span'
            yesterday_income_text = self.safe_get_element_text(yesterday_income_xpath, timeout=8)
            data["昨日收益"] = self.parse_number_with_unit(yesterday_income_text)
            self.log(f"昨日收益: {yesterday_income_text} -> {data['昨日收益']}")

            # 获取总播放
            total_views_xpath = '//*[@id="root"]/div/div/div[1]/div[1]/div[1]/div[2]/div/div[2]'
            total_views_text = self.safe_get_element_text(total_views_xpath, timeout=8)
            data["总播放"] = self.parse_number_with_unit(total_views_text)
            self.log(f"总播放: {total_views_text} -> {data['总播放']}")

            # 获取昨日播放
            yesterday_views_xpath = '//*[@id="root"]/div/div/div[1]/div[1]/div[1]/div[2]/div/div[3]/p/span'
            yesterday_views_text = self.safe_get_element_text(yesterday_views_xpath, timeout=8)
            data["昨日播放"] = self.parse_number_with_unit(yesterday_views_text)
            self.log(f"昨日播放: {yesterday_views_text} -> {data['昨日播放']}")

            # 获取总粉丝
            total_fans_xpath = '//*[@id="root"]/div/div/div[1]/div[1]/div[1]/div[1]/div/div[2]/a'
            total_fans_text = self.safe_get_element_text(total_fans_xpath, timeout=8)
            data["总粉丝"] = self.parse_number_with_unit(total_fans_text)
            self.log(f"总粉丝: {total_fans_text} -> {data['总粉丝']}")

            # 获取昨日粉丝
            yesterday_fans_xpath = '//*[@id="root"]/div/div/div[1]/div[1]/div[1]/div[1]/div/div[3]/p/span'
            yesterday_fans_text = self.safe_get_element_text(yesterday_fans_xpath, timeout=8)
            data["昨日粉丝"] = self.parse_number_with_unit(yesterday_fans_text)
            self.log(f"昨日粉丝: {yesterday_fans_text} -> {data['昨日粉丝']}")

            # 获取七日收益数据
            seven_day_data = self._get_seven_day_income()
            data["七日收益"] = seven_day_data

        except Exception as e:
            self.log(f"获取首页数据时发生错误: {str(e)}")

        return data

    def _get_seven_day_income(self) -> List[Dict[str, Any]]:
        """
        获取七日收益数据

        Returns:
            七日收益数据列表
        """
        seven_day_data = []

        try:
            # 导航到收益分析页面
            income_url = "https://mp.toutiao.com/profile_v4/analysis/income-overview"
            self.log(f"正在访问收益分析页面: {income_url}")
            self.driver.get(income_url)
            self.log("正在等待收益分析页面加载...")
            time.sleep(5)  # 等待页面加载

            # 等待页面主要内容加载
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, '//*[@id="root"]'))
                )
                self.log("收益分析页面主要内容已加载")
            except TimeoutException:
                self.log("收益分析页面加载超时，继续尝试获取数据")

            # 等待表格加载
            self.log("等待七日收益表格加载...")
            time.sleep(3)

            # 首先检查表格是否存在
            table_xpath = '//*[@id="root"]/div/div[4]/div/div/div/div/div/div[1]/div/div/table'
            table_exists = False

            try:
                WebDriverWait(self.driver, 8).until(
                    EC.presence_of_element_located((By.XPATH, table_xpath))
                )
                table_exists = True
                self.log("找到七日收益表格")
            except TimeoutException:
                self.log("未找到七日收益表格，可能页面结构发生变化")
                return seven_day_data

            if not table_exists:
                return seven_day_data

            # 动态检测实际有多少行数据
            max_rows_to_check = 10  # 最多检查10行
            actual_rows = 0

            # 先检测有多少行实际存在
            for row in range(1, max_rows_to_check + 1):
                date_xpath = f'//*[@id="root"]/div/div[4]/div/div/div/div/div/div[1]/div/div/table/tbody/tr[{row}]/td[1]/div'
                try:
                    element = WebDriverWait(self.driver, 1).until(
                        EC.presence_of_element_located((By.XPATH, date_xpath))
                    )
                    if element.text.strip():  # 如果有内容
                        actual_rows = row
                except:
                    break  # 如果找不到元素，说明没有更多行了

            # 限制只获取7天的数据
            actual_rows = min(actual_rows, 7)
            # self.log(f"检测到 {actual_rows} 行数据，限制获取7天收益数据")  # 隐藏调试日志

            # 获取实际存在的行数据
            for row in range(1, actual_rows + 1):
                try:
                    # 获取日期
                    date_xpath = f'//*[@id="root"]/div/div[4]/div/div/div/div/div/div[1]/div/div/table/tbody/tr[{row}]/td[1]/div'
                    date_text = self.safe_get_element_text(date_xpath, timeout=2)

                    # 获取收益
                    income_xpath = f'//*[@id="root"]/div/div[4]/div/div/div/div/div/div[1]/div/div/table/tbody/tr[{row}]/td[2]/div'
                    income_text = self.safe_get_element_text(income_xpath, timeout=2)
                    income_value = self.parse_number_with_unit(income_text)

                    if date_text:  # 只有当日期不为空时才添加
                        day_data = {
                            "日期": date_text,
                            "收益": income_value,
                            "收益文本": income_text
                        }
                        seven_day_data.append(day_data)
                        self.log(f"第{row}天收益: {date_text} - {income_text} -> {income_value}")
                    else:
                        self.log(f"第{row}行日期为空，跳过")

                except Exception as e:
                    self.log(f"获取第{row}天收益数据失败: {type(e).__name__}")
                    continue

        except Exception as e:
            self.log(f"获取七日收益数据时发生错误: {str(e)}")

        # self.log(f"成功获取 {len(seven_day_data)} 天的收益数据")  # 隐藏调试日志
        return seven_day_data

    def _get_draft_data(self) -> Dict[str, Any]:
        """
        获取草稿箱数据

        Returns:
            草稿箱数据字典
        """
        data = {}

        try:
            # 导航到草稿箱页面
            draft_url = "https://mp.toutiao.com/profile_v4/manage/draft"
            self.log(f"正在访问草稿箱页面: {draft_url}")
            self.driver.get(draft_url)
            self.log("正在等待草稿箱页面加载...")
            time.sleep(5)  # 增加等待时间

            # 等待页面主要内容加载
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, '//*[@id="masterRoot"]'))
                )
                self.log("草稿箱页面主要内容已加载")
            except TimeoutException:
                self.log("草稿箱页面加载超时，继续尝试获取数据")

            # 获取草稿箱数量 - 尝试多个可能的XPath
            draft_count_xpaths = [
                '//*[@id="masterRoot"]/div/div[3]/section/main/div[2]/div/div[1]/div/div/div[10]/span/span',
                '//*[@id="masterRoot"]//span[contains(text(), "草稿")]/../span',
                '//*[contains(text(), "草稿")]/..//span[contains(@class, "count")]',
                '//*[contains(text(), "草稿")]//following-sibling::span',
                '//*[contains(text(), "草稿")]//span'
            ]

            draft_count_text = ""
            for xpath in draft_count_xpaths:
                draft_count_text = self.safe_get_element_text(xpath, timeout=3)
                if draft_count_text:
                    self.log(f"使用XPath获取到草稿数量: {xpath}")
                    break

            # 解析草稿箱数量
            draft_count = 0
            if draft_count_text:
                try:
                    # 提取数字
                    numbers = re.findall(r'\d+', draft_count_text)
                    if numbers:
                        draft_count = int(numbers[0])
                except:
                    draft_count = 0

            data["草稿箱数量"] = draft_count
            self.log(f"草稿箱数量: {draft_count_text} -> {draft_count}")

        except Exception as e:
            self.log(f"获取草稿箱数据时发生错误: {str(e)}")
            data["草稿箱数量"] = 0

        return data

    def _get_withdraw_data(self) -> Dict[str, Any]:
        """
        获取提现数据

        Returns:
            提现数据字典
        """
        data = {}

        try:
            # 导航到提现页面
            withdraw_url = "https://mp.toutiao.com/profile_v4/personal/checkout-center"
            self.log(f"正在访问提现页面: {withdraw_url}")
            self.driver.get(withdraw_url)
            self.log("正在等待提现页面加载...")
            time.sleep(5)  # 增加等待时间

            # 等待页面主要内容加载
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, '//*[@id="root"]'))
                )
                self.log("提现页面主要内容已加载")
            except TimeoutException:
                self.log("提现页面加载超时，继续尝试获取数据")

            # 获取可提现金额
            available_xpath = '//*[@id="root"]/div/div[2]/div[1]/div[2]/div/div[2]'
            available_text = self.safe_get_element_text(available_xpath, timeout=8)
            data["可提现"] = self.parse_number_with_unit(available_text)
            self.log(f"可提现: {available_text} -> {data['可提现']}")

            # 获取总提现金额
            total_withdraw_xpath = '//*[@id="root"]/div/div[2]/div[1]/div[3]/div/div[2]'
            total_withdraw_text = self.safe_get_element_text(total_withdraw_xpath, timeout=8)
            data["总提现"] = self.parse_number_with_unit(total_withdraw_text)
            self.log(f"总提现: {total_withdraw_text} -> {data['总提现']}")

            # 获取最近一次提现记录
            try:
                # 先检查提现记录表格是否存在
                table_xpath = '//*[@id="root"]/div/div[2]/div[3]/div/div/div/div/div/div/div/div/table'
                try:
                    WebDriverWait(self.driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, table_xpath))
                    )
                    self.log("找到提现记录表格")

                    # 提现日期
                    recent_date_xpath = '//*[@id="root"]/div/div[2]/div[3]/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[1]/div'
                    recent_date = self.safe_get_element_text(recent_date_xpath, timeout=3)
                    data["最近提现日期"] = recent_date

                    # 提现金额
                    recent_amount_xpath = '//*[@id="root"]/div/div[2]/div[3]/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[5]/div'
                    recent_amount_text = self.safe_get_element_text(recent_amount_xpath, timeout=3)
                    data["最近提现金额"] = self.parse_number_with_unit(recent_amount_text)

                    self.log(f"最近提现: {recent_date} - {recent_amount_text} -> {data['最近提现金额']}")

                except TimeoutException:
                    self.log("未找到提现记录表格，可能没有提现记录")
                    data["最近提现日期"] = ""
                    data["最近提现金额"] = 0.0

            except Exception as e:
                self.log(f"获取最近提现记录失败: {type(e).__name__}")
                data["最近提现日期"] = ""
                data["最近提现金额"] = 0.0

        except Exception as e:
            self.log(f"获取提现数据时发生错误: {str(e)}")
            data["可提现"] = 0.0
            data["总提现"] = 0.0
            data["最近提现日期"] = ""
            data["最近提现金额"] = 0.0

        return data

    def _find_cookie_file(self, account: str) -> Optional[str]:
        """
        查找账号的Cookie文件，只支持账号名.txt格式

        Args:
            account: 账号名称

        Returns:
            Cookie文件路径，如果未找到则返回None
        """
        # 只查找账号名.txt格式
        cookie_path = os.path.join(self.account_dir, f"{account}.txt")
        if os.path.exists(cookie_path):
            return cookie_path

        self.log(f"未找到账号 {account} 的Cookie文件")
        return None

    def query_multiple_accounts_concurrent(self, accounts: List[str], max_workers: int = 3) -> Dict[str, Dict[str, Any]]:
        """
        并发查询多个账号的数据

        Args:
            accounts: 账号列表
            max_workers: 最大并发线程数，默认3个

        Returns:
            账号数据字典，格式: {账号名: 账号数据}
        """
        self.log(f"开始并发查询 {len(accounts)} 个账号，最大并发数: {max_workers}")

        results = {}

        # 使用线程池执行器进行并发查询
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有查询任务
            future_to_account = {
                executor.submit(self.query_account_data, account): account
                for account in accounts
            }

            # 收集结果
            for future in concurrent.futures.as_completed(future_to_account):
                account = future_to_account[future]
                try:
                    result = future.result()
                    results[account] = result

                    if result.get("状态") == "成功":
                        self.log(f"✅ 账号 {account} 查询完成")
                    else:
                        error_msg = result.get("错误信息", "未知错误")
                        self.log(f"❌ 账号 {account} 查询失败: {error_msg}")

                except Exception as e:
                    self.log(f"❌ 账号 {account} 查询异常: {str(e)}")
                    results[account] = {
                        "账号": account,
                        "状态": "异常",
                        "错误信息": str(e),
                        "查询时间": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }

        self.log(f"并发查询完成，成功: {sum(1 for r in results.values() if r.get('状态') == '成功')} 个，失败: {sum(1 for r in results.values() if r.get('状态') != '成功')} 个")
        return results

    def query_accounts_batch(self, accounts: List[str], batch_size: int = 5, max_workers: int = 3) -> Dict[str, Dict[str, Any]]:
        """
        分批并发查询账号数据

        Args:
            accounts: 账号列表
            batch_size: 每批处理的账号数量
            max_workers: 每批的最大并发线程数

        Returns:
            所有账号数据字典
        """
        self.log(f"开始分批查询 {len(accounts)} 个账号，批大小: {batch_size}，并发数: {max_workers}")

        all_results = {}

        # 分批处理
        for i in range(0, len(accounts), batch_size):
            batch = accounts[i:i + batch_size]
            batch_num = i // batch_size + 1
            total_batches = (len(accounts) + batch_size - 1) // batch_size

            self.log(f"正在处理第 {batch_num}/{total_batches} 批，包含 {len(batch)} 个账号")

            # 并发查询当前批次
            batch_results = self.query_multiple_accounts_concurrent(batch, max_workers)
            all_results.update(batch_results)

            # 批次间休息，避免对服务器造成过大压力
            if i + batch_size < len(accounts):
                rest_time = 10  # 休息10秒
                self.log(f"批次完成，休息 {rest_time} 秒后继续...")
                time.sleep(rest_time)

        success_count = sum(1 for r in all_results.values() if r.get('状态') == '成功')
        fail_count = len(all_results) - success_count

        self.log(f"所有批次查询完成！总计: {len(all_results)} 个账号，成功: {success_count} 个，失败: {fail_count} 个")
        return all_results


class ToutiaoDataQueryManager:
    """今日头条数据查询管理器 - 支持多线程并发查询"""

    def __init__(self, account_dir: str, log_callback: Callable = None, headless: bool = True):
        """
        初始化查询管理器

        Args:
            account_dir: 账号目录
            log_callback: 日志回调函数
            headless: 是否使用无头模式，默认为True
        """
        self.account_dir = account_dir
        self.log_callback = log_callback
        self.headless = headless
        self.results = {}
        self.lock = threading.Lock()

    def log(self, message: str) -> None:
        """
        记录日志

        Args:
            message: 日志消息
        """
        if self.log_callback:
            self.log_callback(f"[头条查询管理器] {message}")
        else:
            print(f"[头条查询管理器] {message}")

    def query_single_account_thread(self, account: str, result_queue: Queue) -> None:
        """
        单个账号查询线程函数

        Args:
            account: 账号名称
            result_queue: 结果队列
        """
        try:
            # 为每个线程创建独立的查询对象
            query = ToutiaoDataQuery(self.account_dir, self.log_callback, self.headless)

            # 查询账号数据
            result = query.query_account_data(account)

            # 将结果放入队列
            result_queue.put((account, result))

        except Exception as e:
            error_result = {
                "账号": account,
                "状态": "线程异常",
                "错误信息": str(e),
                "查询时间": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            result_queue.put((account, error_result))

    def query_accounts_threaded(self, accounts: List[str], max_threads: int = 3) -> Dict[str, Dict[str, Any]]:
        """
        使用多线程查询多个账号

        Args:
            accounts: 账号列表
            max_threads: 最大线程数

        Returns:
            查询结果字典
        """
        self.log(f"开始多线程查询 {len(accounts)} 个账号，最大线程数: {max_threads}")

        results = {}
        result_queue = Queue()
        threads = []

        # 创建线程池
        for i, account in enumerate(accounts):
            # 控制并发线程数
            if len(threads) >= max_threads:
                # 等待一个线程完成
                for thread in threads:
                    if not thread.is_alive():
                        thread.join()
                        threads.remove(thread)
                        break
                else:
                    # 如果所有线程都还在运行，等待第一个完成
                    threads[0].join()
                    threads.pop(0)

            # 创建新线程
            thread = threading.Thread(
                target=self.query_single_account_thread,
                args=(account, result_queue),
                name=f"ToutiaoQuery-{account}"
            )
            thread.start()
            threads.append(thread)

            self.log(f"启动线程查询账号: {account} ({i+1}/{len(accounts)})")

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        # 收集所有结果
        while not result_queue.empty():
            account, result = result_queue.get()
            results[account] = result

        success_count = sum(1 for r in results.values() if r.get('状态') == '成功')
        fail_count = len(results) - success_count

        self.log(f"多线程查询完成！总计: {len(results)} 个账号，成功: {success_count} 个，失败: {fail_count} 个")
        return results

    def query_accounts_with_progress(self, accounts: List[str], max_threads: int = 3,
                                   progress_callback: Callable = None, data_update_callback: Callable = None) -> Dict[str, Dict[str, Any]]:
        """
        带进度回调的多线程查询

        Args:
            accounts: 账号列表
            max_threads: 最大线程数
            progress_callback: 进度回调函数，参数为 (completed, total, current_account)
            data_update_callback: 数据更新回调函数，参数为 (account, result)

        Returns:
            查询结果字典
        """
        self.log(f"开始带进度的多线程查询 {len(accounts)} 个账号")

        results = {}
        completed_count = 0

        def account_completed_callback(account: str, result: Dict[str, Any]):
            nonlocal completed_count
            completed_count += 1
            results[account] = result

            # 调用数据更新回调
            if data_update_callback:
                data_update_callback(account, result)

            # 调用进度回调
            if progress_callback:
                progress_callback(completed_count, len(accounts), account)

        # 使用线程池执行器
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_threads) as executor:
            # 提交所有任务
            future_to_account = {}
            for account in accounts:
                query = ToutiaoDataQuery(self.account_dir, self.log_callback, self.headless)
                future = executor.submit(query.query_account_data, account)
                future_to_account[future] = account

            # 处理完成的任务
            for future in concurrent.futures.as_completed(future_to_account):
                account = future_to_account[future]
                try:
                    result = future.result()
                    account_completed_callback(account, result)
                except Exception as e:
                    error_result = {
                        "账号": account,
                        "状态": "执行异常",
                        "错误信息": str(e),
                        "查询时间": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                    account_completed_callback(account, error_result)

        return results