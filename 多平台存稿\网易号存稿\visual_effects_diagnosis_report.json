{"1. 基础导入检查": {"status": "success", "details": ["NeteaseDraftUI 导入成功", "ConfigManager 导入成功", "Theme 导入成功", "AnimationEngine 导入成功", "VisualEffectsManager 导入成功", "StatusFeedbackManager 导入成功", "EnhancedLogDisplay 导入成功", "ResponsiveLayoutManager 导入成功"]}, "2. 配置系统检查": {"status": "success", "details": ["visual_effects_enabled: None", "animations_enabled: None", "transitions_enabled: None", "feedback_enabled: None", "enhanced_log_display: None", "dark_mode: False", "配置保存/加载测试通过"]}, "3. 主UI初始化检查": {"status": "error", "details": ["NeteaseDraftUI 实例创建成功", "visual_effects_enabled: True", "animations_enabled: True", "dark_mode: False", "主UI初始化异常: ConfigManager.get() missing 1 required positional argument: 'key'"], "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Downloads\\网易\\多平台存稿\\网易号存稿\\deep_diagnosis.py\", line 187, in diagnose_main_ui_init\n    result['details'].append(f\"{attr}: {value.get()}\")\n                                        ^^^^^^^^^^^\nTypeError: ConfigManager.get() missing 1 required positional argument: 'key'\n"}, "4. 视觉效果组件状态检查": {"status": "success", "details": ["✅ animation_engine: 已初始化 (AnimationEngine)", "✅ visual_effects_manager: 已初始化 (VisualEffectsManager)", "✅ status_feedback: 已初始化 (StatusFeedbackManager)", "✅ responsive_layout: 已初始化 (ResponsiveLayoutManager)", "✅ 便捷方法 show_success_message: 存在", "✅ 便捷方法 show_error_message: 存在", "✅ 便捷方法 show_warning_message: 存在", "✅ 便捷方法 show_info_message: 存在", "✅ 便捷方法 animate_widget: 存在"]}, "5. 日志系统深度检查": {"status": "warning", "details": ["✅ 日志管理器: 已初始化 (LogManager)", "增强显示模式: True", "✅ 增强日志显示组件: 已初始化", "⚠️ 日志管理器动画引擎: 为None", "✅ 日志回调: 已设置"]}, "6. 主题系统检查": {"status": "success", "details": ["当前主题: light", "✅ 主题方法 apply_theme: 存在", "✅ 主题方法 _on_theme_change: 存在", "✅ 基础主题应用测试通过", "✅ 增强主题应用测试通过"]}, "7. 便捷方法检查": {"status": "success", "details": ["✅ show_success_message: 调用成功", "✅ show_error_message: 调用成功", "✅ show_warning_message: 调用成功", "✅ show_info_message: 调用成功", "✅ animate_widget: 方法存在"]}, "8. 集成关系检查": {"status": "warning", "details": ["✅ status_feedback: 动画引擎已传递", "⚠️ log_manager: 动画引擎为None", "✅ 配置管理器: 已初始化", "✅ 依赖项检查通过"]}}