"""
列出D:\多平台存稿目录的所有内容
"""

from pathlib import Path
import os

def list_directory():
    """列出目录内容"""
    target_dir = Path(r"D:\多平台存稿")
    
    print(f"目录: {target_dir}")
    print(f"存在: {target_dir.exists()}")
    
    if target_dir.exists():
        print("\n目录内容:")
        try:
            items = list(target_dir.iterdir())
            items.sort()
            
            for item in items:
                if item.is_file():
                    size = item.stat().st_size
                    size_mb = size / (1024 * 1024)
                    print(f"  📄 {item.name} ({size_mb:.1f} MB)")
                elif item.is_dir():
                    try:
                        file_count = len(list(item.iterdir()))
                        print(f"  📁 {item.name}/ ({file_count} 项)")
                    except:
                        print(f"  📁 {item.name}/")
        except Exception as e:
            print(f"错误: {e}")
    else:
        print("目录不存在")

if __name__ == "__main__":
    list_directory()
