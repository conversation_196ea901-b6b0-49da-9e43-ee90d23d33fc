#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志管理器 - 负责日志显示、搜索、过滤等功能
遵循MECE原则：与日志相关的所有功能集中管理
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, simpledialog, filedialog
import threading
import queue
import datetime
import re
import os
import subprocess
import platform
import shutil
from typing import Dict, Any, Optional, Callable

from 网易号存稿.common.logger import logger
from 网易号存稿.ui.styles.checkbox_styles import create_beautiful_checkbox


class LogManager:
    """日志管理器 - 负责所有日志相关功能"""
    
    def __init__(self, parent_ui, config_manager):
        """
        初始化日志管理器
        
        Args:
            parent_ui: 主UI实例
            config_manager: 配置管理器
        """
        self.parent_ui = parent_ui
        self.config_manager = config_manager
        
        # 日志相关组件
        self.log_text = None
        self.log_queue = queue.Queue()
        self.log_handler_running = False
        self.log_handler_thread = None
        
        # 日志过滤和搜索
        self.last_draft_messages = []
        self.last_draft_time = None

        # 日志合并缓存
        self.message_cache = {}
        self.last_message_time = {}
        self.merge_timeout = 3  # 3秒内的相似消息会被合并
        
        # 日志配置变量
        self.enable_level_filter = tk.BooleanVar()
        self.enable_keyword_filter = tk.BooleanVar()
        self.min_log_level = tk.StringVar()
        self.simple_log_mode = tk.BooleanVar()
        self.auto_scroll = tk.BooleanVar()  # 自动滚动设置
        self.max_log_lines = tk.IntVar()  # 最大日志行数

        # 加载日志配置
        self._load_log_config()

        # 绑定变量变化事件，实现自动保存
        self._bind_config_auto_save()

        # 延迟启动日志处理线程，确保UI完全初始化
        if hasattr(self.parent_ui, 'root') and self.parent_ui.root:
            self.parent_ui.root.after(100, self.start_log_handler)
            # 启动缓存清理定时器
            self.parent_ui.root.after(30000, self._cleanup_message_cache)  # 30秒清理一次

    def _load_log_config(self):
        """加载日志配置"""
        try:
            # 从通用配置中加载日志设置
            self.enable_level_filter.set(self.config_manager.get("enable_level_filter", False, platform="common"))
            self.enable_keyword_filter.set(self.config_manager.get("enable_keyword_filter", False, platform="common"))
            self.min_log_level.set(self.config_manager.get("min_log_level", "INFO", platform="common"))
            self.simple_log_mode.set(self.config_manager.get("simple_log_mode", False, platform="common"))
            self.auto_scroll.set(self.config_manager.get("auto_scroll", True, platform="common"))  # 默认启用自动滚动
            self.max_log_lines.set(self.config_manager.get("max_log_lines", 1000, platform="common"))  # 默认最大1000行

            # 加载过滤关键词
            filtered_keywords = self.config_manager.get("filtered_keywords", [], platform="common")
            if filtered_keywords and hasattr(logger, 'set_filtered_keywords'):
                logger.set_filtered_keywords(filtered_keywords)

            # 应用日志过滤设置
            logger.enable_level_filtering(self.enable_level_filter.get())
            logger.enable_keyword_filtering(self.enable_keyword_filter.get())
            logger.set_min_level(self.min_log_level.get())

        except Exception as e:
            self.parent_ui.log(f"❌ 加载日志配置失败: {e}")

    def _bind_config_auto_save(self):
        """绑定配置变量的自动保存"""
        try:
            # 绑定所有日志配置变量的变化事件
            self.enable_level_filter.trace_add('write', self._on_config_change)
            self.enable_keyword_filter.trace_add('write', self._on_config_change)
            self.min_log_level.trace_add('write', self._on_config_change)
            self.simple_log_mode.trace_add('write', self._on_config_change)
            self.auto_scroll.trace_add('write', self._on_config_change)
            self.max_log_lines.trace_add('write', self._on_config_change)

        except Exception as e:
            self.parent_ui.log(f"❌ 绑定日志配置自动保存失败: {e}")

    def _on_config_change(self, *args):
        """配置变化时的回调函数"""
        try:
            # 自动保存配置到配置文件
            self.config_manager.set("enable_level_filter", self.enable_level_filter.get(), platform="common")
            self.config_manager.set("enable_keyword_filter", self.enable_keyword_filter.get(), platform="common")
            self.config_manager.set("min_log_level", self.min_log_level.get(), platform="common")
            self.config_manager.set("simple_log_mode", self.simple_log_mode.get(), platform="common")
            self.config_manager.set("auto_scroll", self.auto_scroll.get(), platform="common")

            # 保存过滤关键词
            if hasattr(logger, 'get_filtered_keywords'):
                filtered_keywords = list(logger.get_filtered_keywords())
                self.config_manager.set("filtered_keywords", filtered_keywords, platform="common")

            # 应用设置到logger
            logger.enable_level_filtering(self.enable_level_filter.get())
            logger.enable_keyword_filtering(self.enable_keyword_filter.get())
            logger.set_min_level(self.min_log_level.get())

            # 保存配置到文件
            self.config_manager.save_config()

            # 静默保存，不显示提示信息

        except Exception as e:
            self.parent_ui.log(f"❌ 自动保存日志配置失败: {e}")

    def create_log_tab(self, parent):
        """创建现代化日志标签页"""
        # 创建主容器
        main_container = ttk.Frame(parent)
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 创建日志工具栏
        self._create_log_toolbar(main_container)
        
        # 创建日志显示区域
        self._create_log_display(main_container)
        
        return main_container
    
    def _create_log_toolbar(self, parent):
        """创建日志工具栏"""
        toolbar_frame = ttk.Frame(parent)
        toolbar_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 左侧按钮组
        left_buttons = ttk.Frame(toolbar_frame)
        left_buttons.pack(side=tk.LEFT)
        
        # 日志操作按钮
        ttk.Button(
            left_buttons,
            text="🔍 搜索日志",
            command=self.search_log
        ).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(
            left_buttons,
            text="📊 日志统计",
            command=self.show_log_statistics
        ).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(
            left_buttons,
            text="💾 保存日志",
            command=self.save_log_to_file
        ).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(
            left_buttons,
            text="🗑️ 清空日志",
            command=self.clear_log
        ).pack(side=tk.LEFT, padx=(0, 10))
        
        # 右侧按钮组
        right_buttons = ttk.Frame(toolbar_frame)
        right_buttons.pack(side=tk.RIGHT)

        # 进度按钮
        ttk.Button(
            right_buttons,
            text="📊 进度",
            command=self.show_progress_dialog
        ).pack(side=tk.RIGHT, padx=(0, 10))

        # 设置按钮
        ttk.Button(
            right_buttons,
            text="⚙️ 日志设置",
            command=self.show_log_settings
        ).pack(side=tk.RIGHT)
    
    def _create_log_display(self, parent):
        """创建日志显示区域"""
        # 创建日志文本框容器
        log_container = ttk.Frame(parent)
        log_container.pack(fill=tk.BOTH, expand=True)
        
        # 创建日志文本框 - 删除滚动条，只保留滚轮滚动，使用系统默认字体大小
        self.log_text = tk.Text(
            log_container,
            wrap=tk.WORD,
            font=("Consolas", 9),  # 使用系统默认字体大小
            bg="#ffffff",  # 白色背景
            fg="#000000",  # 黑色文字
            insertbackground="#000000",  # 黑色光标
            selectbackground="#b3d9ff"  # 浅蓝色选择背景
        )

        # 添加滚轮支持
        def _on_mousewheel(event):
            self.log_text.yview_scroll(int(-1*(event.delta/120)), "units")

        def _bind_mousewheel(event):
            self.log_text.bind_all("<MouseWheel>", _on_mousewheel)

        def _unbind_mousewheel(event):
            self.log_text.unbind_all("<MouseWheel>")

        self.log_text.bind('<Enter>', _bind_mousewheel)
        self.log_text.bind('<Leave>', _unbind_mousewheel)

        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # 配置日志文本框标签
        self._configure_log_tags()
        
        # 绑定右键菜单
        self._create_log_context_menu()
    
    def _configure_log_tags(self):
        """配置日志文本标签"""
        if not self.log_text:
            return

        # 配置不同级别的日志颜色（适配白色背景，增强可读性）
        self.log_text.tag_configure("INFO", foreground="#2c3e50")      # 深蓝灰色
        self.log_text.tag_configure("WARNING", foreground="#f39c12")   # 明亮橙色
        self.log_text.tag_configure("ERROR", foreground="#e74c3c")     # 明亮红色
        self.log_text.tag_configure("SUCCESS", foreground="#27ae60")   # 明亮绿色
        self.log_text.tag_configure("DEBUG", foreground="#95a5a6")     # 中等灰色
        self.log_text.tag_configure("CRITICAL", foreground="#c0392b")  # 深红色

        # 为特殊情况添加字体样式
        self.log_text.tag_configure("ERROR", font=("Consolas", 9, "bold"))
        self.log_text.tag_configure("CRITICAL", font=("Consolas", 9, "bold"))
        self.log_text.tag_configure("SUCCESS", font=("Consolas", 9, "bold"))
    
    def _create_log_context_menu(self):
        """创建日志右键菜单"""
        if not self.log_text:
            return
            
        context_menu = tk.Menu(self.log_text, tearoff=0)
        context_menu.add_command(label="复制", command=self.copy_log)
        context_menu.add_command(label="全选", command=lambda: self.log_text.tag_add(tk.SEL, "1.0", tk.END))
        context_menu.add_separator()
        context_menu.add_command(label="搜索", command=self.search_log)
        context_menu.add_command(label="清空", command=self.clear_log)
        
        def show_context_menu(event):
            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()
        
        self.log_text.bind("<Button-3>", show_context_menu)
    
    def start_log_handler(self):
        """启动日志处理线程"""
        self.log_handler_running = True
        
        def log_handler():
            while self.log_handler_running:
                try:
                    # 从队列获取日志消息
                    log_entry = self.log_queue.get(timeout=0.1)
                    if log_entry:
                        # 安全地在主线程中更新UI
                        self._safe_update_ui(log_entry)
                except queue.Empty:
                    continue
                except Exception as e:
                    print(f"日志处理线程错误: {e}")
        
        self.log_handler_thread = threading.Thread(target=log_handler, daemon=True)
        self.log_handler_thread.start()

    def stop_log_handler(self):
        """停止日志处理线程"""
        try:
            self.log_handler_running = False
            if hasattr(self, 'log_handler_thread') and self.log_handler_thread and self.log_handler_thread.is_alive():
                self.log_handler_thread.join(timeout=1.0)
        except Exception as e:
            print(f"停止日志处理线程时出错: {e}")

    def _safe_update_ui(self, log_entry):
        """安全地更新UI，避免主线程错误"""
        try:
            # 检查主UI和root是否存在且有效
            if (hasattr(self, 'parent_ui') and
                self.parent_ui and
                hasattr(self.parent_ui, 'root') and
                self.parent_ui.root and
                self.parent_ui.root.winfo_exists()):

                # 在主线程中更新UI
                self.parent_ui.root.after(0, self._update_log_text, log_entry)
            else:
                # 如果UI不可用，直接写入文件
                self._write_to_file_only(log_entry)

        except Exception as e:
            # 如果UI更新失败，至少保证日志写入文件
            try:
                self._write_to_file_only(log_entry)
            except:
                pass  # 静默处理，避免无限错误循环

    def _write_to_file_only(self, log_entry):
        """仅写入文件，不更新UI"""
        try:
            message = log_entry.get("message", "")
            level = log_entry.get("level", "INFO")

            # 使用现有的文件写入方法
            self._write_to_file_log(message, level)

        except Exception:
            pass  # 静默处理文件写入错误

    def _update_log_text(self, log_entry):
        """更新日志文本框"""
        if not self.log_text:
            return
            
        try:
            # 检查并合并存稿成功相关消息
            merged_message = self._check_and_merge_draft_messages(log_entry.get("message", ""))
            if not merged_message:
                return
            
            # 应用日志过滤
            if not self._should_show_log(merged_message, log_entry.get("level", "INFO")):
                return
            
            # 格式化日志消息
            timestamp = log_entry.get("timestamp", datetime.datetime.now().strftime("%H:%M:%S"))
            level = log_entry.get("level", "INFO")
            formatted_message = f"[{timestamp}] [{level}] {merged_message}\n"
            
            # 插入日志文本
            self.log_text.configure(state=tk.NORMAL)
            
            # 获取插入位置
            insert_pos = self.log_text.index(tk.END + "-1c")
            
            # 插入文本
            self.log_text.insert(tk.END, formatted_message)

            # 应用颜色标签
            line_start = insert_pos
            line_end = self.log_text.index(tk.END + "-1c")
            self.log_text.tag_add(level, line_start, line_end)

            # 检查并限制日志行数
            self._check_and_limit_log_lines()

            # 根据设置决定是否自动滚动到底部
            if self.auto_scroll.get():
                self.log_text.see(tk.END)
            self.log_text.configure(state=tk.DISABLED)
            
        except Exception as e:
            print(f"更新日志文本错误: {e}")

    def _check_and_limit_log_lines(self):
        """检查并限制日志行数，超出时删除旧日志"""
        try:
            if not self.log_text:
                return

            max_lines = self.max_log_lines.get()
            if max_lines <= 0:  # 如果设置为0或负数，表示不限制
                return

            # 获取当前行数
            current_lines = int(self.log_text.index(tk.END).split('.')[0]) - 1

            if current_lines > max_lines:
                # 计算需要删除的行数
                lines_to_delete = current_lines - max_lines

                # 删除开头的旧日志行
                self.log_text.configure(state=tk.NORMAL)
                self.log_text.delete("1.0", f"{lines_to_delete + 1}.0")
                self.log_text.configure(state=tk.DISABLED)

        except Exception as e:
            print(f"限制日志行数时出错: {e}")

    def _check_and_merge_draft_messages(self, message):
        """检查并合并重复和相似的日志消息"""
        current_time = datetime.datetime.now()

        # 获取消息的关键特征用于分组
        message_key = self._get_message_key(message)

        # 如果是需要过滤的重复消息
        if self._should_filter_message(message):
            return None

        # 检查是否需要合并
        if message_key in self.message_cache:
            last_time = self.last_message_time.get(message_key)
            if last_time and (current_time - last_time).total_seconds() < self.merge_timeout:
                # 更新缓存计数
                self.message_cache[message_key]['count'] += 1
                self.message_cache[message_key]['last_message'] = message
                self.last_message_time[message_key] = current_time
                return None  # 不显示重复消息
            else:
                # 输出之前累积的消息汇总
                cached_info = self.message_cache[message_key]
                if cached_info['count'] > 1:
                    summary = self._create_summary_message(cached_info)
                    # 重置缓存
                    self.message_cache[message_key] = {'count': 1, 'last_message': message}
                    self.last_message_time[message_key] = current_time
                    # 只有当汇总消息不为None时才返回
                    if summary:
                        return summary

        # 新消息或超时消息
        self.message_cache[message_key] = {'count': 1, 'last_message': message}
        self.last_message_time[message_key] = current_time

        return message

    def _get_message_key(self, message):
        """获取消息的关键特征用于分组"""
        # 移除账号名、时间戳等变化部分，保留核心信息
        import re

        # 移除账号名 [账号名]
        message = re.sub(r'\[([^\]]+)\]\s*', '', message)

        # 移除端口号
        message = re.sub(r'\(端口:\s*\d+\)', '', message)

        # 移除具体的Cookie名称和域名
        message = re.sub(r'Cookie\s+\d+:\s*\w+\s*\(域名:[^)]+\)', 'Cookie处理', message)
        message = re.sub(r'Cookie添加成功:\s*\w+', 'Cookie添加成功', message)
        message = re.sub(r'正在添加Cookie:\s*\w+', '正在添加Cookie', message)
        message = re.sub(r'Cookie添加完成:\s*成功\s*\d+/\d+\s*个', 'Cookie添加完成', message)

        # 移除具体的文件路径，保留操作类型
        message = re.sub(r'[A-Z]:[/\\][^\\s]+', '[文件路径]', message)

        # 移除具体数字，保留操作类型
        message = re.sub(r'\d+\s*个', 'N个', message)
        message = re.sub(r'\d+/\d+\s*个', 'N/N个', message)
        message = re.sub(r'\d+\s*次', 'N次', message)
        message = re.sub(r'第\s*\d+\s*次', '第N次', message)
        message = re.sub(r'\d+\s*秒', 'N秒', message)

        # 移除具体的文件名，保留操作类型
        message = re.sub(r'[^/\\]+\.(mp4|jpg|png|jpeg)', '[文件名]', message)

        # 移除具体的URL，保留操作类型
        message = re.sub(r'https?://[^\s]+', '[URL]', message)

        # 移除时间戳
        message = re.sub(r'\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}', '[时间]', message)
        message = re.sub(r'\d{2}:\d{2}:\d{2}', '[时间]', message)

        # 移除具体的进度数值，保留操作类型
        message = re.sub(r'进度=\d+', '进度=N', message)
        message = re.sub(r'存稿成功=\d+', '存稿成功=N', message)

        # 移除具体的循环次数
        message = re.sub(r'第\s*\d+/\d+\s*次循环', '第N/N次循环', message)

        # 移除具体的检测次数和时间
        message = re.sub(r'第\d+次检测\s*\(\d+秒\)', '第N次检测', message)
        message = re.sub(r'超时时间:\s*\d+秒', '超时时间:N秒', message)
        message = re.sub(r'每\d+秒检测一次', '每N秒检测一次', message)
        message = re.sub(r'最多检测\d+次', '最多检测N次', message)

        # 移除具体的页面源码长度
        message = re.sub(r'长度:\s*\d+', '长度:N', message)

        return message.strip()

    def _should_filter_message(self, message):
        """判断是否应该过滤掉某些过于详细的消息"""

        filter_patterns = [
            # Cookie相关详细信息
            r'使用原有域名:',
            r'Cookie有效期正常:',
            r'处理Cookie\s+\d+:',
            r'正在添加Cookie:',
            r'开始添加Cookie到浏览器',
            r'清除浏览器中的所有现有Cookie',
            r'已清除所有现有Cookie',
            r'验证Cookie是否被正确添加',
            r'当前Cookie名称:',
            r'浏览器当前Cookie数量:',
            r'成功添加Cookie\s+\d+/\d+:',
            r'Cookie文件大小:\s*\d+\s*字符',
            r'Cookie文件内容预览:',
            r'Cookie文件内容:',
            r'正在加载TXT格式的Cookie文件',
            r'成功使用\s+\w+\s+编码读取文件',
            r'文件内容长度:\s*\d+\s*字符',
            r'文件内容预览:',
            r'开始解析Cookie字符串，长度:',
            r'Cookie内容前\d+字符:',
            r'成功解析为JSON格式',
            r'从JSON结构中提取cookies字段',
            r'转换字典格式cookies为列表',
            r'从TXT文件加载了\s*\d+\s*个cookies',
            r'cookies字段是列表格式',
            r'成功添加\s*\d+/\d+\s*个Cookies',
            r'找到Cookie文件:',
            r'\[toutiao\]\s*分配端口:',
            r'当前页面URL:',
            r'等待后的页面URL:',
            r'Cookie登录成功\s*-\s*URL匹配',
            r'Cookie登录成功\s*-\s*URL包含',
            r'Cookie登录成功\s*-\s*延迟检查确认',
            r'Cookie登录失败\s*-\s*当前URL:',
            r'期望URL:',
            r'页面被重定向到登录页面',
            r'等待页面重定向',

            # 初始化相关
            r'✅\s*视频分配跟踪器初始化成功',
            r'基本进度已更新:\s*进度=0',

            # 调试信息
            r'🔍\s*\[DEBUG\]',
            r'🔍\s*第\d+次检测',
            r'🔍\s*文字检测开始:',
            r'🔍\s*文字检测:',
            r'🔍\s*备用文字检测',
            r'🔍\s*检测配置:',
            r'🔍\s*目标文字:',
            r'🔍\s*实际文字:',
            r'🔍\s*XPath模式',

            # 重复的状态检查
            r'第\d+次URL检测',
            r'刷新后的URL:',
            r'等待\d+秒后继续',
            r'未发现上传完成，等待',

            # 文件操作详情
            r'格式化后的文件路径:',
            r'找到文件输入元素',
            r'已选择视频文件:',
            r'视频完整路径:',
            r'准备上传视频:',

            # 重复的进度信息
            r'已随机打乱\s*\d+\s*个视频文件的顺序',
            r'已将\s*\d+\s*个视频文件加入队列',
            r'为账号.*分配了\s*\d+\s*个可用视频',
            r'为账号.*分配了\s*\d+\s*个视频',
            r'找到\s*\d+\s*个视频文件',
            r'在目录.*中找到\s*\d+\s*个文件',

            # 重复的设置信息
            r'已设置存稿数量限制:',
            r'已设置循环次数限制:',

            # 封面处理详情
            r'已创建已处理封面文件副本:',
            r'找到封面文件:',
            r'使用已处理的封面文件:',

            # 页面导航详情
            r'当前不在上传页面，重新导航',
            r'正在打开网易视频上传页面',

            # 账号处理相关重复信息
            r'账号.*基本进度已更新:\s*进度=\d+',
            r'账号.*本次处理成功存稿数量:',
            r'账号.*从processor获取总存稿成功数量:',
            r'账号.*成功存稿\s*\d+\s*个视频',
            r'账号.*处理失败',
            r'账号.*处理完成',
            r'账号.*首次进度记录:',
            r'存稿成功数量更新:',
            r'进度数据已更新:',

            # 循环和任务相关
            r'====\s*开始第\s*\d+/\d+\s*次循环\s*====',
            r'从目录.*获取视频文件',
            r'开始循环处理视频文件',
            r'is_running状态：',

            # 浏览器和页面相关
            r'Cookie已添加，刷新页面让Cookie生效',
            r'正在加载Cookie',
            r'从JSON文件加载到\s*\d+\s*个cookies',

            # 其他冗余信息
            r'开始存稿任务，设置运行状态为True',
            r'开始存稿任务',
            r'等待上传视频',
            r'开始处理视频:',
            r'⏳\s*等待视频上传完成',
            r'🔍\s*开始改进的视频上传完成检测',
            r'超时时间:\s*\d+秒',
            r'每\d+秒检测一次',
            r'最多检测\d+次',
        ]

        for pattern in filter_patterns:
            if re.search(pattern, message):
                return True
        return False

    def _create_summary_message(self, cached_info):
        """创建汇总消息"""
        count = cached_info['count']
        last_message = cached_info['last_message']

        # 根据消息类型创建不同的汇总
        if 'Cookie添加成功' in last_message or 'Cookie添加完成' in last_message:
            return f"✅ 批量Cookie操作完成 (共{count}个)"
        elif '正在登录' in last_message:
            return f"🔐 批量账号登录中 (共{count}个账号)"
        elif '视频分配跟踪器初始化成功' in last_message:
            return f"✅ 批量初始化完成 (共{count}个账号)"
        elif '基本进度已更新' in last_message:
            return f"📊 批量进度更新 (共{count}个账号)"
        elif '存稿成功' in last_message:
            return f"🎉 批量存稿完成 (共{count}个视频)"
        elif '登录成功' in last_message:
            return f"✅ 批量登录完成 (共{count}个账号)"
        elif '登录失败' in last_message:
            return f"❌ 批量登录失败 (共{count}个账号)"
        elif '检测' in last_message and ('未发现' in last_message or '等待' in last_message):
            return f"⏳ 批量检测操作 (共{count}次检测)"
        elif '文字检测' in last_message or '备用文字检测' in last_message:
            return f"🔍 批量文字检测 (共{count}次检测)"
        elif 'URL检测' in last_message:
            return f"🔗 批量URL检测 (共{count}次检测)"
        elif '分配了' in last_message and '视频' in last_message:
            return f"📹 批量视频分配 (共{count}个账号)"
        elif '已创建' in last_message and '封面' in last_message:
            return f"🖼️ 批量封面处理 (共{count}个文件)"
        elif '基本进度已更新' in last_message:
            return f"📊 批量进度更新 (共{count}个账号)"
        elif '本次处理成功存稿数量' in last_message:
            return f"📈 批量存稿统计 (共{count}个账号)"
        elif '从processor获取总存稿成功数量' in last_message:
            return f"📊 批量数据获取 (共{count}个账号)"
        elif '处理完成' in last_message or '处理失败' in last_message:
            return f"✅ 批量账号处理 (共{count}个账号)"
        elif '开始第' in last_message and '次循环' in last_message:
            return f"🔄 批量循环处理 (共{count}次循环)"
        elif '从目录' in last_message and '获取视频文件' in last_message:
            return f"📁 批量文件获取 (共{count}次操作)"
        elif 'Cookie已添加' in last_message or '正在加载Cookie' in last_message:
            return f"🍪 批量Cookie处理 (共{count}次操作)"
        elif count >= 5:  # 对于大量重复的其他消息，只有超过5次才显示汇总
            return f"📝 批量操作完成 (共{count}次)"
        else:
            return None  # 少量重复消息不显示汇总，直接过滤

    def _cleanup_message_cache(self):
        """清理过期的消息缓存"""
        try:
            current_time = datetime.datetime.now()
            expired_keys = []

            for key, last_time in self.last_message_time.items():
                if (current_time - last_time).total_seconds() > 60:  # 60秒后清理
                    expired_keys.append(key)

            for key in expired_keys:
                self.message_cache.pop(key, None)
                self.last_message_time.pop(key, None)

            # 重新安排下次清理
            if hasattr(self.parent_ui, 'root') and self.parent_ui.root:
                self.parent_ui.root.after(30000, self._cleanup_message_cache)

        except Exception as e:
            print(f"清理消息缓存时出错: {e}")

    def _should_show_log(self, message, level):
        """判断是否应该显示日志"""
        # 级别过滤
        if self.enable_level_filter.get():
            if level in ["DEBUG"] and not logger.is_debug_enabled():
                return False
        
        # 关键词过滤
        if self.enable_keyword_filter.get():
            filtered_keywords = logger.get_filtered_keywords()
            for keyword in filtered_keywords:
                if keyword.lower() in message.lower():
                    return False
        
        return True

    def _detect_log_level(self, message):
        """根据消息内容自动检测日志级别"""
        message_lower = message.lower()

        # 警告级别关键词（优先检查，避免被错误级别误判）
        warning_keywords = [
            "⚠️", "警告", "warning", "注意", "小心", "建议", "提醒",
            "warn", "caution", "alert", "提示", "可能的问题", "可能", "或许"
        ]

        # 错误级别关键词
        error_keywords = [
            "❌", "错误", "失败", "error", "exception", "异常", "崩溃", "crash",
            "无法", "不能", "无效", "invalid", "failed", "failure"
        ]

        # 成功级别关键词
        success_keywords = [
            "✅", "成功", "完成", "success", "successful", "已完成", "已成功",
            "ok", "done", "finished", "已保存", "已创建", "已删除", "已更新"
        ]

        # 调试级别关键词
        debug_keywords = [
            "🔍", "调试", "debug", "trace", "详细", "检查", "测试", "验证",
            "正在", "开始", "结束", "步骤", "处理中"
        ]

        # 特殊处理：如果包含"可能"等不确定词汇，优先判断为警告
        if any(word in message_lower for word in ["可能", "或许", "也许", "建议", "提示"]):
            return "WARNING"

        # 按优先级检查（警告 > 错误 > 成功 > 调试）
        for keyword in warning_keywords:
            if keyword in message_lower:
                return "WARNING"

        for keyword in error_keywords:
            if keyword in message_lower:
                return "ERROR"

        for keyword in success_keywords:
            if keyword in message_lower:
                return "SUCCESS"

        for keyword in debug_keywords:
            if keyword in message_lower:
                return "DEBUG"

        # 默认返回INFO级别
        return "INFO"

    def log(self, message, level="INFO"):
        """记录日志 - 统一处理UI显示和文件记录"""
        # 自动识别日志级别（如果没有明确指定）
        if level == "INFO":
            level = self._detect_log_level(message)

        log_entry = {
            "message": message,
            "level": level,
            "timestamp": datetime.datetime.now().strftime("%H:%M:%S")
        }

        # 添加到UI队列用于显示
        self.log_queue.put(log_entry)

        # 直接记录到文件日志，避免通过全局logger造成循环调用
        self._write_to_file_log(message, level)

    def _write_to_file_log(self, message, level):
        """直接写入文件日志，避免通过全局logger"""
        try:
            # 确保日志目录存在
            log_dir = "logs"
            os.makedirs(log_dir, exist_ok=True)

            # 生成日志文件名（与全局logger保持一致）
            # 首先尝试找到今天已存在的日志文件
            today = datetime.datetime.now().strftime('%Y%m%d')
            existing_files = [f for f in os.listdir(log_dir) if f.startswith(f"netease_draft_{today}") and f.endswith('.log')]

            if existing_files:
                # 使用已存在的文件
                log_filename = sorted(existing_files)[-1]  # 使用最新的文件
            else:
                # 创建新的日志文件
                log_filename = f"netease_draft_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

            log_path = os.path.join(log_dir, log_filename)

            # 格式化日志消息
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            formatted_message = f"[{timestamp}] [{level}] {message}\n"

            # 写入文件
            with open(log_path, 'a', encoding='utf-8') as f:
                f.write(formatted_message)

        except Exception as e:
            # 如果文件写入失败，只打印到控制台，不再递归调用
            print(f"写入日志文件失败: {e}")
    
    def clear_log(self):
        """清空日志"""
        try:
            if self.log_text:
                self.log_text.configure(state=tk.NORMAL)
                self.log_text.delete(1.0, tk.END)
                self.log_text.configure(state=tk.DISABLED)
                self.log("日志已清空")
        except Exception as e:
            messagebox.showerror("错误", f"清空日志失败: {e}")
    
    def copy_log(self):
        """复制日志内容到剪贴板"""
        try:
            if not self.log_text:
                return
                
            # 获取选中的文本，如果没有选中则复制全部
            try:
                selected_text = self.log_text.get(tk.SEL_FIRST, tk.SEL_LAST)
            except tk.TclError:
                # 没有选中文本，复制全部
                selected_text = self.log_text.get(1.0, tk.END)
            
            if selected_text:
                self.parent_ui.root.clipboard_clear()
                self.parent_ui.root.clipboard_append(selected_text)
                self.log("日志内容已复制到剪贴板")
        except Exception as e:
            messagebox.showerror("错误", f"复制日志失败: {e}")

    def search_log(self):
        """搜索日志内容 - 基础搜索"""
        try:
            # 创建搜索对话框
            search_dialog = tk.Toplevel(self.parent_ui.root)
            search_dialog.title("搜索日志")
            search_dialog.geometry("400x180")
            search_dialog.resizable(False, False)  # 禁止调整大小
            search_dialog.transient(self.parent_ui.root)
            search_dialog.grab_set()

            # 居中显示
            self.parent_ui._center_window(search_dialog)

            # 主框架
            main_frame = ttk.Frame(search_dialog)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

            # 搜索框架
            search_frame = ttk.Frame(main_frame)
            search_frame.pack(fill=tk.X)

            # 搜索关键词
            ttk.Label(search_frame, text="搜索关键词:").pack(side=tk.LEFT, padx=(0, 5))
            search_var = tk.StringVar()
            search_entry = ttk.Entry(search_frame, textvariable=search_var, width=30)
            search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
            search_entry.focus()

            # 搜索选项
            options_frame = ttk.Frame(main_frame)
            options_frame.pack(fill=tk.X, pady=(10, 0))

            # 搜索选项
            case_sensitive_var = tk.BooleanVar(value=False)
            case_checkbox = create_beautiful_checkbox(
                options_frame,
                text="区分大小写",
                variable=case_sensitive_var,
                style_type="modern"
            )
            case_checkbox.pack(side=tk.LEFT, padx=(0, 15))

            highlight_all_var = tk.BooleanVar(value=True)
            highlight_checkbox = create_beautiful_checkbox(
                options_frame,
                text="高亮所有匹配项",
                variable=highlight_all_var,
                style_type="accent"
            )
            highlight_checkbox.pack(side=tk.LEFT)

            # 结果显示
            result_frame = ttk.Frame(main_frame)
            result_frame.pack(fill=tk.X, pady=(10, 0))

            result_label = ttk.Label(result_frame, text="",
                                    font=("微软雅黑", 9), foreground="gray")
            result_label.pack(side=tk.LEFT)

            def do_search():
                keyword = search_var.get().strip()

                if not keyword:
                    result_label.config(text="请输入搜索关键词", foreground="orange")
                    return

                if not self.log_text:
                    result_label.config(text="没有可搜索的日志内容", foreground="red")
                    return

                # 清除之前的高亮
                self.log_text.tag_remove("search_highlight", "1.0", tk.END)

                # 搜索参数
                search_kwargs = {
                    'nocase': not case_sensitive_var.get()
                }

                # 搜索并高亮所有匹配项
                start_pos = "1.0"
                count = 0

                while True:
                    pos = self.log_text.search(keyword, start_pos, tk.END, **search_kwargs)
                    if not pos:
                        break

                    end_pos = f"{pos}+{len(keyword)}c"

                    # 高亮匹配项
                    if highlight_all_var.get():
                        self.log_text.tag_add("search_highlight", pos, end_pos)

                    # 如果是第一个匹配项，滚动到该位置
                    if count == 0:
                        self.log_text.see(pos)

                    start_pos = end_pos
                    count += 1

                # 更新结果显示
                if count > 0:
                    result_label.config(text=f"找到 {count} 个匹配项", foreground="green")
                else:
                    result_label.config(text="未找到匹配项", foreground="red")

                # 配置高亮样式
                self.log_text.tag_configure("search_highlight", background="yellow", foreground="black")

            # 按钮框架
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X, pady=(10, 0))

            # 左侧：清除高亮按钮
            def clear_highlight():
                if self.log_text:
                    self.log_text.tag_remove("search_highlight", "1.0", tk.END)

            clear_btn = ttk.Button(button_frame, text="清除高亮", command=clear_highlight)
            clear_btn.pack(side=tk.LEFT)

            # 右侧：关闭按钮
            close_btn = ttk.Button(button_frame, text="关闭", command=search_dialog.destroy)
            close_btn.pack(side=tk.RIGHT)

            # 执行搜索
            do_search()

            # 绑定回车键搜索
            search_entry.bind("<Return>", lambda e: do_search())

            ttk.Button(button_frame, text="搜索", command=do_search).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="取消", command=search_dialog.destroy).pack(side=tk.LEFT, padx=5)

            # 绑定回车键
            search_entry.bind("<Return>", lambda e: do_search())

        except Exception as e:
            messagebox.showerror("错误", f"搜索日志失败: {e}")

    def save_log_to_file(self):
        """保存日志到文件"""
        try:
            if not self.log_text:
                return

            # 选择保存位置
            filename = filedialog.asksaveasfilename(
                title="保存日志文件",
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )

            if filename:
                # 获取日志内容
                log_content = self.log_text.get(1.0, tk.END)

                # 写入文件
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(f"日志导出时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write("=" * 50 + "\n\n")
                    f.write(log_content)

                self.log(f"日志已保存到: {filename}")

        except Exception as e:
            messagebox.showerror("错误", f"保存日志失败: {e}")

    def show_log_statistics(self):
        """显示日志统计信息"""
        try:
            if not self.log_text:
                return

            # 获取日志内容
            log_content = self.log_text.get(1.0, tk.END)
            lines = log_content.split('\n')

            # 统计信息
            total_lines = len([line for line in lines if line.strip()])
            info_count = log_content.count('[INFO]')
            warning_count = log_content.count('[WARNING]')
            error_count = log_content.count('[ERROR]')
            success_count = log_content.count('[SUCCESS]')

            # 创建统计窗口
            stats_window = tk.Toplevel(self.parent_ui.root)
            stats_window.title("日志统计")
            stats_window.geometry("300x200")
            stats_window.transient(self.parent_ui.root)
            stats_window.grab_set()

            # 居中显示
            self.parent_ui._center_window(stats_window)

            # 统计信息显示
            stats_frame = ttk.Frame(stats_window)
            stats_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

            ttk.Label(stats_frame, text="📊 日志统计信息", font=("微软雅黑", 12, "bold")).pack(pady=(0, 15))

            stats_text = f"""总行数: {total_lines}
信息日志: {info_count}
警告日志: {warning_count}
错误日志: {error_count}
成功日志: {success_count}"""

            ttk.Label(stats_frame, text=stats_text, justify=tk.LEFT).pack()

            ttk.Button(stats_frame, text="关闭", command=stats_window.destroy).pack(pady=(15, 0))

        except Exception as e:
            messagebox.showerror("错误", f"显示日志统计失败: {e}")

    def show_log_settings(self):
        """显示日志配置对话框"""
        try:
            # 创建日志配置对话框
            settings_dialog = tk.Toplevel(self.parent_ui.root)
            settings_dialog.title("⚙️ 日志设置")
            settings_dialog.geometry("750x600")  # 增加宽度和高度以适应新布局
            settings_dialog.resizable(True, True)  # 允许调整大小，提高适应性
            settings_dialog.minsize(700, 550)  # 设置最小尺寸，防止内容被压缩
            settings_dialog.transient(self.parent_ui.root)
            settings_dialog.grab_set()

            # 居中显示
            self.parent_ui._center_window(settings_dialog)

            # 创建设置内容
            self._create_log_settings_content(settings_dialog)

        except Exception as e:
            messagebox.showerror("错误", f"显示日志设置失败: {e}")

    def _add_mousewheel_support(self, widget):
        """为控件添加鼠标滚轮支持"""
        def _on_mousewheel(event):
            # 查找可滚动的控件
            for child in widget.winfo_children():
                if hasattr(child, 'yview_scroll'):
                    try:
                        child.yview_scroll(int(-1*(event.delta/120)), "units")
                        break
                    except:
                        pass
                elif hasattr(child, 'winfo_children'):
                    self._add_mousewheel_support_recursive(child, event)

        def _bind_mousewheel(event):
            widget.bind_all("<MouseWheel>", _on_mousewheel)

        def _unbind_mousewheel(event):
            widget.unbind_all("<MouseWheel>")

        widget.bind('<Enter>', _bind_mousewheel)
        widget.bind('<Leave>', _unbind_mousewheel)

    def _add_mousewheel_support_recursive(self, widget, event):
        """递归为子控件添加滚轮支持"""
        if hasattr(widget, 'yview_scroll'):
            try:
                widget.yview_scroll(int(-1*(event.delta/120)), "units")
                return True
            except:
                pass

        for child in widget.winfo_children():
            if self._add_mousewheel_support_recursive(child, event):
                return True
        return False

    def _create_log_settings_content(self, parent):
        """创建日志设置内容"""
        # 主容器 - 紧凑布局
        main_frame = ttk.Frame(parent)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        # 标题区域 - 更紧凑
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 10))

        title_label = ttk.Label(title_frame, text="⚙️ 日志配置",
                               font=("微软雅黑", 12, "bold"))
        title_label.pack(side=tk.LEFT)

        # 添加帮助按钮
        help_btn = ttk.Button(title_frame, text="❓",
                             command=self.show_log_help, width=3)
        help_btn.pack(side=tk.RIGHT)

        # 按钮区域先创建并固定在底部
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(10, 0))

        # 创建内容区域 - 填充剩余空间，但不覆盖按钮
        content_container = ttk.Frame(main_frame)
        content_container.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 创建设置卡片容器 - 使用expand=True充分利用垂直空间
        cards_container = ttk.Frame(content_container)
        cards_container.pack(fill=tk.BOTH, expand=True, pady=5)

        # 创建两列布局 - 使用更均衡的布局
        left_column = ttk.Frame(cards_container)
        left_column.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 8))

        right_column = ttk.Frame(cards_container)
        right_column.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(8, 0))

        # 左侧：重置按钮
        reset_btn = ttk.Button(button_frame, text="🔄 重置默认",
                              command=self._reset_log_settings)
        reset_btn.pack(side=tk.LEFT)

        # 右侧：保存和取消按钮 - 修复按钮顺序
        cancel_btn = ttk.Button(button_frame, text="❌ 取消",
                               command=parent.destroy)
        cancel_btn.pack(side=tk.RIGHT)

        save_btn = ttk.Button(button_frame, text="💾 保存",
                             command=lambda: self._save_log_settings(parent))
        save_btn.pack(side=tk.RIGHT, padx=(0, 5))

        # 左列：基本显示设置
        basic_card = ttk.LabelFrame(left_column, text="🎨 基本设置", padding=10)
        basic_card.pack(fill=tk.X, pady=(0, 10))

        simple_mode_checkbox = create_beautiful_checkbox(
            basic_card,
            text="极简日志模式",
            variable=self.simple_log_mode,
            style_type="success"
        )
        simple_mode_checkbox.pack(anchor=tk.W, pady=(0, 8))

        # 自动滚动选项
        auto_scroll_checkbox = create_beautiful_checkbox(
            basic_card,
            text="自动滚动到底部",
            variable=self.auto_scroll,
            style_type="info"
        )
        auto_scroll_checkbox.pack(anchor=tk.W, pady=(0, 8))

        # 最大日志行数设置
        max_lines_frame = ttk.Frame(basic_card)
        max_lines_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(max_lines_frame, text="最大日志行数:",
                 font=("微软雅黑", 9)).pack(anchor=tk.W, pady=(0, 3))

        # 创建行数输入框和说明的容器
        lines_input_frame = ttk.Frame(max_lines_frame)
        lines_input_frame.pack(fill=tk.X)

        # 行数输入框
        lines_spinbox = tk.Spinbox(
            lines_input_frame,
            from_=0, to=10000,
            textvariable=self.max_log_lines,
            width=8,
            font=("微软雅黑", 9)
        )
        lines_spinbox.pack(side=tk.LEFT)

        # 说明标签
        lines_desc = ttk.Label(lines_input_frame,
                              text="行 (0=不限制)",
                              font=("微软雅黑", 8), foreground="gray")
        lines_desc.pack(side=tk.LEFT, padx=(5, 0))

        # 日志文件管理卡片
        file_card = ttk.LabelFrame(left_column, text="📁 文件管理", padding=10)
        file_card.pack(fill=tk.X, pady=(0, 0))

        # 日志文件管理
        file_mgmt_frame = ttk.Frame(file_card)
        file_mgmt_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(file_mgmt_frame, text="文件操作:",
                 font=("微软雅黑", 9)).pack(anchor=tk.W, pady=(0, 5))

        # 文件管理按钮区域 - 使用更紧凑的布局
        file_buttons_frame = ttk.Frame(file_mgmt_frame)
        file_buttons_frame.pack(fill=tk.X)

        # 第一行按钮 - 调整按钮大小
        file_buttons_row1 = ttk.Frame(file_buttons_frame)
        file_buttons_row1.pack(fill=tk.X, pady=(0, 2))

        open_folder_btn = ttk.Button(file_buttons_row1, text="📁 打开文件夹",
                                    command=self.open_log_folder, width=12)
        open_folder_btn.pack(side=tk.LEFT, padx=(0, 3))

        view_current_btn = ttk.Button(file_buttons_row1, text="📄 查看日志",
                                     command=self.view_current_log, width=12)
        view_current_btn.pack(side=tk.LEFT)

        # 第二行按钮
        file_buttons_row2 = ttk.Frame(file_buttons_frame)
        file_buttons_row2.pack(fill=tk.X)

        cleanup_btn = ttk.Button(file_buttons_row2, text="🗑️ 清理旧日志",
                                command=self.cleanup_old_logs, width=12)
        cleanup_btn.pack(side=tk.LEFT, padx=(0, 3))

        export_btn = ttk.Button(file_buttons_row2, text="📤 导出日志",
                               command=self.export_current_log, width=12)
        export_btn.pack(side=tk.LEFT)

        # 右列：日志级别过滤设置
        level_card = ttk.LabelFrame(right_column, text="📊 日志级别过滤", padding=10)
        level_card.pack(fill=tk.X, pady=(0, 10))

        level_filter_checkbox = create_beautiful_checkbox(
            level_card,
            text="启用日志级别过滤",
            variable=self.enable_level_filter,
            style_type="modern"
        )
        level_filter_checkbox.pack(anchor=tk.W, pady=(0, 8))

        # 日志级别选择
        level_select_frame = ttk.Frame(level_card)
        level_select_frame.pack(fill=tk.X, pady=(0, 8))

        ttk.Label(level_select_frame, text="最小日志级别:",
                 font=("微软雅黑", 9)).pack(anchor=tk.W, pady=(0, 3))
        level_combo = ttk.Combobox(level_select_frame, textvariable=self.min_log_level,
                                  values=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
                                  state="readonly", width=15, font=("微软雅黑", 9))
        level_combo.pack(anchor=tk.W, pady=(0, 3))

        # 级别说明
        level_desc = ttk.Label(level_select_frame,
                              text="只显示此级别及以上的日志",
                              font=("微软雅黑", 8), foreground="gray")
        level_desc.pack(anchor=tk.W)

        # 关键词过滤设置
        keyword_card = ttk.LabelFrame(right_column, text="🔍 关键词过滤", padding=10)
        keyword_card.pack(fill=tk.X, pady=(0, 10))

        keyword_filter_checkbox = create_beautiful_checkbox(
            keyword_card,
            text="启用关键词过滤",
            variable=self.enable_keyword_filter,
            style_type="accent"
        )
        keyword_filter_checkbox.pack(anchor=tk.W, pady=(0, 8))

        # 关键词管理按钮
        keyword_mgmt_frame = ttk.Frame(keyword_card)
        keyword_mgmt_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(keyword_mgmt_frame, text="关键词管理:",
                 font=("微软雅黑", 9)).pack(anchor=tk.W, pady=(0, 3))
        keyword_btn = ttk.Button(keyword_mgmt_frame, text="管理过滤关键词",
                               command=self.manage_filtered_keywords, width=18)
        keyword_btn.pack(anchor=tk.W, pady=(0, 3))

        # 关键词说明
        keyword_desc = ttk.Label(keyword_mgmt_frame,
                                text="隐藏包含指定关键词的日志",
                                font=("微软雅黑", 8), foreground="gray")
        keyword_desc.pack(anchor=tk.W)

        # 使用提示和帮助卡片
        help_card = ttk.LabelFrame(right_column, text="💡 使用提示", padding=10)
        help_card.pack(fill=tk.BOTH, expand=True, pady=(0, 0))

        # 功能说明
        features_frame = ttk.Frame(help_card)
        features_frame.pack(fill=tk.X, pady=(0, 8))

        features_text = ttk.Label(features_frame,
                                 text="🎨 基本设置：\n"
                                      "• 极简模式：隐藏详细调试信息\n"
                                      "• 自动滚动：新日志自动滚动到底部\n"
                                      "• 最大行数：超出时自动删除旧日志\n\n"
                                      "📊 级别过滤：\n"
                                      "• 优先级：CRITICAL > ERROR > WARNING > INFO > DEBUG\n"
                                      "• 只显示选定级别及以上的日志\n\n"
                                      "🔍 关键词过滤：\n"
                                      "• 支持多个关键词过滤\n"
                                      "• 隐藏包含指定关键词的日志\n\n"
                                      "📁 文件管理：\n"
                                      "• 查看、导出和清理日志文件",
                                 font=("微软雅黑", 8), foreground="gray",
                                 justify=tk.LEFT)
        features_text.pack(anchor=tk.W)

        # 快捷操作按钮
        quick_actions_frame = ttk.Frame(help_card)
        quick_actions_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Label(quick_actions_frame, text="🚀 快捷操作:",
                 font=("微软雅黑", 9, "bold")).pack(anchor=tk.W, pady=(0, 5))

        # 快捷按钮行
        quick_btn_frame = ttk.Frame(quick_actions_frame)
        quick_btn_frame.pack(fill=tk.X)

        ttk.Button(quick_btn_frame, text="清空日志",
                  command=self.parent_ui.log_manager.clear_log, width=10).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(quick_btn_frame, text="搜索日志",
                  command=self.search_log, width=10).pack(side=tk.LEFT, padx=(0, 5))



    def _reset_log_settings(self):
        """重置日志设置为默认值"""
        try:
            # 重置为默认值
            self.enable_level_filter.set(False)
            self.enable_keyword_filter.set(False)
            self.min_log_level.set("INFO")
            self.simple_log_mode.set(False)
            self.auto_scroll.set(True)  # 默认启用自动滚动
            self.max_log_lines.set(1000)  # 默认最大1000行

            # 清空过滤关键词
            if hasattr(logger, 'clear_filtered_keywords'):
                logger.clear_filtered_keywords()

            self.parent_ui.log("🔄 日志设置已重置为默认值")

        except Exception as e:
            messagebox.showerror("错误", f"重置日志设置失败: {e}")

    def _save_log_settings(self, dialog):
        """保存日志设置"""
        try:
            # 保存所有日志设置到通用配置
            self.config_manager.set("enable_level_filter", self.enable_level_filter.get(), platform="common")
            self.config_manager.set("enable_keyword_filter", self.enable_keyword_filter.get(), platform="common")
            self.config_manager.set("min_log_level", self.min_log_level.get(), platform="common")
            self.config_manager.set("simple_log_mode", self.simple_log_mode.get(), platform="common")
            self.config_manager.set("auto_scroll", self.auto_scroll.get(), platform="common")
            self.config_manager.set("max_log_lines", self.max_log_lines.get(), platform="common")
            self.config_manager.set("max_log_lines", self.max_log_lines.get(), platform="common")

            # 保存过滤关键词
            if hasattr(logger, 'get_filtered_keywords'):
                filtered_keywords = list(logger.get_filtered_keywords())
                self.config_manager.set("filtered_keywords", filtered_keywords, platform="common")

            # 应用设置
            logger.enable_level_filtering(self.enable_level_filter.get())
            logger.enable_keyword_filtering(self.enable_keyword_filter.get())
            logger.set_min_level(self.min_log_level.get())

            # 保存配置到文件
            self.config_manager.save_config()

            self.parent_ui.log("✅ 日志设置已保存")
            dialog.destroy()

        except Exception as e:
            messagebox.showerror("错误", f"保存日志设置失败: {e}")

    def manage_filtered_keywords(self):
        """管理过滤关键词"""
        try:
            # 创建关键词管理对话框
            keyword_dialog = tk.Toplevel(self.parent_ui.root)
            keyword_dialog.title("管理过滤关键词")
            keyword_dialog.geometry("400x300")
            keyword_dialog.transient(self.parent_ui.root)
            keyword_dialog.grab_set()

            # 居中显示
            self.parent_ui._center_window(keyword_dialog)

            # 主框架
            main_frame = ttk.Frame(keyword_dialog)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

            # 说明
            ttk.Label(main_frame, text="管理日志过滤关键词", font=("微软雅黑", 12, "bold")).pack(pady=(0, 10))
            ttk.Label(main_frame, text="添加需要过滤的关键词，每行一个").pack(pady=(0, 10))

            # 关键词文本框
            text_frame = ttk.Frame(main_frame)
            text_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

            keyword_text = tk.Text(text_frame, height=10, width=40)

            # 添加滚轮支持
            def _on_mousewheel(event):
                keyword_text.yview_scroll(int(-1*(event.delta/120)), "units")

            def _bind_mousewheel(event):
                keyword_text.bind_all("<MouseWheel>", _on_mousewheel)

            def _unbind_mousewheel(event):
                keyword_text.unbind_all("<MouseWheel>")

            keyword_text.bind('<Enter>', _bind_mousewheel)
            keyword_text.bind('<Leave>', _unbind_mousewheel)

            keyword_text.pack(fill=tk.BOTH, expand=True)

            # 加载现有关键词
            keywords = logger.get_filtered_keywords() if hasattr(logger, 'get_filtered_keywords') else []
            keyword_text.insert("1.0", "\n".join(keywords))

            # 按钮框架
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X)

            def save_keywords():
                keywords = keyword_text.get("1.0", tk.END).strip().split("\n")
                keywords = [k.strip() for k in keywords if k.strip()]
                if hasattr(logger, 'set_filtered_keywords'):
                    logger.set_filtered_keywords(keywords)

                # 保存到配置
                self.config_manager.set("filtered_keywords", keywords, platform="common")
                self.config_manager.save_config()

                self.parent_ui.log(f"✅ 已更新过滤关键词，共 {len(keywords)} 个")
                keyword_dialog.destroy()

            # 添加常用关键词按钮
            def add_common_keywords():
                common_keywords = [
                    "正在查找", "已找到", "准备点击", "正在输入标签",
                    "正在检查上传状态", "正在检查页面状态", "等待页面加载",
                    "正在滚动页面", "正在等待元素出现"
                ]
                current_text = keyword_text.get("1.0", tk.END).strip()
                if current_text:
                    keyword_text.insert(tk.END, "\n" + "\n".join(common_keywords))
                else:
                    keyword_text.insert("1.0", "\n".join(common_keywords))

            ttk.Button(button_frame, text="添加常用", command=add_common_keywords).pack(side=tk.LEFT)
            ttk.Button(button_frame, text="保存", command=save_keywords).pack(side=tk.RIGHT, padx=(5, 0))
            ttk.Button(button_frame, text="取消", command=keyword_dialog.destroy).pack(side=tk.RIGHT)

        except Exception as e:
            messagebox.showerror("错误", f"管理过滤关键词失败: {e}")

        except Exception as e:
            messagebox.showerror("错误", f"管理过滤关键词失败: {e}")

    def show_log_help(self):
        """显示日志配置帮助"""
        try:
            # 创建帮助对话框
            help_dialog = tk.Toplevel(self.parent_ui.root)
            help_dialog.title("📖 日志设置帮助")
            help_dialog.geometry("700x600")
            help_dialog.transient(self.parent_ui.root)
            help_dialog.grab_set()

            # 居中显示
            self.parent_ui._center_window(help_dialog)

            # 主框架
            main_frame = ttk.Frame(help_dialog)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

            # 标题
            title_label = ttk.Label(main_frame, text="📖 日志设置帮助文档",
                                   font=("微软雅黑", 16, "bold"))
            title_label.pack(pady=(0, 20))

            # 创建滚动文本区域
            text_frame = ttk.Frame(main_frame)
            text_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

            help_text = tk.Text(text_frame, wrap=tk.WORD, font=("微软雅黑", 10),
                               relief=tk.FLAT, borderwidth=1, padx=15, pady=15)

            # 添加滚轮支持
            def _on_mousewheel(event):
                help_text.yview_scroll(int(-1*(event.delta/120)), "units")

            def _bind_mousewheel(event):
                help_text.bind_all("<MouseWheel>", _on_mousewheel)

            def _unbind_mousewheel(event):
                help_text.unbind_all("<MouseWheel>")

            help_text.bind('<Enter>', _bind_mousewheel)
            help_text.bind('<Leave>', _unbind_mousewheel)

            help_text.pack(fill=tk.BOTH, expand=True)

            # 帮助内容
            help_content = """
🔍 日志过滤设置

📊 日志级别过滤
• 启用后，只显示指定级别及以上的日志信息
• 级别从低到高：DEBUG → INFO → WARNING → ERROR → CRITICAL
• 建议设置：
  - 开发调试时：DEBUG
  - 正常使用时：INFO
  - 只看重要信息：WARNING

🔍 关键词过滤
• 启用后，包含指定关键词的日志将被隐藏
• 支持多个关键词，每行一个
• 常用过滤关键词：
  - "正在查找" - 隐藏元素查找过程
  - "已找到" - 隐藏元素找到提示
  - "准备点击" - 隐藏点击准备信息
  - "正在输入标签" - 隐藏标签输入过程

🎨 显示设置

✨ 极简日志模式
• 启用后隐藏详细的调试信息
• 只显示重要的操作结果和错误信息
• 适合不需要查看详细过程的用户

⚡ 快速操作

🗑️ 清空日志
• 清除当前显示的所有日志内容
• 不影响已保存的日志文件

📤 导出日志
• 将当前日志内容导出为文本文件
• 可选择导出全部或指定时间段的日志

📋 复制日志
• 将当前显示的日志内容复制到剪贴板
• 方便分享或保存到其他地方

🔍 搜索日志
• 在日志内容中搜索指定关键词
• 支持高亮显示搜索结果
• 可快速定位到相关日志

🔄 重置默认
• 将所有设置恢复为默认值
• 清空自定义的过滤关键词
• 重置为推荐的配置

💡 使用建议

1. 首次使用建议保持默认设置
2. 如果日志过多，可启用关键词过滤
3. 调试问题时，建议设置日志级别为DEBUG
4. 正常使用时，建议启用极简日志模式
5. 定期导出重要日志以备查看

❓ 常见问题

Q: 为什么设置了过滤但还是显示某些日志？
A: 请检查日志级别设置，ERROR和CRITICAL级别的日志通常不会被过滤

Q: 如何恢复被过滤的日志？
A: 在关键词管理中删除相应关键词，或使用"重置默认"功能

Q: 日志文件保存在哪里？
A: 日志文件保存在程序目录的logs文件夹中，按日期命名

Q: 如何提高日志查看性能？
A: 启用极简日志模式，并适当设置关键词过滤
"""

            # 插入帮助内容
            help_text.insert("1.0", help_content)
            help_text.configure(state="disabled")  # 设置为只读

            # 配置文本样式
            help_text.tag_configure("title", font=("微软雅黑", 12, "bold"), foreground="#2c3e50")
            help_text.tag_configure("subtitle", font=("微软雅黑", 11, "bold"), foreground="#3498db")
            help_text.tag_configure("content", font=("微软雅黑", 10), foreground="#34495e")

            # 按钮框架
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X)

            ttk.Button(button_frame, text="关闭", command=help_dialog.destroy).pack(side=tk.RIGHT)

        except Exception as e:
            messagebox.showerror("错误", f"显示帮助失败: {e}")

    def show_progress_dialog(self):
        """显示进度条对话框"""
        try:
            # 委托给主UI处理
            if hasattr(self.parent_ui, 'show_progress_dialog'):
                self.parent_ui.show_progress_dialog()
            else:
                messagebox.showinfo("提示", "进度功能暂不可用")
        except Exception as e:
            messagebox.showerror("错误", f"显示进度对话框失败: {e}")

    def reset_log_settings(self):
        """重置日志配置"""
        try:
            # 重置为默认值
            self.enable_level_filter.set(False)
            self.enable_keyword_filter.set(False)
            self.min_log_level.set("INFO")
            self.simple_log_mode.set(False)

            # 保存配置
            self.config_manager.set("enable_level_filter", False, platform="common")
            self.config_manager.set("enable_keyword_filter", False, platform="common")
            self.config_manager.set("min_log_level", "INFO", platform="common")
            self.config_manager.set("simple_log_mode", False, platform="common")

            # 重置过滤关键词为默认值
            default_keywords = [
                "正在查找", "已找到", "准备点击", "正在输入标签",
                "正在检查上传状态", "正在检查页面状态", "等待页面加载",
                "正在滚动页面", "正在等待元素出现"
            ]
            self.config_manager.set("filtered_keywords", default_keywords, platform="common")
            if hasattr(logger, 'set_filtered_keywords'):
                logger.set_filtered_keywords(default_keywords)

            # 应用设置
            logger.enable_level_filtering(False)
            logger.enable_keyword_filtering(False)
            logger.set_min_level("INFO")

            # 保存配置到文件
            self.config_manager.save_config()

            self.parent_ui.log("✅ 日志设置已重置为默认值")

        except Exception as e:
            messagebox.showerror("错误", f"重置日志设置失败: {e}")



    def open_log_folder(self):
        """打开日志文件夹"""
        try:
            import os
            import subprocess
            import platform

            # 获取日志文件夹路径
            log_folder = self._get_log_folder_path()

            if not os.path.exists(log_folder):
                os.makedirs(log_folder, exist_ok=True)
                self.parent_ui.log(f"📁 已创建日志文件夹: {log_folder}")

            # 根据操作系统打开文件夹
            system = platform.system()
            if system == "Windows":
                os.startfile(log_folder)
            elif system == "Darwin":  # macOS
                subprocess.run(["open", log_folder])
            else:  # Linux
                subprocess.run(["xdg-open", log_folder])

            self.parent_ui.log(f"📁 已打开日志文件夹: {log_folder}")

        except Exception as e:
            messagebox.showerror("错误", f"打开日志文件夹失败: {e}")

    def view_current_log(self):
        """查看当前日志文件"""
        try:
            import os
            import subprocess
            import platform

            # 获取当前日志文件路径
            current_log_file = self._get_current_log_file_path()

            if not os.path.exists(current_log_file):
                messagebox.showwarning("提示", "当前日志文件不存在")
                return

            # 根据操作系统打开文件
            system = platform.system()
            if system == "Windows":
                os.startfile(current_log_file)
            elif system == "Darwin":  # macOS
                subprocess.run(["open", current_log_file])
            else:  # Linux
                subprocess.run(["xdg-open", current_log_file])

            self.parent_ui.log(f"📄 已打开当前日志文件: {current_log_file}")

        except Exception as e:
            messagebox.showerror("错误", f"打开当前日志文件失败: {e}")

    def cleanup_old_logs(self):
        """清理旧日志文件"""
        try:
            import os
            import datetime
            from tkinter import simpledialog

            # 询问保留天数
            days_to_keep = simpledialog.askinteger(
                "清理旧日志",
                "请输入要保留的日志天数：",
                initialvalue=7,
                minvalue=1,
                maxvalue=365
            )

            if days_to_keep is None:
                return

            log_folder = self._get_log_folder_path()
            if not os.path.exists(log_folder):
                messagebox.showinfo("提示", "日志文件夹不存在")
                return

            # 计算截止日期
            cutoff_date = datetime.datetime.now() - datetime.timedelta(days=days_to_keep)

            # 清理旧文件
            deleted_count = 0
            total_size = 0

            for filename in os.listdir(log_folder):
                if filename.endswith('.log') or filename.endswith('.txt'):
                    file_path = os.path.join(log_folder, filename)
                    try:
                        # 获取文件修改时间
                        file_time = datetime.datetime.fromtimestamp(os.path.getmtime(file_path))

                        if file_time < cutoff_date:
                            file_size = os.path.getsize(file_path)
                            os.remove(file_path)
                            deleted_count += 1
                            total_size += file_size

                    except Exception as e:
                        print(f"删除文件 {filename} 失败: {e}")

            # 显示清理结果
            size_mb = total_size / (1024 * 1024)
            result_msg = f"清理完成！\n删除了 {deleted_count} 个文件\n释放空间: {size_mb:.2f} MB"
            messagebox.showinfo("清理结果", result_msg)

            self.parent_ui.log(f"🗑️ 清理旧日志完成，删除 {deleted_count} 个文件，释放 {size_mb:.2f} MB")

        except Exception as e:
            messagebox.showerror("错误", f"清理旧日志失败: {e}")

    def export_current_log(self):
        """导出当前日志"""
        try:
            from tkinter import filedialog
            import datetime
            import shutil

            # 获取当前日志文件
            current_log_file = self._get_current_log_file_path()

            if not os.path.exists(current_log_file):
                messagebox.showwarning("提示", "当前日志文件不存在")
                return

            # 选择保存位置
            default_filename = f"日志导出_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
            export_path = filedialog.asksaveasfilename(
                title="导出日志文件",
                defaultextension=".log",
                filetypes=[("日志文件", "*.log"), ("文本文件", "*.txt"), ("所有文件", "*.*")],
                initialname=default_filename
            )

            if export_path:
                # 复制文件
                shutil.copy2(current_log_file, export_path)

                # 获取文件大小
                file_size = os.path.getsize(export_path) / 1024  # KB

                messagebox.showinfo("导出成功", f"日志文件已导出到:\n{export_path}\n\n文件大小: {file_size:.1f} KB")
                self.parent_ui.log(f"📤 日志文件已导出到: {export_path}")

        except Exception as e:
            messagebox.showerror("错误", f"导出日志文件失败: {e}")

    def _get_log_folder_path(self):
        """获取日志文件夹路径"""
        try:
            import os
            # 默认日志文件夹路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(os.path.dirname(current_dir))
            log_folder = os.path.join(project_root, "logs")
            return log_folder
        except Exception:
            # 备用路径
            return os.path.join(os.getcwd(), "logs")

    def _get_current_log_file_path(self):
        """获取当前日志文件路径"""
        try:
            import datetime
            log_folder = self._get_log_folder_path()
            today = datetime.datetime.now().strftime('%Y-%m-%d')
            log_filename = f"app_{today}.log"
            return os.path.join(log_folder, log_filename)
        except Exception:
            # 备用文件名
            log_folder = self._get_log_folder_path()
            return os.path.join(log_folder, "app.log")

    def cleanup(self):
        """清理日志管理器资源"""
        try:
            # 停止日志处理线程
            self.log_handler_running = False
            if self.log_handler_thread and self.log_handler_thread.is_alive():
                self.log_handler_thread.join(timeout=1)
        except Exception as e:
            print(f"清理日志管理器失败: {e}")
