# 网易号存稿工具 - 模块化版 v2.1.1

这是网易号存稿工具的模块化版本，提供了更好的代码组织结构和可维护性。本版本进一步优化了代码结构，增强了功能稳定性，并改进了用户体验。

## 功能介绍

网易号存稿工具是一款自动化上传视频到网易号后台并保存为草稿的工具。主要功能包括：

1. **多账号管理**：支持导入、删除和管理多个网易号账号
2. **批量处理**：可以批量上传多个视频文件
3. **并发执行**：支持多账号并发执行任务，提高效率
4. **自动填充**：自动生成视频标题、标签，并完成分类选择
5. **智能匹配封面**：根据视频文件名自动匹配对应的封面图片
6. **任务状态显示**：实时显示各账号任务执行进度
7. **失败截图功能**：在无头模式下，当视频上传或存稿操作失败时自动截图保存，便于排查问题
8. **多线程处理**：使用专业线程管理器高效处理多账号任务
9. **视频智能分配**：确保并发处理时不同账号获取不同视频
10. **数据查询功能**：查询账号数据，显示七日收益历史
11. **无头模式支持**：不显示浏览器界面，在后台运行
12. **存稿数量限制**：可设置处理特定数量视频后自动停止
13. **循环次数限制**：可设置任务循环特定次数后自动停止
14. **全部账号批量存稿**：不选择账号则自动为所有账号存稿
15. **失败原因记录**：自动记录并显示存稿失败的具体原因
16. **灵活配置**：支持相对路径和环境变量配置，更易于部署
17. **丰富的动画系统**：集成多种加载动画，提供视觉反馈和更好的用户体验

## 模块结构

项目采用模块化设计，各模块职责明确，便于维护和扩展：

```python
网易号存稿/
  ├── __init__.py - 模块包初始化文件
  ├── main.py - 主程序入口
  ├── ui/ - UI模块
  │   ├── __init__.py
  │   ├── main.py - 主UI类
  │   ├── components/ - UI组件
  │   │   ├── __init__.py
  │   │   ├── account_panel.py - 账号管理面板
  │   │   ├── log_panel.py - 日志面板
  │   │   ├── progress_panel.py - 进度面板
  │   │   ├── settings_panel.py - 设置面板
  │   │   └── toolbar.py - 工具栏
  │   ├── dialogs/ - 对话框
  │   │   ├── __init__.py
  │   │   ├── account_dialog.py - 账号添加对话框
  │   │   ├── data_viewer.py - 数据查看对话框
  │   │   └── settings_dialog.py - 设置对话框
  │   └── styles/ - 样式
  │       ├── __init__.py
  │       └── theme.py - 主题定义
  ├── account/ - 账号管理模块
  │   ├── __init__.py
  │   ├── manager.py - 账号管理器
  │   ├── data.py - 账号数据处理
  │   └── login.py - 账号登录功能
  ├── draft/ - 存稿功能模块
  │   ├── __init__.py
  │   ├── processor.py - 存稿处理器
  │   ├── uploader.py - 视频上传功能
  │   ├── original_uploader.py - 原始视频上传器
  │   ├── failure_detector.py - 失败原因检测器
  │   └── tagger.py - 标签生成功能
  ├── browser/ - 浏览器控制模块
  │   ├── __init__.py
  │   ├── driver.py - 浏览器驱动管理
  │   └── actions.py - 浏览器操作
  ├── common/ - 通用模块
  │   ├── __init__.py
  │   ├── config.py - 配置管理
  │   ├── logger.py - 日志功能
  │   ├── utils.py - 工具函数
  │   └── constants.py - 常量定义
  └── concurrent/ - 并发处理模块
      ├── __init__.py
      ├── manager.py - 并发管理器
      └── worker.py - 工作线程
```

## 安装要求

1. Python 3.11或更高版本（推荐使用Python 3.11.9）
2. Chrome浏览器（最新版本）
3. 稳定的网络连接
4. 所需Python库（见requirements.txt）：
   - selenium
   - webdriver-manager
   - pillow
   - pandas
   - openpyxl
   - colorama
   - psutil

## 安装方法

1. 确保已安装Python 3.11+
2. 安装依赖库：
   ```bash
   pip install -r requirements.txt
   ```
3. 确保已安装Chrome浏览器
4. 运行启动脚本：
   ```bash
   python run_app.py
   ```
5. 首次运行时，程序会自动下载并配置ChromeDriver

## 使用指南

### 目录配置

使用前请配置以下目录：

- **账号目录**：存放账号信息的文件夹
- **视频目录**：待处理的视频文件夹
- **封面目录**：视频封面图片的文件夹
  - 注意：程序会在封面目录下创建一个`temp`子目录，用于存放并发处理时的临时封面文件副本
  - 这些临时文件通常会在处理完成后自动清理，如果程序异常终止可能会有残留
  - 可以在程序未运行时安全地清理`temp`子目录中的文件
- **已处理目录**：处理完成后视频的归档文件夹
- **违规账号目录**：用于存放被标记为违规的账号信息
- **截图目录**：程序自动创建，存放无头模式下出错时的截图

### 账号管理

支持两种方式添加账号：

1. **手动添加**：点击"添加账号"按钮，系统将自动打开浏览器，引导您完成网易号登录过程，登录成功后自动获取并保存cookie
2. **批量导入**：点击"批量导入"按钮，从文本文件批量导入多个账号

账号信息将保存在账号目录中，可以通过界面管理和选择需要使用的账号。

#### 添加账号流程

1. 点击"添加账号(手机号登录)"按钮
2. 系统会自动打开浏览器，并跳转到网易号登录页面
3. 使用手机号验证码方式在浏览器中完成登录
4. 登录成功后，系统会自动获取cookie并保存
5. 为账号设置一个名称（如果不设置，系统会自动生成一个默认名称）
6. 账号添加完成后，会显示在账号下拉列表中，可以直接使用

### 并发控制

可以设置同时处理的账号数量（并发数），默认为1个账号同时处理。根据您的计算机性能和网络状况，可以适当调整此值。

在以下位置设置并发数：
- 主界面上方的"并发账号"后的数字输入框
- 设置标签页 > 其他设置 > 并发账号数量

建议配置：
- 中低配置电脑：设置2-3个并发数
- 高配置电脑：可设置4-6个并发数
- 服务器：可根据性能设置更高并发数

**注意**：建议在无头模式下使用并发功能，以节省系统资源并提高效率。

### 存稿限制

可以设置以下限制：

- **存稿数量限制**：每个账号最多存稿的数量，达到限制数量后，任务会自动停止
- **循环次数限制**：程序循环执行的次数，达到限制次数后，任务会自动停止

设置为0表示不限制。

**设置方法**：
1. 主界面快速设置：在主界面底部的输入框中直接设置数值
2. 详细设置：在"设置"标签页 -> "其他设置"中设置并查看详细说明

**注意事项**：
- 存稿数量限制按成功存稿的视频计数，失败的不计入
- 循环次数是指系统检查视频目录的次数，即使没有视频也计入循环次数
- 两项限制同时启用时，满足任一条件都将导致任务停止

### 无头模式

启用无头模式后，浏览器将在后台运行，不会显示界面，适合长时间自动化运行。

**优点**：
- 减少系统资源消耗
- 不会干扰您的其他操作
- 适合批量、长时间运行

**使用方法**：
1. 在主界面勾选"无头模式"复选框
2. 您可以点击"测试无头模式"按钮检查无头模式是否正常工作
3. 无头模式下所有操作与可视化模式相同，但不会显示浏览器窗口

**注意事项**：
- 添加账号操作始终在可视化模式下进行，无论是否勾选了无头模式
- 如果您在无头模式下遇到问题，可以尝试切换回可视化模式进行排查
- 建议在首次使用时先通过可视化模式测试流程，确认一切正常后再启用无头模式

### 归档模式

启用归档模式后，处理完成的视频将被移动到已处理目录，而不是删除。

### 全部账号批量存稿

**功能说明**：
- 当您不选择具体账号时，系统将自动为所有账号执行存稿操作
- 每个账号会按顺序依次处理，完成一个账号后自动切换到下一个
- 在批量存稿模式下，每个账号都会使用相同的存稿设置（包括存稿数量和循环次数限制）

**使用方法**：
1. 不选择任何账号（或清除当前选择的账号）
2. 设置好存稿相关参数
3. 点击"开始存稿"按钮
4. 程序将自动为所有账号依次执行存稿任务

**注意事项**：
- 批量存稿会顺序处理所有账号，如果账号较多，整个过程可能需要较长时间
- 如果中途停止，将不会继续处理剩余账号
- 每个账号将独立打开一个新的浏览器会话，避免cookie和登录状态混淆
- 在批量存稿日志中可以清晰看到当前正在处理的账号信息

### 数据查询功能

系统提供了账号数据查询功能，可以查询账号的粉丝数、播放量、收益等数据，并显示七日收益历史。

**使用方法**：
1. 点击"查询数据"按钮
2. 系统会自动查询所有账号的数据
3. 查询完成后，可以点击"显示数据"按钮查看详细数据
4. 可以导出数据到Excel文件

## 注意事项

1. 视频文件名会被用作标题，请确保名称合适
2. 封面图片的文件名需要与视频文件名一致（不包括扩展名）
3. 程序会自动从标题中提取关键词作为标签
4. 支持的视频格式：MP4, MOV, AVI, WMV, FLV
5. 支持的封面格式：JPG, JPEG, PNG, WEBP
6. 首次登录账号时，可能需要手动处理验证码
7. 程序会自动保存cookie，后续使用同一账号时可以直接登录
8. 上传大视频文件时请保持良好的网络连接
9. 长时间运行可能会导致浏览器内存占用增加
10. 如果在无头模式下遇到存稿失败问题，可以查看screenshots目录下的截图以诊断问题
11. 当运行多个并发账号时，请确保系统有足够的内存和CPU资源

## 图片检测优化

在v2.1.1版本中，我们对封面图片检测逻辑进行了重要优化：

### 优化内容

1. **移除剪裁区域检测**：
   - 由于网易号编辑器实际上没有剪裁区域，我们移除了相关检测代码
   - 这避免了因为寻找不存在的元素而导致的错误判断

2. **多维度图片检测**：
   - 实现了多种方式检测图片是否成功加载：
     - 检查图片元素是否存在并完全加载
     - 检查图片src是否包含特定模式（如dingyue.ws.126.net或.jpg等）
     - 检查页面中是否有包含"{图片中}"文本的元素
   - 这种多维度检测大大提高了图片加载检测的准确性

3. **无头模式增强**：
   - 特别优化了无头模式下的图片检测逻辑
   - 使用JavaScript深度检查DOM元素，确保图片真正加载完成

### 技术实现

图片检测使用以下JavaScript代码实现：

```javascript
function checkImageBySource() {
    // 查找所有图片元素
    const images = document.querySelectorAll('img');

    // 检查是否有任何图片的src包含特定的模式
    for (const img of images) {
        if (img.src &&
            (img.src.includes('dingyue.ws.126.net') ||
             img.src.includes('.jpg') ||
             img.src.includes('.png') ||
             img.src.includes('.jpeg'))) {

            // 检查图片是否已加载
            if (img.complete && typeof img.naturalWidth !== 'undefined' && img.naturalWidth > 0) {
                return { found: true, loaded: true };
            }
        }
    }

    // 检查是否有包含"{图片中}"文本的元素
    const elements = document.querySelectorAll('*');
    for (const el of elements) {
        if (el.textContent && el.textContent.includes('{图片中}')) {
            return { found: true, isTextMarker: true };
        }
    }

    return { found: false };
}
```

这种优化确保了封面图片上传流程的可靠性，减少了因图片检测不准确导致的失败情况。

## 配置文件说明

程序使用`config.json`文件保存配置信息，支持相对路径和环境变量：

```json
{
    "account_dir": "${NETEASE_BASE_DIR}/网易号全自动/账号",
    "video_dir": "${NETEASE_BASE_DIR}/网易号全自动/未处理",
    "cover_dir": "${NETEASE_BASE_DIR}/网易号全自动/未处理",
    "processed_dir": "${NETEASE_BASE_DIR}/网易号全自动/已处理",
    "processed_covers_dir": "${NETEASE_BASE_DIR}/网易号全自动/已处理/封面",
    "violation_dir": "${NETEASE_BASE_DIR}/网易号全自动/违规",
    "screenshots_dir": "${NETEASE_BASE_DIR}/网易号全自动/截图",
    "archive_completed": true,
    "headless_mode": false,
    "draft_limit": 0,
    "loop_limit": 0,
    "concurrent_accounts": 1,
    "random_video_allocation": true,
    "enable_level_filter": false,
    "enable_keyword_filter": false,
    "min_log_level": "INFO",
    "account_progress": {}
}
```

- 可以使用环境变量`NETEASE_BASE_DIR`设置基础目录
- 如果未设置环境变量，将使用相对路径或默认路径
- 配置文件会在首次运行时自动创建

## 故障排除

如果遇到问题，请检查：

1. **浏览器驱动问题**：
   - 确保Chrome浏览器是最新版本
   - 程序会自动下载匹配的ChromeDriver，如果失败，可以手动下载并放置在程序目录
   - 如果遇到"浏览器已被自动化软件控制"的提示，请更新Chrome和ChromeDriver

2. **网络连接问题**：
   - 确保网络连接稳定
   - 检查是否可以正常访问网易号后台
   - 如果使用代理，请确保代理设置正确

3. **账号问题**：
   - 确认账号信息是否正确
   - 检查账号是否被平台限制
   - 尝试手动登录并更新Cookie

4. **路径和权限问题**：
   - 确保各目录路径设置正确且有访问权限
   - 避免使用包含特殊字符的路径
   - 检查是否有足够的磁盘空间

5. **性能问题**：
   - 如果并发数设置过高导致系统卡顿，请减小并发数
   - 检查系统资源使用情况，特别是内存和CPU使用率
   - 长时间运行可能导致内存泄漏，定期重启程序

6. **失败分析**：
   - 查看screenshots目录下的失败截图，了解错误发生的页面状态
   - 检查日志文件中的详细错误信息
   - 尝试在可视化模式下重现问题

## 常见问题

1. **Q: 为什么上传视频后长时间没有反应？**
   A: 视频上传需要时间，特别是大文件。程序默认等待600秒，请耐心等待。

2. **Q: 登录失败怎么办？**
   A: 确保您使用的是正确的手机号和验证码，如果多次尝试仍然失败，可以尝试手动登录后截图联系技术支持。

3. **Q: 为什么有些账号会被标记为违规？**
   A: 当程序检测到账号可能存在违规风险时，会自动将其标记并移动到违规账号目录。

4. **Q: 无头模式与可视化模式有什么区别？**
   A: 无头模式不会显示浏览器界面，在后台运行，可以节省系统资源；可视化模式则可以看到整个操作过程，便于调试和问题排查。

5. **Q: 无头模式下如何知道程序运行状态？**
   A: 程序界面中的日志区域会实时显示所有操作步骤和状态，无论是在无头模式还是可视化模式下。

6. **Q: 如何设置任务自动在特定数量视频后停止？**
   A: 在主界面底部的"存稿数量"输入框中输入您希望处理的视频数量即可。

7. **Q: 如何设置任务运行特定次数后停止？**
   A: 在主界面底部的"循环次数"输入框中输入您希望的循环次数即可。

8. **Q: 如何对所有账号进行批量存稿？**
   A: 只需不选择具体账号（或清除当前选择），然后点击"开始存稿"按钮，系统会自动为所有账号依次执行存稿任务。

## 动画系统

网易号存稿工具集成了丰富的加载动画系统，为用户提供视觉反馈和更好的用户体验。

### 动画类型

动画系统分为三个主要类别：

1. **基础动画** - 简单、轻量级的加载动画
   - SpinnerAnimation - 旋转圆点动画
   - PulseAnimation - 脉冲波动动画
   - ProgressBarAnimation - 进度条动画
   - TextFadeAnimation - 文字渐变动画
   - BouncingDotsAnimation - 弹跳点动画

2. **创意动画** - 中等复杂度的创意动画效果
   - ParticleFlowAnimation - 粒子流动画
   - LiquidWaveAnimation - 液体波浪动画
   - NeonGlowAnimation - 霓虹灯光效果
   - GeometricMorphAnimation - 几何变形动画
   - TypewriterAnimation - 打字机效果

3. **高级创意动画** - 复杂、精细的高级动画效果
   - DNAHelixAnimation - DNA螺旋动画
   - GalaxyAnimation - 星系动画
   - MatrixRainAnimation - 矩阵数字雨
   - EnergyPulseAnimation - 能量脉冲动画
   - AudioWaveAnimation - 音频波形动画

### 使用方法

启动画面使用动画系统提供视觉反馈。可以通过修改 `run_app.py` 中的 `animation_type` 变量来选择不同的动画效果：

```python
# 选择动画类型
animation_type = "dna"  # 可选值见上方列表
```

更多详细信息请参阅 `动画系统文档.md`。

## 更新日志

### v2.1.1 (当前版本)

- 优化封面缩略图检测逻辑，移除剪裁区域检测（因为实际上没有剪裁区域）
- 增强图片可见性检测，通过多种方式验证图片是否加载成功：
  - 检查图片元素是否存在并完全加载
  - 检查图片src是否包含特定模式（如dingyue.ws.126.net或.jpg等）
  - 检查页面中是否有包含"{图片中}"文本的元素
- 改进无头模式下的图片检测可靠性
- 优化封面上传流程，提高成功率

### v2.1

- 重新启用并优化存稿功能
- 添加失败原因检测和记录功能
- 改进配置系统，支持相对路径和环境变量
- 优化七日收益历史显示
- 增强错误处理和日志记录
- 修复视频分配算法中的问题
- 改进浏览器驱动自动更新机制
- 优化UI响应性能
- 集成丰富的动画系统，提供三类共15种动画效果
- 修复动画模块导入问题，确保所有动画类型可用
- 添加动画系统文档，详细说明各类动画的使用方法

### v2.0

- 完成模块化重构，提高代码可维护性
- 优化UI界面，改进用户体验
- 增强数据查询功能
- 暂时禁用存稿功能，保留UI组件

### v1.5

- 新增全部账号批量存稿功能，不选择账号则自动为所有账号存稿
- 优化多账号处理逻辑，提高批量处理的稳定性和效率
- 改进浏览器会话管理，每个账号使用独立的浏览器实例
- 完善批量存稿的日志显示，清晰标识当前处理的账号
- 修复若干已知问题，提高整体稳定性

### v1.4

- 新增存稿数量限制功能，可设置处理特定数量视频后自动停止
- 新增循环次数限制功能，可设置任务循环特定次数后自动停止
- 在主界面添加快速设置区域，方便调整限制值
- 优化日志显示，实时显示任务进度和限制状态
- 完善配置保存功能，自动记住用户的限制设置

### v1.3

- 完善无头模式功能，提高稳定性
- 增加无头模式测试按钮
- 优化浏览器自动化参数配置
- 改进网页元素定位策略，提高操作成功率
- 新增防检测机制，降低被网站识别为自动化工具的风险

### v1.2

- 添加多账号并发处理功能
- 优化界面显示和进度展示
- 增加账号进度条
- 修复已知问题

### v1.1

- 添加自动匹配封面功能
- 增加批量账号导入功能
- 优化任务执行流程

### v1.0

- 初始版本发布
- 基本的存稿功能
- 账号管理功能
