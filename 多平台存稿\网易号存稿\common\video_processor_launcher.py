"""
视频处理工具启动模块
"""

import os
import sys
import subprocess
from tkinter import messagebox

def launch_video_processor(auto_mode=False, status_file=None):
    """启动视频处理工具

    Args:
        auto_mode (bool): 是否启用自动模式
        status_file (str): 状态文件路径（可选）
    """
    try:
        # 获取当前脚本所在目录
        current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

        # 检查是否在打包环境中
        if getattr(sys, 'frozen', False):
            # 在打包环境中，使用exe文件
            video_processor_exe = os.path.join(current_dir, "_internal", "视频处理工具", "视频处理工具.exe")

            if os.path.exists(video_processor_exe):
                # 构建命令参数
                cmd = [video_processor_exe]

                # 添加自动模式参数
                if auto_mode:
                    cmd.append("--auto-mode")
                    print("🚀 启动视频处理工具（自动模式）")
                else:
                    print("🚀 启动视频处理工具（手动模式）")

                # 添加状态文件参数
                if status_file:
                    cmd.extend(["--status-file", status_file])
                    print(f"📊 状态文件: {status_file}")

                print(f"执行命令: {' '.join(cmd)}")

                # 启动视频处理工具exe，不创建新控制台
                process = subprocess.Popen(cmd)
                print(f"✅ 视频处理工具已启动，进程ID: {process.pid}")
                return True
            else:
                print(f"❌ 视频处理工具exe不存在: {video_processor_exe}")
                messagebox.showerror("错误", "找不到视频处理工具exe文件")
                return False
        else:
            # 在开发环境中，使用Python脚本
            video_processor_path = os.path.join(current_dir, "视频处理工具", "main.py")

            # 检查文件是否存在
            if not os.path.exists(video_processor_path):
                # 尝试查找备用文件
                video_processor_path = os.path.join(current_dir, "视频处理工具", "视频处理工具.py")
                if not os.path.exists(video_processor_path):
                    messagebox.showerror("错误", "找不到视频处理工具文件")
                    return False

            # 构建命令参数
            python_exe = sys.executable
            cmd = [python_exe, video_processor_path]

            # 添加自动模式参数
            if auto_mode:
                cmd.append("--auto-mode")
                print("🚀 启动视频处理工具（自动模式）")
            else:
                print("🚀 启动视频处理工具（手动模式）")

            # 添加状态文件参数
            if status_file:
                cmd.extend(["--status-file", status_file])
                print(f"📊 状态文件: {status_file}")

            print(f"执行命令: {' '.join(cmd)}")

            # 使用Python解释器启动视频处理工具，不创建新控制台
            process = subprocess.Popen(cmd)
            print(f"✅ 视频处理工具已启动，进程ID: {process.pid}")
            return True

    except Exception as e:
        error_msg = f"启动视频处理工具时出错: {str(e)}"
        print(f"❌ {error_msg}")
        messagebox.showerror("启动错误", error_msg)
        return False
