[2025-08-07 00:19:25] [INFO] 🚀 启动多平台存稿工具...
[2025-08-07 00:19:25] [INFO] ✅ 定时任务管理器UI初始化完成
[2025-08-07 00:19:25] [INFO] ✅ 定时任务管理标签页创建完成
[2025-08-07 00:23:21] [INFO] ✅ 定时任务管理器UI清理完成
25-08-07 00:19:25] [INFO] 已从toutiao平台加载存稿详情数据，共 83 个账号
[2025-08-07 00:19:25] [INFO] 🚀 定时任务调度器已启动
[2025-08-07 00:19:25] [DEBUG] 🔄 调度器主循环开始
[2025-08-07 00:19:26] [INFO] 已确保所有必要目录存在
[2025-08-07 00:19:26] [DEBUG] 🔍 [调试] 当前平台: toutiao
[2025-08-07 00:19:26] [DEBUG] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-08-07 00:19:26] [DEBUG] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-08-07 00:19:26] [SUCCESS] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-08-07 00:19:26] [DEBUG] 🔍 [调试] 更新账号树 - 当前平台: toutiao
[2025-08-07 00:19:26] [DEBUG] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-08-07 00:19:26] [DEBUG] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-08-07 00:19:26] [INFO] 已加载账号数据: 85 条记录
[2025-08-07 00:19:26] [DEBUG] 🔍 [调试] 账号数据为空或未加载
[2025-08-07 00:19:26] [SUCCESS] ✅ 初始UI更新完成
[2025-08-07 00:20:08] [INFO] 已选择账号: ***********
[2025-08-07 00:20:09] [DEBUG] 🚀 开始为账号 *********** 存稿
[2025-08-07 00:20:09] [INFO] 已确保所有必要目录存在
[2025-08-07 00:20:09] [INFO] 使用单线程模式处理账号
[2025-08-07 00:20:09] [DEBUG] 🔍 [DEBUG] 准备启动单线程任务，参数: ***********
[2025-08-07 00:20:09] [DEBUG] 🔍 [DEBUG] 单线程任务已启动
[2025-08-07 00:20:09] [DEBUG] 🔍 [DEBUG] _single_task_thread 开始执行，参数: ***********
[2025-08-07 00:20:09] [DEBUG] 📋 开始处理单个账号: ***********
[2025-08-07 00:20:09] [INFO] 视频分配方式: 随机分配
[2025-08-07 00:20:09] [DEBUG] 开始处理账号: ***********
[2025-08-07 00:20:09] [SUCCESS] 正在使用Cookie登录账号: ***********
[2025-08-07 00:20:09] [SUCCESS] ✅ Cookie预检测通过，启动浏览器验证...
[2025-08-07 00:20:15] [SUCCESS] 开始查询账号数据: *********** [toutiao] 浏览器驱动创建成功，使用端口: 9515
[2025-08-07 00:20:19] [SUCCESS] 正在加载Cookie文件: D:/网易号全自动/头条号账号\***********.txt
[2025-08-07 00:20:29] [SUCCESS] ✅ Cookie添加完成
[2025-08-07 00:20:52] [ERROR] ❌ 头条号登录失败：停留在登录页面
[2025-08-07 00:20:54] [ERROR] Cookie登录失败，尝试使用手机号登录: ***********
[2025-08-07 00:21:02] [SUCCESS] [toutiao] 浏览器驱动创建成功，使用端口: 9516
[2025-08-07 00:21:13] [SUCCESS] 请在浏览器中手动完成登录操作...
[2025-08-07 00:21:38] [INFO] 检测到浏览器已关闭，取消登录操作
[2025-08-07 00:21:38] [ERROR] 手机号登录失败: ***********
[2025-08-07 00:21:38] [ERROR] 账号 *********** 处理失败
[2025-08-07 00:21:38] [SUCCESS] 账号 *********** 处理完成
[2025-08-07 00:21:38] [SUCCESS] ✅ 账号 *********** 处理成功
[2025-08-07 00:21:38] [SUCCESS] 📊 任务进度完成: 1/1
[2025-08-07 00:21:38] [SUCCESS] ✅ 存稿任务已完成
[2025-08-07 00:21:43] [INFO] 已选择账号: 18876662667
[2025-08-07 00:21:45] [INFO] 已选择账号: 18876662667
[2025-08-07 00:21:46] [INFO] 已选择账号: 18876662667
[2025-08-07 00:21:47] [DEBUG] 🚀 开始为账号 18876662667 存稿
[2025-08-07 00:21:47] [INFO] 已确保所有必要目录存在
[2025-08-07 00:21:47] [INFO] 使用单线程模式处理账号
[2025-08-07 00:21:47] [DEBUG] 🔍 [DEBUG] 准备启动单线程任务，参数: 18876662667
[2025-08-07 00:21:47] [DEBUG] 🔍 [DEBUG] 单线程任务已启动
[2025-08-07 00:21:47] [DEBUG] 🔍 [DEBUG] _single_task_thread 开始执行，参数: 18876662667
[2025-08-07 00:21:47] [DEBUG] 📋 开始处理单个账号: 18876662667
[2025-08-07 00:21:47] [INFO] 视频分配方式: 随机分配
[2025-08-07 00:21:47] [DEBUG] 开始处理账号: 18876662667
[2025-08-07 00:21:47] [SUCCESS] 正在使用Cookie登录账号: 18876662667
[2025-08-07 00:21:47] [SUCCESS] ✅ Cookie预检测通过，启动浏览器验证...
[2025-08-07 00:21:54] [SUCCESS] 开始查询账号数据: 18876662667 [toutiao] 浏览器驱动创建成功，使用端口: 9517
[2025-08-07 00:21:58] [SUCCESS] 正在加载Cookie文件: D:/网易号全自动/头条号账号\18876662667.txt
[2025-08-07 00:22:02] [SUCCESS] ✅ Cookie添加完成
[2025-08-07 00:22:03] [SUCCESS] ✅ 头条号快速登录成功 (等待0.5秒)
[2025-08-07 00:22:03] [SUCCESS] 登录成功，开始处理视频: 18876662667
[2025-08-07 00:22:03] [INFO] 找到 652 个视频文件
[2025-08-07 00:22:03] [INFO] 已随机打乱 652 个视频文件的顺序
[2025-08-07 00:22:03] [DEBUG] 开始处理视频: 21世纪的学术前沿：复杂系统科学.mp4
[2025-08-07 00:22:03] [DEBUG] 🎬 开始头条号视频存稿流程
[2025-08-07 00:22:03] [DEBUG] 🔍 正在定位视频菜单...
[2025-08-07 00:22:04] [SUCCESS] ✅ 已点击视频菜单
[2025-08-07 00:22:07] [INFO] 🌐 当前不在上传页面，导航到视频上传页面...
[2025-08-07 00:22:11] [SUCCESS] ✅ 已导航到视频上传页面
[2025-08-07 00:22:11] [INFO] 📁 准备上传视频: 21世纪的学术前沿：复杂系统科学.mp4
[2025-08-07 00:22:11] [DEBUG] 🔍 找到上传按钮，准备点击...
[2025-08-07 00:22:11] [SUCCESS] ✅ 已点击上传视频按钮
[2025-08-07 00:22:13] [SUCCESS] ✅ 视频文件已选择并开始上传
[2025-08-07 00:22:13] [DEBUG] ⏳ 开始监控视频上传状态...
[2025-08-07 00:22:13] [INFO] 📊 当前上传状态: 上传中… 0%
[2025-08-07 00:22:13] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 00:22:18] [INFO] 📊 当前上传状态: 上传中… 17.92%
[2025-08-07 00:22:18] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 00:22:23] [INFO] 📊 当前上传状态: 上传中… 46.92%
[2025-08-07 00:22:23] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 00:22:28] [INFO] 📊 当前上传状态: 上传中… 71.46%
[2025-08-07 00:22:28] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 00:22:33] [INFO] 📊 当前上传状态: 上传中… 90.49%
[2025-08-07 00:22:33] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 00:22:38] [INFO] 📊 当前上传状态: 上传中… 99.84%
[2025-08-07 00:22:38] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 00:22:43] [SUCCESS] 📊 当前上传状态: 上传成功
[2025-08-07 00:22:43] [SUCCESS] ✅ 视频上传成功！
[2025-08-07 00:22:43] [DEBUG] 📷 开始上传视频封面...
[2025-08-07 00:22:43] [SUCCESS] ✅ 已点击上传封面按钮
[2025-08-07 00:22:46] [SUCCESS] ✅ 已选择本地上传
[2025-08-07 00:22:48] [INFO] 📁 准备上传封面文件: D:\头条全自动\视频搬运\已处理封面\21世纪的学术前沿：复杂系统科学.jpg
[2025-08-07 00:22:48] [INFO] 📊 封面文件大小: 190317 bytes
[2025-08-07 00:22:48] [SUCCESS] ✅ 封面文件已选择，开始上传...
[2025-08-07 00:22:48] [INFO] ⏳ 等待封面图片加载...
[2025-08-07 00:22:48] [DEBUG] 🔍 开始检测封面上传状态...
[2025-08-07 00:22:50] [ERROR] ❌ 未找到Canvas元素
[2025-08-07 00:22:50] [WARNING] ✅ 未检测到加载指示器，封面可能已加载
[2025-08-07 00:22:51] [DEBUG] 🔍 寻找第一个确认按钮...
[2025-08-07 00:23:02] [WARNING] ⚠️ 原XPath无法找到第一个确认按钮，尝试其他方法
[2025-08-07 00:23:02] [SUCCESS] ✅ 通过文本匹配点击第一个确定按钮
[2025-08-07 00:23:05] [DEBUG] 🔍 寻找第二个确认按钮...
[2025-08-07 00:23:15] [WARNING] ⚠️ 原XPath无法找到第二个确认按钮，尝试其他方法
[2025-08-07 00:23:15] [ERROR] ❌ 无法找到第二个确认按钮
[2025-08-07 00:23:15] [ERROR] ❌ 点击确认按钮失败
[2025-08-07 00:23:15] [WARNING] ⚠️ 上传封面失败，继续存稿流程
[2025-08-07 00:23:15] [DEBUG] 💾 开始保存草稿...
[2025-08-07 00:23:15] [SUCCESS] ✅ 已点击存草稿按钮
[2025-08-07 00:23:18] [WARNING] ✅ 草稿保存成功！检测到'全部保存成功'提示
[2025-08-07 00:23:18] [SUCCESS] ✅ 头条号视频存稿成功！
[2025-08-07 00:23:18] [SUCCESS] ✅ 视频存稿成功: 21世纪的学术前沿：复杂系统科学.mp4
[2025-08-07 00:23:18] [DEBUG] 开始处理视频: 牛弹琴发声：印度开始发飙了.mp4
[2025-08-07 00:23:18] [DEBUG] 🎬 开始头条号视频存稿流程
[2025-08-07 00:23:18] [DEBUG] 🔍 正在定位视频菜单...
[2025-08-07 00:23:18] [SUCCESS] ✅ 已点击视频菜单
[2025-08-07 00:23:20] [DEBUG] 正在关闭程序，清理后台资源...
[2025-08-07 00:23:21] [DEBUG] 🔄 调度器主循环结束
[2025-08-07 00:23:21] [DEBUG] 正在关闭任务执行器...
[2025-08-07 00:23:21] [INFO] 任务执行器已关闭
[2025-08-07 00:23:21] [INFO] ⏹️ 定时任务调度器已停止
[2025-08-07 00:23:21] [SUCCESS] ✅ 调度器管理器清理完成
[2025-08-07 00:23:21] [SUCCESS] ✅ 定时任务管理器UI清理完成
[2025-08-07 00:23:21] [DEBUG] 🧹 开始清理所有资源...
[2025-08-07 00:23:21] [DEBUG] ⏹️ 正在停止存稿任务...
[2025-08-07 00:23:21] [INFO] ⏹️ 存稿任务已停止
[2025-08-07 00:23:21] [DEBUG] 正在停止并发管理器...
[2025-08-07 00:23:21] [SUCCESS] ✅ 运行中的任务已停止
[2025-08-07 00:23:21] [SUCCESS] ✅ 定时器线程已清理（升级版系统自动管理）
[2025-08-07 00:23:21] [ERROR] ❌ 进入视频管理页面失败: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff6c2416f75+76917]
	GetHandleVerifier [0x0x7ff6c2416fd0+77008]
	(No symbol) [0x0x7ff6c21c9dea]
	(No symbol) [0x0x7ff6c21b5f15]
	(No symbol) [0x0x7ff6c21dabf4]
	(No symbol) [0x0x7ff6c224fa85]
	(No symbol) [0x0x7ff6c226ff72]
	(No symbol) [0x0x7ff6c2248243]
	(No symbol) [0x0x7ff6c2211431]
	(No symbol) [0x0x7ff6c22121c3]
	GetHandleVerifier [0x0x7ff6c26ed2ad+3051437]
	GetHandleVerifier [0x0x7ff6c26e7903+3028483]
	GetHandleVerifier [0x0x7ff6c270589d+3151261]
	GetHandleVerifier [0x0x7ff6c243183e+185662]
	GetHandleVerifier [0x0x7ff6c24396ff+218111]
	GetHandleVerifier [0x0x7ff6c241faf4+112628]
	GetHandleVerifier [0x0x7ff6c241fca9+113065]
	GetHandleVerifier [0x0x7ff6c2406c78+10616]
	BaseThreadInitThunk [0x0x7ffcc2f7e8d7+23]
	RtlUserThreadStart [0x0x7ffcc4e5c34c+44]

[2025-08-07 00:23:21] [ERROR] ❌ 进入视频管理页面失败
[2025-08-07 00:23:21] [ERROR] ❌ 视频存稿失败: 牛弹琴发声：印度开始发飙了.mp4
[2025-08-07 00:23:21] [DEBUG] 开始处理视频: 中国RoHS管控升级 强制性国家标准正式发布.mp4
[2025-08-07 00:23:21] [DEBUG] 🎬 开始头条号视频存稿流程
[2025-08-07 00:23:21] [DEBUG] 🔍 正在定位视频菜单...
[2025-08-07 00:23:21] [ERROR] ❌ 进入视频管理页面失败: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff6c2416f75+76917]
	GetHandleVerifier [0x0x7ff6c2416fd0+77008]
	(No symbol) [0x0x7ff6c21c9c1c]
	(No symbol) [0x0x7ff6c221055f]
	(No symbol) [0x0x7ff6c2248332]
	(No symbol) [0x0x7ff6c2242e53]
	(No symbol) [0x0x7ff6c2241f19]
	(No symbol) [0x0x7ff6c2194b05]
	GetHandleVerifier [0x0x7ff6c26ed2ad+3051437]
	GetHandleVerifier [0x0x7ff6c26e7903+3028483]
	GetHandleVerifier [0x0x7ff6c270589d+3151261]
	GetHandleVerifier [0x0x7ff6c243183e+185662]
	GetHandleVerifier [0x0x7ff6c24396ff+218111]
	(No symbol) [0x0x7ff6c2193b00]
	GetHandleVerifier [0x0x7ff6c2805f18+4201496]
	BaseThreadInitThunk [0x0x7ffcc2f7e8d7+23]
	RtlUserThreadStart [0x0x7ffcc4e5c34c+44]

[2025-08-07 00:23:21] [ERROR] ❌ 进入视频管理页面失败
[2025-08-07 00:23:21] [ERROR] ❌ 视频存稿失败: 中国RoHS管控升级 强制性国家标准正式发布.mp4
[2025-08-07 00:23:21] [INFO] 已达到循环限制 3，停止处理
[2025-08-07 00:23:21] [INFO] [toutiao] 已释放端口: 9517
[2025-08-07 00:23:21] [SUCCESS] ✅ Chrome进程强制清理完成
[2025-08-07 00:23:21] [SUCCESS] ✅ 浏览器进程已清理
[2025-08-07 00:23:22] [SUCCESS] ✅ 日志处理线程已清理
[2025-08-07 00:23:22] [SUCCESS] ✅ 存稿处理器已清理
[2025-08-07 00:23:22] [SUCCESS] ✅ 线程池已清理
[2025-08-07 00:23:22] [DEBUG] 正在停止串行刷新工作线程...
[2025-08-07 00:23:23] [SUCCESS] 账号 18876662667 处理完成
[2025-08-07 00:23:23] [SUCCESS] 账号 18876662667 处理完成
[2025-08-07 00:23:25] [WARNING] ⚠️ 仍有 3 个线程未结束
[2025-08-07 00:23:25] [SUCCESS] ✅ 临时文件已清理
[2025-08-07 00:23:25] [SUCCESS] ✅ 资源清理完成
[2025-08-07 00:23:26] [SUCCESS] ✅ 设置已静默保存
[2025-08-07 00:23:26] [SUCCESS] 程序清理完成，正在退出...
