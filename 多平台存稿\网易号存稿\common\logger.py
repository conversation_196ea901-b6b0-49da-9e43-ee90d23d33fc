"""
日志模块 - 处理日志记录和显示，支持颜色和日志级别过滤
提供更灵活的日志过滤控制和配置选项
"""

import os
import time
import queue
import threading
import json
from datetime import datetime
from typing import Optional, Callable, TextIO, Dict, List, Set, Any, Union

# 日志级别定义
LOG_LEVELS = {
    "DEBUG": 10,
    "INFO": 20,
    "WARNING": 30,
    "ERROR": 40,
    "CRITICAL": 50
}

# 日志颜色定义 (用于UI显示) - 增强版颜色方案
LOG_COLORS = {
    "DEBUG": "#95a5a6",   # 中等灰色
    "INFO": "#2c3e50",    # 深蓝灰色
    "WARNING": "#f39c12", # 明亮橙色
    "ERROR": "#e74c3c",   # 明亮红色
    "CRITICAL": "#c0392b", # 深红色
    "SUCCESS": "#27ae60", # 明亮绿色
    "PROCESS": "#3498db", # 明亮蓝色
    "UPLOAD": "#9b59b6",  # 明亮紫色
    "WAIT": "#f1c40f"     # 明亮黄色
}

class Logger:
    """日志记录器类，负责记录和显示日志，支持颜色和可选的日志级别过滤与关键词过滤"""

    def __init__(self, log_to_file: bool = True, log_dir: str = "logs", min_level: str = "INFO",
                 enable_level_filter: bool = False, enable_keyword_filter: bool = False,
                 config_file: Optional[str] = None):
        """
        初始化日志记录器

        Args:
            log_to_file: 是否将日志写入文件
            log_dir: 日志文件目录
            min_level: 最小日志级别，低于此级别的日志将被过滤（如果启用级别过滤）
            enable_level_filter: 是否启用日志级别过滤
            enable_keyword_filter: 是否启用关键词过滤
            config_file: 日志配置文件路径，如果提供则从文件加载配置
        """
        self.log_to_file = log_to_file
        self.log_dir = log_dir
        self.log_file: Optional[TextIO] = None
        self.log_queue = queue.Queue()
        self.log_thread: Optional[threading.Thread] = None
        self.running = False
        self.ui_callback: Optional[Callable] = None
        self.min_level = LOG_LEVELS.get(min_level, LOG_LEVELS["INFO"])
        self.filtered_keywords: Set[str] = set()  # 需要过滤的关键词
        self.enable_level_filter = enable_level_filter  # 是否启用日志级别过滤
        self.enable_keyword_filter = enable_keyword_filter  # 是否启用关键词过滤
        self.config_file = config_file  # 日志配置文件路径

        # 如果提供了配置文件，则从文件加载配置
        if self.config_file and os.path.exists(self.config_file):
            self.load_config()

        # 如果需要写入文件，确保日志目录存在
        if self.log_to_file:
            os.makedirs(self.log_dir, exist_ok=True)

    def start(self) -> None:
        """启动日志处理线程"""
        if self.running:
            return

        self.running = True

        # 如果需要写入文件，打开日志文件
        if self.log_to_file:
            log_filename = f"netease_draft_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
            log_path = os.path.join(self.log_dir, log_filename)
            self.log_file = open(log_path, 'w', encoding='utf-8')

        # 启动日志处理线程
        self.log_thread = threading.Thread(target=self._log_worker, daemon=True)
        self.log_thread.start()

    def stop(self) -> None:
        """停止日志处理线程"""
        self.running = False

        # 等待日志线程结束
        if self.log_thread and self.log_thread.is_alive():
            self.log_thread.join(timeout=1.0)

        # 关闭日志文件
        if self.log_file:
            self.log_file.close()
            self.log_file = None

    def set_ui_callback(self, callback: Callable) -> None:
        """
        设置UI回调函数，用于在UI中显示日志

        Args:
            callback: 回调函数，接受日志消息和颜色参数
        """
        self.ui_callback = callback

    def set_min_level(self, level: str) -> None:
        """
        设置最小日志级别

        Args:
            level: 日志级别，如INFO、WARNING、ERROR等
        """
        self.min_level = LOG_LEVELS.get(level, LOG_LEVELS["INFO"])

    def enable_level_filtering(self, enable: bool = True) -> None:
        """
        启用或禁用日志级别过滤

        Args:
            enable: 是否启用日志级别过滤
        """
        self.enable_level_filter = enable

    def enable_keyword_filtering(self, enable: bool = True) -> None:
        """
        启用或禁用关键词过滤

        Args:
            enable: 是否启用关键词过滤
        """
        self.enable_keyword_filter = enable

    def add_filtered_keyword(self, keyword: str) -> None:
        """
        添加需要过滤的关键词

        Args:
            keyword: 关键词
        """
        self.filtered_keywords.add(keyword)

    def remove_filtered_keyword(self, keyword: str) -> None:
        """
        移除需要过滤的关键词

        Args:
            keyword: 关键词
        """
        if keyword in self.filtered_keywords:
            self.filtered_keywords.remove(keyword)

    def clear_filtered_keywords(self) -> None:
        """清空需要过滤的关键词"""
        self.filtered_keywords.clear()

    def get_filtered_keywords(self) -> set:
        """
        获取当前的过滤关键词集合

        Returns:
            当前的过滤关键词集合
        """
        return self.filtered_keywords.copy()

    def set_filtered_keywords(self, keywords: list) -> None:
        """
        设置过滤关键词列表

        Args:
            keywords: 关键词列表
        """
        self.filtered_keywords.clear()
        for keyword in keywords:
            if keyword:  # 忽略空字符串
                self.filtered_keywords.add(keyword)

    def load_config(self) -> bool:
        """
        从配置文件加载日志配置

        Returns:
            是否成功加载配置
        """
        try:
            if not self.config_file or not os.path.exists(self.config_file):
                return False

            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 加载日志级别过滤设置
            if 'enable_level_filter' in config:
                self.enable_level_filter = config['enable_level_filter']

            if 'min_level' in config:
                self.set_min_level(config['min_level'])

            # 加载关键词过滤设置
            if 'enable_keyword_filter' in config:
                self.enable_keyword_filter = config['enable_keyword_filter']

            if 'filtered_keywords' in config and isinstance(config['filtered_keywords'], list):
                # 清空现有关键词
                self.filtered_keywords.clear()
                # 添加新关键词
                for keyword in config['filtered_keywords']:
                    self.filtered_keywords.add(keyword)

            # 加载其他设置
            if 'log_to_file' in config:
                self.log_to_file = config['log_to_file']

            if 'log_dir' in config:
                self.log_dir = config['log_dir']
                # 确保日志目录存在
                if self.log_to_file:
                    os.makedirs(self.log_dir, exist_ok=True)

            return True

        except Exception as e:
            print(f"加载日志配置失败: {str(e)}")
            return False

    def save_config(self) -> bool:
        """
        保存日志配置到配置文件

        Returns:
            是否成功保存配置
        """
        try:
            if not self.config_file:
                return False

            # 创建配置字典
            config = {
                'enable_level_filter': self.enable_level_filter,
                'min_level': next((k for k, v in LOG_LEVELS.items() if v == self.min_level), "INFO"),
                'enable_keyword_filter': self.enable_keyword_filter,
                'filtered_keywords': list(self.filtered_keywords),
                'log_to_file': self.log_to_file,
                'log_dir': self.log_dir
            }

            # 确保目录存在
            os.makedirs(os.path.dirname(os.path.abspath(self.config_file)), exist_ok=True)

            # 保存到文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)

            return True

        except Exception as e:
            print(f"保存日志配置失败: {str(e)}")
            return False

    def get_config(self) -> Dict[str, Any]:
        """
        获取当前日志配置

        Returns:
            当前日志配置字典
        """
        return {
            'enable_level_filter': self.enable_level_filter,
            'min_level': next((k for k, v in LOG_LEVELS.items() if v == self.min_level), "INFO"),
            'enable_keyword_filter': self.enable_keyword_filter,
            'filtered_keywords': list(self.filtered_keywords),
            'log_to_file': self.log_to_file,
            'log_dir': self.log_dir
        }

    def log(self, message: str, level: str = "INFO") -> None:
        """
        记录日志

        Args:
            message: 日志消息
            level: 日志级别，如INFO、WARNING、ERROR等
        """
        # 检查日志级别（如果启用了级别过滤）
        if self.enable_level_filter:
            level_value = LOG_LEVELS.get(level, LOG_LEVELS["INFO"])
            if level_value < self.min_level:
                return

        # 检查是否包含需要过滤的关键词（如果启用了关键词过滤）
        if self.enable_keyword_filter:
            for keyword in self.filtered_keywords:
                if keyword in message:
                    return

        # 确定日志颜色
        color = LOG_COLORS.get(level, "#000000")

        # 根据消息内容确定特殊颜色
        if "✅" in message or "成功" in message:
            color = LOG_COLORS["SUCCESS"]
        elif "❌" in message or "失败" in message or "错误" in message:
            color = LOG_COLORS["ERROR"]
        elif "⚠️" in message or "警告" in message:
            color = LOG_COLORS["WARNING"]
        elif "上传" in message:
            color = LOG_COLORS["UPLOAD"]
        elif "等待" in message or "⏳" in message:
            color = LOG_COLORS["WAIT"]
        elif "处理" in message or "开始" in message or "完成" in message:
            color = LOG_COLORS["PROCESS"]

        # 将日志消息放入队列
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = {
            "text": f"[{timestamp}] [{level}] {message}",
            "color": color,
            "level": level,
            "timestamp": timestamp,
            "message": message
        }
        self.log_queue.put(log_entry)

    def _log_worker(self) -> None:
        """日志处理线程的工作函数"""
        while self.running or not self.log_queue.empty():
            try:
                # 从队列获取日志消息，最多等待0.5秒
                log_entry = self.log_queue.get(timeout=0.5)

                # 写入日志文件
                if self.log_file:
                    self.log_file.write(log_entry["text"] + "\n")
                    self.log_file.flush()

                # 调用UI回调函数
                if self.ui_callback:
                    # 传递message和level参数，匹配LogManager.log()的签名
                    self.ui_callback(log_entry["message"], log_entry["level"])
                else:
                    print(log_entry["text"])

                # 标记任务完成
                self.log_queue.task_done()

            except queue.Empty:
                # 队列为空，继续等待
                continue
            except Exception as e:
                # 处理异常
                print(f"日志处理异常: {str(e)}")
                time.sleep(0.1)

    # 便捷方法
    def debug(self, message: str) -> None:
        """记录DEBUG级别日志"""
        self.log(message, "DEBUG")

    def info(self, message: str) -> None:
        """记录INFO级别日志"""
        self.log(message, "INFO")

    def warning(self, message: str) -> None:
        """记录WARNING级别日志"""
        self.log(message, "WARNING")

    def error(self, message: str) -> None:
        """记录ERROR级别日志"""
        self.log(message, "ERROR")

    def critical(self, message: str) -> None:
        """记录CRITICAL级别日志"""
        self.log(message, "CRITICAL")

    def is_debug_enabled(self) -> bool:
        """检查是否启用DEBUG级别日志"""
        return self.min_level <= LOG_LEVELS.get("DEBUG", 10)

# 获取日志配置文件路径
def get_log_config_file() -> str:
    """
    获取日志配置文件路径

    Returns:
        日志配置文件路径
    """
    # 首先尝试从环境变量获取
    config_file = os.environ.get("NETEASE_LOG_CONFIG")
    if config_file and os.path.exists(config_file):
        return config_file

    # 然后尝试在当前目录查找
    current_dir_config = os.path.join(os.getcwd(), "log_config.json")
    if os.path.exists(current_dir_config):
        return current_dir_config

    # 最后尝试在用户目录查找
    user_dir_config = os.path.join(os.path.expanduser("~"), ".netease", "log_config.json")
    if os.path.exists(user_dir_config):
        return user_dir_config

    # 如果都不存在，返回默认路径
    return os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "log_config.json")

# 创建全局日志记录器实例 - 禁用文件写入，由LogManager统一处理
logger = Logger(
    log_to_file=False,  # 禁用文件写入，避免与LogManager重复
    enable_level_filter=False,
    enable_keyword_filter=False,
    config_file=get_log_config_file()
)

# 启动全局logger的处理线程
logger.start()

# 添加默认过滤关键词 - 即使默认不启用过滤，也预先添加这些关键词，以便用户可以随时启用过滤
default_keywords = [
    "正在查找",
    "已找到",
    "准备点击",
    "正在输入标签",
    "正在检查上传状态",
    "正在检查页面状态",
    "等待页面加载",
    "正在滚动页面",
    "正在等待元素出现"
]

# 添加默认关键词
for keyword in default_keywords:
    logger.add_filtered_keyword(keyword)

def log(message: str, level: str = "INFO") -> None:
    """
    记录日志的全局函数

    Args:
        message: 日志消息
        level: 日志级别，如INFO、WARNING、ERROR等
    """
    logger.log(message, level)

def debug(message: str) -> None:
    """记录DEBUG级别日志"""
    log(message, "DEBUG")

def info(message: str) -> None:
    """记录INFO级别日志"""
    log(message, "INFO")

def warning(message: str) -> None:
    """记录WARNING级别日志"""
    log(message, "WARNING")

def error(message: str) -> None:
    """记录ERROR级别日志"""
    log(message, "ERROR")

def critical(message: str) -> None:
    """记录CRITICAL级别日志"""
    log(message, "CRITICAL")
