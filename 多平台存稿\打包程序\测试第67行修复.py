#!/usr/bin/env python3
"""
测试第67行PyInstaller导入修复
"""

import sys
from pathlib import Path

def test_pyinstaller_check():
    """测试PyInstaller检查功能"""
    print("🔍 测试PyInstaller检查功能...")
    
    try:
        # 添加当前目录到路径
        sys.path.insert(0, str(Path(__file__).parent))
        
        # 导入统一打包器
        from 统一自动打包 import UnifiedPackager
        
        # 创建实例
        packager = UnifiedPackager()
        
        # 测试依赖检查方法
        print("执行依赖检查...")
        result = packager.check_dependencies()
        
        if result:
            print("✅ 依赖检查成功")
            return True
        else:
            print("❌ 依赖检查失败")
            return False
            
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("🔍 测试基本功能...")
    
    try:
        sys.path.insert(0, str(Path(__file__).parent))
        from 统一自动打包 import UnifiedPackager
        
        packager = UnifiedPackager()
        
        # 测试日志功能
        packager.log_message("测试日志消息")
        print("✅ 日志功能正常")
        
        # 测试配置
        print(f"源目录: {packager.config['source_dir']}")
        print(f"目标目录: {packager.config['target_dir']}")
        print("✅ 配置读取正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

def test_gui_import():
    """测试GUI导入"""
    print("🔍 测试GUI导入...")
    
    try:
        sys.path.insert(0, str(Path(__file__).parent))
        from 统一自动打包 import GUI_AVAILABLE, PackageGUI
        
        if GUI_AVAILABLE:
            print("✅ GUI模块可用")
            print("✅ PackageGUI类导入成功")
        else:
            print("⚠️  GUI模块不可用（这是正常的）")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI导入测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("第67行PyInstaller导入修复验证")
    print("=" * 60)
    
    tests = [
        ("基本功能", test_basic_functionality),
        ("PyInstaller检查", test_pyinstaller_check),
        ("GUI导入", test_gui_import),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*15} {test_name} {'='*15}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 出错: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 第67行导入问题修复成功！")
        print("✅ 统一自动打包器可以正常使用")
    else:
        print("⚠️  仍有问题需要解决")
    
    print("=" * 60)
    
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n测试{'成功' if success else '失败'}")
    input("按回车键退出...")
