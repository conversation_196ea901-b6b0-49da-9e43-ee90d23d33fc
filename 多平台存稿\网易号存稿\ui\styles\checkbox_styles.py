#!/usr/bin/env python3
"""
复选框样式美化工具
用于美化程序中所有复选框的外观
"""

import tkinter as tk
from tkinter import ttk
import sys
import os
from tkinter import font as tkFont

class BeautifulCheckbox(tk.Frame):
    """美化的自定义复选框控件"""

    def __init__(self, parent, text="", variable=None, style_type="modern", command=None, **kwargs):
        """初始化美化复选框

        Args:
            parent: 父控件
            text: 显示文本
            variable: 绑定的BooleanVar变量
            style_type: 样式类型
            command: 状态改变时的回调函数
        """
        super().__init__(parent, **kwargs)

        self.text = text
        self.variable = variable or tk.BooleanVar()
        self.style_type = style_type
        self.command = command

        # 样式配置
        self.styles = {
            "modern": {
                "bg": "#ffffff",
                "check_bg": "#0078d4",
                "check_fg": "#ffffff",
                "text_fg": "#333333",
                "border": "#cccccc",
                "hover_bg": "#f0f8ff",
                "hover_border": "#0078d4",
                "check_symbol": "✓"
            },
            "accent": {
                "bg": "#ffffff",
                "check_bg": "#0078d4",
                "check_fg": "#ffffff",
                "text_fg": "#0078d4",
                "border": "#0078d4",
                "hover_bg": "#e8f4fd",
                "hover_border": "#106ebe",
                "check_symbol": "✓"
            },
            "success": {
                "bg": "#ffffff",
                "check_bg": "#28a745",
                "check_fg": "#ffffff",
                "text_fg": "#28a745",
                "border": "#28a745",
                "hover_bg": "#e8f5e8",
                "hover_border": "#1e7e34",
                "check_symbol": "✓"
            },
            "warning": {
                "bg": "#ffffff",
                "check_bg": "#ffc107",
                "check_fg": "#000000",
                "text_fg": "#856404",
                "border": "#ffc107",
                "hover_bg": "#fff3cd",
                "hover_border": "#e0a800",
                "check_symbol": "!"
            },
            "error": {
                "bg": "#ffffff",
                "check_bg": "#dc3545",
                "check_fg": "#ffffff",
                "text_fg": "#dc3545",
                "border": "#dc3545",
                "hover_bg": "#f8d7da",
                "hover_border": "#c82333",
                "check_symbol": "✗"
            }
        }

        self.current_style = self.styles.get(style_type, self.styles["modern"])
        self.is_hovered = False

        self.create_widgets()
        self.bind_events()

    def create_widgets(self):
        """创建控件"""
        self.configure(bg=self.current_style["bg"], relief="flat", bd=0)

        # 创建复选框画布
        self.checkbox_canvas = tk.Canvas(
            self,
            width=20,
            height=20,
            bg=self.current_style["bg"],
            highlightthickness=0,
            relief="flat",
            bd=0
        )
        self.checkbox_canvas.pack(side=tk.LEFT, padx=(0, 8))

        # 创建文本标签
        if self.text:
            self.text_label = tk.Label(
                self,
                text=self.text,
                bg=self.current_style["bg"],
                fg=self.current_style["text_fg"],
                font=("微软雅黑", 10),
                anchor="w"
            )
            self.text_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 绘制初始状态
        self.draw_checkbox()

    def draw_checkbox(self):
        """绘制复选框"""
        self.checkbox_canvas.delete("all")

        # 获取当前状态
        is_checked = self.variable.get()

        # 选择颜色
        if self.is_hovered:
            bg_color = self.current_style["hover_bg"] if not is_checked else self.current_style["check_bg"]
            border_color = self.current_style["hover_border"]
        else:
            bg_color = self.current_style["check_bg"] if is_checked else self.current_style["bg"]
            border_color = self.current_style["border"] if is_checked else self.current_style["border"]

        # 绘制复选框背景（圆角矩形）
        self._create_rounded_rectangle(
            self.checkbox_canvas,
            2, 2, 18, 18,
            radius=3,
            fill=bg_color,
            outline=border_color,
            width=2
        )

        # 绘制选中标记
        if is_checked:
            symbol = self.current_style["check_symbol"]
            self.checkbox_canvas.create_text(
                10, 10,
                text=symbol,
                fill=self.current_style["check_fg"],
                font=("微软雅黑", 12, "bold"),
                anchor="center"
            )

    def _create_rounded_rectangle(self, canvas, x1, y1, x2, y2, radius=3, **kwargs):
        """在画布上创建圆角矩形"""
        points = []

        # 计算圆角路径点
        for x, y in [(x1, y1 + radius), (x1, y1), (x1 + radius, y1),
                     (x2 - radius, y1), (x2, y1), (x2, y1 + radius),
                     (x2, y2 - radius), (x2, y2), (x2 - radius, y2),
                     (x1 + radius, y2), (x1, y2), (x1, y2 - radius)]:
            points.extend([x, y])

        return canvas.create_polygon(points, smooth=True, **kwargs)

    def bind_events(self):
        """绑定事件"""
        # 鼠标事件
        self.bind("<Button-1>", self.on_click)
        self.bind("<Enter>", self.on_enter)
        self.bind("<Leave>", self.on_leave)

        self.checkbox_canvas.bind("<Button-1>", self.on_click)
        self.checkbox_canvas.bind("<Enter>", self.on_enter)
        self.checkbox_canvas.bind("<Leave>", self.on_leave)

        if hasattr(self, 'text_label'):
            self.text_label.bind("<Button-1>", self.on_click)
            self.text_label.bind("<Enter>", self.on_enter)
            self.text_label.bind("<Leave>", self.on_leave)

        # 变量变化事件
        self.variable.trace_add("write", self.on_variable_change)

    def on_click(self, event=None):
        """点击事件"""
        # 切换状态
        self.variable.set(not self.variable.get())

        # 执行回调
        if self.command:
            self.command()

    def on_enter(self, event=None):
        """鼠标进入事件"""
        self.is_hovered = True
        self.draw_checkbox()

        # 更新文本颜色
        if hasattr(self, 'text_label'):
            self.text_label.configure(fg=self.current_style["hover_border"])

    def on_leave(self, event=None):
        """鼠标离开事件"""
        self.is_hovered = False
        self.draw_checkbox()

        # 恢复文本颜色
        if hasattr(self, 'text_label'):
            self.text_label.configure(fg=self.current_style["text_fg"])

    def on_variable_change(self, *args):
        """变量变化事件"""
        self.draw_checkbox()

class CheckboxStyleManager:
    """复选框样式管理器"""

    def __init__(self):
        """初始化样式管理器"""
        self.style = None
        self.is_initialized = False
        
    def initialize_styles(self, style_instance=None):
        """初始化美化样式

        Args:
            style_instance: 可选的ttk.Style实例，如果不提供则创建新的
        """
        try:
            if style_instance:
                self.style = style_instance
            else:
                self.style = ttk.Style()

            # 创建美化的复选框样式
            self._create_modern_checkbox_style()
            self._create_accent_checkbox_style()
            self._create_success_checkbox_style()
            self._create_warning_checkbox_style()
            self._create_error_checkbox_style()

            # 将现代化样式设置为默认的TCheckbutton样式
            self._set_default_checkbox_style()

            self.is_initialized = True
            print("复选框样式美化已初始化")
            print("✅ 美化复选框样式已设置为全局默认样式")

        except Exception as e:
            print(f"初始化复选框样式失败: {e}")
            self.is_initialized = False
    
    def _create_modern_checkbox_style(self):
        """创建现代化复选框样式 - 美化选中标记"""
        style_name = "Modern.TCheckbutton"

        # 基础配置 - 增大尺寸，优化间距
        self.style.configure(style_name,
                           focuscolor="none",
                           indicatorsize=20,  # 增大指示器
                           indicatormargins=(4, 4, 6, 4),
                           font=("微软雅黑", 10, "normal"),
                           relief="flat",
                           borderwidth=0)

        # 状态映射 - 丰富的颜色和效果
        self.style.map(style_name,
                     background=[('active', '#f0f8ff'),
                               ('pressed', '#e6f3ff'),
                               ('selected', '#f8f9fa'),
                               ('disabled', '#f5f5f5')],
                     foreground=[('active', '#0066cc'),
                               ('pressed', '#004499'),
                               ('selected', '#333333'),
                               ('disabled', '#999999')],
                     # 美化指示器颜色 - 使用渐变色效果
                     indicatorcolor=[('selected', '#0078d4'),      # 选中时：蓝色
                                   ('selected active', '#106ebe'), # 选中悬停：深蓝
                                   ('selected pressed', '#005a9e'), # 选中按下：更深蓝
                                   ('active', '#b3d9ff'),          # 悬停时：浅蓝
                                   ('pressed', '#99ccff'),         # 按下时：中蓝
                                   ('!selected', '#e0e0e0'),       # 未选中：浅灰
                                   ('disabled', '#f0f0f0')],       # 禁用：更浅灰
                     # 美化指示器边框
                     indicatorrelief=[('selected', 'flat'),
                                    ('!selected', 'flat')],
                     # 选中标记样式 - 隐藏默认的勾选标记
                     indicatoron=[('selected', 0), ('!selected', 0)])

    def _set_default_checkbox_style(self):
        """将美化样式设置为默认的TCheckbutton样式"""
        try:
            # 获取现代化样式的配置
            modern_style = "Modern.TCheckbutton"

            # 将现代化样式的所有配置复制到默认的TCheckbutton样式
            # 这样所有未指定样式的ttk.Checkbutton都会使用美化样式

            # 基础配置
            self.style.configure("TCheckbutton",
                               focuscolor="none",
                               indicatorsize=20,
                               indicatormargins=(4, 4, 6, 4),
                               font=("微软雅黑", 10, "normal"),
                               relief="flat",
                               borderwidth=0)

            # 状态映射
            self.style.map("TCheckbutton",
                         background=[('active', '#f0f8ff'),
                                   ('pressed', '#e6f3ff'),
                                   ('selected', '#f8f9fa'),
                                   ('disabled', '#f5f5f5')],
                         foreground=[('active', '#0066cc'),
                                   ('pressed', '#004499'),
                                   ('selected', '#333333'),
                                   ('disabled', '#999999')],
                         # 美化指示器颜色
                         indicatorcolor=[('selected', '#0078d4'),
                                       ('selected active', '#106ebe'),
                                       ('selected pressed', '#005a9e'),
                                       ('active', '#b3d9ff'),
                                       ('pressed', '#99ccff'),
                                       ('!selected', '#e0e0e0'),
                                       ('disabled', '#f0f0f0')],
                         # 美化指示器边框
                         indicatorrelief=[('selected', 'flat'),
                                        ('!selected', 'flat')],
                         # 选中标记样式 - 隐藏默认的勾选标记
                         indicatoron=[('selected', 0), ('!selected', 0)])

            print("✅ 默认复选框样式已设置为美化样式")

        except Exception as e:
            print(f"设置默认复选框样式失败: {e}")
    
    def _create_accent_checkbox_style(self):
        """创建强调色复选框样式"""
        style_name = "Accent.TCheckbutton"
        
        self.style.configure(style_name,
                           focuscolor="none",
                           indicatorsize=16,
                           indicatormargins=(2, 2, 4, 2),
                           font=("微软雅黑", 9, "bold"))
        
        self.style.map(style_name,
                     background=[('active', '#e8f4fd'),
                               ('pressed', '#cce7f0')],
                     foreground=[('active', '#0078d4'),
                               ('pressed', '#106ebe')],
                     indicatorcolor=[('selected', '#0078d4'),
                                   ('active', '#106ebe'),
                                   ('pressed', '#005a9e')],
                     # 隐藏默认的勾选标记
                     indicatoron=[('selected', 0), ('!selected', 0)])
    
    def _create_success_checkbox_style(self):
        """创建成功状态复选框样式"""
        style_name = "Success.TCheckbutton"
        
        self.style.configure(style_name,
                           focuscolor="none",
                           indicatorsize=16,
                           font=("微软雅黑", 9))
        
        self.style.map(style_name,
                     background=[('active', '#e8f5e8'),
                               ('pressed', '#d4edda')],
                     foreground=[('active', '#28a745'),
                               ('pressed', '#1e7e34')],
                     indicatorcolor=[('selected', '#28a745'),
                                   ('active', '#1e7e34'),
                                   ('pressed', '#155724')],
                     # 隐藏默认的勾选标记
                     indicatoron=[('selected', 0), ('!selected', 0)])
    
    def _create_warning_checkbox_style(self):
        """创建警告状态复选框样式"""
        style_name = "Warning.TCheckbutton"
        
        self.style.configure(style_name,
                           focuscolor="none",
                           indicatorsize=16,
                           font=("微软雅黑", 9))
        
        self.style.map(style_name,
                     background=[('active', '#fff3cd'),
                               ('pressed', '#ffeaa7')],
                     foreground=[('active', '#856404'),
                               ('pressed', '#533f03')],
                     indicatorcolor=[('selected', '#ffc107'),
                                   ('active', '#e0a800'),
                                   ('pressed', '#b69500')],
                     # 隐藏默认的勾选标记
                     indicatoron=[('selected', 0), ('!selected', 0)])
    
    def _create_error_checkbox_style(self):
        """创建错误状态复选框样式"""
        style_name = "Error.TCheckbutton"
        
        self.style.configure(style_name,
                           focuscolor="none",
                           indicatorsize=16,
                           font=("微软雅黑", 9))
        
        self.style.map(style_name,
                     background=[('active', '#f8d7da'),
                               ('pressed', '#f1b0b7')],
                     foreground=[('active', '#721c24'),
                               ('pressed', '#491217')],
                     indicatorcolor=[('selected', '#dc3545'),
                                   ('active', '#c82333'),
                                   ('pressed', '#a71e2a')],
                     # 隐藏默认的勾选标记
                     indicatoron=[('selected', 0), ('!selected', 0)])
    
    def get_style_name(self, style_type="modern"):
        """获取样式名称
        
        Args:
            style_type: 样式类型 ("modern", "accent", "success", "warning", "error")
            
        Returns:
            str: 样式名称
        """
        if not self.is_initialized:
            return "TCheckbutton"  # 返回默认样式
        
        style_map = {
            "modern": "Modern.TCheckbutton",
            "accent": "Accent.TCheckbutton", 
            "success": "Success.TCheckbutton",
            "warning": "Warning.TCheckbutton",
            "error": "Error.TCheckbutton"
        }
        
        return style_map.get(style_type, "Modern.TCheckbutton")
    
    def apply_to_widget(self, widget, style_type="modern"):
        """将样式应用到指定控件
        
        Args:
            widget: ttk.Checkbutton控件
            style_type: 样式类型
        """
        try:
            if self.is_initialized and hasattr(widget, 'configure'):
                style_name = self.get_style_name(style_type)
                widget.configure(style=style_name)
        except Exception as e:
            print(f"应用复选框样式失败: {e}")

# 全局样式管理器实例
_global_checkbox_manager = None

def get_checkbox_manager():
    """获取全局复选框样式管理器"""
    global _global_checkbox_manager
    if _global_checkbox_manager is None:
        _global_checkbox_manager = CheckboxStyleManager()
    return _global_checkbox_manager

def initialize_global_checkbox_styles(style_instance=None):
    """初始化全局复选框样式"""
    manager = get_checkbox_manager()
    manager.initialize_styles(style_instance)
    return manager

def get_checkbox_style(style_type="modern"):
    """获取复选框样式名称"""
    manager = get_checkbox_manager()
    return manager.get_style_name(style_type)

def apply_checkbox_style(widget, style_type="modern"):
    """应用复选框样式到控件"""
    manager = get_checkbox_manager()
    manager.apply_to_widget(widget, style_type)

def create_beautiful_checkbox(parent, text="", variable=None, style_type="modern", command=None, **kwargs):
    """创建美化的复选框控件

    Args:
        parent: 父控件
        text: 显示文本
        variable: 绑定的BooleanVar变量
        style_type: 样式类型 ("modern", "accent", "success", "warning", "error")
        command: 状态改变时的回调函数
        **kwargs: 其他参数

    Returns:
        BeautifulCheckbox: 美化的复选框控件
    """
    return BeautifulCheckbox(parent, text=text, variable=variable,
                           style_type=style_type, command=command, **kwargs)

def apply_global_checkbox_styles(root_widget):
    """递归应用美化样式到所有现有的ttk.Checkbutton控件

    Args:
        root_widget: 根控件，从这里开始递归查找所有复选框
    """
    try:
        manager = get_checkbox_manager()
        if not manager.is_initialized:
            return

        def apply_to_children(widget):
            """递归应用样式到子控件"""
            try:
                # 检查当前控件是否是ttk.Checkbutton
                if isinstance(widget, ttk.Checkbutton):
                    # 如果没有指定样式或使用默认样式，则应用美化样式
                    current_style = widget.cget('style')
                    if not current_style or current_style == 'TCheckbutton':
                        # 默认使用modern样式
                        widget.configure(style='Modern.TCheckbutton')
                        print(f"✅ 已应用美化样式到复选框: {widget.cget('text')}")

                # 递归处理子控件
                for child in widget.winfo_children():
                    apply_to_children(child)

            except Exception as e:
                # 静默处理单个控件的错误，不影响其他控件
                pass

        # 开始递归应用
        apply_to_children(root_widget)
        print("✅ 全局复选框美化样式应用完成")

    except Exception as e:
        print(f"应用全局复选框样式失败: {e}")

def auto_beautify_checkboxes():
    """自动美化所有新创建的ttk.Checkbutton控件"""
    try:
        # 保存原始的ttk.Checkbutton类
        original_checkbutton = ttk.Checkbutton

        class BeautifiedCheckbutton(original_checkbutton):
            """自动美化的ttk.Checkbutton"""
            def __init__(self, parent, **kwargs):
                # 如果没有指定样式，使用美化样式
                if 'style' not in kwargs:
                    kwargs['style'] = 'Modern.TCheckbutton'
                super().__init__(parent, **kwargs)

        # 替换ttk.Checkbutton为美化版本
        ttk.Checkbutton = BeautifiedCheckbutton
        print("✅ 已启用自动复选框美化功能")

    except Exception as e:
        print(f"启用自动复选框美化失败: {e}")

# 测试函数
def test_checkbox_styles():
    """测试复选框样式"""
    root = tk.Tk()
    root.title("🎨 美化复选框样式测试")
    root.geometry("800x700")
    root.configure(bg="#f8f9fa")

    # 创建测试界面
    main_frame = tk.Frame(root, bg="#f8f9fa", padx=30, pady=20)
    main_frame.pack(fill=tk.BOTH, expand=True)

    # 标题
    title_label = tk.Label(main_frame,
                          text="✨ 美化复选框样式对比测试",
                          font=("微软雅黑", 16, "bold"),
                          bg="#f8f9fa",
                          fg="#2E86AB")
    title_label.pack(pady=(0, 30))

    # 说明文字
    desc_label = tk.Label(main_frame,
                         text="对比原始ttk复选框和美化后的自定义复选框效果",
                         font=("微软雅黑", 11),
                         bg="#f8f9fa",
                         fg="#666666")
    desc_label.pack(pady=(0, 20))

    # 原始样式区域
    original_frame = tk.LabelFrame(main_frame,
                                  text="❌ 原始 TTK 复选框样式",
                                  font=("微软雅黑", 12, "bold"),
                                  bg="#f8f9fa",
                                  fg="#dc3545",
                                  padx=20, pady=15)
    original_frame.pack(fill=tk.X, pady=(0, 20))

    # 初始化ttk样式
    initialize_global_checkbox_styles()

    original_options = [
        ("modern", "默认现代化样式"),
        ("accent", "默认强调色样式"),
        ("success", "默认成功状态样式")
    ]

    for style_type, text in original_options:
        var = tk.BooleanVar(value=True)
        cb = ttk.Checkbutton(original_frame, text=text, variable=var,
                           style=get_checkbox_style(style_type))
        cb.pack(anchor=tk.W, pady=5)

    # 美化样式区域
    beautiful_frame = tk.LabelFrame(main_frame,
                                   text="✨ 美化自定义复选框样式",
                                   font=("微软雅黑", 12, "bold"),
                                   bg="#f8f9fa",
                                   fg="#28a745",
                                   padx=20, pady=15)
    beautiful_frame.pack(fill=tk.X, pady=(0, 20))

    # 测试美化复选框
    beautiful_styles = [
        ("modern", "🎨 现代化样式 - 蓝色勾选标记"),
        ("accent", "🔵 强调色样式 - 突出显示效果"),
        ("success", "🟢 成功状态样式 - 绿色勾选标记"),
        ("warning", "🟡 警告状态样式 - 黄色感叹号标记"),
        ("error", "🔴 错误状态样式 - 红色叉号标记")
    ]

    beautiful_vars = []
    for i, (style_type, text) in enumerate(beautiful_styles):
        var = tk.BooleanVar(value=i < 3)  # 前3个默认选中
        beautiful_vars.append(var)

        cb = create_beautiful_checkbox(beautiful_frame,
                                     text=text,
                                     variable=var,
                                     style_type=style_type)
        cb.pack(fill=tk.X, pady=8)

    # 操作按钮区域
    button_frame = tk.Frame(main_frame, bg="#f8f9fa")
    button_frame.pack(pady=20)

    def toggle_all():
        """切换所有美化复选框状态"""
        for var in beautiful_vars:
            var.set(not var.get())

    def demo_animation():
        """演示动画效果"""
        import time
        for var in beautiful_vars:
            var.set(False)
            root.update()
            time.sleep(0.2)

        for var in beautiful_vars:
            var.set(True)
            root.update()
            time.sleep(0.2)

    # 操作按钮
    tk.Button(button_frame, text="🔄 切换所有状态",
             command=toggle_all,
             font=("微软雅黑", 10),
             bg="#0078d4", fg="white",
             relief="flat", padx=15, pady=5).pack(side=tk.LEFT, padx=10)

    tk.Button(button_frame, text="🎬 演示动画效果",
             command=demo_animation,
             font=("微软雅黑", 10),
             bg="#28a745", fg="white",
             relief="flat", padx=15, pady=5).pack(side=tk.LEFT, padx=10)

    # 特性说明
    features_frame = tk.LabelFrame(main_frame,
                                  text="🎯 美化特性",
                                  font=("微软雅黑", 11, "bold"),
                                  bg="#f8f9fa",
                                  padx=15, pady=10)
    features_frame.pack(fill=tk.X, pady=(20, 0))

    features_text = """
✓ 圆角矩形设计，更加现代化
✓ 丰富的颜色搭配和状态指示
✓ 不同样式使用不同的选中标记（✓、!、✗）
✓ 鼠标悬停效果和颜色变化
✓ 平滑的视觉过渡和交互反馈
✓ 支持多种主题和状态样式
    """

    tk.Label(features_frame,
            text=features_text.strip(),
            font=("微软雅黑", 9),
            bg="#f8f9fa",
            fg="#333333",
            justify=tk.LEFT).pack(anchor=tk.W)

    root.mainloop()

if __name__ == "__main__":
    test_checkbox_styles()
