#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
存稿任务管理器 - 负责存稿任务相关功能
遵循MECE原则：与存稿任务相关的所有功能集中管理
"""

import os
import threading
import time
from tkinter import messagebox
from typing import Dict, Any, Optional, List

# logger导入已移除，使用parent_ui.log代替


class DraftTaskManager:
    """存稿任务管理器 - 负责所有存稿任务相关功能"""
    
    def __init__(self, parent_ui, config_manager, account_manager, concurrent_manager):
        """
        初始化存稿任务管理器
        
        Args:
            parent_ui: 主UI实例
            config_manager: 配置管理器
            account_manager: 账号管理器
            concurrent_manager: 并发管理器
        """
        self.parent_ui = parent_ui
        self.config_manager = config_manager
        self.account_manager = account_manager
        self.concurrent_manager = concurrent_manager
        
        # 任务状态
        self.is_running = False
        self.task_thread = None
        self.account_progress = {}
        
        # 存稿详情数据
        self.draft_details = []

        # 总账号数（用于进度计算）
        self.total_accounts = 0
    
    def start_task(self):
        """开始存稿任务"""
        # 移除DEBUG日志，简化输出

        if self.is_running:
            self.parent_ui.log("⚠️ 任务已在运行中")
            messagebox.showwarning("警告", "任务已在运行中")
            return

        # 启动计时器和重置统计
        self.parent_ui.start_runtime_timer()
        self.parent_ui.reset_success_fail_stats()

        # 检查账号选择状态，不选择账号时为全部账号存稿
        selected_account = self.parent_ui.selected_account.get()

        if not selected_account:
            if not self.parent_ui.accounts:
                self.parent_ui.log("❌ 账号列表为空，请先添加账号")
                messagebox.showwarning("警告", "账号列表为空，请先添加账号")
                return
            self.parent_ui.log(f"🚀 开始为全部 {len(self.parent_ui.accounts)} 个账号存稿")
            self.total_accounts = len(self.parent_ui.accounts)
        else:
            self.parent_ui.log(f"🚀 开始为账号 {selected_account} 存稿")
            self.total_accounts = 1

        # 确保目录存在
        self._create_required_dirs()

        # 更新UI状态
        print("🔍 [DEBUG] 更新UI状态...")
        self.is_running = True
        # 同步主UI的运行状态
        self.parent_ui.is_running = True
        if hasattr(self.parent_ui, 'start_button'):
            self.parent_ui.start_button["state"] = "disabled"
            print("🔍 [DEBUG] 开始按钮已禁用")
        if hasattr(self.parent_ui, 'stop_button'):
            self.parent_ui.stop_button["state"] = "normal"
            print("🔍 [DEBUG] 停止按钮已启用")
        if hasattr(self.parent_ui, 'status_text'):
            self.parent_ui.status_text.config(text="运行中...")
            print("🔍 [DEBUG] 状态文本已更新")

        # 获取并发账号数量
        print("🔍 [DEBUG] 获取并发配置...")
        concurrent_num = self.config_manager.get("concurrent_accounts", 1, platform=self.parent_ui.current_platform)
        print(f"🔍 [DEBUG] 并发账号数量: {concurrent_num}")

        # 如果选择了单个账号或并发数量为1，则使用单线程模式
        print(f"🔍 [DEBUG] 判断线程模式: selected_account='{selected_account}', concurrent_num={concurrent_num}")
        print(f"🔍 [DEBUG] 条件判断: selected_account={bool(selected_account)}, concurrent_num==1={concurrent_num==1}")
        if selected_account or concurrent_num == 1:
            self.parent_ui.log("使用单线程模式处理账号")
            self.parent_ui.log(f"🔍 [DEBUG] 准备启动单线程任务，参数: {selected_account if selected_account else 'all'}")
            # 启动任务线程 - 如果未选择账号，传入"all"，让_single_task_thread处理全部账号
            self.task_thread = threading.Thread(
                target=self._single_task_thread,
                args=(selected_account if selected_account else "all",),
                daemon=True
            )
            self.task_thread.start()
            self.parent_ui.log("🔍 [DEBUG] 单线程任务已启动")
        else:
            # 使用多线程模式处理所有账号
            # 使用主UI已经加载好的账号列表，而不是重新加载
            print("🔍 [DEBUG] 进入多线程模式分支")
            self.parent_ui.log(f"使用多线程模式处理 {len(self.parent_ui.accounts)} 个账号，并发数: {concurrent_num}")
            print(f"🔍 [DEBUG] 使用多线程模式处理 {len(self.parent_ui.accounts)} 个账号，并发数: {concurrent_num}")
            self.parent_ui.log(f"🔍 [DEBUG] 准备启动多线程任务")
            print("🔍 [DEBUG] 准备启动多线程任务")

            try:
                print("🔍 [DEBUG] 创建多线程任务线程...")
                self.task_thread = threading.Thread(
                    target=self._concurrent_task_thread,
                    args=(self.parent_ui.accounts.copy(),),
                    daemon=True
                )
                print("🔍 [DEBUG] 启动多线程任务线程...")
                self.task_thread.start()
                print("🔍 [DEBUG] 多线程任务线程启动成功")
                self.parent_ui.log("🔍 [DEBUG] 多线程任务已启动")
            except Exception as e:
                print(f"❌ [DEBUG] 启动多线程任务失败: {str(e)}")
                import traceback
                traceback.print_exc()
    
    def start_single_task(self, account):
        """开始单个账号的存稿任务"""
        if self.is_running:
            messagebox.showwarning("警告", "任务已在运行中")
            return

        # 启动计时器和重置统计
        self.parent_ui.start_runtime_timer()
        self.parent_ui.reset_success_fail_stats()

        # 检查已处理视频目录（存稿任务从这里读取视频）
        processed_dir = self.config_manager.get("processed_dir", "", platform=self.parent_ui.current_platform)
        if not processed_dir:
            messagebox.showwarning("警告", "请先设置已处理视频目录")
            return

        # 确保目录存在（自动创建）
        if not os.path.exists(processed_dir):
            try:
                os.makedirs(processed_dir, exist_ok=True)
                self.parent_ui.log(f"✅ 已创建已处理视频目录: {processed_dir}")
            except Exception as e:
                self.parent_ui.log(f"❌ 创建已处理视频目录失败: {e}")
                messagebox.showerror("错误", f"创建已处理视频目录失败: {e}")
                return
        
        # 开始单个任务
        self.is_running = True
        # 同步主UI的运行状态
        self.parent_ui.is_running = True
        self.parent_ui.log(f"🚀 开始单个账号存稿任务: {account}")

        # 初始化单账号任务进度
        self.parent_ui.update_task_progress(0, 1)

        # 更新UI状态
        if hasattr(self.parent_ui, 'start_button'):
            self.parent_ui.start_button["state"] = "disabled"
        if hasattr(self.parent_ui, 'stop_button'):
            self.parent_ui.stop_button["state"] = "normal"
        if hasattr(self.parent_ui, 'status_text'):
            self.parent_ui.status_text.config(text="运行中...")

        # 启动单个任务线程
        self.task_thread = threading.Thread(
            target=self._single_task_thread,
            args=(account,),
            daemon=True
        )
        self.task_thread.start()
    

    
    def _process_all_accounts(self):
        """处理所有账号"""
        try:
            # 创建账号列表副本以避免遍历时修改
            accounts = list(self.account_manager.load_accounts())
            
            if not accounts:
                self.parent_ui.log("❌ 没有找到账号")
                return
            
            self.parent_ui.log(f"📋 共找到 {len(accounts)} 个账号")
            
            # 检查是否启用并发模式
            concurrent_mode = self.config_manager.get("concurrent_mode", False, platform=self.parent_ui.current_platform)
            
            if concurrent_mode:
                self.parent_ui.log("🔄 使用并发模式处理账号")
                self.start_concurrent_task(accounts)
            else:
                self.parent_ui.log("📝 使用顺序模式处理账号")
                self._process_accounts_sequentially(accounts)
            
        except Exception as e:
            error_msg = f"处理所有账号时发生错误: {str(e)}"
            self.parent_ui.log(f"❌ {error_msg}")
        finally:
            self._task_completed()
    
    def _process_accounts_sequentially(self, accounts):
        """顺序处理账号"""
        success_count = 0
        fail_count = 0
        
        for i, account in enumerate(accounts, 1):
            if not self.is_running:
                self.parent_ui.log("⏹️ 任务已停止")
                break
            
            try:
                self.parent_ui.log(f"📝 正在处理账号 {account} ({i}/{len(accounts)})")
                
                # 更新进度 - 修复lambda变量绑定问题
                self.parent_ui.root.after(0, lambda current=i-1, total=len(accounts): self.parent_ui.update_task_progress(current, total))
                self.parent_ui.root.after(0, lambda acc=account: self.parent_ui.update_current_account_status(acc, "processing"))
                
                # 根据平台处理账号
                current_platform = self.parent_ui.current_platform
                
                if current_platform == "netease":
                    result = self._process_netease_account(account)
                elif current_platform == "toutiao":
                    result = self._process_toutiao_account(account)
                elif current_platform == "dayu":
                    result = self._process_dayu_account(account)
                else:
                    self.parent_ui.log(f"❌ 不支持的平台: {current_platform}")
                    fail_count += 1
                    continue
                
                if result:
                    success_count += 1
                    self.parent_ui.log(f"✅ 账号 {account} 处理成功")
                else:
                    fail_count += 1
                    self.parent_ui.log(f"❌ 账号 {account} 处理失败")
                
            except Exception as e:
                fail_count += 1
                error_msg = f"处理账号 {account} 时发生错误: {str(e)}"
                self.parent_ui.log(f"❌ {error_msg}")
        

        self.parent_ui.log(f"🎉 所有账号处理完成！成功: {success_count}, 失败: {fail_count}")
    
    def start_concurrent_task(self, accounts):
        """开始并发处理多个账号"""
        if not accounts:
            self.parent_ui.log("❌ 没有账号需要处理")
            return

        # 启动计时器和重置统计
        self.parent_ui.start_runtime_timer()
        self.parent_ui.reset_success_fail_stats()

        self.parent_ui.log(f"🔄 开始并发处理 {len(accounts)} 个账号")
        
        # 启动并发任务线程
        concurrent_thread = threading.Thread(
            target=self._concurrent_task_thread,
            args=(accounts,),
            daemon=True
        )
        concurrent_thread.start()
    
    def _concurrent_task_thread(self, accounts):
        """并发任务线程"""
        print("🔍 [DEBUG] _concurrent_task_thread 开始执行")
        self.parent_ui.log("🔍 [DEBUG] _concurrent_task_thread 开始执行")

        try:
            # 根据当前平台选择处理器和并发管理器
            current_platform = self.parent_ui.current_platform
            print(f"🔍 [DEBUG] 当前平台: {current_platform}")
            print(f"🔍 [DEBUG] 账号数量: {len(accounts)}")

            # 根据平台使用不同的并发处理方式
            if current_platform == "netease":
                # 网易号使用原有的通用并发管理器
                return self._process_netease_concurrent(accounts)
            elif current_platform == "toutiao":
                # 头条号使用专用的并发管理器
                return self._process_toutiao_concurrent(accounts)
            elif current_platform == "dayu":
                # 大鱼号使用通用并发管理器
                return self._process_dayu_concurrent(accounts)
            else:
                self.parent_ui.log(f"❌ 不支持的平台: {current_platform}")
                print(f"❌ 不支持的平台: {current_platform}")
                return
            
            # 设置进度回调
            def progress_callback(account, progress, status, message=None, video_info=None):
                # 确保所有日志消息都能正确传递给UI
                # 注意：不在这里记录日志，避免与ConcurrentManager的_account_log重复

                # 更新账号进度
                self.update_account_progress(account, progress, status, message, {
                    'account': account,
                    'progress': progress,
                    'status': status,
                    'message': message,
                    'video_info': video_info
                })

                # 不在这里记录日志，因为ConcurrentManager的_account_log已经处理了
                # 这样避免了重复日志输出的问题

                # 如果是存稿成功，记录详情
                if (status == "完成" or status == "success") and video_info:
                    video_path = video_info.get("video_path", video_info.get("path", ""))
                    video_status = video_info.get("status", "成功")
                    reason = video_info.get("reason", "")
                    screenshot = video_info.get("screenshot", "")
                    self.add_draft_detail(account, video_path, video_status, reason, screenshot, video_info)
                elif status == "失败" or status == "error":
                    # 记录失败详情
                    if video_info:
                        video_path = video_info.get("video_path", "")
                        reason = video_info.get("reason", message or "未知错误")
                        screenshot = video_info.get("screenshot", "")
                        self.add_draft_detail(account, video_path, "失败", reason, screenshot, video_info)
                    else:
                        self.add_draft_detail(account, "", "失败", message or "未知错误", "", None)
            
            # 获取配置信息
            account_dir = self.config_manager.get("account_dir", "", platform=current_platform)
            processed_dir = self.config_manager.get("processed_dir", "", platform=current_platform)
            processed_covers_dir = self.config_manager.get("processed_covers_dir", "", platform=current_platform)
            archive_completed = self.config_manager.get("archive_completed", False, platform=current_platform)
            headless_mode = self.config_manager.get("headless_mode", True, platform=current_platform)
            draft_limit = self.config_manager.get("draft_limit", 0, platform=current_platform)
            loop_limit = self.config_manager.get("loop_limit", 0, platform=current_platform)
            concurrent_num = self.config_manager.get("concurrent_accounts", 1, platform=current_platform)

            print("🔍 [DEBUG] 准备配置并发管理器...")

            # 更新并发管理器配置 - 与原始版本相同的方式
            self.concurrent_manager.account_dir = account_dir
            self.concurrent_manager.processed_dir = processed_dir
            self.concurrent_manager.processed_covers_dir = processed_covers_dir
            self.concurrent_manager.archive_completed = archive_completed
            self.concurrent_manager.headless_mode = headless_mode
            self.concurrent_manager.draft_limit = draft_limit
            self.concurrent_manager.loop_limit = loop_limit
            self.concurrent_manager.max_workers = concurrent_num
            self.concurrent_manager.screenshots_dir = self.parent_ui.screenshots_dir.get()
            self.concurrent_manager.random_video_allocation = self.parent_ui.random_video_allocation.get()

            print("🔍 [DEBUG] 并发管理器配置完成")

            try:
                print("🔍 [DEBUG] 开始调用并发管理器...")

                # 设置进度回调 - 与原始版本相同的方式
                self.concurrent_manager.set_progress_callback(progress_callback)

                # 启动并发管理器 - 与原始版本相同的方式
                success = self.concurrent_manager.start(accounts)

                print(f"🔍 [DEBUG] 并发管理器调用完成，结果: {success}")
            except Exception as e:
                print(f"❌ [DEBUG] 并发管理器调用失败: {str(e)}")
                import traceback
                traceback.print_exc()
                raise

            # 更新UI状态 - 与原始版本相同的方式
            if success:
                self.parent_ui.log("所有账号处理完成")
            else:
                self.parent_ui.log("部分账号处理失败")

                # 更新总进度
                completed = 0
                for acc, prog_data in self.account_progress.items():
                    if prog_data.get("progress", 0) >= 100:
                        completed += 1
                total_progress = int(completed / len(accounts) * 100)

            # 不需要手动统计结果，因为并发管理器会通过回调更新进度
            print("🔍 [DEBUG] 并发处理完成")

        except Exception as e:
            error_msg = f"并发处理失败: {str(e)}"
            self.parent_ui.log(f"❌ {error_msg}")
            import traceback
            traceback.print_exc()

    def _process_toutiao_concurrent(self, accounts):
        """处理头条号并发任务"""
        try:
            self.parent_ui.log("🔄 启动头条号专用并发处理")

            # 导入头条号处理器
            from 网易号存稿.platforms.toutiao.processor import ToutiaoDraftProcessor

            # 获取配置信息
            current_platform = self.parent_ui.current_platform
            account_dir = self.config_manager.get("account_dir", "", platform=current_platform)
            processed_dir = self.config_manager.get("processed_dir", "", platform=current_platform)
            processed_covers_dir = self.config_manager.get("processed_covers_dir", "", platform=current_platform)
            archive_completed = self.config_manager.get("archive_completed", False, platform=current_platform)
            headless_mode = self.config_manager.get("headless_mode", True, platform=current_platform)
            draft_limit = self.config_manager.get("draft_limit", 0, platform=current_platform)
            loop_limit = self.config_manager.get("loop_limit", 0, platform=current_platform)
            concurrent_num = self.config_manager.get("concurrent_accounts", 1, platform=current_platform)

            # 创建头条号处理器
            processor = ToutiaoDraftProcessor(
                account_dir=account_dir,
                processed_dir=processed_dir,
                processed_covers_dir=processed_covers_dir,
                archive_completed=archive_completed,
                headless_mode=headless_mode,
                draft_limit=draft_limit,
                loop_limit=loop_limit,
                log_callback=self.parent_ui.log,
                screenshots_dir=self.parent_ui.screenshots_dir.get(),
                random_video_allocation=self.parent_ui.random_video_allocation.get(),
                add_draft_detail_callback=self.add_draft_detail
            )

            # 设置进度回调
            def progress_callback(account, progress, status, details, progress_data=None):
                # 更新账号进度
                self.update_account_progress(account, progress, status, details, progress_data or {})

                # 如果有存稿详情，记录
                if progress_data and progress_data.get("successful_drafts", 0) > 0:
                    # 这里可以添加存稿详情记录逻辑
                    pass

            # 启动头条号并发处理
            success = processor.start_concurrent(
                accounts=accounts,
                max_workers=concurrent_num,
                progress_callback=progress_callback
            )

            if success:
                self.parent_ui.log(f"✅ 头条号并发处理启动成功，处理 {len(accounts)} 个账号")

                # 等待处理完成
                while processor.is_concurrent_running():
                    time.sleep(1)

                # 获取最终进度
                final_progress = processor.get_concurrent_progress()
                self.parent_ui.log(f"🎉 头条号并发处理完成")

                # 更新最终统计
                success_count = 0
                for account, progress_info in final_progress.items():
                    if progress_info.get("successful_drafts", 0) > 0:
                        success_count += 1

                self.parent_ui.log(f"📊 处理结果：成功 {success_count} 个账号，失败 {len(accounts) - success_count} 个账号")

            else:
                self.parent_ui.log("❌ 头条号并发处理启动失败")

        except Exception as e:
            self.parent_ui.log(f"❌ 头条号并发处理异常: {str(e)}")
            import traceback
            traceback.print_exc()

    def _process_netease_concurrent(self, accounts):
        """处理网易号并发任务（使用原有逻辑）"""
        try:
            # 这里保留原有的网易号并发处理逻辑
            processor_class = "网易号存稿.draft.processor.DraftProcessor"

            # 设置进度回调
            def progress_callback(account, progress, status, message=None, video_info=None):
                # 更新账号进度
                self.update_account_progress(account, progress, status, message, {
                    'account': account,
                    'progress': progress,
                    'status': status,
                    'message': message,
                    'video_info': video_info
                })

                # 如果是存稿成功，记录详情
                if (status == "完成" or status == "success") and video_info:
                    video_path = video_info.get("video_path", video_info.get("path", ""))
                    video_status = video_info.get("status", "成功")
                    reason = video_info.get("reason", "")
                    screenshot = video_info.get("screenshot", "")
                    self.add_draft_detail(account, video_path, video_status, reason, screenshot, video_info)
                elif status == "失败" or status == "error":
                    # 记录失败详情
                    if video_info:
                        video_path = video_info.get("video_path", "")
                        reason = video_info.get("reason", message or "未知错误")
                        screenshot = video_info.get("screenshot", "")
                        self.add_draft_detail(account, video_path, "失败", reason, screenshot, video_info)
                    else:
                        self.add_draft_detail(account, "", "失败", message or "未知错误", "", None)

            # 获取配置信息
            current_platform = self.parent_ui.current_platform
            account_dir = self.config_manager.get("account_dir", "", platform=current_platform)
            processed_dir = self.config_manager.get("processed_dir", "", platform=current_platform)
            processed_covers_dir = self.config_manager.get("processed_covers_dir", "", platform=current_platform)
            archive_completed = self.config_manager.get("archive_completed", False, platform=current_platform)
            headless_mode = self.config_manager.get("headless_mode", True, platform=current_platform)
            draft_limit = self.config_manager.get("draft_limit", 0, platform=current_platform)
            loop_limit = self.config_manager.get("loop_limit", 0, platform=current_platform)
            concurrent_num = self.config_manager.get("concurrent_accounts", 1, platform=current_platform)

            print("🔍 [DEBUG] 准备配置并发管理器...")

            # 更新并发管理器配置 - 与原始版本相同的方式
            self.concurrent_manager.account_dir = account_dir
            self.concurrent_manager.processed_dir = processed_dir
            self.concurrent_manager.processed_covers_dir = processed_covers_dir
            self.concurrent_manager.archive_completed = archive_completed
            self.concurrent_manager.headless_mode = headless_mode
            self.concurrent_manager.draft_limit = draft_limit
            self.concurrent_manager.loop_limit = loop_limit
            self.concurrent_manager.max_workers = concurrent_num
            self.concurrent_manager.screenshots_dir = self.parent_ui.screenshots_dir.get()
            self.concurrent_manager.random_video_allocation = self.parent_ui.random_video_allocation.get()

            print("🔍 [DEBUG] 并发管理器配置完成")

            try:
                print("🔍 [DEBUG] 开始调用并发管理器...")

                # 设置进度回调 - 与原始版本相同的方式
                self.concurrent_manager.set_progress_callback(progress_callback)

                # 启动并发管理器 - 与原始版本相同的方式
                success = self.concurrent_manager.start(accounts)

                print(f"🔍 [DEBUG] 并发管理器调用完成，结果: {success}")
            except Exception as e:
                print(f"❌ [DEBUG] 并发管理器调用失败: {str(e)}")
                import traceback
                traceback.print_exc()
                raise

        except Exception as e:
            self.parent_ui.log(f"❌ 网易号并发处理异常: {str(e)}")
            import traceback
            traceback.print_exc()

    def _process_dayu_concurrent(self, accounts):
        """处理大鱼号并发任务"""
        try:
            # 大鱼号暂时使用通用并发管理器
            self.parent_ui.log("⚠️ 大鱼号并发处理暂未实现，使用顺序处理")
            self._process_accounts_sequentially(accounts)
        except Exception as e:
            self.parent_ui.log(f"❌ 大鱼号并发处理异常: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def _process_netease_account(self, account):
        """处理网易号账号"""
        try:
            from 网易号存稿.draft.processor import DraftProcessor

            # 获取当前平台配置
            current_platform = self.parent_ui.current_platform

            # 获取账号目录 - 与数据查询管理器保持一致
            if self.account_manager and hasattr(self.account_manager, 'account_dir'):
                account_dir = self.account_manager.account_dir
            else:
                account_dir = self.config_manager.get("account_dir", "", platform=current_platform)

            # 创建存稿处理器
            processor = DraftProcessor(
                account_dir=account_dir,
                processed_dir=self.config_manager.get("processed_dir", "", platform=current_platform),
                processed_covers_dir=self.config_manager.get("processed_covers_dir", "", platform=current_platform),
                archive_completed=self.config_manager.get("archive_completed", False, platform=current_platform),
                headless_mode=self.config_manager.get("headless_mode", True, platform=current_platform),
                draft_limit=self.config_manager.get("draft_limit", 0, platform=current_platform),
                loop_limit=self.config_manager.get("loop_limit", 0, platform=current_platform),
                log_callback=self.parent_ui.log,
                screenshots_dir=self.config_manager.get("screenshots_dir", "", platform=current_platform),
                random_video_allocation=self.config_manager.get("random_video_allocation", True, platform=current_platform),
                update_draft_detail_callback=self.parent_ui.add_draft_detail
            )

            # 处理账号
            success, processed_videos = processor.process_account(account)
            return success

        except Exception as e:
            self.parent_ui.log(f"❌ 处理网易号账号 {account} 失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _process_toutiao_account(self, account):
        """处理头条账号"""
        try:
            # 使用头条专用处理器
            from 网易号存稿.platforms.toutiao.processor import ToutiaoDraftProcessor

            # 获取当前平台配置
            current_platform = self.parent_ui.current_platform

            # 获取账号目录 - 与数据查询管理器保持一致
            if self.account_manager and hasattr(self.account_manager, 'account_dir'):
                account_dir = self.account_manager.account_dir
            else:
                account_dir = self.config_manager.get("account_dir", "", platform=current_platform)

            # 创建头条存稿处理器
            processor = ToutiaoDraftProcessor(
                account_dir=account_dir,
                processed_dir=self.config_manager.get("processed_dir", "", platform=current_platform),
                processed_covers_dir=self.config_manager.get("processed_covers_dir", "", platform=current_platform),
                archive_completed=self.config_manager.get("archive_completed", False, platform=current_platform),
                headless_mode=self.config_manager.get("headless_mode", True, platform=current_platform),
                draft_limit=self.config_manager.get("draft_limit", 0, platform=current_platform),
                loop_limit=self.config_manager.get("loop_limit", 0, platform=current_platform),
                log_callback=self.parent_ui.log,
                screenshots_dir=self.config_manager.get("screenshots_dir", "", platform=current_platform),
                random_video_allocation=self.config_manager.get("random_video_allocation", True, platform=current_platform),
                add_draft_detail_callback=self.parent_ui.add_draft_detail
            )

            # 处理账号
            success, processed_videos = processor.process_account(account)
            return success

        except Exception as e:
            self.parent_ui.log(f"❌ 处理头条账号 {account} 失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _process_dayu_account(self, account):
        """处理大鱼号账号"""
        try:
            # 使用大鱼号专用处理器
            from 网易号存稿.platforms.dayu.processor import DayuDraftProcessor

            # 获取当前平台配置
            current_platform = self.parent_ui.current_platform

            # 获取账号目录 - 与数据查询管理器保持一致
            if self.account_manager and hasattr(self.account_manager, 'account_dir'):
                account_dir = self.account_manager.account_dir
            else:
                account_dir = self.config_manager.get("account_dir", "", platform=current_platform)

            # 创建大鱼号存稿处理器
            processor = DayuDraftProcessor(
                account_dir=account_dir,
                processed_dir=self.config_manager.get("processed_dir", "", platform=current_platform),
                processed_covers_dir=self.config_manager.get("processed_covers_dir", "", platform=current_platform),
                archive_completed=self.config_manager.get("archive_completed", False, platform=current_platform),
                headless_mode=self.config_manager.get("headless_mode", True, platform=current_platform),
                draft_limit=self.config_manager.get("draft_limit", 0, platform=current_platform),
                loop_limit=self.config_manager.get("loop_limit", 0, platform=current_platform),
                log_callback=self.parent_ui.log,
                screenshots_dir=self.config_manager.get("screenshots_dir", "", platform=current_platform),
                random_video_allocation=self.config_manager.get("random_video_allocation", True, platform=current_platform),
                add_draft_detail_callback=self.parent_ui.add_draft_detail
            )

            # 处理账号
            success, processed_videos = processor.process_account(account)
            return success

        except Exception as e:
            self.parent_ui.log(f"❌ 处理大鱼号账号 {account} 失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    

    
    def stop_task(self):
        """停止存稿任务"""
        if not self.is_running:
            return

        self.is_running = False
        # 同步主UI的运行状态
        self.parent_ui.is_running = False
        self.parent_ui.log("⏹️ 正在停止存稿任务...")

        # 停止并发管理器
        if self.concurrent_manager:
            self.concurrent_manager.stop()

        # 更新UI状态
        if hasattr(self.parent_ui, 'start_button'):
            self.parent_ui.start_button["state"] = "normal"
        if hasattr(self.parent_ui, 'stop_button'):
            self.parent_ui.stop_button["state"] = "disabled"
        if hasattr(self.parent_ui, 'status_text'):
            self.parent_ui.status_text.config(text="就绪")

        self.parent_ui.log("⏹️ 存稿任务已停止")
    
    def update_account_progress(self, account, progress, status=None, details=None, progress_data=None):
        """更新账号进度"""
        # 确保主UI的账号进度字典已初始化
        if not hasattr(self.parent_ui, 'account_progress'):
            self.parent_ui.account_progress = {}

        # 提取存稿成功数量
        successful_drafts = 0
        if progress_data and isinstance(progress_data, dict):
            successful_drafts = progress_data.get("successful_drafts", 0)

        # 更新主UI的账号进度信息（这是进度条对话框读取的数据源）
        self.parent_ui.account_progress[account] = {
            'progress': progress,
            'status': status or 'processing',
            'details': details,
            'last_update': time.strftime("%Y-%m-%d %H:%M:%S"),
            'successful_drafts': successful_drafts,  # 确保存稿成功数量被正确设置
            'data': progress_data
        }

        # 同时更新本地的账号进度信息（保持兼容性）
        if not hasattr(self, 'account_progress'):
            self.account_progress = {}

        self.account_progress[account] = {
            'progress': progress,
            'status': status or 'processing',
            'details': details,
            'last_update': time.time(),
            'successful_drafts': successful_drafts,
            'data': progress_data
        }

        # 更新UI显示（在主线程中执行）
        self.parent_ui.root.after(0, lambda: self._update_progress_display(account, progress, status, details))
    
    def _update_progress_display(self, account, progress, status, details):
        """更新进度显示"""
        try:
            # 更新当前账号状态
            self.parent_ui.update_current_account_status(account, status or "processing")
            
            # 如果有详细信息，记录日志
            if details:
                self.parent_ui.log(f"[{account}] {details}")
        except Exception as e:
            self.parent_ui.log(f"❌ 更新进度显示失败: {e}")
    
    def add_draft_detail(self, account, video_path, status, reason="", screenshot="", video_info=None):
        """
        添加或更新存稿详情记录

        Args:
            account: 账号名称
            video_path: 视频文件路径（可以为空字符串，表示账号级别的错误，如登录失败）
            status: 状态（成功/失败）
            reason: 原因（失败时的错误信息）
            screenshot: 截图路径（失败时的截图）
            video_info: 视频信息字典（可选），包含额外信息如存稿成功数量
        """
        # 确保账号在存稿详情数据结构中
        if account not in self.parent_ui.account_details:
            self.parent_ui.account_details[account] = []

        from datetime import datetime
        import os
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 处理视频名称
        if video_path:
            # 正常视频文件
            video_name = os.path.basename(video_path)
        else:
            # 账号级别的错误（如登录失败）
            if "登录失败" in reason:
                video_name = "账号登录失败"
            else:
                video_name = "未知错误"

        # 获取存稿成功数
        successful_drafts = 0

        # 如果提供了视频信息字典，从中获取存稿成功数
        if video_info and isinstance(video_info, dict):
            # 直接使用视频信息中的存稿成功数
            if "successful_drafts" in video_info:
                successful_drafts = video_info["successful_drafts"]
                self.parent_ui.log(f"从视频信息中获取存稿成功: {successful_drafts}")

        # 如果是成功状态，从账号进度中获取存稿成功数
        if status == "成功" and hasattr(self.parent_ui, 'account_progress') and account in self.parent_ui.account_progress:
            # 从账号进度中获取当前存稿成功数
            current_successful_drafts = self.parent_ui.account_progress[account].get("successful_drafts", 0)

            # 使用账号进度中的值，这是由processor计算的准确值
            if current_successful_drafts > 0:
                successful_drafts = current_successful_drafts

        # 如果存稿成功数为空字符串，设置为0
        if successful_drafts == "":
            successful_drafts = 0

        # 检查是否已存在该视频的记录
        found = False
        for i, detail in enumerate(self.parent_ui.account_details[account]):
            if detail.get("视频") == video_name:
                # 获取旧记录中的存稿成功数（如果有）
                old_successful_drafts = detail.get("存稿成功", 0)
                # 兼容旧版本，如果"存稿成功"字段不存在，则尝试使用"存稿成功数量"字段
                if old_successful_drafts == 0:
                    old_successful_drafts = detail.get("存稿成功数量", 0)

                # 如果存稿成功数为空字符串，设置为0
                if old_successful_drafts == "":
                    old_successful_drafts = 0

                # 更新现有记录，包含存稿成功数
                self.parent_ui.account_details[account][i] = {
                    "时间": current_time,
                    "视频": video_name,
                    "状态": status,
                    "原因": reason,
                    "截图": screenshot,
                    "存稿成功": successful_drafts  # 使用统一的"存稿成功"字段
                }
                found = True
                break

        # 如果没有找到现有记录，则添加新记录
        # 但是，如果是同一个账号的新视频，且已经有其他视频记录，则替换最旧的一条记录
        if not found:
            if account in self.parent_ui.account_details and len(self.parent_ui.account_details[account]) > 0:
                # 找到最旧的记录（按时间排序）
                oldest_index = 0
                oldest_time = None

                for i, detail in enumerate(self.parent_ui.account_details[account]):
                    detail_time = detail.get("时间", "")
                    if oldest_time is None or detail_time < oldest_time:
                        oldest_time = detail_time
                        oldest_index = i

                # 获取旧记录中的存稿成功数（如果有）
                old_successful_drafts = self.parent_ui.account_details[account][oldest_index].get("存稿成功", 0)
                # 兼容旧版本，如果"存稿成功"字段不存在，则尝试使用"存稿成功数量"字段
                if old_successful_drafts == 0:
                    old_successful_drafts = self.parent_ui.account_details[account][oldest_index].get("存稿成功数量", 0)

                # 如果存稿成功数为空字符串，设置为0
                if old_successful_drafts == "":
                    old_successful_drafts = 0

                # 替换最旧的记录，包含存稿成功数
                self.parent_ui.account_details[account][oldest_index] = {
                    "时间": current_time,
                    "视频": video_name,
                    "状态": status,
                    "原因": reason,
                    "截图": screenshot,
                    "存稿成功": successful_drafts  # 使用统一的"存稿成功"字段
                }
            else:
                # 如果账号没有任何记录，则添加新记录，包含存稿成功数
                self.parent_ui.account_details[account].append({
                    "时间": current_time,
                    "视频": video_name,
                    "状态": status,
                    "原因": reason,
                    "截图": screenshot,
                    "存稿成功": successful_drafts  # 使用统一的"存稿成功"字段
                })

        # 更新账号进度中的存稿成功数量
        if hasattr(self.parent_ui, 'account_progress'):
            if account not in self.parent_ui.account_progress:
                self.parent_ui.account_progress[account] = {
                    'progress': 0,
                    'status': 'processing',
                    'details': '',
                    'last_update': current_time,
                    'successful_drafts': 0
                }

            # 更新存稿成功数量
            self.parent_ui.account_progress[account]['successful_drafts'] = successful_drafts
            self.parent_ui.account_progress[account]['last_update'] = current_time

            # 如果是成功状态，更新状态
            if status == "成功":
                self.parent_ui.account_progress[account]['status'] = "processing"
                self.parent_ui.account_progress[account]['details'] = f"已存稿 {successful_drafts} 个视频"
            elif status == "失败":
                self.parent_ui.account_progress[account]['status'] = "失败"
                self.parent_ui.account_progress[account]['details'] = reason

            self.parent_ui.log(f"已更新账号 {account} 的进度信息: 存稿成功数={successful_drafts}, 状态={status}")

        # 更新账号管理器中的状态信息（关键修复：确保状态列正确显示）
        if hasattr(self.parent_ui, 'account_manager') and self.parent_ui.account_manager:
            try:
                # 根据失败原因确定状态描述
                if status == "失败":
                    if "登录失败" in reason or "Cookie" in reason:
                        # 登录失败或Cookie相关问题（优先判断登录失败）
                        if "登录失败" in reason:
                            status_desc = "登录失效"
                        elif "过期" in reason:
                            status_desc = "Cookie过期"
                        else:
                            status_desc = "Cookie失效"
                    else:
                        status_desc = "存稿失败"
                elif status == "成功":
                    status_desc = "正常"
                else:
                    status_desc = status

                # 更新账号状态到账号管理器
                update_success = self.parent_ui.account_manager.update_account_status(account, status_desc, reason)
                if update_success:
                    self.parent_ui.log(f"已更新账号 {account} 状态到账号管理器: {status_desc}")

                    # 刷新主界面账号列表显示
                    if hasattr(self.parent_ui, 'filter_main_accounts'):
                        self.parent_ui.root.after(100, self.parent_ui.filter_main_accounts)
                else:
                    self.parent_ui.log(f"更新账号 {account} 状态到账号管理器失败")

            except Exception as e:
                self.parent_ui.log(f"更新账号状态到账号管理器时出错: {str(e)}")

        # 保存数据
        self.save_draft_details()
    
    def log_last_draft_detail(self, account, video_path, status):
        """记录最后一条存稿详情记录的日志"""
        try:
            if status == "成功":
                # 获取视频文件名
                import os
                video_name = os.path.basename(video_path) if video_path else "未知视频"
                self.parent_ui.log(f"✅ [{account}] 存稿成功 - 视频: {video_name}")
            else:
                self.parent_ui.log(f"❌ [{account}] 存稿失败 - {status}")
        except Exception as e:
            self.parent_ui.log(f"❌ 记录存稿详情日志失败: {e}")
    
    def save_draft_details(self):
        """保存存稿详情数据 - 按平台分离存储"""
        try:
            # 保存存稿详情数据到文件
            import json
            import os

            # 获取当前平台
            current_platform = self.parent_ui.current_platform

            # 根据平台确定存储路径
            if current_platform == "netease":
                # 网易平台：保存到项目根目录
                root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
                draft_details_path = os.path.join(root_dir, "netease_draft_details.json")
            elif current_platform == "toutiao":
                # 头条平台：保存到项目根目录
                root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
                draft_details_path = os.path.join(root_dir, "toutiao_draft_details.json")
            elif current_platform == "dayu":
                # 大鱼平台：保存到项目根目录
                root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
                draft_details_path = os.path.join(root_dir, "dayu_draft_details.json")
            else:
                # 默认保存到通用文件（兼容旧版本）
                root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
                draft_details_path = os.path.join(root_dir, "draft_details.json")

            with open(draft_details_path, 'w', encoding='utf-8') as f:
                json.dump(self.parent_ui.account_details, f, ensure_ascii=False, indent=2)
            # 不记录每次保存的日志，减少日志输出

        except Exception as e:
            self.parent_ui.log(f"保存存稿详情数据失败: {str(e)}")
    
    def load_draft_details(self):
        """加载存稿详情数据 - 按平台分离加载"""
        try:
            import json
            import os

            # 获取当前平台
            current_platform = self.parent_ui.current_platform
            root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

            # 根据平台确定加载路径
            platform_draft_details_path = None
            legacy_draft_details_path = os.path.join(root_dir, "draft_details.json")  # 旧版本通用文件

            if current_platform == "netease":
                platform_draft_details_path = os.path.join(root_dir, "netease_draft_details.json")
            elif current_platform == "toutiao":
                platform_draft_details_path = os.path.join(root_dir, "toutiao_draft_details.json")
            elif current_platform == "dayu":
                platform_draft_details_path = os.path.join(root_dir, "dayu_draft_details.json")

            # 优先加载平台特定的文件
            if platform_draft_details_path and os.path.exists(platform_draft_details_path):
                with open(platform_draft_details_path, 'r', encoding='utf-8') as f:
                    loaded_details = json.load(f)

                # 过滤掉测试账号
                self.parent_ui.account_details = {}
                for account, details in loaded_details.items():
                    # 检查是否为测试账号（这里可以根据实际情况调整判断条件）
                    if account.lower() != "测试账号" and not account.lower().startswith("test"):
                        self.parent_ui.account_details[account] = details

                self.parent_ui.log(f"已从{current_platform}平台加载存稿详情数据，共 {len(self.parent_ui.account_details)} 个账号")

            # 如果平台特定文件不存在，尝试从旧版本通用文件迁移数据
            elif os.path.exists(legacy_draft_details_path):
                with open(legacy_draft_details_path, 'r', encoding='utf-8') as f:
                    loaded_details = json.load(f)

                # 过滤掉测试账号，并且只保留当前平台的账号
                self.parent_ui.account_details = {}
                current_accounts = set()

                # 获取当前平台的账号列表
                try:
                    current_accounts = set(account.account_name for account in self.account_manager.load_accounts())
                except:
                    pass

                for account, details in loaded_details.items():
                    # 检查是否为测试账号和是否为当前平台账号
                    if (account.lower() != "测试账号" and
                        not account.lower().startswith("test") and
                        (not current_accounts or account in current_accounts)):
                        self.parent_ui.account_details[account] = details

                self.parent_ui.log(f"已从通用文件迁移{current_platform}平台存稿详情数据，共 {len(self.parent_ui.account_details)} 个账号")

                # 将数据保存到平台特定文件
                if platform_draft_details_path:
                    with open(platform_draft_details_path, 'w', encoding='utf-8') as f:
                        json.dump(self.parent_ui.account_details, f, ensure_ascii=False, indent=2)
                    self.parent_ui.log(f"已将存稿详情数据迁移到{current_platform}平台文件: {platform_draft_details_path}")
            else:
                self.parent_ui.log(f"未找到{current_platform}平台存稿详情数据文件，将创建新的数据结构")
                self.parent_ui.account_details = {}

        except Exception as e:
            self.parent_ui.log(f"加载存稿详情数据失败: {str(e)}")
            self.parent_ui.account_details = {}

    def start_all_accounts_task(self):
        """开始所有账号的存稿任务"""
        if self.parent_ui.is_running:
            self.parent_ui.log("⚠️ 任务已在运行中，无法启动新任务")
            return False

        # 检查账号列表
        if not self.parent_ui.accounts:
            self.parent_ui.log("❌ 账号列表为空，请先添加账号")
            return False

        self.parent_ui.log(f"🚀 开始为所有 {len(self.parent_ui.accounts)} 个账号执行存稿任务")

        # 确保目录存在
        self.parent_ui.create_required_dirs()

        # 更新UI状态 - 修复状态同步问题
        self.is_running = True  # 关键修复：设置任务管理器的运行状态
        self.parent_ui.is_running = True
        self.parent_ui.start_button["state"] = "disabled"
        self.parent_ui.stop_button["state"] = "normal"
        self.parent_ui.status_text.config(text="运行中...")

        # 获取并发账号数量
        concurrent_num = self.parent_ui.concurrent_accounts.get()

        # 根据并发数量选择处理模式
        if concurrent_num <= 1:
            self.parent_ui.log("使用单线程模式处理所有账号")
            import threading
            threading.Thread(target=self._single_task_thread, args=("all",), daemon=True).start()
        else:
            self.parent_ui.log(f"使用多线程模式处理 {len(self.parent_ui.accounts)} 个账号，并发数: {concurrent_num}")
            import threading
            threading.Thread(target=self._concurrent_task_thread, args=(self.parent_ui.accounts.copy(),), daemon=True).start()

        return True

    def _create_required_dirs(self):
        """创建必要的目录"""
        # 获取所有需要的目录 - 与原始版本保持一致
        dirs = [
            self.config_manager.get("account_dir", "", platform=self.parent_ui.current_platform),
            self.config_manager.get("video_dir", "", platform=self.parent_ui.current_platform),
            self.config_manager.get("cover_dir", "", platform=self.parent_ui.current_platform),
            self.config_manager.get("processed_dir", "", platform=self.parent_ui.current_platform),
            self.config_manager.get("processed_covers_dir", "", platform=self.parent_ui.current_platform),
            self.config_manager.get("violation_dir", "", platform=self.parent_ui.current_platform),
            self.config_manager.get("screenshots_dir", "", platform=self.parent_ui.current_platform)
        ]

        # 创建目录
        for dir_path in dirs:
            if dir_path and not os.path.exists(dir_path):
                try:
                    os.makedirs(dir_path, exist_ok=True)
                    self.parent_ui.log(f"已创建目录: {dir_path}")
                except Exception as e:
                    self.parent_ui.log(f"创建目录失败: {dir_path}, 错误: {str(e)}")

        self.parent_ui.log("已确保所有必要目录存在")

    def _single_task_thread(self, account):
        """
        单个账号任务线程

        Args:
            account: 账号名称
        """
        try:
            self.parent_ui.log(f"🔍 [DEBUG] _single_task_thread 开始执行，参数: {account}")
            # 检查是否是全部账号模式
            if not account or account == "all":
                # 全部账号模式 - 使用主UI已经加载好的账号列表
                accounts = self.parent_ui.accounts
                if not accounts:
                    self.parent_ui.log("❌ 没有找到账号")
                    return

                self.parent_ui.log(f"📋 开始处理全部 {len(accounts)} 个账号")

                # 处理每个账号
                success_count = 0
                failed_count = 0

                for i, account_name in enumerate(accounts):
                    if not self.is_running:
                        self.parent_ui.log("⏹️ 任务被用户停止")
                        break

                    self.parent_ui.log(f"📝 处理账号 {i+1}/{len(accounts)}: {account_name}")
                    self.parent_ui.selected_account.set(account_name)

                    # 处理单个账号
                    success = self._process_single_account(account_name)
                    if success:
                        success_count += 1
                    else:
                        failed_count += 1

                # 恢复未选择状态
                if self.is_running:
                    self.parent_ui.selected_account.set("")
                    self.parent_ui.log(f"🎉 全部账号存稿任务完成: 成功 {success_count} 个，失败 {failed_count} 个")
            else:
                # 单个账号模式
                self.parent_ui.log(f"📋 开始处理单个账号: {account}")
                success = self._process_single_account(account)

                # 更新单账号任务进度为完成
                self.parent_ui.root.after(0, lambda: self.parent_ui.update_task_progress(1, 1))

                if success:
                    self.parent_ui.log(f"✅ 账号 {account} 处理成功")
                else:
                    self.parent_ui.log(f"❌ 账号 {account} 处理失败")

        except Exception as e:
            error_msg = f"单个任务线程发生错误: {str(e)}"
            self.parent_ui.log(f"❌ {error_msg}")
        finally:
            self._task_completed()

    def _process_single_account(self, account):
        """处理单个账号 - 使用统一的draft_processor"""
        try:
            # 更新UI状态（移除重复的日志，因为上层已经输出了）
            self.parent_ui.update_current_account_status(account, "processing")

            # 初始化账号进度数据
            if account not in self.parent_ui.account_progress:
                self.parent_ui.account_progress[account] = {
                    "progress": 0,
                    "status": "准备中",
                    "last_update": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "details": "等待处理"
                }

            # 更新进度条
            self.parent_ui.update_account_progress(account, 0, "准备中...")

            # 检查存稿处理器是否存在
            if not hasattr(self.parent_ui, 'draft_processor') or self.parent_ui.draft_processor is None:
                self.parent_ui.log("❌ 存稿处理器未初始化，正在重新初始化...")
                self.parent_ui.init_platform_processors()

                # 再次检查
                if not hasattr(self.parent_ui, 'draft_processor') or self.parent_ui.draft_processor is None:
                    self.parent_ui.log("❌ 存稿处理器初始化失败")
                    self.parent_ui.update_account_progress(account, 0, "初始化失败")
                    return False

            # 获取配置参数
            account_dir = self.parent_ui.account_dir.get()
            processed_dir = self.parent_ui.processed_dir.get()
            processed_covers_dir = self.parent_ui.processed_covers_dir.get()
            archive_completed = self.parent_ui.archive_completed.get()
            headless_mode = self.parent_ui.headless_mode.get()
            draft_limit = self.parent_ui.draft_limit.get()
            loop_limit = self.parent_ui.loop_limit.get()

            # 更新存稿处理器配置
            self.parent_ui.draft_processor.account_dir = account_dir
            self.parent_ui.draft_processor.processed_dir = processed_dir
            self.parent_ui.draft_processor.processed_covers_dir = processed_covers_dir
            self.parent_ui.draft_processor.archive_completed = archive_completed
            self.parent_ui.draft_processor.headless_mode = headless_mode
            self.parent_ui.draft_processor.draft_limit = draft_limit
            self.parent_ui.draft_processor.loop_limit = loop_limit
            self.parent_ui.draft_processor.screenshots_dir = self.parent_ui.screenshots_dir.get()
            self.parent_ui.draft_processor.random_video_allocation = self.parent_ui.random_video_allocation.get()

            # 记录视频分配方式
            if self.parent_ui.random_video_allocation.get():
                self.parent_ui.log("视频分配方式: 随机分配")
            else:
                self.parent_ui.log("视频分配方式: 顺序分配")

            # 启动存稿处理器
            success, processed_videos = self.parent_ui.draft_processor.start(account=account)

            # 更新UI状态
            if success:
                self.parent_ui.log(f"账号 {account} 处理完成")
                self.parent_ui.update_account_progress(account, 100, "已完成")

                # 记录存稿详情
                for video_info in processed_videos:
                    video_path = video_info.get("video_path", "")
                    status = video_info.get("status", "")
                    reason = video_info.get("reason", "")
                    screenshot = video_info.get("screenshot", "")
                    self.parent_ui.add_draft_detail(account, video_path, status, reason, screenshot, video_info)
            else:
                self.parent_ui.log(f"账号 {account} 处理失败")
                self.parent_ui.update_account_progress(account, 0, "失败")

                # 记录失败详情
                if processed_videos:
                    for video_info in processed_videos:
                        video_path = video_info.get("video_path", "")
                        status = video_info.get("status", "失败")
                        reason = video_info.get("reason", "未知错误")
                        self.parent_ui.add_draft_detail(account, video_path, status, reason, "", video_info)
                else:
                    # 如果没有处理任何视频，记录一个通用的失败记录
                    self.parent_ui.add_draft_detail(account, "", "失败", "存稿过程启动失败", "", None)

            return success

        except Exception as e:
            self.parent_ui.log(f"处理账号 {account} 时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            self.parent_ui.update_account_progress(account, 0, "错误")
            return False

    def _task_completed(self):
        """任务完成后的清理工作"""
        self.is_running = False
        # 同步主UI的运行状态
        self.parent_ui.is_running = False

        # 更新UI状态
        if hasattr(self.parent_ui, 'start_button'):
            self.parent_ui.start_button["state"] = "normal"
        if hasattr(self.parent_ui, 'stop_button'):
            self.parent_ui.stop_button["state"] = "disabled"
        if hasattr(self.parent_ui, 'status_text'):
            self.parent_ui.status_text.config(text="就绪")

        # 重置侧边栏运行状态为空闲
        if hasattr(self.parent_ui, 'update_running_status'):
            self.parent_ui.update_running_status("🟢 空闲", "#28A745")

        self.parent_ui.log("✅ 存稿任务已完成")

    def cleanup_draft_processors(self):
        """清理存稿处理器资源"""
        try:
            # 清理网易号存稿处理器
            if hasattr(self.parent_ui, 'netease_processor') and self.parent_ui.netease_processor:
                if hasattr(self.parent_ui.netease_processor, 'driver_manager') and self.parent_ui.netease_processor.driver_manager:
                    self.parent_ui.log("正在关闭网易号存稿处理器的浏览器...")
                    self.parent_ui.netease_processor.driver_manager.close_driver()

                # 重置处理器状态
                if hasattr(self.parent_ui.netease_processor, 'is_running'):
                    self.parent_ui.netease_processor.is_running = False

            # 清理头条存稿处理器
            if hasattr(self.parent_ui, 'toutiao_processor') and self.parent_ui.toutiao_processor:
                if hasattr(self.parent_ui.toutiao_processor, 'driver_manager') and self.parent_ui.toutiao_processor.driver_manager:
                    self.parent_ui.log("正在关闭头条存稿处理器的浏览器...")
                    self.parent_ui.toutiao_processor.driver_manager.close_driver()

                # 重置处理器状态
                if hasattr(self.parent_ui.toutiao_processor, 'is_running'):
                    self.parent_ui.toutiao_processor.is_running = False

            # 清理存稿处理器（通用）
            if hasattr(self.parent_ui, 'draft_processor') and self.parent_ui.draft_processor:
                if hasattr(self.parent_ui.draft_processor, 'driver_manager') and self.parent_ui.draft_processor.driver_manager:
                    self.parent_ui.log("正在关闭存稿处理器的浏览器...")
                    self.parent_ui.draft_processor.driver_manager.close_driver()

                # 重置处理器状态
                if hasattr(self.parent_ui.draft_processor, 'is_running'):
                    self.parent_ui.draft_processor.is_running = False

            self.parent_ui.log("存稿处理器资源清理完成")

        except Exception as e:
            self.parent_ui.log(f"清理存稿处理器资源时出错: {e}")

    def cleanup(self):
        """清理存稿任务管理器资源"""
        try:
            # 停止任务
            self.stop_task()
            
            # 等待任务线程结束
            if self.task_thread and self.task_thread.is_alive():
                self.task_thread.join(timeout=2)
            
            # 保存存稿详情
            self.save_draft_details()
            
        except Exception as e:
            self.parent_ui.log(f"❌ 清理存稿任务管理器失败: {e}")
