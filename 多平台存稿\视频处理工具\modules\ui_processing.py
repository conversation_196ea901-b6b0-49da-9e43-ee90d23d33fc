"""
UI处理模块 - 包含视频处理相关代码
"""

import os
import time
import threading
import tkinter as tk
from tkinter import ttk, messagebox
import multiprocessing
from queue import Queue
import subprocess

# 导入系统工具模块
from .common.system import ensure_ffmpeg_available, ensure_tesseract_available, ensure_moviepy_available


def start_preprocess(self):
    """开始预处理视频"""
    if self.is_processing:
        self.log("⚠️ 预处理任务正在运行中")
        return

    try:
        # 检查必要的目录设置
        if not self.video_dir.get():
            messagebox.showerror("错误", "请先设置待处理视频目录！")
            return
        if not self.processed_videos_dir.get():
            messagebox.showerror("错误", "请先设置处理后视频目录！")
            return
        if not self.processed_covers_dir.get():
            messagebox.showerror("错误", "请先设置处理后封面目录！")
            return

        # 获取目录信息（不再显示确认对话框）
        video_dir = self.video_dir.get()
        processed_videos_dir = self.processed_videos_dir.get()
        processed_covers_dir = self.processed_covers_dir.get()

        # 计算视频文件数量
        video_extensions = ('.mp4', '.avi', '.mkv', '.mov', '.flv', '.wmv', '.webm')
        try:
            found_files = [f for f in os.listdir(video_dir) if any(f.lower().endswith(ext) for ext in video_extensions)]
            file_count = len(found_files)
        except:
            file_count = "未知"

        # 记录操作日志
        self.log(f"🚀 开始处理视频任务")
        self.log(f"📂 视频源目录: {video_dir}")
        self.log(f"📊 视频文件数量: {file_count}")
        self.log(f"📂 处理后视频目录: {processed_videos_dir}")
        self.log(f"📂 处理后封面目录: {processed_covers_dir}")

        # 设置处理状态为True，防止重复启动
        self.is_processing = True

        # 启动预处理线程
        thread = threading.Thread(target=self.preprocess_task)
        thread.daemon = True
        thread.start()
    except Exception as e:
        self.log(f"❌ 启动预处理失败: {str(e)}")
        self.is_processing = False


def stop_processing(self):
    """停止处理任务"""
    if not self.is_processing:
        self.log("⚠️ 没有正在运行的处理任务")
        return

    # 直接停止处理，不显示确认对话框
    self.is_processing = False
    self.log("⏹️ 正在停止处理任务...")


# 这些函数已被移动到模块化组件中，这里保留空函数以保持向后兼容
def reset_progress_ui(self):
    """重置进度条UI - 使用新的模块化组件"""
    # 避免递归调用，直接使用模块化组件中的函数
    from .ui.components.progress_panel import reset_progress
    reset_progress(self)


def update_progress_ui(self, success, fail, total, percent):
    """更新进度条UI - 使用新的模块化组件"""
    # 避免递归调用，直接使用模块化组件中的函数
    from .ui.components.progress_panel import update_progress
    # 传递参数给进度面板组件
    update_progress(self, success=success, fail=fail, total=total, percent=percent)


def preprocess_task(self):
    """预处理视频任务"""
    try:
        # is_processing 已经在 start_preprocess 中设置为 True
        # 重置计数器
        self.success_count = 0
        self.failed_count = 0

        # 重置进度UI - 使用新的模块化组件
        self.reset_progress_ui()

        self.log("\n=== 开始预处理视频 ===")

        # 获取视频目录
        video_dir = self.video_dir.get()
        if not os.path.exists(video_dir):
            self.log(f"❌ 视频目录不存在: {video_dir}")
            self.is_processing = False
            return

        # 检查ffmpeg是否可用
        ffmpeg_available = ensure_ffmpeg_available()
        if ffmpeg_available:
            self.log("✅ 检测到ffmpeg，将使用ffmpeg进行高速处理")
        else:
            self.log("⚠️ 未检测到ffmpeg，将使用moviepy处理（速度较慢）")

        # 检查Tesseract是否可用
        tesseract_available = ensure_tesseract_available()
        if tesseract_available:
            self.log("✅ 检测到Tesseract OCR，可以进行文字识别")
        else:
            self.log("⚠️ 未检测到Tesseract OCR，文字识别功能可能不可用")

        # 检查MoviePy是否可用
        moviepy_available = ensure_moviepy_available()
        if not moviepy_available:
            self.log("❌ MoviePy库不可用，视频处理功能将无法正常工作！")
            messagebox.showerror("错误", "MoviePy库不可用，视频处理功能将无法正常工作！\n请安装MoviePy库后再试。")
            self.is_processing = False
            return

        # 如果启用了水印但FFmpeg不可用，显示警告
        if self.enable_watermark.get() and not ffmpeg_available:
            self.log("⚠️ 警告: 启用了水印功能，但未检测到FFmpeg。水印功能需要FFmpeg支持！")
            messagebox.showwarning("水印功能警告", "您启用了水印功能，但系统未检测到FFmpeg。\n\n水印功能需要FFmpeg支持，可能无法正常工作！")

        # 检查视频目录内容
        self.log(f"📂 检查视频目录: {video_dir}")
        video_extensions = ('.mp4', '.avi', '.mkv', '.mov', '.flv', '.wmv', '.webm')
        found_files = [f for f in os.listdir(video_dir) if any(f.lower().endswith(ext) for ext in video_extensions)]

        if not found_files:
            self.log("❌ 视频目录中没有找到视频文件")
            self.is_processing = False
            return

        self.log(f"📊 目录中找到 {len(found_files)} 个视频文件")
        # 只显示前10个文件的名称
        for file in found_files[:10]:
            self.log(f"   - {file}")
        if len(found_files) > 10:
            self.log(f"   ... 等 {len(found_files)-10} 个文件")

        # 获取处理后视频目录
        processed_videos_dir = self.processed_videos_dir.get()
        if not os.path.exists(processed_videos_dir):
            os.makedirs(processed_videos_dir)
            self.log(f"✅ 创建了处理后视频目录: {processed_videos_dir}")

        # 获取处理后封面目录
        processed_covers_dir = self.processed_covers_dir.get()
        if not os.path.exists(processed_covers_dir):
            os.makedirs(processed_covers_dir)
            self.log(f"✅ 创建了处理后封面目录: {processed_covers_dir}")

        # 显示水印设置信息
        if self.enable_watermark.get():
            watermark_color = self.color_names.get(self.watermark_color.get(), "默认颜色")
            self.log(f"🖼️ 封面水印已启用: {watermark_color}, 透明度:{self.watermark_opacity.get():.2f}, 位置:{self.watermark_position.get()}")

        # 显示新增功能设置信息
        if self.enable_format_conversion.get():
            self.log(f"🎬 视频格式转换已启用: 输出格式为 {self.output_format.get()}")

        if self.enable_custom_cover_resolution.get():
            self.log(f"📐 自定义封面分辨率已启用: {self.cover_width.get()}x{self.cover_height.get()}")

        # 显示文件名过滤设置信息
        if hasattr(self, 'enable_filename_filter') and self.enable_filename_filter.get():
            filter_count = 0
            if hasattr(self, 'video_processor') and hasattr(self.video_processor, 'filename_filters'):
                filter_count = len(self.video_processor.filename_filters)
            self.log(f"🔍 文件名过滤已启用: 共有 {filter_count} 个过滤关键词")

        # 清空目标文件夹
        self.log("🗑️ 清空处理后视频目录和封面目录...")

        # 使用单线程清空文件夹以避免序列化问题
        for folder in [processed_videos_dir, processed_covers_dir]:
            if os.path.exists(folder):
                for file in os.listdir(folder):
                    file_path = os.path.join(folder, file)
                    try:
                        if os.path.isfile(file_path):
                            os.remove(file_path)
                    except Exception as e:
                        self.log(f"⚠️ 删除文件失败: {file} - {str(e)}")

        # 使用找到的文件列表
        video_files = found_files

        # 获取视频处理参数
        min_duration = float(self.video_min_duration.get())  # 秒
        max_duration = float(self.video_max_duration.get())  # 秒

        # 创建进度条
        total_videos = len(video_files)

        # 更新UI上的总数 - 使用新的模块化组件
        # 传递正确的总数参数
        self.update_progress_ui(success=0, fail=0, total=total_videos, percent=0)

        # 获取并发处理线程数，根据CPU核心数和内存优化
        cpu_count = multiprocessing.cpu_count()
        user_threads = int(self.thread_num.get())

        # 检查是否启用了智能资源分配
        if self.enable_smart_resource.get():
            self.log("🧠 智能资源分配已启用")

            try:
                # 尝试导入psutil - 如果安装了此库，可以获得更智能的线程分配
                import psutil

                try:
                    # 获取系统信息
                    mem = psutil.virtual_memory()
                    total_mem_gb = mem.total / (1024**3)  # 转换为GB
                    available_mem_gb = mem.available / (1024**3)  # 可用内存

                    # 获取内存限制
                    memory_limit_mb = self.memory_limit.get()
                    if memory_limit_mb > 0:
                        # 用户设置了内存限制
                        memory_limit_gb = memory_limit_mb / 1024
                        self.log(f"📊 内存限制: {memory_limit_gb:.1f}GB (用户设置)")

                        # 检查内存限制是否合理
                        if memory_limit_gb > total_mem_gb * 0.9:
                            self.log(f"⚠️ 内存限制 ({memory_limit_gb:.1f}GB) 接近系统总内存 ({total_mem_gb:.1f}GB)，可能导致系统不稳定")
                            # 调整为更合理的值
                            memory_limit_gb = total_mem_gb * 0.7
                            self.log(f"⚠️ 已自动调整内存限制为 {memory_limit_gb:.1f}GB")
                    else:
                        # 自动设置内存限制
                        memory_limit_gb = min(available_mem_gb * 0.8, total_mem_gb * 0.5)
                        self.log(f"📊 内存限制: {memory_limit_gb:.1f}GB (自动设置)")

                    # 根据内存限制和每个线程预估内存使用量计算最大线程数
                    # 假设每个视频处理线程使用约500MB内存
                    thread_mem_gb = 0.5  # 每个线程0.5GB
                    mem_based_threads = max(1, int(memory_limit_gb / thread_mem_gb))

                    # 根据系统性能决定线程数
                    if total_mem_gb < 4:  # 低内存系统
                        suggested_workers = max(1, min(cpu_count // 2, mem_based_threads))
                        self.log("💡 检测到低内存系统，减少线程数以避免内存不足")
                    elif total_mem_gb < 8:  # 中等内存系统
                        suggested_workers = max(2, min(cpu_count - 2, mem_based_threads))
                    else:  # 高内存系统
                        suggested_workers = min(cpu_count, mem_based_threads)

                    # 获取用户设置的线程数，但不超过建议值
                    max_workers = min(total_videos, min(user_threads, suggested_workers))

                    self.log(f"🧮 系统信息: CPU {cpu_count}核, 总内存 {total_mem_gb:.1f}GB, 可用内存 {available_mem_gb:.1f}GB")
                    self.log(f"🧵 使用 {max_workers} 个线程处理视频 (推荐值: {suggested_workers}, 用户设置: {user_threads})")

                    # 检查GPU加速
                    if self.enable_gpu_acceleration.get():
                        gpu_device = self.gpu_device.get()
                        self.log(f"🎮 GPU加速已启用，使用设备: {gpu_device}")

                        # 如果使用GPU，可以适当增加线程数，因为GPU会分担一部分计算负担
                        if gpu_device != "auto" and max_workers < cpu_count:
                            max_workers = min(max_workers + 2, cpu_count, total_videos)
                            self.log(f"🧵 由于启用GPU加速，线程数已调整为: {max_workers}")
                except Exception as e:
                    # 捕获psutil使用时可能出现的任何错误
                    self.log(f"⚠️ 使用psutil获取系统信息时出错: {str(e)}")
                    # 使用备选方案
                    max_workers = min(total_videos, min(user_threads, cpu_count))
                    self.log(f"🧵 使用备选方案: {max_workers} 个线程处理视频 (用户设置: {user_threads}, CPU: {cpu_count}核)")
            except ImportError:
                # 如果无法导入psutil，使用简单的线程数确定方法
                max_workers = min(total_videos, min(user_threads, cpu_count))
                self.log(f"🧵 使用 {max_workers} 个线程处理视频 (CPU: {cpu_count}核)")
            except Exception as e:
                # 捕获任何其他异常
                self.log(f"⚠️ 确定线程数时出错: {str(e)}")
                # 使用保守的设置
                max_workers = min(total_videos, min(2, cpu_count))
                self.log(f"🧵 使用保守设置: {max_workers} 个线程处理视频")
        else:
            # 如果未启用智能资源分配，直接使用用户设置的线程数
            max_workers = min(total_videos, user_threads)
            self.log(f"🧵 使用用户设置的线程数: {max_workers} (智能资源分配已禁用)")

        # 创建全局锁，用于同步处理结果
        self.processing_lock = threading.Lock()

        # 创建已处理文件记录，避免重复处理
        processed_files = set()
        processed_count_lock = threading.Lock()

        # 进度更新函数 - 添加节流控制，避免过度更新
        last_update_time = [0]  # 使用列表作为可变对象，存储上次更新时间

        def update_progress(force=False):
            current_time = time.time()
            # 如果距离上次更新不足1秒且不是强制更新，则跳过
            if not force and current_time - last_update_time[0] < 1.0:
                return

            with self.processing_lock:
                # 计算已处理的视频数量（成功+失败）
                processed_count = self.success_count + self.failed_count
                # 计算进度百分比
                progress = int((processed_count / total_videos) * 100) if total_videos > 0 else 0
                message = f"🔄 预处理进度: {processed_count}/{total_videos} ({progress}%) | 成功: {self.success_count}, 失败: {self.failed_count}"
                self.log(message)
                # 更新UI上的进度 - 使用新的模块化组件
                # 传递总视频数和已处理数的正确值
                self.update_progress_ui(success=self.success_count, fail=self.failed_count,
                                      total=total_videos, percent=progress)

                # 更新上次更新时间
                last_update_time[0] = current_time

        # 创建任务队列，有助于更均匀地分配任务
        task_queue = Queue()
        for video_file in video_files:
            task_queue.put(video_file)

        # 准备封面设置
        cover_settings = {
            'top_text': self.cover_text_top.get(),
            'bottom_text': self.cover_text_bottom.get(),
            'top_color': self.cover_color_top.get(),
            'bottom_color': self.cover_color_bottom.get(),
            'font_size': self.cover_size.get(),
            'auto_use_filename': self.auto_use_filename.get()
        }

        # 准备水印设置
        watermark_settings = {
            'enable': self.enable_watermark.get(),
            'color': self.watermark_color.get(),
            'opacity': self.watermark_opacity.get(),
            'position': self.watermark_position.get()
        }

        # 准备封面分辨率
        cover_resolution = None
        if self.enable_custom_cover_resolution.get():
            cover_resolution = (self.cover_width.get(), self.cover_height.get())

        # 准备输出格式和编码器设置
        output_format = None
        video_codec = None
        if self.enable_format_conversion.get():
            output_format = self.output_format.get()
            # 获取编码器设置
            if hasattr(self, 'video_codec'):
                video_codec = self.video_codec.get()

        # 准备GPU加速设置
        gpu_settings = None
        if self.enable_gpu_acceleration.get():
            gpu_settings = {
                'enable': True,
                'device': self.gpu_device.get()
            }

        # 准备智能资源分配设置
        resource_settings = None
        if self.enable_smart_resource.get():
            resource_settings = {
                'enable': True,
                'memory_limit': self.memory_limit.get()
            }

        # 处理单个文件的函数
        def process_file_worker():
            while not task_queue.empty() and self.is_processing:
                try:
                    # 从队列获取任务
                    video_file = task_queue.get(block=False)
                    if not video_file:
                        continue

                    # 检查是否已处理
                    with processed_count_lock:
                        if video_file in processed_files:
                            task_queue.task_done()
                            continue
                        processed_files.add(video_file)

                    # 调用处理回调
                    if self.process_callback:
                        # 准备去重设置
                        dedup_settings = None
                        if self.enable_deduplication.get():
                            dedup_settings = {
                                'enable': True,
                                'method': self.dedup_method.get() if hasattr(self, 'dedup_method') else 'content',
                                'sample_frames': self.sample_frames.get() if hasattr(self, 'sample_frames') else 10
                            }

                        # 检查文件名过滤设置
                        if hasattr(self, 'enable_filename_filter') and self.enable_filename_filter.get():
                            # 如果启用了文件名过滤，先检查文件名是否应该被过滤
                            if hasattr(self, 'video_processor') and hasattr(self.video_processor, 'should_filter_file'):
                                should_filter, filter_keyword = self.video_processor.should_filter_file(video_file)
                                if should_filter:
                                    # 文件名包含过滤关键词，跳过处理
                                    with self.processing_lock:
                                        self.failed_count += 1
                                    self.log(f"⏭️ 跳过视频 {video_file}: 包含过滤关键词 '{filter_keyword}'")
                                    task_queue.task_done()
                                    continue

                        result = self.process_callback(
                            video_file=video_file,
                            video_dir=video_dir,
                            processed_videos_dir=processed_videos_dir,
                            processed_covers_dir=processed_covers_dir,
                            min_duration=min_duration,
                            max_duration=max_duration,
                            target_ratio=self.video_ratio.get(),
                            enable_deduplication=self.enable_deduplication.get(),
                            dedup_settings=dedup_settings,  # 添加去重设置
                            cover_settings=cover_settings,
                            watermark_settings=watermark_settings,
                            output_format=output_format,
                            video_codec=video_codec,  # 添加编码器参数
                            cover_resolution=cover_resolution,
                            gpu_settings=gpu_settings,
                            resource_settings=resource_settings
                        )
                    else:
                        # 如果没有提供处理回调，返回错误
                        result = {
                            'success': False,
                            'message': "未提供视频处理回调函数"
                        }

                    # 更新统计信息
                    with self.processing_lock:
                        # 不再使用 self.total_processed，而是根据成功和失败计数来计算总处理数
                        if result.get('success'):
                            self.success_count += 1
                            # 仅在控制台显示成功消息，不在GUI中显示每个文件
                            print(result.get('message', ''))
                        else:
                            self.failed_count += 1
                            # 显示失败消息在GUI中
                            self.log(f"❌ {result.get('message', '处理失败')}")

                    # 使用节流控制的进度更新函数
                    # 在处理完所有文件时强制更新，否则由节流控制决定是否更新
                    processed_count = self.success_count + self.failed_count
                    force_update = (processed_count == total_videos)
                    update_progress(force=force_update)

                    # 标记任务完成
                    task_queue.task_done()
                except Exception as e:
                    print(f"处理任务异常: {str(e)}")

        # 创建并启动工作线程
        workers = []
        for _ in range(max_workers):
            thread = threading.Thread(target=process_file_worker)
            thread.daemon = True
            thread.start()
            workers.append(thread)

        # 等待任务完成
        try:
            # 主线程定期更新进度并检查是否应该停止
            while self.is_processing and any(thread.is_alive() for thread in workers) and not task_queue.empty():
                # 使用节流控制的进度更新函数，不强制更新
                update_progress(force=False)
                # 等待一小段时间
                time.sleep(1)

            # 如果处理被停止，确保任务队列被清空
            if not self.is_processing:
                with task_queue.mutex:
                    task_queue.queue.clear()
                self.log("⏹️ 预处理已手动停止")
            else:
                # 等待所有工作线程完成
                for thread in workers:
                    thread.join(timeout=1.0)

                # 最终更新进度 - 强制更新
                update_progress(force=True)
                self.log(f"\n✅ 视频预处理完成!")
                self.log(f"📊 总计: {total_videos}, 成功: {self.success_count}, 失败: {self.failed_count}")
        except Exception as e:
            self.log(f"监控任务进度时发生错误: {str(e)}")

    except Exception as e:
        self.log(f"❌ 预处理任务失败: {str(e)}")
        import traceback
        self.log(traceback.format_exc())

    finally:
        self.is_processing = False
        # 尝试释放资源
        import gc
        gc.collect()
