"""
工具函数模块 - 包含各种通用工具函数
"""

import os
import re
import time
import random
import tkinter as tk
import jieba
import subprocess
import sys
from typing import List, Dict, Any, Tuple, Optional, Callable

def center_window(window: tk.Tk) -> None:
    """
    使窗口在屏幕中居中显示

    Args:
        window: Tkinter窗口对象
    """
    window.update_idletasks()  # 更新窗口大小信息
    width = window.winfo_width()  # 获取窗口宽度
    height = window.winfo_height()  # 获取窗口高度
    screen_width = window.winfo_screenwidth()  # 获取屏幕宽度
    screen_height = window.winfo_screenheight()  # 获取屏幕高度

    # 计算窗口居中的位置坐标
    x = (screen_width - width) // 2
    y = (screen_height - height) // 2

    # 设置窗口位置
    window.geometry(f"+{x}+{y}")

def validate_number_input(value: str) -> bool:
    """
    验证输入是否为数字

    Args:
        value: 输入值

    Returns:
        是否为有效数字
    """
    if value == "":
        return True
    try:
        int(value)
        return True
    except ValueError:
        return False

def truncate_filename(filename: str, max_length: int = 30) -> str:
    """
    截取文件名到指定长度

    Args:
        filename: 文件名
        max_length: 最大长度

    Returns:
        截取后的文件名
    """
    name, ext = os.path.splitext(filename)
    if len(name) > max_length:
        name = name[:max_length]
    return name + ext

def generate_tags_from_title(title: str) -> List[str]:
    """
    从视频标题生成标签

    Args:
        title: 视频标题

    Returns:
        标签列表
    """
    # 如果标题为空，返回空列表
    if not title:
        return []

    # 使用jieba分词
    words = jieba.cut(title)

    # 过滤掉停用词和短词
    filtered_words = [word for word in words if len(word) > 1 and not word.isdigit()]

    # 初始化标签列表
    tags = []

    # 1. 首先添加完整标题作为第一个标签（如果不太长）
    if len(title) <= 20:
        tags.append(title)

    # 2. 添加分词结果作为标签
    for word in filtered_words:
        if word not in tags and len(tags) < 5:
            tags.append(word)

    # 3. 如果标题中有特殊符号分隔的部分，将它们作为标签
    parts = re.split(r'[,，.。:：;；!！?？\-_|]', title)
    for part in parts:
        part = part.strip()
        if part and part not in tags and len(part) > 1 and len(tags) < 5:
            tags.append(part)

    # 4. 尝试提取两个词的组合作为标签
    if len(filtered_words) >= 2:
        for i in range(len(filtered_words) - 1):
            two_word = filtered_words[i] + filtered_words[i+1]
            if two_word not in tags and len(tags) < 5:
                tags.append(two_word)

    # 5. 如果标题中有引号包围的内容，将其作为标签
    quoted = re.findall(r'["\'""'']([^"\'""'']+)["\'""'']', title)
    for q in quoted:
        if q not in tags and len(tags) < 5:
            tags.append(q)

    # 6. 如果标题中有括号包围的内容，将其作为标签
    bracketed = re.findall(r'[（(]([^）)]+)[）)]', title)
    for b in bracketed:
        if b not in tags and len(tags) < 5:
            tags.append(b)

    # 7. 标题中的数字可能是重要信息（如日期、时间、数量），将它们添加为标签
    number_patterns = re.findall(r'\d+', title)
    for num in number_patterns:
        # 查找数字周围的上下文
        index = title.find(num)
        start = max(0, index - 2)
        end = min(len(title), index + len(num) + 2)
        context = title[start:end]
        if context not in tags and len(tags) < 5:
            tags.append(context)

    # 确保至少有3个标签
    if len(tags) < 3:
        # 如果实在不够，重复一些标签或者使用标题的不同部分
        for i in range(min(3, len(title) - 1)):
            subset = title[i:i+min(5, len(title)-i)]
            if subset not in tags:
                tags.append(subset)
            if len(tags) >= 5:
                break

    # 调整标签顺序，使长标签在前面
    tags.sort(key=len, reverse=True)

    # 返回标签，确保至少3个，最多5个
    return tags[:5]

def random_sleep(min_seconds: float = 0.5, max_seconds: float = 2.0) -> None:
    """
    随机睡眠一段时间，用于模拟人类操作

    Args:
        min_seconds: 最小睡眠时间（秒）
        max_seconds: 最大睡眠时间（秒）
    """
    sleep_time = random.uniform(min_seconds, max_seconds)
    time.sleep(sleep_time)

def create_required_dirs(dirs: List[str]) -> None:
    """
    确保必要的目录存在

    Args:
        dirs: 目录路径列表，支持环境变量和相对路径
    """
    for dir_path in dirs:
        if not dir_path:
            continue

        # 解析路径中的环境变量
        if '%' in dir_path or '${' in dir_path:
            # 解析环境变量 ${ENV_VAR} 格式
            if '${' in dir_path and '}' in dir_path:
                for match in re.finditer(r'\${([^}]+)}', dir_path):
                    env_var = match.group(1)
                    env_value = os.environ.get(env_var, '')
                    dir_path = dir_path.replace(match.group(0), env_value)

            # 解析环境变量 %ENV_VAR% 格式
            if '%' in dir_path:
                for match in re.finditer(r'%([^%]+)%', dir_path):
                    env_var = match.group(1)
                    env_value = os.environ.get(env_var, '')
                    dir_path = dir_path.replace(match.group(0), env_value)

        # 创建目录
        if dir_path and not os.path.exists(dir_path):
            os.makedirs(dir_path, exist_ok=True)

def parse_number(number_str: Any) -> float:
    """
    解析数字字符串为浮点数，支持带"万"单位的数字

    Args:
        number_str: 数字字符串或数字

    Returns:
        解析后的浮点数
    """
    try:
        # 如果已经是数字类型，直接返回
        if isinstance(number_str, (int, float)):
            return float(number_str)

        # 如果是空值，返回0
        if not number_str:
            return 0.0

        # 转换为字符串并去除逗号
        str_value = str(number_str).replace(',', '')

        # 检查是否包含"万"单位
        if "万" in str_value:
            # 提取数字部分
            match = re.search(r'([\d\.]+)', str_value)
            if match:
                # 将"万"单位的数字转换为实际数值
                return float(match.group(1)) * 10000
        else:
            # 提取数字部分
            match = re.search(r'([\d\.]+)', str_value)
            if match:
                return float(match.group(1))

        # 无法解析时返回0
        return 0.0
    except Exception:
        return 0.0

def format_number(number: Any) -> str:
    """
    格式化数字，大于10000的数字显示为"x.xx万"格式
    如果输入是带"万"单位的字符串，保持原格式

    Args:
        number: 要格式化的数字

    Returns:
        格式化后的字符串
    """
    try:
        # 如果是字符串且包含"万"，直接返回原格式
        if isinstance(number, str) and "万" in number:
            # 确保格式正确
            try:
                # 提取数字部分
                match = re.search(r'([\d\.]+)', number)
                if match:
                    # 格式化为标准格式，但保持"万"单位
                    return f"{float(match.group(1)):.2f}万".replace('.00', '')
                return number
            except:
                return number

        # 转换为浮点数
        num = float(number) if number is not None else 0

        # 对于0或非常小的数，直接返回"0"
        if abs(num) < 0.01:
            return "0"

        # 对于大于等于10000的数，使用"万"作为单位
        if abs(num) >= 10000:
            # 转换为"万"单位
            wan_value = num / 10000
            # 格式化为两位小数，并去除末尾的0
            formatted = f"{wan_value:.2f}万".replace('.00', '')
            return formatted
        # 特殊处理：如果数字是5000，可能是0.5万
        elif abs(num) == 5000:
            # 对于5000，返回0.5万格式
            return "0.5万"

        # 对于普通数字，直接返回整数或保留两位小数
        if num.is_integer():
            return str(int(num))
        else:
            # 格式化为两位小数，并去除末尾的0
            formatted = f"{num:.2f}".replace('.00', '')
            return formatted
    except Exception:
        # 出错时返回"0"
        return "0"

def parse_income(income_str: Any) -> float:
    """
    解析收益字符串为数字

    Args:
        income_str: 收益字符串或数字

    Returns:
        解析后的数字
    """
    return parse_number(income_str)

def launch_video_processor(log_callback: Callable = None) -> bool:
    """
    启动视频处理工具

    Args:
        log_callback: 日志回调函数

    Returns:
        是否成功启动
    """
    try:
        import tkinter as tk
        from tkinter import messagebox

        # 检查是否在打包环境中
        if getattr(sys, 'frozen', False):
            # 在打包环境中，直接导入并启动视频处理工具
            try:
                # 导入视频处理工具模块
                sys.path.insert(0, os.path.join(sys._MEIPASS, '视频处理工具'))
                from 视频处理工具.modules.ui.main import VideoPreprocessUI
                from 视频处理工具.modules.video_processor import VideoProcessor

                # 创建新窗口
                video_window = tk.Toplevel()
                video_window.title("🎬 视频处理工具")
                video_window.geometry("1200x800")
                video_window.minsize(1000, 700)

                # 创建视频处理器
                video_processor = VideoProcessor()

                # 创建UI实例
                ui = VideoPreprocessUI(video_window, process_callback=video_processor.process_video,
                                     auto_mode=False, video_processor=video_processor)

                if log_callback:
                    log_callback("✅ 视频处理工具已在当前程序中打开")
                return True

            except Exception as e:
                if log_callback:
                    log_callback(f"❌ 在打包环境中启动视频处理工具失败: {str(e)}")
                # 回退到外部启动方式
                return launch_video_processor_external(log_callback)
        else:
            # 在开发环境中，使用外部启动方式
            return launch_video_processor_external(log_callback)

    except Exception as e:
        if log_callback:
            log_callback(f"启动视频处理工具时出错: {str(e)}")
        return False

def launch_video_processor_external(log_callback: Callable = None) -> bool:
    """
    外部启动视频处理工具（开发环境或回退方案）
    """
    try:
        # 获取当前脚本所在目录
        current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

        # 获取视频处理工具的路径
        video_processor_path = os.path.join(current_dir, "视频处理工具", "main.py")

        # 检查文件是否存在
        if not os.path.exists(video_processor_path):
            # 尝试查找备用文件
            video_processor_path = os.path.join(current_dir, "视频处理工具", "视频处理工具.py")
            if not os.path.exists(video_processor_path):
                if log_callback:
                    log_callback("找不到视频处理工具文件")
                return False

        # 使用Python解释器启动视频处理工具，不创建新控制台
        python_exe = sys.executable
        subprocess.Popen([python_exe, video_processor_path])

        if log_callback:
            log_callback("已启动视频处理工具")
        return True

    except Exception as e:
        if log_callback:
            log_callback(f"启动视频处理工具时出错: {str(e)}")
        return False
