"""
账号控制器 - 遵循MECE原则的独立控制器

职责：
- 处理账号相关的用户交互
- 协调账号UI组件和账号服务
- 管理账号选择状态
- 处理账号操作事件

独立性：不依赖其他控制器，只依赖服务层和UI组件
"""

import tkinter as tk
from tkinter import messagebox
from typing import Optional, Callable, Dict, Any, List


class AccountController:
    """账号控制器"""
    
    def __init__(self, config_manager, account_service=None, ui_components: Dict = None):
        """
        初始化账号控制器
        
        Args:
            config_manager: 配置管理器
            account_service: 账号服务
            ui_components: UI组件字典
        """
        self.config_manager = config_manager
        self.account_service = account_service
        self.ui_components = ui_components or {}
        
        # 当前选中的账号
        self.selected_account = None
        
        # 账号列表
        self.accounts = []
        
        # 回调函数
        self.callbacks = {}
    
    def set_account_service(self, account_service):
        """设置账号服务"""
        self.account_service = account_service
    
    def set_ui_component(self, component_name: str, component):
        """设置UI组件"""
        self.ui_components[component_name] = component
    
    def set_callback(self, callback_name: str, callback: Callable):
        """设置回调函数"""
        self.callbacks[callback_name] = callback

    def _delegate_to_ui(self, method_name: str) -> Callable:
        """创建委托到UI的回调函数"""
        def wrapper(*args, **kwargs):
            callback = self.callbacks.get(method_name)
            if callback:
                return callback(*args, **kwargs)
            else:
                print(f"警告：回调函数 {method_name} 未设置")
        return wrapper
    
    def get_callback_dict(self) -> Dict[str, Callable]:
        """获取回调函数字典，供UI组件使用"""
        return {
            'add_account': self.add_account,
            'delete_selected_accounts': self.delete_selected_accounts,
            'refresh_account_list': self.refresh_account_list,
            'open_account_directory': self.open_account_directory,
            'show_batch_delete_dialog': self.show_batch_delete_dialog,
            'show_duplicate_cleaner_dialog': self.show_duplicate_cleaner_dialog,
            'on_account_select': self.on_account_select,
            'filter_main_accounts': self.filter_main_accounts,
            'sort_main_table_by_column': self.sort_main_table_by_column,
            'apply_saved_main_sort_config': self.apply_saved_main_sort_config,
            # 添加缺失的回调函数
            'start_data_query': self._delegate_to_ui('start_data_query'),
            'show_seven_day_income': self._delegate_to_ui('show_seven_day_income'),
            'export_accounts_to_excel': self._delegate_to_ui('export_accounts_to_excel'),
            'backup_account_data': self._delegate_to_ui('backup_account_data'),
            'show_collected_data': self._delegate_to_ui('show_collected_data'),
            'show_duplicate_cleaner_dialog': self._delegate_to_ui('show_duplicate_cleaner_dialog')
        }
    
    def add_account(self):
        """添加账号"""
        try:
            # 根据当前平台选择不同的添加逻辑
            current_platform = self.config_manager.get_current_platform()

            if current_platform == "netease":
                self._add_netease_account()
            elif current_platform == "toutiao":
                self._add_toutiao_account()
            elif current_platform == "dayu":
                self._add_dayu_account()
            else:
                messagebox.showwarning("警告", "当前平台不支持添加账号")
        except Exception as e:
            messagebox.showerror("错误", f"添加账号失败：\n{str(e)}")
    
    def _add_netease_account(self):
        """添加网易号账号"""
        # 直接调用回调函数
        callback = self.callbacks.get('add_netease_account')
        if callback:
            callback()
        else:
            messagebox.showwarning("警告", "网易号添加账号功能未配置")

    def _add_toutiao_account(self):
        """添加头条账号"""
        # 直接调用回调函数
        callback = self.callbacks.get('add_toutiao_account')
        if callback:
            callback()
        else:
            messagebox.showwarning("警告", "头条添加账号功能未配置")

    def _add_dayu_account(self):
        """添加大鱼号账号"""
        # 直接调用回调函数
        callback = self.callbacks.get('add_dayu_account')
        if callback:
            callback()
        else:
            messagebox.showwarning("警告", "大鱼号添加账号功能未配置")
    
    def delete_selected_accounts(self):
        """删除选中的账号"""
        try:
            # 获取选中的账号
            accounts_table = self.ui_components.get('accounts_table')
            if not accounts_table:
                return
            
            selected_items = accounts_table.get_selected_items()
            if not selected_items:
                messagebox.showwarning("警告", "请先选择要删除的账号")
                return
            
            # 获取选中的账号列表
            selected_accounts = []
            for item in selected_items:
                values = accounts_table.get_item_values(item)
                if values and len(values) > 1:
                    account = values[1]  # 账号列的索引为1
                    selected_accounts.append(account)
            
            if not selected_accounts:
                return
            
            # 确认删除
            account_list = "\n".join(selected_accounts)
            result = messagebox.askyesno(
                "确认删除",
                f"确定要删除以下账号吗？\n\n{account_list}\n\n此操作不可撤销！"
            )
            
            if result:
                # 执行删除
                if self.account_service:
                    success_count = self.account_service.delete_accounts(selected_accounts)
                else:
                    # 调用回调函数
                    callback = self.callbacks.get('delete_accounts')
                    if callback:
                        success_count = callback(selected_accounts)
                    else:
                        success_count = 0
                
                # 显示结果
                if success_count > 0:
                    # 清除当前选中的账号（如果被删除了）
                    if self.selected_account in selected_accounts:
                        self.selected_account = None
                    
                    # 更新账号列表
                    self.refresh_account_list()
                    
                    # 更新统计信息面板
                    self._update_stats_panel()
                    
                    # 刷新表格显示
                    self._refresh_table_display()
                
                # 显示结果消息
                if success_count == len(selected_accounts):
                    messagebox.showinfo("成功", f"成功删除 {success_count} 个账号")
                elif success_count > 0:
                    messagebox.showwarning("部分成功", f"成功删除 {success_count} 个账号，{len(selected_accounts) - success_count} 个账号删除失败")
                else:
                    messagebox.showerror("失败", "删除账号失败")
                    
        except Exception as e:
            messagebox.showerror("错误", f"删除账号失败：\n{str(e)}")
    
    def refresh_account_list(self):
        """刷新账号列表"""
        try:
            if self.account_service:
                # 重新加载账号数据
                self.accounts = self.account_service.load_accounts()
                
                # 更新表格显示
                self._refresh_table_display()
            else:
                # 调用回调函数
                callback = self.callbacks.get('load_accounts')
                if callback:
                    callback()
        except Exception as e:
            messagebox.showerror("错误", f"刷新账号列表失败：\n{str(e)}")
    
    def open_account_directory(self):
        """打开账号目录"""
        try:
            if self.account_service:
                self.account_service.open_account_directory()
            else:
                # 调用回调函数
                callback = self.callbacks.get('open_account_directory')
                if callback:
                    callback()
        except Exception as e:
            messagebox.showerror("错误", f"打开账号目录失败：\n{str(e)}")
    
    def show_batch_delete_dialog(self):
        """显示批量删除对话框"""
        try:
            # 调用回调函数
            callback = self.callbacks.get('show_batch_delete_dialog')
            if callback:
                callback()
        except Exception as e:
            messagebox.showerror("错误", f"显示批量删除对话框失败：\n{str(e)}")
    
    def show_duplicate_cleaner_dialog(self):
        """显示重复账号清理对话框"""
        try:
            # 调用回调函数
            callback = self.callbacks.get('show_duplicate_cleaner_dialog')
            if callback:
                callback()
        except Exception as e:
            messagebox.showerror("错误", f"显示重复账号清理对话框失败：\n{str(e)}")
    
    def on_account_select(self, event):
        """账号选择事件处理"""
        try:
            # 委托给主UI的on_account_select方法
            main_ui = self.ui_components.get('main_ui')
            if main_ui and hasattr(main_ui, 'on_account_select'):
                main_ui.on_account_select(event)
            else:
                # 备用处理逻辑
                accounts_table = self.ui_components.get('accounts_table')
                if not accounts_table:
                    return

                # 获取选中的项目
                selected_items = accounts_table.get_selected_items()
                if not selected_items:
                    return

                # 获取选中的账号
                values = accounts_table.get_item_values(selected_items[0])
                if values and len(values) > 1:
                    account = values[1]  # 账号列的索引为1

                    # 设置选中的账号
                    self.selected_account = account

                    # 更新统计信息面板中的当前账号显示
                    self._update_current_account_display(account)

        except Exception as e:
            # 忽略选择错误
            pass
    
    def filter_main_accounts(self, *args):
        """过滤主页面账号数据"""
        try:
            # 调用回调函数
            callback = self.callbacks.get('filter_main_accounts')
            if callback:
                callback(*args)
        except Exception as e:
            # 忽略过滤错误
            pass
    
    def sort_main_table_by_column(self, column):
        """主页面表格按列排序"""
        try:
            # 调用回调函数
            callback = self.callbacks.get('sort_main_table_by_column')
            if callback:
                callback(column)
        except Exception as e:
            # 忽略排序错误
            pass
    
    def apply_saved_main_sort_config(self):
        """应用已保存的主页面排序配置"""
        try:
            # 调用回调函数
            callback = self.callbacks.get('apply_saved_main_sort_config')
            if callback:
                callback()
        except Exception as e:
            # 忽略配置应用错误
            pass
    
    def _update_stats_panel(self):
        """更新统计信息面板"""
        navigation_panel = self.ui_components.get('navigation_panel')
        if navigation_panel:
            navigation_panel.update_stats_panel()
    
    def _update_current_account_display(self, account_name: str):
        """更新当前账号显示"""
        navigation_panel = self.ui_components.get('navigation_panel')
        if navigation_panel:
            navigation_panel.update_current_account(account_name)
    
    def _refresh_table_display(self):
        """刷新表格显示"""
        # 调用回调函数
        callback = self.callbacks.get('update_account_tree')
        if callback:
            callback()
    
    def get_selected_account(self) -> Optional[str]:
        """获取当前选中的账号"""
        return self.selected_account
    
    def set_selected_account(self, account: str):
        """设置当前选中的账号"""
        self.selected_account = account
        self._update_current_account_display(account)
    
    def get_accounts(self) -> List:
        """获取账号列表"""
        return self.accounts
    
    def set_accounts(self, accounts: List):
        """设置账号列表"""
        self.accounts = accounts
