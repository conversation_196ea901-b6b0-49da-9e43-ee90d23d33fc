#!/usr/bin/env python3
"""
测试jaraco.text模块是否可用
"""

def test_jaraco():
    print("测试jaraco.text模块...")
    
    try:
        import jaraco.text
        print("✅ jaraco.text 导入成功")
        
        # 测试一些基本功能
        try:
            # 测试一些jaraco.text的功能
            result = jaraco.text.normalize_space("  hello   world  ")
            print(f"✅ jaraco.text.normalize_space 工作正常: '{result}'")
        except Exception as e:
            print(f"⚠️  jaraco.text 功能测试失败: {e}")
            
    except ImportError as e:
        print(f"❌ jaraco.text 导入失败: {e}")
        
        # 尝试安装
        print("尝试安装jaraco.text...")
        import subprocess
        import sys
        
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'jaraco.text'])
            print("✅ jaraco.text 安装成功")
            
            # 重新测试
            import jaraco.text
            print("✅ jaraco.text 重新导入成功")
            
        except Exception as install_error:
            print(f"❌ jaraco.text 安装失败: {install_error}")

def test_other_deps():
    """测试其他关键依赖"""
    deps = [
        'pkg_resources',
        'setuptools', 
        'packaging',
        'pyinstaller'
    ]
    
    print("\n测试其他关键依赖...")
    for dep in deps:
        try:
            __import__(dep)
            print(f"✅ {dep}")
        except ImportError:
            print(f"❌ {dep}")

if __name__ == "__main__":
    test_jaraco()
    test_other_deps()
    print("\n测试完成！")
