"""
主UI模块 - 负责创建和管理整体界面

"""

import os
import sys
import time
import json
import shutil
from datetime import datetime
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, simpledialog, filedialog
import threading
import queue
from typing import Dict, Any, Optional, Callable, List
from concurrent.futures import ThreadPoolExecutor, as_completed
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from 网易号存稿.common.config import ConfigManager
from 网易号存稿.common.logger import logger
from 网易号存稿.common.utils import center_window
from 网易号存稿.account.manager import AccountManager
from 网易号存稿.account.data import AccountData
# 启用存稿相关模块
from 网易号存稿.concurrency.manager import ConcurrentManager
from 网易号存稿.ui.styles.theme import Theme
from 网易号存稿.ui.styles.checkbox_styles import create_beautiful_checkbox

# 导入新的模块化组件
from 网易号存稿.ui.components.header_bar import HeaderBar
from 网易号存稿.ui.components.navigation_panel import NavigationPanel
from 网易号存稿.ui.components.accounts_table import AccountsTable
from 网易号存稿.ui.controllers.account_controller import AccountController

class NeteaseDraftUI:
    """网易号存稿工具主UI类"""

    def __init__(self, root: tk.Tk, config_manager: ConfigManager):
        """
        初始化主UI

        Args:
            root: Tkinter根窗口
            config_manager: 配置管理器
        """
        self.root = root
        self.config_manager = config_manager

        # 获取当前平台
        self.current_platform = self.config_manager.get_current_platform()

        # 初始化变量
        self.is_running = False
        self.is_querying = False
        self.data_query_running = False
        self.selected_account = tk.StringVar()
        self._platform_switching = False  # 平台切换状态标志

        # 运行选项 - 从当前平台配置中获取
        self.headless_mode = tk.BooleanVar(value=self.config_manager.get("headless_mode", False, platform=self.current_platform))
        self.archive_completed = tk.BooleanVar(value=self.config_manager.get("archive_completed", True, platform=self.current_platform))
        self.draft_limit = tk.IntVar(value=self.config_manager.get("draft_limit", 0, platform=self.current_platform))
        self.loop_limit = tk.IntVar(value=self.config_manager.get("loop_limit", 0, platform=self.current_platform))
        self.concurrent_accounts = tk.IntVar(value=self.config_manager.get("concurrent_accounts", 1, platform=self.current_platform))
        self.random_video_allocation = tk.BooleanVar(value=self.config_manager.get("random_video_allocation", True, platform=self.current_platform))

        # 数据查询选项 - 从当前平台配置中获取
        self.query_headless_mode = tk.BooleanVar(value=self.config_manager.get("query_headless_mode", False, platform=self.current_platform))
        self.query_max_threads = tk.IntVar(value=self.config_manager.get("query_max_threads", 10, platform=self.current_platform))

        # 浏览器运行选项 - 从当前平台配置中获取
        self.auto_close = tk.BooleanVar(value=self.config_manager.get("auto_close", True, platform=self.current_platform))
        self.concurrent_mode = tk.BooleanVar(value=self.config_manager.get("concurrent_mode", False, platform=self.current_platform))
        self.max_workers = tk.IntVar(value=self.config_manager.get("max_workers", 3, platform=self.current_platform))

        # 主题设置 - 从通用配置中获取
        self.dark_mode = tk.BooleanVar(value=self.config_manager.get("dark_mode", False, platform="common"))

        # 界面设置 - 从通用配置中获取，增加窗口宽度50像素，统一使用系统默认字体大小
        self.font_size = tk.IntVar(value=self.config_manager.get("font_size", Theme.FONT_SIZE_NORMAL, platform="common"))
        self.window_width = tk.IntVar(value=self.config_manager.get("window_width", 1330, platform="common"))  # 从1280增加到1330
        self.window_height = tk.IntVar(value=self.config_manager.get("window_height", 800, platform="common"))

        # 日志过滤设置 - 从通用配置中获取
        self.enable_level_filter = tk.BooleanVar(value=self.config_manager.get("enable_level_filter", False, platform="common"))
        self.enable_keyword_filter = tk.BooleanVar(value=self.config_manager.get("enable_keyword_filter", False, platform="common"))
        self.min_log_level = tk.StringVar(value=self.config_manager.get("min_log_level", "INFO", platform="common"))

        # 极简日志模式设置 - 从通用配置中获取

        # 串行刷新管理器
        self._refresh_queue = queue.Queue()
        self._refresh_thread = None
        self._refresh_running = False
        self._refresh_lock = threading.Lock()
        self._last_sort_column = None
        self._last_sort_reverse = False
        self._current_scroll_position = 0
        self._selected_items = []  # 保留兼容性
        self._selected_accounts = []  # 新的基于账号名的选中状态

        # 防抖定时器
        self._debounce_timer = None
        self._pending_updates = set()  # 待更新的账号集合
        self.simple_log_mode = tk.BooleanVar(value=self.config_manager.get("simple_log_mode", False, platform="common"))

        self.accounts = []  # 初始化账号列表

        # 初始化进度数据
        self.account_progress = {}  # 存储账号进度数据

        # 运行时长计时器相关变量
        self.operation_start_time = None
        self.runtime_timer = None
        self.is_timing = False

        # 成功/失败统计
        self.current_success_count = 0
        self.current_fail_count = 0

        # 目录变量 - 从当前平台配置中获取
        self.account_dir = tk.StringVar(value=self.config_manager.get("account_dir", "", platform=self.current_platform))
        self.video_dir = tk.StringVar(value=self.config_manager.get("video_dir", "", platform=self.current_platform))
        self.cover_dir = tk.StringVar(value=self.config_manager.get("cover_dir", "", platform=self.current_platform))
        self.processed_dir = tk.StringVar(value=self.config_manager.get("processed_dir", "", platform=self.current_platform))
        self.processed_covers_dir = tk.StringVar(value=self.config_manager.get("processed_covers_dir", "", platform=self.current_platform))
        self.violation_dir = tk.StringVar(value=self.config_manager.get("violation_dir", "", platform=self.current_platform))
        self.screenshots_dir = tk.StringVar(value=self.config_manager.get("screenshots_dir", "", platform=self.current_platform))

        # 初始化日志队列
        self.log_queue = queue.Queue()

        # 初始化存稿详情数据结构
        self.account_details = {}

        # 预初始化所有管理器属性为None，避免AttributeError
        self.log_manager = None
        self.data_query_manager = None
        self.excel_export_manager = None
        self.draft_task_manager = None
        self.settings_manager = None
        self.resource_manager = None

        # 初始化账号管理器
        self.account_manager = AccountManager(self.account_dir.get(), self.log, self.current_platform)

        # 手动加载账号列表（因为已经移除了AccountManager构造函数中的自动加载）
        self.accounts = self.account_manager.load_accounts()
        self.log(f"已加载 {len(self.accounts)} 个账号")

        # 初始化账号数据处理器 - 使用统一的数据文件路径逻辑
        data_file = self._get_data_file_path()
        self.account_data = AccountData(data_file, self.log)

        # 初始化并发管理器
        self.concurrent_manager = ConcurrentManager(
            self.account_dir.get(),
            self.processed_dir.get(),
            self.processed_covers_dir.get(),
            self.archive_completed.get(),
            self.headless_mode.get(),
            self.draft_limit.get(),
            self.loop_limit.get(),
            self.log,
            self.screenshots_dir.get()
        )

        # 初始化新的管理器组件
        self._init_managers()

        # 初始化新的模块化组件
        self.header_bar = None
        self.navigation_panel = None
        self.accounts_table = None
        self.account_controller = None

        # 应用主题
        self.apply_theme()

        # 初始化复选框样式美化
        self.initialize_checkbox_styles()

        # 设置窗口大小
        self.set_window_size()

        # 创建UI
        self.create_ui()

        # 设置日志回调 - 使用LogManager的log方法避免循环
        logger.set_ui_callback(self.log_manager.log)

        # 初始化日志过滤设置
        logger.enable_level_filtering(self.enable_level_filter.get())
        logger.enable_keyword_filtering(self.enable_keyword_filter.get())
        logger.set_min_level(self.min_log_level.get())

        # 初始化存稿处理器（只调用一次）
        self.init_platform_processors()

        # 延迟绑定变量变化事件，避免初始化时触发
        self.root.after(1000, self.bind_variable_changes)

        # 初始化统计信息显示
        self.root.after(100, self.update_stats_panel)

        # 旧定时任务检查已移除，现在使用升级版定时任务系统

        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 确保所有必要的目录都已创建
        self.create_required_dirs()

        # 初始化完成后更新UI显示
        self.root.after(200, self.update_initial_ui)

    def update_initial_ui(self):
        """初始化完成后更新UI显示"""
        try:
            # 更新账号数量显示
            if hasattr(self, 'accounts'):
                self.update_account_count_display()

            # 更新账号列表显示
            self.update_account_tree()

            # 更新统计信息面板
            self.update_stats_panel()

            self.log("✅ 初始UI更新完成")

        except Exception as e:
            self.log(f"❌ 初始UI更新失败: {e}")

    def _init_managers(self):
        """初始化所有管理器组件"""
        # 导入管理器
        try:
            from 网易号存稿.ui.managers.log_manager import LogManager
            from 网易号存稿.ui.managers.data_query_manager import DataQueryManager
            from 网易号存稿.ui.managers.excel_export_manager import ExcelExportManager
            from 网易号存稿.ui.managers.draft_task_manager import DraftTaskManager
            from 网易号存稿.ui.managers.settings_manager import SettingsManager
            from 网易号存稿.ui.managers.resource_manager import ResourceManager
            from 网易号存稿.ui.managers.scheduler_manager import SchedulerManagerUI
        except ImportError as e:
            print(f"❌ 导入管理器模块失败: {e}")
            return

        # 逐个初始化管理器，确保单个失败不影响其他管理器
        managers_config = [
            ("log_manager", LogManager, "日志管理器"),
            ("data_query_manager", DataQueryManager, "数据查询管理器"),
            ("excel_export_manager", ExcelExportManager, "Excel导出管理器"),
            ("draft_task_manager", DraftTaskManager, "存稿任务管理器"),
            ("settings_manager", SettingsManager, "设置管理器"),
            ("resource_manager", ResourceManager, "资源管理器"),
            ("scheduler_manager", SchedulerManagerUI, "定时任务管理器")
        ]

        initialized_count = 0
        for attr_name, manager_class, display_name in managers_config:
            try:
                if attr_name == "data_query_manager":
                    manager = manager_class(self, self.config_manager, self.account_manager, self.account_data)
                elif attr_name == "excel_export_manager":
                    manager = manager_class(self, self.config_manager, self.account_data)
                elif attr_name == "draft_task_manager":
                    manager = manager_class(self, self.config_manager, self.account_manager, self.concurrent_manager)
                elif attr_name == "scheduler_manager":
                    manager = manager_class(self, self.config_manager)
                else:
                    manager = manager_class(self, self.config_manager)

                setattr(self, attr_name, manager)
                initialized_count += 1
                print(f"✅ {display_name}初始化成功")

            except Exception as e:
                print(f"❌ {display_name}初始化失败: {e}")
                setattr(self, attr_name, None)

        print(f"✅ 管理器初始化完成: {initialized_count}/{len(managers_config)}")

        # 设置调度器管理器与其他管理器的关联
        if hasattr(self, 'scheduler_manager') and self.scheduler_manager:
            try:
                self.scheduler_manager.set_managers(
                    data_query_manager=getattr(self, 'data_query_manager', None),
                    draft_task_manager=getattr(self, 'draft_task_manager', None)
                )
                print("✅ 调度器管理器关联设置完成")
            except Exception as e:
                print(f"❌ 调度器管理器关联设置失败: {e}")

        # 初始化数据查看器列表
        self.data_viewers = []

        # 管理器初始化完成后，加载存稿详情数据
        try:
            self.load_draft_details()
        except Exception as e:
            print(f"❌ 加载存稿详情失败: {e}")

    def create_ui(self):
        """创建美化的用户界面"""
        # 设置窗口标题和图标
        self.root.title("🚀 多平台存稿工具 - 现代化版本")

        # 创建主容器
        main_container = ttk.Frame(self.root)
        main_container.pack(fill=tk.BOTH, expand=True)

        # 初始化控制器
        self._init_controllers()

        # 创建顶部标题栏（使用新的HeaderBar组件）
        self.header_bar = HeaderBar(main_container, self.config_manager, self.toggle_theme_bulb)
        self.header_bar.pack(fill=tk.X, padx=15, pady=15)

        # 创建主要内容区域
        content_container = ttk.Frame(main_container)
        content_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))

        # 创建左右分栏布局（使用新的模块化组件）
        self.create_modular_layout(content_container)

        # 创建底部状态栏
        self.create_modern_status_bar(main_container)

    def _init_controllers(self):
        """初始化控制器"""
        # 初始化账号控制器
        self.account_controller = AccountController(
            self.config_manager,
            account_service=None,  # 稍后设置
            ui_components={}
        )

        # 设置账号控制器的回调函数
        self.account_controller.set_callback('add_netease_account', self._add_netease_account)
        self.account_controller.set_callback('add_toutiao_account', self._add_toutiao_account)
        self.account_controller.set_callback('add_dayu_account', self._add_dayu_account)
        self.account_controller.set_callback('delete_accounts', self._delete_accounts_impl)
        self.account_controller.set_callback('load_accounts', self.load_accounts)
        self.account_controller.set_callback('open_account_directory', self.open_account_directory)
        self.account_controller.set_callback('show_batch_delete_dialog', self.show_batch_delete_dialog)
        self.account_controller.set_callback('show_duplicate_cleaner_dialog', self.show_duplicate_cleaner_dialog)
        self.account_controller.set_callback('filter_main_accounts', self.filter_main_accounts)
        self.account_controller.set_callback('update_account_tree', self.update_account_tree)

        # 添加缺失的回调函数
        self.account_controller.set_callback('start_data_query', self.start_data_query)
        self.account_controller.set_callback('export_accounts_to_excel', self.export_accounts_to_excel)
        self.account_controller.set_callback('backup_account_data', self.backup_account_data)
        self.account_controller.set_callback('show_collected_data', self.show_collected_data)

    def create_modular_layout(self, parent):
        """创建模块化布局"""
        # 创建左右分栏
        left_panel = ttk.Frame(parent, width=145)  # 设置固定宽度145像素
        left_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        left_panel.pack_propagate(False)  # 防止子组件改变框架大小

        right_panel = ttk.Frame(parent)
        right_panel.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 创建左侧导航面板（使用新的NavigationPanel组件）
        from 网易号存稿.ui.components.platform_sidebar import PlatformSidebar
        self.navigation_panel = NavigationPanel(left_panel, self.config_manager, PlatformSidebar)
        self.navigation_panel.pack(fill=tk.BOTH, expand=True)

        # 设置平台侧边栏的UI引用
        self.navigation_panel.set_platform_sidebar_ui_reference(self)

        # 创建右侧内容面板
        self.create_content_panel(right_panel)

        # 设置UI组件引用
        self.account_controller.set_ui_component('navigation_panel', self.navigation_panel)

    def _delete_accounts_impl(self, account_list):
        """删除账号的实现"""
        try:
            success_count = 0
            for account in account_list:
                if self.account_manager.delete_account(account):
                    success_count += 1
            return success_count
        except Exception as e:
            self.log(f"❌ 删除账号失败: {str(e)}")
            return 0



    def create_main_layout(self, parent):
        """创建主要布局"""
        # 使用Frame布局而不是PanedWindow，以便更好地控制侧边栏宽度
        main_container = ttk.Frame(parent)
        main_container.pack(fill=tk.BOTH, expand=True)

        # 左侧导航面板 - 固定宽度的窄侧边栏，参考图片比例
        left_panel = ttk.Frame(main_container, width=145)  # 调整到145像素，确保平台标识文字能完整显示
        left_panel.pack(side=tk.LEFT, fill=tk.Y)
        left_panel.pack_propagate(False)  # 防止子组件改变框架大小

        # 右侧内容面板 - 占据剩余空间
        right_panel = ttk.Frame(main_container)
        right_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 创建左侧导航
        self.create_navigation_panel(left_panel)

        # 创建右侧内容区域
        self.create_content_panel(right_panel)

    def create_navigation_panel(self, parent):
        """创建左侧导航面板 - 带滚动条"""
        # 创建主容器 - 最小边距，让侧边栏尽可能窄
        nav_container = ttk.Frame(parent)
        nav_container.pack(fill=tk.BOTH, expand=True, padx=1, pady=1)  # 最小边距



        # 创建滚动区域
        self.create_scrollable_content(nav_container)

    def create_scrollable_content(self, parent):
        """创建可滚动的内容区域"""
        # 创建Canvas
        canvas = tk.Canvas(parent, highlightthickness=0)
        scrollable_frame = ttk.Frame(canvas)

        # 配置滚动
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        # 创建窗口
        canvas_window = canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")

        # 布局Canvas
        canvas.pack(fill="both", expand=True)

        # 绑定鼠标滚轮事件
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        def _bind_to_mousewheel(event):
            canvas.bind_all("<MouseWheel>", _on_mousewheel)

        def _unbind_from_mousewheel(event):
            canvas.unbind_all("<MouseWheel>")

        canvas.bind('<Enter>', _bind_to_mousewheel)
        canvas.bind('<Leave>', _unbind_from_mousewheel)

        # 配置Canvas窗口大小自适应
        def _configure_canvas(event):
            # 更新scrollable_frame的宽度以匹配canvas
            canvas_width = event.width
            canvas.itemconfig(canvas_window, width=canvas_width)

        canvas.bind('<Configure>', _configure_canvas)

        # 在scrollable_frame中创建内容
        self.create_navigation_content(scrollable_frame)

    def create_navigation_content(self, parent):
        """创建导航面板的内容"""
        # 统计信息面板 - 放在最上方
        self.create_stats_panel(parent)

        # 创建平台导航侧边栏
        self.create_platform_navigation(parent)

        # 设置现代化按钮样式（不包含特效绑定）
        self.setup_modern_button_styles_only()

        # 在所有按钮创建完成后设置特效
        self.setup_button_effects()

    def create_platform_navigation(self, parent):
        """创建平台导航区域"""
        # 导入平台侧边栏类
        from 网易号存稿.ui.components.platform_sidebar import PlatformSidebar

        # 创建平台侧边栏
        self.platform_sidebar = PlatformSidebar(
            parent,
            self.config_manager,
            self.log,
            self.on_platform_change
        )
        # 设置主程序UI引用
        self.platform_sidebar.main_ui = self



    def initialize_checkbox_styles(self):
        """初始化复选框样式美化"""
        try:
            from .styles.checkbox_styles import (
                initialize_global_checkbox_styles,
                get_checkbox_style,
                auto_beautify_checkboxes,
                apply_global_checkbox_styles
            )

            # 初始化全局复选框样式
            self.checkbox_manager = initialize_global_checkbox_styles()
            self.get_checkbox_style = get_checkbox_style

            # 启用自动美化功能，让所有新创建的复选框都使用美化样式
            auto_beautify_checkboxes()

            # 延迟应用样式到现有控件，确保UI完全创建后再应用
            if hasattr(self, 'root') and self.root:
                self.root.after(1000, lambda: apply_global_checkbox_styles(self.root))

            print("复选框样式美化已初始化")
        except Exception as e:
            print(f"初始化复选框样式失败: {e}")
            self.checkbox_manager = None
            self.get_checkbox_style = lambda style_type="modern": "TCheckbutton"

    def setup_modern_button_styles_only(self):
        """设置现代化按钮样式 - 仅样式配置，不绑定特效"""
        style = ttk.Style()

        # 获取系统默认颜色
        default_bg = style.lookup('TButton', 'background')
        default_fg = style.lookup('TButton', 'foreground')

        # 基础现代按钮样式
        style.configure("Modern.TButton",
                       font=("微软雅黑", 10),
                       padding=(15, 8),
                       relief="flat",
                       borderwidth=1)

        # 大按钮样式 - 用于主要操作
        style.configure("Large.TButton",
                       font=("微软雅黑", 12, "bold"),
                       padding=(25, 15),
                       relief="raised",
                       borderwidth=2)
        style.map("Large.TButton",
                 relief=[('pressed', 'sunken'),
                        ('active', 'raised')],
                 borderwidth=[('pressed', '3'),
                             ('active', '2')],
                 background=[('active', '#f0f0f0'),
                           ('pressed', '#e0e0e0')])

        # 中等按钮样式 - 用于常规操作
        style.configure("Medium.TButton",
                       font=("微软雅黑", 10),
                       padding=(18, 12),
                       relief="raised",
                       borderwidth=1)
        style.map("Medium.TButton",
                 relief=[('pressed', 'sunken'),
                        ('active', 'raised')],
                 borderwidth=[('pressed', '2'),
                             ('active', '1')],
                 background=[('active', '#f5f5f5'),
                           ('pressed', '#e8e8e8')])

        # 小按钮样式 - 用于辅助操作，更紧凑的设计
        style.configure("Small.TButton",
                       font=("微软雅黑", 8),  # 减小字体
                       padding=(8, 4),  # 减小内边距
                       relief="flat",
                       borderwidth=1)
        style.map("Small.TButton",
                 relief=[('pressed', 'sunken'),
                        ('active', 'groove')],
                 borderwidth=[('pressed', '2'),
                             ('active', '1')],
                 background=[('active', '#f8f8f8'),
                           ('pressed', '#eeeeee')])

        # 工具栏按钮样式 - 扁平化设计，更紧凑
        style.configure("Toolbar.TButton",
                       font=("微软雅黑", 8),  # 减小字体
                       padding=(6, 3),  # 减小内边距
                       relief="flat",
                       borderwidth=0)
        style.map("Toolbar.TButton",
                 relief=[('pressed', 'sunken'),
                        ('active', 'solid')],
                 borderwidth=[('pressed', '1'),
                             ('active', '1')],
                 background=[('active', '#e6e6e6'),
                           ('pressed', '#d4d4d4')])

        # 强调按钮样式 - 用于重要操作，保持系统色但增加视觉重量
        style.configure("Accent.TButton",
                       font=("微软雅黑", 11, "bold"),
                       padding=(20, 12),
                       relief="raised",
                       borderwidth=2)
        style.map("Accent.TButton",
                 relief=[('pressed', 'sunken'),
                        ('active', 'raised')],
                 borderwidth=[('pressed', '3'),
                             ('active', '2')],
                 background=[('active', '#e0e0e0'),
                           ('pressed', '#d0d0d0')],
                 foreground=[('active', '#000000'),
                           ('pressed', '#333333')])

        # 危险操作按钮样式 - 使用系统色但增加警告效果
        style.configure("Danger.TButton",
                       font=("微软雅黑", 10, "bold"),
                       padding=(18, 12),
                       relief="raised",
                       borderwidth=2)
        style.map("Danger.TButton",
                 relief=[('pressed', 'sunken'),
                        ('active', 'raised')],
                 borderwidth=[('pressed', '3'),
                             ('active', '2')],
                 background=[('active', '#ffe6e6'),
                           ('pressed', '#ffcccc')],
                 foreground=[('active', '#cc0000'),
                           ('pressed', '#990000')])

    def setup_button_effects(self):
        """设置按钮特效"""
        # 为主要按钮添加特效（带安全检查）
        if hasattr(self, 'start_button'):
            self.add_button_effects(self.start_button, "start")
        if hasattr(self, 'stop_button'):
            self.add_button_effects(self.stop_button, "stop")
        if hasattr(self, 'settings_button'):
            self.add_button_effects(self.settings_button, "settings")
        if hasattr(self, 'cache_cleaner_button'):
            self.add_button_effects(self.cache_cleaner_button, "cache")
        if hasattr(self, 'tools_button'):
            self.add_button_effects(self.tools_button, "tools")

    def add_button_effects(self, button, button_type="default"):
        """为按钮添加特效"""
        try:
            # 保存原始配置
            original_config = {}

            def on_enter(event):
                """鼠标进入按钮时的效果"""
                try:
                    # 保存当前配置
                    original_config['relief'] = button.cget('relief')

                    # 应用悬停效果
                    if button_type == "start":
                        button.configure(relief="raised")
                    elif button_type == "stop":
                        button.configure(relief="raised")
                    elif button_type == "tools":
                        button.configure(relief="raised")
                    else:
                        button.configure(relief="raised")

                    # 添加光标变化
                    button.configure(cursor="hand2")

                except Exception as e:
                    pass  # 忽略配置错误

            def on_leave(event):
                """鼠标离开按钮时的效果"""
                try:
                    # 恢复原始状态
                    if 'relief' in original_config:
                        button.configure(relief=original_config['relief'])
                    else:
                        # 根据按钮类型设置默认relief
                        if button_type in ["start", "tools"]:
                            button.configure(relief="raised")
                        else:
                            button.configure(relief="raised")

                    # 恢复默认光标
                    button.configure(cursor="")

                except Exception as e:
                    pass  # 忽略配置错误

            def on_click(event):
                """按钮点击时的效果"""
                try:
                    # 点击动画效果
                    button.configure(relief="sunken")

                    # 延迟恢复
                    def restore_relief():
                        try:
                            button.configure(relief="raised")
                        except:
                            pass

                    # 100ms后恢复
                    button.after(100, restore_relief)

                except Exception as e:
                    pass  # 忽略配置错误

            # 绑定事件
            button.bind("<Enter>", on_enter)
            button.bind("<Leave>", on_leave)
            button.bind("<Button-1>", on_click)

        except Exception as e:
            # 如果绑定失败，不影响程序运行
            pass



    def create_stats_panel(self, parent):
        """创建统计信息面板"""
        # 使用LabelFrame创建卡片式设计 - 减少内边距让侧边栏更紧凑
        stats_card = ttk.LabelFrame(parent, text="📊 统计信息", padding=5)  # 从10减少到5
        stats_card.pack(fill=tk.X, pady=(0, 10))  # 从15减少到10

        # 当前平台信息
        platform_frame = ttk.Frame(stats_card)
        platform_frame.pack(fill=tk.X, pady=(0, 8))

        ttk.Label(
            platform_frame,
            text="当前平台:",
            font=("微软雅黑", 9),
            foreground="#6C757D"
        ).pack(side=tk.LEFT)

        self.current_platform_label = ttk.Label(
            platform_frame,
            text="网易号",
            font=("微软雅黑", 9, "bold"),
            foreground="#2E86AB"
        )
        self.current_platform_label.pack(side=tk.RIGHT)

        # 添加分隔线
        ttk.Separator(stats_card, orient="horizontal").pack(fill=tk.X, pady=5)

        # 当前账号信息
        current_account_frame = ttk.Frame(stats_card)
        current_account_frame.pack(fill=tk.X, pady=2)

        ttk.Label(
            current_account_frame,
            text="当前账号:",
            font=("微软雅黑", 9),
            foreground="#6C757D"
        ).pack(side=tk.LEFT)

        self.current_account_label = ttk.Label(
            current_account_frame,
            text="未选择",
            font=("微软雅黑", 9, "bold"),
            foreground="#DC3545"
        )
        self.current_account_label.pack(side=tk.RIGHT)

        # 账号数量
        account_count_frame = ttk.Frame(stats_card)
        account_count_frame.pack(fill=tk.X, pady=2)

        ttk.Label(
            account_count_frame,
            text="账号总数:",
            font=("微软雅黑", 9),
            foreground="#6C757D"
        ).pack(side=tk.LEFT)

        self.account_count_label = ttk.Label(
            account_count_frame,
            text="0",
            font=("微软雅黑", 9, "bold"),
            foreground="#2E86AB"
        )
        self.account_count_label.pack(side=tk.RIGHT)

        # 运行状态
        status_frame = ttk.Frame(stats_card)
        status_frame.pack(fill=tk.X, pady=2)

        ttk.Label(
            status_frame,
            text="运行状态:",
            font=("微软雅黑", 9),
            foreground="#6C757D"
        ).pack(side=tk.LEFT)

        self.running_status_label = ttk.Label(
            status_frame,
            text="🟢 空闲",
            font=("微软雅黑", 9, "bold"),
            foreground="#28A745"
        )
        self.running_status_label.pack(side=tk.RIGHT)

        # 任务进度
        progress_frame = ttk.Frame(stats_card)
        progress_frame.pack(fill=tk.X, pady=2)

        ttk.Label(
            progress_frame,
            text="任务进度:",
            font=("微软雅黑", 9),
            foreground="#6C757D"
        ).pack(side=tk.LEFT)

        self.task_progress_label = ttk.Label(
            progress_frame,
            text="0/0",
            font=("微软雅黑", 9, "bold"),
            foreground="#6C757D"
        )
        self.task_progress_label.pack(side=tk.RIGHT)



    def update_stats_panel(self):
        """更新统计信息面板"""
        try:
            # 更新当前平台显示
            current_platform = self.config_manager.get_current_platform()
            platform_names = self.config_manager.get("platform_names", {})
            platform_display_name = platform_names.get(current_platform, current_platform)

            if hasattr(self, 'current_platform_label'):
                self.current_platform_label.config(text=platform_display_name)

            # 更新当前账号显示
            if hasattr(self, 'current_account_label'):
                # 从selected_account变量获取当前选中的账号
                if hasattr(self, 'selected_account') and self.selected_account.get():
                    account_name = self.selected_account.get()
                    self.current_account_label.config(text=account_name, foreground="#2E86AB")
                else:
                    self.current_account_label.config(text="未选择", foreground="#DC3545")

            # 同时更新NavigationPanel中的当前账号显示
            if hasattr(self, 'navigation_panel') and self.navigation_panel:
                if hasattr(self, 'selected_account') and self.selected_account.get():
                    account_name = self.selected_account.get()
                    self.navigation_panel.update_current_account(account_name)
                else:
                    self.navigation_panel.update_current_account("未选择")

            # 更新账号数量
            if hasattr(self, 'account_manager') and self.account_manager:
                account_count = len(self.account_manager.accounts)
            else:
                account_count = 0

            # 更新主界面的账号数量标签（如果存在）
            if hasattr(self, 'account_count_label'):
                self.account_count_label.config(text=str(account_count))

            # 更新NavigationPanel中的账号数量
            if hasattr(self, 'navigation_panel') and self.navigation_panel:
                self.navigation_panel.update_account_count(account_count)

            # 更新运行状态
            if hasattr(self, 'is_running') and self.is_running:
                # 运行中状态
                if hasattr(self, 'running_status_label'):
                    self.running_status_label.config(text="🔄 运行中", foreground="#FFC107")
                # 同时更新导航面板
                if hasattr(self, 'navigation_panel') and self.navigation_panel:
                    self.navigation_panel.update_running_status("🔄 运行中", "#FFC107")
            else:
                # 空闲状态
                if hasattr(self, 'running_status_label'):
                    self.running_status_label.config(text="🟢 空闲", foreground="#28A745")
                # 同时更新导航面板
                if hasattr(self, 'navigation_panel') and self.navigation_panel:
                    self.navigation_panel.update_running_status("🟢 空闲", "#28A745")

        except Exception as e:
            self.log(f"更新统计信息失败: {str(e)}")

    def update_task_progress(self, current=0, total=0):
        """更新任务进度显示"""
        try:
            # 通过NavigationPanel更新任务进度
            if hasattr(self, 'navigation_panel') and self.navigation_panel:
                progress_text = f"{current}/{total}"
                self.navigation_panel.update_task_progress(progress_text)

                # 只在开始和完成时记录日志
                if current == 0 and total > 0:
                    self.log(f"📊 开始任务进度跟踪: {progress_text}")
                elif current == total and total > 0:
                    self.log(f"📊 任务进度完成: {progress_text}")
            # 如果有直接的task_progress_label，也更新它（备用）
            elif hasattr(self, 'task_progress_label'):
                try:
                    progress_text = f"{current}/{total}"
                    self.task_progress_label.config(text=progress_text)
                except RuntimeError as e:
                    if "main thread is not in main loop" in str(e):
                        return  # 主线程已结束，静默忽略
                    else:
                        raise
        except Exception as e:
            self.log(f"更新任务进度失败: {str(e)}")



    def update_running_status(self, status: str, color: str = "#28A745"):
        """更新侧边栏运行状态显示"""
        try:
            # 通过NavigationPanel更新运行状态
            if hasattr(self, 'navigation_panel') and self.navigation_panel:
                self.navigation_panel.update_running_status(status, color)
            # 如果有直接的running_status_label，也更新它
            elif hasattr(self, 'running_status_label'):
                try:
                    self.running_status_label.config(text=status, foreground=color)
                except RuntimeError as e:
                    if "main thread is not in main loop" in str(e):
                        return  # 主线程已结束，静默忽略
                    else:
                        raise
        except Exception as e:
            self.log(f"更新运行状态失败: {str(e)}")

    def update_success_fail_stats(self, success_count: int, fail_count: int):
        """更新成功/失败统计显示"""
        try:
            self.current_success_count = success_count
            self.current_fail_count = fail_count

            # 通过NavigationPanel更新统计显示
            if hasattr(self, 'navigation_panel') and self.navigation_panel:
                self.navigation_panel.update_success_fail_stats(success_count, fail_count)
        except Exception as e:
            self.log(f"更新成功/失败统计失败: {str(e)}")

    def reset_success_fail_stats(self):
        """重置成功/失败统计显示"""
        try:
            self.current_success_count = 0
            self.current_fail_count = 0

            # 通过NavigationPanel重置统计显示
            if hasattr(self, 'navigation_panel') and self.navigation_panel:
                self.navigation_panel.reset_success_fail_stats()
        except Exception as e:
            self.log(f"重置成功/失败统计失败: {str(e)}")

    def start_runtime_timer(self):
        """启动运行时长计时器"""
        try:
            self.operation_start_time = time.time()
            self.is_timing = True

            # 重置运行时长显示
            if hasattr(self, 'navigation_panel') and self.navigation_panel:
                self.navigation_panel.reset_runtime()

            # 启动计时器更新
            self._update_runtime_display()
        except Exception as e:
            self.log(f"启动运行时长计时器失败: {str(e)}")

    def stop_runtime_timer(self):
        """停止运行时长计时器"""
        try:
            self.is_timing = False

            # 取消定时器
            if self.runtime_timer:
                self.root.after_cancel(self.runtime_timer)
                self.runtime_timer = None
        except Exception as e:
            self.log(f"停止运行时长计时器失败: {str(e)}")

    def _update_runtime_display(self):
        """更新运行时长显示"""
        try:
            if self.is_timing and self.operation_start_time:
                # 计算运行时长
                elapsed_time = time.time() - self.operation_start_time
                hours = int(elapsed_time // 3600)
                minutes = int((elapsed_time % 3600) // 60)
                seconds = int(elapsed_time % 60)

                # 格式化时间显示
                runtime_text = f"{hours:02d}:{minutes:02d}:{seconds:02d}"

                # 更新显示
                if hasattr(self, 'navigation_panel') and self.navigation_panel:
                    self.navigation_panel.update_runtime(runtime_text)

                # 每秒更新一次
                self.runtime_timer = self.root.after(1000, self._update_runtime_display)
        except Exception as e:
            self.log(f"更新运行时长显示失败: {str(e)}")

    def parse_and_update_stats_from_log(self, log_message: str):
        """从日志消息中解析并更新成功/失败统计"""
        try:
            import re

            # 检查是否是完成消息 - 包含明确成功/失败统计的格式
            completion_patterns = [
                "数据查询完成！成功:",
                "处理完成！成功:",
                "存稿任务完成: 成功",
                "账号验证完成！成功:",
                "账号验证完成: 有效"  # 新增：支持账号验证的实际消息格式
            ]

            if any(pattern in log_message for pattern in completion_patterns):
                # 解析成功和失败数量
                # 匹配格式: "成功: X, 失败: Y" 或 "成功 X 个，失败 Y 个" 或 "有效 X 个，失效 Y 个"
                success_pattern = r"(?:成功|有效)[:\s]*(\d+)"
                fail_pattern = r"(?:失败|失效)[:\s]*(\d+)"

                success_match = re.search(success_pattern, log_message)
                fail_match = re.search(fail_pattern, log_message)

                if success_match and fail_match:
                    success_count = int(success_match.group(1))
                    fail_count = int(fail_match.group(1))

                    # 更新统计显示
                    self.update_success_fail_stats(success_count, fail_count)

                    # 停止计时器
                    self.stop_runtime_timer()
                    return

            # 检查是否是单账号查询完成消息 - "并发查询完成，共处理 X 个账号"格式
            single_query_patterns = [
                r"并发查询完成，共处理\s*(\d+)\s*个账号",
                r"查询完成，共处理\s*(\d+)\s*个账号"
            ]

            for pattern in single_query_patterns:
                match = re.search(pattern, log_message)
                if match:
                    total_count = int(match.group(1))

                    # 对于单账号查询（total_count == 1），需要从之前的日志推断成功/失败状态
                    if total_count == 1:
                        success_count, fail_count = self._infer_single_account_result()
                    else:
                        # 对于多账号查询，如果没有明确的成功/失败统计，暂时不处理
                        # 这种情况应该由其他完成消息处理
                        return

                    # 更新统计显示
                    self.update_success_fail_stats(success_count, fail_count)

                    # 停止计时器
                    self.stop_runtime_timer()
                    return

        except Exception as e:
            # 静默处理解析错误，不影响正常日志显示
            pass

    def _infer_single_account_result(self):
        """从最近的日志推断单账号查询的成功/失败状态"""
        try:
            # 获取最近的日志条目
            if hasattr(self, 'log_manager') and self.log_manager:
                # 从日志管理器获取最近的日志
                recent_logs = self._get_recent_logs(10)  # 获取最近10条日志
            else:
                # 如果没有日志管理器，从UI日志文本框获取
                recent_logs = self._get_recent_ui_logs(10)

            # 查找成功/失败的指示
            success_indicators = ["数据查询成功", "查询成功", "✅"]
            failure_indicators = ["数据查询失败", "查询失败", "查询无结果", "查询时出错", "❌"]

            # 从最近的日志中查找成功/失败指示
            for log_entry in reversed(recent_logs):  # 从最新的开始查找
                log_text = log_entry if isinstance(log_entry, str) else str(log_entry)

                # 检查是否包含成功指示
                if any(indicator in log_text for indicator in success_indicators):
                    return 1, 0  # 成功: 1, 失败: 0

                # 检查是否包含失败指示
                if any(indicator in log_text for indicator in failure_indicators):
                    return 0, 1  # 成功: 0, 失败: 1

            # 如果无法推断，默认为成功（因为查询完成了）
            return 1, 0

        except Exception as e:
            # 如果推断失败，默认为成功
            return 1, 0

    def _get_recent_logs(self, count=10):
        """从日志管理器获取最近的日志条目"""
        try:
            if hasattr(self.log_manager, 'get_recent_logs'):
                return self.log_manager.get_recent_logs(count)
            else:
                # 如果没有get_recent_logs方法，尝试其他方式
                return []
        except:
            return []

    def _get_recent_ui_logs(self, count=10):
        """从UI日志文本框获取最近的日志条目"""
        try:
            if hasattr(self, 'log_text') and self.log_text:
                # 获取日志文本框的内容
                log_content = self.log_text.get("1.0", "end-1c")
                # 按行分割并获取最后几行
                lines = log_content.strip().split('\n')
                return lines[-count:] if len(lines) >= count else lines
            else:
                return []
        except:
            return []

    def update_current_account_status(self, account_name, status="processing"):
        """更新当前账号状态显示

        Args:
            account_name: 账号名称
            status: 状态 ("processing", "idle", "none")
        """
        try:
            if hasattr(self, 'current_account_label'):
                try:
                    if status == "processing":
                        self.current_account_label.config(text=f"🔄 {account_name}", foreground="#FFC107")
                    elif status == "idle":
                        self.current_account_label.config(text=account_name, foreground="#2E86AB")
                    elif status == "none":
                        self.current_account_label.config(text="未选择", foreground="#DC3545")
                    else:
                        self.current_account_label.config(text=account_name, foreground="#2E86AB")
                except RuntimeError as e:
                    if "main thread is not in main loop" in str(e):
                        return  # 主线程已结束，静默忽略
                    else:
                        raise
        except Exception as e:
            self.log(f"更新当前账号状态失败: {str(e)}")

    def create_content_panel(self, parent):
        """创建右侧内容面板"""
        content_frame = ttk.Frame(parent)
        content_frame.pack(fill=tk.BOTH, expand=True)

        # 创建现代化标签页
        self.create_modern_notebook(content_frame)

    def create_modern_notebook(self, parent):
        """创建现代化标签页"""
        # 创建标签页容器
        notebook_container = ttk.Frame(parent)
        notebook_container.pack(fill=tk.BOTH, expand=True)

        # 创建标签页控件
        self.notebook = ttk.Notebook(notebook_container)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # 设置标签页样式
        style = ttk.Style()
        style.configure("Modern.TNotebook.Tab", font=("微软雅黑", 10))
        self.notebook.configure(style="Modern.TNotebook")

        # 创建账号管理标签页
        accounts_tab = ttk.Frame(self.notebook)
        self.notebook.add(accounts_tab, text="👥 账号管理")
        self.create_modern_accounts_tab(accounts_tab)

        # 创建日志标签页
        log_tab = ttk.Frame(self.notebook)
        self.notebook.add(log_tab, text="📝 运行日志")
        self.log_manager.create_log_tab(log_tab)

        # 设置主UI的log_text为LogManager的log_text
        self.log_text = self.log_manager.log_text

        # 创建定时任务标签页
        if hasattr(self, 'scheduler_manager') and self.scheduler_manager:
            try:
                self.scheduler_manager.create_scheduler_tab(self.notebook)
                print("✅ 定时任务标签页创建完成")
            except Exception as e:
                print(f"❌ 定时任务标签页创建失败: {e}")

        # 创建设置标签页
        settings_tab = ttk.Frame(self.notebook)
        self.notebook.add(settings_tab, text="⚙️ 系统设置")
        self.create_modern_settings_tab(settings_tab)

    def create_modern_accounts_tab(self, parent):
        """创建现代化账号管理标签页"""
        # 创建主容器
        main_container = ttk.Frame(parent)
        main_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # 创建工具栏
        self.create_accounts_toolbar(main_container)

        # 创建账号表格（使用AccountsTable组件）
        self.accounts_table = AccountsTable(
            main_container,
            self.config_manager,
            callbacks=self.account_controller.get_callback_dict(),
            data_query_manager=self.data_query_manager,
            current_platform=self.current_platform,
            account_data=self.account_data,
            log_callback=self.log
        )
        # 传递账号管理器引用，以便登录功能正常工作
        self.accounts_table.account_manager = self.account_manager
        self.accounts_table.pack(fill=tk.BOTH, expand=True)

        # 设置UI组件引用
        self.account_controller.set_ui_component('accounts_table', self.accounts_table)
        self.account_controller.set_ui_component('main_ui', self)

    def create_accounts_toolbar(self, parent):
        """创建现代化账号管理工具栏"""
        toolbar_frame = ttk.Frame(parent)
        toolbar_frame.pack(fill=tk.X, pady=(0, 15))

        # 工具栏标题和主要操作按钮
        title_actions_frame = ttk.Frame(toolbar_frame)
        title_actions_frame.pack(fill=tk.X, pady=(0, 10))



        # 右侧：主要操作按钮
        actions_frame = ttk.Frame(title_actions_frame)
        actions_frame.pack(side=tk.RIGHT)

        # 开始存稿按钮 - 使用更突出的样式
        self.start_button = ttk.Button(
            actions_frame,
            text="▶️ 开始存稿",
            command=self.start_task,
            style="Accent.TButton"  # 改为强调样式，更突出
        )
        self.start_button.pack(side=tk.LEFT, padx=(0, 8))



        # 停止任务按钮 - 保持危险样式
        self.stop_button = ttk.Button(
            actions_frame,
            text="⏹️ 停止任务",
            command=self.stop_task,
            style="Danger.TButton",
            state="disabled"
        )
        self.stop_button.pack(side=tk.LEFT, padx=(0, 12))

        # 为了兼容性，创建别名引用（指向同一个按钮对象）
        self.start_button_main = self.start_button
        self.stop_button_main = self.stop_button

        # 添加分隔线
        separator = ttk.Separator(actions_frame, orient='vertical')
        separator.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 12))

        # 存稿设置按钮 - 使用中等样式，更突出
        self.settings_button_main = ttk.Button(
            actions_frame,
            text="📝 存稿设置",
            command=self.show_draft_settings,
            style="Medium.TButton"  # 改为中等样式
        )
        self.settings_button_main.pack(side=tk.LEFT, padx=(0, 8))



        # 账号验证按钮 - 验证所有账号的登录状态
        self.cookie_validation_button_main = ttk.Button(
            actions_frame,
            text="🔍 账号验证",
            command=self.validate_all_cookies,
            style="Medium.TButton"
        )
        self.cookie_validation_button_main.pack(side=tk.LEFT, padx=(0, 5))

        # 缓存清理按钮 - 使用中等样式，确保可见性
        self.cache_cleaner_button_main = ttk.Button(
            actions_frame,
            text="🧹 缓存清理",
            command=self.show_cache_cleaner,
            style="Medium.TButton"  # 改为中等样式，提高可见性
        )
        self.cache_cleaner_button_main.pack(side=tk.LEFT, padx=(0, 5))

        # 工具栏已移除，所有功能已整合到表格标题栏

    def refresh_account_list(self):
        """刷新账号列表"""
        try:
            self.log("正在刷新账号列表...")
            # 重新加载账号数据
            self.load_accounts()
            # 更新表格显示
            self.update_account_tree()
            self.log("账号列表刷新完成")
        except Exception as e:
            self.log(f"刷新账号列表失败: {str(e)}")

    def open_account_directory(self):
        """打开账号目录"""
        try:
            import subprocess
            import os

            # 获取当前平台的账号目录
            account_dir = self.account_dir.get()

            if not account_dir:
                messagebox.showwarning("警告", "账号目录路径未设置")
                return

            if not os.path.exists(account_dir):
                # 如果目录不存在，询问是否创建
                if messagebox.askyesno("目录不存在", f"账号目录不存在：\n{account_dir}\n\n是否创建该目录？"):
                    try:
                        os.makedirs(account_dir, exist_ok=True)
                        self.log(f"✅ 已创建账号目录: {account_dir}")
                    except Exception as e:
                        messagebox.showerror("创建失败", f"创建目录失败：\n{str(e)}")
                        return
                else:
                    return

            # 在Windows资源管理器中打开目录
            if os.name == 'nt':  # Windows
                # 将路径转换为Windows格式（使用反斜杠）
                windows_path = os.path.normpath(account_dir)
                # 使用 os.startfile 更可靠，不会因为退出码问题报错
                os.startfile(windows_path)
                self.log(f"📁 已打开账号目录: {windows_path}")
            else:  # Linux/Mac
                subprocess.run(['xdg-open', account_dir], check=False)
                self.log(f"📁 已打开账号目录: {account_dir}")

        except Exception as e:
            self.log(f"❌ 打开账号目录失败: {str(e)}")
            messagebox.showerror("错误", f"打开账号目录失败：\n{str(e)}")





    def search_log(self):
        """搜索日志内容 - 委托给日志管理器"""
        if self.log_manager:
            self.log_manager.search_log()
        else:
            self.log("❌ 日志管理器未初始化")
            messagebox.showerror("错误", "日志搜索功能不可用")

    def show_log_settings(self):
        """显示日志配置对话框 - 委托给日志管理器"""
        if self.log_manager:
            self.log_manager.show_log_settings()
        else:
            self.log("❌ 日志管理器未初始化")
            messagebox.showerror("错误", "日志配置功能不可用")




    def save_log_to_file(self):
        """保存日志到文件 - 委托给日志管理器"""
        if self.log_manager:
            self.log_manager.save_log_to_file()
        else:
            self.log("❌ 日志管理器未初始化")
            messagebox.showerror("错误", "保存日志功能不可用")

    def refresh_log_display(self):
        """刷新日志显示 - 委托给日志管理器"""
        if self.log_manager:
            self.log_manager.refresh_log_display()
        else:
            self.log("❌ 日志管理器未初始化")
            messagebox.showerror("错误", "刷新日志功能不可用")

    def show_log_statistics(self):
        """显示日志统计信息 - 委托给日志管理器"""
        if self.log_manager:
            self.log_manager.show_log_statistics()
        else:
            self.log("❌ 日志管理器未初始化")
            messagebox.showerror("错误", "日志统计功能不可用")

    def show_advanced_log_search(self):
        """显示高级日志搜索 - 委托给日志管理器"""
        if self.log_manager:
            self.log_manager.show_advanced_log_search()
        else:
            self.log("❌ 日志管理器未初始化")
            messagebox.showerror("错误", "高级日志搜索功能不可用")

    def reset_log_settings(self):
        """重置日志配置 - 委托给日志管理器"""
        if self.log_manager:
            self.log_manager.reset_log_settings()
        else:
            self.log("❌ 日志管理器未初始化")
            messagebox.showerror("错误", "重置日志配置功能不可用")

    def show_log_help(self):
        """显示日志配置帮助 - 委托给日志管理器"""
        if self.log_manager:
            self.log_manager.show_log_help()
        else:
            self.log("❌ 日志管理器未初始化")
            messagebox.showerror("错误", "日志帮助功能不可用")

    def copy_log(self):
        """复制日志内容到剪贴板 - 委托给日志管理器"""
        if self.log_manager:
            self.log_manager.copy_log()
        else:
            self.log("❌ 日志管理器未初始化")
            messagebox.showerror("错误", "复制日志功能不可用")

    def create_modern_settings_tab(self, parent):
        """创建现代化设置标签页 - 委托给设置管理器"""
        if self.settings_manager:
            self.settings_manager.create_settings_tab(parent)
        else:
            self.log("❌ 设置管理器未初始化")
            # 创建简单的错误提示
            error_frame = ttk.Frame(parent)
            error_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
            ttk.Label(error_frame, text="❌ 设置功能不可用", font=("微软雅黑", 12)).pack()

    def create_modern_status_bar(self, parent):
        """创建现代化状态栏"""
        status_container = ttk.Frame(parent)
        status_container.pack(side=tk.BOTTOM, fill=tk.X, padx=15, pady=(0, 10))

        # 状态栏背景
        status_frame = ttk.Frame(status_container)
        status_frame.pack(fill=tk.X)

        # 左侧状态信息
        left_status = ttk.Frame(status_frame)
        left_status.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 状态文本
        self.status_text = ttk.Label(
            left_status,
            text="🟢 系统就绪",
            font=("微软雅黑", 9),
            foreground="#28A745"
        )
        self.status_text.pack(side=tk.LEFT, padx=5)

        # 右侧版本信息
        right_status = ttk.Frame(status_frame)
        right_status.pack(side=tk.RIGHT)

        # 版本信息
        version_label = ttk.Label(
            right_status,
            text="v2.0.0 现代化版本",
            font=("微软雅黑", 9),
            foreground="#6C757D"
        )
        version_label.pack(side=tk.RIGHT, padx=5)

    def create_modern_dir_settings(self, parent):
        """创建现代化目录设置 - 委托给设置管理器"""
        if self.settings_manager:
            self.settings_manager.create_modern_dir_settings(parent)
        else:
            self.log("❌ 设置管理器未初始化")
            # 创建简单的错误提示
            error_frame = ttk.Frame(parent)
            error_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
            ttk.Label(error_frame, text="❌ 目录设置功能不可用", font=("微软雅黑", 12)).pack()



    def create_platform_sidebar(self, parent):
        """
        创建左侧平台导航侧边栏

        Args:
            parent: 父容器
        """
        # 创建左侧平台导航侧边栏框架
        sidebar_frame = ttk.LabelFrame(parent, text="平台导航", padding=10)
        sidebar_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10), pady=5)

        # 导入平台侧边栏类
        from 网易号存稿.ui.components.platform_sidebar import PlatformSidebar

        # 创建平台侧边栏
        self.platform_sidebar = PlatformSidebar(
            sidebar_frame,
            self.config_manager,
            self.log,
            self.on_platform_change
        )
        # 设置主程序UI引用
        self.platform_sidebar.main_ui = self

    def on_platform_change(self, platform):
        """
        平台切换回调函数

        Args:
            platform: 平台标识
        """
        # 在切换平台前，先保存当前平台的设置和存稿详情数据，避免丢失手动修改的设置
        if hasattr(self, 'current_platform') and self.current_platform != platform:
            self.log(f"平台切换前保存 {self.current_platform} 的设置和存稿详情数据")
            self.save_config(silent=True)  # 静默保存，不显示弹窗
            # 保存当前平台的存稿详情数据
            if hasattr(self, 'draft_task_manager') and self.draft_task_manager:
                self.draft_task_manager.save_draft_details()

        # 更新当前平台
        self.current_platform = platform

        # 获取平台名称
        platform_name = self.config_manager.get_platform_name(platform)

        # 更新平台标签
        self.update_platform_label(platform_name)

        # 更新目录变量
        self.update_directory_variables()

        # 更新账号管理器（包含账号加载）
        self.update_account_manager()

        # 更新存稿处理器配置（从新平台的配置中加载）
        self.update_processor_config()



        # 更新统计信息面板
        self.update_stats_panel()

        # 更新HeaderBar中的平台显示
        if hasattr(self, 'header_bar'):
            self.header_bar.refresh_platform_display()

        # 记录日志
        self.log(f"已切换到平台: {platform_name}")

        # 保存当前平台到配置
        self.config_manager.set_current_platform(platform)

        # 重新初始化平台处理器
        self.init_platform_processors()

        # 更新UI显示（包含账号表格刷新）
        self.update_account_tree()



        # 保存配置 - 平台切换时使用静默保存
        self.save_config(silent=True)

    def switch_platform(self, platform: str):
        """
        切换平台（别名方法，用于兼容性）
        Args:
            platform: 平台标识
        """
        self.on_platform_change(platform)

    def _get_data_file_path(self):
        """获取当前平台的数据文件路径"""
        # 所有平台都使用各自的配置目录
        target_dir = self.config_manager.get("account_dir", "", platform=self.current_platform)

        self.log(f"🔍 [调试] 当前平台: {self.current_platform}")
        self.log(f"🔍 [调试] 配置的账号目录: {target_dir}")

        if target_dir:
            # 标准化路径（处理混合的斜杠）
            target_dir = os.path.normpath(target_dir)
            self.log(f"🔍 [调试] 标准化后的目录: {target_dir}")

            # 检查目录是否存在
            if not os.path.exists(target_dir):
                self.log(f"⚠️ 目录不存在，尝试创建: {target_dir}")
                try:
                    os.makedirs(target_dir, exist_ok=True)
                    self.log(f"✅ 成功创建目录: {target_dir}")
                except Exception as e:
                    self.log(f"❌ 创建目录失败: {e}")
                    # 备用：使用项目根目录
                    data_file = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), f"{self.current_platform}_account_data.json")
                    self.log(f"⚠️ 使用备用路径: {data_file}")
                    return data_file

            # 使用配置的平台目录
            data_file = os.path.join(target_dir, f"{self.current_platform}_account_data.json")
            self.log(f"✅ 使用数据文件路径: {data_file}")
        else:
            # 没有配置目录：使用项目根目录
            data_file = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), f"{self.current_platform}_account_data.json")
            self.log(f"⚠️ 未配置目录，使用项目根目录: {data_file}")

        return data_file



    def update_account_manager(self):
        """更新账号管理器和账号数据处理器"""
        self.log(f"正在更新账号管理器，当前平台: {self.current_platform}")
        self.log(f"账号目录: {self.account_dir.get()}")

        # 更新账号管理器
        if hasattr(self, 'account_manager'):
            self.account_manager.set_platform(self.current_platform)
            self.account_manager.set_account_dir(self.account_dir.get())
            # 手动加载账号列表
            self.accounts = self.account_manager.load_accounts()
            self.log(f"已加载 {len(self.accounts)} 个{self.current_platform}平台账号")

            # 更新账号数量显示
            self.update_account_count_display()

            # 更新accounts_table的account_manager引用和当前平台
            if hasattr(self, 'accounts_table') and self.accounts_table:
                self.accounts_table.account_manager = self.account_manager
                self.accounts_table.current_platform = self.current_platform

        # 更新账号数据处理器（只在需要时重新创建）
        if hasattr(self, 'account_data'):
            data_file = self._get_data_file_path()

            # 只有当数据文件路径改变时才重新创建
            if not hasattr(self.account_data, 'data_file') or self.account_data.data_file != data_file:
                self.account_data = AccountData(data_file, self.log)
                self.log(f"账号数据文件路径: {data_file}")

                # 更新数据查询管理器的AccountData引用
                if hasattr(self, 'data_query_manager') and self.data_query_manager:
                    self.data_query_manager.account_data = self.account_data
                    self.log(f"已更新数据查询管理器的AccountData引用")

                # 更新Excel导出管理器的AccountData引用
                if hasattr(self, 'excel_export_manager') and self.excel_export_manager:
                    self.excel_export_manager.account_data = self.account_data
                    self.log(f"已更新Excel导出管理器的AccountData引用")
            else:
                # 即使路径没变，也要重新加载数据以确保获取最新内容
                try:
                    self.account_data.load_data()
                    self.log(f"已重新加载账号数据: {len(self.account_data.account_data)} 条记录")
                except Exception as e:
                    self.log(f"重新加载账号数据时出错: {e}")

        # 重新加载新平台的存稿详情数据
        self.load_draft_details()

    def update_platform_label(self, platform_name):
        """
        更新平台标签

        Args:
            platform_name: 平台显示名称
        """
        if hasattr(self, 'platform_label'):
            self.platform_label.config(text=platform_name)

    def update_directory_variables(self):
        """更新目录变量，从当前平台配置中获取"""
        # 临时标记为平台切换状态，避免触发弹窗
        self._platform_switching = True

        try:
            # 通过SettingsManager更新目录变量
            if self.settings_manager:
                self.settings_manager._load_settings()

            # 同步更新主UI中的目录变量（用于兼容性）
            self.account_dir.set(self.config_manager.get("account_dir", "", platform=self.current_platform))
            self.video_dir.set(self.config_manager.get("video_dir", "", platform=self.current_platform))
            self.cover_dir.set(self.config_manager.get("cover_dir", "", platform=self.current_platform))
            self.processed_dir.set(self.config_manager.get("processed_dir", "", platform=self.current_platform))
            self.processed_covers_dir.set(self.config_manager.get("processed_covers_dir", "", platform=self.current_platform))
            self.violation_dir.set(self.config_manager.get("violation_dir", "", platform=self.current_platform))
            self.screenshots_dir.set(self.config_manager.get("screenshots_dir", "", platform=self.current_platform))

            # 确保目录存在
            self.create_required_dirs()

        finally:
            # 清除平台切换状态
            self._platform_switching = False

    def init_platform_processors(self):
        """初始化平台处理器"""
        # 根据当前平台初始化对应的处理器
        if self.current_platform == "netease":
            # 网易平台
            from 网易号存稿.draft.processor import DraftProcessor
            self.draft_processor = DraftProcessor(
                self.account_dir.get(),
                self.processed_dir.get(),
                self.processed_covers_dir.get(),
                self.archive_completed.get(),
                self.headless_mode.get(),
                self.draft_limit.get(),
                self.loop_limit.get(),
                self.log,
                self.screenshots_dir.get(),
                self.random_video_allocation.get(),
                self.add_draft_detail  # 传递存稿详情更新回调函数
            )
        elif self.current_platform == "toutiao":
            # 今日头条平台
            from 网易号存稿.platforms.toutiao.processor import ToutiaoDraftProcessor
            self.draft_processor = ToutiaoDraftProcessor(
                account_dir=self.account_dir.get(),
                processed_dir=self.processed_dir.get(),
                processed_covers_dir=self.processed_covers_dir.get(),
                screenshots_dir=self.screenshots_dir.get(),
                log_callback=self.log
            )

            # 设置今日头条平台处理器的其他属性
            self.draft_processor.archive_completed = self.archive_completed.get()
            self.draft_processor.headless_mode = self.headless_mode.get()
            self.draft_processor.draft_limit = self.draft_limit.get()
            self.draft_processor.loop_limit = self.loop_limit.get()
            self.draft_processor.random_video_allocation = self.random_video_allocation.get()
        elif self.current_platform == "dayu":
            # 大鱼号平台
            from 网易号存稿.platforms.dayu import DayuDraftProcessor
            self.draft_processor = DayuDraftProcessor(
                account_dir=self.account_dir.get(),
                processed_dir=self.processed_dir.get(),
                processed_covers_dir=self.processed_covers_dir.get(),
                archive_completed=self.archive_completed.get(),
                headless_mode=self.headless_mode.get(),
                draft_limit=self.draft_limit.get(),
                loop_limit=self.loop_limit.get(),
                log_callback=self.log,
                screenshots_dir=self.screenshots_dir.get(),
                random_video_allocation=self.random_video_allocation.get(),
                add_draft_detail_callback=self.add_draft_detail
            )
        else:
            # 默认使用网易平台
            from 网易号存稿.draft.processor import DraftProcessor
            self.draft_processor = DraftProcessor(
                self.account_dir.get(),
                self.processed_dir.get(),
                self.processed_covers_dir.get(),
                self.archive_completed.get(),
                self.headless_mode.get(),
                self.draft_limit.get(),
                self.loop_limit.get(),
                self.log,
                self.screenshots_dir.get(),
                self.random_video_allocation.get(),
                self.add_draft_detail  # 传递存稿详情更新回调函数
            )

    def update_processor_config(self):
        """更新存稿处理器配置，从当前平台配置中获取"""
        # 更新运行选项
        self.headless_mode.set(self.config_manager.get("headless_mode", False, platform=self.current_platform))
        self.archive_completed.set(self.config_manager.get("archive_completed", True, platform=self.current_platform))
        self.draft_limit.set(self.config_manager.get("draft_limit", 0, platform=self.current_platform))
        self.loop_limit.set(self.config_manager.get("loop_limit", 0, platform=self.current_platform))
        self.concurrent_accounts.set(self.config_manager.get("concurrent_accounts", 1, platform=self.current_platform))
        self.random_video_allocation.set(self.config_manager.get("random_video_allocation", True, platform=self.current_platform))

        # 更新数据查询选项
        self.query_headless_mode.set(self.config_manager.get("query_headless_mode", False, platform=self.current_platform))
        self.query_max_threads.set(self.config_manager.get("query_max_threads", 10, platform=self.current_platform))

        # 更新浏览器运行选项
        self.auto_close.set(self.config_manager.get("auto_close", True, platform=self.current_platform))
        self.concurrent_mode.set(self.config_manager.get("concurrent_mode", False, platform=self.current_platform))
        self.max_workers.set(self.config_manager.get("max_workers", 3, platform=self.current_platform))

        # 重新初始化平台处理器
        self.init_platform_processors()

        # 更新并发管理器配置
        if hasattr(self, 'concurrent_manager'):
            self.concurrent_manager.account_dir = self.account_dir.get()
            self.concurrent_manager.processed_dir = self.processed_dir.get()
            self.concurrent_manager.processed_covers_dir = self.processed_covers_dir.get()
            self.concurrent_manager.archive_completed = self.archive_completed.get()
            self.concurrent_manager.headless_mode = self.headless_mode.get()
            self.concurrent_manager.draft_limit = self.draft_limit.get()
            self.concurrent_manager.loop_limit = self.loop_limit.get()
            self.concurrent_manager.screenshots_dir = self.screenshots_dir.get()







    def show_draft_settings(self):
        """显示存稿设置对话框 - 委托给设置管理器"""
        if self.settings_manager:
            self.settings_manager.show_draft_settings()
        else:
            self.log("❌ 设置管理器未初始化")
            messagebox.showerror("错误", "设置功能不可用")





    def show_progress_dialog(self):
        """显示进度条对话框"""
        from 网易号存稿.ui.dialogs.progress_dialog import ProgressDialog

        # 记录日志
        self.log("打开进度条对话框")

        # 创建进度条对话框
        progress_dialog = ProgressDialog(
            self.root,
            account_progress=self.account_progress,
            log_callback=self.log,
            config_manager=self.config_manager
        )

        # 等待对话框关闭
        self.root.wait_window(progress_dialog.dialog)

        # 记录日志
        self.log("已关闭进度条对话框")





    def start_log_handler(self):
        """启动日志处理线程"""
        # 初始化日志处理线程运行标志
        self.log_handler_running = True

        def log_handler():
            while self.log_handler_running:
                try:
                    # 从队列获取日志消息
                    message = self.log_queue.get(block=True, timeout=0.1)

                    # 检查线程是否应该停止
                    if not self.log_handler_running:
                        break

                    # 在UI线程中更新日志文本框，添加线程安全检查
                    try:
                        self.root.after(0, self._update_log_text, message)
                    except RuntimeError as e:
                        if "main thread is not in main loop" in str(e):
                            # 主线程已结束，停止日志处理
                            self.log_handler_running = False
                            break
                        else:
                            raise

                    # 标记任务完成
                    self.log_queue.task_done()

                except queue.Empty:
                    # 队列为空，继续等待
                    continue

                except Exception as e:
                    print(f"日志处理异常: {str(e)}")
                    # 如果是主线程结束导致的异常，停止日志处理
                    if "main thread is not in main loop" in str(e):
                        self.log_handler_running = False
                        break

        # 启动日志处理线程
        threading.Thread(target=log_handler, daemon=True).start()

    def _update_log_text(self, log_entry):
        """
        更新日志文本框

        Args:
            log_entry: 日志条目，可以是字符串或字典
        """
        try:
            # 启用文本框编辑
            self.log_text.configure(state=tk.NORMAL)

            # 如果是字符串，转换为日志条目字典
            if isinstance(log_entry, str):
                message = log_entry
                color = "#000000"  # 默认黑色
            else:
                message = log_entry["text"]
                color = log_entry["color"]

            # 检查是否需要合并存稿成功相关消息
            should_merge, merged_message = self._check_and_merge_draft_messages(message)
            if should_merge:
                # 如果需要合并，使用合并后的消息替换原消息
                message = merged_message

            # 添加消息到文本框
            self.log_text.insert(tk.END, message + "\n")

            # 创建颜色标签
            tag_name = f"color_{color.replace('#', '')}"
            if not tag_name in self.log_text.tag_names():
                self.log_text.tag_configure(tag_name, foreground=color)

            # 获取最后一行的索引
            last_line_start = self.log_text.index(f"end-2c linestart")
            last_line_end = self.log_text.index(f"end-1c")

            # 应用颜色标签
            self.log_text.tag_add(tag_name, last_line_start, last_line_end)

            # 滚动到底部
            self.log_text.see(tk.END)

            # 禁用文本框编辑
            self.log_text.configure(state=tk.DISABLED)
        except RuntimeError as e:
            if "main thread is not in main loop" in str(e):
                return  # 主线程已结束，静默忽略
            else:
                raise
        except Exception:
            return  # 忽略其他UI相关错误

    def _check_and_merge_draft_messages(self, message):
        """
        检查并合并存稿成功相关消息

        Args:
            message: 日志消息

        Returns:
            tuple: (是否需要合并, 合并后的消息)
        """
        # 检查是否是存稿成功相关消息
        if "本次成功存稿:" in message or "从processor获取总存稿成功数量:" in message or "存稿成功数量更新:" in message or "进度数据已更新:" in message or "成功存稿" in message and "个视频" in message:
            # 提取账号名称
            import re
            account_match = re.search(r'账号\s+(\d+)', message)
            if account_match:
                account = account_match.group(1)

                # 提取存稿数量
                count_match = re.search(r'(\d+)(?:\s+个视频|存稿成功数量更新:\s*\d+\s*->\s*(\d+)|从processor获取总存稿成功数量:\s*(\d+))', message)
                if count_match:
                    # 确定存稿数量
                    count = count_match.group(1)
                    if count_match.group(2):  # 如果是"存稿成功数量更新"格式
                        count = count_match.group(2)
                    elif count_match.group(3):  # 如果是"从processor获取总存稿成功数量"格式
                        count = count_match.group(3)

                    # 检查是否是最后一条相关消息（成功存稿 X 个视频）
                    if "成功存稿" in message and "个视频" in message:
                        # 生成合并后的消息
                        timestamp_match = re.search(r'\[([\d-]+\s[\d:]+)\]', message)
                        timestamp = timestamp_match.group(1) if timestamp_match else ""

                        merged_message = f"[{timestamp}] 账号 {account} 存稿任务完成，成功存稿 {count} 个视频"
                        return True, merged_message

                    # 如果不是最后一条消息，则跳过不显示
                    return True, ""

        # 不需要合并
        return False, message

    def log(self, message, level="INFO"):
        """
        记录日志 - 统一使用LogManager处理

        Args:
            message: 日志消息或日志条目字典
            level: 日志级别，默认为INFO
        """
        # 直接使用LogManager处理日志，避免重复的日志系统
        if hasattr(self, 'log_manager') and self.log_manager:
            # 如果message是字典格式，提取消息和级别
            if isinstance(message, dict):
                msg = message.get('text', str(message))
                log_level = message.get('level', level)
            else:
                msg = str(message)
                log_level = level

            # 使用LogManager记录日志
            self.log_manager.log(msg, level)

            # 解析日志消息中的统计信息
            self.parse_and_update_stats_from_log(msg)
        else:
            # 如果LogManager不可用，打印到控制台
            print(message)

    def clear_log(self):
        """清空日志 - 委托给日志管理器"""
        if self.log_manager:
            self.log_manager.clear_log()
        else:
            self.log("❌ 日志管理器未初始化")
            messagebox.showerror("错误", "清空日志功能不可用")

# save_log方法已移除，使用save_log_to_file委托给日志管理器

    def load_accounts(self):
        """加载账号列表"""
        # 更新账号目录
        self.account_manager.account_dir = self.account_dir.get()

        # 加载账号
        self.accounts = self.account_manager.load_accounts()
        self.log(f"已加载 {len(self.accounts)} 个账号")

        # 更新账号数量显示
        self.update_account_count_display()

        # 更新账号列表
        self.update_account_tree()

    def update_account_count_display(self):
        """更新所有账号数量显示"""
        account_count = len(self.accounts)

        # 更新左侧统计面板的账号数量
        if hasattr(self, 'account_count_label'):
            self.account_count_label.config(text=str(account_count))

        # 更新表格标题中的账号数量（已移除，保留兼容性）
        if hasattr(self, 'table_account_count_label'):
            self.table_account_count_label.config(text=str(account_count))

        # 更新NavigationPanel中的账号数量显示
        if hasattr(self, 'navigation_panel') and self.navigation_panel:
            self.navigation_panel.update_account_count(account_count)

    def _start_refresh_worker(self):
        """启动串行刷新工作线程"""
        if self._refresh_thread is None or not self._refresh_thread.is_alive():
            self._refresh_running = True
            self._refresh_thread = threading.Thread(target=self._refresh_worker, daemon=True)
            self._refresh_thread.start()

    def _refresh_worker(self):
        """串行刷新工作线程"""
        while self._refresh_running:
            try:
                # 从队列中获取刷新任务，超时1秒
                refresh_task = self._refresh_queue.get(timeout=1.0)

                if refresh_task is None:  # 停止信号
                    break

                # 执行刷新任务
                task_type, data = refresh_task

                if task_type == "account_update":
                    self._process_account_update(data)
                elif task_type == "viewer_refresh":
                    self._process_viewer_refresh(data)

                # 标记任务完成
                self._refresh_queue.task_done()

            except queue.Empty:
                continue
            except Exception as e:
                self.log(f"❌ 刷新工作线程错误: {str(e)}")
                continue

    def _process_account_update(self, data):
        """处理单个账号数据更新"""
        account, result = data

        with self._refresh_lock:
            try:
                # 更新账号数据
                if hasattr(self, 'account_data') and self.account_data:
                    update_success = self.account_data.update_account_data(account, result)
                    if update_success:
                        # 在主线程中执行增量更新（不需要保存/恢复状态）
                        self.root.after(0, lambda: self._incremental_update_account(account, result))
                        self.log(f"✅ {account} 数据已更新")
                    else:
                        self.log(f"❌ {account} 数据更新失败")

            except Exception as e:
                self.log(f"❌ 处理账号更新时出错: {str(e)}")

    def _process_viewer_refresh(self, data):
        """处理数据查看器刷新"""
        try:
            # 在主线程中通知数据查看器刷新
            self.root.after(0, self._notify_viewers_refresh)
        except Exception as e:
            self.log(f"❌ 处理查看器刷新时出错: {str(e)}")

    def queue_account_update(self, account: str, result: dict):
        """将账号更新任务加入队列"""
        if not self._refresh_running:
            self._start_refresh_worker()

        # 添加到待更新集合，用于防抖
        self._pending_updates.add(account)

        # 取消之前的防抖定时器
        if self._debounce_timer:
            self.root.after_cancel(self._debounce_timer)

        # 设置新的防抖定时器
        self._debounce_timer = self.root.after(100, lambda: self._flush_pending_updates())

        # 立即将任务加入队列
        self._refresh_queue.put(("account_update", (account, result)))

    def _flush_pending_updates(self):
        """刷新待更新的账号"""
        if self._pending_updates:
            # 清空待更新集合
            self._pending_updates.clear()

            # 延迟通知数据查看器刷新，避免频繁刷新
            self.root.after(200, lambda: self._refresh_queue.put(("viewer_refresh", None)))



    def update_account_tree(self):
        """更新账号列表树"""
        # 更新账号列表，减少日志输出

        # 确保AccountData实例使用最新的数据文件
        if hasattr(self, 'account_data'):
            try:
                # 获取当前平台的正确数据文件路径
                current_data_file = self._get_data_file_path()
                self.log(f"🔍 [调试] 更新账号树 - 当前平台: {self.current_platform}")
                self.log(f"🔍 [调试] 更新账号树 - 数据文件路径: {current_data_file}")
                self.log(f"🔍 [调试] 更新账号树 - 文件是否存在: {os.path.exists(current_data_file)}")

                # 如果数据文件路径发生变化，重新创建AccountData对象
                if self.account_data.data_file != current_data_file:
                    from 网易号存稿.account.data import AccountData
                    self.account_data = AccountData(current_data_file, self.log)
                    self.log(f"已切换到数据文件: {current_data_file}")

                    # 更新数据查询管理器的AccountData引用，确保数据同步
                    if hasattr(self, 'data_query_manager') and self.data_query_manager:
                        self.data_query_manager.account_data = self.account_data
                        self.log("✅ 已更新数据查询管理器的AccountData引用")
                else:
                    # 重新加载数据以确保获取最新内容
                    self.account_data.load_data()

                # 显示加载的数据统计
                if hasattr(self.account_data, 'data') and self.account_data.data:
                    self.log(f"🔍 [调试] 加载的账号数据包含 {len(self.account_data.data)} 个账号")
                else:
                    self.log(f"🔍 [调试] 账号数据为空或未加载")

            except Exception as e:
                self.log(f"重新加载账号数据时出错: {e}")

        # 保存当前表格状态
        self._save_table_state()

        # 使用搜索过滤功能来更新表格
        self.filter_main_accounts()

        # 恢复表格状态
        self.root.after(100, self._restore_table_state)

    def _save_table_state(self):
        """保存表格当前状态"""
        try:
            account_tree = None
            if hasattr(self, 'accounts_table') and self.accounts_table:
                account_tree = self.accounts_table.account_tree
            elif hasattr(self, 'account_tree'):
                account_tree = self.account_tree

            if account_tree:
                # 保存选中项的账号名（而不是item ID）
                self._selected_accounts = []
                for item in account_tree.selection():
                    try:
                        values = account_tree.item(item, 'values')
                        if values and len(values) > 1:  # 确保有账号名列
                            account_name = values[1]  # 账号名在第二列
                            if account_name:
                                self._selected_accounts.append(account_name)
                    except:
                        pass

                # 保存滚动位置
                try:
                    self._current_scroll_position = account_tree.yview()[0]
                except:
                    self._current_scroll_position = 0

                # 保存排序状态
                if hasattr(self.accounts_table, 'last_sort_column'):
                    self._last_sort_column = self.accounts_table.last_sort_column
                    self._last_sort_reverse = getattr(self.accounts_table, 'last_sort_reverse', False)

        except Exception as e:
            self.log(f"❌ 保存表格状态时出错: {str(e)}")

    def _restore_table_state(self):
        """恢复表格状态"""
        try:
            account_tree = None
            if hasattr(self, 'accounts_table') and self.accounts_table:
                account_tree = self.accounts_table.account_tree
            elif hasattr(self, 'account_tree'):
                account_tree = self.account_tree

            if account_tree:
                # 恢复选中项（基于账号名）
                if hasattr(self, '_selected_accounts') and self._selected_accounts:
                    try:
                        for item in account_tree.get_children():
                            values = account_tree.item(item, 'values')
                            if values and len(values) > 1:
                                account_name = values[1]  # 账号名在第二列
                                if account_name in self._selected_accounts:
                                    account_tree.selection_add(item)
                    except Exception as e:
                        self.log(f"❌ 恢复选中项时出错: {str(e)}")

                # 恢复滚动位置
                try:
                    if hasattr(self, '_current_scroll_position') and self._current_scroll_position > 0:
                        account_tree.yview_moveto(self._current_scroll_position)
                except Exception as e:
                    self.log(f"❌ 恢复滚动位置时出错: {str(e)}")

                # 恢复排序状态
                if (hasattr(self, '_last_sort_column') and self._last_sort_column and
                    hasattr(self.accounts_table, '_sort_table_by_column')):
                    try:
                        self.accounts_table.last_sort_column = self._last_sort_column
                        self.accounts_table.last_sort_reverse = getattr(self, '_last_sort_reverse', False)
                        self.accounts_table._sort_table_by_column(self._last_sort_column)
                    except Exception as e:
                        self.log(f"❌ 恢复排序状态时出错: {str(e)}")

        except Exception as e:
            self.log(f"❌ 恢复表格状态时出错: {str(e)}")

    def _incremental_update_account(self, account: str, result: dict):
        """增量更新单个账号的表格行"""
        try:
            account_tree = None
            if hasattr(self, 'accounts_table') and self.accounts_table:
                account_tree = self.accounts_table.account_tree
            elif hasattr(self, 'account_tree'):
                account_tree = self.account_tree

            if not account_tree:
                return

            # 查找对应的表格行
            updated = False
            for item in account_tree.get_children():
                values = account_tree.item(item, 'values')
                if values and len(values) > 1 and values[1] == account:  # 账号列现在是索引1（状态列是索引0）
                    # 更新这一行的数据
                    self._update_table_row(account_tree, item, result)
                    updated = True
                    break

            # 如果没有找到对应行，可能是新账号，需要重新加载表格
            if not updated:
                self.log(f"🔍 账号 {account} 在表格中未找到，重新加载表格")
                self.filter_main_accounts()
            else:
                self.log(f"✅ 账号 {account} 表格行已增量更新")
                # 增量更新不需要恢复表格状态，因为没有清空表格

        except Exception as e:
            self.log(f"❌ 增量更新账号 {account} 时出错: {str(e)}")
            # 出错时重新加载表格
            self.filter_main_accounts()

    def _update_table_row(self, account_tree, item, result: dict):
        """更新表格中的单行数据"""
        try:
            # 格式化数据的辅助函数
            def format_number(value):
                if isinstance(value, (int, float)):
                    return f"{value:,.0f}" if value >= 1000 else str(value)
                elif isinstance(value, str):
                    try:
                        num_value = float(value)
                        return f"{num_value:,.0f}" if num_value >= 1000 else str(num_value)
                    except:
                        return value
                return str(value)

            # 计算七日收益总和
            seven_day_income = 0
            if "七日收益" in result and isinstance(result["七日收益"], list):
                for day_data in result["七日收益"]:
                    if isinstance(day_data, dict) and "收益" in day_data:
                        try:
                            seven_day_income += float(day_data["收益"])
                        except:
                            pass
            elif "七日收益数据" in result:
                for income in result["七日收益数据"].values():
                    try:
                        seven_day_income += float(income)
                    except:
                        pass

            # 获取状态信息，与完整刷新逻辑保持一致
            account_name = result.get("账号", "")
            status = self._get_account_display_status(account_name, result)

            # 构建新的行数据 - 包含完整的16列数据
            # 列顺序：状态、账号、用户名、七日收益、总收益、昨日收益、总提现、待提现、最近提现日、最近提现金额、更新时间、总播放、昨日播放、总粉丝、昨日粉丝、草稿箱
            new_values = (
                "",  # 状态列，稍后设置
                result.get("账号", ""),
                result.get("用户名", ""),
                format_number(seven_day_income),
                format_number(result.get("总收益", result.get("累计收益", "0"))),
                format_number(result.get("昨日收益", "0")),
                format_number(result.get("总提现", "0")),
                format_number(result.get("待提现", result.get("可提现", "0"))),
                result.get("最近提现日", ""),
                format_number(result.get("最近提现金额", "0")),
                result.get("更新时间", ""),
                format_number(result.get("总播放", result.get("累计播放", "0"))),
                format_number(result.get("昨日播放", "0")),
                format_number(result.get("总粉丝", "0")),
                format_number(result.get("昨日粉丝", "0")),
                format_number(result.get("草稿箱", result.get("草稿数", "0")))
            )

            # 先获取状态显示信息
            if hasattr(self.accounts_table, '_get_status_display'):
                status_text, status_tag = self.accounts_table._get_status_display(status)
                # 更新状态列的值
                new_values = list(new_values)
                new_values[0] = status_text
                new_values = tuple(new_values)

                # 更新行数据并同时设置标签
                account_tree.item(item, values=new_values, tags=(status_tag,))
            else:
                # 兼容性处理：先更新数据，再设置状态
                account_tree.item(item, values=new_values)
                # 更新状态列
                if hasattr(self.accounts_table, '_set_status_image'):
                    self.accounts_table._set_status_image(item, status)

            # 记录状态更新日志
            self.log(f"🔄 已更新账号 {account_name} 状态: {status}")

        except Exception as e:
            self.log(f"❌ 更新表格行时出错: {str(e)}")

    def _get_account_display_status(self, account_name: str, result: dict) -> str:
        """获取账号显示状态，与完整刷新逻辑保持一致"""
        try:
            # 获取账号管理器中的状态信息
            if hasattr(self, 'account_manager') and self.account_manager:
                account_status = self.account_manager.get_account_status(account_name)

                if account_status and account_status.get("status") != "未知":
                    # 使用账号管理器中的状态信息
                    return account_status.get("status", "正常")

            # 使用数据查询的状态
            return result.get("状态", "正常")

        except Exception as e:
            self.log(f"❌ 获取账号 {account_name} 显示状态时出错: {str(e)}")
            return result.get("状态", "未知")





    def _notify_viewers_refresh(self):
        """通知数据查看器刷新"""
        try:
            self.notify_data_viewers_refresh()
        except Exception as e:
            self.log(f"❌ 通知查看器刷新时出错: {str(e)}")

    def stop_refresh_worker(self):
        """停止刷新工作线程"""
        self._refresh_running = False
        if self._refresh_queue:
            self._refresh_queue.put(None)  # 发送停止信号
        if self._refresh_thread and self._refresh_thread.is_alive():
            self._refresh_thread.join(timeout=2.0)



    def on_account_select(self, event=None):
        """
        账号选择事件处理

        Args:
            event: 事件对象
        """
        # 获取账号表格
        account_tree = None
        if hasattr(self, 'accounts_table') and self.accounts_table:
            account_tree = self.accounts_table.account_tree

        if not account_tree:
            return

        # 获取选中的项目
        selected_items = account_tree.selection()
        if not selected_items:
            return

        # 获取选中的账号
        item = selected_items[0]
        values = account_tree.item(item, "values")
        if len(values) < 2:
            return

        account = values[1]  # 账号列的索引为1（状态列是索引0）

        # 设置选中的账号
        self.selected_account.set(account)

        # 更新统计信息面板中的当前账号显示
        self.update_stats_panel()

        self.log(f"已选择账号: {account}")

    def add_account(self):
        """
        添加账号

        注意：所有平台的账号都将以TXT格式保存（内容为JSON格式）
        文件命名格式：账号名.txt
        状态文件格式：账号名_status.json
        """
        # 根据当前平台选择不同的添加逻辑
        current_platform = self.current_platform

        if current_platform == "toutiao":
            self._add_toutiao_account()
        elif current_platform == "dayu":
            self._add_dayu_account()
        else:
            self._add_netease_account()

    def _add_netease_account(self):
        """添加网易号账号"""
        import threading

        # 先显示提示对话框（非阻塞）
        result = messagebox.askquestion("确认操作",
            "即将打开浏览器进行网易号登录操作。\n\n"
            "重要提示：\n"
            "1. 请不要手动关闭浏览器\n"
            "2. 登录完成后系统会自动处理\n"
            "3. 在登录过程中，程序界面保持响应\n\n"
            "是否继续？")

        if result != 'yes':
            self.log("用户取消添加账号操作")
            return

        # 在后台线程中执行登录过程，避免阻塞UI
        def login_thread():
            from 网易号存稿.account.login import AccountLogin
            from 网易号存稿.browser.driver import DriverManager

            driver_manager = None
            try:
                # 创建驱动管理器
                driver_manager = DriverManager(self.log)

                # 创建账号登录对象
                account_login = AccountLogin(driver_manager, self.log)

                # 使用手机号登录
                self.log("正在打开浏览器，请在浏览器中完成网易号登录...")
                self.log("提示：请不要手动关闭浏览器，系统会自动处理")

                login_success, driver, cookies = account_login.login_with_phone(False)

                # 在主线程中处理登录结果
                self.root.after(0, lambda: self._handle_netease_login_result(
                    login_success, driver, cookies, driver_manager))

            except Exception as e:
                self.log(f"添加账号过程中发生错误: {str(e)}")
                import traceback
                traceback.print_exc()

                # 在主线程中处理错误
                self.root.after(0, lambda: self._handle_netease_login_error(str(e), driver_manager))

        # 启动后台线程
        thread = threading.Thread(target=login_thread, daemon=True)
        thread.start()
        self.log("账号添加过程已在后台启动，界面保持响应...")

    def _handle_netease_login_result(self, login_success, driver, cookies, driver_manager):
        """在主线程中处理网易号登录结果"""
        try:
            if login_success and cookies:
                # 获取账号名称
                account_name = simpledialog.askstring("账号名称", "请输入账号名称（如手机号+备注）:")
                if not account_name:
                    self.log("取消添加账号")
                    if driver_manager and driver_manager.is_driver_alive():
                        driver_manager.close_driver()
                    return

                # 添加账号
                if self.account_manager.add_account(account_name, cookies):
                    self.log(f"账号 {account_name} 添加成功")

                    # 强制重新加载账号列表
                    self.account_manager.load_accounts()

                    # 更新UI
                    self.load_accounts()

                    # 选择新添加的账号
                    self.selected_account.set(account_name)
                    # 更新统计信息面板
                    self.update_stats_panel()

                    # 显示成功消息
                    messagebox.showinfo("添加成功", f"账号 {account_name} 添加成功")
                else:
                    self.log(f"账号 {account_name} 添加失败")
                    messagebox.showerror("添加失败", f"账号 {account_name} 添加失败")
            else:
                if driver_manager and not driver_manager.is_driver_alive():
                    self.log("浏览器已被手动关闭，无法完成登录")
                    messagebox.showwarning("登录取消", "浏览器已被关闭，登录操作已取消")
                else:
                    self.log("登录失败，无法添加账号")
                    messagebox.showerror("登录失败", "登录失败，无法添加账号")
        finally:
            # 关闭浏览器（如果仍然活动）
            if driver_manager and driver_manager.is_driver_alive():
                self.log("正在关闭浏览器...")
                driver_manager.close_driver()
            else:
                self.log("浏览器已关闭，无需额外操作")

    def _handle_netease_login_error(self, error_msg, driver_manager):
        """在主线程中处理网易号登录错误"""
        try:
            messagebox.showerror("添加账号失败", f"添加账号过程中发生错误:\n{error_msg}")
        finally:
            # 关闭浏览器（如果仍然活动）
            if driver_manager and driver_manager.is_driver_alive():
                self.log("正在关闭浏览器...")
                driver_manager.close_driver()

    def _add_toutiao_account(self):
        """添加头条账号"""
        import threading

        # 先显示提示对话框（非阻塞）
        result = messagebox.askquestion("确认操作",
            "即将打开浏览器进行头条账号登录操作。\n\n"
            "重要提示：\n"
            "1. 请不要手动关闭浏览器\n"
            "2. 登录完成后系统会自动处理\n"
            "3. 在登录过程中，程序界面保持响应\n\n"
            "是否继续？")

        if result != 'yes':
            self.log("用户取消添加头条账号操作")
            return

        # 在后台线程中执行登录过程，避免阻塞UI
        def login_thread():
            from 网易号存稿.platforms.toutiao.login import ToutiaoLogin

            driver = None
            try:
                # 创建头条登录对象
                toutiao_login = ToutiaoLogin(self.log)

                # 使用手机号登录
                self.log("正在打开浏览器，请在浏览器中完成头条账号登录...")
                self.log("提示：请不要手动关闭浏览器，系统会自动处理")

                login_success, driver, cookies = toutiao_login.login_with_phone(False)

                # 在主线程中处理登录结果
                self.root.after(0, lambda: self._handle_toutiao_login_result(
                    login_success, driver, cookies))

            except Exception as e:
                self.log(f"添加头条账号过程中发生错误: {str(e)}")
                import traceback
                traceback.print_exc()

                # 在主线程中处理错误
                self.root.after(0, lambda: self._handle_toutiao_login_error(str(e), driver))

        # 启动后台线程
        thread = threading.Thread(target=login_thread, daemon=True)
        thread.start()
        self.log("头条账号添加过程已在后台启动，界面保持响应...")

    def _handle_toutiao_login_result(self, login_success, driver, cookies):
        """在主线程中处理头条账号登录结果"""
        try:
            if login_success and cookies:
                # 获取账号名称
                account_name = simpledialog.askstring("账号名称", "请输入头条账号名称（如手机号+备注）:")
                if not account_name:
                    self.log("取消添加账号")
                    if driver:
                        driver.quit()
                    return

                # 添加账号
                if self.account_manager.add_account(account_name, cookies):
                    self.log(f"头条账号 {account_name} 添加成功")

                    # 强制重新加载账号列表
                    self.account_manager.load_accounts()

                    # 更新UI
                    self.load_accounts()

                    # 选择新添加的账号
                    self.selected_account.set(account_name)
                    # 更新统计信息面板
                    self.update_stats_panel()

                    # 显示成功消息
                    messagebox.showinfo("添加成功", f"头条账号 {account_name} 添加成功")
                else:
                    self.log(f"头条账号 {account_name} 添加失败")
                    messagebox.showerror("添加失败", f"头条账号 {account_name} 添加失败")
            else:
                self.log("头条账号登录失败，无法添加账号")
                messagebox.showwarning("登录失败", "头条账号登录失败或浏览器被关闭")
        finally:
            # 关闭浏览器（如果仍然活动）
            if driver:
                try:
                    self.log("正在关闭浏览器...")
                    driver.quit()
                except:
                    pass

    def _handle_toutiao_login_error(self, error_msg, driver):
        """在主线程中处理头条账号登录错误"""
        try:
            messagebox.showerror("添加头条账号失败", f"添加头条账号过程中发生错误:\n{error_msg}")
        finally:
            # 关闭浏览器（如果仍然活动）
            if driver:
                try:
                    self.log("正在关闭浏览器...")
                    driver.quit()
                except:
                    pass

    def _add_dayu_account(self):
        """添加大鱼号账号"""
        import threading

        # 先显示提示对话框（非阻塞）
        result = messagebox.askquestion("确认操作",
            "即将打开浏览器进行大鱼号账号登录操作。\n\n"
            "重要提示：\n"
            "1. 请不要手动关闭浏览器\n"
            "2. 登录完成后系统会自动处理\n"
            "3. 在登录过程中，程序界面保持响应\n"
            "4. 登录地址：https://mp.dayu.com/\n\n"
            "是否继续？")

        if result != 'yes':
            self.log("用户取消添加大鱼号账号操作")
            return

        # 在后台线程中执行登录过程，避免阻塞UI
        def login_thread():
            from 网易号存稿.platforms.dayu.login import DayuLogin

            driver = None
            try:
                # 创建大鱼号登录对象
                dayu_login = DayuLogin(self.log)

                # 使用手机号登录
                self.log("正在打开浏览器，请在浏览器中完成大鱼号账号登录...")
                self.log("提示：请不要手动关闭浏览器，系统会自动处理")

                login_success, driver, cookies = dayu_login.login_with_phone(False)

                # 在主线程中处理登录结果
                self.root.after(0, lambda: self._handle_dayu_login_result(
                    login_success, driver, cookies))

            except Exception as e:
                self.log(f"添加大鱼号账号过程中发生错误: {str(e)}")
                import traceback
                traceback.print_exc()

                # 在主线程中处理错误
                self.root.after(0, lambda: self._handle_dayu_login_error(str(e), driver))

        # 启动后台线程
        thread = threading.Thread(target=login_thread, daemon=True)
        thread.start()
        self.log("大鱼号账号添加过程已在后台启动，界面保持响应...")

    def _handle_dayu_login_result(self, login_success, driver, cookies):
        """在主线程中处理大鱼号账号登录结果"""
        try:
            if login_success and cookies:
                # 获取账号名称
                account_name = simpledialog.askstring("账号名称", "请输入大鱼号账号名称（如手机号+备注）:")
                if not account_name:
                    self.log("取消添加账号")
                    if driver:
                        driver.quit()
                    return

                # 添加账号
                if self.account_manager.add_account(account_name, cookies):
                    self.log(f"大鱼号账号 {account_name} 添加成功")

                    # 强制重新加载账号列表
                    self.account_manager.load_accounts()

                    # 更新UI
                    self.load_accounts()

                    # 选择新添加的账号
                    self.selected_account.set(account_name)
                    # 更新统计信息面板
                    self.update_stats_panel()

                    # 显示成功消息
                    messagebox.showinfo("添加成功", f"大鱼号账号 {account_name} 添加成功")
                else:
                    self.log(f"大鱼号账号 {account_name} 添加失败")
                    messagebox.showerror("添加失败", f"大鱼号账号 {account_name} 添加失败")
            else:
                self.log("大鱼号账号登录失败，无法添加账号")
                messagebox.showwarning("登录失败", "大鱼号账号登录失败或浏览器被关闭")
        finally:
            # 关闭浏览器（如果仍然活动）
            if driver:
                try:
                    self.log("正在关闭浏览器...")
                    driver.quit()
                except:
                    pass

    def _handle_dayu_login_error(self, error_msg, driver):
        """在主线程中处理大鱼号账号登录错误"""
        try:
            messagebox.showerror("添加大鱼号账号失败", f"添加大鱼号账号过程中发生错误:\n{error_msg}")
        finally:
            # 关闭浏览器（如果仍然活动）
            if driver:
                try:
                    self.log("正在关闭浏览器...")
                    driver.quit()
                except:
                    pass

    def _get_toutiao_account_data_after_login(self, account: str, driver):
        """
        登录后获取头条账号数据

        Args:
            account: 账号名称
            driver: 浏览器驱动对象
        """
        try:
            from 网易号存稿.platforms.toutiao.data_query import ToutiaoDataQuery
            import datetime

            self.log(f"开始获取头条账号 {account} 的数据...")

            # 创建数据查询对象，但使用已有的driver
            account_dir = self.account_dir.get()
            query = ToutiaoDataQuery(account_dir, self.log)

            # 使用已有的driver进行数据获取
            query.driver = driver

            # 初始化结果
            result = {
                "账号": account,
                "用户名": "",
                "累计收益": 0.0,
                "昨日收益": 0.0,
                "总播放": 0.0,
                "昨日播放": 0.0,
                "总粉丝": 0.0,
                "昨日粉丝": 0.0,
                "草稿箱数量": 0,
                "可提现": 0.0,
                "总提现": 0.0,
                "最近提现日期": "",
                "最近提现金额": 0.0,
                "七日收益": [],
                "查询时间": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "状态": "失败",
                "错误信息": ""
            }

            try:
                # 1. 获取首页数据
                self.log("正在获取首页数据...")
                home_data = query._get_home_data()
                result.update(home_data)

                # 2. 获取草稿箱数据
                self.log("正在获取草稿箱数据...")
                draft_data = query._get_draft_data()
                result.update(draft_data)

                # 3. 获取提现数据
                self.log("正在获取提现数据...")
                withdraw_data = query._get_withdraw_data()
                result.update(withdraw_data)

                # 统一字段映射标准：草稿箱、待提现、七日收益、查询时间、总收益
                result["总收益"] = result.get("累计收益", 0.0)  # 统一标准字段
                result["草稿箱"] = result.get("草稿箱数量", 0)    # 统一标准字段
                result["待提现"] = result.get("可提现", 0.0)     # 统一标准字段
                result["查询时间"] = result.get("查询时间", "")   # 统一标准字段

                result["状态"] = "成功"
                self.log(f"✅ 头条账号 {account} 数据获取完成")

                # 更新账号数据
                self.account_data.update_account_data(account, result)

                # 保存数据
                save_success = self.account_data.save_data()
                if save_success:
                    self.log("✅ 头条账号数据已成功保存")
                else:
                    self.log("❌ 保存头条账号数据时出错")

                # 在主线程中更新界面
                self.root.after(0, self.update_account_tree)
                self.log("✅ 已触发界面更新")

                # 显示成功消息
                self.root.after(0, lambda: messagebox.showinfo("数据获取成功",
                    f"头条账号 {account} 数据获取成功！\n\n"
                    f"用户名: {result.get('用户名', 'N/A')}\n"
                    f"累计收益: {result.get('累计收益', 0):.2f}\n"
                    f"总粉丝: {result.get('总粉丝', 0):.0f}\n"
                    f"草稿箱数量: {result.get('草稿箱数量', 0)}\n\n"
                    f"浏览器将保持打开状态，您可以随时手动关闭。"))

            except Exception as e:
                self.log(f"❌ 获取头条账号 {account} 数据时发生错误: {str(e)}")
                result["错误信息"] = str(e)

                # 显示错误消息
                self.root.after(0, lambda: messagebox.showerror("数据获取失败",
                    f"获取头条账号 {account} 数据时发生错误:\n\n{str(e)}\n\n"
                    f"浏览器将保持打开状态，您可以手动查看或关闭。"))

        except Exception as e:
            self.log(f"❌ 头条账号数据获取过程中发生错误: {str(e)}")
            import traceback
            traceback.print_exc()



    def export_accounts_to_excel(self):
        """导出美化的账号数据到Excel - 委托给Excel导出管理器"""
        if self.excel_export_manager:
            self.excel_export_manager.export_accounts_to_excel()
        else:
            self.log("❌ Excel导出管理器未初始化")
            messagebox.showerror("错误", "Excel导出功能不可用")




    def start_data_query(self):
        """开始数据查询 - 委托给数据查询管理器"""
        if self.data_query_manager:
            # 更新侧边栏运行状态为查询
            self.update_running_status("🔍 查询", "#17A2B8")
            self.data_query_manager.start_data_query()
        else:
            self.log("❌ 数据查询管理器未初始化")
            messagebox.showerror("错误", "数据查询功能不可用")

    def load_draft_details(self):
        """加载存稿详情 - 委托给存稿任务管理器"""
        if hasattr(self, 'draft_task_manager') and self.draft_task_manager:
            self.draft_task_manager.load_draft_details()
        else:
            self.log("❌ 存稿任务管理器未初始化")
            # 初始化空的存稿详情数据结构，避免后续错误
            if not hasattr(self, 'account_details'):
                self.account_details = {}

    def save_draft_details(self):
        """保存存稿详情 - 委托给存稿任务管理器"""
        if self.draft_task_manager:
            self.draft_task_manager.save_draft_details()
        else:
            self.log("❌ 存稿任务管理器未初始化")
            messagebox.showerror("错误", "保存存稿详情功能不可用")

    def log_last_draft_detail(self, account, video_path, status):
        """记录最后一条存稿详情 - 委托给存稿任务管理器"""
        if self.draft_task_manager:
            self.draft_task_manager.log_last_draft_detail(account, video_path, status)
        else:
            self.log("❌ 存稿任务管理器未初始化")
            messagebox.showerror("错误", "记录存稿详情功能不可用")

    def add_draft_detail(self, account, video_path, status, reason="", screenshot="", video_info=None):
        """添加存稿详情 - 委托给存稿任务管理器"""
        if self.draft_task_manager:
            self.draft_task_manager.add_draft_detail(account, video_path, status, reason, screenshot, video_info)
        else:
            self.log("❌ 存稿任务管理器未初始化")
            messagebox.showerror("错误", "添加存稿详情功能不可用")

    def show_collected_data(self):
        """
        显示已收集的数据

        【作废计划 目前仍在使用 但不更新】
        注意：此方法为详细数据查看器的调用入口，虽然标记为作废计划，但目前仍在使用中。
        该方法不再进行功能更新和维护，仅保持现有数据查看功能的稳定运行。
        """
        # 获取当前账号目录中实际存在的账号列表（使用已加载的账号）
        existing_accounts = set(self.accounts if hasattr(self, 'accounts') else [])

        # 获取所有账号数据（只包含实际存在的账号）
        accounts_data_dict = {}
        for account_data in self.account_data.get_all_accounts_data(existing_accounts):
            accounts_data_dict[account_data.get("账号", "")] = account_data

        # 确保所有已加载的账号都显示，即使没有数据
        accounts_data = []
        for account in existing_accounts:
            if account in accounts_data_dict:
                # 使用已有数据
                accounts_data.append(accounts_data_dict[account])
            else:
                # 创建默认数据
                default_data = {
                    "账号": account,
                    "用户名": "",
                    "总收益": "0",
                    "累计收益": "0",
                    "昨日收益": "0",
                    "总播放": "0",
                    "昨日播放": "0",
                    "总粉丝": "0",
                    "昨日粉丝": "0",
                    "草稿箱": "0",
                    "可提现": "0",
                    "待提现": "0",
                    "状态": "未查询",
                    "更新时间": ""
                }
                accounts_data.append(default_data)

        # 如果没有任何账号，显示提示
        if not accounts_data:
            messagebox.showinfo("提示", "没有找到账号，请先加载账号")
            return

        # 导入数据查看器（包含数据概览和七天收益历史功能）
        # 【作废计划 目前仍在使用 但不更新】
        # 注意：AccountDataViewer为详细数据查看器，虽然标记为作废计划，但目前仍在使用中
        from 网易号存稿.ui.dialogs.data_viewer import AccountDataViewer

        # 创建数据查看器，传递当前平台、配置管理器和数据查询管理器信息
        # 【作废计划 目前仍在使用 但不更新】
        self.log("正在打开账号数据查看器...")
        viewer = AccountDataViewer(
            self,  # 传递主窗口对象而不是root，以便数据查看器能访问account_data等属性
            accounts_data,
            self.log,
            self.account_details,
            self.current_platform,
            self.config_manager,
            self.data_query_manager  # 传递数据查询管理器
        )
        # 传递账号管理器引用，以便删除账号功能正常工作
        viewer.account_manager = self.account_manager

        # 将数据查看器添加到列表中
        self.data_viewers.append(viewer)

        # 绑定窗口关闭事件，从列表中移除
        def on_viewer_close():
            if viewer in self.data_viewers:
                self.data_viewers.remove(viewer)
            viewer.window.destroy()

        viewer.window.protocol("WM_DELETE_WINDOW", on_viewer_close)

    def notify_data_viewers_refresh(self):
        """通知所有打开的数据查看器刷新数据"""
        try:
            # 清理已关闭的数据查看器
            self.data_viewers = [viewer for viewer in self.data_viewers if viewer.window.winfo_exists()]

            # 通知所有活跃的数据查看器刷新
            for viewer in self.data_viewers:
                try:
                    # 在主线程中调用刷新方法
                    viewer.window.after(0, viewer.load_data)
                    self.log(f"✅ 已通知数据查看器刷新")
                except Exception as e:
                    self.log(f"❌ 通知数据查看器刷新失败: {str(e)}")
        except Exception as e:
            self.log(f"❌ 通知数据查看器刷新时发生错误: {str(e)}")

    def show_batch_delete_dialog(self):
        """显示批量删除对话框"""
        try:
            # 获取所有账号（使用已加载的账号）
            accounts = self.accounts if hasattr(self, 'accounts') else []
            if not accounts:
                messagebox.showwarning("警告", "没有可删除的账号")
                return

            # 创建批量删除对话框
            self._create_batch_delete_dialog(accounts)
        except Exception as e:
            self.log(f"显示批量删除对话框失败: {str(e)}")
            messagebox.showerror("错误", f"显示批量删除对话框失败: {str(e)}")

    def _create_batch_delete_dialog(self, accounts):
        """创建批量删除对话框"""
        # 创建对话框窗口
        dialog = tk.Toplevel(self.root)
        dialog.title("批量删除账号")
        dialog.geometry("500x600")
        dialog.minsize(400, 500)
        dialog.transient(self.root)
        dialog.grab_set()

        # 居中显示
        try:
            dialog.update_idletasks()
        except RuntimeError as e:
            if "main thread is not in main loop" in str(e):
                return  # 主线程已结束，静默忽略
            else:
                raise
        except Exception:
            return  # 忽略其他UI相关错误
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        # 创建主框架
        main_frame = ttk.Frame(dialog, padding=15)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(
            main_frame,
            text="选择要删除的账号",
            font=("微软雅黑", 14, "bold"),
            foreground="#DC3545"
        )
        title_label.pack(pady=(0, 15))

        # 警告信息
        warning_label = ttk.Label(
            main_frame,
            text="⚠️ 警告：删除账号将永久删除Cookie文件和所有相关数据，且无法恢复！",
            font=("微软雅黑", 10),
            foreground="#DC3545",
            wraplength=450
        )
        warning_label.pack(pady=(0, 15))

        # 账号列表框架
        list_frame = ttk.Frame(main_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # 创建列表框和滚动条
        listbox_frame = ttk.Frame(list_frame)
        listbox_frame.pack(fill=tk.BOTH, expand=True)

        listbox = tk.Listbox(
            listbox_frame,
            selectmode=tk.MULTIPLE,
            font=("微软雅黑", 10),
            height=15
        )


        # 添加账号到列表
        for account in accounts:
            listbox.insert(tk.END, account)

        listbox.pack(fill=tk.BOTH, expand=True)

        # 绑定鼠标滚轮事件以支持滚动
        def _on_mousewheel(event):
            listbox.yview_scroll(int(-1*(event.delta/120)), "units")

        def _bind_mousewheel(event):
            listbox.bind_all("<MouseWheel>", _on_mousewheel)

        def _unbind_mousewheel(event):
            listbox.unbind_all("<MouseWheel>")

        listbox.bind('<Enter>', _bind_mousewheel)
        listbox.bind('<Leave>', _unbind_mousewheel)

        # 底部按钮框架
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.pack(fill=tk.X)

        # 删除按钮
        def confirm_delete():
            selected_indices = listbox.curselection()
            if not selected_indices:
                messagebox.showwarning("警告", "请选择要删除的账号")
                return

            selected_accounts = [accounts[i] for i in selected_indices]

            # 再次确认
            if not messagebox.askyesno(
                "最终确认",
                f"确定要删除以下 {len(selected_accounts)} 个账号吗？\n\n" +
                "\n".join(selected_accounts) +
                "\n\n此操作无法撤销！"
            ):
                return

            # 关闭对话框
            dialog.destroy()

            # 执行删除
            self._execute_batch_delete(selected_accounts)

        delete_btn = ttk.Button(
            bottom_frame,
            text="🗑️ 删除选中账号",
            command=confirm_delete,
            style="Danger.TButton"
        )
        delete_btn.pack(side=tk.LEFT, padx=5)

        # 取消按钮
        ttk.Button(
            bottom_frame,
            text="取消",
            command=dialog.destroy
        ).pack(side=tk.RIGHT, padx=5)

    def _execute_batch_delete(self, accounts_to_delete):
        """执行批量删除"""
        success_count = 0
        failed_accounts = []

        for account in accounts_to_delete:
            try:
                if self.account_manager.delete_account(account):
                    success_count += 1
                    self.log(f"账号 {account} 删除成功")
                else:
                    failed_accounts.append(account)
                    self.log(f"账号 {account} 删除失败")
            except Exception as e:
                failed_accounts.append(account)
                self.log(f"删除账号 {account} 时出错: {str(e)}")

        # 显示结果
        if success_count > 0:
            # 清除当前选中的账号（如果被删除了）
            if self.selected_account.get() in accounts_to_delete:
                self.selected_account.set("")

            # 更新账号列表
            self.load_accounts()
            # 更新统计信息面板
            self.update_stats_panel()

        # 显示结果消息
        if failed_accounts:
            messagebox.showwarning(
                "删除完成",
                f"成功删除 {success_count} 个账号\n失败 {len(failed_accounts)} 个账号:\n" +
                "\n".join(failed_accounts)
            )
        else:
            messagebox.showinfo("删除完成", f"成功删除 {success_count} 个账号")

    def show_seven_day_income(self):
        """显示七天收益历史"""
        try:
            # 获取所有账号数据
            accounts_data = self.account_data.get_all_accounts_data()
            if not accounts_data:
                messagebox.showwarning("警告", "没有账号数据")
                return

            # 创建七天收益历史窗口
            self._create_seven_day_income_window(accounts_data)
        except Exception as e:
            self.log(f"显示七天收益历史失败: {str(e)}")
            messagebox.showerror("错误", f"显示七天收益历史失败: {str(e)}")

    def _create_seven_day_income_window(self, accounts_data):
        """创建七天收益历史窗口"""
        # 计算居中位置
        width = 1000
        height = 600
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2

        # 创建新窗口并直接设置居中位置
        income_window = tk.Toplevel(self.root)
        income_window.title("七天收益历史")
        income_window.geometry(f"{width}x{height}+{x}+{y}")
        income_window.minsize(800, 500)

        # 设置窗口图标
        try:
            income_window.iconbitmap("icon.ico")
        except:
            pass

        # 创建主框架
        main_frame = ttk.Frame(income_window, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 添加标题和状态栏
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(side=tk.TOP, fill=tk.X, pady=(0, 10))
        ttk.Label(title_frame, text="七天收益历史", font=("Arial", 12, "bold")).pack(side=tk.LEFT)

        # 添加账号数量标签
        income_status_label = ttk.Label(title_frame, text="", font=("Arial", 10))
        income_status_label.pack(side=tk.LEFT, padx=20)

        # 创建表格框架
        table_frame = ttk.Frame(main_frame)
        table_frame.pack(fill=tk.BOTH, expand=True)

        # 创建表格
        columns = ("账号", "七天总收益")
        seven_day_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=20)

        # 设置列标题和宽度
        seven_day_tree.heading("账号", text="账号")
        seven_day_tree.heading("七天总收益", text="七天总收益")

        seven_day_tree.column("账号", width=400, anchor=tk.W)
        seven_day_tree.column("七天总收益", width=150, anchor=tk.CENTER)



        # 布局
        seven_day_tree.pack(fill=tk.BOTH, expand=True)

        # 绑定鼠标滚轮事件以支持滚动
        def _on_mousewheel(event):
            seven_day_tree.yview_scroll(int(-1*(event.delta/120)), "units")

        def _bind_mousewheel(event):
            seven_day_tree.bind_all("<MouseWheel>", _on_mousewheel)

        def _unbind_mousewheel(event):
            seven_day_tree.unbind_all("<MouseWheel>")

        seven_day_tree.bind('<Enter>', _bind_mousewheel)
        seven_day_tree.bind('<Leave>', _unbind_mousewheel)

        # 加载数据
        self._load_seven_day_income_data(accounts_data, seven_day_tree, income_status_label)

        # 底部按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(10, 0))

        ttk.Button(button_frame, text="刷新数据", command=lambda: self._load_seven_day_income_data(accounts_data, seven_day_tree, income_status_label)).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="关闭", command=income_window.destroy).pack(side=tk.RIGHT, padx=5)

    def _load_seven_day_income_data(self, accounts_data, tree, status_label):
        """加载七天收益数据"""
        # 清空现有数据
        for item in tree.get_children():
            tree.delete(item)

        # 收集账号和收益数据
        accounts = []
        account_income_data = {}

        for data in accounts_data:
            account = data.get("账号", "")
            if not account or account == "总计":
                continue

            accounts.append(account)
            account_income_data[account] = {}

            # 收集七日收益数据 - 使用统一标准字段
            if "七日收益" in data and isinstance(data["七日收益"], list):
                # 统一标准格式：七日收益是包含字典的列表
                for day_data in data["七日收益"]:
                    if isinstance(day_data, dict) and "日期" in day_data and "收益" in day_data:
                        try:
                            date = day_data["日期"]
                            income_value = float(day_data["收益"])
                            account_income_data[account][date] = income_value
                        except:
                            pass

        # 添加每个账号的数据行
        grand_total = 0
        for account in accounts:
            # 计算该账号七天总收益
            account_total = 0
            for date, income in account_income_data.get(account, {}).items():
                account_total += income

            # 插入行
            tree.insert("", "end", values=(account, f"{account_total:.2f}"))
            grand_total += account_total

        # 添加总计行
        total_row = ["总计", f"{grand_total:.2f}"]
        tree.insert("", "end", values=tuple(total_row), tags=("total",))

        # 设置总计行样式
        tree.tag_configure("total", background="#E0E0E0", font=("Arial", 10, "bold"))

        # 更新状态标签显示账号数量
        account_count = len(accounts)
        status_label.config(text=f"账号数量: {account_count}")

    def filter_main_accounts(self, *_):
        """根据搜索条件过滤主页面账号数据"""
        # 从AccountsTable组件获取搜索文本
        search_text = ""
        if hasattr(self, 'accounts_table') and self.accounts_table:
            search_text = self.accounts_table.get_search_text().lower()

        # 清空现有数据
        account_tree = None
        if hasattr(self, 'accounts_table') and self.accounts_table:
            account_tree = self.accounts_table.account_tree
        elif hasattr(self, 'account_tree'):
            account_tree = self.account_tree

        if not account_tree:
            return

        # 清空表格数据 - 使用表格组件的clear_data方法
        if hasattr(self.accounts_table, 'clear_data'):
            self.accounts_table.clear_data()
        else:
            # 兼容性处理
            for item in account_tree.get_children():
                account_tree.delete(item)

        # 获取当前账号目录中实际存在的账号列表（使用已加载的账号列表）
        existing_accounts = set(self.account_manager.accounts)

        # 获取所有账号数据（只包含实际存在的账号）
        accounts_data_dict = {}
        for account_data in self.account_data.get_all_accounts_data(existing_accounts):
            accounts_data_dict[account_data.get("账号", "")] = account_data

        # 确保所有已加载的账号都显示，即使没有数据
        accounts_data = []
        for account in existing_accounts:
            if account in accounts_data_dict:
                # 使用已有数据
                accounts_data.append(accounts_data_dict[account])
            else:
                # 创建默认数据
                default_data = {
                    "账号": account,
                    "用户名": "",
                    "总收益": "0",
                    "累计收益": "0",
                    "昨日收益": "0",
                    "总播放": "0",
                    "昨日播放": "0",
                    "草稿箱": "0",
                    "可提现": "0",
                    "待提现": "0",
                    "状态": "未查询",
                    "更新时间": ""
                }
                accounts_data.append(default_data)

        # 匹配的账号数量
        matched_count = 0

        # 加载过滤后的数据
        for i, data in enumerate(accounts_data, 1):
            # 检查是否匹配搜索条件
            account = data.get("账号", "").lower()
            username = data.get("用户名", "").lower()
            if not search_text or search_text in account or search_text in username:
                matched_count += 1

                # 计算七日收益总和
                seven_day_income = 0

                # 计算七日收益数据 - 使用统一标准字段
                if "七日收益" in data and isinstance(data["七日收益"], list):
                    # 统一标准格式：七日收益是包含字典的列表
                    for day_data in data["七日收益"]:
                        if isinstance(day_data, dict) and "收益" in day_data:
                            try:
                                income_value = float(day_data["收益"])
                                seven_day_income += income_value
                            except:
                                pass
                # 兼容旧的七日收益数据格式
                elif "七日收益数据" in data:
                    for income in data["七日收益数据"].values():
                        income_value = float(income) if isinstance(income, (int, float, str)) else 0
                        seven_day_income += income_value

                # 导入解析函数
                from 网易号存稿.common.utils import parse_number

                # 解析数值数据，使用统一字段映射标准
                # 统一标准字段：总收益、待提现、草稿箱
                current_income = parse_number(data.get("总收益", "0"))
                yesterday_income = parse_number(data.get("昨日收益", "0"))
                pending_withdraw = parse_number(data.get("待提现", "0"))
                plays = parse_number(data.get("总播放", "0"))
                yesterday_plays = parse_number(data.get("昨日播放", "0"))
                drafts = parse_number(data.get("草稿箱", "0"))

                # 导入格式化函数
                from 网易号存稿.common.utils import format_number

                # 获取状态信息，优先显示Cookie状态
                account_name = data.get("账号", "")
                account_status = self.account_manager.get_account_status(account_name)

                if account_status and account_status.get("status") != "未知":
                    # 使用账号管理器中的状态信息
                    status = account_status.get("status", "正常")
                else:
                    # 使用数据查询的状态
                    status = data.get("状态", "正常")

                # 插入数据到表格 - 使用新的16列格式（删除查看列，添加最近提现金额列）
                # 获取额外字段数据
                total_withdraw = data.get("总提现", data.get("累计提现", "0"))
                recent_withdraw_date = data.get("最近提现日期", data.get("最近提现日", ""))
                recent_withdraw_amount = data.get("最近提现金额", "0")
                total_fans = data.get("总粉丝", "0")
                yesterday_fans = data.get("昨日粉丝", "0")
                update_time = data.get("更新时间", data.get("查询时间", ""))

                row_data = [
                    data.get("账号", ""),                    # account
                    data.get("用户名", ""),                  # username
                    format_number(seven_day_income),         # seven_day_income
                    format_number(current_income),           # total_income
                    format_number(yesterday_income),         # yesterday_income
                    format_number(total_withdraw),           # total_withdraw
                    format_number(pending_withdraw),         # pending_withdraw
                    recent_withdraw_date,                    # recent_withdraw_date
                    format_number(recent_withdraw_amount),   # recent_withdraw_amount
                    update_time,                             # update_time
                    format_number(plays),                    # total_plays
                    format_number(yesterday_plays),          # yesterday_plays
                    format_number(total_fans),               # total_fans
                    format_number(yesterday_fans),           # yesterday_fans
                    format_number(drafts)                    # drafts
                ]

                # 使用表格组件的insert_row方法
                if hasattr(self.accounts_table, 'insert_row'):
                    item_id = self.accounts_table.insert_row(row_data, status)
                else:
                    # 兼容性处理
                    item_id = account_tree.insert("", "end", values=row_data)

        # 更新账号数量显示
        if hasattr(self, 'accounts_table') and self.accounts_table:
            self.accounts_table.update_account_count(matched_count)
        elif hasattr(self, 'table_account_count_label'):
            self.table_account_count_label.config(text=str(matched_count))

        # 更新NavigationPanel中的账号数量显示
        if hasattr(self, 'navigation_panel') and self.navigation_panel:
            self.navigation_panel.update_account_count(matched_count)

    def delete_selected_accounts(self):
        """删除选中的账号（支持多选）"""
        # 委托给账号控制器处理
        if hasattr(self, 'account_controller') and self.account_controller:
            self.account_controller.delete_selected_accounts()
            return

        # 兼容性代码：如果控制器不可用，使用原有逻辑
        account_tree = None
        if hasattr(self, 'accounts_table') and self.accounts_table:
            account_tree = self.accounts_table.account_tree
        elif hasattr(self, 'account_tree'):
            account_tree = self.account_tree

        if not account_tree:
            messagebox.showwarning("警告", "账号表格未初始化")
            return

        # 获取选中的项目
        selected_items = account_tree.selection()
        if not selected_items:
            messagebox.showwarning("警告", "请先选择要删除的账号")
            return

        # 获取选中的账号列表
        accounts_to_delete = []
        for item in selected_items:
            values = account_tree.item(item, "values")
            if len(values) >= 3:
                account = values[1]  # 账号列的索引为1（状态列是索引0）
                if account and account != "总计":
                    accounts_to_delete.append(account)

        if not accounts_to_delete:
            messagebox.showwarning("警告", "没有有效的账号可以删除")
            return

        # 确认删除
        if not messagebox.askyesno(
            "确认删除",
            f"确定要删除以下 {len(accounts_to_delete)} 个账号吗？\n\n" +
            "\n".join(accounts_to_delete) +
            "\n\n注意：此操作将删除账号的Cookie文件和所有相关数据，且无法恢复！"
        ):
            return

        # 执行删除
        success_count = 0
        failed_accounts = []

        for account in accounts_to_delete:
            try:
                if self.account_manager.delete_account(account):
                    success_count += 1
                    self.log(f"账号 {account} 删除成功")
                else:
                    failed_accounts.append(account)
                    self.log(f"账号 {account} 删除失败")
            except Exception as e:
                failed_accounts.append(account)
                self.log(f"删除账号 {account} 时出错: {str(e)}")

        # 显示结果
        if success_count > 0:
            # 清除当前选中的账号（如果被删除了）
            if self.selected_account.get() in accounts_to_delete:
                self.selected_account.set("")

            # 更新账号列表
            self.load_accounts()
            # 更新统计信息面板
            self.update_stats_panel()
            # 刷新表格显示（保存和恢复状态）
            self._save_table_state()
            self.filter_main_accounts()
            self.root.after(100, self._restore_table_state)

        # 显示结果消息
        if failed_accounts:
            messagebox.showwarning(
                "删除完成",
                f"成功删除 {success_count} 个账号\n失败 {len(failed_accounts)} 个账号:\n" +
                "\n".join(failed_accounts)
            )
        else:
            messagebox.showinfo("删除完成", f"成功删除 {success_count} 个账号")

    def show_duplicate_cleaner_dialog(self):
        """显示重复账号清理对话框"""
        try:
            # 检查重复账号
            duplicates = self.account_manager.find_duplicate_accounts()

            if not duplicates:
                messagebox.showinfo("清理结果", "没有发现重复账号")
                return

            # 创建对话框
            dialog = tk.Toplevel(self.root)
            dialog.title("重复账号清理")
            dialog.geometry("800x600")
            dialog.transient(self.root)
            dialog.grab_set()

            # 居中显示
            dialog.update_idletasks()
            x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
            y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
            dialog.geometry(f"+{x}+{y}")

            # 主框架
            main_frame = ttk.Frame(dialog)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # 标题
            title_label = ttk.Label(main_frame, text=f"发现 {len(duplicates)} 组重复账号", font=("Arial", 12, "bold"))
            title_label.pack(pady=(0, 10))

            # 创建树形视图显示重复账号
            tree_frame = ttk.Frame(main_frame)
            tree_frame.pack(fill=tk.BOTH, expand=True)

            # 树形视图
            columns = ("账号名", "Cookie状态", "最后修改时间", "操作")
            tree = ttk.Treeview(tree_frame, columns=columns, show="tree headings", height=15)

            # 设置列标题
            tree.heading("#0", text="分组")
            for col in columns:
                tree.heading(col, text=col)
                tree.column(col, width=150)

    

            tree.pack(fill=tk.BOTH, expand=True)

            # 绑定鼠标滚轮事件以支持滚动
            def _on_mousewheel(event):
                tree.yview_scroll(int(-1*(event.delta/120)), "units")

            def _bind_mousewheel(event):
                tree.bind_all("<MouseWheel>", _on_mousewheel)

            def _unbind_mousewheel(event):
                tree.unbind_all("<MouseWheel>")

            tree.bind('<Enter>', _bind_mousewheel)
            tree.bind('<Leave>', _unbind_mousewheel)

            # 填充数据
            for base_name, accounts in duplicates.items():
                # 创建分组节点
                group_node = tree.insert("", "end", text=f"{base_name} ({len(accounts)}个)", values=("", "", "", ""))

                for account in accounts:
                    # 检查cookie状态
                    cookie_status = self.account_manager.check_account_cookie_status(account)
                    status_text = "有效" if cookie_status.get("valid", False) else f"无效: {cookie_status.get('reason', '未知')}"

                    # 获取最后修改时间
                    account_dir = os.path.join(self.account_dir.get(), account)
                    if os.path.exists(account_dir):
                        import time
                        mtime = os.path.getmtime(account_dir)
                        mod_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(mtime))
                    else:
                        mod_time = "未知"

                    # 添加账号节点
                    tree.insert(group_node, "end", text="", values=(account, status_text, mod_time, ""))

                # 展开分组
                tree.item(group_node, open=True)

            # 策略选择框架
            strategy_frame = ttk.LabelFrame(main_frame, text="清理策略", padding=10)
            strategy_frame.pack(fill=tk.X, pady=(10, 0))

            strategy_var = tk.StringVar(value="valid_cookie")

            strategies = [
                ("valid_cookie", "保留Cookie有效的账号"),
                ("newest", "保留最新修改的账号"),
                ("oldest", "保留最旧的账号"),
            ]

            for value, text in strategies:
                ttk.Radiobutton(strategy_frame, text=text, variable=strategy_var, value=value).pack(anchor=tk.W, pady=2)

            # 按钮框架
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X, pady=(10, 0))

            def do_clean():
                strategy = strategy_var.get()

                # 确认清理
                if not messagebox.askyesno(
                    "确认清理",
                    f"确定要清理重复账号吗？\n\n"
                    f"清理策略: {dict(strategies)[strategy]}\n"
                    f"将删除 {sum(len(accounts) - 1 for accounts in duplicates.values())} 个重复账号\n\n"
                    f"此操作无法撤销！"
                ):
                    return

                # 执行清理
                dialog.destroy()

                # 显示进度
                progress_dialog = tk.Toplevel(self.root)
                progress_dialog.title("清理进度")
                progress_dialog.geometry("400x150")
                progress_dialog.transient(self.root)
                progress_dialog.grab_set()

                # 居中显示
                progress_dialog.update_idletasks()
                x = (progress_dialog.winfo_screenwidth() // 2) - (progress_dialog.winfo_width() // 2)
                y = (progress_dialog.winfo_screenheight() // 2) - (progress_dialog.winfo_height() // 2)
                progress_dialog.geometry(f"+{x}+{y}")

                progress_frame = ttk.Frame(progress_dialog)
                progress_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

                ttk.Label(progress_frame, text="正在清理重复账号...").pack(pady=(0, 10))

                progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')
                progress_bar.pack(fill=tk.X, pady=(0, 10))
                progress_bar.start()

                status_label = ttk.Label(progress_frame, text="准备中...")
                status_label.pack()

                def clean_thread():
                    try:
                        result = self.account_manager.clean_duplicate_accounts(strategy)

                        # 更新UI
                        self.root.after(0, lambda: [
                            progress_dialog.destroy(),
                            messagebox.showinfo("清理完成", result["message"]),
                            self.refresh_account_list()
                        ])

                    except Exception as e:
                        self.root.after(0, lambda: [
                            progress_dialog.destroy(),
                            messagebox.showerror("清理失败", f"清理过程中出现错误:\n{str(e)}")
                        ])

                import threading
                threading.Thread(target=clean_thread, daemon=True).start()

            # 按钮
            ttk.Button(button_frame, text="开始清理", command=do_clean, style="Accent.TButton").pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(button_frame, text="取消", command=dialog.destroy).pack(side=tk.LEFT)

        except Exception as e:
            self.log(f"显示重复账号清理对话框失败: {str(e)}")
            messagebox.showerror("错误", f"显示重复账号清理对话框失败:\n{str(e)}")

    def get_seven_day_income_data(self, driver):
        """
        获取七日收益数据

        Args:
            driver: 浏览器驱动

        Returns:
            七日收益数据字典，格式为 {日期: 收益}
        """
        try:
            self.log("开始获取七日收益数据...")

            # 1. 首先打开网易号后台首页
            driver.get("https://mp.163.com/subscribe_v4/index.html#/home")
            time.sleep(3)

            # 2. 进入收益数据页面
            driver.get("https://mp.163.com/subscribe_v4/index.html#/profit-data")
            time.sleep(3)

            # 3. 点击视频收益标签
            try:
                video_income_tab = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, '//*[@id="root"]/div/div[2]/div[2]/div[1]/div/div/div[3]/div'))
                )
                driver.execute_script("arguments[0].click();", video_income_tab)
                self.log("已点击视频收益标签")
                time.sleep(2)
            except Exception as e:
                self.log(f"点击视频收益标签失败: {str(e)}")
                return {}

            # 4. 点击数据明细
            try:
                data_detail_tab = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, '//*[@id="root"]/div/div[2]/div[2]/div[3]/div[2]/div[1]/div[1]/div[2]'))
                )
                driver.execute_script("arguments[0].click();", data_detail_tab)
                self.log("已点击数据明细")
                time.sleep(2)
            except Exception as e:
                self.log(f"点击数据明细失败: {str(e)}")
                return {}

            # 5. 获取七天的收益数据
            seven_day_income_data = {}

            # 日期和收益的XPath路径
            date_income_paths = [
                # 第一天
                ('//*[@id="root"]/div/div[2]/div[2]/div[3]/div[2]/div[2]/div/div/div[2]/div[7]/div[1]/div',
                 '//*[@id="root"]/div/div[2]/div[2]/div[3]/div[2]/div[2]/div/div/div[2]/div[7]/div[5]'),
                # 第二天
                ('//*[@id="root"]/div/div[2]/div[2]/div[3]/div[2]/div[2]/div/div/div[2]/div[6]/div[1]/div',
                 '//*[@id="root"]/div/div[2]/div[2]/div[3]/div[2]/div[2]/div/div/div[2]/div[6]/div[5]'),
                # 第三天
                ('//*[@id="root"]/div/div[2]/div[2]/div[3]/div[2]/div[2]/div/div/div[2]/div[5]/div[1]/div',
                 '//*[@id="root"]/div/div[2]/div[2]/div[3]/div[2]/div[2]/div/div/div[2]/div[5]/div[5]'),
                # 第四天
                ('//*[@id="root"]/div/div[2]/div[2]/div[3]/div[2]/div[2]/div/div/div[2]/div[4]/div[1]/div',
                 '//*[@id="root"]/div/div[2]/div[2]/div[3]/div[2]/div[2]/div/div/div[2]/div[4]/div[5]'),
                # 第五天
                ('//*[@id="root"]/div/div[2]/div[2]/div[3]/div[2]/div[2]/div/div/div[2]/div[3]/div[1]/div',
                 '//*[@id="root"]/div/div[2]/div[2]/div[3]/div[2]/div[2]/div/div/div[2]/div[3]/div[5]'),
                # 第六天
                ('//*[@id="root"]/div/div[2]/div[2]/div[3]/div[2]/div[2]/div/div/div[2]/div[2]/div[1]/div',
                 '//*[@id="root"]/div/div[2]/div[2]/div[3]/div[2]/div[2]/div/div/div[2]/div[2]/div[5]'),
                # 第七天
                ('//*[@id="root"]/div/div[2]/div[2]/div[3]/div[2]/div[2]/div/div/div[2]/div[1]/div[1]/div',
                 '//*[@id="root"]/div/div[2]/div[2]/div[3]/div[2]/div[2]/div/div/div[2]/div[1]/div[5]')
            ]

            # 获取每一天的日期和收益
            for i, (date_xpath, income_xpath) in enumerate(date_income_paths):
                try:
                    # 获取日期
                    date_element = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, date_xpath))
                    )
                    date_text = date_element.text.strip()

                    # 获取收益
                    income_element = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, income_xpath))
                    )
                    income_text = income_element.text.strip()

                    # 解析收益值
                    from ..common.utils import parse_income
                    income_value = parse_income(income_text)

                    # 添加到数据字典
                    seven_day_income_data[date_text] = str(income_value)

                except Exception as e:
                    self.log(f"获取第 {i+1} 天的收益数据失败: {str(e)}")
                    # 继续获取下一天的数据

            # 检查是否获取到了数据
            if not seven_day_income_data:
                self.log("❌ 未能获取到任何七日收益数据")
                return {}

            self.log(f"✅ 成功获取到 {len(seven_day_income_data)} 天的收益数据")
            return seven_day_income_data

        except Exception as e:
            self.log(f"❌ 获取七日收益数据失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return {}

    def query_account_data(self, account=None):
        """
        查询账号数据

        Args:
            account: 账号名称，如果为None则使用当前选中的账号

        Returns:
            是否成功查询
        """
        if account is None:
            # 如果未指定账号，获取当前选中的账号
            account_tree = None
            if hasattr(self, 'accounts_table') and self.accounts_table:
                account_tree = self.accounts_table.account_tree

            if not account_tree:
                messagebox.showwarning("警告", "账号表格未初始化")
                return False

            selected = account_tree.selection()
            if not selected:
                messagebox.showwarning("警告", "请先选择一个账号")
                return False
            account = account_tree.item(selected[0])['values'][1]  # 账号列的索引为1

        self.log(f"正在查询账号: {account}")

        # 获取cookie路径
        cookie_path = self.account_manager.get_account_cookie_path(account)
        if not cookie_path:
            self.log(f"❌ 账号 {account} 的cookie文件不存在")
            return False

        # 创建浏览器实例
        driver = None
        try:
            # 创建Chrome选项
            options = webdriver.ChromeOptions()
            # 使用无头模式
            options.add_argument('--headless=new')
            options.add_argument('--start-maximized')
            options.add_argument('--disable-notifications')
            options.add_argument('--disable-gpu')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--window-size=1920,1080')
            options.add_experimental_option('excludeSwitches', ['enable-automation'])
            options.add_experimental_option('useAutomationExtension', False)

            # 创建Chrome实例
            driver = webdriver.Chrome(options=options)
            driver.set_page_load_timeout(30)

            # 登录账号
            self.log("登录账号")

            # 打开网易号后台
            driver.get("https://mp.163.com/login.html")
            time.sleep(2)

            # 加载cookie
            if cookie_path.lower().endswith('.json'):
                # 直接加载JSON格式的cookie
                with open(cookie_path, 'r', encoding='utf-8') as f:
                    cookies = json.load(f)

                # 清除所有现有cookie
                driver.delete_all_cookies()

                # 添加cookie
                for cookie in cookies:
                    # 确保cookie包含必要的字段
                    if not all(k in cookie for k in ('name', 'value')):
                        continue
                    # 添加cookie
                    try:
                        driver.add_cookie(cookie)
                    except Exception as e:
                        self.log(f"添加cookie时出错: {str(e)}")
            else:
                self.log(f"❌ 不支持的cookie文件格式: {cookie_path}")
                return False

            # 刷新页面
            driver.get("https://mp.163.com/subscribe_v4")
            time.sleep(3)  # 等待页面加载

            # 验证登录状态
            if "login" in driver.current_url.lower():
                self.log(f"❌ 账号 {account} 登录失败，请重新添加账号")
                return False

            self.log("登录成功")

            # 初始化数据字典
            account_data = {}

            # 1. 获取首页数据
            self.log("✅ 首页数据获取成功")
            driver.get("https://mp.163.com/subscribe_v4/index.html#/home")
            time.sleep(3)  # 等待页面加载

            try:
                # 获取用户名 - 使用更健壮的选择器
                try:
                    username_element = WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.XPATH, '//*[@id="root"]/div/div[2]/div[2]/div[2]/div[2]/div[1]/div[1]/div[2]/div/div'))
                    )
                    account_data["用户名"] = username_element.text.strip()
                except:
                    # 备用选择器
                    try:
                        username_element = WebDriverWait(driver, 5).until(
                            EC.presence_of_element_located((By.XPATH, "//span[@class='username']"))
                        )
                        account_data["用户名"] = username_element.text.strip()
                    except:
                        account_data["用户名"] = account  # 使用账号名作为备用

                # 获取总粉丝数 - 使用更健壮的选择器
                try:
                    total_fans_element = WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.XPATH, '//*[@id="root"]/div/div[2]/div[2]/div[2]/div[1]/div[1]/div[1]/div[1]/div[1]/span'))
                    )
                    account_data["总粉丝"] = total_fans_element.text.strip()
                except:
                    # 备用选择器
                    try:
                        total_fans_element = WebDriverWait(driver, 5).until(
                            EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'fans-num')]"))
                        )
                        account_data["总粉丝"] = total_fans_element.text.strip()
                    except:
                        account_data["总粉丝"] = "0"  # 默认值

                # 获取总播放量 - 使用更健壮的选择器
                try:
                    total_play_element = WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.XPATH, '//*[@id="root"]/div/div[2]/div[2]/div[2]/div[1]/div[1]/div[1]/div[2]/div[1]/span'))
                    )
                    account_data["总播放"] = total_play_element.text.strip()
                except:
                    # 备用选择器
                    try:
                        total_play_element = WebDriverWait(driver, 5).until(
                            EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'play-num')]"))
                        )
                        account_data["总播放"] = total_play_element.text.strip()
                    except:
                        account_data["总播放"] = "0"  # 默认值

                # 获取总收益 - 使用新的网址和元素路径
                self.log("正在获取总收益数据...")
                try:
                    # 访问新的总收益页面
                    driver.get("https://mp.163.com/subscribe_v4/index.html#/profit-data?wemediaId=W1132261147027196833")
                    time.sleep(3)  # 等待页面加载

                    # 点击总收益标签
                    try:
                        total_income_tab = WebDriverWait(driver, 10).until(
                            EC.element_to_be_clickable((By.XPATH, '//*[@id="root"]/div/div[2]/div[2]/div[1]/div/div/div[3]/div'))
                        )
                        driver.execute_script("arguments[0].click();", total_income_tab)
                        self.log("已点击总收益标签")
                        time.sleep(2)  # 等待数据加载
                    except Exception as e:
                        self.log(f"点击总收益标签失败: {str(e)}，尝试直接获取数据")

                    # 获取总收益数据
                    total_income_element = WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.XPATH, '//*[@id="root"]/div/div[2]/div[2]/div[3]/div[1]/div[1]/div[2]'))
                    )
                    account_data["总收益"] = total_income_element.text.strip()
                    self.log(f"获取到总收益: {account_data['总收益']}")
                except Exception as e:
                    self.log(f"获取总收益失败: {str(e)}")
                    # 备用选择器
                    try:
                        total_income_element = WebDriverWait(driver, 5).until(
                            EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'income-num')]"))
                        )
                        account_data["总收益"] = total_income_element.text.strip()
                        self.log(f"使用备用选择器获取到总收益: {account_data['总收益']}")
                    except:
                        account_data["总收益"] = "0"  # 默认值
                        self.log("无法获取总收益，使用默认值: 0")

                self.log("✅ 首页数据获取成功")
            except Exception as e:
                self.log(f"❌ 获取首页数据失败: {str(e)}")
                # 设置默认值
                if "用户名" not in account_data:
                    account_data["用户名"] = account
                if "总粉丝" not in account_data:
                    account_data["总粉丝"] = "0"
                if "总播放" not in account_data:
                    account_data["总播放"] = "0"

            # 注意：总收益已经在单独的步骤中获取，不需要在这里设置默认值

            # 2. 获取草稿箱数量
            self.log("✅ 内容数据获取成功")
            driver.get("https://mp.163.com/subscribe_v4/index.html#/draft-manage")
            time.sleep(3)  # 等待页面加载

            try:
                # 获取草稿箱数量
                draft_count_element = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, '//*[@id="root"]/div/div[2]/div[2]/div[2]/div[1]/div'))
                )
                draft_count_text = draft_count_element.text.strip()

                # 提取数字
                import re
                draft_count_match = re.search(r'\d+', draft_count_text)
                if draft_count_match:
                    account_data["草稿箱"] = draft_count_match.group(0)
                    self.log(f"获取到草稿箱数量: {account_data['草稿箱']}")
                else:
                    account_data["草稿箱"] = "0"
                    self.log("未能从文本中提取草稿箱数量，使用默认值: 0")
            except Exception as e:
                self.log(f"获取草稿箱数量失败: {str(e)}")
                account_data["草稿箱"] = "0"  # 默认值
                self.log("无法获取草稿箱数量，使用默认值: 0")

            # 3. 获取内容数据
            self.log("✅ 订阅数据获取成功")
            driver.get("https://mp.163.com/subscribe_v4/index.html#/content-data")
            time.sleep(3)

            try:
                # 先点击昨日播放标签 - 使用更健壮的选择器
                try:
                    yesterday_play_tab = WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable((By.XPATH, '//*[@id="root"]/div/div[2]/div[2]/div[1]/div/div/div[2]/div'))
                    )
                    driver.execute_script("arguments[0].click();", yesterday_play_tab)
                except:
                    # 备用选择器
                    try:
                        yesterday_play_tab = WebDriverWait(driver, 5).until(
                            EC.element_to_be_clickable((By.XPATH, "//div[contains(text(), '昨日播放')]"))
                        )
                        driver.execute_script("arguments[0].click();", yesterday_play_tab)
                    except:
                        self.log("未找到昨日播放标签，尝试直接获取数据")

                self.log("已点击昨日播放标签")
                time.sleep(2)  # 等待数据加载

                # 获取昨日播放数据 - 使用更健壮的选择器
                try:
                    yesterday_play_element = WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.XPATH, '//*[@id="root"]/div/div[2]/div[2]/div[3]/div[1]/div[2]/div[2]'))
                    )
                    account_data["昨日播放"] = yesterday_play_element.text.strip()
                except:
                    # 备用选择器
                    try:
                        yesterday_play_element = WebDriverWait(driver, 5).until(
                            EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'yesterday-play-num')]"))
                        )
                        account_data["昨日播放"] = yesterday_play_element.text.strip()
                    except:
                        account_data["昨日播放"] = "0"  # 默认值

                self.log("✅ 内容数据获取成功")
            except Exception as e:
                self.log(f"❌ 获取内容数据失败: {str(e)}")
                if "昨日播放" not in account_data:
                    account_data["昨日播放"] = "0"

            # 3. 获取订阅数据
            self.log("✅ 收益数据获取成功")
            driver.get("https://mp.163.com/subscribe_v4/index.html#/subscribe-data")
            time.sleep(3)

            try:
                # 获取昨日粉丝 - 使用更健壮的选择器
                try:
                    yesterday_fans_element = WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.XPATH, '//*[@id="root"]/div/div[2]/div[2]/div[1]/div[2]/div[2]'))
                    )
                    account_data["昨日粉丝"] = yesterday_fans_element.text.strip()
                except:
                    # 备用选择器
                    try:
                        yesterday_fans_element = WebDriverWait(driver, 5).until(
                            EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'yesterday-fans-num')]"))
                        )
                        account_data["昨日粉丝"] = yesterday_fans_element.text.strip()
                    except:
                        account_data["昨日粉丝"] = "0"  # 默认值

                self.log("✅ 订阅数据获取成功")
            except Exception as e:
                self.log(f"❌ 获取订阅数据失败: {str(e)}")
                if "昨日粉丝" not in account_data:
                    account_data["昨日粉丝"] = "0"

            # 4. 获取收益数据
            self.log("✅ 总提现和待提现数据获取成功")
            driver.get("https://mp.163.com/subscribe_v4/index.html#/profit-data")
            time.sleep(3)

            try:
                # 先点击昨日收益标签 - 使用更健壮的选择器
                try:
                    yesterday_income_tab = WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable((By.XPATH, '//*[@id="root"]/div/div[2]/div[2]/div[1]/div/div/div[3]/div'))
                    )
                    driver.execute_script("arguments[0].click();", yesterday_income_tab)
                except:
                    # 备用选择器
                    try:
                        yesterday_income_tab = WebDriverWait(driver, 5).until(
                            EC.element_to_be_clickable((By.XPATH, "//div[contains(text(), '昨日收益')]"))
                        )
                        driver.execute_script("arguments[0].click();", yesterday_income_tab)
                    except:
                        self.log("未找到昨日收益标签，尝试直接获取数据")

                self.log("已点击昨日收益标签")
                time.sleep(2)  # 等待数据加载

                # 获取昨日收益数据 - 使用更健壮的选择器
                try:
                    yesterday_income_element = WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.XPATH, '//*[@id="root"]/div/div[2]/div[2]/div[3]/div[1]/div[2]/div[2]'))
                    )
                    yesterday_income = yesterday_income_element.text.strip()
                    account_data["昨日收益"] = yesterday_income

                    # 只保存昨日收益数据，不更新七日收益数据
                    # 七日收益数据将在所有账号查询完成后一次性更新
                    try:
                        from ..common.utils import parse_income
                        income_value = parse_income(yesterday_income)
                        # 不在这里更新七日收益数据
                        self.log(f"✅ 已获取昨日收益数据: {income_value}")
                    except Exception as e:
                        self.log(f"❌ 解析昨日收益数据失败: {str(e)}")
                except:
                    # 备用选择器
                    try:
                        yesterday_income_element = WebDriverWait(driver, 5).until(
                            EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'yesterday-income-num')]"))
                        )
                        yesterday_income = yesterday_income_element.text.strip()
                        account_data["昨日收益"] = yesterday_income

                        # 只保存昨日收益数据，不更新七日收益数据
                        # 七日收益数据将在所有账号查询完成后一次性更新
                        try:
                            from ..common.utils import parse_income
                            income_value = parse_income(yesterday_income)
                            # 不在这里更新七日收益数据
                            self.log(f"✅ 已获取昨日收益数据: {income_value}")
                        except Exception as e:
                            self.log(f"❌ 解析昨日收益数据失败: {str(e)}")
                    except:
                        account_data["昨日收益"] = "0"  # 默认值

                # 获取总提现和待提现数据
                self.log("正在获取总提现和待提现数据...")
                try:
                    # 确保在收益页面
                    driver.get("https://mp.163.com/subscribe_v4/index.html#/profit")
                    time.sleep(3)  # 等待页面加载

                    # 获取总提现数据
                    try:
                        total_withdraw_element = WebDriverWait(driver, 10).until(
                            EC.presence_of_element_located((By.XPATH, '//*[@id="root"]/div/div[2]/div[2]/div[3]/div[2]/div[1]/div[2]'))
                        )
                        account_data["总提现"] = total_withdraw_element.text.strip()
                        self.log(f"✅ 获取到总提现: {account_data['总提现']}")
                    except Exception as e:
                        self.log(f"❌ 获取总提现失败: {str(e)}")
                        account_data["总提现"] = "0"

                    # 获取待提现数据
                    try:
                        pending_withdraw_element = WebDriverWait(driver, 10).until(
                            EC.presence_of_element_located((By.XPATH, '//*[@id="root"]/div/div[2]/div[2]/div[3]/div[2]/div[3]/div[2]'))
                        )
                        account_data["待提现"] = pending_withdraw_element.text.strip()
                        self.log(f"✅ 获取到待提现: {account_data['待提现']}")
                    except Exception as e:
                        self.log(f"❌ 获取待提现失败: {str(e)}")
                        account_data["待提现"] = "0"

                    self.log("✅ 总提现和待提现数据获取成功")
                except Exception as e:
                    self.log(f"❌ 获取总提现和待提现数据失败: {str(e)}")
                    if "总提现" not in account_data:
                        account_data["总提现"] = "0"
                    if "待提现" not in account_data:
                        account_data["待提现"] = "0"

                # 获取七日收益数据
                self.log("正在获取七日收益数据...")
                try:
                    # 重新进入收益数据页面
                    driver.get("https://mp.163.com/subscribe_v4/index.html#/profit-data")
                    time.sleep(3)

                    # 获取七日收益数据
                    seven_day_income_data = self.get_seven_day_income_data(driver)
                    if seven_day_income_data:
                        account_data["七日收益数据"] = seven_day_income_data
                        # 计算七日收益总和
                        total_seven_day_income = sum(float(income) for income in seven_day_income_data.values())
                        self.log(f"✅ 七日收益数据获取成功")
                    else:
                        self.log("❌ 七日收益数据获取失败，将使用昨日收益累加")
                except Exception as e:
                    self.log(f"❌ 获取七日收益数据失败: {str(e)}")

                self.log("✅ 收益数据获取成功")
            except Exception as e:
                self.log(f"❌ 获取收益数据失败: {str(e)}")
                if "昨日收益" not in account_data:
                    account_data["昨日收益"] = "0"
                if "总提现" not in account_data:
                    account_data["总提现"] = "0"
                if "待提现" not in account_data:
                    account_data["待提现"] = "0"

            # 添加更新时间和状态
            import datetime
            account_data["更新时间"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            account_data["状态"] = "已查询"

            # 更新账号数据
            update_success = self.account_data.update_account_data(account, account_data)

            if update_success:
                self.log(f"✅ 账号数据已成功保存")
                # 使用增量更新而不是全表刷新
                self.root.after(200, lambda: self._incremental_update_account(account, account_data))
                self.log(f"✅ 已触发界面增量更新")
            else:
                self.log(f"❌ 账号数据保存失败")

            self.log(f"✅ 账号 {account} 数据查询完成")
            return update_success

        except Exception as e:
            self.log(f"❌ 账号数据查询出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

        finally:
            # 关闭浏览器
            if driver:
                try:
                    driver.quit()
                except:
                    pass

    def backup_account_data(self):
        """备份账号数据"""
        from tkinter import filedialog
        import datetime

        # 获取当前时间作为文件名
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"account_data_backup_{timestamp}.json"

        # 打开文件对话框
        file_path = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json")],
            initialfile=filename
        )

        if file_path:
            # 备份数据
            if self.account_data.backup_data(file_path):
                self.log(f"账号数据已备份到: {file_path}")
                messagebox.showinfo("备份成功", f"账号数据已备份到:\n{file_path}")
            else:
                self.log("备份账号数据失败")
                messagebox.showerror("备份失败", "备份账号数据失败")

    def select_dir(self, var):
        """选择目录 - 委托给设置管理器"""
        if self.settings_manager:
            self.settings_manager.select_dir(var)
        else:
            self.log("❌ 设置管理器未初始化")
            messagebox.showerror("错误", "选择目录功能不可用")

    def save_config(self, silent=None, force_save=False):
        """保存配置 - 委托给设置管理器（带弹窗）

        Args:
            silent: 是否静默保存（不显示弹窗），None时根据平台切换状态自动判断
            force_save: 是否强制立即保存（兼容原始版本参数）
        """
        # 如果没有指定silent参数，根据平台切换状态自动判断
        if silent is None:
            silent = getattr(self, '_platform_switching', False)

        # 如果指定了force_save，则强制静默保存
        if force_save:
            silent = True

        if self.settings_manager:
            return self.settings_manager.save_config(silent=silent)
        else:
            self.log("❌ 设置管理器未初始化")
            if not silent:
                messagebox.showerror("错误", "保存配置功能不可用")
            return False

    def save_config_silent(self):
        """静默保存配置 - 用于变量绑定，不显示弹窗"""
        # 辅助函数确保整数变量的安全获取
        def safe_get_int(int_var, default=0):
            try:
                return int(int_var.get())
            except:
                return default

        # 更新配置 - 分别保存平台特定配置和通用配置
        # 平台特定配置
        platform_config = {
            "account_dir": self.account_dir.get(),
            "video_dir": self.video_dir.get(),
            "cover_dir": self.cover_dir.get(),
            "processed_dir": self.processed_dir.get(),
            "processed_covers_dir": self.processed_covers_dir.get(),
            "violation_dir": self.violation_dir.get(),
            "screenshots_dir": self.screenshots_dir.get(),
            "archive_completed": self.archive_completed.get(),
            "headless_mode": self.headless_mode.get(),
            "draft_limit": safe_get_int(self.draft_limit, 0),
            "loop_limit": safe_get_int(self.loop_limit, 0),
            "concurrent_accounts": safe_get_int(self.concurrent_accounts, 1),
            "random_video_allocation": self.random_video_allocation.get(),
            "query_headless_mode": self.query_headless_mode.get(),
            "query_max_threads": safe_get_int(self.query_max_threads, 10),
            "auto_close": self.auto_close.get(),
            "concurrent_mode": self.concurrent_mode.get(),
            "max_workers": safe_get_int(self.max_workers, 3)
        }

        # 通用配置
        common_config = {
            "enable_level_filter": self.enable_level_filter.get(),
            "enable_keyword_filter": self.enable_keyword_filter.get(),
            "min_log_level": self.min_log_level.get(),
            "simple_log_mode": self.simple_log_mode.get(),
            "dark_mode": self.dark_mode.get()
        }

        # 分别更新平台配置和通用配置 - 静默保存
        self.config_manager.update(platform_config, platform=self.current_platform)
        self.config_manager.update(common_config, platform="common")

    def update_log_filter_settings(self):
        """更新日志过滤设置 - 委托给日志管理器"""
        if self.log_manager:
            # 应用日志过滤设置
            logger.enable_level_filtering(self.enable_level_filter.get())
            logger.set_min_level(self.min_log_level.get())
            logger.enable_keyword_filtering(self.enable_keyword_filter.get())

            # 保存配置
            self.save_config()

            # 记录日志
            self.log(f"✅ 日志过滤设置已更新: 级别过滤={self.enable_level_filter.get()}, 关键词过滤={self.enable_keyword_filter.get()}, 极简日志模式={self.simple_log_mode.get()}")
        else:
            self.log("❌ 日志管理器未初始化")

    def manage_filtered_keywords(self):
        """管理过滤关键词 - 委托给日志管理器"""
        if self.log_manager:
            self.log_manager.manage_filtered_keywords()
        else:
            self.log("❌ 日志管理器未初始化")
            messagebox.showerror("错误", "关键词管理功能不可用")


    def apply_theme(self):
        """应用主题设置 - 委托给设置管理器"""
        if self.settings_manager:
            self.settings_manager.apply_theme()
            # 更新主题灯泡颜色
            self.update_theme_bulb_color()
        else:
            self.log("❌ 设置管理器未初始化")
            messagebox.showerror("错误", "应用主题功能不可用")

    def set_window_size(self):
        """设置窗口大小 - 委托给设置管理器"""
        if self.settings_manager:
            self.settings_manager.set_window_size()
        else:
            self.log("❌ 设置管理器未初始化")
            messagebox.showerror("错误", "设置窗口大小功能不可用")



    def set_preset_size(self, width, height):
        """
        设置预设窗口大小

        Args:
            width: 窗口宽度
            height: 窗口高度
        """
        self.window_width.set(width)
        self.window_height.set(height)
        self.set_window_size()
        self.save_config()

    def toggle_theme(self):
        """切换主题"""
        self.dark_mode.set(not self.dark_mode.get())
        self.apply_theme()
        self.save_config()

    def toggle_theme_bulb(self):
        """通过小灯泡切换主题"""
        self.dark_mode.set(not self.dark_mode.get())
        self.update_theme_bulb_color()
        self.apply_theme()
        self.save_config(silent=True)  # 灯泡切换使用静默保存

        # 记录日志
        mode_name = "深色模式" if self.dark_mode.get() else "浅色模式"
        self.log(f"💡 已通过灯泡切换到{mode_name}")

    def update_theme_bulb_color(self):
        """更新主题灯泡的颜色"""
        if hasattr(self, 'header_bar') and hasattr(self.header_bar, 'theme_bulb_button'):
            if self.dark_mode.get():
                # 深色模式 - 亮黄色灯泡表示开启
                self.header_bar.theme_bulb_button.configure(foreground="#FFA500")  # 橙黄色，更醒目
            else:
                # 浅色模式 - 淡灰色灯泡表示关闭
                self.header_bar.theme_bulb_button.configure(foreground="#B0B0B0")  # 淡灰色

    def bind_variable_changes(self):
        """绑定变量变化事件"""
        # 绑定目录变量变化事件 - 使用静默保存
        self.account_dir.trace_add("write", lambda *_: self.save_config_silent())
        self.video_dir.trace_add("write", lambda *_: self.save_config_silent())
        self.cover_dir.trace_add("write", lambda *_: self.save_config_silent())
        self.processed_dir.trace_add("write", lambda *_: self.save_config_silent())
        self.processed_covers_dir.trace_add("write", lambda *_: self.save_config_silent())
        self.violation_dir.trace_add("write", lambda *_: self.save_config_silent())
        self.screenshots_dir.trace_add("write", lambda *_: self.save_config_silent())

        # 绑定其他变量变化事件 - 使用静默保存
        self.archive_completed.trace_add("write", lambda *_: self.save_config_silent())
        self.headless_mode.trace_add("write", lambda *_: self.save_config_silent())
        self.draft_limit.trace_add("write", lambda *_: self.save_config_silent())
        self.loop_limit.trace_add("write", lambda *_: self.save_config_silent())
        self.concurrent_accounts.trace_add("write", lambda *_: self.save_config_silent())
        self.random_video_allocation.trace_add("write", lambda *_: self.save_config_silent())

        # 绑定数据查询配置变量变化事件 - 使用静默保存
        self.query_headless_mode.trace_add("write", lambda *_: self.save_config_silent())
        self.query_max_threads.trace_add("write", lambda *_: self.save_config_silent())

        # 绑定浏览器运行配置变量变化事件 - 使用静默保存
        self.auto_close.trace_add("write", lambda *_: self.save_config_silent())
        self.concurrent_mode.trace_add("write", lambda *_: self.save_config_silent())
        self.max_workers.trace_add("write", lambda *_: self.save_config_silent())

        # 绑定主题和界面设置变量变化事件
        self.dark_mode.trace_add("write", lambda *_: self.apply_theme())
        self.font_size.trace_add("write", lambda *_: self.save_config_silent())
        self.window_width.trace_add("write", lambda *_: self.set_window_size())
        self.window_height.trace_add("write", lambda *_: self.set_window_size())

        # 绑定日志过滤设置变量变化事件
        self.enable_level_filter.trace_add("write", lambda *_: self.update_log_filter_settings())
        self.enable_keyword_filter.trace_add("write", lambda *_: self.update_log_filter_settings())
        self.min_log_level.trace_add("write", lambda *_: self.update_log_filter_settings())
        self.simple_log_mode.trace_add("write", lambda *_: self.update_log_filter_settings())

    def start_task(self):
        """开始存稿任务 - 委托给存稿任务管理器"""
        if self.draft_task_manager:
            # 更新侧边栏运行状态为存稿
            self.update_running_status("🔄 存稿", "#FFC107")
            self.draft_task_manager.start_task()
        else:
            self.log("❌ 存稿任务管理器未初始化")
            messagebox.showerror("错误", "存稿任务功能不可用")

    def start_single_task(self, account):
        """开始单个账号的存稿任务 - 委托给存稿任务管理器"""
        if self.draft_task_manager:
            self.draft_task_manager.start_single_task(account)
        else:
            self.log("❌ 存稿任务管理器未初始化")
            messagebox.showerror("错误", "单个账号存稿功能不可用")





    def start_concurrent_task(self, accounts):
        """开始并发处理多个账号 - 委托给存稿任务管理器"""
        if self.draft_task_manager:
            self.draft_task_manager.start_concurrent_task(accounts)
        else:
            self.log("❌ 存稿任务管理器未初始化")
            messagebox.showerror("错误", "并发存稿功能不可用")






    def stop_task(self):
        """停止存稿任务 - 委托给存稿任务管理器"""
        if self.draft_task_manager:
            self.draft_task_manager.stop_task()
        else:
            self.log("❌ 存稿任务管理器未初始化")
            messagebox.showerror("错误", "停止任务功能不可用")

        # 更新UI状态
        self.start_button["state"] = "normal"
        self.stop_button["state"] = "disabled"
        self.status_text.config(text="就绪")

    def create_required_dirs(self, silent=False):
        """创建必要的目录

        Args:
            silent: 是否静默模式（不输出日志）
        """
        import os

        # 获取所有需要的目录
        dirs = [
            self.account_dir.get(),
            self.video_dir.get(),
            self.cover_dir.get(),
            self.processed_dir.get(),
            self.processed_covers_dir.get(),
            self.violation_dir.get(),
            self.screenshots_dir.get()
        ]

        # 创建目录
        for dir_path in dirs:
            if dir_path and not os.path.exists(dir_path):
                try:
                    os.makedirs(dir_path, exist_ok=True)
                    if not silent:
                        self.log(f"已创建目录: {dir_path}")
                except Exception as e:
                    if not silent:
                        self.log(f"创建目录失败: {dir_path}, 错误: {str(e)}")

        if not silent:
            self.log("已确保所有必要目录存在")

    # 旧的进度条代码已移除

    def update_account_progress(self, account, progress, status=None, details=None, progress_data=None):
        """更新账号进度 - 委托给存稿任务管理器"""
        if self.draft_task_manager:
            self.draft_task_manager.update_account_progress(account, progress, status, details, progress_data)
        else:
            self.log("❌ 存稿任务管理器未初始化")
            messagebox.showerror("错误", "更新账号进度功能不可用")

    # 旧的总进度条代码已移除

# 重复的save_config方法已移除，使用上面的委托版本

    def launch_video_processor(self, auto_mode=False, status_file=None):
        """启动视频处理工具

        Args:
            auto_mode (bool): 是否启用自动模式
            status_file (str): 状态文件路径（可选）
        """
        try:
            # 检查是否在打包环境中
            if getattr(sys, 'frozen', False):
                # 在打包环境中，直接在当前程序内打开视频处理工具
                try:
                    # 导入视频处理工具模块
                    sys.path.insert(0, os.path.join(sys._MEIPASS, '视频处理工具'))
                    from 视频处理工具.modules.ui.main import VideoPreprocessUI
                    from 视频处理工具.modules.video_processor import VideoProcessor

                    # 创建新窗口
                    video_window = tk.Toplevel(self.root)
                    video_window.title("🎬 视频处理工具")
                    video_window.geometry("1200x800")
                    video_window.minsize(1000, 700)

                    # 设置窗口图标（如果有的话）
                    try:
                        video_window.iconbitmap(self.root.iconbitmap())
                    except:
                        pass

                    # 创建视频处理器
                    video_processor = VideoProcessor()

                    # 创建UI实例
                    ui = VideoPreprocessUI(video_window, process_callback=video_processor.process_video,
                                         auto_mode=auto_mode, video_processor=video_processor)

                    self.log("✅ 视频处理工具已在当前程序中打开")
                    return True

                except Exception as e:
                    self.log(f"❌ 在打包环境中启动视频处理工具失败: {str(e)}")
                    # 回退到外部启动方式
                    return self._launch_video_processor_external(auto_mode, status_file)
            else:
                # 在开发环境中，使用外部启动方式
                return self._launch_video_processor_external(auto_mode, status_file)

        except Exception as e:
            self.log(f"❌ 启动视频处理工具失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def _launch_video_processor_external(self, auto_mode=False, status_file=None):
        """外部启动视频处理工具（开发环境或回退方案）"""
        try:
            # 获取当前脚本所在目录
            current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

            # 视频处理工具路径
            video_processor_path = os.path.join(current_dir, "视频处理工具", "main.py")

            # 检查文件是否存在
            if not os.path.exists(video_processor_path):
                self.log(f"❌ 视频处理工具不存在: {video_processor_path}")
                return False

            # 构建命令参数
            python_exe = sys.executable
            cmd = [python_exe, video_processor_path]

            # 添加自动模式参数
            if auto_mode:
                cmd.append("--auto-mode")
                self.log("🚀 启动视频处理工具（自动模式）")
            else:
                self.log("🚀 启动视频处理工具（手动模式）")

            # 添加状态文件参数
            if status_file:
                cmd.extend(["--status-file", status_file])
                self.log(f"📊 状态文件: {status_file}")

            self.log(f"执行命令: {' '.join(cmd)}")

            # 使用subprocess启动视频处理工具，不创建新控制台
            import subprocess
            process = subprocess.Popen(cmd)

            self.log(f"✅ 已启动视频处理工具，进程ID: {process.pid}")
            return True
        except Exception as e:
            self.log(f"❌ 外部启动视频处理工具失败: {str(e)}")
            return False

    def open_video_downloader(self):
        """打开视频下载工具对话框"""
        try:
            # 导入视频下载工具对话框
            from 网易号存稿.ui.dialogs.video_downloader_dialog import VideoDownloaderDialog

            # 创建对话框，传递配置管理器
            self.log("打开视频下载工具对话框")
            video_downloader_dialog = VideoDownloaderDialog(self.root, self.config_manager)

            # 等待对话框关闭
            self.root.wait_window(video_downloader_dialog.dialog)

            # 记录日志
            self.log("已关闭视频下载工具对话框")
            return True
        except Exception as e:
            self.log(f"❌ 打开视频下载工具对话框失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def open_daily_hot(self):
        """打开每日爆文工具对话框"""
        try:
            # 导入每日爆文工具对话框
            from 网易号存稿.ui.dialogs.daily_hot_dialog import DailyHotDialog

            # 创建对话框，传递配置管理器
            self.log("打开每日爆文工具对话框")
            daily_hot_dialog = DailyHotDialog(self.root, self.config_manager)

            # 等待对话框关闭
            self.root.wait_window(daily_hot_dialog.dialog)

            # 记录日志
            self.log("已关闭每日爆文工具对话框")
            return True
        except Exception as e:
            self.log(f"❌ 打开每日爆文工具对话框失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def validate_all_cookies(self):
        """验证所有账号的登录状态（进行实际登录测试）"""
        try:
            self.log("🔍 开始验证所有账号的登录状态...")

            if not self.accounts:
                self.log("❌ 没有找到账号，请先加载账号列表")
                messagebox.showwarning("提示", "没有找到账号，请先加载账号列表")
                return

            # 确认开始验证
            result = messagebox.askyesno(
                "账号验证",
                f"即将验证 {len(self.accounts)} 个账号的登录状态。\n\n"
                "验证方式：实际登录测试\n"
                "预计时间：较长（取决于账号数量）\n\n"
                "是否开始验证？"
            )

            if not result:  # 取消
                return

            # 启动计时器和重置统计
            self.start_runtime_timer()
            self.reset_success_fail_stats()

            deep_validation = True  # 只使用深度验证

            # 创建进度对话框
            progress_dialog = tk.Toplevel(self.root)
            progress_dialog.title("账号验证进度")
            progress_dialog.geometry("400x150")
            progress_dialog.resizable(False, False)
            progress_dialog.transient(self.root)

            # 设置窗口属性
            try:
                progress_dialog.attributes('-topmost', True)
            except:
                pass

            # 居中显示
            self._center_window(progress_dialog)

            # 进度框架
            progress_frame = ttk.Frame(progress_dialog, padding=20)
            progress_frame.pack(fill=tk.BOTH, expand=True)

            # 进度标签
            progress_label = ttk.Label(progress_frame, text="正在验证账号登录状态...")
            progress_label.pack(pady=(0, 10))

            # 进度条
            progress_bar = ttk.Progressbar(progress_frame, mode='determinate', maximum=len(self.accounts))
            progress_bar.pack(fill=tk.X, pady=(0, 10))

            # 状态标签
            status_label = ttk.Label(progress_frame, text="准备中...")
            status_label.pack()

            # 验证结果统计
            valid_count = 0
            invalid_count = 0

            def validate_thread():
                nonlocal valid_count, invalid_count
                try:
                    # 使用深度验证（实际登录测试）
                    valid_count, invalid_count = self._concurrent_validate_cookies(self.accounts, progress_bar, status_label)

                    # 验证完成，更新UI
                    self.root.after(0, lambda: [
                        progress_dialog.destroy(),
                        self.update_account_tree(),
                        messagebox.showinfo("验证完成",
                            f"账号验证完成！\n\n"
                            f"✅ 有效账号: {valid_count} 个\n"
                            f"❌ 失效账号: {invalid_count} 个\n\n"
                            f"状态已更新到账号管理列表"),
                        self.log(f"🔍 账号验证完成: 有效 {valid_count} 个，失效 {invalid_count} 个")
                    ])

                except Exception as e:
                    self.root.after(0, lambda: [
                        progress_dialog.destroy(),
                        messagebox.showerror("验证失败", f"账号验证过程中出现错误:\n{str(e)}"),
                        self.log(f"❌ 账号验证失败: {str(e)}")
                    ])

            # 启动验证线程
            import threading
            threading.Thread(target=validate_thread, daemon=True).start()

        except Exception as e:
            self.log(f"❌ 启动账号验证失败: {str(e)}")
            messagebox.showerror("错误", f"启动账号验证失败:\n{str(e)}")

    def _deep_validate_cookie(self, account: str) -> dict:
        """
        验证账号登录状态：进行真实的登录测试

        Args:
            account: 账号名称

        Returns:
            验证结果字典
        """
        try:
            # 先进行基础检查
            basic_status = self.account_manager.check_account_cookie_status(account)
            if not basic_status.get("valid", False):
                return basic_status

            # 获取Cookie文件路径 - 支持直接文件格式和子目录格式
            cookie_file = self.account_manager.get_account_cookie_path(account)

            if not cookie_file:
                return {"valid": False, "reason": "Cookie文件不存在"}

            # 进行真实登录测试 - 根据当前平台选择登录类
            if self.current_platform == "toutiao":
                from 网易号存稿.platforms.toutiao.login import ToutiaoLogin
                # 创建头条登录实例（静默模式）
                login = ToutiaoLogin(log_callback=None)  # 禁用日志输出
                # 尝试登录
                success, driver = login.login_with_cookies(cookie_file, headless=True)
            else:
                # 网易号或其他平台 - 使用独立驱动创建方式（参考头条号）
                from 网易号存稿.account.login import AccountLogin

                # 创建登录实例（静默模式，使用独立驱动创建）
                login = AccountLogin(log_callback=None)  # 不传递driver_manager，使用独立驱动创建

                # 尝试登录
                success, driver = login.login_with_cookies(cookie_file, headless=True, account=account)

            if success and driver:
                # 登录成功，关闭浏览器
                try:
                    if self.current_platform == "toutiao":
                        # 头条平台：直接关闭（使用自己的端口管理）
                        if hasattr(driver, '_assigned_port'):
                            from 网易号存稿.browser.driver import PortManager
                            PortManager.release_port(driver._assigned_port)
                        driver.quit()
                    else:
                        # 网易号平台：使用登录实例的关闭方法
                        login.close_driver(driver)
                except:
                    pass
                return {"valid": True, "reason": "登录验证成功"}
            else:
                # 登录失败，尝试获取失败原因
                if driver:
                    try:
                        current_url = driver.current_url
                        if self.current_platform == "toutiao":
                            # 头条平台的失败判断
                            if "login" in current_url.lower() or "auth" in current_url.lower():
                                reason = "登录失败：Cookie已过期，被重定向到登录页面"
                            elif "mp.toutiao.com" not in current_url:
                                reason = "登录失败：未能访问头条创作者平台"
                            else:
                                reason = f"登录失败：停留在 {current_url}"
                        else:
                            # 网易号平台的失败判断
                            if "login" in current_url.lower() or "passport" in current_url.lower():
                                reason = "登录失败：Cookie已过期，被重定向到登录页面"
                            elif "mp.163.com" not in current_url:
                                reason = "登录失败：未能访问网易号创作者平台"
                            elif "subscribe_v4" not in current_url:
                                reason = "登录失败：未能进入网易号后台"
                            else:
                                reason = f"登录失败：停留在 {current_url}"

                        # 关闭浏览器
                        try:
                            if self.current_platform == "toutiao":
                                # 头条平台：直接关闭
                                if hasattr(driver, '_assigned_port'):
                                    from 网易号存稿.browser.driver import PortManager
                                    PortManager.release_port(driver._assigned_port)
                                driver.quit()
                            else:
                                # 网易号平台：使用登录实例的关闭方法
                                login.close_driver(driver)
                        except:
                            pass
                    except:
                        reason = "登录失败：浏览器异常"
                else:
                    reason = "登录失败：浏览器启动失败"

                return {"valid": False, "reason": reason}

        except Exception as e:
            return {"valid": False, "reason": f"验证过程异常: {str(e)}"}

    def _concurrent_validate_cookies(self, accounts, progress_bar, status_label):
        """
        并发验证账号登录状态

        Args:
            accounts: 账号列表
            progress_bar: 进度条组件
            status_label: 状态标签组件

        Returns:
            (valid_count, invalid_count): 有效和无效账号的数量
        """
        import concurrent.futures
        import threading
        import time

        # 并发数量
        max_workers = 10

        # 进度跟踪和结果统计
        completed_count = 0
        valid_count = 0
        invalid_count = 0
        total_count = len(accounts)
        lock = threading.Lock()

        def validate_single_account(account):
            """验证单个账号"""
            try:
                # 更新状态显示
                self.root.after(0, lambda: status_label.config(text=f"验证账号: {account}"))

                # 进行深度验证
                cookie_status = self._deep_validate_cookie(account)

                # 更新进度
                nonlocal completed_count, valid_count, invalid_count
                with lock:
                    completed_count += 1
                    self.root.after(0, lambda: progress_bar.config(value=completed_count))

                # 处理验证结果
                if cookie_status.get("valid", False):
                    status_desc = "登录验证成功"
                    self.account_manager.update_account_status(account, status_desc, cookie_status.get("reason", ""))
                    self.log(f"✅ {account}: {status_desc}")

                    with lock:
                        valid_count += 1
                    return {"account": account, "valid": True, "status_desc": status_desc, "reason": cookie_status.get("reason", "")}
                else:
                    reason = cookie_status.get("reason", "未知原因")

                    # 根据失败原因确定状态描述
                    if "登录失败" in reason or "登录页面" in reason:
                        status_desc = "登录失效"
                    elif "过期" in reason:
                        status_desc = "Cookie过期"
                    elif "不存在" in reason:
                        status_desc = "Cookie缺失"
                    elif "格式错误" in reason:
                        status_desc = "Cookie格式错误"
                    else:
                        status_desc = "Cookie失效"

                    self.account_manager.update_account_status(account, status_desc, reason)
                    self.log(f"❌ {account}: {status_desc}")

                    with lock:
                        invalid_count += 1
                    return {"account": account, "valid": False, "status_desc": status_desc, "reason": reason}

            except Exception as e:
                error_msg = f"验证异常: {str(e)}"
                self.account_manager.update_account_status(account, "验证失败", error_msg)
                self.log(f"❌ {account}: 验证失败")

                with lock:
                    invalid_count += 1
                return {"account": account, "valid": False, "status_desc": "验证失败", "reason": error_msg}

        # 使用线程池进行并发验证
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_account = {executor.submit(validate_single_account, account): account for account in accounts}

            # 等待所有任务完成
            for future in concurrent.futures.as_completed(future_to_account):
                account = future_to_account[future]
                try:
                    result = future.result()
                    # 结果已经在validate_single_account中处理了
                except Exception as e:
                    self.log(f"❌ {account}: 验证失败")
                    with lock:
                        invalid_count += 1

        return valid_count, invalid_count

    def show_cache_cleaner(self):
        """显示缓存清理工具"""
        try:
            # 导入缓存清理工具
            from 网易号存稿.tools.cache_cleaner import CacheCleanerGUI

            # 记录日志
            self.log("🧹 正在打开缓存清理工具...")

            # 创建新窗口
            cache_window = tk.Toplevel(self.root)
            cache_window.title("🧹 缓存清理工具")
            cache_window.geometry("800x800")
            cache_window.minsize(700, 700)

            # 设置窗口图标（如果有的话）
            try:
                cache_window.iconbitmap(self.root.iconbitmap())
            except:
                pass

            # 创建缓存清理器GUI
            cache_cleaner_gui = CacheCleanerGUI(cache_window)

            # 居中显示窗口
            self._center_window(cache_window)

            # 记录日志
            self.log("✅ 缓存清理工具已打开")

            return True

        except Exception as e:
            self.log(f"❌ 打开缓存清理工具失败: {str(e)}")
            messagebox.showerror("错误", f"打开缓存清理工具失败:\n{str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def _center_window(self, window):
        """将窗口居中显示"""
        try:
            try:
                window.update_idletasks()
            except RuntimeError as e:
                if "main thread is not in main loop" in str(e):
                    return  # 主线程已结束，静默忽略
                else:
                    raise
            width = window.winfo_width()
            height = window.winfo_height()
            screen_width = window.winfo_screenwidth()
            screen_height = window.winfo_screenheight()
            x = (screen_width - width) // 2
            y = (screen_height - height) // 2
            window.geometry(f"+{x}+{y}")
        except Exception as e:
            self.log(f"窗口居中失败: {str(e)}")

    def start_all_accounts_task(self):
        """开始所有账号的存稿任务 - 委托给存稿任务管理器"""
        if self.draft_task_manager:
            return self.draft_task_manager.start_all_accounts_task()
        else:
            self.log("❌ 存稿任务管理器未初始化")
            messagebox.showerror("错误", "全部账号存稿功能不可用")
            return False

    def show_tab_by_name(self, tab_name, sub_tab_name=None):
        """
        根据名称显示指定的标签页

        Args:
            tab_name: 主标签页名称
            sub_tab_name: 子标签页名称（可选）
        """
        # 查找主标签页索引
        for i in range(self.notebook.index("end")):
            if self.notebook.tab(i, "text") == tab_name:
                # 选择主标签页
                self.notebook.select(i)

                # 如果指定了子标签页，查找并选择子标签页
                if sub_tab_name and tab_name == "设置":
                    for j in range(self.settings_notebook.index("end")):
                        if self.settings_notebook.tab(j, "text") == sub_tab_name:
                            # 选择子标签页
                            self.settings_notebook.select(j)
                            break
                break

        # 记录日志
        if sub_tab_name:
            self.log(f"已切换到 {tab_name} - {sub_tab_name} 标签页")
        else:
            self.log(f"已切换到 {tab_name} 标签页")



    def on_closing(self):
        """窗口关闭事件处理 - 委托给资源管理器"""
        try:
            self.log("正在关闭程序，清理后台资源...")

            # 停止串行刷新工作线程
            self.stop_refresh_worker()

            # 取消防抖定时器
            if self._debounce_timer:
                try:
                    self.root.after_cancel(self._debounce_timer)
                except:
                    pass

            # 清理调度器管理器
            if hasattr(self, 'scheduler_manager') and self.scheduler_manager:
                try:
                    self.scheduler_manager.cleanup()
                    self.log("✅ 调度器管理器清理完成")
                except Exception as e:
                    self.log(f"❌ 调度器管理器清理失败: {e}")

            # 使用资源管理器进行清理
            if self.resource_manager:
                self.resource_manager.cleanup_all_resources()
            else:
                # 如果资源管理器不可用，记录警告
                self.log("⚠️ 资源管理器不可用，跳过资源清理")

            # 保存配置并强制立即保存
            self.save_config(force_save=True)
            self.config_manager.flush_pending_changes()

            self.log("程序清理完成，正在退出...")

        except Exception as e:
            print(f"关闭程序时出错: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # 确保窗口被关闭
            try:
                self.root.destroy()
            except:
                pass

            # 检查是否还有活动的守护线程，如果有则强制退出
            import threading
            import time
            import sys

            # 给一点时间让清理过程完成
            time.sleep(0.5)

            # 检查剩余的守护线程
            active_threads = threading.enumerate()
            daemon_threads = [t for t in active_threads if t.daemon and t != threading.current_thread()]

            if daemon_threads:
                print(f"检测到 {len(daemon_threads)} 个守护线程仍在运行，程序将强制退出")
                # 只有在确实有守护线程无法正常结束时才强制退出
                import os
                os._exit(0)
            else:
                # 正常退出
                sys.exit(0)







    def cleanup_draft_processors(self):
        """清理存稿处理器资源 - 委托给存稿任务管理器"""
        if self.draft_task_manager:
            self.draft_task_manager.cleanup_draft_processors()
        else:
            self.log("❌ 存稿任务管理器未初始化")
            messagebox.showerror("错误", "清理存稿处理器功能不可用")

    def cleanup_thread_pools(self):
        """清理线程池资源"""
        try:
            # 清理主线程池
            if hasattr(self, 'executor') and self.executor:
                self.log("正在关闭主线程池...")
                self.executor.shutdown(wait=False)

            # 清理并发管理器的线程池
            if hasattr(self, 'concurrent_manager') and self.concurrent_manager:
                self.log("正在关闭并发管理器...")
                # 先调用stop方法停止任务
                if hasattr(self.concurrent_manager, 'stop'):
                    self.concurrent_manager.stop()
                # 再调用shutdown方法关闭线程池
                if hasattr(self.concurrent_manager, 'shutdown'):
                    self.concurrent_manager.shutdown(wait=False)

            # 强制停止所有工作线程
            self.force_stop_worker_threads()

            self.log("线程池资源清理完成")

        except Exception as e:
            self.log(f"清理线程池资源时出错: {e}")

    def force_stop_worker_threads(self):
        """强制停止所有工作线程"""
        try:
            # 停止所有可能的工作线程
            import threading

            # 获取所有活动线程
            active_threads = threading.enumerate()
            daemon_threads = [t for t in active_threads if t.daemon and t != threading.current_thread()]

            if daemon_threads:
                self.log(f"发现 {len(daemon_threads)} 个守护线程，正在等待它们结束...")

                # 给守护线程一些时间自然结束
                import time
                time.sleep(1)

                # 再次检查
                active_threads = threading.enumerate()
                remaining_threads = [t for t in active_threads if t.daemon and t != threading.current_thread()]

                if remaining_threads:
                    self.log(f"仍有 {len(remaining_threads)} 个守护线程在运行，程序将强制退出")
                else:
                    self.log("所有守护线程已正常结束")

        except Exception as e:
            self.log(f"强制停止工作线程时出错: {e}")

    def cleanup_temp_files(self):
        """清理临时文件和缓存"""
        try:
            # 清理截图临时文件
            screenshots_dir = self.screenshots_dir.get()
            if screenshots_dir and os.path.exists(screenshots_dir):
                temp_files = []
                for file in os.listdir(screenshots_dir):
                    if file.startswith('temp_') or file.startswith('error_'):
                        temp_files.append(os.path.join(screenshots_dir, file))

                for temp_file in temp_files:
                    try:
                        os.remove(temp_file)
                    except Exception:
                        pass

                if temp_files:
                    self.log(f"已清理 {len(temp_files)} 个临时截图文件")

            # 清理其他可能的临时文件
            # 可以根据需要添加更多临时文件清理逻辑

        except Exception as e:
            self.log(f"清理临时文件时出错: {e}")

    def force_terminate_subprocesses(self):
        """强制终止当前进程的子进程"""
        try:
            import subprocess
            import platform
            import os

            current_pid = os.getpid()
            self.log(f"当前进程PID: {current_pid}")

            # 尝试使用psutil获取子进程（如果可用）
            try:
                import psutil
                current_process = psutil.Process(current_pid)
                children = current_process.children(recursive=True)

                if children:
                    self.log(f"发现 {len(children)} 个子进程，正在终止...")

                    for child in children:
                        try:
                            self.log(f"终止子进程 PID: {child.pid}")
                            child.terminate()
                        except psutil.NoSuchProcess:
                            pass
                        except Exception as e:
                            self.log(f"终止子进程 {child.pid} 时出错: {e}")

                    # 等待一段时间让进程自然终止
                    import time
                    time.sleep(1)

                    # 强制杀死仍然存在的子进程
                    for child in children:
                        try:
                            if child.is_running():
                                self.log(f"强制杀死子进程 PID: {child.pid}")
                                child.kill()
                        except psutil.NoSuchProcess:
                            pass
                        except Exception as e:
                            self.log(f"强制杀死子进程 {child.pid} 时出错: {e}")

                    self.log("所有子进程已终止")
                else:
                    self.log("没有发现子进程")

            except ImportError:
                # 如果没有psutil，只清理Chrome相关进程，避免影响其他Python程序
                self.log("psutil不可用，仅清理Chrome相关进程...")

                # 只清理Chrome和ChromeDriver进程，不影响其他Python程序
                if platform.system() == "Windows":
                    try:
                        # 清理Chrome进程
                        subprocess.run(['taskkill', '/f', '/im', 'chrome.exe'],
                                     capture_output=True, timeout=5)
                        subprocess.run(['taskkill', '/f', '/im', 'chromedriver.exe'],
                                     capture_output=True, timeout=5)
                        self.log("已清理Chrome相关进程")
                    except Exception as e:
                        self.log(f"Windows Chrome进程清理失败: {e}")
                else:
                    # Linux/Mac系统：只清理Chrome进程
                    try:
                        subprocess.run(['pkill', '-f', 'chrome'],
                                     capture_output=True, timeout=5)
                        subprocess.run(['pkill', '-f', 'chromedriver'],
                                     capture_output=True, timeout=5)
                        self.log("已清理Chrome相关进程")
                    except Exception as e:
                        self.log(f"Linux/Mac Chrome进程清理失败: {e}")

        except Exception as e:
            self.log(f"强制终止子进程失败: {e}")

    # 旧的定时任务状态检查方法已移除
    # 现在使用升级版定时任务系统，通过 scheduler_manager 管理

    def get_timer_status_info(self):
        """获取定时任务状态信息（供其他组件调用）- 现在使用升级版定时任务系统"""
        try:
            if hasattr(self, 'scheduler_manager') and self.scheduler_manager:
                # 使用升级版定时任务系统获取状态
                stats = self.scheduler_manager.scheduler_manager.get_statistics()
                return {
                    'enabled': True,
                    'running': stats.get('running_tasks', 0) > 0,
                    'total_tasks': stats.get('total_tasks', 0),
                    'scheduled_tasks': stats.get('scheduled_tasks', 0),
                    'system': 'upgraded'
                }
            else:
                return {'enabled': False, 'running': False, 'total_tasks': 0, 'system': 'upgraded', 'manager_missing': True}
        except Exception as e:
            self.log(f"❌ 获取升级版定时任务状态失败: {e}")
            return {'enabled': False, 'running': False, 'total_tasks': 0, 'system': 'upgraded', 'error': str(e)}

        except Exception as e:
            self.log(f"强制终止子进程时出错: {e}")
