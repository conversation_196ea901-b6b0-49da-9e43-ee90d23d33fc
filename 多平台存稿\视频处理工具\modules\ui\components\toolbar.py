"""
工具栏组件 - 顶部工具栏
"""

import tkinter as tk
from tkinter import ttk

def create_toolbar(parent, ui_instance):
    """创建工具栏"""
    # 创建工具栏框架
    toolbar_frame = ttk.Frame(parent)
    toolbar_frame.pack(fill=tk.X, padx=5, pady=5)

    # 创建自定义样式
    style = ttk.Style()
    style.configure("Icon.TButton", padding=2)

    # 左侧字体控制按钮
    font_frame = ttk.LabelFrame(toolbar_frame, text="界面字体")
    font_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))

    font_buttons_frame = ttk.Frame(font_frame)
    font_buttons_frame.pack(fill=tk.X, padx=5, pady=5)

    # 添加图标按钮
    from ...icons import get_icon
    increase_icon = get_icon("font_increase", (16, 16))
    decrease_icon = get_icon("font_decrease", (16, 16))

    # 增大字体按钮
    increase_button = ttk.Button(font_buttons_frame, image=increase_icon, style="Icon.TButton",
                              command=ui_instance.increase_font_size)
    increase_button.pack(side=tk.LEFT, padx=2)

    # 减小字体按钮
    decrease_button = ttk.Button(font_buttons_frame, image=decrease_icon, style="Icon.TButton",
                               command=ui_instance.decrease_font_size)
    decrease_button.pack(side=tk.LEFT, padx=2)

    # 字体大小标签
    font_size_label = ttk.Label(font_buttons_frame, text=f"当前大小: {ui_instance.default_font_size}px")
    font_size_label.pack(side=tk.LEFT, padx=5)

    # 保存图标引用，防止被垃圾回收
    ui_instance.increase_icon = increase_icon
    ui_instance.decrease_icon = decrease_icon
    ui_instance.font_size_label = font_size_label

    # 右侧控制按钮
    right_frame = ttk.Frame(toolbar_frame)
    right_frame.pack(side=tk.RIGHT, fill=tk.Y)

    # 获取图标
    settings_icon = get_icon("settings", (16, 16))
    save_icon = get_icon("save", (16, 16))
    dark_mode_icon = get_icon("dark_mode", (16, 16))

    # 设置按钮 - 添加图标和工具提示
    settings_button = ttk.Button(right_frame, text="设置", image=settings_icon, compound=tk.LEFT,
                               command=ui_instance.open_settings_dialog, width=8)
    settings_button.pack(side=tk.RIGHT, padx=5)

    # 手动保存配置按钮 - 添加图标和工具提示
    save_button = ttk.Button(right_frame, text="保存", image=save_icon, compound=tk.LEFT,
                           command=lambda: ui_instance.config_manager.save_config(show_message=True), width=8)
    save_button.pack(side=tk.RIGHT, padx=5)

    # 黑暗模式切换按钮 - 添加图标
    dark_mode_frame = ttk.Frame(right_frame)
    dark_mode_frame.pack(side=tk.RIGHT, padx=10)

    ttk.Label(dark_mode_frame, image=dark_mode_icon).pack(side=tk.LEFT, padx=(0, 2))
    dark_mode_button = ttk.Checkbutton(dark_mode_frame, text="黑暗模式",
                                      variable=ui_instance.enable_dark_mode,
                                      command=ui_instance.toggle_dark_mode)
    dark_mode_button.pack(side=tk.LEFT)

    # 保存图标引用，防止被垃圾回收
    ui_instance.settings_icon = settings_icon
    ui_instance.save_icon = save_icon
    ui_instance.dark_mode_icon = dark_mode_icon
    ui_instance.dark_mode_button = dark_mode_button

    # 不再使用工具提示，改为直接在按钮上显示更多信息
    increase_button.config(text="A+ (Ctrl++)")
    decrease_button.config(text="A- (Ctrl+-)")
    settings_button.config(text="设置 (Ctrl+O)")
    save_button.config(text="保存 (Ctrl+S)")

    return toolbar_frame
