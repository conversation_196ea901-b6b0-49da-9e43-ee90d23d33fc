"""
工作线程模块 - 负责执行具体的存稿任务
"""

import os
import time
import queue
import random
import threading
from typing import Dict, Any, Optional, Callable, List

from ..draft.processor import DraftProcessor
from ..browser.driver import DriverManager
from ..browser.actions import BrowserActions
from ..account.login import AccountLogin

class Worker:
    """工作线程类，负责执行具体的存稿任务"""

    def __init__(self,
                 account: str,
                 account_dir: str,
                 processed_dir: str,
                 processed_covers_dir: str,
                 video_queue: queue.Queue,
                 archive_completed: bool = True,
                 headless_mode: bool = False,
                 draft_limit: int = 0,
                 loop_limit: int = 0,
                 log_callback: Callable = None,
                 progress_callback: Callable = None):
        """
        初始化工作线程

        Args:
            account: 账号名称
            account_dir: 账号目录
            processed_dir: 已处理视频目录
            processed_covers_dir: 已处理封面目录
            video_queue: 视频队列
            archive_completed: 是否归档已完成视频
            headless_mode: 是否使用无头模式
            draft_limit: 存稿数量限制，0表示不限制
            loop_limit: 循环次数限制，0表示不限制
            log_callback: 日志回调函数
            progress_callback: 进度回调函数
        """
        self.account = account
        self.account_dir = account_dir
        self.processed_dir = processed_dir
        self.processed_covers_dir = processed_covers_dir
        self.video_queue = video_queue
        self.archive_completed = archive_completed
        self.headless_mode = headless_mode
        self.draft_limit = draft_limit
        self.loop_limit = loop_limit
        self.log_callback = log_callback
        self.progress_callback = progress_callback

        # 初始化状态
        self.is_running = False
        self.thread = None
        self.driver_manager = None
        self.browser_actions = None
        self.driver = None

        # 创建截图目录
        self.screenshot_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "screenshots")
        os.makedirs(self.screenshot_dir, exist_ok=True)

    def log(self, message: str) -> None:
        """
        记录日志

        Args:
            message: 日志消息
        """
        if self.log_callback:
            self.log_callback(self.account, message)
        else:
            print(f"[{self.account}] {message}")

    def update_progress(self, progress: int, status: str, details: str, progress_data=None) -> None:
        """
        更新进度

        Args:
            progress: 进度百分比
            status: 状态
            details: 详细信息
            progress_data: 完整的进度数据字典（可选）
        """
        if self.progress_callback:
            if progress_data and isinstance(progress_data, dict):
                # 如果提供了完整的进度数据，直接传递
                self.progress_callback(self.account, progress_data)
            else:
                # 否则使用单独的参数
                self.progress_callback(self.account, progress, status, details)

    def start(self) -> bool:
        """
        开始工作线程

        Returns:
            是否成功启动
        """
        if self.is_running:
            self.log("工作线程已在运行中")
            return False

        self.is_running = True
        self.thread = threading.Thread(target=self._work, daemon=True)
        self.thread.start()

        self.log("工作线程已启动")
        return True

    def stop(self) -> None:
        """停止工作线程"""
        if not self.is_running:
            return

        self.is_running = False
        self.log("正在停止工作线程...")

        # 关闭浏览器
        if self.driver_manager:
            self.driver_manager.close_driver()

        # 等待线程结束
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5.0)

        self.log("工作线程已停止")

    def _work(self) -> None:
        """工作线程的主要工作函数"""
        try:
            # 更新进度
            self.update_progress(0, "开始处理", "正在初始化")

            # 初始化驱动管理器
            self.driver_manager = DriverManager(lambda msg: self.log(msg))

            # 获取账号Cookie路径 - 只查找账号名.txt格式
            cookie_path = os.path.join(self.account_dir, f"{self.account}.txt")
            if not os.path.exists(cookie_path):
                self.log(f"账号Cookie文件不存在: {cookie_path}")
                self.update_progress(0, "失败", "Cookie文件不存在")
                return

            # 使用Cookie登录
            self.log(f"正在登录账号: {self.account}")
            account_login = AccountLogin(self.driver_manager, lambda msg: self.log(msg))
            login_success, self.driver = account_login.login_with_cookies(cookie_path, self.headless_mode, self.account)

            if not login_success or not self.driver:
                self.log(f"登录账号失败: {self.account}")
                self.update_progress(0, "失败", "登录失败")
                return

            # 更新进度
            self.update_progress(10, "已登录", "登录成功")

            # 创建浏览器操作对象
            self.browser_actions = BrowserActions(self.driver, lambda msg: self.log(msg))

            # 初始化计数器
            draft_count = 0  # 已成功存稿数量
            loop_count = 0   # 已循环次数

            # 显示限制设置
            if self.draft_limit > 0:
                self.log(f"已设置存稿数量限制: {self.draft_limit} 个视频")
            if self.loop_limit > 0:
                self.log(f"已设置循环次数限制: {self.loop_limit} 次")

            # 循环处理视频
            while self.is_running:
                # 检查循环次数限制
                if self.loop_limit > 0 and loop_count >= self.loop_limit:
                    self.log(f"已达到循环次数限制: {self.loop_limit} 次")
                    break

                # 检查存稿数量限制
                if self.draft_limit > 0 and draft_count >= self.draft_limit:
                    self.log(f"已达到存稿数量限制: {self.draft_limit} 个视频")
                    break

                # 从队列获取视频
                try:
                    video_path = self.video_queue.get(timeout=5.0)
                except queue.Empty:
                    self.log("视频队列为空，等待中...")
                    time.sleep(5)
                    continue

                # 处理视频
                video_file = os.path.basename(video_path)
                self.log(f"正在处理视频: {video_file}")

                # 更新进度
                self.update_progress(20, "处理中", f"正在处理视频: {video_file}")

                # 查找对应的封面文件
                cover_path = self._find_cover_for_video(video_file)

                # 创建存稿处理器
                processor = DraftProcessor(
                    account_dir=self.account_dir,
                    processed_dir=self.processed_dir,
                    processed_covers_dir=self.processed_covers_dir,
                    archive_completed=self.archive_completed,
                    headless_mode=self.headless_mode,
                    draft_limit=1,  # 每次只处理一个视频
                    loop_limit=1,   # 每次只循环一次
                    log_callback=lambda msg: self.log(msg)
                )

                # 设置处理器的驱动和浏览器操作对象
                processor.driver_manager = self.driver_manager
                processor.driver = self.driver
                processor.browser_actions = self.browser_actions

                # 执行存稿
                result = processor._draft_video(video_path, cover_path)

                if result:
                    self.log(f"视频 {video_file} 存稿成功")
                    draft_count += 1

                    # 更新进度和成功存稿数量
                    progress = min(10 + (draft_count * 90 // self.draft_limit if self.draft_limit > 0 else 90), 100)

                    # 创建包含成功存稿数量的进度数据
                    progress_data = {
                        "progress": progress,
                        "status": "已存稿",
                        "details": f"已存稿 {draft_count} 个视频",
                        "successful_drafts": draft_count  # 添加成功存稿数量
                    }

                    # 更新进度，包含成功存稿数量
                    self.update_progress(progress, f"已存稿 ({draft_count})", f"已存稿 {draft_count} 个视频", progress_data)

                    # 确保进度回调函数被调用，实时更新UI
                    if hasattr(self, 'progress_callback') and self.progress_callback:
                        # 直接调用回调函数，确保UI立即更新
                        self.progress_callback(self.account, progress, f"已存稿 ({draft_count})",
                                              f"已存稿 {draft_count} 个视频", progress_data)

                    # 归档或删除已处理的视频
                    if self.archive_completed:
                        # 移动到已处理目录
                        archive_dir = os.path.join(self.processed_dir, "已存稿")
                        os.makedirs(archive_dir, exist_ok=True)
                        archive_path = os.path.join(archive_dir, video_file)

                        try:
                            os.rename(video_path, archive_path)
                            self.log(f"已归档视频: {video_file}")
                        except Exception as e:
                            self.log(f"归档视频失败: {str(e)}")
                    else:
                        # 删除视频
                        try:
                            os.remove(video_path)
                            self.log(f"已删除视频: {video_file}")
                        except Exception as e:
                            self.log(f"删除视频失败: {str(e)}")

                    # 处理封面文件
                    if cover_path and os.path.exists(cover_path):
                        cover_file = os.path.basename(cover_path)

                        # 检查是否是临时目录中的副本文件
                        if "temp" in cover_path:
                            # 如果是临时文件，直接删除，不影响原始封面
                            try:
                                os.remove(cover_path)
                                self.log(f"已删除临时封面副本: {cover_file}")
                            except Exception as e:
                                self.log(f"删除临时封面副本失败: {str(e)}")
                        else:
                            # 如果是原始封面文件，按照归档设置处理
                            if self.archive_completed:
                                # 移动到已处理目录
                                archive_dir = os.path.join(self.processed_covers_dir, "已存稿")
                                os.makedirs(archive_dir, exist_ok=True)
                                archive_path = os.path.join(archive_dir, cover_file)

                                try:
                                    # 使用复制而不是移动，以防其他线程仍在使用原始文件
                                    import shutil
                                    shutil.copy2(cover_path, archive_path)
                                    self.log(f"已归档封面: {cover_file}")

                                    # 检查是否有其他线程正在使用此封面
                                    # 这里使用一个简单的延迟删除策略，给其他线程留出时间
                                    time.sleep(2)  # 等待2秒，让其他可能的线程有时间处理
                                    try:
                                        os.remove(cover_path)
                                        self.log(f"已删除原始封面: {cover_file}")
                                    except Exception as e:
                                        self.log(f"删除原始封面失败(可能被其他线程使用): {str(e)}")
                                except Exception as e:
                                    self.log(f"归档封面失败: {str(e)}")
                            else:
                                # 不归档，但也使用延迟删除策略
                                try:
                                    # 等待2秒，让其他可能的线程有时间处理
                                    time.sleep(2)
                                    os.remove(cover_path)
                                    self.log(f"已删除封面: {cover_file}")
                                except Exception as e:
                                    self.log(f"删除封面失败: {str(e)}")
                else:
                    self.log(f"视频 {video_file} 存稿失败")

                    # 更新进度
                    self.update_progress(
                        min(10 + (draft_count * 90 // self.draft_limit if self.draft_limit > 0 else 90), 100),
                        "警告",
                        f"视频 {video_file} 存稿失败"
                    )

                    # 将视频放回队列
                    self.video_queue.put(video_path)

                # 标记任务完成
                self.video_queue.task_done()

            # 任务完成
            self.log(f"账号 {self.account} 存稿任务完成，共存稿 {draft_count} 个视频")

            # 创建包含成功存稿数量的进度数据
            progress_data = {
                "progress": 100,
                "status": "完成",
                "details": f"共存稿 {draft_count} 个视频",
                "successful_drafts": draft_count  # 添加成功存稿数量
            }

            # 更新进度，包含成功存稿数量
            self.update_progress(100, f"完成 ({draft_count})", f"共存稿 {draft_count} 个视频", progress_data)

            # 确保进度回调函数被调用，实时更新UI
            if hasattr(self, 'progress_callback') and self.progress_callback:
                # 直接调用回调函数，确保UI立即更新
                self.progress_callback(self.account, 100, f"完成 ({draft_count})",
                                      f"共存稿 {draft_count} 个视频", progress_data)

        except Exception as e:
            self.log(f"工作线程发生错误: {str(e)}")
            self.update_progress(0, "错误", f"发生错误: {str(e)}")

        finally:
            # 关闭浏览器
            if self.driver_manager:
                self.driver_manager.close_driver()

            self.is_running = False

    def _find_cover_for_video(self, video_file: str) -> Optional[str]:
        """
        查找视频对应的封面文件

        Args:
            video_file: 视频文件名

        Returns:
            封面文件路径，如果未找到则返回None
        """
        try:
            if not os.path.exists(self.processed_covers_dir):
                os.makedirs(self.processed_covers_dir, exist_ok=True)
                return None

            # 获取视频文件名（不含扩展名）
            video_name = os.path.splitext(video_file)[0]

            # 查找匹配的封面文件
            for ext in ['.jpg', '.jpeg', '.png', '.webp']:
                cover_path = os.path.join(self.processed_covers_dir, f"{video_name}{ext}")
                if os.path.exists(cover_path):
                    # 在并发环境中，创建封面文件的副本，避免多个线程同时访问同一个文件
                    try:
                        # 创建临时目录（如果不存在）
                        temp_dir = os.path.join(self.processed_covers_dir, "temp")
                        os.makedirs(temp_dir, exist_ok=True)

                        # 创建封面文件的副本
                        import shutil
                        temp_cover_path = os.path.join(temp_dir, f"{video_name}_{int(time.time())}_{random.randint(1000, 9999)}{ext}")
                        shutil.copy2(cover_path, temp_cover_path)

                        self.log(f"已创建封面文件副本: {os.path.basename(temp_cover_path)}")
                        return temp_cover_path
                    except Exception as copy_error:
                        self.log(f"创建封面文件副本失败: {str(copy_error)}")
                        # 如果创建副本失败，仍然返回原始路径
                        return cover_path

            return None

        except Exception as e:
            self.log(f"查找封面文件失败: {str(e)}")
            return None
