#!/usr/bin/env python3
"""
快速功能验证脚本
"""

def test_basic_import():
    """测试基本导入"""
    try:
        import sys
        from pathlib import Path
        sys.path.insert(0, str(Path(__file__).parent))
        
        from 统一自动打包 import UnifiedPackager, GUI_AVAILABLE
        print("✅ 基本导入成功")
        return True
    except Exception as e:
        print(f"❌ 基本导入失败: {e}")
        return False

def test_packager_creation():
    """测试打包器创建"""
    try:
        from 统一自动打包 import UnifiedPackager
        packager = UnifiedPackager()
        print("✅ 打包器创建成功")
        
        # 测试基本方法
        packager.log_message("测试日志")
        print("✅ 日志功能正常")
        
        return True
    except Exception as e:
        print(f"❌ 打包器创建失败: {e}")
        return False

def test_core_methods():
    """测试核心方法"""
    try:
        from 统一自动打包 import UnifiedPackager
        packager = UnifiedPackager()
        
        # 测试关键方法是否存在
        methods = [
            'check_dependencies',
            'install_missing_dependencies',
            'scan_project_imports',
            'setup_directories',
            'generate_spec_file',
            'run_pyinstaller',
            'finalize_package',
            'verify_package_result',
            'package_command_line'
        ]
        
        for method in methods:
            if hasattr(packager, method):
                print(f"✅ {method}")
            else:
                print(f"❌ {method}")
                return False
        
        return True
    except Exception as e:
        print(f"❌ 核心方法测试失败: {e}")
        return False

def test_gui_availability():
    """测试GUI可用性"""
    try:
        from 统一自动打包 import GUI_AVAILABLE, PackageGUI
        
        if GUI_AVAILABLE:
            print("✅ GUI模块可用")
            print("✅ PackageGUI类存在")
            return True
        else:
            print("⚠️  GUI模块不可用（tkinter未安装）")
            return True  # 这不算错误
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        return False

def test_command_line_functions():
    """测试命令行功能"""
    try:
        from 统一自动打包 import main, show_menu, launch_gui
        print("✅ main函数存在")
        print("✅ show_menu函数存在")
        print("✅ launch_gui函数存在")
        return True
    except Exception as e:
        print(f"❌ 命令行功能测试失败: {e}")
        return False

def check_original_files():
    """检查原始文件"""
    from pathlib import Path
    
    original_files = [
        "自动打包.py",
        "自动打包GUI.py", 
        "package_app.py",
        "安装缺失依赖.py",
        "验证打包结果.py",
        "全面依赖检查.py"
    ]
    
    current_dir = Path(__file__).parent
    
    print("📁 原始文件检查:")
    for filename in original_files:
        file_path = current_dir / filename
        status = "存在" if file_path.exists() else "缺失"
        print(f"  {filename}: {status}")
    
    return True

def main():
    print("=" * 60)
    print("统一自动打包器 - 快速功能验证")
    print("=" * 60)
    
    tests = [
        ("基本导入", test_basic_import),
        ("打包器创建", test_packager_creation),
        ("核心方法", test_core_methods),
        ("GUI可用性", test_gui_availability),
        ("命令行功能", test_command_line_functions),
        ("原始文件", check_original_files),
    ]
    
    passed = 0
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        if test_func():
            passed += 1
    
    print(f"\n📊 结果: {passed}/{len(tests)} 通过")
    
    if passed == len(tests):
        print("🎉 所有功能验证通过！")
        print("✅ 统一自动打包器集成完整且功能正常")
    else:
        print("⚠️  部分功能验证失败")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
