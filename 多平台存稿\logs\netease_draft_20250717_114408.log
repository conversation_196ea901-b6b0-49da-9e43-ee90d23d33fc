[2025-07-17 11:44:09] [INFO] 🚀 启动多平台存稿工具...
[2025-07-17 11:44:10] [INFO] 🔍 [调试] 当前平台: toutiao
[2025-07-17 11:44:10] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-07-17 11:44:10] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-07-17 11:44:10] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-17 11:44:10] [INFO] 🔄 发现旧数据文件: C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\account_data.json
[2025-07-17 11:44:10] [INFO] ℹ️ 新数据文件已存在且更新，跳过迁移: C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\account_data.json
[2025-07-17 11:44:10] [INFO] 已从toutiao平台加载存稿详情数据，共 27 个账号
[2025-07-17 11:44:11] [INFO] 已确保所有必要目录存在
[2025-07-17 11:44:11] [INFO] 🔍 [调试] 当前平台: toutiao
[2025-07-17 11:44:11] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-07-17 11:44:11] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-07-17 11:44:11] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-17 11:44:11] [INFO] 🔍 [调试] 更新账号树 - 当前平台: toutiao
[2025-07-17 11:44:11] [INFO] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-17 11:44:11] [INFO] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-07-17 11:44:11] [INFO] 已加载账号数据: 87 条记录
[2025-07-17 11:44:11] [INFO] 🔍 [调试] 账号数据为空或未加载
[2025-07-17 11:44:11] [INFO] ✅ 初始UI更新完成
[2025-07-17 11:45:16] [INFO] 📊 开始导出头条号平台账号数据...
[2025-07-17 11:45:17] [INFO] ⚠️ 未找到toutiao平台特定数据，返回所有数据
[2025-07-17 11:45:17] [INFO] 📊 Excel导出调试: 获取到 87 条头条号账号数据
[2025-07-17 11:45:17] [INFO] 📊 Excel导出调试: 数据示例 - {'用户名': '葵葵杂聊', '总粉丝': '2', '总播放': '4343', '总收益': '0.26', '草稿箱': '6', '昨日播放': '2272', '昨日粉丝': '1', '昨日收益': '0.21', '七日收益数据': {'2025-05-13': '0.0', '2025-05-14': '0.0', '2025-05-15': '0.0', '2025-05-16': '0.0', '2025-05-17': '0.0', '2025-05-18': '0.05', '2025-05-19': '0.21'}, '更新时间': '2025-05-20 13:00:55', '状态': '已查询', '账号': '15008083451 网易'}
[2025-07-17 11:45:17] [INFO] 📊 Excel导出调试: 转换后字典包含 87 个账号
[2025-07-17 11:45:17] [INFO] ✅ 头条号账号数据已导出到: C:/Users/<USER>/Downloads/头条号账号数据_20250717_114508.xlsx
[2025-07-17 11:47:37] [INFO] 平台切换前保存 toutiao 的设置和存稿详情数据
[2025-07-17 11:47:37] [INFO] ✅ 设置已静默保存
[2025-07-17 11:47:37] [INFO] 已确保所有必要目录存在
[2025-07-17 11:47:37] [INFO] 正在更新账号管理器，当前平台: netease
[2025-07-17 11:47:37] [INFO] 账号目录: D:/网易号全自动/网易号账号
[2025-07-17 11:47:37] [INFO] 已加载 19 个netease平台账号
[2025-07-17 11:47:37] [INFO] 🔍 [调试] 当前平台: netease
[2025-07-17 11:47:37] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/网易号账号
[2025-07-17 11:47:37] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\网易号账号
[2025-07-17 11:47:37] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\网易号账号\netease_account_data.json
[2025-07-17 11:47:37] [INFO] 已加载账号数据: 25 条记录
[2025-07-17 11:47:37] [INFO] 账号数据文件路径: D:\网易号全自动\网易号账号\netease_account_data.json
[2025-07-17 11:47:37] [INFO] 已更新数据查询管理器的AccountData引用
[2025-07-17 11:47:37] [INFO] 已更新Excel导出管理器的AccountData引用
[2025-07-17 11:47:37] [INFO] 已从netease平台加载存稿详情数据，共 24 个账号
[2025-07-17 11:47:37] [INFO] [netease] ✅ 视频分配跟踪器初始化成功
[2025-07-17 11:47:37] [INFO] 已切换到平台: 网易号平台
[2025-07-17 11:47:37] [INFO] [netease] ✅ 视频分配跟踪器初始化成功
[2025-07-17 11:47:37] [INFO] 🔍 [调试] 当前平台: netease
[2025-07-17 11:47:37] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/网易号账号
[2025-07-17 11:47:37] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\网易号账号
[2025-07-17 11:47:37] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\网易号账号\netease_account_data.json
[2025-07-17 11:47:37] [INFO] 🔍 [调试] 更新账号树 - 当前平台: netease
[2025-07-17 11:47:37] [INFO] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\网易号账号\netease_account_data.json
[2025-07-17 11:47:37] [INFO] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-07-17 11:47:37] [INFO] 已加载账号数据: 25 条记录
[2025-07-17 11:47:37] [INFO] 🔍 [调试] 账号数据为空或未加载
[2025-07-17 11:47:37] [INFO] ✅ 设置已静默保存
[2025-07-17 11:47:40] [INFO] 📊 开始导出网易号平台账号数据...
[2025-07-17 11:47:40] [INFO] 📊 Excel导出: 筛选出 24 条netease平台数据
[2025-07-17 11:47:40] [INFO] 📊 Excel导出调试: 获取到 24 条网易号账号数据
[2025-07-17 11:47:40] [INFO] 📊 Excel导出调试: 数据示例 - {'用户名': '葵葵杂聊', '总粉丝': '2', '总播放': '4343', '总收益': '0.26', '草稿箱': '6', '昨日播放': '2272', '昨日粉丝': '1', '昨日收益': '0.21', '七日收益数据': {'2025-05-13': '0.0', '2025-05-14': '0.0', '2025-05-15': '0.0', '2025-05-16': '0.0', '2025-05-17': '0.0', '2025-05-18': '0.05', '2025-05-19': '0.21'}, '更新时间': '2025-05-20 13:00:55', '状态': '已查询', '账号': '15008083451 网易'}
[2025-07-17 11:47:40] [INFO] 📊 Excel导出调试: 转换后字典包含 24 个账号
[2025-07-17 11:47:40] [INFO] ✅ 网易号账号数据已导出到: C:/Users/<USER>/Downloads/网易号账号数据_20250717_114739.xlsx
[2025-07-17 11:48:29] [INFO] 平台切换前保存 netease 的设置和存稿详情数据
[2025-07-17 11:48:29] [INFO] ✅ 设置已静默保存
[2025-07-17 11:48:29] [INFO] 已确保所有必要目录存在
[2025-07-17 11:48:29] [INFO] 正在更新账号管理器，当前平台: toutiao
[2025-07-17 11:48:29] [INFO] 账号目录: D:/网易号全自动/头条号账号
[2025-07-17 11:48:29] [INFO] 已加载 60 个toutiao平台账号
[2025-07-17 11:48:29] [INFO] 🔍 [调试] 当前平台: toutiao
[2025-07-17 11:48:29] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-07-17 11:48:29] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-07-17 11:48:29] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-17 11:48:29] [INFO] 已加载账号数据: 87 条记录
[2025-07-17 11:48:29] [INFO] 账号数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-17 11:48:29] [INFO] 已更新数据查询管理器的AccountData引用
[2025-07-17 11:48:29] [INFO] 已更新Excel导出管理器的AccountData引用
[2025-07-17 11:48:29] [INFO] 已从toutiao平台加载存稿详情数据，共 27 个账号
[2025-07-17 11:48:29] [INFO] 已切换到平台: 今日头条平台
[2025-07-17 11:48:29] [INFO] 🔍 [调试] 当前平台: toutiao
[2025-07-17 11:48:29] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-07-17 11:48:29] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-07-17 11:48:29] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-17 11:48:29] [INFO] 🔍 [调试] 更新账号树 - 当前平台: toutiao
[2025-07-17 11:48:29] [INFO] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-17 11:48:29] [INFO] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-07-17 11:48:29] [INFO] 已加载账号数据: 87 条记录
[2025-07-17 11:48:29] [INFO] 🔍 [调试] 账号数据为空或未加载
[2025-07-17 11:48:29] [INFO] ✅ 设置已静默保存
[2025-07-17 11:48:56] [INFO] 已加载账号数据: 87 条记录
[2025-07-17 11:50:05] [INFO] 已加载账号数据: 87 条记录
