#!/usr/bin/env python3
"""
依赖检查脚本 - 检查所有必要的模块是否可用
"""

import sys
import importlib
import subprocess

def check_module(module_name):
    """检查模块是否可用"""
    try:
        importlib.import_module(module_name)
        return True, None
    except ImportError as e:
        return False, str(e)

def install_module(module_name):
    """尝试安装缺失的模块"""
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', module_name])
        return True
    except subprocess.CalledProcessError:
        return False

# 关键依赖列表
critical_modules = [
    'jaraco.text',
    'jaraco.functools', 
    'jaraco.context',
    'jaraco.collections',
    'jaraco.itertools',
    'jaraco.classes',
    'pkg_resources',
    'setuptools',
    'packaging',
    'pyinstaller',
]

# 可选依赖列表
optional_modules = [
    'tkinter',
    'selenium',
    'PIL',
    'requests',
    'jieba',
    'moviepy',
    'pyautogui',
]

def main():
    print("=" * 60)
    print("依赖检查和修复工具")
    print("=" * 60)
    
    missing_critical = []
    missing_optional = []
    
    print("\n🔍 检查关键依赖...")
    for module in critical_modules:
        available, error = check_module(module)
        if available:
            print(f"  ✅ {module}")
        else:
            print(f"  ❌ {module} - {error}")
            missing_critical.append(module)
    
    print("\n🔍 检查可选依赖...")
    for module in optional_modules:
        available, error = check_module(module)
        if available:
            print(f"  ✅ {module}")
        else:
            print(f"  ⚠️  {module} - {error}")
            missing_optional.append(module)
    
    # 尝试修复缺失的关键依赖
    if missing_critical:
        print(f"\n🔧 发现 {len(missing_critical)} 个缺失的关键依赖，尝试安装...")
        for module in missing_critical:
            print(f"  📦 安装 {module}...")
            # 特殊处理jaraco模块
            if module.startswith('jaraco.'):
                install_name = 'jaraco.text' if 'text' in module else module.split('.')[0]
            else:
                install_name = module
            
            if install_module(install_name):
                print(f"  ✅ {module} 安装成功")
            else:
                print(f"  ❌ {module} 安装失败")
    
    # 重新检查
    print("\n🔍 重新检查关键依赖...")
    all_good = True
    for module in critical_modules:
        available, error = check_module(module)
        if available:
            print(f"  ✅ {module}")
        else:
            print(f"  ❌ {module} - {error}")
            all_good = False
    
    print("\n" + "=" * 60)
    if all_good:
        print("🎉 所有关键依赖都已就绪！可以进行打包。")
    else:
        print("❌ 仍有关键依赖缺失，请手动安装后再试。")
        print("\n建议手动执行以下命令:")
        print("pip install jaraco.text setuptools packaging")
    print("=" * 60)

if __name__ == "__main__":
    main()
