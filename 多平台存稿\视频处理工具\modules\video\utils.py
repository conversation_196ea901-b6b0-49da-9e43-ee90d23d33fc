"""
视频工具模块 - 提供视频处理相关的工具函数
"""

import os
import re
import json
import subprocess
from typing import Dict, List, Tuple, Optional, Any

def get_video_info(video_path: str) -> Dict[str, Any]:
    """
    获取视频信息
    
    Args:
        video_path: 视频文件路径
        
    Returns:
        Dict: 视频信息，包含width、height、duration、codec等
    """
    try:
        # 使用ffprobe获取视频信息
        cmd = [
            'ffprobe',
            '-v', 'error',
            '-select_streams', 'v:0',
            '-show_entries', 'stream=width,height,duration,codec_name,bit_rate',
            '-show_entries', 'format=duration,size,bit_rate',
            '-of', 'json',
            video_path
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            info = json.loads(result.stdout)
            
            # 提取流信息
            stream = info.get('streams', [{}])[0]
            format_info = info.get('format', {})
            
            # 获取基本信息
            width = int(stream.get('width', 0))
            height = int(stream.get('height', 0))
            
            # 优先使用流的持续时间，如果没有则使用格式的持续时间
            duration = float(stream.get('duration', format_info.get('duration', 0)))
            
            # 获取编解码器
            codec = stream.get('codec_name', 'unknown')
            
            # 获取比特率
            bit_rate = int(stream.get('bit_rate', format_info.get('bit_rate', 0)))
            
            # 获取文件大小
            size = int(format_info.get('size', 0))
            
            return {
                'width': width,
                'height': height,
                'duration': duration,
                'codec': codec,
                'bit_rate': bit_rate,
                'size': size,
                'aspect_ratio': width / height if height > 0 else 0
            }
        else:
            raise Exception(f"ffprobe返回错误: {result.stderr}")
    except Exception as e:
        print(f"获取视频信息失败: {str(e)}")
        return {}

def format_video_info(info: Dict[str, Any]) -> str:
    """
    格式化视频信息为可读字符串
    
    Args:
        info: 视频信息字典
        
    Returns:
        str: 格式化后的视频信息
    """
    if not info:
        return "无法获取视频信息"
    
    # 格式化时长
    duration = info.get('duration', 0)
    hours = int(duration // 3600)
    minutes = int((duration % 3600) // 60)
    seconds = int(duration % 60)
    
    if hours > 0:
        duration_str = f"{hours}:{minutes:02d}:{seconds:02d}"
    else:
        duration_str = f"{minutes}:{seconds:02d}"
    
    # 格式化比特率
    bit_rate = info.get('bit_rate', 0)
    if bit_rate > 0:
        bit_rate_str = f"{bit_rate / 1000000:.2f} Mbps"
    else:
        bit_rate_str = "未知"
    
    # 格式化文件大小
    size = info.get('size', 0)
    if size > 1024 * 1024 * 1024:
        size_str = f"{size / (1024 * 1024 * 1024):.2f} GB"
    elif size > 1024 * 1024:
        size_str = f"{size / (1024 * 1024):.2f} MB"
    elif size > 1024:
        size_str = f"{size / 1024:.2f} KB"
    else:
        size_str = f"{size} B"
    
    # 格式化宽高比
    aspect_ratio = info.get('aspect_ratio', 0)
    if aspect_ratio > 0:
        # 检查是否是常见比例
        if abs(aspect_ratio - 16/9) < 0.01:
            ratio_str = "16:9"
        elif abs(aspect_ratio - 4/3) < 0.01:
            ratio_str = "4:3"
        elif abs(aspect_ratio - 1) < 0.01:
            ratio_str = "1:1"
        elif abs(aspect_ratio - 9/16) < 0.01:
            ratio_str = "9:16"
        else:
            ratio_str = f"{aspect_ratio:.2f}"
    else:
        ratio_str = "未知"
    
    return (
        f"分辨率: {info.get('width', 0)}x{info.get('height', 0)}\n"
        f"时长: {duration_str}\n"
        f"编码: {info.get('codec', '未知')}\n"
        f"比特率: {bit_rate_str}\n"
        f"文件大小: {size_str}\n"
        f"宽高比: {ratio_str}"
    )

def is_video_file(file_path: str) -> bool:
    """
    检查文件是否是视频文件
    
    Args:
        file_path: 文件路径
        
    Returns:
        bool: 是否是视频文件
    """
    video_extensions = ['.mp4', '.mov', '.avi', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.mpg', '.mpeg', '.3gp']
    _, ext = os.path.splitext(file_path)
    return ext.lower() in video_extensions

def get_video_files(directory: str) -> List[str]:
    """
    获取目录中的所有视频文件
    
    Args:
        directory: 目录路径
        
    Returns:
        List[str]: 视频文件列表
    """
    if not os.path.exists(directory):
        return []
    
    video_files = []
    for file in os.listdir(directory):
        file_path = os.path.join(directory, file)
        if os.path.isfile(file_path) and is_video_file(file_path):
            video_files.append(file)
    
    return video_files
