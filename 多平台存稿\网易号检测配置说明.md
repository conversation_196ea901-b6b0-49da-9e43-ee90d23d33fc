# 网易号视频上传检测配置说明

## 配置概述

本文档详细说明了网易号视频上传检测功能的各种配置选项和参数设置。

## 核心配置参数

### 1. 检测超时设置

```python
# 默认超时时间（秒）
DEFAULT_TIMEOUT = 180  # 3分钟

# 长视频超时时间（秒）
LONG_VIDEO_TIMEOUT = 600  # 10分钟

# 短视频超时时间（秒）
SHORT_VIDEO_TIMEOUT = 120  # 2分钟
```

**使用示例**:
```python
# 使用默认超时
success = uploader._wait_for_upload_completion_improved()

# 自定义超时时间
success = uploader._wait_for_upload_completion_improved(timeout=300)
```

### 2. 检测间隔设置

```python
# 检测间隔（秒）
CHECK_INTERVAL = 3  # 每3秒检测一次

# 二次验证等待时间（秒）
SECOND_CHECK_DELAY = 3  # 二次验证前等待3秒
```

### 3. 目标元素配置

```python
# 主要检测XPath
TARGET_XPATH = '//*[@id="root"]/div/div[2]/div/div[2]/div/div/div[1]/div/div[2]/div[1]/div[2]/div[2]/div'

# 目标文本
TARGET_TEXT = "推荐封面"

# 备用XPath列表
BACKUP_XPATHS = [
    '//*[@id="root"]/div/div[2]/div/div[2]/div/div/div[1]/div/div[2]/div[1]/div[2]/div[2]/ul/li[4]',
    '//*[contains(text(), "推荐封面")]',
    '//*[contains(@class, "cover") and contains(text(), "推荐")]'
]
```

## 日志配置

### 1. 日志级别设置

```python
import logging

# 设置不同的日志级别
LOG_LEVELS = {
    'DEBUG': logging.DEBUG,      # 详细调试信息
    'INFO': logging.INFO,        # 一般信息
    'WARNING': logging.WARNING,  # 警告信息
    'ERROR': logging.ERROR       # 错误信息
}

# 默认日志级别
DEFAULT_LOG_LEVEL = 'INFO'
```

### 2. 日志输出配置

```python
# 日志文件配置
LOG_CONFIG = {
    'filename': 'upload_detection.log',
    'encoding': 'utf-8',
    'max_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5,
    'format': '%(asctime)s - %(levelname)s - %(message)s'
}

# 控制台输出配置
CONSOLE_LOG_CONFIG = {
    'enabled': True,
    'level': 'INFO',
    'format': '%(asctime)s - %(message)s'
}
```

## 浏览器配置

### 1. Chrome浏览器选项

```python
def get_chrome_options(headless=False, user_data_dir=None):
    """获取Chrome浏览器配置选项"""
    options = Options()
    
    # 基本设置
    if headless:
        options.add_argument('--headless')
    
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-gpu')
    options.add_argument('--window-size=1920,1080')
    
    # 用户数据目录（用于多浏览器隔离）
    if user_data_dir:
        options.add_argument(f'--user-data-dir={user_data_dir}')
    
    # 性能优化
    options.add_argument('--disable-extensions')
    options.add_argument('--disable-plugins')
    options.add_argument('--disable-images')
    options.add_argument('--disable-javascript')  # 可选，可能影响检测
    
    return options
```

### 2. 多浏览器并发配置

```python
# 并发浏览器配置
CONCURRENT_CONFIG = {
    'max_browsers': 5,           # 最大并发浏览器数
    'browser_timeout': 300,      # 单个浏览器超时时间
    'isolation_enabled': True,   # 启用浏览器隔离
    'user_data_prefix': './chrome_data_'  # 用户数据目录前缀
}
```

## 检测方法配置

### 1. 检测方法优先级

```python
# 检测方法配置
DETECTION_METHODS = {
    'xpath': {
        'enabled': True,
        'priority': 1,
        'timeout': 5
    },
    'javascript': {
        'enabled': True,
        'priority': 2,
        'timeout': 5
    },
    'backup': {
        'enabled': True,
        'priority': 3,
        'timeout': 10
    }
}
```

### 2. 二次验证配置

```python
# 二次验证配置
DOUBLE_CHECK_CONFIG = {
    'enabled': True,              # 启用二次验证
    'delay': 3,                   # 验证前等待时间（秒）
    'required_methods': ['xpath'], # 需要二次验证的方法
    'max_attempts': 2             # 最大验证次数
}
```

## 错误处理配置

### 1. 重试配置

```python
# 重试配置
RETRY_CONFIG = {
    'max_retries': 3,           # 最大重试次数
    'retry_delay': 5,           # 重试间隔（秒）
    'exponential_backoff': True, # 指数退避
    'retry_on_errors': [
        'TimeoutException',
        'NoSuchElementException',
        'WebDriverException'
    ]
}
```

### 2. 截图配置

```python
# 截图配置
SCREENSHOT_CONFIG = {
    'enabled': True,
    'save_on_failure': True,
    'save_on_success': False,
    'directory': './screenshots',
    'filename_format': 'detection_{timestamp}_{result}.png',
    'max_files': 100  # 最大保存文件数
}
```

## 性能配置

### 1. 资源限制

```python
# 资源限制配置
RESOURCE_CONFIG = {
    'max_memory_mb': 512,        # 最大内存使用（MB）
    'max_cpu_percent': 50,       # 最大CPU使用率（%）
    'max_detection_time': 300,   # 单次检测最大时间（秒）
    'cleanup_interval': 3600     # 资源清理间隔（秒）
}
```

### 2. 缓存配置

```python
# 缓存配置
CACHE_CONFIG = {
    'enabled': True,
    'cache_size': 100,           # 缓存大小
    'cache_ttl': 300,            # 缓存生存时间（秒）
    'cache_detection_results': True
}
```

## 环境配置

### 1. 开发环境配置

```python
# 开发环境配置
DEV_CONFIG = {
    'debug_mode': True,
    'verbose_logging': True,
    'save_all_screenshots': True,
    'disable_headless': True,
    'extended_timeout': True
}
```

### 2. 生产环境配置

```python
# 生产环境配置
PROD_CONFIG = {
    'debug_mode': False,
    'verbose_logging': False,
    'save_all_screenshots': False,
    'disable_headless': False,
    'extended_timeout': False,
    'performance_monitoring': True
}
```

## 配置文件示例

### config.json
```json
{
    "detection": {
        "timeout": 180,
        "check_interval": 3,
        "target_xpath": "//*[@id=\"root\"]/div/div[2]/div/div[2]/div/div/div[1]/div/div[2]/div[1]/div[2]/div[2]/div",
        "target_text": "推荐封面"
    },
    "browser": {
        "headless": true,
        "window_size": "1920,1080",
        "disable_images": true,
        "max_concurrent": 3
    },
    "logging": {
        "level": "INFO",
        "file": "detection.log",
        "console": true
    },
    "retry": {
        "max_attempts": 3,
        "delay": 5
    }
}
```

### 配置加载示例

```python
import json

def load_config(config_file='config.json'):
    """加载配置文件"""
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        return config
    except FileNotFoundError:
        print(f"配置文件 {config_file} 未找到，使用默认配置")
        return get_default_config()
    except json.JSONDecodeError as e:
        print(f"配置文件格式错误: {e}")
        return get_default_config()

def get_default_config():
    """获取默认配置"""
    return {
        "detection": {
            "timeout": 180,
            "check_interval": 3,
            "target_xpath": "//*[@id=\"root\"]/div/div[2]/div/div[2]/div/div/div[1]/div/div[2]/div[1]/div[2]/div[2]/div",
            "target_text": "推荐封面"
        },
        "browser": {
            "headless": True,
            "window_size": "1920,1080"
        },
        "logging": {
            "level": "INFO"
        }
    }
```

## 配置验证

### 配置验证函数

```python
def validate_config(config):
    """验证配置参数"""
    errors = []
    
    # 验证检测配置
    if config.get('detection', {}).get('timeout', 0) <= 0:
        errors.append("检测超时时间必须大于0")
    
    if config.get('detection', {}).get('check_interval', 0) <= 0:
        errors.append("检测间隔必须大于0")
    
    # 验证XPath
    xpath = config.get('detection', {}).get('target_xpath', '')
    if not xpath or not xpath.startswith('/'):
        errors.append("目标XPath格式不正确")
    
    # 验证目标文本
    target_text = config.get('detection', {}).get('target_text', '')
    if not target_text:
        errors.append("目标文本不能为空")
    
    return errors

# 使用示例
config = load_config()
errors = validate_config(config)
if errors:
    print("配置验证失败:")
    for error in errors:
        print(f"  - {error}")
else:
    print("配置验证通过")
```

## 最佳实践

### 1. 配置管理建议
- 使用配置文件而不是硬编码
- 为不同环境准备不同的配置文件
- 定期验证配置的有效性
- 记录配置变更历史

### 2. 性能优化建议
- 根据视频大小调整超时时间
- 在生产环境中禁用详细日志
- 合理设置并发浏览器数量
- 定期清理日志和截图文件

### 3. 安全配置建议
- 不在配置文件中存储敏感信息
- 限制日志文件的访问权限
- 定期更新浏览器版本
- 监控资源使用情况

## 故障排除

### 常见配置问题
1. **XPath失效**: 网站更新导致元素路径变化
2. **超时设置过短**: 网络慢或视频大时需要更长时间
3. **并发数过高**: 系统资源不足导致检测失败
4. **日志文件过大**: 需要配置日志轮转

### 解决方案
1. 定期检查和更新XPath
2. 根据实际情况调整超时时间
3. 监控系统资源使用情况
4. 配置合适的日志管理策略
