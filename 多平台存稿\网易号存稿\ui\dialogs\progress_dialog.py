"""
进度条对话框模块 - 显示存稿进度情况
"""

import tkinter as tk
from tkinter import ttk
import time
from typing import Dict, Any, Callable, Optional

class ProgressDialog:
    """进度条对话框类，用于显示存稿进度情况"""

    def __init__(self, parent, account_progress=None, log_callback=None, config_manager=None):
        """
        初始化进度条对话框

        Args:
            parent: 父窗口
            account_progress: 账号进度数据
            log_callback: 日志回调函数
            config_manager: 配置管理器
        """
        self.parent = parent
        self.account_progress = account_progress or {}
        self.log_callback = log_callback
        self.config_manager = config_manager

        # 保存对主UI的引用，用于实时获取最新进度数据
        self.main_ui = parent

        # 创建对话框
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("存稿进度")
        self.dialog.geometry("800x600")
        self.dialog.minsize(600, 400)

        # 设置模态，但不阻塞主窗口
        self.dialog.transient(parent)

        # 绑定关闭事件
        self.dialog.protocol("WM_DELETE_WINDOW", self.on_close)

        # 居中显示
        self.center_window()

        # 创建UI
        self.create_ui()

        # 更新进度数据
        if account_progress:
            self.update_progress_data(self.account_progress)
            self.log("已加载当前进度数据")

        # 自动刷新标志已在create_ui中初始化

        # 初始化更新间隔（将在create_ui中从配置读取）
        self.update_interval = 1.0  # 默认更新间隔（秒）

        # 初始化更新状态
        self.is_updating = False

        # 启动自动刷新
        if self.auto_refresh.get():
            self.start_auto_refresh()

    def on_close(self):
        """关闭对话框"""
        # 停止自动刷新
        self.stop_auto_refresh()

        # 关闭对话框
        self.dialog.destroy()

    def log(self, message):
        """记录日志"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def center_window(self):
        """居中显示窗口"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")

    def create_ui(self):
        """创建用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.dialog, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建工具栏
        toolbar = ttk.Frame(main_frame)
        toolbar.pack(fill=tk.X, pady=(0, 10))

        # 刷新按钮
        refresh_button = ttk.Button(toolbar, text="刷新", command=self.refresh_progress)
        refresh_button.pack(side=tk.LEFT, padx=5)

        # 自动刷新开关
        self.auto_refresh = tk.BooleanVar(value=True)
        auto_refresh_check = ttk.Checkbutton(
            toolbar,
            text="自动刷新",
            variable=self.auto_refresh,
            command=self.toggle_auto_refresh
        )
        auto_refresh_check.pack(side=tk.LEFT, padx=5)

        # 刷新间隔
        ttk.Label(toolbar, text="刷新间隔:").pack(side=tk.LEFT, padx=(10, 0))

        # 从配置中读取刷新间隔设置
        refresh_interval_value = "1.0"  # 默认值
        if self.config_manager:
            refresh_interval_value = str(self.config_manager.get("progress_refresh_interval", 1.0))

        self.refresh_interval = tk.StringVar(value=refresh_interval_value)
        interval_combobox = ttk.Combobox(toolbar, textvariable=self.refresh_interval, width=5, state="readonly")
        interval_combobox["values"] = ("0.5", "1.0", "2.0", "5.0")
        interval_combobox.pack(side=tk.LEFT, padx=(0, 5))
        interval_combobox.bind("<<ComboboxSelected>>", self.on_interval_changed)

        # 创建总进度框架
        total_progress_frame = ttk.LabelFrame(main_frame, text="总进度")
        total_progress_frame.pack(fill=tk.X, pady=(0, 10))

        # 总进度条
        total_progress_inner = ttk.Frame(total_progress_frame, padding=10)
        total_progress_inner.pack(fill=tk.X)

        ttk.Label(total_progress_inner, text="总进度:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.total_progress_bar = ttk.Progressbar(total_progress_inner, orient=tk.HORIZONTAL, length=400, mode="determinate")
        self.total_progress_bar.grid(row=0, column=1, sticky=tk.EW, padx=5, pady=5)
        self.total_progress_label = ttk.Label(total_progress_inner, text="0%")
        self.total_progress_label.grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)

        # 总状态
        ttk.Label(total_progress_inner, text="总状态:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.total_status_label = ttk.Label(total_progress_inner, text="就绪")
        self.total_status_label.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        # 设置列权重
        total_progress_inner.columnconfigure(1, weight=1)

        # 创建进度列表框架
        progress_frame = ttk.LabelFrame(main_frame, text="账号进度")
        progress_frame.pack(fill=tk.BOTH, expand=True)

        # 创建进度列表（使用Treeview）
        columns = ("账号", "状态", "详情", "最后更新")
        self.progress_tree = ttk.Treeview(progress_frame, columns=columns, show="headings", height=10)

        # 设置列标题和宽度
        self.progress_tree.heading("账号", text="账号")
        self.progress_tree.heading("状态", text="状态")
        self.progress_tree.heading("详情", text="存稿数量")
        self.progress_tree.heading("最后更新", text="最后更新")

        self.progress_tree.column("账号", width=180, anchor=tk.W)
        self.progress_tree.column("状态", width=120, anchor=tk.CENTER)
        self.progress_tree.column("详情", width=100, anchor=tk.CENTER)
        self.progress_tree.column("最后更新", width=150, anchor=tk.CENTER)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(progress_frame, orient=tk.VERTICAL, command=self.progress_tree.yview)
        self.progress_tree.configure(yscrollcommand=scrollbar.set)

        # 布局
        self.progress_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 5), pady=5)

        # 配置标签样式
        self.progress_tree.tag_configure("success", foreground="green")
        self.progress_tree.tag_configure("error", foreground="red")
        self.progress_tree.tag_configure("warning", foreground="orange")
        self.progress_tree.tag_configure("processing", foreground="blue")

    def toggle_auto_refresh(self):
        """切换自动刷新状态"""
        if self.auto_refresh.get():
            self.start_auto_refresh()
        else:
            self.stop_auto_refresh()

    def on_interval_changed(self, event):
        """刷新间隔变化事件"""
        try:
            # 获取新的刷新间隔
            new_interval = float(self.refresh_interval.get())
            self.update_interval = new_interval  # 更新实例变量

            # 保存到配置
            if self.config_manager:
                self.config_manager.set("progress_refresh_interval", new_interval)
                self.log(f"已保存刷新间隔设置: {new_interval}秒")

            # 重启自动刷新
            if self.auto_refresh.get():
                self.stop_auto_refresh()
                self.start_auto_refresh()
        except ValueError as e:
            self.log(f"设置刷新间隔时出错: {str(e)}")
            pass

    def start_auto_refresh(self):
        """启动自动刷新"""
        if not self.is_updating:
            self.is_updating = True
            self.auto_refresh_task()

    def stop_auto_refresh(self):
        """停止自动刷新"""
        self.is_updating = False

    def auto_refresh_task(self):
        """自动刷新任务"""
        if self.is_updating and self.dialog.winfo_exists():
            self.refresh_progress()
            self.dialog.after(int(self.update_interval * 1000), self.auto_refresh_task)

    def refresh_progress(self):
        """刷新进度显示"""
        # 从主UI获取最新的进度数据
        if hasattr(self.main_ui, 'account_progress') and self.main_ui.account_progress:
            self.account_progress = self.main_ui.account_progress
            self.log("已从主UI获取最新进度数据")

        # 更新显示
        self.update_progress_tree()
        self.update_total_progress()

    def update_progress_data(self, progress_data):
        """
        更新进度数据

        Args:
            progress_data: 进度数据字典，格式为 {账号: {progress: 进度, status: 状态, details: 详情, last_update: 最后更新时间}}
        """
        if progress_data:
            self.account_progress = progress_data
            self.update_progress_tree()
            self.update_total_progress()

            # 记录日志
            self.log("进度数据已更新")

    def update_progress_tree(self):
        """更新进度列表"""
        # 清空现有项目
        for item in self.progress_tree.get_children():
            self.progress_tree.delete(item)

        # 添加进度数据
        for account, data in self.account_progress.items():
            progress = data.get("progress", 0)
            status = data.get("status", "未知")
            details = data.get("details", "")
            last_update = data.get("last_update", "")
            successful_drafts = data.get("successful_drafts", 0)

            # 如果存稿成功数为空字符串，设置为0
            if successful_drafts == "":
                successful_drafts = 0

            # 准备显示文本
            account_display = account

            # 存稿数量显示
            draft_count_display = str(successful_drafts) if successful_drafts > 0 else "0"

            # 插入数据
            item = self.progress_tree.insert("", "end", values=(
                account_display,
                status,
                draft_count_display,
                last_update
            ))

            # 根据状态设置标签
            if status == "完成":
                self.progress_tree.item(item, tags=("success",))
            elif status == "错误" or status == "失败":
                self.progress_tree.item(item, tags=("error",))
            elif status == "警告":
                self.progress_tree.item(item, tags=("warning",))
            elif status == "处理中" or status == "上传中" or status == "设置中":
                self.progress_tree.item(item, tags=("processing",))

    def update_total_progress(self):
        """更新总进度"""
        if not self.account_progress:
            self.total_progress_bar["value"] = 0
            self.total_progress_label.config(text="0%")
            self.total_status_label.config(text="就绪")
            return

        # 计算总进度
        total_progress = 0
        completed_count = 0
        error_count = 0
        processing_count = 0
        total_successful_drafts = 0  # 总成功存稿数量

        for data in self.account_progress.values():
            progress = data.get("progress", 0)
            status = data.get("status", "")

            # 获取成功存稿数
            successful_drafts = data.get("successful_drafts", 0)

            # 如果存稿成功数为空字符串，设置为0
            if successful_drafts == "":
                successful_drafts = 0

            # 累加总成功存稿数
            total_successful_drafts += successful_drafts

            total_progress += progress

            if status == "完成":
                completed_count += 1
            elif status == "错误" or status == "失败":
                error_count += 1
            elif status == "处理中" or status == "上传中" or status == "设置中":
                processing_count += 1

        # 计算平均进度
        avg_progress = total_progress / len(self.account_progress) if self.account_progress else 0

        # 更新进度条
        self.total_progress_bar["value"] = avg_progress

        # 根据进度更新样式
        style_name = "TProgressbar"
        if avg_progress < 30:
            style_name = "TProgressbar"
        elif avg_progress < 70:
            style_name = "blue.Horizontal.TProgressbar"
        else:
            style_name = "green.Horizontal.TProgressbar"

        # 设置进度条样式
        self.total_progress_bar["style"] = style_name

        # 更新进度标签
        self.total_progress_label.config(text=f"{int(avg_progress)}%")

        # 更新状态，包含总成功存稿数
        status_text = ""
        if error_count > 0:
            status_text = f"错误 ({error_count}/{len(self.account_progress)})"
            status_color = "red"
        elif processing_count > 0:
            status_text = f"处理中 ({processing_count}/{len(self.account_progress)})"
            status_color = "blue"
        elif completed_count == len(self.account_progress):
            status_text = "全部完成"
            status_color = "green"
        else:
            status_text = f"部分完成 ({completed_count}/{len(self.account_progress)})"
            status_color = "orange"

        # 添加总成功存稿数
        if total_successful_drafts > 0:
            status_text = f"{status_text} [总存稿: {total_successful_drafts}]"

        # 更新状态标签
        self.total_status_label.config(text=status_text, foreground=status_color)
