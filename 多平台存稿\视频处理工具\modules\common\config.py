"""
配置管理模块 - 处理配置的保存和加载
"""

import os
import tkinter as tk
from typing import List, Dict, Any, Optional, Callable

class ConfigManager:
    """配置管理类，负责保存和加载配置"""

    def __init__(self, config_file: str, tracked_vars: List[tk.Variable], logger=None):
        """
        初始化配置管理器

        Args:
            config_file: 配置文件路径
            tracked_vars: 需要跟踪的变量列表
            logger: 日志记录函数，如果为None则使用print
        """
        self.config_file = config_file
        self.tracked_vars = tracked_vars
        self.log_callback = logger
        self._auto_saving = False

    @property
    def logger(self):
        """获取日志记录函数"""
        return self.log_callback if self.log_callback else print

    def save_config(self, show_message=True) -> bool:
        """
        保存配置到文件

        Args:
            show_message: 是否显示保存成功的消息

        Returns:
            bool: 是否成功保存
        """
        try:
            config_dir = os.path.dirname(self.config_file)
            # 确保配置文件目录存在
            if not os.path.exists(config_dir):
                os.makedirs(config_dir)

            with open(self.config_file, "w", encoding="utf-8") as f:
                for var in self.tracked_vars:
                    # 获取变量名
                    name = str(var).split(".")[-1]
                    value = var.get()

                    # 确保浮点数变量有有效值
                    if isinstance(var, tk.DoubleVar) and (value == "" or value is None):
                        value = 0.0  # 设置默认值

                    f.write(f"{name}={value}\n")

            # 只在需要时显示消息（手动保存或明确要求）
            if show_message and not self._auto_saving:
                self.logger(f"✅ 配置已保存到: {self.config_file}")
            return True
        except Exception as e:
            self.logger(f"⚠️ 保存配置失败: {str(e)}")
            return False

    def load_config(self) -> bool:
        """
        从文件加载配置

        Returns:
            bool: 是否成功加载
        """
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, "r", encoding="utf-8") as f:
                    config = {}
                    for line in f:
                        if "=" in line:
                            key, value = line.strip().split("=", 1)
                            config[key] = value

                # 应用配置到变量
                for var in self.tracked_vars:
                    name = str(var).split(".")[-1]
                    if name in config:
                        value = config[name]

                        # 根据变量类型设置值
                        if isinstance(var, tk.BooleanVar):
                            var.set(value.lower() == "true")
                        elif isinstance(var, tk.IntVar):
                            try:
                                var.set(int(value))
                            except ValueError:
                                pass
                        elif isinstance(var, tk.DoubleVar):
                            try:
                                # 处理空值情况
                                if value == "" or value is None:
                                    # 获取变量的默认值或使用0.0
                                    default_value = 0.0
                                    var.set(default_value)
                                else:
                                    var.set(float(value))
                            except ValueError:
                                # 如果转换失败，设置为默认值
                                var.set(0.0)
                        else:
                            var.set(value)

                self.logger("✅ 已加载保存的配置")
                return True
            return False
        except Exception as e:
            self.logger(f"⚠️ 加载配置失败: {str(e)}")
            return False

    def schedule_auto_save(self, root: tk.Tk, interval: int = 60000) -> None:
        """
        计划自动保存配置

        Args:
            root: Tkinter根窗口
            interval: 自动保存间隔（毫秒）
        """
        # 使用一个内部函数来确保_auto_saving标记在函数调用期间保持一致
        def do_auto_save():
            self._auto_saving = True  # 标记为自动保存
            self.save_config(show_message=False)  # 保存配置但不显示消息
            self._auto_saving = False  # 重置标记
            # 计划下一次保存
            root.after(interval, do_auto_save)

        # 启动自动保存循环
        do_auto_save()
