[2025-08-07 00:57:38] [INFO] 🚀 启动多平台存稿工具...
[2025-08-07 00:57:38] [INFO] ✅ 定时任务管理器UI初始化完成
[2025-08-07 00:57:39] [INFO] ✅ 定时任务管理标签页创建完成
[2025-08-07 00:59:39] [INFO] ✅ 定时任务管理器UI清理完成
25-08-07 00:57:38] [INFO] 已从toutiao平台加载存稿详情数据，共 83 个账号
[2025-08-07 00:57:39] [DEBUG] 🔄 调度器主循环开始
[2025-08-07 00:57:39] [INFO] 🚀 定时任务调度器已启动
[2025-08-07 00:57:39] [INFO] 已确保所有必要目录存在
[2025-08-07 00:57:39] [DEBUG] 🔍 [调试] 当前平台: toutiao
[2025-08-07 00:57:39] [DEBUG] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-08-07 00:57:39] [DEBUG] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-08-07 00:57:39] [SUCCESS] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-08-07 00:57:39] [DEBUG] 🔍 [调试] 更新账号树 - 当前平台: toutiao
[2025-08-07 00:57:39] [DEBUG] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-08-07 00:57:39] [DEBUG] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-08-07 00:57:39] [INFO] 已加载账号数据: 85 条记录
[2025-08-07 00:57:39] [DEBUG] 🔍 [调试] 账号数据为空或未加载
[2025-08-07 00:57:39] [SUCCESS] ✅ 初始UI更新完成
[2025-08-07 00:57:57] [INFO] 已选择账号: ***********
[2025-08-07 00:57:58] [DEBUG] 🚀 开始为账号 *********** 存稿
[2025-08-07 00:57:58] [INFO] 已确保所有必要目录存在
[2025-08-07 00:57:58] [INFO] 使用单线程模式处理账号
[2025-08-07 00:57:58] [DEBUG] 🔍 [DEBUG] 准备启动单线程任务，参数: ***********
[2025-08-07 00:57:58] [DEBUG] 🔍 [DEBUG] _single_task_thread 开始执行，参数: ***********
[2025-08-07 00:57:58] [DEBUG] 🔍 [DEBUG] 单线程任务已启动
[2025-08-07 00:57:58] [DEBUG] 📋 开始处理单个账号: ***********
[2025-08-07 00:57:58] [INFO] 视频分配方式: 随机分配
[2025-08-07 00:57:58] [DEBUG] 开始处理账号: ***********
[2025-08-07 00:57:58] [SUCCESS] 正在使用Cookie登录账号: ***********
[2025-08-07 00:57:58] [SUCCESS] ✅ Cookie预检测通过，启动浏览器验证...
[2025-08-07 00:58:04] [SUCCESS] 开始查询账号数据: *********** [toutiao] 浏览器驱动创建成功，使用端口: 9515
[2025-08-07 00:58:08] [SUCCESS] 正在加载Cookie文件: D:/网易号全自动/头条号账号\***********.txt
[2025-08-07 00:58:14] [SUCCESS] ✅ Cookie添加完成
[2025-08-07 00:58:16] [SUCCESS] ✅ 头条号快速登录成功 (等待0.5秒)
[2025-08-07 00:58:16] [SUCCESS] 登录成功，开始处理视频: ***********
[2025-08-07 00:58:16] [INFO] 找到 652 个视频文件
[2025-08-07 00:58:16] [INFO] 已随机打乱 652 个视频文件的顺序
[2025-08-07 00:58:16] [DEBUG] 开始处理视频: 矮小亚裔被8名白人围殴，反抗干掉6人，美警：跪地求饶机会都没.mp4
[2025-08-07 00:58:16] [DEBUG] 🎬 开始头条号视频存稿流程
[2025-08-07 00:58:16] [DEBUG] 🔍 正在定位视频菜单...
[2025-08-07 00:58:16] [SUCCESS] ✅ 已点击视频菜单
[2025-08-07 00:58:19] [INFO] 🌐 当前不在上传页面，导航到视频上传页面...
[2025-08-07 00:58:23] [SUCCESS] ✅ 已导航到视频上传页面
[2025-08-07 00:58:23] [INFO] 📁 准备上传视频: 矮小亚裔被8名白人围殴，反抗干掉6人，美警：跪地求饶机会都没.mp4
[2025-08-07 00:58:23] [DEBUG] 🔍 找到上传按钮，准备点击...
[2025-08-07 00:58:23] [SUCCESS] ✅ 已点击上传视频按钮
[2025-08-07 00:58:25] [SUCCESS] ✅ 视频文件已选择并开始上传
[2025-08-07 00:58:25] [DEBUG] ⏳ 开始监控视频上传状态...
[2025-08-07 00:58:25] [INFO] 📊 当前上传状态: 上传中… 0%
[2025-08-07 00:58:25] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 00:58:30] [INFO] 📊 当前上传状态: 上传中… 9.97%
[2025-08-07 00:58:30] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 00:58:35] [INFO] 📊 当前上传状态: 上传中… 22.14%
[2025-08-07 00:58:35] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 00:58:40] [INFO] 📊 当前上传状态: 上传中… 36.5%
[2025-08-07 00:58:40] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 00:58:45] [INFO] 📊 当前上传状态: 上传中… 48.68%
[2025-08-07 00:58:45] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 00:58:50] [INFO] 📊 当前上传状态: 上传中… 60.74%
[2025-08-07 00:58:50] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 00:58:55] [INFO] 📊 当前上传状态: 上传中… 73.11%
[2025-08-07 00:58:55] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 00:59:00] [INFO] 📊 当前上传状态: 上传中… 87.19%
[2025-08-07 00:59:00] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 00:59:05] [INFO] 📊 当前上传状态: 上传中… 98.3%
[2025-08-07 00:59:05] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 00:59:10] [INFO] 📊 当前上传状态: 上传中… 100%
[2025-08-07 00:59:10] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 00:59:15] [SUCCESS] 📊 当前上传状态: 上传成功
[2025-08-07 00:59:15] [SUCCESS] ✅ 视频上传成功！
[2025-08-07 00:59:15] [DEBUG] 📷 开始上传视频封面...
[2025-08-07 00:59:15] [SUCCESS] ✅ 已点击上传封面按钮
[2025-08-07 00:59:18] [SUCCESS] ✅ 已选择本地上传
[2025-08-07 00:59:19] [DEBUG] 🔍 通过accept属性找到封面专用文件输入框
[2025-08-07 00:59:19] [SUCCESS] ✅ 封面文件已选择
[2025-08-07 00:59:20] [DEBUG] 🔍 开始检测封面canvas加载状态...
[2025-08-07 00:59:20] [SUCCESS] ✅ Canvas已加载并有内容 (888x499)
[2025-08-07 00:59:20] [SUCCESS] ✅ 封面canvas已加载完成
[2025-08-07 00:59:20] [SUCCESS] ✅ 已点击第一个确定按钮
[2025-08-07 00:59:32] [ERROR] ❌ 封面上传过程中元素定位超时
[2025-08-07 00:59:32] [WARNING] ⚠️ 上传封面失败，继续存稿流程
[2025-08-07 00:59:32] [DEBUG] 💾 开始保存草稿...
[2025-08-07 00:59:32] [SUCCESS] ✅ 已点击存草稿按钮
[2025-08-07 00:59:35] [WARNING] ✅ 草稿保存成功！检测到'全部保存成功'提示
[2025-08-07 00:59:35] [SUCCESS] ✅ 头条号视频存稿成功！
[2025-08-07 00:59:35] [SUCCESS] ✅ 视频存稿成功: 矮小亚裔被8名白人围殴，反抗干掉6人，美警：跪地求饶机会都没.mp4
[2025-08-07 00:59:35] [DEBUG] 开始处理视频: 定风波：女捕快找仵作索要瘫瘫丸，结果仵作错把剧毒给了出去！.mp4
[2025-08-07 00:59:35] [DEBUG] 🎬 开始头条号视频存稿流程
[2025-08-07 00:59:35] [DEBUG] 🔍 正在定位视频菜单...
[2025-08-07 00:59:35] [SUCCESS] ✅ 已点击视频菜单
[2025-08-07 00:59:38] [INFO] 🌐 当前不在上传页面，导航到视频上传页面...
[2025-08-07 00:59:38] [DEBUG] 正在关闭程序，清理后台资源...
[2025-08-07 00:59:39] [DEBUG] 🔄 调度器主循环结束
[2025-08-07 00:59:39] [DEBUG] 正在关闭任务执行器...
[2025-08-07 00:59:39] [INFO] 任务执行器已关闭
[2025-08-07 00:59:39] [INFO] ⏹️ 定时任务调度器已停止
[2025-08-07 00:59:39] [SUCCESS] ✅ 调度器管理器清理完成
[2025-08-07 00:59:39] [SUCCESS] ✅ 定时任务管理器UI清理完成
[2025-08-07 00:59:39] [DEBUG] 🧹 开始清理所有资源...
[2025-08-07 00:59:39] [DEBUG] ⏹️ 正在停止存稿任务...
[2025-08-07 00:59:39] [INFO] ⏹️ 存稿任务已停止
[2025-08-07 00:59:39] [DEBUG] 正在停止并发管理器...
[2025-08-07 00:59:39] [SUCCESS] ✅ 运行中的任务已停止
[2025-08-07 00:59:39] [SUCCESS] ✅ 定时器线程已清理（升级版系统自动管理）
[2025-08-07 00:59:39] [ERROR] ❌ 进入视频管理页面失败: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: Unable to receive message from renderer
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff6c2416f75+76917]
	GetHandleVerifier [0x0x7ff6c2416fd0+77008]
	(No symbol) [0x0x7ff6c21c9dea]
	(No symbol) [0x0x7ff6c21b75cc]
	(No symbol) [0x0x7ff6c21b72ba]
	(No symbol) [0x0x7ff6c21b611e]
	(No symbol) [0x0x7ff6c21d9e11]
	(No symbol) [0x0x7ff6c22709b1]
	(No symbol) [0x0x7ff6c2248243]
	(No symbol) [0x0x7ff6c2211431]
	(No symbol) [0x0x7ff6c22121c3]
	GetHandleVerifier [0x0x7ff6c26ed2ad+3051437]
	GetHandleVerifier [0x0x7ff6c26e7903+3028483]
	GetHandleVerifier [0x0x7ff6c270589d+3151261]
	GetHandleVerifier [0x0x7ff6c243183e+185662]
	GetHandleVerifier [0x0x7ff6c24396ff+218111]
	GetHandleVerifier [0x0x7ff6c241faf4+112628]
	GetHandleVerifier [0x0x7ff6c241fca9+113065]
	GetHandleVerifier [0x0x7ff6c2406c78+10616]
	BaseThreadInitThunk [0x0x7ffcc2f7e8d7+23]
	RtlUserThreadStart [0x0x7ffcc4e5c34c+44]

[2025-08-07 00:59:39] [ERROR] ❌ 进入视频管理页面失败
[2025-08-07 00:59:39] [ERROR] ❌ 视频存稿失败: 定风波：女捕快找仵作索要瘫瘫丸，结果仵作错把剧毒给了出去！.mp4
[2025-08-07 00:59:39] [DEBUG] 开始处理视频: 洪玛奈恶行曝光.mp4
[2025-08-07 00:59:39] [DEBUG] 🎬 开始头条号视频存稿流程
[2025-08-07 00:59:39] [DEBUG] 🔍 正在定位视频菜单...
[2025-08-07 00:59:39] [ERROR] ❌ 进入视频管理页面失败: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff6c2416f75+76917]
	GetHandleVerifier [0x0x7ff6c2416fd0+77008]
	(No symbol) [0x0x7ff6c21c9c1c]
	(No symbol) [0x0x7ff6c221055f]
	(No symbol) [0x0x7ff6c2248332]
	(No symbol) [0x0x7ff6c2242e53]
	(No symbol) [0x0x7ff6c2241f19]
	(No symbol) [0x0x7ff6c2194b05]
	GetHandleVerifier [0x0x7ff6c26ed2ad+3051437]
	GetHandleVerifier [0x0x7ff6c26e7903+3028483]
	GetHandleVerifier [0x0x7ff6c270589d+3151261]
	GetHandleVerifier [0x0x7ff6c243183e+185662]
	GetHandleVerifier [0x0x7ff6c24396ff+218111]
	(No symbol) [0x0x7ff6c2193b00]
	GetHandleVerifier [0x0x7ff6c2805f18+4201496]
	BaseThreadInitThunk [0x0x7ffcc2f7e8d7+23]
	RtlUserThreadStart [0x0x7ffcc4e5c34c+44]

[2025-08-07 00:59:39] [ERROR] ❌ 进入视频管理页面失败
[2025-08-07 00:59:39] [ERROR] ❌ 视频存稿失败: 洪玛奈恶行曝光.mp4
[2025-08-07 00:59:39] [INFO] 已达到循环限制 3，停止处理
[2025-08-07 00:59:39] [INFO] [toutiao] 已释放端口: 9515
[2025-08-07 00:59:39] [SUCCESS] ✅ Chrome进程强制清理完成
[2025-08-07 00:59:39] [SUCCESS] ✅ 浏览器进程已清理
[2025-08-07 00:59:40] [SUCCESS] ✅ 日志处理线程已清理
[2025-08-07 00:59:40] [SUCCESS] ✅ 存稿处理器已清理
[2025-08-07 00:59:40] [SUCCESS] ✅ 线程池已清理
[2025-08-07 00:59:40] [DEBUG] 正在停止串行刷新工作线程...
