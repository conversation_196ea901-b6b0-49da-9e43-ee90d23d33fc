"""
视频设置组件 - 视频处理相关设置
"""

import tkinter as tk
from tkinter import ttk, simpledialog, messagebox

def manage_filename_filters(ui_instance):
    """管理文件名过滤关键词"""
    # 创建一个新窗口
    filter_window = tk.Toplevel(ui_instance.root)
    filter_window.title("管理文件名过滤关键词")
    filter_window.geometry("600x450")  # 稍微增加窗口大小
    filter_window.resizable(True, True)

    # 居中窗口
    window_width = 600
    window_height = 450
    screen_width = filter_window.winfo_screenwidth()
    screen_height = filter_window.winfo_screenheight()
    x = (screen_width - window_width) // 2
    y = (screen_height - window_height) // 2
    filter_window.geometry(f"{window_width}x{window_height}+{x}+{y}")

    # 设置窗口图标和样式
    if hasattr(ui_instance, 'root') and hasattr(ui_instance.root, 'iconbitmap') and hasattr(ui_instance.root, 'iconphoto'):
        try:
            # 尝试复制主窗口的图标
            filter_window.iconbitmap(ui_instance.root.iconbitmap())
        except:
            pass
        try:
            # 尝试复制主窗口的图标（对于Linux/Mac）
            icon = ui_instance.root._nametowidget(ui_instance.root.iconphoto())
            filter_window.iconphoto(True, icon)
        except:
            pass

    # 创建框架
    frame = ttk.Frame(filter_window, padding=10)
    frame.pack(fill=tk.BOTH, expand=True)

    # 创建关键词列表
    ttk.Label(frame, text="当前过滤关键词:", font=("", 12, "bold")).pack(anchor=tk.W, pady=(0, 5))

    # 创建列表框和滚动条
    list_frame = ttk.Frame(frame)
    list_frame.pack(fill=tk.BOTH, expand=True, pady=5)

    # 创建带边框的列表框
    list_container = ttk.Frame(list_frame, borderwidth=1, relief="solid")
    list_container.pack(fill=tk.BOTH, expand=True)

    # 创建列表框标题
    header_frame = ttk.Frame(list_container, style="Header.TFrame")
    header_frame.pack(fill=tk.X)

    # 设置标题样式
    style = ttk.Style()
    style.configure("Header.TFrame", background="#f0f0f0")
    style.configure("Header.TLabel", background="#f0f0f0", font=("", 11, "bold"))

    # 添加标题
    ttk.Label(header_frame, text="关键词列表", style="Header.TLabel").pack(side=tk.LEFT, padx=10, pady=5)
    ttk.Label(header_frame, text=f"共 {len(ui_instance.video_processor.filename_filters) if hasattr(ui_instance, 'video_processor') and hasattr(ui_instance.video_processor, 'filename_filters') else 0} 个关键词",
             style="Header.TLabel").pack(side=tk.RIGHT, padx=10, pady=5)

    # 创建列表框和滚动条
    keyword_list = tk.Listbox(list_container, height=12, width=50, font=("", 14, "bold"),
                             selectbackground="#4dabf7", selectforeground="white",
                             activestyle="none")
    keyword_list.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

    # 删除滚动条，添加鼠标滚轮支持
    def _on_mousewheel(event):
        keyword_list.yview_scroll(int(-1*(event.delta/120)), "units")

    def _bind_mousewheel(event):
        keyword_list.bind_all("<MouseWheel>", _on_mousewheel)

    def _unbind_mousewheel(event):
        keyword_list.unbind_all("<MouseWheel>")

    keyword_list.bind('<Enter>', _bind_mousewheel)
    keyword_list.bind('<Leave>', _unbind_mousewheel)

    # 填充关键词列表
    if hasattr(ui_instance, 'video_processor') and hasattr(ui_instance.video_processor, 'filename_filters'):
        for keyword in sorted(ui_instance.video_processor.filename_filters):
            keyword_list.insert(tk.END, keyword)

    # 添加提示文本
    if keyword_list.size() == 0:
        ttk.Label(list_container, text="暂无过滤关键词，点击\"添加关键词\"按钮添加",
                 foreground="#888888", font=("", 12)).place(relx=0.5, rely=0.5, anchor=tk.CENTER)

    # 创建按钮框架
    button_frame = ttk.Frame(frame)
    button_frame.pack(fill=tk.X, pady=10)

    # 更新StringVar变量
    def update_filters_var():
        if hasattr(ui_instance, 'video_processor') and hasattr(ui_instance, 'filename_filters'):
            filters_str = ui_instance.video_processor.get_filters_as_string()
            ui_instance.filename_filters.set(filters_str)
            # 保存配置
            if hasattr(ui_instance, 'config_manager'):
                ui_instance.config_manager.save_config(show_message=False)

    # 添加关键词
    def add_keyword():
        keyword = simpledialog.askstring("添加关键词", "请输入要过滤的文件名关键词:", parent=filter_window)
        if keyword:
            # 添加到视频处理器
            if hasattr(ui_instance, 'video_processor'):
                ui_instance.video_processor.add_filename_filter(keyword)
            # 更新列表显示
            keyword_list.insert(tk.END, keyword)
            # 按字母顺序排序
            all_keywords = list(keyword_list.get(0, tk.END))
            all_keywords.sort()
            keyword_list.delete(0, tk.END)
            for kw in all_keywords:
                keyword_list.insert(tk.END, kw)
            ui_instance.log(f"已添加文件名过滤关键词: {keyword}")
            # 更新StringVar变量
            update_filters_var()

    # 删除关键词
    def remove_keyword():
        selected = keyword_list.curselection()
        if selected:
            keyword = keyword_list.get(selected[0])
            # 从视频处理器移除
            if hasattr(ui_instance, 'video_processor'):
                ui_instance.video_processor.remove_filename_filter(keyword)
            # 更新列表显示
            keyword_list.delete(selected[0])
            ui_instance.log(f"已移除文件名过滤关键词: {keyword}")
            # 更新StringVar变量
            update_filters_var()

    # 清空关键词
    def clear_keywords():
        if messagebox.askyesno("确认", "确定要清空所有文件名过滤关键词吗?", parent=filter_window):
            # 清空视频处理器
            if hasattr(ui_instance, 'video_processor'):
                ui_instance.video_processor.clear_filename_filters()
            # 更新列表显示
            keyword_list.delete(0, tk.END)
            ui_instance.log("已清空所有文件名过滤关键词")
            # 更新StringVar变量
            update_filters_var()

    # 创建左侧按钮框架
    left_button_frame = ttk.Frame(button_frame)
    left_button_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

    # 创建右侧按钮框架
    right_button_frame = ttk.Frame(button_frame)
    right_button_frame.pack(side=tk.RIGHT, fill=tk.X)

    # 添加按钮 - 使用更大的按钮和更好的间距
    ttk.Button(left_button_frame, text="添加关键词", command=add_keyword, width=12).pack(side=tk.LEFT, padx=5)
    ttk.Button(left_button_frame, text="删除选中", command=remove_keyword, width=12).pack(side=tk.LEFT, padx=5)
    ttk.Button(left_button_frame, text="清空所有", command=clear_keywords, width=12).pack(side=tk.LEFT, padx=5)
    ttk.Button(right_button_frame, text="关闭", command=filter_window.destroy, width=10).pack(side=tk.RIGHT, padx=5)

    # 设置模态窗口
    filter_window.transient(ui_instance.root)
    filter_window.grab_set()
    ui_instance.root.wait_window(filter_window)

def create_video_settings(parent, ui_instance):
    """创建视频设置面板"""
    # 创建视频设置框架
    settings_frame = ttk.LabelFrame(parent, text="视频设置")
    settings_frame.pack(fill=tk.X, padx=5, pady=5)

    # 获取文件夹图标
    from ...icons import get_icon
    folder_icon = get_icon("folder", (16, 16))
    ui_instance.folder_icon = folder_icon  # 保存引用

    # 第三行: 视频参数
    params_frame = ttk.Frame(settings_frame)
    params_frame.pack(fill=tk.X, padx=10, pady=8)

    # 视频时长设置
    ttk.Label(params_frame, text="视频时长:").pack(side=tk.LEFT)
    min_duration = ttk.Spinbox(params_frame, from_=1, to=3600, textvariable=ui_instance.video_min_duration, width=6)
    min_duration.pack(side=tk.LEFT, padx=(10, 0))

    ttk.Label(params_frame, text="至").pack(side=tk.LEFT, padx=5)

    max_duration = ttk.Spinbox(params_frame, from_=1, to=3600, textvariable=ui_instance.video_max_duration, width=6)
    max_duration.pack(side=tk.LEFT)

    ttk.Label(params_frame, text="秒").pack(side=tk.LEFT, padx=(0, 20))

    # 视频比例设置
    ttk.Label(params_frame, text="视频比例:").pack(side=tk.LEFT, padx=(10, 0))

    ratio_combo = ttk.Combobox(params_frame, textvariable=ui_instance.video_ratio,
                             values=["16:9", "9:16", "4:3", "1:1"],
                             state="readonly", width=8)
    ratio_combo.pack(side=tk.LEFT, padx=10)

    # 线程数设置
    ttk.Label(params_frame, text="线程数:").pack(side=tk.LEFT, padx=(10, 0))

    thread_spinner = ttk.Spinbox(params_frame, from_=1, to=32, textvariable=ui_instance.thread_num, width=4)
    thread_spinner.pack(side=tk.LEFT, padx=10)

    # 去重选项
    dedup_check = ttk.Checkbutton(params_frame, text="视频去重", variable=ui_instance.enable_deduplication)
    dedup_check.pack(side=tk.LEFT, padx=10)

    # 第四行: 文件名过滤设置
    filter_frame = ttk.Frame(settings_frame)
    filter_frame.pack(fill=tk.X, padx=10, pady=8)

    # 文件名过滤选项
    filter_check = ttk.Checkbutton(filter_frame, text="文件名过滤", variable=ui_instance.enable_filename_filter)
    filter_check.pack(side=tk.LEFT, padx=10)

    # 文件名过滤管理按钮
    filter_button = ttk.Button(filter_frame, text="管理过滤关键词",
                              command=lambda: manage_filename_filters(ui_instance))
    filter_button.pack(side=tk.LEFT, padx=5)

    # 第五行: 视频格式设置
    format_frame = ttk.Frame(settings_frame)
    format_frame.pack(fill=tk.X, padx=10, pady=8)

    # 转换视频格式选项
    convert_check = ttk.Checkbutton(format_frame, text="转换视频格式", variable=ui_instance.enable_format_conversion)
    convert_check.pack(side=tk.LEFT)

    # 目标格式下拉框
    ttk.Label(format_frame, text="目标格式:").pack(side=tk.LEFT, padx=(20, 0))
    format_combo = ttk.Combobox(format_frame, textvariable=ui_instance.output_format,
                              values=["mp4", "mov", "avi", "mkv", "webm"],
                              state="readonly", width=8)
    format_combo.pack(side=tk.LEFT, padx=10)

    # 编码器设置
    ttk.Label(format_frame, text="编码器:").pack(side=tk.LEFT, padx=(20, 0))
    codec_combo = ttk.Combobox(format_frame, textvariable=ui_instance.video_codec,
                             values=["h264", "h265", "vp9", "av1", "copy"],
                             state="readonly", width=8)
    codec_combo.pack(side=tk.LEFT, padx=10)

    # 添加工具提示
    if hasattr(ui_instance, 'create_tooltip'):
        ui_instance.create_tooltip(min_duration, "设置要处理的视频的最小时长（秒）")
        ui_instance.create_tooltip(max_duration, "设置要处理的视频的最大时长（秒）")
        ui_instance.create_tooltip(ratio_combo, "选择要处理的视频的宽高比例")
        ui_instance.create_tooltip(thread_spinner, "设置处理视频的并行线程数，建议设置为CPU核心数-1")
        ui_instance.create_tooltip(dedup_check, "启用后将跳过处理重复的视频文件")
        ui_instance.create_tooltip(filter_check, "启用后将根据关键词过滤视频文件名")
        ui_instance.create_tooltip(filter_button, "添加、删除或清空文件名过滤关键词")
        ui_instance.create_tooltip(convert_check, "启用后将视频转换为指定格式")
        ui_instance.create_tooltip(format_combo, "选择转换后的视频格式")
        ui_instance.create_tooltip(codec_combo, "选择视频编码器，copy表示不重新编码")

    return settings_frame
