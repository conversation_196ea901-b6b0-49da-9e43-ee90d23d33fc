"""
视频下载工具对话框 - CR TubeGet集成
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import webbrowser
import subprocess
import threading

class VideoDownloaderDialog:
    """视频下载工具对话框类"""

    def __init__(self, parent, config_manager=None):
        """
        初始化视频下载工具对话框

        Args:
            parent: 父窗口
            config_manager: 配置管理器，如果为None则尝试从父窗口获取
        """
        # 保存父窗口引用
        self.parent = parent

        # 获取配置管理器
        self.config_manager = config_manager
        if self.config_manager is None and hasattr(parent, 'config_manager'):
            self.config_manager = parent.config_manager

        # 配置键名
        self.config_key_cr_tubeget_path = "cr_tubeget_path"

        # CR TubeGet官方网站URL
        self.cr_tubeget_url = "https://www.cr-soft.net/crtubeget.html"

        # 计算居中位置
        width = 500
        height = 550
        screen_width = parent.winfo_screenwidth()
        screen_height = parent.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2

        # 创建对话框并直接设置居中位置
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("视频下载工具")
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")  # 直接设置大小和位置
        self.dialog.minsize(500, 550)    # 设置最小窗口大小
        self.dialog.resizable(True, True)  # 允许调整大小
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 设置窗口图标
        try:
            icon_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "icon.ico")
            if os.path.exists(icon_path):
                self.dialog.iconbitmap(icon_path)
        except Exception:
            pass

        # 创建界面
        self.create_widgets()

        # 检查CR TubeGet是否已安装
        self.check_cr_tubeget_installed()



    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.dialog, padding=5)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text="视频下载工具 - CR TubeGet集成", font=("微软雅黑", 14, "bold"))
        title_label.pack(pady=(0, 5))

        # 描述
        desc_label = ttk.Label(main_frame, text="本工具集成了CR TubeGet视频下载器，可以批量解析和下载各大平台视频。", wraplength=450)
        desc_label.pack(pady=(0, 5))

        # 创建上半部分框架（固定高度）
        upper_frame = ttk.Frame(main_frame)
        upper_frame.pack(fill=tk.X, pady=2)

        # 软件状态框架
        status_frame = ttk.LabelFrame(upper_frame, text="软件状态", padding=5)
        status_frame.pack(fill=tk.X, pady=2)

        # 软件状态
        self.status_label = ttk.Label(status_frame, text="正在检查CR TubeGet软件...", foreground="blue")
        self.status_label.pack(pady=2)

        # 软件路径
        path_frame = ttk.Frame(status_frame)
        path_frame.pack(fill=tk.X, pady=2)

        ttk.Label(path_frame, text="软件路径:").pack(side=tk.LEFT, padx=(0, 5))
        self.path_var = tk.StringVar()
        path_entry = ttk.Entry(path_frame, textvariable=self.path_var, width=35)
        path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        self.browse_button = ttk.Button(path_frame, text="浏览...", command=self.browse_cr_tubeget, width=8)
        self.browse_button.pack(side=tk.RIGHT)

        # 操作框架
        operation_frame = ttk.LabelFrame(upper_frame, text="操作", padding=5)
        operation_frame.pack(fill=tk.X, pady=2)

        # 操作按钮容器
        button_frame = ttk.Frame(operation_frame)
        button_frame.pack(fill=tk.X, pady=2)

        # 创建一个居中的容器，使用网格布局
        center_frame = ttk.Frame(button_frame)
        center_frame.pack(anchor=tk.CENTER, pady=2)

        # 使用网格布局排列按钮，使其更加紧凑
        # 启动CR TubeGet按钮
        self.launch_button = ttk.Button(center_frame, text="启动CR TubeGet", command=self.launch_cr_tubeget, width=25)
        self.launch_button.grid(row=0, column=0, padx=5, pady=2)
        self.launch_button["state"] = "disabled"  # 初始状态为禁用

        # 下载CR TubeGet按钮
        self.download_button = ttk.Button(center_frame, text="下载CR TubeGet", command=self.download_cr_tubeget, width=25)
        self.download_button.grid(row=1, column=0, padx=5, pady=2)

        # 访问官方网站按钮
        self.website_button = ttk.Button(center_frame, text="访问官方网站", command=self.visit_official_website, width=25)
        self.website_button.grid(row=2, column=0, padx=5, pady=2)

        # 使用提示框架 - 设置为固定高度
        tips_frame = ttk.LabelFrame(main_frame, text="使用提示", padding=5)
        tips_frame.pack(fill=tk.BOTH, expand=True, pady=2)

        # 创建滚动文本框来显示使用提示
        tips_text = (
            "CR TubeGet使用提示：\n\n"
            "1. 支持批量解析和下载各大平台视频\n"
            "2. 可以从剪贴板批量导入链接\n"
            "3. 支持自定义下载来和文件命名\n"
            "4. 支持多线程下载加速\n"
            "5. 支持视频格式转换\n\n"
            "支持的平台：抖音、快手、哔哩哔哩、西瓜视频、YouTube等多个平台"
        )

        # 创建文本框和滚动条的容器
        tips_container = ttk.Frame(tips_frame)
        tips_container.pack(fill=tk.BOTH, expand=True, pady=2)

        # 使用滚动文本框代替标签，以便在空间不足时可以滚动
        # 增加高度以显示更多内容
        tips_text_widget = tk.Text(tips_container, wrap=tk.WORD, height=12, width=50)
        tips_text_widget.insert(tk.END, tips_text)
        # 设置为只读但允许选择文本
        tips_text_widget.config(state=tk.DISABLED, cursor="arrow",
                               background="#f0f0f0", relief=tk.FLAT)
        # 允许选择文本
        tips_text_widget.bind("<1>", lambda event: tips_text_widget.focus_set())
        tips_text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 删除滚动条，添加鼠标滚轮支持
        def _on_mousewheel(event):
            tips_text_widget.yview_scroll(int(-1*(event.delta/120)), "units")

        def _bind_mousewheel(event):
            tips_text_widget.bind_all("<MouseWheel>", _on_mousewheel)

        def _unbind_mousewheel(event):
            tips_text_widget.unbind_all("<MouseWheel>")

        tips_text_widget.bind('<Enter>', _bind_mousewheel)
        tips_text_widget.bind('<Leave>', _unbind_mousewheel)

        # 确保文本框可以滚动但不可编辑
        def make_readonly(event):
            tips_text_widget.config(state=tk.DISABLED)
            return "break"

        # 允许选择文本但不允许编辑
        tips_text_widget.bind("<Key>", make_readonly)

        # 底部按钮框架
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.pack(fill=tk.X, pady=2)

        # 关闭按钮
        close_button = ttk.Button(bottom_frame, text="关闭", command=self.dialog.destroy, width=15)
        close_button.pack(side=tk.TOP, anchor=tk.CENTER, pady=2)

    def check_cr_tubeget_installed(self):
        """检查CR TubeGet是否已安装"""
        # 在后台线程中执行检查，避免阻塞UI
        threading.Thread(target=self._check_cr_tubeget_thread, daemon=True).start()

    def _check_cr_tubeget_thread(self):
        """在后台线程中检查CR TubeGet"""
        # 首先尝试从配置中加载保存的路径
        cr_tubeget_path = None
        if self.config_manager:
            saved_path = self.config_manager.get(self.config_key_cr_tubeget_path)
            if saved_path and os.path.exists(saved_path):
                cr_tubeget_path = saved_path

        # 如果配置中没有有效路径，则检查常见安装路径
        if not cr_tubeget_path:
            # 常见的安装路径
            common_paths = [
                os.path.join(os.environ.get("ProgramFiles", "C:\\Program Files"), "CR-Soft", "CR TubeGet", "CRTubeGet.exe"),
                os.path.join(os.environ.get("ProgramFiles(x86)", "C:\\Program Files (x86)"), "CR-Soft", "CR TubeGet", "CRTubeGet.exe"),
                os.path.join(os.environ.get("LOCALAPPDATA", ""), "CR-Soft", "CR TubeGet", "CRTubeGet.exe"),
                os.path.join(os.environ.get("APPDATA", ""), "CR-Soft", "CR TubeGet", "CRTubeGet.exe")
            ]

            # 检查是否存在
            for path in common_paths:
                if os.path.exists(path):
                    cr_tubeget_path = path
                    # 找到后保存到配置
                    if self.config_manager:
                        self.config_manager.update({self.config_key_cr_tubeget_path: path})
                    break

        # 更新UI
        self.dialog.after(0, lambda: self._update_ui_after_check(cr_tubeget_path))

    def _update_ui_after_check(self, cr_tubeget_path):
        """检查完成后更新UI"""
        if cr_tubeget_path:
            self.status_label.config(text="✅ 已找到CR TubeGet软件", foreground="green")
            self.path_var.set(cr_tubeget_path)
            self.launch_button["state"] = "normal"
        else:
            self.status_label.config(text="❌ 未找到CR TubeGet软件，请下载安装或手动指定路径", foreground="red")
            self.path_var.set("")
            self.launch_button["state"] = "disabled"

    def browse_cr_tubeget(self):
        """浏览选择CR TubeGet可执行文件"""
        file_path = filedialog.askopenfilename(
            title="选择CR TubeGet可执行文件",
            filetypes=[("可执行文件", "*.exe"), ("所有文件", "*.*")]
        )
        if file_path:
            self.path_var.set(file_path)
            self.status_label.config(text="✅ 已手动设置CR TubeGet路径", foreground="green")
            self.launch_button["state"] = "normal"

            # 保存路径到配置
            if self.config_manager:
                self.config_manager.update({self.config_key_cr_tubeget_path: file_path})
                self.status_label.config(text="✅ 已手动设置并保存CR TubeGet路径", foreground="green")

    def launch_cr_tubeget(self):
        """启动CR TubeGet"""
        cr_tubeget_path = self.path_var.get()
        if not cr_tubeget_path or not os.path.exists(cr_tubeget_path):
            messagebox.showerror("错误", "CR TubeGet路径无效，请重新选择")
            return

        try:
            subprocess.Popen([cr_tubeget_path])
            self.status_label.config(text="✅ 已启动CR TubeGet", foreground="green")
        except Exception as e:
            messagebox.showerror("启动错误", f"启动CR TubeGet时出错: {str(e)}")

    def download_cr_tubeget(self):
        """下载CR TubeGet"""
        webbrowser.open(self.cr_tubeget_url)
        self.status_label.config(text="已打开下载页面，请在浏览器中完成下载", foreground="blue")

    def visit_official_website(self):
        """访问官方网站"""
        webbrowser.open(self.cr_tubeget_url)
        self.status_label.config(text="已打开官方网站", foreground="blue")
