"""
工具栏模块 - 负责显示和管理工具栏
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, Any, Callable, List, Optional

class Toolbar:
    """工具栏类，负责显示和管理工具栏"""
    
    def __init__(self, parent: ttk.Frame, log_callback: Callable = None):
        """
        初始化工具栏
        
        Args:
            parent: 父容器
            log_callback: 日志回调函数
        """
        self.parent = parent
        self.log_callback = log_callback
        
        # 初始化变量
        self.is_running = False
        self.selected_account = tk.StringVar()
        self.headless_mode = tk.BooleanVar(value=False)
        self.archive_completed = tk.BooleanVar(value=True)
        
        # 创建UI
        self.create_ui()
    
    def log(self, message: str) -> None:
        """
        记录日志
        
        Args:
            message: 日志消息
        """
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)
    
    def create_ui(self) -> None:
        """创建用户界面"""
        # 创建工具栏框架
        toolbar_frame = ttk.Frame(self.parent)
        toolbar_frame.pack(fill=tk.X, pady=5)
        
        # 平台标识（固定为网易号平台）
        platform_frame = ttk.Frame(toolbar_frame)
        platform_frame.pack(side=tk.LEFT, padx=5, pady=5)
        
        ttk.Label(platform_frame, text="当前平台:").pack(side=tk.LEFT)
        platform_label = ttk.Label(platform_frame, text="网易号平台", font=("宋体", 9, "bold"))
        platform_label.pack(side=tk.LEFT, padx=5)
        
        # 当前选中账号显示
        account_frame = ttk.Frame(toolbar_frame)
        account_frame.pack(side=tk.LEFT, padx=(20, 5), pady=5)
        
        ttk.Label(account_frame, text="当前账号:").pack(side=tk.LEFT)
        self.current_account_label = ttk.Label(account_frame, text="未选择", foreground="red")
        self.current_account_label.pack(side=tk.LEFT, padx=5)
        
        # 运行按钮
        self.start_button = ttk.Button(toolbar_frame, text="开始存稿", command=self.start_task)
        self.start_button.pack(side=tk.LEFT, padx=20, pady=5)
        
        self.stop_button = ttk.Button(toolbar_frame, text="停止", command=self.stop_task)
        self.stop_button.pack(side=tk.LEFT, padx=5, pady=5)
        self.stop_button["state"] = "disabled"
        
        # 导入美化复选框
        from ..styles.checkbox_styles import create_beautiful_checkbox

        # 无头模式复选框
        headless_check = create_beautiful_checkbox(toolbar_frame, text="无头模式", variable=self.headless_mode, style_type="modern")
        headless_check.pack(side=tk.LEFT, padx=20, pady=5)

        # 归档模式复选框
        archive_check = create_beautiful_checkbox(toolbar_frame, text="归档视频(不删除)", variable=self.archive_completed, style_type="success")
        archive_check.pack(side=tk.LEFT, padx=20, pady=5)
        
        # 创建额外的工具栏框架（用于存稿限制设置）
        extra_toolbar = ttk.Frame(self.parent)
        extra_toolbar.pack(fill=tk.X, pady=(0, 5))
        
        # 存稿数量限制
        self.draft_limit = tk.IntVar(value=0)
        ttk.Label(extra_toolbar, text="存稿数量:").pack(side=tk.LEFT, padx=(5, 0), pady=5)
        ttk.Spinbox(extra_toolbar, from_=0, to=1000, textvariable=self.draft_limit, width=5).pack(side=tk.LEFT, padx=(0, 5), pady=5)
        
        # 循环次数限制
        self.loop_limit = tk.IntVar(value=0)
        ttk.Label(extra_toolbar, text="循环次数:").pack(side=tk.LEFT, padx=(15, 0), pady=5)
        ttk.Spinbox(extra_toolbar, from_=0, to=1000, textvariable=self.loop_limit, width=5).pack(side=tk.LEFT, padx=(0, 5), pady=5)
        
        # 提示文本
        ttk.Label(extra_toolbar, text="(0表示不限制)").pack(side=tk.LEFT, padx=5, pady=5)
    
    def set_selected_account(self, account: str) -> None:
        """
        设置选中的账号
        
        Args:
            account: 账号名称
        """
        self.selected_account.set(account)
        
        if account:
            self.current_account_label.config(text=account, foreground="green")
        else:
            self.current_account_label.config(text="未选择", foreground="red")
    
    def set_running_state(self, is_running: bool) -> None:
        """
        设置运行状态
        
        Args:
            is_running: 是否正在运行
        """
        self.is_running = is_running
        
        if is_running:
            self.start_button["state"] = "disabled"
            self.stop_button["state"] = "normal"
        else:
            self.start_button["state"] = "normal"
            self.stop_button["state"] = "disabled"
    
    def start_task(self) -> None:
        """开始存稿任务"""
        # 检查是否已经在运行
        if self.is_running:
            messagebox.showwarning("警告", "任务已在运行中")
            return
        
        # 获取选中的账号
        selected_account = self.selected_account.get()
        
        # 如果没有选中账号，询问是否要处理所有账号
        if not selected_account:
            if not messagebox.askyesno("确认", "没有选中账号，是否要处理所有账号？"):
                return
            
            # 触发处理所有账号事件
            self.on_start_all_accounts()
        else:
            # 触发处理单个账号事件
            self.on_start_single_account(selected_account)
    
    def stop_task(self) -> None:
        """停止存稿任务"""
        # 检查是否正在运行
        if not self.is_running:
            return
        
        # 确认停止
        if not messagebox.askyesno("确认停止", "确定要停止当前任务吗？"):
            return
        
        # 触发停止任务事件
        self.on_stop_task()
    
    def on_start_single_account(self, account: str) -> None:
        """
        处理单个账号事件
        
        Args:
            account: 账号名称
        """
        # 这个方法可以被子类重写，用于处理单个账号事件
        self.log(f"开始处理账号: {account}")
        self.set_running_state(True)
    
    def on_start_all_accounts(self) -> None:
        """处理所有账号事件"""
        # 这个方法可以被子类重写，用于处理所有账号事件
        self.log("开始处理所有账号")
        self.set_running_state(True)
    
    def on_stop_task(self) -> None:
        """停止任务事件"""
        # 这个方法可以被子类重写，用于处理停止任务事件
        self.log("停止任务")
        self.set_running_state(False)
    
    def get_task_options(self) -> Dict[str, Any]:
        """
        获取任务选项
        
        Returns:
            任务选项字典
        """
        return {
            "headless_mode": self.headless_mode.get(),
            "archive_completed": self.archive_completed.get(),
            "draft_limit": self.draft_limit.get(),
            "loop_limit": self.loop_limit.get()
        }
