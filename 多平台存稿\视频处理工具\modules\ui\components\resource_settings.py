"""
资源设置组件 - 智能资源分配相关设置
"""

import os
import tkinter as tk
from tkinter import ttk, messagebox
import psutil
from typing import Dict, List, Any, Callable

def create_resource_settings(parent, ui_instance):
    """创建智能资源分配设置面板"""
    # 创建资源设置框架
    resource_frame = ttk.LabelFrame(parent, text="智能资源分配")
    resource_frame.pack(fill=tk.X, padx=10, pady=10)

    # 启用智能资源分配复选框
    resource_check = ttk.Checkbutton(resource_frame, text="启用智能资源分配",
                                   variable=ui_instance.enable_smart_resource,
                                   command=lambda: toggle_smart_resource(ui_instance))
    resource_check.pack(anchor=tk.W, padx=10, pady=5)

    # 内存限制框架
    memory_frame = ttk.Frame(resource_frame)
    memory_frame.pack(fill=tk.X, padx=10, pady=5)

    # 内存限制标签
    ttk.Label(memory_frame, text="内存限制 (GB):").pack(side=tk.LEFT)

    # 内存限制微调框
    memory_spinbox = ttk.Spinbox(memory_frame, from_=0, to=64, width=5,
                               textvariable=ui_instance.memory_limit)
    memory_spinbox.pack(side=tk.LEFT, padx=10)

    # 当前内存信息
    memory_info = psutil.virtual_memory()
    total_memory_gb = memory_info.total / (1024 ** 3)
    available_memory_gb = memory_info.available / (1024 ** 3)

    # 内存信息标签
    memory_info_label = ttk.Label(memory_frame, 
                                text=f"总内存: {total_memory_gb:.1f} GB, 可用: {available_memory_gb:.1f} GB")
    memory_info_label.pack(side=tk.LEFT, padx=10)

    # 保存标签引用
    ui_instance.memory_info_label = memory_info_label

    # 刷新内存信息按钮
    refresh_button = ttk.Button(memory_frame, text="刷新",
                              command=lambda: refresh_memory_info(ui_instance), width=6)
    refresh_button.pack(side=tk.LEFT, padx=5)

    # 线程设置框架
    thread_frame = ttk.Frame(resource_frame)
    thread_frame.pack(fill=tk.X, padx=10, pady=5)

    # 线程数标签
    ttk.Label(thread_frame, text="处理线程数:").pack(side=tk.LEFT)

    # 线程数微调框
    thread_spinbox = ttk.Spinbox(thread_frame, from_=1, to=32, width=5,
                               textvariable=ui_instance.thread_num)
    thread_spinbox.pack(side=tk.LEFT, padx=10)

    # CPU信息
    cpu_count = os.cpu_count()
    cpu_info_label = ttk.Label(thread_frame, text=f"CPU核心数: {cpu_count}")
    cpu_info_label.pack(side=tk.LEFT, padx=10)

    # 自动设置按钮
    auto_set_button = ttk.Button(thread_frame, text="自动设置",
                               command=lambda: auto_set_threads(ui_instance), width=10)
    auto_set_button.pack(side=tk.LEFT, padx=5)

    # 添加工具提示
    if hasattr(ui_instance, 'create_tooltip'):
        ui_instance.create_tooltip(resource_check, "启用智能资源分配可以根据系统资源自动调整处理参数")
        ui_instance.create_tooltip(memory_spinbox, "设置处理过程中使用的最大内存量，0表示自动")
        ui_instance.create_tooltip(refresh_button, "刷新当前内存使用情况")
        ui_instance.create_tooltip(thread_spinbox, "设置处理视频的并行线程数，建议设置为CPU核心数-1")
        ui_instance.create_tooltip(auto_set_button, "根据CPU核心数自动设置最佳线程数")

    return resource_frame

def toggle_smart_resource(ui_instance):
    """切换智能资源分配状态"""
    if ui_instance.enable_smart_resource.get():
        # 如果启用了智能资源分配，自动设置线程数
        auto_set_threads(ui_instance)
        # 刷新内存信息
        refresh_memory_info(ui_instance)
        ui_instance.log("✅ 已启用智能资源分配")
    else:
        ui_instance.log("⚠️ 已禁用智能资源分配")

    # 保存配置
    ui_instance.config_manager.save_config()

def refresh_memory_info(ui_instance):
    """刷新内存信息"""
    try:
        # 获取当前内存信息
        memory_info = psutil.virtual_memory()
        total_memory_gb = memory_info.total / (1024 ** 3)
        available_memory_gb = memory_info.available / (1024 ** 3)

        # 更新内存信息标签
        ui_instance.memory_info_label.config(
            text=f"总内存: {total_memory_gb:.1f} GB, 可用: {available_memory_gb:.1f} GB")

        # 如果启用了智能资源分配，自动设置内存限制
        if ui_instance.enable_smart_resource.get():
            # 设置为可用内存的75%
            recommended_limit = int(available_memory_gb * 0.75)
            ui_instance.memory_limit.set(recommended_limit)
            ui_instance.log(f"✅ 已自动设置内存限制为 {recommended_limit} GB")

        ui_instance.log(f"✅ 内存信息已刷新: 总内存 {total_memory_gb:.1f} GB, 可用 {available_memory_gb:.1f} GB")
    except Exception as e:
        ui_instance.log(f"❌ 刷新内存信息失败: {str(e)}")

def auto_set_threads(ui_instance):
    """自动设置线程数"""
    try:
        # 获取CPU核心数
        cpu_count = os.cpu_count()
        
        # 设置为CPU核心数-1，但不少于1
        recommended_threads = max(1, cpu_count - 1)
        
        # 更新线程数
        ui_instance.thread_num.set(recommended_threads)
        
        ui_instance.log(f"✅ 已自动设置线程数为 {recommended_threads} (CPU核心数: {cpu_count})")
    except Exception as e:
        ui_instance.log(f"❌ 自动设置线程数失败: {str(e)}")
