"""
每日爆文工具对话框
"""

import os
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import webbrowser
import pandas as pd
from ..styles.checkbox_styles import create_beautiful_checkbox

class DailyHotDialog:
    """每日爆文工具对话框类"""

    def __init__(self, parent, config_manager=None):
        """
        初始化每日爆文工具对话框

        Args:
            parent: 父窗口
            config_manager: 配置管理器，如果为None则尝试从父窗口获取
        """
        # 保存父窗口引用
        self.parent = parent

        # 获取配置管理器
        self.config_manager = config_manager
        if self.config_manager is None and hasattr(parent, 'config_manager'):
            self.config_manager = parent.config_manager

        # 配置键名
        self.config_key_cookie = "daily_hot_cookie"
        self.config_key_categories = "daily_hot_categories"
        self.config_key_platform = "daily_hot_platform"
        self.config_key_content_type = "daily_hot_content_type"
        self.config_key_read_count = "daily_hot_read_count"

        # Excel数据
        self.excel_data = None
        self.filtered_data = None

        # 网站URL
        self.daily_hot_url = "https://www.czgts.cn/v1/hots/article"
        self.login_url = "https://www.czgts.cn/v1/home?reloadType=register"

        # 根据是否在打包环境中调整窗口大小和字体
        import sys
        if getattr(sys, 'frozen', False):
            # 在打包环境中使用较小的窗口，统一使用系统默认字体大小
            width = 1100
            height = 850
            self.default_font_size = 9  # 统一使用系统默认字体大小
            self.title_font_size = 11   # 标题稍大
            self.desc_font_size = 8     # 描述稍小
            min_width, min_height = 1000, 750
            print("爆文工具: 检测到打包环境，使用适配的窗口大小和系统默认字体")
        else:
            # 在开发环境中使用原始大小，统一使用系统默认字体大小
            width = 1300
            height = 1000
            self.default_font_size = 9  # 统一使用系统默认字体大小
            self.title_font_size = 11   # 标题稍大
            self.desc_font_size = 8     # 描述稍小
            min_width, min_height = 1250, 950
            print("爆文工具: 检测到开发环境，使用原始窗口大小和系统默认字体")

        # 计算居中位置
        screen_width = parent.winfo_screenwidth()
        screen_height = parent.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2

        # 创建对话框并直接设置居中位置
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("每日爆文工具")
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")  # 直接设置大小和位置
        self.dialog.minsize(min_width, min_height)    # 根据环境设置最小窗口大小
        self.dialog.resizable(True, True)  # 允许调整大小
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 设置窗口图标
        try:
            icon_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "icon.ico")
            if os.path.exists(icon_path):
                self.dialog.iconbitmap(icon_path)
        except Exception:
            pass

        # 创建自定义样式
        self.create_custom_styles()

        # 创建界面
        self.create_widgets()

        # 加载保存的设置
        self.load_settings()

        # 加载保存的Cookie
        self.load_cookie()



    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.dialog, padding=15)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题区域
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 15))

        # 主标题
        title_label = ttk.Label(title_frame, text="🔥 每日爆文工具",
                               font=("微软雅黑", self.title_font_size, "bold"),
                               foreground="#2E86AB")
        title_label.pack(side=tk.LEFT)

        # 状态指示器
        self.status_frame = ttk.Frame(title_frame)
        self.status_frame.pack(side=tk.RIGHT)

        self.status_label = ttk.Label(self.status_frame, text="● 就绪",
                                     font=("微软雅黑", self.desc_font_size),
                                     foreground="#28A745")
        self.status_label.pack()

        # 创建顶部选项卡（素材选择/Excel处理）
        self.top_notebook = ttk.Notebook(main_frame)
        self.top_notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 创建"素材选择"选项卡
        material_tab = ttk.Frame(self.top_notebook)
        self.top_notebook.add(material_tab, text="📰 素材选择")

        # 创建"Excel处理"选项卡
        excel_tab = ttk.Frame(self.top_notebook)
        self.top_notebook.add(excel_tab, text="📊 Excel处理")

        # 设置"素材选择"选项卡内容
        self.create_article_tab(material_tab)

        # 设置"Excel处理"选项卡内容
        self.create_excel_tab(excel_tab)

        # 底部状态栏和按钮
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.pack(fill=tk.X, pady=(10, 0))

        # 状态栏
        self.progress_frame = ttk.Frame(bottom_frame)
        self.progress_frame.pack(fill=tk.X, pady=(0, 10))

        # 进度条（初始隐藏）
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(self.progress_frame, variable=self.progress_var,
                                           mode='determinate', length=400)

        # 进度文本
        self.progress_text = ttk.Label(self.progress_frame, text="",
                                      font=("微软雅黑", self.desc_font_size))

        # 按钮框架
        button_frame = ttk.Frame(bottom_frame)
        button_frame.pack(fill=tk.X)

        # 关闭按钮
        close_button = ttk.Button(button_frame, text="关闭",
                                 command=self.dialog.destroy,
                                 style=getattr(self, 'button_style', 'TButton'), width=15)
        close_button.pack(side=tk.RIGHT, padx=(5, 0))

    def create_article_tab(self, parent):
        """创建文章选择选项卡内容"""
        # 创建滚动框架（移除右侧滚动条）
        canvas = tk.Canvas(parent)
        scrollable_frame = ttk.Frame(canvas)

        canvas_window = canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")

        # 配置Canvas和滚动区域
        def configure_scroll_region(event=None):
            canvas.configure(scrollregion=canvas.bbox("all"))
            # 确保canvas宽度与内容匹配
            canvas_width = canvas.winfo_width()
            canvas.itemconfig(canvas_window, width=canvas_width)

        scrollable_frame.bind("<Configure>", configure_scroll_region)
        canvas.bind("<Configure>", configure_scroll_region)

        # 添加鼠标滚轮支持
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        def bind_mousewheel(event):
            canvas.bind_all("<MouseWheel>", _on_mousewheel)

        def unbind_mousewheel(event):
            canvas.unbind_all("<MouseWheel>")

        canvas.bind('<Enter>', bind_mousewheel)
        canvas.bind('<Leave>', unbind_mousewheel)

        canvas.pack(fill="both", expand=True)

        # 登录状态框架
        login_frame = ttk.LabelFrame(scrollable_frame, text="🔐 登录状态", padding=12)
        login_frame.pack(fill=tk.X, pady=(0, 10), padx=5)

        # 登录状态指示器
        status_container = ttk.Frame(login_frame)
        status_container.pack(fill=tk.X)

        # 状态图标和文字
        self.login_status_var = tk.StringVar(value="未登录")
        self.login_status_icon = ttk.Label(status_container, text="🔴", font=("微软雅黑", 12))
        self.login_status_icon.pack(side=tk.LEFT, padx=(0, 5))

        login_status_label = ttk.Label(status_container, textvariable=self.login_status_var,
                                      font=("微软雅黑", self.default_font_size),
                                      foreground="#DC3545")
        login_status_label.pack(side=tk.LEFT, padx=5)

        # 登录按钮
        self.login_button = ttk.Button(status_container, text="🌐 登录网站",
                                      command=self.auto_login,
                                      style=getattr(self, 'button_style', 'TButton'), width=22)
        self.login_button.pack(side=tk.RIGHT, padx=5)

        # 文章类别框架
        category_frame = ttk.LabelFrame(scrollable_frame, text="📂 文章类别选择", padding=12)
        category_frame.pack(fill=tk.X, pady=(0, 10), padx=5)

        # 类别说明
        category_desc = ttk.Label(category_frame,
                                 text="选择您感兴趣的文章类别，支持多选",
                                 font=("微软雅黑", self.desc_font_size),
                                 foreground="#6C757D")
        category_desc.pack(anchor=tk.W, pady=(0, 8))

        # 创建类别选择网格
        categories = [
            ("🌟 全部", "全部"), ("🏠 生活", "生活"), ("🎬 影视", "影视"), ("⚽ 体育", "体育"), ("💕 情感", "情感"), ("🎭 娱乐", "娱乐"),
            ("💰 财经", "财经"), ("🌾 三农", "三农"), ("🌍 国际", "国际"), ("🎖️ 军事", "军事"), ("😄 搞笑", "搞笑"), ("🍽️ 美食", "美食"),
            ("🚗 汽车", "汽车"), ("👗 时尚", "时尚"), ("✈️ 旅游", "旅游"), ("🎵 音乐", "音乐"), ("💊 健康", "健康"), ("📚 历史", "历史"),
            ("🎮 游戏", "游戏"), ("🎓 教育", "教育"), ("⚖️ 法律", "法律"), ("👴 养老", "养老"), ("👶 育儿", "育儿"), ("💼 职业职场", "职业职场"),
            ("🏛️ 时政社会", "时政社会"), ("📖 人文社科", "人文社科"), ("🐾 动物宠物", "动物宠物"), ("🔬 科学技术", "科学技术"), ("📝 其他", "其他")
        ]

        # 创建类别变量和复选框
        self.category_vars = {}
        category_grid = ttk.Frame(category_frame)
        category_grid.pack(fill=tk.X, pady=(0, 8))

        # 使用网格布局排列类别，每行6个
        for i, (display_name, category) in enumerate(categories):
            row = i // 6
            col = i % 6
            var = tk.BooleanVar(value=False)
            self.category_vars[category] = var
            # 导入美化复选框
            from ..styles.checkbox_styles import create_beautiful_checkbox
            cb = create_beautiful_checkbox(category_grid, text=display_name, variable=var, style_type="modern")
            cb.grid(row=row, column=col, sticky=tk.W, padx=8, pady=3)

            # 为网格列设置权重
            if row == 0:
                category_grid.columnconfigure(col, weight=1)

        # 类别操作按钮框架
        category_button_frame = ttk.Frame(category_frame)
        category_button_frame.pack(fill=tk.X, pady=(8, 0))

        # 左侧快捷操作按钮
        quick_buttons = ttk.Frame(category_button_frame)
        quick_buttons.pack(side=tk.LEFT)

        # 全选按钮
        select_all_button = ttk.Button(quick_buttons, text="✅ 全选",
                                      command=self.select_all_categories, width=12)
        select_all_button.pack(side=tk.LEFT, padx=(0, 5))

        # 取消全选按钮
        deselect_all_button = ttk.Button(quick_buttons, text="❌ 取消全选",
                                        command=self.deselect_all_categories, width=12)
        deselect_all_button.pack(side=tk.LEFT, padx=5)

        # 恢复默认按钮
        reset_button = ttk.Button(quick_buttons, text="🔄 恢复默认",
                                 command=self.reset_categories, width=12)
        reset_button.pack(side=tk.LEFT, padx=5)

        # 右侧保存按钮
        save_buttons = ttk.Frame(category_button_frame)
        save_buttons.pack(side=tk.RIGHT)

        # 记住选择按钮
        remember_button = ttk.Button(save_buttons, text="💾 记住选择",
                                    command=self.save_categories,
                                    style=getattr(self, 'button_style', 'TButton'), width=15)
        remember_button.pack(side=tk.RIGHT)

        # 创建设置区域的水平布局
        settings_container = ttk.Frame(scrollable_frame)
        settings_container.pack(fill=tk.X, pady=(0, 10), padx=5)

        # 媒体平台框架
        platform_frame = ttk.LabelFrame(settings_container, text="📱 媒体平台选择", padding=12)
        platform_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        # 平台说明
        platform_desc = ttk.Label(platform_frame,
                                 text="选择目标媒体平台",
                                 font=("微软雅黑", self.desc_font_size),
                                 foreground="#6C757D")
        platform_desc.pack(anchor=tk.W, pady=(0, 8))

        # 媒体平台选择
        self.platform_var = tk.StringVar(value="今日头条")
        platform_options = [("📰 今日头条", "今日头条"), ("📝 百家号", "百家号")]

        platform_grid = ttk.Frame(platform_frame)
        platform_grid.pack(fill=tk.X)

        # 使用单选按钮组
        for i, (display_name, platform) in enumerate(platform_options):
            rb = ttk.Radiobutton(platform_grid, text=display_name, value=platform,
                               variable=self.platform_var, style="Custom.TRadiobutton")
            rb.grid(row=0, column=i, padx=10, pady=5, sticky=tk.W)
            platform_grid.columnconfigure(i, weight=1)

        # 内容类型框架
        content_type_frame = ttk.LabelFrame(settings_container, text="📄 内容类型选择", padding=12)
        content_type_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))

        # 内容类型说明
        content_desc = ttk.Label(content_type_frame,
                                text="选择内容类型",
                                font=("微软雅黑", self.desc_font_size),
                                foreground="#6C757D")
        content_desc.pack(anchor=tk.W, pady=(0, 8))

        # 内容类型选择
        self.content_type_var = tk.StringVar(value="图文")
        content_type_options = [("📝 图文", "图文"), ("🎥 视频", "视频"), ("❓ 问答", "问答")]

        content_grid = ttk.Frame(content_type_frame)
        content_grid.pack(fill=tk.X)

        # 使用单选按钮组
        for i, (display_name, content_type) in enumerate(content_type_options):
            rb = ttk.Radiobutton(content_grid, text=display_name, value=content_type,
                               variable=self.content_type_var, style="Custom.TRadiobutton")
            rb.grid(row=0, column=i, padx=8, pady=5, sticky=tk.W)
            content_grid.columnconfigure(i, weight=1)

        # 阅读量设置框架
        read_count_frame = ttk.LabelFrame(scrollable_frame, text="📊 阅读量设置", padding=12)
        read_count_frame.pack(fill=tk.X, pady=(0, 10), padx=5)

        # 阅读量设置容器
        read_count_container = ttk.Frame(read_count_frame)
        read_count_container.pack(fill=tk.X)

        # 阅读量说明和输入
        read_count_left = ttk.Frame(read_count_container)
        read_count_left.pack(side=tk.LEFT, fill=tk.X, expand=True)

        ttk.Label(read_count_left, text="最低阅读量筛选:",
                 font=("微软雅黑", self.default_font_size)).pack(side=tk.LEFT, padx=(0, 8))

        self.read_count_var = tk.StringVar(value="10000")
        read_count_entry = ttk.Entry(read_count_left, textvariable=self.read_count_var,
                                   width=15, font=("微软雅黑", self.default_font_size))
        read_count_entry.pack(side=tk.LEFT, padx=(0, 8))

        ttk.Label(read_count_left, text="次",
                 font=("微软雅黑", self.desc_font_size),
                 foreground="#6C757D").pack(side=tk.LEFT)

        # 保存设置按钮
        save_settings_button = ttk.Button(read_count_container, text="💾 保存设置",
                                        command=self.save_platform_settings,
                                        style=getattr(self, 'button_style', 'TButton'), width=15)
        save_settings_button.pack(side=tk.RIGHT, padx=(10, 0))

        # 操作框架
        operation_frame = ttk.LabelFrame(scrollable_frame, text="🚀 操作", padding=12)
        operation_frame.pack(fill=tk.X, pady=(0, 10), padx=5)

        # 操作按钮网格
        operation_grid = ttk.Frame(operation_frame)
        operation_grid.pack(fill=tk.X)

        # 配置网格权重
        operation_grid.columnconfigure(0, weight=1)
        operation_grid.columnconfigure(1, weight=1)

        # 打开网站按钮
        open_site_button = ttk.Button(operation_grid, text="🌐 打开每日爆文网站",
                                     command=self.open_daily_hot_site,
                                     style=getattr(self, 'button_style', 'TButton'))
        open_site_button.grid(row=0, column=0, sticky="ew", padx=(0, 5), pady=5)

        # 获取热门文章按钮
        get_articles_button = ttk.Button(operation_grid, text="🔥 获取热门文章",
                                        command=self.get_hot_articles,
                                        style=getattr(self, 'button_style', 'TButton'))
        get_articles_button.grid(row=0, column=1, sticky="ew", padx=(5, 0), pady=5)

    def auto_login(self):
        """自动登录并获取Cookie"""
        # 导入必要的模块
        import threading

        # 显示提示消息
        messagebox.showinfo("登录提示", "即将打开浏览器，请在浏览器中完成登录操作。\n\n登录成功后，请点击确认按钮保存Cookie。")

        # 使用线程运行浏览器，避免阻塞主线程
        self.login_thread = threading.Thread(target=self._run_browser_login)
        self.login_thread.daemon = True  # 设置为守护线程，主程序退出时自动结束
        self.login_thread.start()

        # 更新状态
        self.update_login_status("正在打开浏览器...")
        self.update_status("正在启动浏览器进行登录", "info")

    def _run_browser_login(self):
        """在单独的线程中运行浏览器登录过程"""
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service
        from selenium.webdriver.chrome.options import Options
        import os

        driver = None
        try:
            # 创建Chrome浏览器选项
            chrome_options = Options()
            chrome_options.add_argument("--start-maximized")  # 最大化窗口
            chrome_options.add_argument("--disable-extensions")  # 禁用扩展
            chrome_options.add_argument("--disable-gpu")  # 禁用GPU加速
            chrome_options.add_argument("--disable-dev-shm-usage")  # 禁用/dev/shm使用
            chrome_options.add_argument("--no-sandbox")  # 禁用沙盒

            # 尝试直接使用Chrome驱动
            try:
                # 尝试查找Chrome驱动
                driver_path = None
                possible_paths = [
                    os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "drivers", "chromedriver.exe"),
                    os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))), "drivers", "chromedriver.exe"),
                    "chromedriver.exe"
                ]

                for path in possible_paths:
                    if os.path.exists(path):
                        driver_path = path
                        break

                if driver_path:
                    # 使用找到的驱动路径
                    service = Service(executable_path=driver_path)
                    driver = webdriver.Chrome(service=service, options=chrome_options)
                else:
                    # 如果找不到驱动，尝试让Selenium自动查找
                    driver = webdriver.Chrome(options=chrome_options)
            except Exception as driver_error:
                # 如果直接使用Chrome驱动失败，尝试使用webdriver_manager
                try:
                    from webdriver_manager.chrome import ChromeDriverManager
                    service = Service(ChromeDriverManager().install())
                    driver = webdriver.Chrome(service=service, options=chrome_options)
                except Exception as wdm_error:
                    # 在主线程中显示错误消息
                    self.dialog.after(0, lambda: self._show_error(f"无法创建Chrome浏览器实例: {str(driver_error)}\n尝试使用webdriver_manager也失败: {str(wdm_error)}"))
                    return

            # 保存driver引用，以便在其他方法中访问
            self.browser_driver = driver

            # 打开登录页面
            driver.get(self.login_url)

            # 在主线程中更新状态
            self.dialog.after(0, lambda: self.login_status_var.set("请在浏览器中完成登录..."))

            # 创建确认对话框
            self.dialog.after(0, self._create_confirm_dialog)

        except Exception as e:
            # 在主线程中显示错误消息
            self.dialog.after(0, lambda: self._show_error(f"登录过程中出现错误：{str(e)}"))

            # 确保浏览器关闭
            try:
                if driver:
                    driver.quit()
                    self.browser_driver = None
            except:
                pass

    def _create_confirm_dialog(self):
        """创建确认对话框"""
        # 创建确认对话框
        self.confirm_dialog = tk.Toplevel(self.dialog)
        self.confirm_dialog.title("登录确认")

        # 根据环境调整确认对话框大小
        import sys
        if getattr(sys, 'frozen', False):
            dialog_width, dialog_height = 350, 180
            wrap_length = 300
        else:
            dialog_width, dialog_height = 400, 200
            wrap_length = 350

        self.confirm_dialog.geometry(f"{dialog_width}x{dialog_height}")
        self.confirm_dialog.transient(self.dialog)
        self.confirm_dialog.grab_set()

        # 居中显示
        self.confirm_dialog.update_idletasks()
        width = self.confirm_dialog.winfo_width()
        height = self.confirm_dialog.winfo_height()
        x = (self.confirm_dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.confirm_dialog.winfo_screenheight() // 2) - (height // 2)
        self.confirm_dialog.geometry(f"{width}x{height}+{x}+{y}")

        # 说明标签
        ttk.Label(self.confirm_dialog, text="请在浏览器中完成登录后，点击下方按钮确认保存Cookie。",
                 wraplength=wrap_length, font=("微软雅黑", self.default_font_size)).pack(pady=20, padx=20)

        # 按钮框架
        button_frame = ttk.Frame(self.confirm_dialog)
        button_frame.pack(fill=tk.X, padx=20, pady=20)

        # 确认按钮
        confirm_button = ttk.Button(button_frame, text="已完成登录，保存Cookie",
                                  command=self._save_cookie_from_browser,
                                  style=getattr(self, 'button_style', 'TButton'), width=25)
        confirm_button.pack(side=tk.LEFT, padx=5)

        # 取消按钮
        cancel_button = ttk.Button(button_frame, text="取消",
                                 command=self._cancel_login,
                                 width=10)
        cancel_button.pack(side=tk.RIGHT, padx=5)

        # 设置关闭窗口事件
        self.confirm_dialog.protocol("WM_DELETE_WINDOW", self._cancel_login)

    def _save_cookie_from_browser(self):
        """从浏览器保存Cookie"""
        try:
            if hasattr(self, 'browser_driver') and self.browser_driver:
                # 获取所有Cookie
                cookies = self.browser_driver.get_cookies()

                # 将Cookie转换为字符串格式
                cookie_str = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in cookies])

                # 保存Cookie
                if self.config_manager:
                    self.config_manager.update({self.config_key_cookie: cookie_str})
                    self.login_status_var.set("已登录")

                    # 关闭确认对话框
                    if hasattr(self, 'confirm_dialog') and self.confirm_dialog:
                        self.confirm_dialog.destroy()
                        self.confirm_dialog = None

                    # 关闭浏览器
                    self.browser_driver.quit()
                    self.browser_driver = None

                    messagebox.showinfo("登录成功", "已成功登录并保存Cookie")
                else:
                    messagebox.showwarning("警告", "无法保存Cookie，配置管理器不可用")
            else:
                messagebox.showwarning("警告", "浏览器已关闭，无法获取Cookie")
        except Exception as e:
            messagebox.showerror("错误", f"保存Cookie时出错: {str(e)}")
            import traceback
            traceback.print_exc()

            # 关闭确认对话框
            if hasattr(self, 'confirm_dialog') and self.confirm_dialog:
                self.confirm_dialog.destroy()
                self.confirm_dialog = None

    def _cancel_login(self):
        """取消登录过程"""
        # 关闭浏览器
        try:
            if hasattr(self, 'browser_driver') and self.browser_driver:
                self.browser_driver.quit()
                self.browser_driver = None
        except:
            pass

        # 关闭确认对话框
        if hasattr(self, 'confirm_dialog') and self.confirm_dialog:
            self.confirm_dialog.destroy()
            self.confirm_dialog = None

        # 更新状态
        self.login_status_var.set("登录已取消")

    def _show_error(self, message):
        """显示错误消息"""
        self.login_status_var.set("登录失败")
        messagebox.showerror("登录错误", message)

    def select_all_categories(self):
        """全选所有类别"""
        for var in self.category_vars.values():
            var.set(True)

    def deselect_all_categories(self):
        """取消全选所有类别"""
        for var in self.category_vars.values():
            var.set(False)

    def reset_categories(self):
        """恢复默认类别选择"""
        self.deselect_all_categories()
        default_categories = ["全部"]
        for category in default_categories:
            if category in self.category_vars:
                self.category_vars[category].set(True)

    def save_categories(self):
        """保存类别选择"""
        if self.config_manager:
            selected_categories = [cat for cat, var in self.category_vars.items() if var.get()]
            self.config_manager.update({self.config_key_categories: selected_categories})
            messagebox.showinfo("成功", "已保存类别选择")
        else:
            messagebox.showwarning("警告", "无法保存类别选择，配置管理器不可用")

    def save_platform_settings(self):
        """保存平台设置"""
        if self.config_manager:
            # 保存平台、内容类型和阅读量设置
            self.config_manager.update({
                self.config_key_platform: self.platform_var.get(),
                self.config_key_content_type: self.content_type_var.get(),
                self.config_key_read_count: self.read_count_var.get()
            })
            messagebox.showinfo("成功", "已保存平台设置")
        else:
            messagebox.showwarning("警告", "无法保存设置，配置管理器不可用")

    def load_settings(self):
        """加载保存的设置"""
        if self.config_manager:
            # 加载类别选择
            saved_categories = self.config_manager.get(self.config_key_categories, ["全部"])

            # 先取消全选
            self.deselect_all_categories()

            # 设置保存的类别
            for category in saved_categories:
                if category in self.category_vars:
                    self.category_vars[category].set(True)

            # 加载平台设置
            platform = self.config_manager.get(self.config_key_platform, "今日头条")
            content_type = self.config_manager.get(self.config_key_content_type, "图文")
            read_count = self.config_manager.get(self.config_key_read_count, "10000")

            # 设置平台、内容类型和阅读量
            self.platform_var.set(platform)
            self.content_type_var.set(content_type)
            self.read_count_var.set(read_count)

    def load_cookie(self):
        """加载保存的Cookie"""
        if self.config_manager:
            cookie = self.config_manager.get(self.config_key_cookie, "")
            if cookie:
                self.login_status_var.set("已登录")

    def open_daily_hot_site(self):
        """打开每日爆文网站"""
        # 检查是否有保存的Cookie
        if self.config_manager:
            cookie = self.config_manager.get(self.config_key_cookie, "")
            if cookie:
                try:
                    # 导入必要的模块
                    from selenium import webdriver
                    from selenium.webdriver.chrome.service import Service
                    from selenium.webdriver.chrome.options import Options
                    import os

                    # 创建Chrome浏览器选项
                    chrome_options = Options()
                    chrome_options.add_argument("--start-maximized")  # 最大化窗口

                    # 尝试直接使用Chrome驱动
                    driver = None
                    try:
                        # 尝试查找Chrome驱动
                        driver_path = None
                        possible_paths = [
                            os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "drivers", "chromedriver.exe"),
                            os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))), "drivers", "chromedriver.exe"),
                            "chromedriver.exe"
                        ]

                        for path in possible_paths:
                            if os.path.exists(path):
                                driver_path = path
                                break

                        if driver_path:
                            # 使用找到的驱动路径
                            service = Service(executable_path=driver_path)
                            driver = webdriver.Chrome(service=service, options=chrome_options)
                        else:
                            # 如果找不到驱动，尝试让Selenium自动查找
                            driver = webdriver.Chrome(options=chrome_options)
                    except Exception:
                        # 如果直接使用Chrome驱动失败，尝试使用webdriver_manager
                        try:
                            from webdriver_manager.chrome import ChromeDriverManager
                            service = Service(ChromeDriverManager().install())
                            driver = webdriver.Chrome(service=service, options=chrome_options)
                        except Exception:
                            # 如果所有方法都失败，使用普通的webbrowser打开
                            webbrowser.open(self.daily_hot_url)
                            return

                    # 先访问一个页面
                    driver.get("https://www.czgts.cn")

                    # 添加Cookie
                    cookie_list = cookie.split("; ")
                    for c in cookie_list:
                        if "=" in c:
                            name, value = c.split("=", 1)
                            driver.add_cookie({"name": name, "value": value, "domain": ".czgts.cn"})

                    # 打开目标页面
                    driver.get(self.daily_hot_url)

                    # 不关闭浏览器，让用户可以继续操作
                    return
                except Exception as e:
                    print(f"使用Cookie打开网站失败: {str(e)}")
                    # 如果出错，使用普通的webbrowser打开
                    webbrowser.open(self.daily_hot_url)
                    return

        # 如果没有Cookie或出错，使用普通的webbrowser打开
        webbrowser.open(self.daily_hot_url)

    def create_excel_tab(self, parent):
        """创建Excel处理选项卡内容"""
        # 使用PanedWindow创建可调整大小的分割窗口
        main_paned = ttk.PanedWindow(parent, orient=tk.VERTICAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 上部分 - 设置和操作区域
        top_frame = ttk.Frame(main_paned)

        # 说明标签
        desc_label = ttk.Label(top_frame, text="本工具用于处理Excel文件，可以筛选和删除A列中包含特定关键词的行，并复制列内容。",
                              wraplength=850, font=("微软雅黑", self.desc_font_size))
        desc_label.pack(pady=(5, 10), fill=tk.X)

        # 创建左右分栏
        settings_frame = ttk.Frame(top_frame)
        settings_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # 使用网格布局来确保两个框架并排显示且高度一致
        settings_frame.columnconfigure(0, weight=3)  # 左侧权重更大
        settings_frame.columnconfigure(1, weight=2)  # 右侧权重较小
        settings_frame.rowconfigure(0, weight=1)     # 行高度自适应

        # 左侧 - 筛选设置
        filter_frame = ttk.LabelFrame(settings_frame, text="Excel数据筛选设置", padding=10)
        filter_frame.grid(row=0, column=0, sticky="nsew", padx=(0, 5))

        # 确保筛选设置框架有足够的高度
        filter_frame.rowconfigure(0, weight=0)  # 标签行
        filter_frame.rowconfigure(1, weight=0)  # 输入框行
        filter_frame.rowconfigure(2, weight=0)  # 示例行
        filter_frame.rowconfigure(3, weight=0)  # 复选框行
        filter_frame.rowconfigure(4, weight=0)  # 按钮行
        filter_frame.columnconfigure(0, weight=1)  # 单列布局

        # 标签和输入框 - 使用网格布局
        # 标题标签
        label_frame = ttk.Frame(filter_frame)
        label_frame.grid(row=0, column=0, sticky="ew", pady=(0, 5))

        ttk.Label(label_frame, text="请输入要筛选的关键词",
                 font=("微软雅黑", self.default_font_size, "bold")).pack(side=tk.LEFT)
        ttk.Label(label_frame, text="（多个关键词用逗号分隔）",
                 foreground="gray").pack(side=tk.LEFT, padx=5)

        # 输入框
        self.keyword_var = tk.StringVar()
        keyword_entry = ttk.Entry(filter_frame, textvariable=self.keyword_var,
                                 font=("微软雅黑", self.default_font_size))
        keyword_entry.grid(row=1, column=0, sticky="ew", pady=5)

        # 示例文本
        example_frame = ttk.Frame(filter_frame)
        example_frame.grid(row=2, column=0, sticky="ew", pady=5)

        example_label = ttk.Label(example_frame, text="例如:", foreground="gray")
        example_label.pack(side=tk.LEFT)

        example_keywords = ttk.Label(example_frame, text="关键词1, 关键词2, 关键词3",
                                    foreground="blue", font=("微软雅黑", self.desc_font_size, "italic"))
        example_keywords.pack(side=tk.LEFT, padx=5)

        # 复制到剪贴板选项
        self.auto_copy_var = tk.BooleanVar(value=True)
        auto_copy_check = create_beautiful_checkbox(filter_frame,
                                         text="📋 筛选后自动复制列内容到剪贴板",
                                         variable=self.auto_copy_var,
                                         style_type="accent")
        auto_copy_check.grid(row=3, column=0, sticky="w", pady=10)

        # 记住设置按钮
        remember_button = ttk.Button(filter_frame, text="保存设置",
                                    command=self.save_excel_settings,
                                    style=getattr(self, 'button_style', 'TButton'))
        remember_button.grid(row=4, column=0, sticky="ew", pady=5)

        # 右侧 - 文件操作
        file_frame = ttk.LabelFrame(settings_frame, text="Excel文件操作", padding=10)
        file_frame.grid(row=0, column=1, sticky="nsew")

        # 确保文件操作框架有足够的高度
        file_frame.rowconfigure(0, weight=0)  # 选择文件按钮行
        file_frame.rowconfigure(1, weight=1)  # 文件路径行
        file_frame.rowconfigure(2, weight=0)  # 操作按钮行
        file_frame.columnconfigure(0, weight=1)  # 单列布局

        # 选择文件按钮
        select_file_button = ttk.Button(file_frame, text="选择Excel文件",
                                       command=self.select_excel_file,
                                       style=getattr(self, 'button_style', 'TButton'), width=18)
        select_file_button.grid(row=0, column=0, sticky="ew", pady=5)

        # 文件路径
        path_frame = ttk.Frame(file_frame)
        path_frame.grid(row=1, column=0, sticky="nsew", pady=5)
        path_frame.columnconfigure(0, weight=1)

        ttk.Label(path_frame, text="文件路径:").grid(row=0, column=0, sticky="w", pady=(0, 5))

        self.file_path_var = tk.StringVar(value="未选择文件")
        file_path_label = ttk.Label(path_frame, textvariable=self.file_path_var,
                                   background="#f0f0f0", relief="sunken", padding=5,
                                   wraplength=300)
        file_path_label.grid(row=1, column=0, sticky="ew", pady=5)

        # 操作按钮 - 使用水平布局
        buttons_frame = ttk.Frame(file_frame)
        buttons_frame.grid(row=2, column=0, sticky="ew", pady=10)

        # 配置按钮框架的列权重
        buttons_frame.columnconfigure(0, weight=1)
        buttons_frame.columnconfigure(1, weight=1)
        buttons_frame.columnconfigure(2, weight=1)

        # 筛选按钮
        filter_button = ttk.Button(buttons_frame, text="筛选删除关键词行",
                                  command=self.filter_excel,
                                  style=getattr(self, 'button_style', 'TButton'))
        filter_button.grid(row=0, column=0, padx=2, sticky="ew")

        # 复制按钮
        copy_button = ttk.Button(buttons_frame, text="复制列内容",
                                command=self.copy_column_content,
                                style=getattr(self, 'button_style', 'TButton'))
        copy_button.grid(row=0, column=1, padx=2, sticky="ew")

        # 保存按钮
        save_button = ttk.Button(buttons_frame, text="保存Excel文件",
                                command=self.save_excel_file,
                                style=getattr(self, 'button_style', 'TButton'))
        save_button.grid(row=0, column=2, padx=2, sticky="ew")

        # 下部分 - 数据预览区域
        preview_frame = ttk.LabelFrame(main_paned, text="数据预览", padding=10)

        # 预览状态和刷新按钮行
        status_row = ttk.Frame(preview_frame)
        status_row.pack(fill=tk.X, pady=(0, 5))

        # 预览状态
        self.preview_status_var = tk.StringVar(value="未加载数据")
        preview_status_label = ttk.Label(status_row, textvariable=self.preview_status_var)
        preview_status_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 刷新按钮
        refresh_button = ttk.Button(status_row, text="刷新预览",
                                   command=self.refresh_preview,
                                   width=10)
        refresh_button.pack(side=tk.RIGHT, padx=5)

        # 创建表格预览
        preview_table_frame = ttk.Frame(preview_frame)
        preview_table_frame.pack(fill=tk.BOTH, expand=True)

        # 配置网格布局
        preview_table_frame.grid_rowconfigure(0, weight=1)
        preview_table_frame.grid_columnconfigure(0, weight=1)

        # 创建表格（使用Treeview）
        columns = ("A列 (标题)", "B列 (内容)")
        self.preview_tree = ttk.Treeview(preview_table_frame, columns=columns, show="headings", height=15, selectmode="browse",
                                        style=getattr(self, 'treeview_style', 'Treeview'))

        # 设置列标题和宽度
        for col in columns:
            self.preview_tree.heading(col, text=col)
            if col == "A列 (标题)":
                self.preview_tree.column(col, width=350, anchor=tk.W, minwidth=200, stretch=True)
            else:
                # 增加B列宽度，确保内容能够显示
                self.preview_tree.column(col, width=650, anchor=tk.W, minwidth=400, stretch=True)

        # 添加滚轮支持
        def _on_mousewheel(event):
            self.preview_tree.yview_scroll(int(-1*(event.delta/120)), "units")

        def _on_horizontal_mousewheel(event):
            self.preview_tree.xview_scroll(int(-1*(event.delta/120)), "units")

        def _bind_mousewheel(event):
            self.preview_tree.bind_all("<MouseWheel>", _on_mousewheel)
            # 绑定Shift+滚轮进行水平滚动
            self.preview_tree.bind_all("<Shift-MouseWheel>", _on_horizontal_mousewheel)

        def _unbind_mousewheel(event):
            self.preview_tree.unbind_all("<MouseWheel>")
            self.preview_tree.unbind_all("<Shift-MouseWheel>")

        self.preview_tree.bind('<Enter>', _bind_mousewheel)
        self.preview_tree.bind('<Leave>', _unbind_mousewheel)

        # 表格布局
        self.preview_tree.grid(row=0, column=0, sticky="nsew")

        # 添加上下两个框架到主PanedWindow
        main_paned.add(top_frame, weight=3)  # 增加上部分的权重，确保有足够空间显示所有元素
        main_paned.add(preview_frame, weight=4)  # 给预览区域适当空间

        # 加载保存的设置
        self.load_excel_settings()

    def select_excel_file(self):
        """选择Excel文件"""
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel文件", "*.xlsx *.xls"), ("所有文件", "*.*")]
        )
        if file_path:
            self.file_path_var.set(file_path)
            self.load_excel_preview(file_path)

    def load_excel_preview(self, file_path):
        """加载Excel文件预览"""
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                messagebox.showerror("错误", f"文件不存在: {file_path}")
                return

            # 检查文件扩展名
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in ['.xlsx', '.xls', '.csv']:
                messagebox.showwarning("警告", f"不支持的文件格式: {file_ext}\n请使用.xlsx、.xls或.csv格式")

            # 使用pandas加载Excel文件，添加错误处理
            try:
                if file_ext == '.csv':
                    # 尝试不同的编码方式读取CSV文件
                    encodings = ['utf-8', 'gbk', 'gb2312', 'latin1']
                    for encoding in encodings:
                        try:
                            self.excel_data = pd.read_csv(file_path, encoding=encoding)
                            break
                        except UnicodeDecodeError:
                            continue
                    else:
                        # 如果所有编码都失败
                        messagebox.showerror("错误", "无法解码CSV文件，请检查文件编码")
                        return
                else:
                    # 读取Excel文件
                    self.excel_data = pd.read_excel(file_path)
            except Exception as e:
                messagebox.showerror("错误", f"读取文件失败: {str(e)}")
                return

            # 复制数据
            self.filtered_data = self.excel_data.copy()

            # 确保至少有两列数据
            if len(self.excel_data.columns) < 2:
                # 如果只有一列，添加一个空列
                if len(self.excel_data.columns) == 1:
                    # 添加第二列
                    self.excel_data["内容"] = ""
                    self.filtered_data = self.excel_data.copy()
                    messagebox.showinfo("提示", "文件只有一列数据，已自动添加空的第二列")
                else:
                    messagebox.showwarning("警告", "Excel文件没有足够的列")
                    return

            # 更新状态
            self.preview_status_var.set(f"已加载文件: {os.path.basename(file_path)} (共{len(self.excel_data)}行)")

            # 清空现有数据
            for item in self.preview_tree.get_children():
                self.preview_tree.delete(item)

            # 添加实际数据
            max_rows = min(100, len(self.excel_data))  # 最多显示100行，避免加载过多数据
            for i in range(max_rows):
                # 获取前两列的值
                col1 = ""
                col2 = ""

                # 获取第一列值
                if len(self.excel_data.columns) > 0:
                    try:
                        value = self.excel_data.iloc[i, 0]
                        if not pd.isna(value):
                            # 确保转换为字符串，处理所有可能的数据类型
                            col1 = str(value).strip()
                    except Exception as e:
                        # 如果获取或转换值时出错，记录错误并使用错误提示
                        print(f"获取第一列值时出错: {str(e)}")
                        col1 = "[数据读取错误]"

                # 获取第二列值
                if len(self.excel_data.columns) > 1:
                    try:
                        value = self.excel_data.iloc[i, 1]
                        if not pd.isna(value):
                            # 确保转换为字符串，处理所有可能的数据类型
                            col2_text = str(value).strip()
                            # 如果文本过长，截断显示
                            max_length = 1000  # 设置最大长度
                            if len(col2_text) > max_length:
                                col2 = col2_text[:max_length] + "... [文本过长，已截断]"
                            else:
                                col2 = col2_text
                    except Exception as e:
                        # 如果获取或转换值时出错，记录错误并使用空字符串
                        print(f"获取第二列值时出错: {str(e)}")
                        col2 = "[数据读取错误]"

                # 插入到树形视图
                self.preview_tree.insert("", tk.END, values=(col1, col2))

            if len(self.excel_data) > max_rows:
                self.preview_tree.insert("", tk.END, values=("...", f"(仅显示前{max_rows}行，共{len(self.excel_data)}行)"))

            messagebox.showinfo("成功", f"已加载Excel文件预览，共{len(self.excel_data)}行")
        except Exception as e:
            self.preview_status_var.set("加载文件失败")
            messagebox.showerror("错误", f"加载Excel文件失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def filter_excel(self):
        """筛选Excel文件"""
        if self.file_path_var.get() == "未选择文件":
            messagebox.showwarning("警告", "请先选择Excel文件")
            return

        if self.excel_data is None:
            messagebox.showwarning("警告", "Excel数据为空，请重新加载文件")
            return

        keywords = self.keyword_var.get().split(",")
        keywords = [k.strip() for k in keywords if k.strip()]

        if not keywords:
            messagebox.showwarning("警告", "请输入至少一个关键词")
            return

        try:
            # 复制原始数据
            self.filtered_data = self.excel_data.copy()

            # 获取第一列数据
            if len(self.excel_data.columns) > 0:
                first_col = self.excel_data.iloc[:, 0]

                # 创建筛选条件
                mask = pd.Series(False, index=first_col.index)

                # 对每个关键词进行筛选
                for keyword in keywords:
                    if keyword.strip():  # 确保关键词不为空
                        # 将列转换为字符串并检查是否包含关键词
                        mask = mask | first_col.astype(str).str.contains(keyword.strip(), case=False, na=False)

                # 获取需要删除的行索引
                rows_to_remove = self.filtered_data[mask].index

                # 从数据中删除这些行
                self.filtered_data = self.filtered_data.drop(rows_to_remove)

                # 更新预览
                self.update_preview_with_filtered_data()

                # 如果设置了自动复制，则复制列内容
                if self.auto_copy_var.get():
                    self.copy_column_content()

                # 显示成功消息
                removed_count = len(rows_to_remove)
                remaining_count = len(self.filtered_data)
                messagebox.showinfo("成功", f"已筛选并删除包含以下关键词的行: {', '.join(keywords)}\n\n删除了 {removed_count} 行，剩余 {remaining_count} 行")
            else:
                messagebox.showwarning("警告", "Excel文件没有数据列")
        except Exception as e:
            messagebox.showerror("错误", f"筛选Excel文件失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def update_preview_with_filtered_data(self):
        """使用筛选后的数据更新预览"""
        if self.filtered_data is None:
            return

        # 清空现有数据
        for item in self.preview_tree.get_children():
            self.preview_tree.delete(item)

        # 添加筛选后的数据
        max_rows = min(100, len(self.filtered_data))  # 最多显示100行
        for i in range(max_rows):
            # 获取前两列的值
            col1 = ""
            col2 = ""

            # 获取第一列值
            if len(self.filtered_data.columns) > 0:
                try:
                    value = self.filtered_data.iloc[i, 0]
                    if not pd.isna(value):
                        # 确保转换为字符串，处理所有可能的数据类型
                        col1 = str(value).strip()
                except Exception as e:
                    # 如果获取或转换值时出错，记录错误并使用错误提示
                    print(f"获取筛选后第一列值时出错: {str(e)}")
                    col1 = "[数据读取错误]"

            # 获取第二列值
            if len(self.filtered_data.columns) > 1:
                try:
                    value = self.filtered_data.iloc[i, 1]
                    if not pd.isna(value):
                        # 确保转换为字符串，处理所有可能的数据类型
                        col2_text = str(value).strip()
                        # 如果文本过长，截断显示
                        max_length = 1000  # 设置最大长度
                        if len(col2_text) > max_length:
                            col2 = col2_text[:max_length] + "... [文本过长，已截断]"
                        else:
                            col2 = col2_text
                except Exception as e:
                    # 如果获取或转换值时出错，记录错误并使用空字符串
                    print(f"获取筛选后第二列值时出错: {str(e)}")
                    col2 = "[数据读取错误]"

            # 插入到树形视图
            self.preview_tree.insert("", tk.END, values=(col1, col2))

        if len(self.filtered_data) > max_rows:
            self.preview_tree.insert("", tk.END, values=("...", f"(仅显示前{max_rows}行，共{len(self.filtered_data)}行)"))

        # 更新状态
        self.preview_status_var.set(f"筛选后: {len(self.filtered_data)}行")

    def copy_column_content(self):
        """复制列内容到剪贴板"""
        if self.file_path_var.get() == "未选择文件":
            messagebox.showwarning("警告", "请先选择Excel文件")
            return

        if self.filtered_data is None or len(self.filtered_data) == 0:
            messagebox.showwarning("警告", "没有可复制的数据")
            return

        try:
            # 获取第二列数据（如果存在）
            if len(self.filtered_data.columns) >= 2:
                # 使用第二列
                column_data = self.filtered_data.iloc[:, 1]
            else:
                # 否则使用第一列
                column_data = self.filtered_data.iloc[:, 0]

            # 将数据转换为字符串并过滤掉NaN值和空字符串
            text_data = []
            for item in column_data:
                try:
                    if not pd.isna(item) and str(item).strip() != "":
                        text_data.append(str(item).strip())
                except Exception as e:
                    print(f"处理列数据时出错: {str(e)}")
                    # 添加错误提示，但继续处理其他数据
                    text_data.append("[数据处理错误]")

            # 合并为一个字符串
            try:
                clipboard_text = "\n".join(text_data)

                # 复制到剪贴板
                self.dialog.clipboard_clear()
                self.dialog.clipboard_append(clipboard_text)
            except Exception as e:
                # 如果数据太大或有其他问题，尝试分批处理
                print(f"复制到剪贴板时出错: {str(e)}")
                messagebox.showwarning("警告", "数据量可能过大，尝试分批复制...")

                # 分批处理，每批最多1000行
                batch_size = 1000
                copied = False
                for i in range(0, len(text_data), batch_size):
                    batch = text_data[i:i+batch_size]
                    try:
                        batch_text = "\n".join(batch)
                        self.dialog.clipboard_clear()
                        self.dialog.clipboard_append(batch_text)
                        messagebox.showinfo("部分复制成功", f"已复制第{i+1}到第{min(i+batch_size, len(text_data))}行内容到剪贴板")
                        copied = True
                        break  # 只复制第一批，避免多次提示
                    except Exception as batch_error:
                        print(f"分批复制时出错: {str(batch_error)}")
                        continue

                # 如果分批处理也失败，显示错误消息
                if not copied:
                    messagebox.showerror("错误", "无法复制数据到剪贴板，数据可能过大或包含不支持的字符")
                return  # 已经显示了消息，不需要再显示成功消息

            # 只有在正常复制成功时才显示此消息
            messagebox.showinfo("成功", f"已复制{len(text_data)}行内容到剪贴板")
        except Exception as e:
            messagebox.showerror("错误", f"复制列内容失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def save_excel_file(self):
        """保存Excel文件"""
        if self.file_path_var.get() == "未选择文件":
            messagebox.showwarning("警告", "请先选择Excel文件")
            return

        if self.filtered_data is None:
            messagebox.showwarning("警告", "没有可保存的数据")
            return

        save_path = filedialog.asksaveasfilename(
            title="保存Excel文件",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")],
            defaultextension=".xlsx"
        )

        if save_path:
            try:
                # 保存筛选后的数据到Excel文件
                self.filtered_data.to_excel(save_path, index=False)
                messagebox.showinfo("成功", f"已保存Excel文件到: {save_path}\n\n共保存了{len(self.filtered_data)}行数据")
            except Exception as e:
                messagebox.showerror("错误", f"保存Excel文件失败: {str(e)}")
                import traceback
                traceback.print_exc()

    def save_excel_settings(self):
        """保存Excel设置"""
        if self.config_manager:
            settings = {
                "excel_keywords": self.keyword_var.get(),
                "excel_auto_copy": self.auto_copy_var.get()
            }
            self.config_manager.update(settings)
            messagebox.showinfo("成功", "已保存Excel设置")
        else:
            messagebox.showwarning("警告", "无法保存设置，配置管理器不可用")

    def refresh_preview(self):
        """刷新Excel预览"""
        if self.file_path_var.get() != "未选择文件":
            # 重新加载原始数据
            self.load_excel_preview(self.file_path_var.get())

            # 重置筛选状态
            self.filtered_data = self.excel_data.copy() if self.excel_data is not None else None

            # 更新状态
            if self.excel_data is not None:
                self.preview_status_var.set(f"已重新加载文件: {os.path.basename(self.file_path_var.get())} (共{len(self.excel_data)}行)")
        else:
            messagebox.showwarning("警告", "请先选择Excel文件")

    def load_excel_settings(self):
        """加载Excel设置"""
        if self.config_manager:
            keywords = self.config_manager.get("excel_keywords", "")
            auto_copy = self.config_manager.get("excel_auto_copy", True)

            self.keyword_var.set(keywords)
            self.auto_copy_var.set(auto_copy)

    def create_custom_styles(self):
        """创建自定义样式 - 只影响当前对话框"""
        try:
            # 创建独立的样式实例，避免影响全局样式
            self.style = ttk.Style()

            # 获取系统默认按钮颜色
            default_button_bg = self.style.lookup('TButton', 'background')
            default_button_fg = self.style.lookup('TButton', 'foreground')

            # 创建对话框专用的按钮样式，使用唯一的样式名
            dialog_button_style = f"DailyHot.Accent.TButton"
            self.style.configure(dialog_button_style,
                           background=default_button_bg,
                           foreground=default_button_fg,
                           font=("微软雅黑", self.default_font_size, "bold"),
                           relief="raised",
                           borderwidth=1)
            self.style.map(dialog_button_style,
                     background=[('active', '#f0f0f0'),
                                ('pressed', '#e0e0e0')],
                     relief=[('pressed', 'sunken'),
                            ('active', 'raised')])

            # 创建对话框专用的表格样式，使用唯一的样式名
            dialog_treeview_style = f"DailyHot.Treeview"
            self.style.configure(dialog_treeview_style,
                           font=("微软雅黑", self.default_font_size),
                           rowheight=25)

            # 创建对话框专用的表格标题样式，使用唯一的样式名
            dialog_heading_style = f"DailyHot.Treeview.Heading"
            self.style.configure(dialog_heading_style,
                           font=("微软雅黑", self.default_font_size, "bold"),
                           background="#e0e0e0")

            # 创建美化的复选框样式
            dialog_checkbutton_style = f"DailyHot.TCheckbutton"
            self.style.configure(dialog_checkbutton_style,
                           font=("微软雅黑", self.default_font_size),
                           focuscolor="none",
                           indicatorsize=16,
                           indicatormargins=(2, 2, 4, 2))

            # 设置复选框的选中和未选中状态颜色
            self.style.map(dialog_checkbutton_style,
                     background=[('active', '#e8f4fd'),
                               ('pressed', '#cce7f0'),
                               ('selected', '#f0f8ff')],
                     foreground=[('active', '#0078d4'),
                               ('pressed', '#106ebe'),
                               ('selected', '#0066cc')],
                     indicatorcolor=[('selected', '#0078d4'),
                                   ('active', '#106ebe'),
                                   ('pressed', '#005a9e')])

            # 保存样式名称供后续使用
            self.button_style = dialog_button_style
            self.treeview_style = dialog_treeview_style
            self.heading_style = dialog_heading_style
            self.checkbutton_style = dialog_checkbutton_style

        except Exception as e:
            # 如果样式创建失败，使用默认样式
            print(f"创建爆文工具自定义样式失败: {e}")
            self.button_style = "TButton"
            self.treeview_style = "Treeview"
            self.heading_style = "Treeview.Heading"
            self.checkbutton_style = "TCheckbutton"

    def update_status(self, message, status_type="info"):
        """更新状态显示

        Args:
            message: 状态消息
            status_type: 状态类型 ("info", "success", "warning", "error")
        """
        try:
            # 状态图标和颜色映射
            status_config = {
                "info": {"icon": "🔵", "color": "#17A2B8"},
                "success": {"icon": "🟢", "color": "#28A745"},
                "warning": {"icon": "🟡", "color": "#FFC107"},
                "error": {"icon": "🔴", "color": "#DC3545"}
            }

            config = status_config.get(status_type, status_config["info"])

            # 更新状态标签
            if hasattr(self, 'status_label'):
                self.status_label.config(text=f"{config['icon']} {message}",
                                       foreground=config['color'])

        except Exception as e:
            print(f"更新状态失败: {e}")

    def show_progress(self, message="", progress=0):
        """显示进度条

        Args:
            message: 进度消息
            progress: 进度值 (0-100)
        """
        try:
            if hasattr(self, 'progress_bar') and hasattr(self, 'progress_text'):
                # 显示进度条和文本
                self.progress_bar.pack(fill=tk.X, pady=(0, 5))
                self.progress_text.pack()

                # 更新进度
                self.progress_var.set(progress)
                self.progress_text.config(text=message)

                # 刷新界面
                self.dialog.update_idletasks()

        except Exception as e:
            print(f"显示进度失败: {e}")

    def hide_progress(self):
        """隐藏进度条"""
        try:
            if hasattr(self, 'progress_bar') and hasattr(self, 'progress_text'):
                self.progress_bar.pack_forget()
                self.progress_text.pack_forget()

        except Exception as e:
            print(f"隐藏进度失败: {e}")

    def update_login_status(self, status, is_logged_in=False):
        """更新登录状态

        Args:
            status: 状态文本
            is_logged_in: 是否已登录
        """
        try:
            self.login_status_var.set(status)

            if hasattr(self, 'login_status_icon'):
                if is_logged_in:
                    self.login_status_icon.config(text="🟢")
                    if hasattr(self, 'login_button'):
                        self.login_button.config(text="✅ 已登录", state="disabled")
                else:
                    self.login_status_icon.config(text="🔴")
                    if hasattr(self, 'login_button'):
                        self.login_button.config(text="🌐 登录网站", state="normal")

        except Exception as e:
            print(f"更新登录状态失败: {e}")

    def get_hot_articles(self):
        """获取热门文章"""
        # 导入必要的模块
        import threading

        # 获取选中的类别
        selected_categories = [cat for cat, var in self.category_vars.items() if var.get()]

        # 验证是否选择了类别
        if not selected_categories:
            messagebox.showwarning("警告", "请至少选择一个文章类别")
            return

        # 获取平台和内容类型
        platform = self.platform_var.get()
        content_type = self.content_type_var.get()

        # 获取阅读量
        try:
            read_count = int(self.read_count_var.get())
            if read_count < 0:
                raise ValueError("阅读量不能为负数")
        except ValueError:
            messagebox.showwarning("警告", "阅读量必须是有效的正整数")
            return

        # 显示选择的参数
        category_text = ', '.join(selected_categories[:5])  # 只显示前5个类别
        if len(selected_categories) > 5:
            category_text += f" 等{len(selected_categories)}个类别"

        message = f"准备获取热门文章：\n\n📱 平台：{platform}\n📄 内容类型：{content_type}\n📂 类别：{category_text}\n📊 阅读量阈值：{read_count:,}"

        result = messagebox.askyesno("确认获取", f"{message}\n\n是否开始获取？")
        if not result:
            return

        # 更新状态
        self.update_status("正在准备获取热门文章", "info")
        self.show_progress("正在初始化浏览器...", 10)

        # 使用线程运行浏览器，避免阻塞主线程
        self.hot_articles_thread = threading.Thread(target=self._run_hot_articles_browser,
                                                  args=(platform, content_type, selected_categories, read_count))
        self.hot_articles_thread.daemon = True  # 设置为守护线程，主程序退出时自动结束
        self.hot_articles_thread.start()

    def _run_hot_articles_browser(self, platform, content_type, categories, read_count):
        """在单独的线程中运行获取热门文章的浏览器"""
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        import time
        import os

        driver = None
        try:
            # 创建Chrome浏览器选项
            chrome_options = Options()
            chrome_options.add_argument("--start-maximized")  # 最大化窗口
            chrome_options.add_argument("--disable-extensions")  # 禁用扩展
            chrome_options.add_argument("--disable-gpu")  # 禁用GPU加速
            chrome_options.add_argument("--disable-dev-shm-usage")  # 禁用/dev/shm使用
            chrome_options.add_argument("--no-sandbox")  # 禁用沙盒

            # 尝试直接使用Chrome驱动
            try:
                # 尝试查找Chrome驱动
                driver_path = None
                possible_paths = [
                    os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "drivers", "chromedriver.exe"),
                    os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))), "drivers", "chromedriver.exe"),
                    "chromedriver.exe"
                ]

                for path in possible_paths:
                    if os.path.exists(path):
                        driver_path = path
                        break

                if driver_path:
                    # 使用找到的驱动路径
                    service = Service(executable_path=driver_path)
                    driver = webdriver.Chrome(service=service, options=chrome_options)
                else:
                    # 如果找不到驱动，尝试让Selenium自动查找
                    driver = webdriver.Chrome(options=chrome_options)
            except Exception as driver_error:
                # 如果直接使用Chrome驱动失败，尝试使用webdriver_manager
                try:
                    from webdriver_manager.chrome import ChromeDriverManager
                    service = Service(ChromeDriverManager().install())
                    driver = webdriver.Chrome(service=service, options=chrome_options)
                except Exception as wdm_error:
                    # 在主线程中显示错误消息
                    self.dialog.after(0, lambda: messagebox.showerror("错误",
                                                                    f"无法创建Chrome浏览器实例: {str(driver_error)}\n尝试使用webdriver_manager也失败: {str(wdm_error)}"))
                    return

            # 保存driver引用，以便在其他方法中访问
            self.browser_driver = driver

            # 检查是否有保存的Cookie
            cookie = ""
            if self.config_manager:
                cookie = self.config_manager.get(self.config_key_cookie, "")

            if not cookie:
                # 如果没有Cookie，提示用户先登录
                self.dialog.after(0, lambda: messagebox.showwarning("警告", "请先登录网站"))
                driver.quit()
                self.browser_driver = None
                return

            # 先访问一个页面
            driver.get("https://www.czgts.cn")

            # 添加Cookie
            cookie_list = cookie.split("; ")
            for c in cookie_list:
                if "=" in c:
                    name, value = c.split("=", 1)
                    driver.add_cookie({"name": name, "value": value, "domain": ".czgts.cn"})

            # 打开目标页面
            driver.get(self.daily_hot_url)

            # 等待页面加载完成
            wait = WebDriverWait(driver, 10)

            try:
                # 1. 选择媒体平台
                platform_xpath = ""
                if platform == "今日头条":
                    platform_xpath = '//*[@id="platforms"]/div/div/div/label[1]/span'
                elif platform == "百家号":
                    platform_xpath = '//*[@id="platforms"]/div/div/div/label[2]/span'

                if platform_xpath:
                    platform_element = wait.until(EC.element_to_be_clickable((By.XPATH, platform_xpath)))
                    platform_element.click()
                    time.sleep(1)  # 等待平台选择生效

                # 2. 选择内容类型
                content_type_xpath = ""
                if content_type == "图文":
                    content_type_xpath = '//*[@id="articleGenres"]/div/div/div/label[1]/span'
                elif content_type == "视频":
                    content_type_xpath = '//*[@id="articleGenres"]/div/div/div/label[3]/span'
                elif content_type == "问答":
                    content_type_xpath = '//*[@id="articleGenres"]/div/div/div/label[2]/span'

                if content_type_xpath:
                    content_type_element = wait.until(EC.element_to_be_clickable((By.XPATH, content_type_xpath)))
                    content_type_element.click()
                    time.sleep(1)  # 等待内容类型选择生效

                # 3. 选择内容领域
                try:
                    # 查找内容领域元素，尝试多个可能的XPath
                    domain_container = None
                    possible_xpaths = [
                        '//*[@id="arco-tabs-0-panel-1"]/div/div/div[1]/form/div[3]',
                        '//div[contains(@class, "domain-container")]',
                        '//div[contains(text(), "内容领域")]/following-sibling::div'
                    ]

                    for xpath in possible_xpaths:
                        try:
                            domain_container = wait.until(EC.presence_of_element_located((By.XPATH, xpath)))
                            if domain_container:
                                break
                        except:
                            continue

                    if not domain_container:
                        # 如果找不到内容领域容器，记录错误但继续执行
                        print("无法找到内容领域容器元素")
                        self.dialog.after(0, lambda: messagebox.showwarning("警告", "无法找到内容领域选择区域，将跳过类别选择"))
                    else:
                        # 遍历所有类别，查找匹配的内容领域并点击
                        for category in categories:
                            # 跳过"全部"类别
                            if category == "全部":
                                continue

                            try:
                                # 使用文本查找方法查找类别
                                category_elements = domain_container.find_elements(By.XPATH, f".//span[contains(text(), '{category}')]")
                                if category_elements:
                                    category_elements[0].click()
                                    time.sleep(0.5)  # 等待类别选择生效
                            except Exception as e:
                                print(f"选择类别 {category} 时出错: {str(e)}")
                except Exception as domain_error:
                    print(f"处理内容领域时出错: {str(domain_error)}")
                    # 记录错误但继续执行
                    self.dialog.after(0, lambda: messagebox.showwarning("警告", f"设置内容领域时出错: {str(domain_error)}\n将继续执行其他设置"))

                # 4. 输入阅读量
                try:
                    # 尝试多个可能的XPath来定位阅读量输入框
                    read_count_input = None
                    read_count_xpaths = [
                        '//*[@id="readLimits"]/div/div/div[2]/div[1]/span/span/input',
                        '//div[contains(text(), "阅读量")]/following-sibling::div//input',
                        '//input[@placeholder="请输入阅读量"]'
                    ]

                    for xpath in read_count_xpaths:
                        try:
                            read_count_input = wait.until(EC.presence_of_element_located((By.XPATH, xpath)))
                            if read_count_input:
                                break
                        except:
                            continue

                    if read_count_input:
                        read_count_input.clear()
                        read_count_input.send_keys(str(read_count))
                    else:
                        # 如果找不到阅读量输入框，记录错误
                        print("无法找到阅读量输入框")
                        self.dialog.after(0, lambda: messagebox.showwarning("警告", "无法找到阅读量输入框，请手动设置阅读量"))
                except Exception as e:
                    print(f"设置阅读量时出错: {str(e)}")
                    self.dialog.after(0, lambda: messagebox.showwarning("警告", f"设置阅读量时出错: {str(e)}\n请手动设置阅读量"))

                # 显示成功消息
                self.dialog.after(0, lambda: messagebox.showinfo("成功", "已成功设置热门文章参数"))

            except Exception as e:
                self.dialog.after(0, lambda: messagebox.showerror("错误", f"设置热门文章参数时出错: {str(e)}"))
                import traceback
                traceback.print_exc()

        except Exception as e:
            # 在主线程中显示错误消息
            self.dialog.after(0, lambda: messagebox.showerror("错误", f"获取热门文章时出错: {str(e)}"))
            import traceback
            traceback.print_exc()

            # 确保浏览器关闭
            try:
                if driver:
                    driver.quit()
                    self.browser_driver = None
            except:
                pass
