[2025-07-20 11:22:29] [INFO] 🚀 启动多平台存稿工具...
[2025-07-20 11:22:29] [INFO] 已从toutiao平台加载存稿详情数据，共 83 个账号
[2025-07-20 11:22:29] [INFO] ✅ 定时任务面板已成功集成
[2025-07-20 11:22:29] [INFO] 已确保所有必要目录存在
[2025-07-20 11:22:30] [INFO] 🔍 [调试] 当前平台: toutiao
[2025-07-20 11:22:30] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-07-20 11:22:30] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-07-20 11:22:30] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-20 11:22:30] [INFO] 🔍 [调试] 更新账号树 - 当前平台: toutiao
[2025-07-20 11:22:30] [INFO] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-20 11:22:30] [INFO] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-07-20 11:22:30] [INFO] 已加载账号数据: 79 条记录
[2025-07-20 11:22:30] [INFO] 🔍 [调试] 账号数据为空或未加载
[2025-07-20 11:22:30] [INFO] ✅ 初始UI更新完成
[2025-07-20 11:22:31] [INFO] ✅ 定时任务正在运行，计划执行时间: 2025-07-21T10:28:00
[2025-07-20 11:22:32] [INFO] 已加载 59 个账号
[2025-07-20 11:22:32] [INFO] 🔍 [调试] 当前平台: toutiao
[2025-07-20 11:22:32] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-07-20 11:22:32] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-07-20 11:22:32] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-20 11:22:32] [INFO] 🔍 [调试] 更新账号树 - 当前平台: toutiao
[2025-07-20 11:22:32] [INFO] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-20 11:22:32] [INFO] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-07-20 11:22:32] [INFO] 已加载账号数据: 79 条记录
[2025-07-20 11:22:32] [INFO] 🔍 [调试] 账号数据为空或未加载
[2025-07-20 11:22:32] [INFO] 已加载 59 个账号
[2025-07-20 11:22:32] [INFO] 🔍 [调试] 当前平台: toutiao
[2025-07-20 11:22:32] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-07-20 11:22:32] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-07-20 11:22:32] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-20 11:22:32] [INFO] 🔍 [调试] 更新账号树 - 当前平台: toutiao
[2025-07-20 11:22:32] [INFO] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-20 11:22:32] [INFO] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-07-20 11:22:32] [INFO] 已加载账号数据: 79 条记录
[2025-07-20 11:22:32] [INFO] 🔍 [调试] 账号数据为空或未加载
[2025-07-20 11:22:32] [INFO] 已加载 59 个账号
[2025-07-20 11:22:32] [INFO] 🔍 [调试] 当前平台: toutiao
[2025-07-20 11:22:32] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-07-20 11:22:32] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-07-20 11:22:32] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-20 11:22:32] [INFO] 🔍 [调试] 更新账号树 - 当前平台: toutiao
[2025-07-20 11:22:32] [INFO] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-20 11:22:32] [INFO] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-07-20 11:22:33] [INFO] 已加载账号数据: 79 条记录
[2025-07-20 11:22:33] [INFO] 🔍 [调试] 账号数据为空或未加载
[2025-07-20 11:22:33] [INFO] 已加载 59 个账号
[2025-07-20 11:22:33] [INFO] 🔍 [调试] 当前平台: toutiao
[2025-07-20 11:22:33] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-07-20 11:22:33] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-07-20 11:22:33] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-20 11:22:33] [INFO] 🔍 [调试] 更新账号树 - 当前平台: toutiao
[2025-07-20 11:22:33] [INFO] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-20 11:22:33] [INFO] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-07-20 11:22:33] [INFO] 已加载账号数据: 79 条记录
[2025-07-20 11:22:33] [INFO] 🔍 [调试] 账号数据为空或未加载
[2025-07-20 11:22:48] [INFO] 平台切换前保存 toutiao 的设置和存稿详情数据
[2025-07-20 11:22:48] [INFO] ✅ 设置已静默保存
[2025-07-20 11:22:48] [INFO] 已确保所有必要目录存在
[2025-07-20 11:22:48] [INFO] 正在更新账号管理器，当前平台: netease
[2025-07-20 11:22:48] [INFO] 账号目录: D:/网易号全自动/网易号账号
[2025-07-20 11:22:48] [INFO] 已加载 19 个netease平台账号
[2025-07-20 11:22:48] [INFO] 🔍 [调试] 当前平台: netease
[2025-07-20 11:22:48] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/网易号账号
[2025-07-20 11:22:48] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\网易号账号
[2025-07-20 11:22:48] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\网易号账号\netease_account_data.json
[2025-07-20 11:22:48] [INFO] 已加载账号数据: 24 条记录
[2025-07-20 11:22:48] [INFO] 账号数据文件路径: D:\网易号全自动\网易号账号\netease_account_data.json
[2025-07-20 11:22:48] [INFO] 已更新数据查询管理器的AccountData引用
[2025-07-20 11:22:48] [INFO] 已更新Excel导出管理器的AccountData引用
[2025-07-20 11:22:48] [INFO] 已从netease平台加载存稿详情数据，共 18 个账号
[2025-07-20 11:22:48] [INFO] [netease] ✅ 视频分配跟踪器初始化成功
[2025-07-20 11:22:48] [INFO] 已切换到平台: 网易号平台
[2025-07-20 11:22:48] [INFO] [netease] ✅ 视频分配跟踪器初始化成功
[2025-07-20 11:22:48] [INFO] 🔍 [调试] 当前平台: netease
[2025-07-20 11:22:48] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/网易号账号
[2025-07-20 11:22:48] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\网易号账号
[2025-07-20 11:22:48] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\网易号账号\netease_account_data.json
[2025-07-20 11:22:48] [INFO] 🔍 [调试] 更新账号树 - 当前平台: netease
[2025-07-20 11:22:48] [INFO] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\网易号账号\netease_account_data.json
[2025-07-20 11:22:48] [INFO] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-07-20 11:22:48] [INFO] 已加载账号数据: 24 条记录
[2025-07-20 11:22:48] [INFO] 🔍 [调试] 账号数据为空或未加载
[2025-07-20 11:22:48] [INFO] ✅ 设置已静默保存
[2025-07-20 11:22:51] [INFO] 已加载 19 个账号
[2025-07-20 11:22:51] [INFO] 🔍 [调试] 当前平台: netease
[2025-07-20 11:22:51] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/网易号账号
[2025-07-20 11:22:51] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\网易号账号
[2025-07-20 11:22:51] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\网易号账号\netease_account_data.json
[2025-07-20 11:22:51] [INFO] 🔍 [调试] 更新账号树 - 当前平台: netease
[2025-07-20 11:22:51] [INFO] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\网易号账号\netease_account_data.json
[2025-07-20 11:22:51] [INFO] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-07-20 11:22:51] [INFO] 已加载账号数据: 24 条记录
[2025-07-20 11:22:51] [INFO] 🔍 [调试] 账号数据为空或未加载
[2025-07-20 11:22:52] [INFO] 已加载 19 个账号
[2025-07-20 11:22:52] [INFO] 🔍 [调试] 当前平台: netease
[2025-07-20 11:22:52] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/网易号账号
[2025-07-20 11:22:52] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\网易号账号
[2025-07-20 11:22:52] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\网易号账号\netease_account_data.json
[2025-07-20 11:22:52] [INFO] 🔍 [调试] 更新账号树 - 当前平台: netease
[2025-07-20 11:22:52] [INFO] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\网易号账号\netease_account_data.json
[2025-07-20 11:22:52] [INFO] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-07-20 11:22:52] [INFO] 已加载账号数据: 24 条记录
[2025-07-20 11:22:52] [INFO] 🔍 [调试] 账号数据为空或未加载
[2025-07-20 11:22:52] [INFO] 已加载 19 个账号
[2025-07-20 11:22:52] [INFO] 🔍 [调试] 当前平台: netease
[2025-07-20 11:22:52] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/网易号账号
[2025-07-20 11:22:52] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\网易号账号
[2025-07-20 11:22:52] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\网易号账号\netease_account_data.json
[2025-07-20 11:22:52] [INFO] 🔍 [调试] 更新账号树 - 当前平台: netease
[2025-07-20 11:22:52] [INFO] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\网易号账号\netease_account_data.json
[2025-07-20 11:22:52] [INFO] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-07-20 11:22:52] [INFO] 已加载账号数据: 24 条记录
[2025-07-20 11:22:52] [INFO] 🔍 [调试] 账号数据为空或未加载
[2025-07-20 11:22:53] [INFO] 平台切换前保存 netease 的设置和存稿详情数据
[2025-07-20 11:22:53] [INFO] ✅ 设置已静默保存
[2025-07-20 11:22:53] [INFO] 已确保所有必要目录存在
[2025-07-20 11:22:53] [INFO] 正在更新账号管理器，当前平台: toutiao
[2025-07-20 11:22:53] [INFO] 账号目录: D:/网易号全自动/头条号账号
[2025-07-20 11:22:53] [INFO] 已加载 59 个toutiao平台账号
[2025-07-20 11:22:53] [INFO] 🔍 [调试] 当前平台: toutiao
[2025-07-20 11:22:53] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-07-20 11:22:53] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-07-20 11:22:53] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-20 11:22:53] [INFO] 已加载账号数据: 79 条记录
[2025-07-20 11:22:53] [INFO] 账号数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-20 11:22:53] [INFO] 已更新数据查询管理器的AccountData引用
[2025-07-20 11:22:53] [INFO] 已更新Excel导出管理器的AccountData引用
[2025-07-20 11:22:53] [INFO] 已从toutiao平台加载存稿详情数据，共 83 个账号
[2025-07-20 11:22:53] [INFO] 已切换到平台: 今日头条平台
[2025-07-20 11:22:53] [INFO] 🔍 [调试] 当前平台: toutiao
[2025-07-20 11:22:53] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-07-20 11:22:53] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-07-20 11:22:53] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-20 11:22:53] [INFO] 🔍 [调试] 更新账号树 - 当前平台: toutiao
[2025-07-20 11:22:53] [INFO] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-20 11:22:53] [INFO] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-07-20 11:22:53] [INFO] 已加载账号数据: 79 条记录
[2025-07-20 11:22:53] [INFO] 🔍 [调试] 账号数据为空或未加载
[2025-07-20 11:22:53] [INFO] ✅ 设置已静默保存
[2025-07-20 11:22:54] [INFO] 已加载 59 个账号
[2025-07-20 11:22:54] [INFO] 🔍 [调试] 当前平台: toutiao
[2025-07-20 11:22:54] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-07-20 11:22:54] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-07-20 11:22:54] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-20 11:22:54] [INFO] 🔍 [调试] 更新账号树 - 当前平台: toutiao
[2025-07-20 11:22:54] [INFO] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-20 11:22:54] [INFO] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-07-20 11:22:54] [INFO] 已加载账号数据: 79 条记录
[2025-07-20 11:22:54] [INFO] 🔍 [调试] 账号数据为空或未加载
[2025-07-20 11:22:54] [INFO] 已加载 59 个账号
[2025-07-20 11:22:54] [INFO] 🔍 [调试] 当前平台: toutiao
[2025-07-20 11:22:54] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-07-20 11:22:54] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-07-20 11:22:54] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-20 11:22:54] [INFO] 🔍 [调试] 更新账号树 - 当前平台: toutiao
[2025-07-20 11:22:54] [INFO] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-20 11:22:54] [INFO] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-07-20 11:22:54] [INFO] 已加载账号数据: 79 条记录
[2025-07-20 11:22:54] [INFO] 🔍 [调试] 账号数据为空或未加载
[2025-07-20 11:22:54] [INFO] 已加载 59 个账号
[2025-07-20 11:22:54] [INFO] 🔍 [调试] 当前平台: toutiao
[2025-07-20 11:22:54] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-07-20 11:22:54] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-07-20 11:22:54] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-20 11:22:54] [INFO] 🔍 [调试] 更新账号树 - 当前平台: toutiao
[2025-07-20 11:22:54] [INFO] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-20 11:22:54] [INFO] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-07-20 11:22:54] [INFO] 已加载账号数据: 79 条记录
[2025-07-20 11:22:54] [INFO] 🔍 [调试] 账号数据为空或未加载
[2025-07-20 11:22:55] [INFO] 已加载 59 个账号
[2025-07-20 11:22:55] [INFO] 🔍 [调试] 当前平台: toutiao
[2025-07-20 11:22:55] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-07-20 11:22:55] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-07-20 11:22:55] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-20 11:22:55] [INFO] 🔍 [调试] 更新账号树 - 当前平台: toutiao
[2025-07-20 11:22:55] [INFO] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-20 11:22:55] [INFO] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-07-20 11:22:55] [INFO] 已加载账号数据: 79 条记录
[2025-07-20 11:22:55] [INFO] 🔍 [调试] 账号数据为空或未加载
