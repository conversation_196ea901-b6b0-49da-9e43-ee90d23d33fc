#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
定时任务调度管理器UI - 负责定时任务相关的用户界面功能
遵循MECE原则：与定时任务管理相关的所有UI功能集中管理
"""

import tkinter as tk
from tkinter import ttk, messagebox
import datetime
from typing import Dict, Any, Optional, List

from 网易号存稿.common.logger import logger
try:
    from 网易号存稿.common.scheduler import AdvancedSchedulerManager, TaskType, TaskStatus, RepeatRule, RepeatType
    from 网易号存稿.common.scheduler.time_utils import TimeUtils
except ImportError as e:
    print(f"❌ 调度器模块导入失败: {e}")
    # 创建占位符类以避免运行时错误
    class TaskType:
        DATA_QUERY_ALL = "data_query_all"
        DATA_QUERY_SINGLE = "data_query_single"
        DRAFT_UPLOAD = "draft_upload"
        COMBINED = "combined"

    class TaskStatus:
        PENDING = "pending"
        RUNNING = "running"
        COMPLETED = "completed"
        FAILED = "failed"
        CANCELLED = "cancelled"
        DISABLED = "disabled"


class SchedulerManagerUI:
    """定时任务调度管理器UI - 负责所有定时任务管理相关的界面功能"""
    
    def __init__(self, parent_ui, config_manager):
        """
        初始化定时任务管理器UI
        
        Args:
            parent_ui: 主UI实例
            config_manager: 配置管理器
        """
        self.parent_ui = parent_ui
        self.config_manager = config_manager
        
        # 初始化调度管理器
        try:
            self.scheduler_manager = AdvancedSchedulerManager(logger=self.parent_ui.log)
            self.scheduler_available = True
        except Exception as e:
            self.parent_ui.log(f"❌ 调度管理器初始化失败: {e}")
            self.scheduler_manager = None
            self.scheduler_available = False
        
        # UI组件
        self.scheduler_tab = None
        self.task_tree = None
        self.status_frame = None
        self.control_frame = None
        
        # 刷新定时器
        self.refresh_timer = None
        
        logger.info("✅ 定时任务管理器UI初始化完成")
    
    def set_managers(self, data_query_manager=None, draft_task_manager=None):
        """设置外部管理器"""
        self.scheduler_manager.set_managers(data_query_manager, draft_task_manager)
    
    def create_scheduler_tab(self, parent_notebook):
        """
        创建定时任务管理标签页
        
        Args:
            parent_notebook: 父级Notebook组件
        """
        # 创建定时任务标签页
        self.scheduler_tab = ttk.Frame(parent_notebook)
        parent_notebook.add(self.scheduler_tab, text="⏰ 定时任务")
        
        # 创建主要布局
        self._create_main_layout()
        
        # 启动调度器
        self.scheduler_manager.start_scheduler()
        
        # 启动UI刷新定时器
        self._start_refresh_timer()
        
        logger.info("✅ 定时任务管理标签页创建完成")
    
    def _create_main_layout(self):
        """创建主要布局"""
        # 创建顶部控制面板
        self._create_control_panel()
        
        # 创建任务列表
        self._create_task_list()
        
        # 创建状态面板
        self._create_status_panel()
        
        # 初始加载任务列表
        self._refresh_task_list()
    
    def _create_control_panel(self):
        """创建控制面板"""
        self.control_frame = ttk.Frame(self.scheduler_tab)
        self.control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 标题
        title_label = ttk.Label(self.control_frame, text="定时任务管理", font=("微软雅黑", 12, "bold"))
        title_label.pack(side=tk.LEFT)
        
        # 按钮区域
        button_frame = ttk.Frame(self.control_frame)
        button_frame.pack(side=tk.RIGHT)
        
        # 新建任务按钮
        self.new_task_btn = ttk.Button(button_frame, text="➕ 新建任务", command=self._show_new_task_dialog)
        self.new_task_btn.pack(side=tk.LEFT, padx=2)
        
        # 编辑任务按钮
        self.edit_task_btn = ttk.Button(button_frame, text="✏️ 编辑任务", command=self._show_edit_task_dialog)
        self.edit_task_btn.pack(side=tk.LEFT, padx=2)
        
        # 删除任务按钮
        self.delete_task_btn = ttk.Button(button_frame, text="🗑️ 删除任务", command=self._delete_selected_task)
        self.delete_task_btn.pack(side=tk.LEFT, padx=2)
        
        # 立即执行按钮
        self.execute_now_btn = ttk.Button(button_frame, text="▶️ 立即执行", command=self._execute_task_now)
        self.execute_now_btn.pack(side=tk.LEFT, padx=2)
        
        # 刷新按钮
        self.refresh_btn = ttk.Button(button_frame, text="🔄 刷新", command=self._refresh_task_list)
        self.refresh_btn.pack(side=tk.LEFT, padx=2)
    
    def _create_task_list(self):
        """创建任务列表"""
        # 创建框架
        list_frame = ttk.LabelFrame(self.scheduler_tab, text="任务列表")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 创建Treeview
        columns = ("name", "type", "platform", "next_execution", "status", "last_execution")
        self.task_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=12)
        
        # 设置列标题
        self.task_tree.heading("name", text="任务名称")
        self.task_tree.heading("type", text="任务类型")
        self.task_tree.heading("platform", text="平台")
        self.task_tree.heading("next_execution", text="下次执行时间")
        self.task_tree.heading("status", text="状态")
        self.task_tree.heading("last_execution", text="上次执行时间")
        
        # 设置列宽
        self.task_tree.column("name", width=150)
        self.task_tree.column("type", width=100)
        self.task_tree.column("platform", width=80)
        self.task_tree.column("next_execution", width=150)
        self.task_tree.column("status", width=80)
        self.task_tree.column("last_execution", width=150)
        
        # 创建滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.task_tree.yview)
        self.task_tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.task_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定双击事件
        self.task_tree.bind("<Double-1>", lambda e: self._show_edit_task_dialog())
        
        # 绑定右键菜单
        self._create_context_menu()
    
    def _create_context_menu(self):
        """创建右键菜单"""
        self.context_menu = tk.Menu(self.task_tree, tearoff=0)

        # 添加菜单项（使用变量存储，便于动态更新）
        self.menu_items = {}
        self.menu_items['edit'] = self.context_menu.add_command(label="📝 编辑任务", command=self._show_edit_task_dialog)
        self.menu_items['execute'] = self.context_menu.add_command(label="▶️ 立即执行", command=self._execute_task_now)
        self.context_menu.add_separator()
        self.menu_items['enable'] = self.context_menu.add_command(label="✅ 启用任务", command=self._enable_selected_task)
        self.menu_items['disable'] = self.context_menu.add_command(label="⏸️ 禁用任务", command=self._disable_selected_task)
        self.context_menu.add_separator()
        self.menu_items['delete'] = self.context_menu.add_command(label="🗑️ 删除任务", command=self._delete_selected_task)

        # 绑定右键事件
        self.task_tree.bind("<Button-3>", self._show_context_menu)
    
    def _show_context_menu(self, event):
        """显示右键菜单"""
        try:
            # 选择点击的项目
            item = self.task_tree.identify_row(event.y)
            if not item:
                return

            # 选中该项目
            self.task_tree.selection_set(item)

            # 更新菜单状态
            self._update_context_menu_state()

            # 显示菜单
            self.context_menu.post(event.x_root, event.y_root)

        except Exception as e:
            self.parent_ui.log(f"❌ 显示右键菜单失败: {e}")
        finally:
            # 确保菜单在异常情况下也能正确释放
            try:
                self.context_menu.grab_release()
            except:
                pass

    def _update_context_menu_state(self):
        """更新右键菜单状态"""
        try:
            task_id = self._get_selected_task_id()
            if not task_id:
                # 如果没有选中任务，禁用所有菜单项
                self.context_menu.entryconfig("📝 编辑任务", state="disabled")
                self.context_menu.entryconfig("▶️ 立即执行", state="disabled")
                self.context_menu.entryconfig("✅ 启用任务", state="disabled")
                self.context_menu.entryconfig("⏸️ 禁用任务", state="disabled")
                self.context_menu.entryconfig("🗑️ 删除任务", state="disabled")
                return

            # 获取任务信息
            task = self.scheduler_manager.get_task(task_id)
            if not task:
                return

            # 启用基本菜单项
            self.context_menu.entryconfig("📝 编辑任务", state="normal")
            self.context_menu.entryconfig("▶️ 立即执行", state="normal")
            self.context_menu.entryconfig("🗑️ 删除任务", state="normal")

            # 根据任务状态更新启用/禁用菜单
            if task.enabled:
                self.context_menu.entryconfig("✅ 启用任务", state="disabled")
                self.context_menu.entryconfig("⏸️ 禁用任务", state="normal")
            else:
                self.context_menu.entryconfig("✅ 启用任务", state="normal")
                self.context_menu.entryconfig("⏸️ 禁用任务", state="disabled")

        except Exception as e:
            self.parent_ui.log(f"❌ 更新右键菜单状态失败: {e}")

    def _create_status_panel(self):
        """创建状态面板"""
        self.status_frame = ttk.LabelFrame(self.scheduler_tab, text="调度器状态")
        self.status_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 创建状态显示区域
        status_info_frame = ttk.Frame(self.status_frame)
        status_info_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 状态标签
        self.status_labels = {}
        
        # 第一行状态
        row1_frame = ttk.Frame(status_info_frame)
        row1_frame.pack(fill=tk.X, pady=2)
        
        self.status_labels['total_tasks'] = ttk.Label(row1_frame, text="总任务数: 0")
        self.status_labels['total_tasks'].pack(side=tk.LEFT, padx=10)
        
        self.status_labels['enabled_tasks'] = ttk.Label(row1_frame, text="启用任务: 0")
        self.status_labels['enabled_tasks'].pack(side=tk.LEFT, padx=10)
        
        self.status_labels['running_tasks'] = ttk.Label(row1_frame, text="运行中: 0")
        self.status_labels['running_tasks'].pack(side=tk.LEFT, padx=10)
        
        self.status_labels['pending_tasks'] = ttk.Label(row1_frame, text="等待执行: 0")
        self.status_labels['pending_tasks'].pack(side=tk.LEFT, padx=10)
        
        # 第二行状态
        row2_frame = ttk.Frame(status_info_frame)
        row2_frame.pack(fill=tk.X, pady=2)
        
        self.status_labels['total_executions'] = ttk.Label(row2_frame, text="总执行次数: 0")
        self.status_labels['total_executions'].pack(side=tk.LEFT, padx=10)
        
        self.status_labels['successful_executions'] = ttk.Label(row2_frame, text="成功: 0")
        self.status_labels['successful_executions'].pack(side=tk.LEFT, padx=10)
        
        self.status_labels['failed_executions'] = ttk.Label(row2_frame, text="失败: 0")
        self.status_labels['failed_executions'].pack(side=tk.LEFT, padx=10)
        
        self.status_labels['scheduler_status'] = ttk.Label(row2_frame, text="调度器: 运行中", foreground="green")
        self.status_labels['scheduler_status'].pack(side=tk.LEFT, padx=10)
    
    def _refresh_task_list(self):
        """刷新任务列表"""
        try:
            # 检查调度器是否可用
            if not self.scheduler_available or not self.scheduler_manager:
                self.parent_ui.log("⚠️ 调度器不可用，无法刷新任务列表")
                return

            # 清空现有项目
            for item in self.task_tree.get_children():
                self.task_tree.delete(item)

            # 获取所有任务
            tasks = self.scheduler_manager.get_all_tasks()

            # 添加任务到列表
            for task in tasks:
                try:
                    # 格式化任务类型
                    type_text = self._format_task_type(task.task_type)

                    # 格式化下次执行时间
                    if hasattr(task, 'next_execution') and task.next_execution:
                        try:
                            if hasattr(TimeUtils, 'format_datetime'):
                                next_exec_text = TimeUtils.format_datetime(task.next_execution, 'full')
                            else:
                                next_exec_text = str(task.next_execution)
                        except:
                            next_exec_text = str(task.next_execution)
                    else:
                        next_exec_text = "无"

                    # 格式化状态
                    status_text = self._format_task_status(task.status)

                    # 格式化上次执行时间
                    if hasattr(task, 'last_execution') and task.last_execution:
                        try:
                            if hasattr(TimeUtils, 'format_datetime'):
                                last_exec_text = TimeUtils.format_datetime(task.last_execution, 'full')
                            else:
                                last_exec_text = str(task.last_execution)
                        except:
                            last_exec_text = str(task.last_execution)
                    else:
                        last_exec_text = "从未执行"

                    # 格式化平台名称
                    platform_text = self._format_platform_name(task.platform)

                    # 插入到树形视图
                    item_id = self.task_tree.insert("", tk.END, values=(
                        task.name,
                        type_text,
                        platform_text,
                        next_exec_text,
                        status_text,
                        last_exec_text
                    ), tags=(f"task_id:{task.id}",))

                    # 额外的任务ID存储方式（备用）
                    try:
                        self.task_tree.set(item_id, "task_id", task.id)
                    except:
                        # 如果set方法失败，tags方式已经存储了
                        pass

                    # 根据状态设置颜色
                    if not getattr(task, 'enabled', True):
                        self.task_tree.item(item_id, tags=("disabled",))
                    elif hasattr(task, 'status'):
                        if str(task.status).lower() in ['running', 'TaskStatus.RUNNING']:
                            self.task_tree.item(item_id, tags=("running",))
                        elif str(task.status).lower() in ['failed', 'TaskStatus.FAILED']:
                            self.task_tree.item(item_id, tags=("failed",))

                except Exception as task_error:
                    self.parent_ui.log(f"❌ 处理任务时出错: {task_error}")
                    continue

            # 设置标签样式
            try:
                self.task_tree.tag_configure("disabled", foreground="gray")
                self.task_tree.tag_configure("running", foreground="blue")
                self.task_tree.tag_configure("failed", foreground="red")
            except:
                pass

            # 更新状态面板
            self._update_status_panel()

        except Exception as e:
            self.parent_ui.log(f"❌ 刷新任务列表失败: {e}")
            import traceback
            self.parent_ui.log(f"详细错误: {traceback.format_exc()}")
    
    def _format_task_type(self, task_type: TaskType) -> str:
        """格式化任务类型显示"""
        type_map = {
            TaskType.DATA_QUERY_ALL: "数据查询(全部)",
            TaskType.DATA_QUERY_SINGLE: "数据查询(单个)",
            TaskType.DRAFT_UPLOAD: "存稿任务",
            TaskType.COMBINED: "组合任务"
        }
        return type_map.get(task_type, str(task_type))

    def _format_platform_name(self, platform: str) -> str:
        """格式化平台名称显示"""
        platform_map = {
            "netease": "网易号",
            "toutiao": "头条号",
            "dayu": "大鱼号"
        }
        return platform_map.get(platform, platform)

    def _format_task_status(self, status) -> str:
        """格式化任务状态显示"""
        # 处理不同类型的状态值
        status_str = str(status).lower()

        # 移除可能的枚举前缀
        if 'taskstatus.' in status_str:
            status_str = status_str.split('.')[-1]

        status_map = {
            "pending": "等待执行",
            "running": "运行中",
            "completed": "已完成",
            "failed": "执行失败",
            "cancelled": "已取消",
            "disabled": "已禁用"
        }

        return status_map.get(status_str, str(status))

    def _update_status_panel(self):
        """更新状态面板"""
        try:
            stats = self.scheduler_manager.get_statistics()

            # 更新状态标签
            self.status_labels['total_tasks'].config(text=f"总任务数: {stats['total_tasks']}")
            self.status_labels['enabled_tasks'].config(text=f"启用任务: {stats['enabled_tasks']}")
            self.status_labels['running_tasks'].config(text=f"运行中: {stats['running_tasks']}")
            self.status_labels['pending_tasks'].config(text=f"等待执行: {stats['pending_tasks']}")
            self.status_labels['total_executions'].config(text=f"总执行次数: {stats['total_executions']}")
            self.status_labels['successful_executions'].config(text=f"成功: {stats['successful_executions']}")
            self.status_labels['failed_executions'].config(text=f"失败: {stats['failed_executions']}")

            # 更新调度器状态
            if stats['scheduler_running']:
                self.status_labels['scheduler_status'].config(text="调度器: 运行中", foreground="green")
            else:
                self.status_labels['scheduler_status'].config(text="调度器: 已停止", foreground="red")

        except Exception as e:
            self.parent_ui.log(f"❌ 更新状态面板失败: {e}")

    def _start_refresh_timer(self):
        """启动刷新定时器"""
        def refresh():
            if self.scheduler_tab and self.scheduler_tab.winfo_exists():
                self._refresh_task_list()
                # 每30秒刷新一次
                self.refresh_timer = self.parent_ui.root.after(30000, refresh)

        refresh()

    def _stop_refresh_timer(self):
        """停止刷新定时器"""
        if self.refresh_timer:
            self.parent_ui.root.after_cancel(self.refresh_timer)
            self.refresh_timer = None

    def _get_selected_task_id(self) -> Optional[str]:
        """获取选中的任务ID"""
        try:
            selection = self.task_tree.selection()
            if not selection:
                return None

            item = selection[0]

            # 首先尝试从item的tags中获取task_id
            try:
                tags = self.task_tree.item(item, "tags")
                for tag in tags:
                    if tag.startswith("task_id:"):
                        return tag.split(":", 1)[1]
            except:
                pass

            # 如果tags中没有，从任务列表中查找对应的任务ID
            values = self.task_tree.item(item, "values")
            if not values:
                return None

            # 通过任务名称和类型查找任务ID（更精确的匹配）
            task_name = values[0]
            task_type = values[1] if len(values) > 1 else ""

            for task in self.scheduler_manager.get_all_tasks():
                if task.name == task_name:
                    # 如果有类型信息，进行双重验证
                    if task_type:
                        expected_type = self._format_task_type(task.task_type)
                        if expected_type == task_type:
                            return task.id
                    else:
                        return task.id

            return None

        except Exception as e:
            self.parent_ui.log(f"❌ 获取选中任务ID失败: {e}")
            return None

    def _show_new_task_dialog(self):
        """显示新建任务对话框"""
        try:
            from 网易号存稿.ui.dialogs.task_dialog import TaskDialog

            dialog = TaskDialog(
                parent=self.scheduler_tab,
                scheduler_manager=self.scheduler_manager,
                callback=self._refresh_task_list
            )

        except Exception as e:
            self.parent_ui.log(f"❌ 显示新建任务对话框失败: {e}")
            messagebox.showerror("错误", f"无法打开新建任务对话框: {e}")

    def _show_edit_task_dialog(self):
        """显示编辑任务对话框"""
        try:
            task_id = self._get_selected_task_id()
            if not task_id:
                messagebox.showwarning("提示", "请先选择要编辑的任务")
                return

            task = self.scheduler_manager.get_task(task_id)
            if not task:
                messagebox.showerror("错误", "任务不存在或已被删除")
                self._refresh_task_list()  # 刷新列表
                return

            from 网易号存稿.ui.dialogs.task_dialog import TaskDialog

            dialog = TaskDialog(
                parent=self.scheduler_tab,
                scheduler_manager=self.scheduler_manager,
                task=task,
                callback=self._refresh_task_list
            )

        except ImportError as e:
            self.parent_ui.log(f"❌ 导入任务对话框失败: {e}")
            messagebox.showerror("错误", "任务对话框组件不可用")
        except Exception as e:
            self.parent_ui.log(f"❌ 显示编辑任务对话框失败: {e}")
            messagebox.showerror("错误", f"无法打开编辑任务对话框: {e}")

    def _delete_selected_task(self):
        """删除选中的任务"""
        task_id = self._get_selected_task_id()
        if not task_id:
            messagebox.showwarning("提示", "请先选择要删除的任务")
            return

        task = self.scheduler_manager.get_task(task_id)
        if not task:
            messagebox.showerror("错误", "任务不存在")
            return

        # 确认删除
        result = messagebox.askyesno(
            "确认删除",
            f"确定要删除任务 '{task.name}' 吗？\n\n此操作不可撤销。"
        )

        if result:
            try:
                success = self.scheduler_manager.delete_task(task_id)
                if success:
                    self.parent_ui.log(f"✅ 删除任务成功: {task.name}")
                    self._refresh_task_list()
                else:
                    messagebox.showerror("错误", "删除任务失败")
            except Exception as e:
                self.parent_ui.log(f"❌ 删除任务失败: {e}")
                messagebox.showerror("错误", f"删除任务失败: {e}")

    def _execute_task_now(self):
        """立即执行选中的任务"""
        try:
            task_id = self._get_selected_task_id()
            if not task_id:
                messagebox.showwarning("提示", "请先选择要执行的任务")
                return

            task = self.scheduler_manager.get_task(task_id)
            if not task:
                messagebox.showerror("错误", "任务不存在或已被删除")
                self._refresh_task_list()  # 刷新列表
                return

            # 确认执行
            result = messagebox.askyesno(
                "确认执行",
                f"确定要立即执行任务 '{task.name}' 吗？\n\n"
                f"任务类型: {self._format_task_type(task.task_type)}\n"
                f"目标平台: {self._format_platform_name(task.platform)}"
            )

            if not result:
                return

            success = self.scheduler_manager.execute_task_now(task_id)
            if success:
                self.parent_ui.log(f"✅ 任务已提交立即执行: {task.name}")
                messagebox.showinfo("执行成功", f"任务 '{task.name}' 已提交执行\n\n请查看日志了解执行进度")
                self._refresh_task_list()
            else:
                messagebox.showerror("执行失败", "任务提交失败，请检查任务配置")

        except Exception as e:
            self.parent_ui.log(f"❌ 立即执行任务失败: {e}")
            messagebox.showerror("错误", f"立即执行任务失败: {e}")

    def _enable_selected_task(self):
        """启用选中的任务"""
        try:
            task_id = self._get_selected_task_id()
            if not task_id:
                messagebox.showwarning("提示", "请先选择要启用的任务")
                return

            task = self.scheduler_manager.get_task(task_id)
            if not task:
                messagebox.showerror("错误", "任务不存在或已被删除")
                self._refresh_task_list()
                return

            if task.enabled:
                messagebox.showinfo("提示", f"任务 '{task.name}' 已经是启用状态")
                return

            success = self.scheduler_manager.enable_task(task_id)
            if success:
                self.parent_ui.log(f"✅ 启用任务成功: {task.name}")
                messagebox.showinfo("成功", f"任务 '{task.name}' 已启用")
                self._refresh_task_list()
            else:
                messagebox.showerror("失败", "启用任务失败，请检查任务配置")

        except Exception as e:
            self.parent_ui.log(f"❌ 启用任务失败: {e}")
            messagebox.showerror("错误", f"启用任务失败: {e}")

    def _disable_selected_task(self):
        """禁用选中的任务"""
        try:
            task_id = self._get_selected_task_id()
            if not task_id:
                messagebox.showwarning("提示", "请先选择要禁用的任务")
                return

            task = self.scheduler_manager.get_task(task_id)
            if not task:
                messagebox.showerror("错误", "任务不存在或已被删除")
                self._refresh_task_list()
                return

            if not task.enabled:
                messagebox.showinfo("提示", f"任务 '{task.name}' 已经是禁用状态")
                return

            success = self.scheduler_manager.disable_task(task_id)
            if success:
                self.parent_ui.log(f"✅ 禁用任务成功: {task.name}")
                messagebox.showinfo("成功", f"任务 '{task.name}' 已禁用")
                self._refresh_task_list()
            else:
                messagebox.showerror("失败", "禁用任务失败")

        except Exception as e:
            self.parent_ui.log(f"❌ 禁用任务失败: {e}")
            messagebox.showerror("错误", f"禁用任务失败: {e}")

    def cleanup(self):
        """清理资源"""
        try:
            # 停止刷新定时器
            self._stop_refresh_timer()

            # 停止调度器
            if self.scheduler_manager:
                self.scheduler_manager.stop_scheduler()

            logger.info("✅ 定时任务管理器UI清理完成")
        except Exception as e:
            logger.error(f"❌ 定时任务管理器UI清理失败: {e}")
