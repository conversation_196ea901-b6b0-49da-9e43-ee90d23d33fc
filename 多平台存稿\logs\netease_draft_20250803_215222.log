[2025-08-03 21:52:27] [INFO] 🚀 启动多平台存稿工具...
[2025-08-03 21:52:27] [INFO] ✅ 定时任务管理器UI初始化完成
[2025-08-03 21:52:28] [INFO] ✅ 定时任务管理标签页创建完成
[2025-08-03 21:58:07] [INFO] ✅ 定时任务管理器UI清理完成
25-08-03 21:52:27] [INFO] 已从toutiao平台加载存稿详情数据，共 83 个账号
[2025-08-03 21:52:28] [INFO] 🚀 定时任务调度器已启动
[2025-08-03 21:52:28] [INFO] 已确保所有必要目录存在
[2025-08-03 21:52:28] [DEBUG] 🔍 [调试] 当前平台: toutiao
[2025-08-03 21:52:28] [DEBUG] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-08-03 21:52:28] [DEBUG] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-08-03 21:52:28] [SUCCESS] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-08-03 21:52:28] [DEBUG] 🔍 [调试] 更新账号树 - 当前平台: toutiao
[2025-08-03 21:52:28] [DEBUG] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-08-03 21:52:28] [DEBUG] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-08-03 21:52:28] [INFO] 已加载账号数据: 85 条记录
[2025-08-03 21:52:28] [DEBUG] 🔍 [调试] 账号数据为空或未加载
[2025-08-03 21:52:28] [SUCCESS] ✅ 初始UI更新完成
[2025-08-03 21:53:34] [DEBUG] 正在登录头条账号: ***********
[2025-08-03 21:53:34] [SUCCESS] 找到头条账号 *********** 的Cookie文件: D:/网易号全自动/头条号账号\***********.txt
[2025-08-03 21:53:34] [SUCCESS] 正在使用Cookie登录头条账号: ***********
[2025-08-03 21:53:34] [SUCCESS] Cookie文件路径: D:/网易号全自动/头条号账号\***********.txt
[2025-08-03 21:53:34] [SUCCESS] ✅ Cookie预检测通过，启动浏览器验证...
[2025-08-03 21:53:39] [SUCCESS] [toutiao] 浏览器驱动创建成功，使用端口: 9515
[2025-08-03 21:53:43] [SUCCESS] 正在加载Cookie文件: D:/网易号全自动/头条号账号\***********.txt
[2025-08-03 21:53:46] [SUCCESS] ✅ Cookie添加完成
[2025-08-03 21:54:07] [ERROR] ❌ 头条号登录失败：停留在登录页面
[2025-08-03 21:54:10] [ERROR] ❌ 头条账号 *********** 登录失败
[2025-08-03 21:55:12] [DEBUG] 正在登录头条账号: 是微风吖
[2025-08-03 21:55:12] [SUCCESS] 找到头条账号 是微风吖 的Cookie文件: D:/网易号全自动/头条号账号\是微风吖.txt
[2025-08-03 21:55:12] [SUCCESS] 正在使用Cookie登录头条账号: 是微风吖
[2025-08-03 21:55:12] [SUCCESS] Cookie文件路径: D:/网易号全自动/头条号账号\是微风吖.txt
[2025-08-03 21:55:12] [SUCCESS] ✅ Cookie预检测通过，启动浏览器验证...
[2025-08-03 21:55:18] [SUCCESS] [toutiao] 浏览器驱动创建成功，使用端口: 9516
[2025-08-03 21:55:21] [SUCCESS] 正在加载Cookie文件: D:/网易号全自动/头条号账号\是微风吖.txt
[2025-08-03 21:55:37] [SUCCESS] ✅ Cookie添加完成
[2025-08-03 21:55:38] [SUCCESS] ✅ 头条号快速登录成功 (等待0.5秒)
[2025-08-03 21:55:38] [SUCCESS] ✅ 头条账号 是微风吖 登录成功
[2025-08-03 21:55:43] [INFO] 跳过数据获取，浏览器将保持打开状态
[2025-08-03 21:55:45] [INFO] 已选择账号: 15259538073
[2025-08-03 21:55:47] [DEBUG] 正在登录头条账号: 15259538073
[2025-08-03 21:55:47] [SUCCESS] 找到头条账号 15259538073 的Cookie文件: D:/网易号全自动/头条号账号\15259538073.txt
[2025-08-03 21:55:47] [SUCCESS] 正在使用Cookie登录头条账号: 15259538073
[2025-08-03 21:55:47] [SUCCESS] Cookie文件路径: D:/网易号全自动/头条号账号\15259538073.txt
[2025-08-03 21:55:47] [SUCCESS] ✅ Cookie预检测通过，启动浏览器验证...
[2025-08-03 21:55:54] [SUCCESS] [toutiao] 浏览器驱动创建成功，使用端口: 9517
[2025-08-03 21:55:58] [SUCCESS] 正在加载Cookie文件: D:/网易号全自动/头条号账号\15259538073.txt
[2025-08-03 21:56:17] [SUCCESS] ✅ Cookie添加完成
[2025-08-03 21:56:19] [SUCCESS] ✅ 头条号快速登录成功 (等待0.5秒)
[2025-08-03 21:56:19] [SUCCESS] ✅ 头条账号 15259538073 登录成功
[2025-08-03 21:56:28] [INFO] 跳过数据获取，浏览器将保持打开状态
[2025-08-03 21:56:51] [INFO] 已选择账号: 19063973323
[2025-08-03 21:56:51] [INFO] 已选择账号: 19063973323
[2025-08-03 21:56:53] [DEBUG] 🚀 开始为账号 19063973323 存稿
[2025-08-03 21:56:53] [INFO] 已确保所有必要目录存在
[2025-08-03 21:56:53] [INFO] 使用单线程模式处理账号
[2025-08-03 21:56:53] [DEBUG] 🔍 [DEBUG] 准备启动单线程任务，参数: 19063973323
[2025-08-03 21:56:53] [DEBUG] 🔍 [DEBUG] 单线程任务已启动
[2025-08-03 21:56:53] [DEBUG] 🔍 [DEBUG] _single_task_thread 开始执行，参数: 19063973323
[2025-08-03 21:56:53] [DEBUG] 📋 开始处理单个账号: 19063973323
[2025-08-03 21:56:53] [INFO] 视频分配方式: 随机分配
[2025-08-03 21:56:53] [DEBUG] 开始处理账号: 19063973323
[2025-08-03 21:56:53] [SUCCESS] 正在使用Cookie登录账号: 19063973323
[2025-08-03 21:56:53] [SUCCESS] ✅ Cookie预检测通过，启动浏览器验证...
[2025-08-03 21:56:58] [SUCCESS] 开始查询账号数据: 19063973323 [toutiao] 浏览器驱动创建成功，使用端口: 9518
[2025-08-03 21:57:02] [SUCCESS] 正在加载Cookie文件: D:/网易号全自动/头条号账号\19063973323.txt
[2025-08-03 21:57:06] [SUCCESS] ✅ Cookie添加完成
[2025-08-03 21:57:08] [SUCCESS] ✅ 头条号快速登录成功 (等待0.5秒)
[2025-08-03 21:57:08] [SUCCESS] 登录成功，开始处理视频: 19063973323
[2025-08-03 21:57:08] [INFO] 找到 3 个视频文件
[2025-08-03 21:57:08] [INFO] 已随机打乱 3 个视频文件的顺序
[2025-08-03 21:57:08] [DEBUG] 开始处理视频: 19岁少年错付千元轻生，司机否认：他要套现！死无对证引哗然！.mp4
[2025-08-03 21:57:08] [DEBUG] 🎬 开始头条号视频存稿流程
[2025-08-03 21:57:08] [DEBUG] 🔍 正在定位视频菜单...
[2025-08-03 21:57:09] [SUCCESS] ✅ 已点击视频菜单
[2025-08-03 21:57:12] [INFO] 🌐 当前不在上传页面，导航到视频上传页面...
[2025-08-03 21:57:16] [SUCCESS] ✅ 已导航到视频上传页面
[2025-08-03 21:57:16] [INFO] 📁 准备上传视频: 19岁少年错付千元轻生，司机否认：他要套现！死无对证引哗然！.mp4
[2025-08-03 21:57:21] [DEBUG] 🔍 找到上传按钮，准备点击...
[2025-08-03 21:57:21] [SUCCESS] ✅ 已点击上传视频按钮
[2025-08-03 21:57:23] [SUCCESS] ✅ 视频文件已选择并开始上传
[2025-08-03 21:57:23] [DEBUG] ⏳ 开始监控视频上传状态...
[2025-08-03 21:57:23] [INFO] 📊 当前上传状态: 上传中… 0%
[2025-08-03 21:57:23] [DEBUG] ⏳ 视频正在上传中...
[2025-08-03 21:57:28] [INFO] 📊 当前上传状态: 上传中… 97.83%
[2025-08-03 21:57:28] [DEBUG] ⏳ 视频正在上传中...
[2025-08-03 21:57:33] [SUCCESS] 📊 当前上传状态: 上传成功
[2025-08-03 21:57:33] [SUCCESS] ✅ 视频上传成功！
[2025-08-03 21:57:33] [DEBUG] 📷 开始上传视频封面...
[2025-08-03 21:57:33] [SUCCESS] ✅ 已点击上传封面按钮
[2025-08-03 21:57:36] [DEBUG] 🔍 正在定位本地上传选项...
[2025-08-03 21:57:51] [ERROR] ❌ 封面上传过程中元素定位超时
[2025-08-03 21:57:51] [WARNING] ⚠️ 上传封面失败，继续存稿流程
[2025-08-03 21:57:51] [DEBUG] 💾 开始保存草稿...
[2025-08-03 21:57:52] [SUCCESS] ✅ 已点击存草稿按钮
[2025-08-03 21:57:55] [WARNING] ✅ 草稿保存成功！检测到'全部保存成功'提示
[2025-08-03 21:57:55] [SUCCESS] ✅ 头条号视频存稿成功！
[2025-08-03 21:57:55] [SUCCESS] ✅ 视频存稿成功: 19岁少年错付千元轻生，司机否认：他要套现！死无对证引哗然！.mp4
[2025-08-03 21:57:55] [DEBUG] 开始处理视频: 2025上海大师赛半决赛，赵心童3-2再胜威尔逊，结局万万没.mp4
[2025-08-03 21:57:55] [DEBUG] 🎬 开始头条号视频存稿流程
[2025-08-03 21:57:55] [DEBUG] 🔍 正在定位视频菜单...
[2025-08-03 21:57:55] [SUCCESS] ✅ 已点击视频菜单
[2025-08-03 21:57:58] [INFO] 🌐 当前不在上传页面，导航到视频上传页面...
[2025-08-03 21:58:02] [SUCCESS] ✅ 已导航到视频上传页面
[2025-08-03 21:58:02] [INFO] 📁 准备上传视频: 2025上海大师赛半决赛，赵心童3-2再胜威尔逊，结局万万没.mp4
[2025-08-03 21:58:02] [DEBUG] 🔍 找到上传按钮，准备点击...
[2025-08-03 21:58:02] [SUCCESS] ✅ 已点击上传视频按钮
[2025-08-03 21:58:04] [SUCCESS] ✅ 视频文件已选择并开始上传
[2025-08-03 21:58:04] [DEBUG] ⏳ 开始监控视频上传状态...
[2025-08-03 21:58:04] [INFO] 📊 当前上传状态: 上传中… 0%
[2025-08-03 21:58:04] [DEBUG] ⏳ 视频正在上传中...
[2025-08-03 21:58:06] [DEBUG] 正在关闭程序，清理后台资源...
[2025-08-03 21:58:07] [DEBUG] 🔄 调度器主循环结束
[2025-08-03 21:58:07] [DEBUG] 正在关闭任务执行器...
[2025-08-03 21:58:07] [INFO] 任务执行器已关闭
[2025-08-03 21:58:07] [INFO] ⏹️ 定时任务调度器已停止
[2025-08-03 21:58:07] [SUCCESS] ✅ 定时任务管理器UI清理完成
[2025-08-03 21:58:07] [SUCCESS] ✅ 调度器管理器清理完成
[2025-08-03 21:58:07] [DEBUG] 🧹 开始清理所有资源...
[2025-08-03 21:58:07] [DEBUG] ⏹️ 正在停止存稿任务...
[2025-08-03 21:58:07] [INFO] ⏹️ 存稿任务已停止
[2025-08-03 21:58:07] [DEBUG] 正在停止并发管理器...
[2025-08-03 21:58:07] [SUCCESS] ✅ 运行中的任务已停止
[2025-08-03 21:58:07] [SUCCESS] ✅ 定时器线程已清理（升级版系统自动管理）
[2025-08-03 21:58:07] [SUCCESS] ✅ Chrome进程强制清理完成
[2025-08-03 21:58:07] [SUCCESS] ✅ 浏览器进程已清理
[2025-08-03 21:58:08] [SUCCESS] ✅ 日志处理线程已清理
[2025-08-03 21:58:08] [SUCCESS] ✅ 存稿处理器已清理
[2025-08-03 21:58:08] [SUCCESS] ✅ 线程池已清理
[2025-08-03 21:58:08] [DEBUG] 正在停止串行刷新工作线程...
[2025-08-03 21:58:11] [WARNING] ⚠️ 仍有 3 个线程未结束
[2025-08-03 21:58:11] [SUCCESS] ✅ 临时文件已清理
[2025-08-03 21:58:11] [SUCCESS] ✅ 资源清理完成
[2025-08-03 21:58:11] [SUCCESS] ✅ 设置已静默保存
[2025-08-03 21:58:11] [SUCCESS] 程序清理完成，正在退出...
