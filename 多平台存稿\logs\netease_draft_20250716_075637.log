[2025-07-16 07:56:39] [INFO] 🚀 启动多平台存稿工具...
[2025-07-16 07:56:39] [INFO] 🔍 [调试] 当前平台: toutiao
[2025-07-16 07:56:39] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-07-16 07:56:39] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-07-16 07:56:39] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-16 07:56:39] [INFO] 🔄 发现旧数据文件: C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\account_data.json
[2025-07-16 07:56:39] [INFO] ℹ️ 新数据文件已存在且更新，跳过迁移: C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\account_data.json
[2025-07-16 07:56:39] [INFO] 已从toutiao平台加载存稿详情数据，共 27 个账号
[2025-07-16 07:56:40] [INFO] 已确保所有必要目录存在
[2025-07-16 07:56:41] [INFO] 🔍 [调试] 当前平台: toutiao
[2025-07-16 07:56:41] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-07-16 07:56:41] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-07-16 07:56:41] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-16 07:56:41] [INFO] 🔍 [调试] 更新账号树 - 当前平台: toutiao
[2025-07-16 07:56:41] [INFO] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-16 07:56:41] [INFO] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-07-16 07:56:41] [INFO] 已加载账号数据: 86 条记录
[2025-07-16 07:56:41] [INFO] 🔍 [调试] 账号数据为空或未加载
[2025-07-16 07:56:41] [INFO] ✅ 初始UI更新完成
[2025-07-16 07:56:47] [INFO] 开始查询头条号账号: ***********
[2025-07-16 07:56:47] [INFO] 🔍 已启动账号 *********** 的数据查询
[2025-07-16 07:56:47] [INFO] 🔍 开始查询头条号账号: ***********
[2025-07-16 07:56:47] [INFO] [头条查询管理器] 🚀 开始并发查询 1 个账号，最大并发数: 1
[2025-07-16 07:56:47] [INFO] ✅ Cookie预检测通过，启动浏览器验证...
[2025-07-16 07:56:50] [INFO] 开始查询账号数据: *********** [toutiao] 浏览器驱动创建成功，使用端口: 9515
[2025-07-16 07:56:53] [INFO] 正在加载Cookie文件: D:/网易号全自动/头条号账号\***********.txt
[2025-07-16 07:56:55] [INFO] 平台切换前保存 toutiao 的设置和存稿详情数据
[2025-07-16 07:56:55] [INFO] ✅ 设置已静默保存
[2025-07-16 07:56:55] [INFO] 已确保所有必要目录存在
[2025-07-16 07:56:55] [INFO] 正在更新账号管理器，当前平台: netease
[2025-07-16 07:56:55] [INFO] 账号目录: D:/网易号全自动/网易号账号
[2025-07-16 07:56:55] [INFO] 已加载 19 个netease平台账号
[2025-07-16 07:56:55] [INFO] 🔍 [调试] 当前平台: netease
[2025-07-16 07:56:55] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/网易号账号
[2025-07-16 07:56:55] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\网易号账号
[2025-07-16 07:56:55] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\网易号账号\netease_account_data.json
[2025-07-16 07:56:55] [INFO] 已加载账号数据: 24 条记录
[2025-07-16 07:56:55] [INFO] 账号数据文件路径: D:\网易号全自动\网易号账号\netease_account_data.json
[2025-07-16 07:56:55] [INFO] 已更新数据查询管理器的AccountData引用
[2025-07-16 07:56:55] [INFO] 已更新Excel导出管理器的AccountData引用
[2025-07-16 07:56:55] [INFO] 已从netease平台加载存稿详情数据，共 24 个账号
[2025-07-16 07:56:55] [INFO] [netease] ✅ 视频分配跟踪器初始化成功
[2025-07-16 07:56:55] [INFO] 已切换到平台: 网易号平台
[2025-07-16 07:56:55] [INFO] [netease] ✅ 视频分配跟踪器初始化成功
[2025-07-16 07:56:55] [INFO] 🔍 [调试] 当前平台: netease
[2025-07-16 07:56:55] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/网易号账号
[2025-07-16 07:56:55] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\网易号账号
[2025-07-16 07:56:55] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\网易号账号\netease_account_data.json
[2025-07-16 07:56:55] [INFO] 🔍 [调试] 更新账号树 - 当前平台: netease
[2025-07-16 07:56:55] [INFO] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\网易号账号\netease_account_data.json
[2025-07-16 07:56:55] [INFO] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-07-16 07:56:55] [INFO] 已加载账号数据: 24 条记录
[2025-07-16 07:56:55] [INFO] 🔍 [调试] 账号数据为空或未加载
[2025-07-16 07:56:55] [INFO] ✅ 设置已静默保存
[2025-07-16 07:57:01] [INFO] ✅ Cookie添加完成
[2025-07-16 07:57:02] [INFO] ✅ 头条号快速登录成功 (等待0.5秒)
[2025-07-16 07:57:02] [INFO] 正在获取首页数据...
[2025-07-16 07:57:03] [INFO] 已选择账号: *********** 网易
[2025-07-16 07:57:03] [INFO] 已选择账号: *********** 网易
[2025-07-16 07:57:03] [INFO] 已选择账号: *********** 网易
[2025-07-16 07:57:03] [INFO] 正在等待首页加载...
[2025-07-16 07:57:06] [INFO] 🚀 开始为账号 *********** 网易 存稿
[2025-07-16 07:57:06] [INFO] 已确保所有必要目录存在
[2025-07-16 07:57:06] [INFO] 使用单线程模式处理账号
[2025-07-16 07:57:06] [INFO] 🔍 [DEBUG] 准备启动单线程任务，参数: *********** 网易
[2025-07-16 07:57:06] [INFO] 🔍 [DEBUG] 单线程任务已启动
[2025-07-16 07:57:06] [INFO] 🔍 [DEBUG] _single_task_thread 开始执行，参数: *********** 网易
[2025-07-16 07:57:06] [INFO] 📋 开始处理单个账号: *********** 网易
[2025-07-16 07:57:06] [INFO] 视频分配方式: 随机分配
[2025-07-16 07:57:06] [INFO] 开始存稿任务，设置运行状态为True
[2025-07-16 07:57:06] [INFO] 开始处理账号: *********** 网易
[2025-07-16 07:57:06] [INFO] ✅ Cookie预检测通过，启动浏览器验证...
[2025-07-16 07:57:08] [INFO] 首页主要内容已加载
[2025-07-16 07:57:08] [INFO] 用户名: 宇凡影视
[2025-07-16 07:57:08] [INFO] 累计收益: 217.41元 -> 217.41
[2025-07-16 07:57:08] [INFO] 开始查询账号数据: *********** 网易 [netease] 浏览器驱动创建成功，使用端口: 9516
[2025-07-16 07:57:08] [INFO] 🔐 *********** 网易 正在登录
[2025-07-16 07:57:13] [INFO] ✅ Cookie添加完成: 成功 8/8 个
[2025-07-16 07:57:13] [INFO] Cookie已添加，开始快速验证...
[2025-07-16 07:57:15] [INFO] ✅ *********** 网易 快速登录成功 (等待0.5秒)
[2025-07-16 07:57:15] [INFO] ✅ 账号 *********** 网易 登录成功
[2025-07-16 07:57:15] [INFO] 开始存稿任务
[2025-07-16 07:57:15] [INFO] 正在打开网易视频上传页面
[2025-07-16 07:57:15] [INFO] ⚙️ 任务配置: 2个视频/3次循环
[2025-07-16 07:57:15] [INFO] 为账号 *********** 网易 分配了 435 个可用视频
[2025-07-16 07:57:15] [INFO] 📁 文件准备完成: 435个视频已随机排序
[2025-07-16 07:57:15] [INFO] 🔄 开始第1/3轮处理
[2025-07-16 07:57:15] [INFO] 为账号 *********** 网易 分配了 435 个可用视频
[2025-07-16 07:57:15] [INFO] 📁 循环1: 435个视频已随机排序
[2025-07-16 07:57:15] [INFO] 🖼️ 封面准备完成: 大满贯结束，王励勤重新调整男单阵容，4 个人恐失去“信任”.jpg
[2025-07-16 07:57:15] [INFO] 开始处理视频: 大满贯结束，王励勤重新调整男单阵容，4 个人恐失去“信任”.mp4
[2025-07-16 07:57:15] [INFO] 视频完整路径: D:/头条全自动/视频搬运/已处理视频\大满贯结束，王励勤重新调整男单阵容，4 个人恐失去“信任”.mp4
[2025-07-16 07:57:15] [INFO] 等待上传视频
[2025-07-16 07:57:15] [INFO] 正在打开网易视频上传页面
[2025-07-16 07:57:15] [INFO] 准备上传视频: D:\头条全自动\视频搬运\已处理视频\大满贯结束，王励勤重新调整男单阵容，4 个人恐失去“信任”.mp4
[2025-07-16 07:57:16] [INFO] 昨日收益:  -> 0.0
[2025-07-16 07:57:16] [INFO] 总播放: 133.9万 -> 1339000.0
[2025-07-16 07:57:17] [INFO] 找到文件输入元素
[2025-07-16 07:57:17] [INFO] 格式化后的文件路径: D:/头条全自动/视频搬运/已处理视频/大满贯结束，王励勤重新调整男单阵容，4 个人恐失去“信任”.mp4
[2025-07-16 07:57:17] [INFO] ✅ 已选择视频文件: 大满贯结束，王励勤重新调整男单阵容，4 个人恐失去“信任”.mp4
[2025-07-16 07:57:17] [INFO] ⏳ 等待视频上传完成...
[2025-07-16 07:57:17] [INFO] 🔍 开始改进的视频上传完成检测，超时时间: 180秒
[2025-07-16 07:57:17] [INFO] 🔍 检测配置: 每3秒检测一次，最多检测60次
[2025-07-16 07:57:17] [INFO] 🔍 第1次检测 (3秒)
[2025-07-16 07:57:17] [INFO] 🔍 文字检测开始: 查找路径 //*[@id="root"]/div/div[2]/div/div[2]/div/div/div[1]/div/div[2]/div[1]/div[2]/div[2]/div
[2025-07-16 07:57:18] [INFO] 🔍 目标文字: '推荐封面'
[2025-07-16 07:57:18] [INFO] 🔍 文字检测: 找到 1 个匹配元素
[2025-07-16 07:57:18] [INFO] 🔍 文字检测: 元素 1 - 标签: div, 可见: True
[2025-07-16 07:57:18] [INFO] 🔍 文字检测: 元素 1 - 实际文字: '视频正在解析，可点击上传图片进行本地/手机上传封面图'
[2025-07-16 07:57:18] [INFO] 🔍 文字检测: 元素文字不匹配目标文字
[2025-07-16 07:57:18] [INFO] 🔍 实际文字: '视频正在解析，可点击上传图片进行本地/手机上传封面图' | 目标文字: '推荐封面'
[2025-07-16 07:57:18] [INFO] 🔍 文字检测: 所有元素检查完毕，未找到匹配的目标文字
[2025-07-16 07:57:18] [INFO] 🔍 备用文字检测开始: 使用多种方式检测'推荐封面'文字
[2025-07-16 07:57:18] [INFO] 🔍 备用文字检测方法1: 检查页面源码 (长度: 21280)
[2025-07-16 07:57:18] [INFO] 🔍 备用文字检测方法1: 页面源码中未发现'推荐封面'文字
[2025-07-16 07:57:18] [INFO] 🔍 备用文字检测方法2: 查找包含'推荐封面'的元素
[2025-07-16 07:57:18] [INFO] 🔍 备用文字检测方法2: 找到 0 个包含'推荐封面'的元素
[2025-07-16 07:57:18] [INFO] 🔍 备用文字检测方法2: 未找到包含'推荐封面'的元素
[2025-07-16 07:57:18] [INFO] 🔍 备用文字检测方法3: 使用宽泛XPath查找'推荐封面'
[2025-07-16 07:57:18] [INFO] 🔍 备用文字检测方法3: XPath模式 1 找到 0 个元素
[2025-07-16 07:57:18] [INFO] 🔍 备用文字检测方法3: XPath模式 2 找到 0 个元素
[2025-07-16 07:57:18] [INFO] 🔍 备用文字检测方法3: XPath模式 3 找到 0 个元素
[2025-07-16 07:57:18] [INFO] 🔍 备用文字检测方法3: XPath模式 4 找到 0 个元素
[2025-07-16 07:57:18] [INFO] 🔍 备用文字检测方法3: XPath模式 5 找到 0 个元素
[2025-07-16 07:57:18] [INFO] 🔍 备用文字检测方法3: XPath模式 6 找到 0 个元素
[2025-07-16 07:57:18] [INFO] 🔍 备用文字检测: 所有方法都未检测到'推荐封面'文字
[2025-07-16 07:57:18] [INFO] ⏳ 第1次检测未发现上传完成，等待3秒后继续...
[2025-07-16 07:57:21] [INFO] 🔍 第2次检测 (6秒)
[2025-07-16 07:57:21] [INFO] 🔍 文字检测开始: 查找路径 //*[@id="root"]/div/div[2]/div/div[2]/div/div/div[1]/div/div[2]/div[1]/div[2]/div[2]/div
[2025-07-16 07:57:21] [INFO] 🔍 目标文字: '推荐封面'
[2025-07-16 07:57:21] [INFO] 🔍 文字检测: 找到 1 个匹配元素
[2025-07-16 07:57:21] [INFO] 🔍 文字检测: 元素 1 - 标签: div, 可见: True
[2025-07-16 07:57:21] [INFO] 🔍 文字检测: 元素 1 - 实际文字: '视频正在解析，可点击上传图片进行本地/手机上传封面图'
[2025-07-16 07:57:21] [INFO] 🔍 文字检测: 元素文字不匹配目标文字
[2025-07-16 07:57:21] [INFO] 🔍 实际文字: '视频正在解析，可点击上传图片进行本地/手机上传封面图' | 目标文字: '推荐封面'
[2025-07-16 07:57:21] [INFO] 🔍 文字检测: 所有元素检查完毕，未找到匹配的目标文字
[2025-07-16 07:57:21] [INFO] 🔍 备用文字检测开始: 使用多种方式检测'推荐封面'文字
[2025-07-16 07:57:21] [INFO] 🔍 备用文字检测方法1: 检查页面源码 (长度: 23798)
[2025-07-16 07:57:21] [INFO] 🔍 备用文字检测方法1: 页面源码中未发现'推荐封面'文字
[2025-07-16 07:57:21] [INFO] 🔍 备用文字检测方法2: 查找包含'推荐封面'的元素
[2025-07-16 07:57:21] [INFO] 🔍 备用文字检测方法2: 找到 0 个包含'推荐封面'的元素
[2025-07-16 07:57:21] [INFO] 🔍 备用文字检测方法2: 未找到包含'推荐封面'的元素
[2025-07-16 07:57:21] [INFO] 🔍 备用文字检测方法3: 使用宽泛XPath查找'推荐封面'
[2025-07-16 07:57:21] [INFO] 🔍 备用文字检测方法3: XPath模式 1 找到 0 个元素
[2025-07-16 07:57:21] [INFO] 🔍 备用文字检测方法3: XPath模式 2 找到 0 个元素
[2025-07-16 07:57:21] [INFO] 🔍 备用文字检测方法3: XPath模式 3 找到 0 个元素
[2025-07-16 07:57:21] [INFO] 🔍 备用文字检测方法3: XPath模式 4 找到 0 个元素
[2025-07-16 07:57:21] [INFO] 🔍 备用文字检测方法3: XPath模式 5 找到 0 个元素
[2025-07-16 07:57:21] [INFO] 🔍 备用文字检测方法3: XPath模式 6 找到 0 个元素
[2025-07-16 07:57:21] [INFO] 🔍 备用文字检测: 所有方法都未检测到'推荐封面'文字
[2025-07-16 07:57:21] [INFO] ⏳ 第2次检测未发现上传完成，等待3秒后继续...
[2025-07-16 07:57:24] [INFO] 🔍 第3次检测 (9秒)
[2025-07-16 07:57:24] [INFO] 🔍 文字检测开始: 查找路径 //*[@id="root"]/div/div[2]/div/div[2]/div/div/div[1]/div/div[2]/div[1]/div[2]/div[2]/div
[2025-07-16 07:57:24] [INFO] 🔍 目标文字: '推荐封面'
[2025-07-16 07:57:24] [INFO] 🔍 文字检测: 找到 1 个匹配元素
[2025-07-16 07:57:24] [INFO] 🔍 文字检测: 元素 1 - 标签: div, 可见: True
[2025-07-16 07:57:24] [INFO] 🔍 文字检测: 元素 1 - 实际文字: '推荐封面'
[2025-07-16 07:57:24] [INFO] ✅ 文字检测成功: 元素文字完全匹配目标文字 '推荐封面'
[2025-07-16 07:57:24] [INFO] ✅ 第3次检测: 通过文字检测到上传完成
[2025-07-16 07:57:24] [INFO] 🔍 进行二次验证...
[2025-07-16 07:57:25] [INFO] 昨日播放:  -> 0.0
[2025-07-16 07:57:25] [INFO] 总粉丝: 1,335 -> 1335.0
[2025-07-16 07:57:27] [INFO] 🔍 文字检测开始: 查找路径 //*[@id="root"]/div/div[2]/div/div[2]/div/div/div[1]/div/div[2]/div[1]/div[2]/div[2]/div
[2025-07-16 07:57:27] [INFO] 🔍 目标文字: '推荐封面'
[2025-07-16 07:57:27] [INFO] 🔍 文字检测: 找到 1 个匹配元素
[2025-07-16 07:57:27] [INFO] 🔍 文字检测: 元素 1 - 标签: div, 可见: True
[2025-07-16 07:57:27] [INFO] 🔍 文字检测: 元素 1 - 实际文字: '推荐封面'
[2025-07-16 07:57:27] [INFO] ✅ 文字检测成功: 元素文字完全匹配目标文字 '推荐封面'
[2025-07-16 07:57:27] [INFO] ✅ 二次验证成功: 确认视频上传完成
[2025-07-16 07:57:27] [INFO] 视频上传成功，等待页面稳定...
[2025-07-16 07:57:29] [INFO] 视频标题: 大满贯结束，王励勤重新调整男单阵容，4 个人恐失去“信任”
[2025-07-16 07:57:29] [INFO] 准备上传封面，等待页面完全加载...
[2025-07-16 07:57:30] [INFO] 无头模式: 尝试上传封面
[2025-07-16 07:57:30] [INFO] 无头模式: 已找到并点击上传封面按钮
[2025-07-16 07:57:31] [INFO] 无头模式: 已找到并点击本地上传按钮
[2025-07-16 07:57:32] [INFO] 无头模式: 封面文件已上传
[2025-07-16 07:57:32] [INFO] 无头模式: 等待图片缩略图出现
[2025-07-16 07:57:32] [INFO] 无头模式: 等待图片加载完成...
[2025-07-16 07:57:32] [INFO] ✅ 找到并加载了图片: https://mobilepics.ws.126.net/2025_03_04_19_47_26h18zj0li65cod8.jpg
[2025-07-16 07:57:32] [INFO] 无头模式: ✅ 检测到图片已完全加载
[2025-07-16 07:57:32] [INFO] 无头模式: 已点击确认按钮
[2025-07-16 07:57:32] [INFO] 已关闭弹窗
[2025-07-16 07:57:33] [INFO] 昨日粉丝:  -> 0.0
[2025-07-16 07:57:33] [INFO] 正在访问收益分析页面: https://mp.toutiao.com/profile_v4/analysis/income-overview
[2025-07-16 07:57:33] [INFO] 已尝试按ESC键关闭弹窗
[2025-07-16 07:57:34] [INFO] 正在等待收益分析页面加载...
[2025-07-16 07:57:34] [INFO] 已点击声明按钮
[2025-07-16 07:57:35] [INFO] 已点击声明内容按钮
[2025-07-16 07:57:35] [INFO] 已选择'取材网络'选项 (通过文本内容查找)
[2025-07-16 07:57:35] [INFO] 已点击分类输入框
[2025-07-16 07:57:35] [INFO] 已选择第一级分类'要闻'(通过文本内容查找)
[2025-07-16 07:57:35] [INFO] 已选择第二级分类'国际'(通过文本内容查找)
[2025-07-16 07:57:35] [INFO] 分类设置完成：要闻 > 国际
[2025-07-16 07:57:36] [INFO] 已生成标签: 大满贯, 王励勤, 结束, 重新, 调整
[2025-07-16 07:57:37] [INFO] 准备输入标签
[2025-07-16 07:57:39] [INFO] 收益分析页面主要内容已加载
[2025-07-16 07:57:39] [INFO] 等待七日收益表格加载...
[2025-07-16 07:57:42] [INFO] 找到七日收益表格
[2025-07-16 07:57:42] [INFO] 第1天收益: 2025-07-15 - 0 -> 0.0
[2025-07-16 07:57:42] [INFO] 第2天收益: 2025-07-14 - 0.23 -> 0.23
[2025-07-16 07:57:42] [INFO] 第3天收益: 2025-07-13 - 0.30 -> 0.3
[2025-07-16 07:57:42] [INFO] 第4天收益: 2025-07-12 - 1.05 -> 1.05
[2025-07-16 07:57:42] [INFO] 第5天收益: 2025-07-11 - 0.38 -> 0.38
[2025-07-16 07:57:42] [INFO] 第6天收益: 2025-07-10 - 0.44 -> 0.44
[2025-07-16 07:57:42] [INFO] 第7天收益: 2025-07-09 - 2.54 -> 2.54
[2025-07-16 07:57:42] [INFO] 正在获取草稿箱数据...
[2025-07-16 07:57:42] [INFO] 正在访问草稿箱页面: https://mp.toutiao.com/profile_v4/manage/draft
[2025-07-16 07:57:42] [INFO] 验证标签设置时出错: Message: stale element reference: stale element not found in the current frame
  (Session info: chrome=138.0.7204.101); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff77f536f75+76917]
	GetHandleVerifier [0x0x7ff77f536fd0+77008]
	(No symbol) [0x0x7ff77f2e9dea]
	(No symbol) [0x0x7ff77f2f1789]
	(No symbol) [0x0x7ff77f2f482c]
	(No symbol) [0x0x7ff77f2f48ff]
	(No symbol) [0x0x7ff77f339d66]
	(No symbol) [0x0x7ff77f36846a]
	(No symbol) [0x0x7ff77f332c16]
	(No symbol) [0x0x7ff77f368680]
	(No symbol) [0x0x7ff77f39065c]
	(No symbol) [0x0x7ff77f368243]
	(No symbol) [0x0x7ff77f331431]
	(No symbol) [0x0x7ff77f3321c3]
	GetHandleVerifier [0x0x7ff77f80d2ad+3051437]
	GetHandleVerifier [0x0x7ff77f807903+3028483]
	GetHandleVerifier [0x0x7ff77f82589d+3151261]
	GetHandleVerifier [0x0x7ff77f55183e+185662]
	GetHandleVerifier [0x0x7ff77f5596ff+218111]
	GetHandleVerifier [0x0x7ff77f53faf4+112628]
	GetHandleVerifier [0x0x7ff77f53fca9+113065]
	GetHandleVerifier [0x0x7ff77f526c78+10616]
	BaseThreadInitThunk [0x0x7ffbde9be8d7+23]
	RtlUserThreadStart [0x0x7ffbdf43c34c+44]

[2025-07-16 07:57:42] [INFO] 已设置标签: 大满贯, 王励勤, 结束, 重新, 调整
[2025-07-16 07:57:43] [INFO] 正在等待草稿箱页面加载...
[2025-07-16 07:57:43] [INFO] ✅ 已点击保存草稿按钮
[2025-07-16 07:57:45] [INFO] ✅ 存稿成功！视频已保存到草稿箱
[2025-07-16 07:57:45] [INFO] 视频上传成功
[2025-07-16 07:57:45] [INFO] 存稿成功
[2025-07-16 07:57:45] [INFO] ✅ 视频存稿成功: 大满贯结束，王励勤重新调整男单阵容，4 个人恐失去“信任”.mp4
[2025-07-16 07:57:45] [INFO] 更新总存稿成功数量: 1，当前循环: 1，循环限制: 3，每循环限制: 2
[2025-07-16 07:57:45] [INFO] 已标记视频 大满贯结束，王励勤重新调整男单阵容，4 个人恐失去“信任”.mp4 被账号 *********** 网易 处理 (成功)
[2025-07-16 07:57:45] [INFO] 已从处理列表中移除视频: 大满贯结束，王励勤重新调整男单阵容，4 个人恐失去“信任”.mp4
[2025-07-16 07:57:45] [INFO] 当前存稿进度: 1/2
[2025-07-16 07:57:45] [INFO] 从视频信息中获取存稿成功: 1
[2025-07-16 07:57:45] [INFO] 已更新账号 *********** 网易 的进度信息: 存稿成功数=1, 状态=成功
[2025-07-16 07:57:45] [INFO] 已更新账号 *********** 网易 状态到账号管理器: 正常
[2025-07-16 07:57:45] [INFO] 已更新存稿详情数据: 大满贯结束，王励勤重新调整男单阵容，4 个人恐失去“信任”.mp4 - 成功，总存稿数: 1
[2025-07-16 07:57:45] [INFO] 等待 2.8 秒后处理下一个视频...
[2025-07-16 07:57:48] [INFO] 草稿箱页面主要内容已加载
[2025-07-16 07:57:48] [INFO] 使用XPath获取到草稿数量: //*[@id="masterRoot"]/div/div[3]/section/main/div[2]/div/div[1]/div/div/div[10]/span/span
[2025-07-16 07:57:48] [INFO] 草稿箱数量: 草稿箱 (21) -> 21
[2025-07-16 07:57:48] [INFO] 正在获取提现数据...
[2025-07-16 07:57:48] [INFO] 正在访问提现页面: https://mp.toutiao.com/profile_v4/personal/checkout-center
[2025-07-16 07:57:48] [INFO] 重新打开网易视频上传页面
[2025-07-16 07:57:49] [INFO] 正在等待提现页面加载...
[2025-07-16 07:57:50] [INFO] ✅ 存稿成功后准备下一次存稿
[2025-07-16 07:57:50] [INFO] 🖼️ 封面准备完成: 普京参观少林寺真实影像，中途提出想和武僧比武，为何又取消了？.jpg
[2025-07-16 07:57:50] [INFO] 开始处理视频: 普京参观少林寺真实影像，中途提出想和武僧比武，为何又取消了？.mp4
[2025-07-16 07:57:50] [INFO] 视频完整路径: D:/头条全自动/视频搬运/已处理视频\普京参观少林寺真实影像，中途提出想和武僧比武，为何又取消了？.mp4
[2025-07-16 07:57:50] [INFO] 等待上传视频
[2025-07-16 07:57:50] [INFO] 正在打开网易视频上传页面
[2025-07-16 07:57:50] [INFO] 准备上传视频: D:\头条全自动\视频搬运\已处理视频\普京参观少林寺真实影像，中途提出想和武僧比武，为何又取消了？.mp4
[2025-07-16 07:57:52] [INFO] 找到文件输入元素
[2025-07-16 07:57:52] [INFO] 格式化后的文件路径: D:/头条全自动/视频搬运/已处理视频/普京参观少林寺真实影像，中途提出想和武僧比武，为何又取消了？.mp4
[2025-07-16 07:57:52] [INFO] ✅ 已选择视频文件: 普京参观少林寺真实影像，中途提出想和武僧比武，为何又取消了？.mp4
[2025-07-16 07:57:52] [INFO] ⏳ 等待视频上传完成...
[2025-07-16 07:57:52] [INFO] 🔍 开始改进的视频上传完成检测，超时时间: 180秒
[2025-07-16 07:57:52] [INFO] 🔍 检测配置: 每3秒检测一次，最多检测60次
[2025-07-16 07:57:52] [INFO] 🔍 第1次检测 (3秒)
[2025-07-16 07:57:52] [INFO] 🔍 文字检测开始: 查找路径 //*[@id="root"]/div/div[2]/div/div[2]/div/div/div[1]/div/div[2]/div[1]/div[2]/div[2]/div
[2025-07-16 07:57:52] [INFO] 🔍 目标文字: '推荐封面'
[2025-07-16 07:57:52] [INFO] 🔍 文字检测: 找到 1 个匹配元素
[2025-07-16 07:57:52] [INFO] 🔍 文字检测: 元素 1 - 标签: div, 可见: True
[2025-07-16 07:57:52] [INFO] 🔍 文字检测: 元素 1 - 实际文字: '视频正在解析，可点击上传图片进行本地/手机上传封面图'
[2025-07-16 07:57:52] [INFO] 🔍 文字检测: 元素文字不匹配目标文字
[2025-07-16 07:57:52] [INFO] 🔍 实际文字: '视频正在解析，可点击上传图片进行本地/手机上传封面图' | 目标文字: '推荐封面'
[2025-07-16 07:57:52] [INFO] 🔍 文字检测: 所有元素检查完毕，未找到匹配的目标文字
[2025-07-16 07:57:52] [INFO] 🔍 备用文字检测开始: 使用多种方式检测'推荐封面'文字
[2025-07-16 07:57:52] [INFO] 🔍 备用文字检测方法1: 检查页面源码 (长度: 20406)
[2025-07-16 07:57:52] [INFO] 🔍 备用文字检测方法1: 页面源码中未发现'推荐封面'文字
[2025-07-16 07:57:52] [INFO] 🔍 备用文字检测方法2: 查找包含'推荐封面'的元素
[2025-07-16 07:57:52] [INFO] 🔍 备用文字检测方法2: 找到 0 个包含'推荐封面'的元素
[2025-07-16 07:57:52] [INFO] 🔍 备用文字检测方法2: 未找到包含'推荐封面'的元素
[2025-07-16 07:57:52] [INFO] 🔍 备用文字检测方法3: 使用宽泛XPath查找'推荐封面'
[2025-07-16 07:57:52] [INFO] 🔍 备用文字检测方法3: XPath模式 1 找到 0 个元素
[2025-07-16 07:57:52] [INFO] 🔍 备用文字检测方法3: XPath模式 2 找到 0 个元素
[2025-07-16 07:57:52] [INFO] 🔍 备用文字检测方法3: XPath模式 3 找到 0 个元素
[2025-07-16 07:57:52] [INFO] 🔍 备用文字检测方法3: XPath模式 4 找到 0 个元素
[2025-07-16 07:57:52] [INFO] 🔍 备用文字检测方法3: XPath模式 5 找到 0 个元素
[2025-07-16 07:57:52] [INFO] 🔍 备用文字检测方法3: XPath模式 6 找到 0 个元素
[2025-07-16 07:57:52] [INFO] 🔍 备用文字检测: 所有方法都未检测到'推荐封面'文字
[2025-07-16 07:57:52] [INFO] ⏳ 第1次检测未发现上传完成，等待3秒后继续...
[2025-07-16 07:57:54] [INFO] 提现页面主要内容已加载
[2025-07-16 07:57:54] [INFO] 可提现: 2.4元 -> 2.4
[2025-07-16 07:57:54] [INFO] 总提现: 215.01元 -> 215.01
[2025-07-16 07:57:54] [INFO] 找到提现记录表格
[2025-07-16 07:57:54] [INFO] 最近提现: 2025-07-10 - 14.57 -> 14.57
[2025-07-16 07:57:54] [INFO] 账号 *********** 数据查询完成
[2025-07-16 07:57:55] [INFO] 🔍 第2次检测 (6秒)
[2025-07-16 07:57:55] [INFO] 🔍 文字检测开始: 查找路径 //*[@id="root"]/div/div[2]/div/div[2]/div/div/div[1]/div/div[2]/div[1]/div[2]/div[2]/div
[2025-07-16 07:57:55] [INFO] 🔍 目标文字: '推荐封面'
[2025-07-16 07:57:55] [INFO] 🔍 文字检测: 找到 1 个匹配元素
[2025-07-16 07:57:55] [INFO] 🔍 文字检测: 元素 1 - 标签: div, 可见: True
[2025-07-16 07:57:55] [INFO] 🔍 文字检测: 元素 1 - 实际文字: '视频正在解析，可点击上传图片进行本地/手机上传封面图'
[2025-07-16 07:57:55] [INFO] 🔍 文字检测: 元素文字不匹配目标文字
[2025-07-16 07:57:55] [INFO] 🔍 实际文字: '视频正在解析，可点击上传图片进行本地/手机上传封面图' | 目标文字: '推荐封面'
[2025-07-16 07:57:55] [INFO] 🔍 文字检测: 所有元素检查完毕，未找到匹配的目标文字
[2025-07-16 07:57:55] [INFO] 🔍 备用文字检测开始: 使用多种方式检测'推荐封面'文字
[2025-07-16 07:57:55] [INFO] 🔍 备用文字检测方法1: 检查页面源码 (长度: 23103)
[2025-07-16 07:57:55] [INFO] 🔍 备用文字检测方法1: 页面源码中未发现'推荐封面'文字
[2025-07-16 07:57:56] [INFO] 🔍 备用文字检测方法2: 查找包含'推荐封面'的元素
[2025-07-16 07:57:56] [INFO] 🔍 备用文字检测方法2: 找到 0 个包含'推荐封面'的元素
[2025-07-16 07:57:56] [INFO] 🔍 备用文字检测方法2: 未找到包含'推荐封面'的元素
[2025-07-16 07:57:56] [INFO] 🔍 备用文字检测方法3: 使用宽泛XPath查找'推荐封面'
[2025-07-16 07:57:56] [INFO] 🔍 备用文字检测方法3: XPath模式 1 找到 0 个元素
[2025-07-16 07:57:56] [INFO] 🔍 备用文字检测方法3: XPath模式 2 找到 0 个元素
[2025-07-16 07:57:56] [INFO] 🔍 备用文字检测方法3: XPath模式 3 找到 0 个元素
[2025-07-16 07:57:56] [INFO] 🔍 备用文字检测方法3: XPath模式 4 找到 0 个元素
[2025-07-16 07:57:56] [INFO] 🔍 备用文字检测方法3: XPath模式 5 找到 0 个元素
[2025-07-16 07:57:56] [INFO] 🔍 备用文字检测方法3: XPath模式 6 找到 0 个元素
[2025-07-16 07:57:56] [INFO] 🔍 备用文字检测: 所有方法都未检测到'推荐封面'文字
[2025-07-16 07:57:56] [INFO] ⏳ 第2次检测未发现上传完成，等待3秒后继续...
[2025-07-16 07:57:56] [INFO] 已保存账号数据: 25 条记录
[2025-07-16 07:57:56] [INFO] ✅ *********** 数据已更新到文件
[2025-07-16 07:57:56] [INFO] 📊 任务进度完成: 1/1
[2025-07-16 07:57:56] [INFO] [头条查询管理器] ✅ 账号 *********** 查询完成 (1/1)
[2025-07-16 07:57:56] [INFO] [头条查询管理器] 🎉 并发查询完成，共处理 1 个账号
[2025-07-16 07:57:56] [INFO] ✅ 头条号账号 *********** 数据查询成功
[2025-07-16 07:57:56] [INFO] 🔍 头条号账号 *********** 查询完成
[2025-07-16 07:57:56] [INFO] 🔍 [调试] 当前平台: netease
[2025-07-16 07:57:56] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/网易号账号
[2025-07-16 07:57:56] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\网易号账号
[2025-07-16 07:57:56] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\网易号账号\netease_account_data.json
[2025-07-16 07:57:56] [INFO] 🔍 [调试] 更新账号树 - 当前平台: netease
[2025-07-16 07:57:56] [INFO] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\网易号账号\netease_account_data.json
[2025-07-16 07:57:56] [INFO] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-07-16 07:57:56] [INFO] 已加载账号数据: 25 条记录
[2025-07-16 07:57:56] [INFO] 🔍 [调试] 账号数据为空或未加载
[2025-07-16 07:57:56] [INFO] 🔍 [调试] 当前平台: netease
[2025-07-16 07:57:56] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/网易号账号
[2025-07-16 07:57:56] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\网易号账号
[2025-07-16 07:57:56] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\网易号账号\netease_account_data.json
[2025-07-16 07:57:56] [INFO] 🔍 [调试] 更新账号树 - 当前平台: netease
[2025-07-16 07:57:56] [INFO] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\网易号账号\netease_account_data.json
[2025-07-16 07:57:56] [INFO] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-07-16 07:57:56] [INFO] 已加载账号数据: 25 条记录
[2025-07-16 07:57:56] [INFO] 🔍 [调试] 账号数据为空或未加载
[2025-07-16 07:57:59] [INFO] 🔍 第3次检测 (9秒)
[2025-07-16 07:57:59] [INFO] 🔍 文字检测开始: 查找路径 //*[@id="root"]/div/div[2]/div/div[2]/div/div/div[1]/div/div[2]/div[1]/div[2]/div[2]/div
[2025-07-16 07:57:59] [INFO] 🔍 目标文字: '推荐封面'
[2025-07-16 07:57:59] [INFO] 🔍 文字检测: 找到 1 个匹配元素
[2025-07-16 07:57:59] [INFO] 🔍 文字检测: 元素 1 - 标签: div, 可见: True
[2025-07-16 07:57:59] [INFO] 🔍 文字检测: 元素 1 - 实际文字: '视频正在解析，可点击上传图片进行本地/手机上传封面图'
[2025-07-16 07:57:59] [INFO] 🔍 文字检测: 元素文字不匹配目标文字
[2025-07-16 07:57:59] [INFO] 🔍 实际文字: '视频正在解析，可点击上传图片进行本地/手机上传封面图' | 目标文字: '推荐封面'
[2025-07-16 07:57:59] [INFO] 🔍 文字检测: 所有元素检查完毕，未找到匹配的目标文字
[2025-07-16 07:57:59] [INFO] 🔍 备用文字检测开始: 使用多种方式检测'推荐封面'文字
[2025-07-16 07:57:59] [INFO] 🔍 备用文字检测方法1: 检查页面源码 (长度: 23102)
[2025-07-16 07:57:59] [INFO] 🔍 备用文字检测方法1: 页面源码中未发现'推荐封面'文字
[2025-07-16 07:57:59] [INFO] 🔍 备用文字检测方法2: 查找包含'推荐封面'的元素
[2025-07-16 07:57:59] [INFO] 🔍 备用文字检测方法2: 找到 0 个包含'推荐封面'的元素
[2025-07-16 07:57:59] [INFO] 🔍 备用文字检测方法2: 未找到包含'推荐封面'的元素
[2025-07-16 07:57:59] [INFO] 🔍 备用文字检测方法3: 使用宽泛XPath查找'推荐封面'
[2025-07-16 07:57:59] [INFO] 🔍 备用文字检测方法3: XPath模式 1 找到 0 个元素
[2025-07-16 07:57:59] [INFO] 🔍 备用文字检测方法3: XPath模式 2 找到 0 个元素
[2025-07-16 07:57:59] [INFO] 🔍 备用文字检测方法3: XPath模式 3 找到 0 个元素
[2025-07-16 07:57:59] [INFO] 🔍 备用文字检测方法3: XPath模式 4 找到 0 个元素
[2025-07-16 07:57:59] [INFO] 🔍 备用文字检测方法3: XPath模式 5 找到 0 个元素
[2025-07-16 07:57:59] [INFO] 🔍 备用文字检测方法3: XPath模式 6 找到 0 个元素
[2025-07-16 07:57:59] [INFO] 🔍 备用文字检测: 所有方法都未检测到'推荐封面'文字
[2025-07-16 07:57:59] [INFO] ⏳ 第3次检测未发现上传完成，等待3秒后继续...
[2025-07-16 07:58:02] [INFO] 🔍 第4次检测 (12秒)
[2025-07-16 07:58:02] [INFO] 🔍 文字检测开始: 查找路径 //*[@id="root"]/div/div[2]/div/div[2]/div/div/div[1]/div/div[2]/div[1]/div[2]/div[2]/div
[2025-07-16 07:58:02] [INFO] 🔍 目标文字: '推荐封面'
[2025-07-16 07:58:02] [INFO] 🔍 文字检测: 找到 1 个匹配元素
[2025-07-16 07:58:02] [INFO] 🔍 文字检测: 元素 1 - 标签: div, 可见: True
[2025-07-16 07:58:02] [INFO] 🔍 文字检测: 元素 1 - 实际文字: '推荐封面'
[2025-07-16 07:58:02] [INFO] ✅ 文字检测成功: 元素文字完全匹配目标文字 '推荐封面'
[2025-07-16 07:58:02] [INFO] ✅ 第4次检测: 通过文字检测到上传完成
[2025-07-16 07:58:02] [INFO] 🔍 进行二次验证...
[2025-07-16 07:58:05] [INFO] 🔍 文字检测开始: 查找路径 //*[@id="root"]/div/div[2]/div/div[2]/div/div/div[1]/div/div[2]/div[1]/div[2]/div[2]/div
[2025-07-16 07:58:05] [INFO] 🔍 目标文字: '推荐封面'
[2025-07-16 07:58:05] [INFO] 🔍 文字检测: 找到 1 个匹配元素
[2025-07-16 07:58:05] [INFO] 🔍 文字检测: 元素 1 - 标签: div, 可见: True
[2025-07-16 07:58:05] [INFO] 🔍 文字检测: 元素 1 - 实际文字: '推荐封面'
[2025-07-16 07:58:05] [INFO] ✅ 文字检测成功: 元素文字完全匹配目标文字 '推荐封面'
[2025-07-16 07:58:05] [INFO] ✅ 二次验证成功: 确认视频上传完成
[2025-07-16 07:58:05] [INFO] 视频上传成功，等待页面稳定...
[2025-07-16 07:58:07] [INFO] 视频标题: 普京参观少林寺真实影像，中途提出想和武僧比武，为何又取消了？
[2025-07-16 07:58:07] [INFO] 准备上传封面，等待页面完全加载...
[2025-07-16 07:58:08] [INFO] 无头模式: 尝试上传封面
[2025-07-16 07:58:08] [INFO] 无头模式: 已找到并点击上传封面按钮
[2025-07-16 07:58:09] [INFO] 无头模式: 已找到并点击本地上传按钮
[2025-07-16 07:58:10] [INFO] 无头模式: 封面文件已上传
[2025-07-16 07:58:10] [INFO] 无头模式: 等待图片缩略图出现
[2025-07-16 07:58:10] [INFO] 无头模式: 等待图片加载完成...
[2025-07-16 07:58:10] [INFO] ✅ 找到并加载了图片: https://mobilepics.ws.126.net/2025_03_04_19_47_26h18zj0li65cod8.jpg
[2025-07-16 07:58:10] [INFO] 无头模式: ✅ 检测到图片已完全加载
[2025-07-16 07:58:10] [INFO] 无头模式: 已点击确认按钮
[2025-07-16 07:58:10] [INFO] 已关闭弹窗
[2025-07-16 07:58:11] [INFO] 已尝试按ESC键关闭弹窗
[2025-07-16 07:58:12] [INFO] 已点击声明按钮
[2025-07-16 07:58:13] [INFO] 已点击声明内容按钮
[2025-07-16 07:58:13] [INFO] 已选择'取材网络'选项 (通过文本内容查找)
[2025-07-16 07:58:13] [INFO] 已点击分类输入框
[2025-07-16 07:58:13] [INFO] 已选择第一级分类'要闻'(通过文本内容查找)
[2025-07-16 07:58:13] [INFO] 已选择第二级分类'国际'(通过文本内容查找)
[2025-07-16 07:58:13] [INFO] 分类设置完成：要闻 > 国际
[2025-07-16 07:58:13] [INFO] 已生成标签: 少林寺, 普京, 参观, 真实, 影像
[2025-07-16 07:58:14] [INFO] 准备输入标签
[2025-07-16 07:58:19] [INFO] 验证标签设置时出错: Message: stale element reference: stale element not found in the current frame
  (Session info: chrome=138.0.7204.101); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff77f536f75+76917]
	GetHandleVerifier [0x0x7ff77f536fd0+77008]
	(No symbol) [0x0x7ff77f2e9dea]
	(No symbol) [0x0x7ff77f2f1789]
	(No symbol) [0x0x7ff77f2f482c]
	(No symbol) [0x0x7ff77f2f48ff]
	(No symbol) [0x0x7ff77f339d66]
	(No symbol) [0x0x7ff77f36846a]
	(No symbol) [0x0x7ff77f332c16]
	(No symbol) [0x0x7ff77f368680]
	(No symbol) [0x0x7ff77f39065c]
	(No symbol) [0x0x7ff77f368243]
	(No symbol) [0x0x7ff77f331431]
	(No symbol) [0x0x7ff77f3321c3]
	GetHandleVerifier [0x0x7ff77f80d2ad+3051437]
	GetHandleVerifier [0x0x7ff77f807903+3028483]
	GetHandleVerifier [0x0x7ff77f82589d+3151261]
	GetHandleVerifier [0x0x7ff77f55183e+185662]
	GetHandleVerifier [0x0x7ff77f5596ff+218111]
	GetHandleVerifier [0x0x7ff77f53faf4+112628]
	GetHandleVerifier [0x0x7ff77f53fca9+113065]
	GetHandleVerifier [0x0x7ff77f526c78+10616]
	BaseThreadInitThunk [0x0x7ffbde9be8d7+23]
	RtlUserThreadStart [0x0x7ffbdf43c34c+44]

[2025-07-16 07:58:19] [INFO] 已设置标签: 少林寺, 普京, 参观, 真实, 影像
[2025-07-16 07:58:20] [INFO] ✅ 已点击保存草稿按钮
[2025-07-16 07:58:22] [INFO] ✅ 存稿成功！视频已保存到草稿箱
[2025-07-16 07:58:22] [INFO] 视频上传成功
[2025-07-16 07:58:22] [INFO] 存稿成功
[2025-07-16 07:58:22] [INFO] ✅ 视频存稿成功: 普京参观少林寺真实影像，中途提出想和武僧比武，为何又取消了？.mp4
[2025-07-16 07:58:22] [INFO] 更新总存稿成功数量: 2，当前循环: 1，循环限制: 3，每循环限制: 2
[2025-07-16 07:58:22] [INFO] 已标记视频 普京参观少林寺真实影像，中途提出想和武僧比武，为何又取消了？.mp4 被账号 *********** 网易 处理 (成功)
[2025-07-16 07:58:22] [INFO] 已从处理列表中移除视频: 普京参观少林寺真实影像，中途提出想和武僧比武，为何又取消了？.mp4
[2025-07-16 07:58:22] [INFO] 当前存稿进度: 2/2
[2025-07-16 07:58:22] [INFO] 从视频信息中获取存稿成功: 2
[2025-07-16 07:58:22] [INFO] 已更新账号 *********** 网易 的进度信息: 存稿成功数=1, 状态=成功
[2025-07-16 07:58:22] [INFO] 已更新账号 *********** 网易 状态到账号管理器: 正常
[2025-07-16 07:58:22] [INFO] 已更新存稿详情数据: 普京参观少林寺真实影像，中途提出想和武僧比武，为何又取消了？.mp4 - 成功，总存稿数: 2
[2025-07-16 07:58:22] [INFO] 等待 2.6 秒后处理下一个视频...
[2025-07-16 07:58:25] [INFO] 已达到设定的存稿数量限制 (2 个)，当前循环已完成
[2025-07-16 07:58:25] [INFO] 存稿数量已达到限制 (2 个)，准备进入下一轮循环
[2025-07-16 07:58:25] [INFO] 等待1秒后开始下一轮循环...
[2025-07-16 07:58:26] [INFO] 🔄 开始第2/3轮处理
[2025-07-16 07:58:28] [INFO] 重新打开网易视频上传页面
[2025-07-16 07:58:28] [INFO] 为账号 *********** 网易 分配了 433 个可用视频
[2025-07-16 07:58:28] [INFO] 📁 循环2: 433个视频已随机排序
[2025-07-16 07:58:28] [INFO] 🖼️ 封面准备完成: 中国男足1-0香港，首胜引爆冲突.jpg
[2025-07-16 07:58:28] [INFO] 开始处理视频: 中国男足1-0香港，首胜引爆冲突.mp4
[2025-07-16 07:58:28] [INFO] 视频完整路径: D:/头条全自动/视频搬运/已处理视频\中国男足1-0香港，首胜引爆冲突.mp4
[2025-07-16 07:58:28] [INFO] 等待上传视频
[2025-07-16 07:58:28] [INFO] 正在打开网易视频上传页面
[2025-07-16 07:58:28] [INFO] 准备上传视频: D:\头条全自动\视频搬运\已处理视频\中国男足1-0香港，首胜引爆冲突.mp4
[2025-07-16 07:58:30] [INFO] 找到文件输入元素
[2025-07-16 07:58:30] [INFO] 格式化后的文件路径: D:/头条全自动/视频搬运/已处理视频/中国男足1-0香港，首胜引爆冲突.mp4
[2025-07-16 07:58:30] [INFO] ✅ 已选择视频文件: 中国男足1-0香港，首胜引爆冲突.mp4
[2025-07-16 07:58:30] [INFO] ⏳ 等待视频上传完成...
[2025-07-16 07:58:30] [INFO] 🔍 开始改进的视频上传完成检测，超时时间: 180秒
[2025-07-16 07:58:30] [INFO] 🔍 检测配置: 每3秒检测一次，最多检测60次
[2025-07-16 07:58:30] [INFO] 🔍 第1次检测 (3秒)
[2025-07-16 07:58:30] [INFO] 🔍 文字检测开始: 查找路径 //*[@id="root"]/div/div[2]/div/div[2]/div/div/div[1]/div/div[2]/div[1]/div[2]/div[2]/div
[2025-07-16 07:58:30] [INFO] 🔍 目标文字: '推荐封面'
[2025-07-16 07:58:30] [INFO] 🔍 文字检测: 找到 1 个匹配元素
[2025-07-16 07:58:30] [INFO] 🔍 文字检测: 元素 1 - 标签: div, 可见: True
[2025-07-16 07:58:30] [INFO] 🔍 文字检测: 元素 1 - 实际文字: '视频正在解析，可点击上传图片进行本地/手机上传封面图'
[2025-07-16 07:58:30] [INFO] 🔍 文字检测: 元素文字不匹配目标文字
[2025-07-16 07:58:30] [INFO] 🔍 实际文字: '视频正在解析，可点击上传图片进行本地/手机上传封面图' | 目标文字: '推荐封面'
[2025-07-16 07:58:30] [INFO] 🔍 文字检测: 所有元素检查完毕，未找到匹配的目标文字
[2025-07-16 07:58:30] [INFO] 🔍 备用文字检测开始: 使用多种方式检测'推荐封面'文字
[2025-07-16 07:58:30] [INFO] 🔍 备用文字检测方法1: 检查页面源码 (长度: 20364)
[2025-07-16 07:58:30] [INFO] 🔍 备用文字检测方法1: 页面源码中未发现'推荐封面'文字
[2025-07-16 07:58:30] [INFO] 🔍 备用文字检测方法2: 查找包含'推荐封面'的元素
[2025-07-16 07:58:30] [INFO] 🔍 备用文字检测方法2: 找到 0 个包含'推荐封面'的元素
[2025-07-16 07:58:30] [INFO] 🔍 备用文字检测方法2: 未找到包含'推荐封面'的元素
[2025-07-16 07:58:30] [INFO] 🔍 备用文字检测方法3: 使用宽泛XPath查找'推荐封面'
[2025-07-16 07:58:30] [INFO] 🔍 备用文字检测方法3: XPath模式 1 找到 0 个元素
[2025-07-16 07:58:30] [INFO] 🔍 备用文字检测方法3: XPath模式 2 找到 0 个元素
[2025-07-16 07:58:30] [INFO] 🔍 备用文字检测方法3: XPath模式 3 找到 0 个元素
[2025-07-16 07:58:31] [INFO] 🔍 备用文字检测方法3: XPath模式 4 找到 0 个元素
[2025-07-16 07:58:31] [INFO] 🔍 备用文字检测方法3: XPath模式 5 找到 0 个元素
[2025-07-16 07:58:31] [INFO] 🔍 备用文字检测方法3: XPath模式 6 找到 0 个元素
[2025-07-16 07:58:31] [INFO] 🔍 备用文字检测: 所有方法都未检测到'推荐封面'文字
[2025-07-16 07:58:31] [INFO] ⏳ 第1次检测未发现上传完成，等待3秒后继续...
[2025-07-16 07:58:34] [INFO] 🔍 第2次检测 (6秒)
[2025-07-16 07:58:34] [INFO] 🔍 文字检测开始: 查找路径 //*[@id="root"]/div/div[2]/div/div[2]/div/div/div[1]/div/div[2]/div[1]/div[2]/div[2]/div
[2025-07-16 07:58:34] [INFO] 🔍 目标文字: '推荐封面'
[2025-07-16 07:58:34] [INFO] 🔍 文字检测: 找到 1 个匹配元素
[2025-07-16 07:58:34] [INFO] 🔍 文字检测: 元素 1 - 标签: div, 可见: True
[2025-07-16 07:58:34] [INFO] 🔍 文字检测: 元素 1 - 实际文字: '视频正在解析，可点击上传图片进行本地/手机上传封面图'
[2025-07-16 07:58:34] [INFO] 🔍 文字检测: 元素文字不匹配目标文字
[2025-07-16 07:58:34] [INFO] 🔍 实际文字: '视频正在解析，可点击上传图片进行本地/手机上传封面图' | 目标文字: '推荐封面'
[2025-07-16 07:58:34] [INFO] 🔍 文字检测: 所有元素检查完毕，未找到匹配的目标文字
[2025-07-16 07:58:34] [INFO] 🔍 备用文字检测开始: 使用多种方式检测'推荐封面'文字
[2025-07-16 07:58:34] [INFO] 🔍 备用文字检测方法1: 检查页面源码 (长度: 22858)
[2025-07-16 07:58:34] [INFO] 🔍 备用文字检测方法1: 页面源码中未发现'推荐封面'文字
[2025-07-16 07:58:34] [INFO] 🔍 备用文字检测方法2: 查找包含'推荐封面'的元素
[2025-07-16 07:58:34] [INFO] 🔍 备用文字检测方法2: 找到 0 个包含'推荐封面'的元素
[2025-07-16 07:58:34] [INFO] 🔍 备用文字检测方法2: 未找到包含'推荐封面'的元素
[2025-07-16 07:58:34] [INFO] 🔍 备用文字检测方法3: 使用宽泛XPath查找'推荐封面'
[2025-07-16 07:58:34] [INFO] 🔍 备用文字检测方法3: XPath模式 1 找到 0 个元素
[2025-07-16 07:58:34] [INFO] 🔍 备用文字检测方法3: XPath模式 2 找到 0 个元素
[2025-07-16 07:58:34] [INFO] 🔍 备用文字检测方法3: XPath模式 3 找到 0 个元素
[2025-07-16 07:58:34] [INFO] 🔍 备用文字检测方法3: XPath模式 4 找到 0 个元素
[2025-07-16 07:58:34] [INFO] 🔍 备用文字检测方法3: XPath模式 5 找到 0 个元素
[2025-07-16 07:58:34] [INFO] 🔍 备用文字检测方法3: XPath模式 6 找到 0 个元素
[2025-07-16 07:58:34] [INFO] 🔍 备用文字检测: 所有方法都未检测到'推荐封面'文字
[2025-07-16 07:58:34] [INFO] ⏳ 第2次检测未发现上传完成，等待3秒后继续...
