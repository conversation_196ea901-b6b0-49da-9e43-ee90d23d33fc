"""
高级创意加载动画组件 - 提供更具创意和视觉冲击力的加载动画效果
"""

import tkinter as tk
import time
import math
import random
import string  # 用于MatrixRainAnimation中的字符选择
from typing import List, Optional, Callable, Dict, Any, Tuple, Union

class BaseAdvancedAnimation:
    """高级创意加载动画基类"""

    def __init__(self,
                 parent: tk.Widget,
                 width: int = 200,
                 height: int = 200,
                 bg_color: str = None,
                 fg_color: str = "#0d6efd",
                 text: str = "加载中...",
                 font: tuple = ("微软雅黑", 10),
                 show_text: bool = True):
        """
        初始化加载动画

        参数:
            parent: 父容器
            width: 动画宽度
            height: 动画高度
            bg_color: 背景颜色 (None表示使用父容器背景)
            fg_color: 前景颜色 (动画主色)
            text: 显示文本
            font: 文本字体
            show_text: 是否显示文本
        """
        self.parent = parent
        self.width = width
        self.height = height
        self.bg_color = bg_color or parent.cget("background")
        self.fg_color = fg_color
        self.text = text
        self.font = font
        self.show_text = show_text

        # 创建容器框架
        self.container = tk.Frame(parent, width=width, height=height, bg=self.bg_color)

        # 创建画布
        self.canvas = tk.Canvas(
            self.container,
            width=width,
            height=height if not show_text else height - 25,
            bg=self.bg_color,
            highlightthickness=0
        )
        self.canvas.pack(pady=(0, 10 if show_text else 0))

        # 创建文本标签
        if show_text:
            self.text_label = tk.Label(
                self.container,
                text=text,
                font=font,
                bg=self.bg_color,
                fg=self.fg_color
            )
            self.text_label.pack(pady=(0, 5))

        # 动画状态
        self.is_running = False
        self.animation_id = None
        self.start_time = None

    def start(self):
        """开始动画"""
        if self.is_running:
            return

        # 设置动画优先级
        self.canvas.config(takefocus=1)

        # 启用双缓冲，减少闪烁
        self.canvas.config(highlightthickness=0, bd=0)

        # 优化渲染
        self.canvas.update_idletasks()

        self.is_running = True
        self.start_time = time.time()
        self._animate()
        return self

    def stop(self):
        """停止动画"""
        if self.animation_id:
            self.canvas.after_cancel(self.animation_id)
            self.animation_id = None
        self.is_running = False
        return self

    def _animate(self):
        """动画帧更新方法，由子类实现具体动画效果"""
        pass

    def pack(self, **kwargs):
        """打包容器"""
        self.container.pack(**kwargs)
        return self

    def grid(self, **kwargs):
        """网格布局容器"""
        self.container.grid(**kwargs)
        return self

    def place(self, **kwargs):
        """位置布局容器"""
        self.container.place(**kwargs)
        return self

    def destroy(self):
        """销毁动画"""
        self.stop()
        self.container.destroy()

    def _hex_to_rgb(self, hex_color):
        """将十六进制颜色转换为RGB元组"""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

    def _rgb_to_hex(self, rgb):
        """将RGB元组转换为十六进制颜色"""
        return f"#{rgb[0]:02x}{rgb[1]:02x}{rgb[2]:02x}"

    def _blend_colors(self, color1, color2, factor):
        """混合两种颜色"""
        r1, g1, b1 = self._hex_to_rgb(color1)
        r2, g2, b2 = self._hex_to_rgb(color2)

        r = int(r1 + (r2 - r1) * factor)
        g = int(g1 + (g2 - g1) * factor)
        b = int(b1 + (b2 - b1) * factor)

        return f"#{r:02x}{g:02x}{b:02x}"

    def _adjust_color_brightness(self, color, factor):
        """调整颜色亮度"""
        r, g, b = self._hex_to_rgb(color)
        r = min(255, max(0, int(r * factor)))
        g = min(255, max(0, int(g * factor)))
        b = min(255, max(0, int(b * factor)))
        return f"#{r:02x}{g:02x}{b:02x}"


class DNAHelixAnimation(BaseAdvancedAnimation):
    """DNA螺旋动画 - 模拟DNA双螺旋结构的动态加载效果"""

    def __init__(self,
                 parent: tk.Widget,
                 width: int = 200,
                 height: int = 200,
                 bg_color: str = None,
                 fg_color: str = "#0d6efd",
                 text: str = "加载中...",
                 font: tuple = ("微软雅黑", 10),
                 show_text: bool = True,
                 rotation_speed: float = 1.0,
                 helix_width: int = 100,
                 helix_height: int = 140,
                 strand_colors: List[str] = None,
                 base_pair_colors: List[str] = None):
        """
        初始化DNA螺旋动画

        参数:
            parent: 父容器
            width: 动画宽度
            height: 动画高度
            bg_color: 背景颜色
            fg_color: 前景颜色
            text: 显示文本
            font: 文本字体
            show_text: 是否显示文本
            rotation_speed: 旋转速度
            helix_width: 螺旋宽度
            helix_height: 螺旋高度
            strand_colors: 链颜色列表
            base_pair_colors: 碱基对颜色列表
        """
        super().__init__(parent, width, height, bg_color, fg_color, text, font, show_text)
        self.rotation_speed = rotation_speed
        self.helix_width = helix_width
        self.helix_height = helix_height
        self.strand_colors = strand_colors or ["#0d6efd", "#dc3545"]  # 蓝色和红色
        self.base_pair_colors = base_pair_colors or ["#fd7e14", "#20c997"]  # 橙色和青色

        # DNA参数
        self.strand_points = 30  # 每条链的点数
        self.base_pairs = 10  # 碱基对数量

        # 创建DNA对象
        self.strand1_ids = []
        self.strand2_ids = []
        self.base_pair_ids = []

        # 初始化DNA结构
        self._create_dna()

    def _create_dna(self):
        """创建DNA结构"""
        canvas_height = self.height if not self.show_text else self.height - 25
        # 计算中心点坐标
        self.center_x = self.width // 2
        self.center_y = canvas_height // 2

        # 清除现有对象
        for strand_id in self.strand1_ids:
            self.canvas.delete(strand_id)
        for strand_id in self.strand2_ids:
            self.canvas.delete(strand_id)
        for pair_id in self.base_pair_ids:
            self.canvas.delete(pair_id)

        self.strand1_ids = []
        self.strand2_ids = []
        self.base_pair_ids = []

        # 创建DNA链
        for i in range(self.strand_points):
            # 第一条链
            strand1_id = self.canvas.create_oval(
                0, 0, 6, 6,
                fill=self.strand_colors[0],
                outline=""
            )
            self.strand1_ids.append(strand1_id)

            # 第二条链
            strand2_id = self.canvas.create_oval(
                0, 0, 6, 6,
                fill=self.strand_colors[1],
                outline=""
            )
            self.strand2_ids.append(strand2_id)

        # 创建碱基对
        for i in range(self.base_pairs):
            base_pair_id = self.canvas.create_line(
                0, 0, 0, 0,
                fill=self.base_pair_colors[i % len(self.base_pair_colors)],
                width=2
            )
            self.base_pair_ids.append(base_pair_id)

    def _animate(self):
        """更新动画帧"""
        if not self.is_running:
            return

        self.elapsed = time.time() - self.start_time
        canvas_height = self.height if not self.show_text else self.height - 25
        center_x = self.center_x
        center_y = self.center_y

        # 计算DNA链的位置
        for i in range(self.strand_points):
            # 计算当前点的角度
            angle = 2 * math.pi * i / self.strand_points + self.elapsed * self.rotation_speed

            # 第一条链
            x1 = center_x + self.helix_width/2 * math.cos(angle)
            y1 = center_y + i * self.helix_height / self.strand_points - self.helix_height/2

            # 第二条链 - 相位差180度
            x2 = center_x + self.helix_width/2 * math.cos(angle + math.pi)
            y2 = center_y + i * self.helix_height / self.strand_points - self.helix_height/2

            # 更新链的位置
            self.canvas.coords(
                self.strand1_ids[i],
                x1 - 3, y1 - 3, x1 + 3, y1 + 3
            )
            self.canvas.coords(
                self.strand2_ids[i],
                x2 - 3, y2 - 3, x2 + 3, y2 + 3
            )

            # 更新链的颜色 - 添加脉动效果
            pulse_factor = 0.7 + 0.3 * math.sin(self.elapsed * 2 + i * 0.2)
            strand1_color = self._adjust_color_brightness(self.strand_colors[0], pulse_factor)
            strand2_color = self._adjust_color_brightness(self.strand_colors[1], pulse_factor)

            self.canvas.itemconfig(self.strand1_ids[i], fill=strand1_color)
            self.canvas.itemconfig(self.strand2_ids[i], fill=strand2_color)

        # 更新碱基对
        for i in range(self.base_pairs):
            # 计算碱基对的位置
            pair_index = int(i * self.strand_points / self.base_pairs)
            if pair_index >= len(self.strand1_ids):
                continue

            # 获取两条链上对应点的坐标
            coords1 = self.canvas.coords(self.strand1_ids[pair_index])
            coords2 = self.canvas.coords(self.strand2_ids[pair_index])

            if not coords1 or not coords2:
                continue

            x1 = (coords1[0] + coords1[2]) / 2
            y1 = (coords1[1] + coords1[3]) / 2
            x2 = (coords2[0] + coords2[2]) / 2
            y2 = (coords2[1] + coords2[3]) / 2

            # 更新碱基对的位置
            self.canvas.coords(
                self.base_pair_ids[i],
                x1, y1, x2, y2
            )

            # 更新碱基对的颜色
            base_color = self.base_pair_colors[i % len(self.base_pair_colors)]
            pulse_factor = 0.7 + 0.3 * math.sin(self.elapsed * 3 + i * 0.5)
            base_color = self._adjust_color_brightness(base_color, pulse_factor)

            self.canvas.itemconfig(self.base_pair_ids[i], fill=base_color)

        # 继续动画 - 使用更高的帧率和优化的计时
        frame_delay = 10  # 约100fps，更流畅
        self.animation_id = self.canvas.after(frame_delay, self._animate)




class GalaxyAnimation(BaseAdvancedAnimation):
    """宇宙星系动画 - 模拟旋转星系的加载效果"""

    def __init__(self,
                 parent: tk.Widget,
                 width: int = 200,
                 height: int = 200,
                 bg_color: str = None,
                 fg_color: str = "#0d6efd",
                 text: str = "加载中...",
                 font: tuple = ("微软雅黑", 10),
                 show_text: bool = True,
                 rotation_speed: float = 0.5,
                 star_count: int = 200,
                 galaxy_colors: List[str] = None,
                 has_black_hole: bool = True):
        """
        初始化宇宙星系动画

        参数:
            parent: 父容器
            width: 动画宽度
            height: 动画高度
            bg_color: 背景颜色
            fg_color: 前景颜色
            text: 显示文本
            font: 文本字体
            show_text: 是否显示文本
            rotation_speed: 旋转速度
            star_count: 星星数量
            galaxy_colors: 星系颜色列表
            has_black_hole: 是否有黑洞
        """
        # 确保背景是深色
        if not bg_color:
            bg_color = "#121212"  # 深黑色

        super().__init__(parent, width, height, bg_color, fg_color, text, font, show_text)
        self.rotation_speed = rotation_speed
        self.star_count = star_count
        self.galaxy_colors = galaxy_colors or ["#ffffff", "#00ffff", "#ff00ff", "#ffff00", "#ff8800"]
        self.has_black_hole = has_black_hole

        # 星系参数
        self.stars = []
        self.black_hole_id = None
        self.black_hole_glow_id = None

        # 创建星系
        self._create_galaxy()

    def _create_galaxy(self):
        """创建星系"""
        canvas_height = self.height if not self.show_text else self.height - 25
        center_x = self.width // 2
        center_y = canvas_height // 2

        # 清除现有对象
        for star in self.stars:
            self.canvas.delete(star['id'])
        if self.black_hole_id:
            self.canvas.delete(self.black_hole_id)
        if self.black_hole_glow_id:
            self.canvas.delete(self.black_hole_glow_id)

        self.stars = []

        # 创建黑洞
        if self.has_black_hole:
            # 黑洞光晕
            self.black_hole_glow_id = self.canvas.create_oval(
                center_x - 20, center_y - 20,
                center_x + 20, center_y + 20,
                fill="#0066ff",
                outline=""
            )

            # 黑洞
            self.black_hole_id = self.canvas.create_oval(
                center_x - 10, center_y - 10,
                center_x + 10, center_y + 10,
                fill="#000000",
                outline="#0066ff",
                width=2
            )

        # 创建星星
        for _ in range(self.star_count):
            # 随机距离和角度
            distance = random.uniform(20, min(self.width, canvas_height) / 2 - 10)
            angle = random.uniform(0, 2 * math.pi)

            # 随机大小
            size = random.uniform(1, 3)

            # 随机颜色
            color = random.choice(self.galaxy_colors)

            # 随机亮度
            brightness = random.uniform(0.5, 1.0)

            # 计算位置
            x = center_x + distance * math.cos(angle)
            y = center_y + distance * math.sin(angle)

            # 创建星星
            star_id = self.canvas.create_oval(
                x - size/2, y - size/2,
                x + size/2, y + size/2,
                fill=self._adjust_color_brightness(color, brightness),
                outline=""
            )

            # 保存星星信息
            star = {
                'id': star_id,
                'distance': distance,
                'angle': angle,
                'size': size,
                'color': color,
                'brightness': brightness,
                'twinkle_speed': random.uniform(1.0, 3.0)  # 闪烁速度
            }

            self.stars.append(star)

    def _animate(self):
        """更新动画帧"""
        if not self.is_running:
            return

        self.elapsed = time.time() - self.start_time
        canvas_height = self.height if not self.show_text else self.height - 25
        center_x = self.width // 2
        center_y = canvas_height // 2

        # 更新黑洞光晕
        if self.has_black_hole and self.black_hole_glow_id:
            glow_size = 15 + 5 * math.sin(self.elapsed * 2)
            self.canvas.coords(
                self.black_hole_glow_id,
                center_x - glow_size, center_y - glow_size,
                center_x + glow_size, center_y + glow_size
            )

            # 更新光晕颜色
            glow_color = self._blend_colors("#0066ff", "#00ffff", (math.sin(self.elapsed) + 1) / 2)
            self.canvas.itemconfig(self.black_hole_glow_id, fill=glow_color)

        # 更新星星
        for star in self.stars:
            # 更新角度 - 距离中心越远，旋转越慢
            angular_velocity = self.rotation_speed * (0.5 + 0.5 * (1 - star['distance'] / (min(self.width, canvas_height) / 2)))
            star['angle'] += angular_velocity * 0.01

            # 计算新位置
            x = center_x + star['distance'] * math.cos(star['angle'])
            y = center_y + star['distance'] * math.sin(star['angle'])

            # 更新星星位置
            self.canvas.coords(
                star['id'],
                x - star['size']/2, y - star['size']/2,
                x + star['size']/2, y + star['size']/2
            )

            # 更新星星亮度 - 闪烁效果
            brightness = 0.5 + 0.5 * math.sin(self.elapsed * star['twinkle_speed'] + star['angle'])
            color = self._adjust_color_brightness(star['color'], brightness)
            self.canvas.itemconfig(star['id'], fill=color)

        # 继续动画 - 使用更高的帧率和优化的计时
        frame_delay = 10  # 约100fps，更流畅
        self.animation_id = self.canvas.after(frame_delay, self._animate)


class MatrixRainAnimation(BaseAdvancedAnimation):
    """数字雨动画 - 类似《黑客帝国》中的数字雨效果"""

    def __init__(self,
                 parent: tk.Widget,
                 width: int = 200,
                 height: int = 200,
                 bg_color: str = None,
                 fg_color: str = "#00ff00",
                 text: str = "加载中...",
                 font: tuple = ("Courier New", 10),
                 show_text: bool = True,
                 drop_speed: float = 1.0,
                 density: float = 1.0,
                 char_set: str = None):
        """
        初始化数字雨动画

        参数:
            parent: 父容器
            width: 动画宽度
            height: 动画高度
            bg_color: 背景颜色
            fg_color: 前景颜色
            text: 显示文本
            font: 文本字体
            show_text: 是否显示文本
            drop_speed: 下落速度
            density: 密度
            char_set: 字符集
        """
        # 确保背景是深色
        if not bg_color:
            bg_color = "#000000"  # 黑色

        super().__init__(parent, width, height, bg_color, fg_color, text, font, show_text)
        self.drop_speed = drop_speed
        self.density = density
        self.char_set = char_set or "01"

        # 数字雨参数
        self.columns = int(self.width / 10)  # 列数
        self.char_height = 14  # 字符高度
        self.drops = []
        self.chars = []

        # 创建数字雨
        self._create_matrix_rain()

    def _create_matrix_rain(self):
        """创建数字雨"""
        canvas_height = self.height if not self.show_text else self.height - 25

        # 清除现有对象
        for char in self.chars:
            self.canvas.delete(char)

        self.chars = []
        self.drops = []

        # 创建雨滴
        for i in range(self.columns):
            # 随机起始位置
            y = random.randint(-canvas_height, 0)

            # 随机长度
            length = random.randint(5, 15)

            # 随机速度
            speed = random.uniform(0.5, 1.5) * self.drop_speed

            # 创建雨滴
            drop = {
                'x': i * 10 + 5,  # 列位置
                'y': y,  # 顶部位置
                'length': length,  # 长度
                'speed': speed,  # 速度
                'chars': []  # 字符ID列表
            }

            # 创建字符
            for j in range(length):
                char_y = y + j * self.char_height
                if char_y >= 0 and char_y < canvas_height:
                    char = random.choice(self.char_set)
                    char_id = self.canvas.create_text(
                        drop['x'], char_y,
                        text=char,
                        fill=self._adjust_color_brightness(self.fg_color, 1.0 - j/length),
                        font=("Courier New", 12)
                    )
                    drop['chars'].append({'id': char_id, 'char': char})
                    self.chars.append(char_id)

            self.drops.append(drop)

    def _animate(self):
        """更新动画帧"""
        if not self.is_running:
            return

        self.elapsed = time.time() - self.start_time
        canvas_height = self.height if not self.show_text else self.height - 25

        # 更新雨滴
        for drop in self.drops:
            # 更新位置
            drop['y'] += drop['speed']

            # 检查是否超出画布
            if drop['y'] - drop['length'] * self.char_height > canvas_height:
                # 重置位置
                drop['y'] = random.randint(-drop['length'] * self.char_height, 0)

                # 清除旧字符
                for char in drop['chars']:
                    self.canvas.delete(char['id'])
                    if char['id'] in self.chars:
                        self.chars.remove(char['id'])

                drop['chars'] = []

                # 随机更新长度和速度
                drop['length'] = random.randint(5, 15)
                drop['speed'] = random.uniform(0.5, 1.5) * self.drop_speed

            # 更新字符
            for j in range(drop['length']):
                char_y = drop['y'] + j * self.char_height

                # 检查是否在可见区域
                if char_y >= 0 and char_y < canvas_height:
                    # 检查是否需要创建新字符
                    if j >= len(drop['chars']):
                        char = random.choice(self.char_set)
                        char_id = self.canvas.create_text(
                            drop['x'], char_y,
                            text=char,
                            fill=self._adjust_color_brightness(self.fg_color, 1.0 - j/drop['length']),
                            font=("Courier New", 12)
                        )
                        drop['chars'].append({'id': char_id, 'char': char})
                        self.chars.append(char_id)
                    else:
                        # 更新现有字符位置
                        self.canvas.coords(drop['chars'][j]['id'], drop['x'], char_y)

                        # 随机更改字符
                        if random.random() < 0.05:  # 5%的概率更改字符
                            new_char = random.choice(self.char_set)
                            self.canvas.itemconfig(drop['chars'][j]['id'], text=new_char)
                            drop['chars'][j]['char'] = new_char

                        # 更新颜色 - 头部字符最亮
                        brightness = 1.0 - j/drop['length']
                        color = self._adjust_color_brightness(self.fg_color, brightness)
                        self.canvas.itemconfig(drop['chars'][j]['id'], fill=color)

            # 移除不可见的字符
            for j in range(len(drop['chars'])-1, -1, -1):
                char_y = drop['y'] + j * self.char_height
                if char_y < 0 or char_y >= canvas_height:
                    self.canvas.delete(drop['chars'][j]['id'])
                    if drop['chars'][j]['id'] in self.chars:
                        self.chars.remove(drop['chars'][j]['id'])
                    drop['chars'].pop(j)

        # 继续动画 - 使用更高的帧率和优化的计时
        frame_delay = 10  # 约100fps，更流畅
        self.animation_id = self.canvas.after(frame_delay, self._animate)


class EnergyPulseAnimation(BaseAdvancedAnimation):
    """能量脉冲动画 - 模拟能量脉冲波扩散的效果"""

    def __init__(self,
                 parent: tk.Widget,
                 width: int = 200,
                 height: int = 200,
                 bg_color: str = None,
                 fg_color: str = "#0d6efd",
                 text: str = "加载中...",
                 font: tuple = ("微软雅黑", 10),
                 show_text: bool = True,
                 pulse_speed: float = 1.0,
                 pulse_count: int = 3,
                 pulse_colors: List[str] = None,
                 glow_effect: bool = True):
        """
        初始化能量脉冲动画

        参数:
            parent: 父容器
            width: 动画宽度
            height: 动画高度
            bg_color: 背景颜色
            fg_color: 前景颜色
            text: 显示文本
            font: 文本字体
            show_text: 是否显示文本
            pulse_speed: 脉冲速度
            pulse_count: 脉冲数量
            pulse_colors: 脉冲颜色列表
            glow_effect: 是否启用发光效果
        """
        super().__init__(parent, width, height, bg_color, fg_color, text, font, show_text)
        self.pulse_speed = pulse_speed
        self.pulse_count = pulse_count
        self.pulse_colors = pulse_colors or ["#0d6efd", "#6610f2", "#6f42c1"]
        self.glow_effect = glow_effect

        # 脉冲参数
        self.pulses = []
        self.core_id = None
        self.glow_id = None

        # 创建脉冲
        self._create_pulses()

    def _create_pulses(self):
        """创建脉冲"""
        canvas_height = self.height if not self.show_text else self.height - 25
        center_x = self.width // 2
        center_y = canvas_height // 2

        # 清除现有对象
        for pulse in self.pulses:
            self.canvas.delete(pulse['id'])
        if self.core_id:
            self.canvas.delete(self.core_id)
        if self.glow_id:
            self.canvas.delete(self.glow_id)

        self.pulses = []

        # 创建核心
        self.core_id = self.canvas.create_oval(
            center_x - 10, center_y - 10,
            center_x + 10, center_y + 10,
            fill=self.fg_color,
            outline=""
        )

        # 创建发光效果
        if self.glow_effect:
            self.glow_id = self.canvas.create_oval(
                center_x - 15, center_y - 15,
                center_x + 15, center_y + 15,
                fill="",
                outline=self.fg_color,
                width=2
            )

        # 创建脉冲
        max_radius = min(self.width, canvas_height) / 2 - 10
        for i in range(self.pulse_count):
            # 计算初始半径
            radius = max_radius * i / self.pulse_count

            # 选择颜色
            color = self.pulse_colors[i % len(self.pulse_colors)]

            # 创建脉冲
            pulse_id = self.canvas.create_oval(
                center_x - radius, center_y - radius,
                center_x + radius, center_y + radius,
                fill="",
                outline=color,
                width=2
            )

            # 保存脉冲信息
            pulse = {
                'id': pulse_id,
                'radius': radius,
                'max_radius': max_radius,
                'color': color,
                'phase': 2 * math.pi * i / self.pulse_count  # 相位差
            }

            self.pulses.append(pulse)

    def _animate(self):
        """更新动画帧"""
        if not self.is_running:
            return

        self.elapsed = time.time() - self.start_time
        canvas_height = self.height if not self.show_text else self.height - 25
        center_x = self.width // 2
        center_y = canvas_height // 2

        # 更新核心
        core_size = 8 + 4 * math.sin(self.elapsed * 3)
        self.canvas.coords(
            self.core_id,
            center_x - core_size, center_y - core_size,
            center_x + core_size, center_y + core_size
        )

        # 更新核心颜色
        core_color = self._blend_colors(
            self.fg_color,
            self.pulse_colors[0],
            (math.sin(self.elapsed * 2) + 1) / 2
        )
        self.canvas.itemconfig(self.core_id, fill=core_color)

        # 更新发光效果
        if self.glow_effect and self.glow_id:
            glow_size = 15 + 10 * math.sin(self.elapsed * 2)
            self.canvas.coords(
                self.glow_id,
                center_x - glow_size, center_y - glow_size,
                center_x + glow_size, center_y + glow_size
            )

            # 更新发光颜色
            glow_color = self._blend_colors(
                self.fg_color,
                self.pulse_colors[0],
                (math.sin(self.elapsed * 3 + 0.5) + 1) / 2
            )
            self.canvas.itemconfig(self.glow_id, outline=glow_color)

        # 更新脉冲
        for pulse in self.pulses:
            # 计算当前半径
            phase_offset = self.elapsed * self.pulse_speed + pulse['phase']
            normalized_radius = (math.sin(phase_offset) + 1) / 2  # 0-1范围
            current_radius = pulse['max_radius'] * normalized_radius

            # 更新脉冲位置
            self.canvas.coords(
                pulse['id'],
                center_x - current_radius, center_y - current_radius,
                center_x + current_radius, center_y + current_radius
            )

            # 更新脉冲宽度和透明度
            width = 3 * (1 - normalized_radius * 0.7)  # 越大越细
            self.canvas.itemconfig(pulse['id'], width=width)

            # 更新脉冲颜色
            color_blend = (math.sin(phase_offset * 2) + 1) / 2
            current_color = self._blend_colors(
                pulse['color'],
                self.fg_color,
                color_blend
            )
            self.canvas.itemconfig(pulse['id'], outline=current_color)

        # 继续动画 - 使用更高的帧率和优化的计时
        frame_delay = 10  # 约100fps，更流畅
        self.animation_id = self.canvas.after(frame_delay, self._animate)


class AudioWaveAnimation(BaseAdvancedAnimation):
    """音频波形动画 - 模拟音频波形的动态效果"""

    def __init__(self,
                 parent: tk.Widget,
                 width: int = 200,
                 height: int = 200,
                 bg_color: str = None,
                 fg_color: str = "#0d6efd",
                 text: str = "加载中...",
                 font: tuple = ("微软雅黑", 10),
                 show_text: bool = True,
                 bar_count: int = 20,
                 wave_speed: float = 1.0,
                 bar_width: int = 4,
                 bar_spacing: int = 2,
                 color_gradient: bool = True,
                 gradient_colors: List[str] = None):
        """
        初始化音频波形动画

        参数:
            parent: 父容器
            width: 动画宽度
            height: 动画高度
            bg_color: 背景颜色
            fg_color: 前景颜色
            text: 显示文本
            font: 文本字体
            show_text: 是否显示文本
            bar_count: 音频条数量
            wave_speed: 波形速度
            bar_width: 音频条宽度
            bar_spacing: 音频条间距
            color_gradient: 是否使用颜色渐变
            gradient_colors: 渐变颜色列表
        """
        super().__init__(parent, width, height, bg_color, fg_color, text, font, show_text)
        self.bar_count = bar_count
        self.wave_speed = wave_speed
        self.bar_width = bar_width
        self.bar_spacing = bar_spacing
        self.color_gradient = color_gradient
        self.gradient_colors = gradient_colors or ["#0d6efd", "#6f42c1", "#d63384", "#dc3545"]

        # 音频条参数
        self.bars = []

        # 创建音频条
        self._create_audio_bars()

    def _create_audio_bars(self):
        """创建音频条"""
        canvas_height = self.height if not self.show_text else self.height - 25
        center_y = canvas_height // 2

        # 计算总宽度
        total_width = self.bar_count * (self.bar_width + self.bar_spacing) - self.bar_spacing
        start_x = (self.width - total_width) // 2

        # 清除现有对象
        for bar in self.bars:
            self.canvas.delete(bar['id'])

        self.bars = []

        # 创建音频条
        for i in range(self.bar_count):
            # 计算位置
            x = start_x + i * (self.bar_width + self.bar_spacing)

            # 初始高度
            height = random.randint(10, int(canvas_height * 0.7))

            # 选择颜色
            if self.color_gradient:
                # 使用渐变色
                color_index = int(i * len(self.gradient_colors) / self.bar_count)
                color = self.gradient_colors[color_index]
            else:
                # 使用单一颜色
                color = self.fg_color

            # 创建音频条
            bar_id = self.canvas.create_rectangle(
                x, center_y - height // 2,
                x + self.bar_width, center_y + height // 2,
                fill=color,
                outline=""
            )

            # 保存音频条信息
            bar = {
                'id': bar_id,
                'x': x,
                'height': height,
                'color': color,
                'phase': random.uniform(0, 2 * math.pi)  # 随机相位
            }

            self.bars.append(bar)

    def _animate(self):
        """更新动画帧"""
        if not self.is_running:
            return

        self.elapsed = time.time() - self.start_time
        canvas_height = self.height if not self.show_text else self.height - 25
        center_y = canvas_height // 2

        # 更新音频条
        for i, bar in enumerate(self.bars):
            # 计算当前高度
            phase = self.elapsed * self.wave_speed * 3 + bar['phase'] + i * 0.2
            height_factor = 0.3 + 0.7 * (0.5 + 0.5 * math.sin(phase))
            current_height = int(canvas_height * 0.7 * height_factor)

            # 更新音频条位置
            self.canvas.coords(
                bar['id'],
                bar['x'], center_y - current_height // 2,
                bar['x'] + self.bar_width, center_y + current_height // 2
            )

            # 更新颜色
            if self.color_gradient:
                # 使用渐变色 - 添加脉动效果
                color_index = int(i * len(self.gradient_colors) / self.bar_count)
                base_color = self.gradient_colors[color_index]
                next_color = self.gradient_colors[(color_index + 1) % len(self.gradient_colors)]

                blend_factor = (math.sin(phase * 0.5) + 1) / 2
                current_color = self._blend_colors(base_color, next_color, blend_factor)

                self.canvas.itemconfig(bar['id'], fill=current_color)

        # 继续动画 - 使用更高的帧率和优化的计时
        frame_delay = 10  # 约100fps，更流畅
        self.animation_id = self.canvas.after(frame_delay, self._animate)