"""
多平台存稿工具启动脚本 - 带加载动画
支持网易号和今日头条平台
使用左侧导航栏进行平台切换
"""

import os
import sys
import tkinter as tk
from tkinter import messagebox, simpledialog
import traceback
import threading
import time
import subprocess
import datetime
from tkinter import ttk 

# 全局变量，用于存储动画类
DNAHelixAnimation = None
GalaxyAnimation = None
MatrixRainAnimation = None
EnergyPulseAnimation = None
AudioWaveAnimation = None
ParticleFlowAnimation = None
LiquidWaveAnimation = None
NeonGlowAnimation = None
GeometricMorphAnimation = None
TypewriterAnimation = None
SpinnerAnimation = None
PulseAnimation = None
ProgressBarAnimation = None
TextFadeAnimation = None
BouncingDotsAnimation = None

# 获取当前脚本所在目录
current_dir = os.path.dirname(os.path.abspath(__file__))

# 添加当前目录到Python路径，使网易号存稿成为顶级包
sys.path.insert(0, current_dir)

# 导入加载动画模块
try:
    # 尝试导入所有动画模块
    # 1. 高级创意动画
    from 网易号存稿.ui.components.advanced_animations import (
        DNAHelixAnimation,
        GalaxyAnimation,
        MatrixRainAnimation,
        EnergyPulseAnimation,
        AudioWaveAnimation
    )

    # 2. 创意动画
    from 网易号存稿.ui.components.advanced_animations import (
    DNAHelixAnimation,
    GalaxyAnimation,
    MatrixRainAnimation,
    EnergyPulseAnimation,
    AudioWaveAnimation
)

    # 3. 基础动画
    from 网易号存稿.ui.components.advanced_animations import (
    DNAHelixAnimation,
    GalaxyAnimation,
    MatrixRainAnimation,
    EnergyPulseAnimation,
    AudioWaveAnimation
)

except ImportError as e:
    print(f"标准导入路径失败: {e}")

    # 添加额外的导入路径
    sys.path.append(os.path.join(current_dir, "网易号存稿"))

    try:
        # 再次尝试导入所有动画模块
        # 1. 高级创意动画
        from 网易号存稿.ui.components.advanced_animations import (
            DNAHelixAnimation,
            GalaxyAnimation,
            MatrixRainAnimation,
            EnergyPulseAnimation,   
            AudioWaveAnimation
        )

        # 2. 创意动画
        from 网易号存稿.ui.components.advanced_animations import (
    DNAHelixAnimation,
    GalaxyAnimation,
    MatrixRainAnimation,
    EnergyPulseAnimation,
    AudioWaveAnimation
)

        # 3. 基础动画
        from 网易号存稿.ui.components.advanced_animations import (
    DNAHelixAnimation,
    GalaxyAnimation,
    MatrixRainAnimation,
    EnergyPulseAnimation,
    AudioWaveAnimation
)

    except ImportError as e2:
        print(f"扩展导入路径后仍然失败: {e2}")
        print("将使用动态导入方式...")

        # 使用动态导入
        import importlib.util

        # 定义所有需要导入的模块和类
        modules_to_import = {
            "advanced_animations": [
                "DNAHelixAnimation",
                "GalaxyAnimation",
                "MatrixRainAnimation",
                "EnergyPulseAnimation",
                "AudioWaveAnimation"
            ],
            "creative_animations": [
                "ParticleFlowAnimation",
                "LiquidWaveAnimation",
                "NeonGlowAnimation",
                "GeometricMorphAnimation",
                "TypewriterAnimation"
            ],
            "loading_animations": [
                "SpinnerAnimation",
                "PulseAnimation",
                "ProgressBarAnimation",
                "TextFadeAnimation",
                "BouncingDotsAnimation"
            ]
        }

        # 动态导入所有模块
        for module_name, class_names in modules_to_import.items():
            module_path = os.path.join(current_dir, "网易号存稿", "ui", "components", f"{module_name}.py")

            # 如果文件不存在，尝试查找
            if not os.path.exists(module_path):
                print(f"模块文件不存在: {module_path}")
                for root, dirs, files in os.walk(current_dir):
                    if f"{module_name}.py" in files:
                        module_path = os.path.join(root, f"{module_name}.py")
                        print(f"找到模块文件: {module_path}")
                        break

            # 如果找到了文件，导入它
            if os.path.exists(module_path):
                spec = importlib.util.spec_from_file_location(module_name, module_path)
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)

                # 从模块中获取所有类
                for class_name in class_names:
                    if hasattr(module, class_name):
                        globals()[class_name] = getattr(module, class_name)
                    else:
                        print(f"警告: 在模块 {module_name} 中找不到类 {class_name}")
            else:
                print(f"错误: 无法找到模块 {module_name}")

        # 检查是否成功导入了必要的类
        required_classes = ["DNAHelixAnimation", "ParticleFlowAnimation", "LiquidWaveAnimation", "SpinnerAnimation"]
        for cls in required_classes:
            if cls not in globals():
                print(f"错误: 无法导入必要的类 {cls}")
                sys.exit(1)

class SplashScreen:
    """启动画面类"""

    def __init__(self, animation_type="dna"):
        """
        初始化启动画面

        参数:
            animation_type: 动画类型，可选值为:
                - 高级创意动画: "dna", "galaxy", "matrix", "energy", "audio"
                - 创意动画: "particle", "liquid", "neon", "geometric", "typewriter"
                - 基础动画: "spinner", "pulse", "progress", "text", "bouncing"
        """
        # 定时任务已移至主界面
        # 创建根窗口
        self.root = tk.Tk()
        self.root.title("多平台存稿工具")
        self.root.configure(bg="#212529")  # 深色背景
        self.root.overrideredirect(True)  # 无边框窗口

        # 设置窗口大小并居中显示
        window_width = 400
        window_height = 300
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # 创建主框架
        self.main_frame = tk.Frame(self.root, bg="#212529")
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建标题
        tk.Label(
            self.main_frame,
            text="多平台存稿工具",
            font=("微软雅黑", 18, "bold"),
            fg="#ffffff",
            bg="#212529"
        ).pack(pady=(40, 20))

        # 创建动画容器
        self.animation_container = tk.Frame(self.main_frame, bg="#212529")
        self.animation_container.pack(fill=tk.BOTH, expand=True)

        # 创建状态标签
        self.status_label = tk.Label(
            self.main_frame,
            text="正在加载应用程序...",
            font=("微软雅黑", 10),
            fg="#adb5bd",
            bg="#212529"
        )
        self.status_label.pack(pady=(10, 20))

        # 定时任务已移至主界面

        # 创建加载动画
        self.create_animation(animation_type)

        # 应用程序加载标志
        self.app_loaded = False

        # 显示窗口
        self.root.update()



    def create_animation(self, animation_type):
        """创建加载动画"""
        # 清空容器
        for widget in self.animation_container.winfo_children():
            widget.destroy()

        # 根据类型创建动画
        if animation_type == "dna":
            self.animation = DNAHelixAnimation(
                self.animation_container,
                width=200,
                height=120,
                bg_color="#212529",
                fg_color="#0d6efd",
                text="",
                show_text=False,
                rotation_speed=1.2,
                helix_width=100,
                helix_height=100,
                strand_colors=["#0d6efd", "#dc3545"],
                base_pair_colors=["#fd7e14", "#20c997"]
            )
        elif animation_type == "galaxy":
            self.animation = GalaxyAnimation(
                self.animation_container,
                width=200,
                height=120,
                bg_color="#212529",
                fg_color="#6f42c1",
                text="",
                show_text=False,
                rotation_speed=0.5,
                star_count=150,
                galaxy_colors=["#ffffff", "#00ffff", "#ff00ff", "#ffff00", "#ff8800"],
                has_black_hole=True
            )
        elif animation_type == "matrix":
            self.animation = MatrixRainAnimation(
                self.animation_container,
                width=200,
                height=120,
                bg_color="#212529",
                fg_color="#00ff00",
                text="",
                show_text=False,
                drop_speed=1.2,
                density=1.0,
                char_set="01"
            )
        elif animation_type == "energy":
            self.animation = EnergyPulseAnimation(
                self.animation_container,
                width=200,
                height=120,
                bg_color="#212529",
                fg_color="#0dcaf0",
                text="",
                show_text=False,
                pulse_speed=1.0,
                pulse_count=3,
                pulse_colors=["#0dcaf0", "#6610f2", "#d63384"],
                glow_effect=True
            )
        elif animation_type == "audio":
            self.animation = AudioWaveAnimation(
                self.animation_container,
                width=200,
                height=120,
                bg_color="#212529",
                fg_color="#fd7e14",
                text="",
                show_text=False,
                bar_count=15,
                wave_speed=1.2,
                bar_width=6,
                bar_spacing=3,
                color_gradient=True,
                gradient_colors=["#0d6efd", "#6f42c1", "#d63384", "#dc3545"]
            )
        # 创意动画
        elif animation_type == "liquid":
            self.animation = LiquidWaveAnimation(
                self.animation_container,
                width=200,
                height=120,
                bg_color="#212529",
                fg_color="#6f42c1",
                text="",
                show_text=False,
                wave_count=3,
                wave_height=12,
                wave_speed=1.5,
                fill_level=0.6,
                color_gradient=True
            )
        elif animation_type == "particle":
            self.animation = ParticleFlowAnimation(
                self.animation_container,
                width=200,
                height=120,
                bg_color="#212529",
                fg_color="#0d6efd",
                text="",
                show_text=False,
                particle_count=60,
                flow_speed=1.2
            )
        elif animation_type == "neon":
            self.animation = NeonGlowAnimation(
                self.animation_container,
                width=200,
                height=120,
                bg_color="#212529",
                fg_color="#dc3545",
                text="",
                show_text=False,
                glow_width=4,
                glow_intensity=1.2,
                rotation_speed=0.5,
                shape="star"
            )
        # 基础动画
        elif animation_type == "spinner":
            self.animation = SpinnerAnimation(
                self.animation_container,
                width=200,
                height=120,
                bg_color="#212529",
                fg_color="#0d6efd",
                text="",
                show_text=False
            )
        else:
            # 默认使用DNA螺旋动画
            self.animation = DNAHelixAnimation(
                self.animation_container,
                width=200,
                height=120,
                bg_color="#212529",
                fg_color="#0d6efd",
                text="",
                show_text=False,
                rotation_speed=1.2,
                helix_width=100,
                helix_height=100,
                strand_colors=["#0d6efd", "#dc3545"],
                base_pair_colors=["#fd7e14", "#20c997"]
            )

        # 显示动画
        self.animation.pack(fill=tk.BOTH, expand=True)

        # 设置更高的优先级
        self.root.attributes("-topmost", True)
        self.root.update()
        self.root.attributes("-topmost", False)

        # 启动动画
        self.animation.start()

    def update_status(self, text):
        """更新状态文本"""
        if self.status_label.cget("text") != text:
            self.status_label.config(text=text)
            # 使用update_idletasks而不是update，减少阻塞
            self.root.update_idletasks()

    # 定时任务已移至主界面

    def close(self):
        """关闭启动画面"""
        # 停止动画
        if hasattr(self, 'animation'):
            self.animation.stop()

        # 销毁窗口
        self.root.destroy()

def launch_video_processor(auto_mode=False, status_file=None):
    """启动视频处理工具

    Args:
        auto_mode (bool): 是否启用自动模式
        status_file (str): 状态文件路径（可选）
    """
    try:
        # 检查是否在打包环境中
        if getattr(sys, 'frozen', False):
            # 在打包环境中，使用exe文件
            video_processor_exe = os.path.join(current_dir, "_internal", "视频处理工具", "视频处理工具.exe")

            if os.path.exists(video_processor_exe):
                # 构建命令参数
                cmd = [video_processor_exe]

                # 添加自动模式参数
                if auto_mode:
                    cmd.append("--auto-mode")
                    print("🚀 启动视频处理工具（自动模式）")
                else:
                    print("🚀 启动视频处理工具（手动模式）")

                # 添加状态文件参数
                if status_file:
                    cmd.extend(["--status-file", status_file])
                    print(f"📊 状态文件: {status_file}")

                print(f"执行命令: {' '.join(cmd)}")

                # 启动视频处理工具exe
                process = subprocess.Popen(cmd, creationflags=subprocess.CREATE_NEW_CONSOLE)
                print(f"✅ 视频处理工具已启动，进程ID: {process.pid}")
                return True
            else:
                print(f"❌ 视频处理工具exe不存在: {video_processor_exe}")
                messagebox.showerror("错误", "找不到视频处理工具exe文件")
                return False
        else:
            # 在开发环境中，使用Python脚本
            video_processor_path = os.path.join(current_dir, "视频处理工具", "main.py")

            # 检查文件是否存在
            if not os.path.exists(video_processor_path):
                # 尝试查找备用文件
                video_processor_path = os.path.join(current_dir, "视频处理工具", "视频处理工具.py")
                if not os.path.exists(video_processor_path):
                    messagebox.showerror("错误", "找不到视频处理工具文件")
                    return False

            # 构建命令参数
            python_exe = sys.executable
            cmd = [python_exe, video_processor_path]

            # 添加自动模式参数
            if auto_mode:
                cmd.append("--auto-mode")
                print("🚀 启动视频处理工具（自动模式）")
            else:
                print("🚀 启动视频处理工具（手动模式）")

            # 添加状态文件参数
            if status_file:
                cmd.extend(["--status-file", status_file])
                print(f"📊 状态文件: {status_file}")

            print(f"执行命令: {' '.join(cmd)}")

            # 启动视频处理工具
            process = subprocess.Popen(cmd, creationflags=subprocess.CREATE_NEW_CONSOLE)
            print(f"✅ 视频处理工具已启动，进程ID: {process.pid}")
            return True

    except Exception as e:
        error_msg = f"启动视频处理工具时出错: {str(e)}"
        print(f"❌ {error_msg}")
        messagebox.showerror("启动错误", error_msg)
        return False

def open_video_downloader():
    """打开视频下载工具对话框"""
    try:
        # 导入视频下载工具对话框和配置管理器
        sys.path.insert(0, current_dir)
        from 网易号存稿.ui.dialogs.video_downloader_dialog import VideoDownloaderDialog
        from 网易号存稿.common.config import ConfigManager

        # 创建临时根窗口（如果需要）
        if 'root' not in globals():
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
        else:
            root = globals()['root']

        # 创建配置管理器
        config_file = os.path.join(current_dir, "config.json")
        config_manager = ConfigManager(config_file, base_dir=current_dir)

        # 创建对话框，传递配置管理器
        video_downloader_dialog = VideoDownloaderDialog(root, config_manager)

        # 等待对话框关闭
        root.wait_window(video_downloader_dialog.dialog)

    except Exception as e:
        messagebox.showerror("打开错误", f"打开视频下载工具对话框失败: {str(e)}")

def open_daily_hot():
    """打开每日爆文工具对话框"""
    try:
        # 导入每日爆文工具对话框和配置管理器
        sys.path.insert(0, current_dir)
        from 网易号存稿.ui.dialogs.daily_hot_dialog import DailyHotDialog
        from 网易号存稿.common.config import ConfigManager

        # 创建临时根窗口（如果需要）
        if 'root' not in globals():
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
        else:
            root = globals()['root']

        # 创建配置管理器
        config_file = os.path.join(current_dir, "config.json")
        config_manager = ConfigManager(config_file, base_dir=current_dir)

        # 创建对话框，传递配置管理器
        daily_hot_dialog = DailyHotDialog(root, config_manager)

        # 等待对话框关闭
        root.wait_window(daily_hot_dialog.dialog)

    except Exception as e:
        messagebox.showerror("打开错误", f"打开每日爆文工具对话框失败: {str(e)}")

def open_cache_cleaner():
    """打开缓存清理工具"""
    try:
        # 导入缓存清理工具
        sys.path.insert(0, current_dir)
        from 网易号存稿.tools.cache_cleaner import CacheCleanerGUI

        # 创建临时根窗口（如果需要）
        if 'root' not in globals():
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
        else:
            root = globals()['root']

        # 创建缓存清理工具窗口
        try:
            # 尝试创建子窗口
            cache_window = tk.Toplevel(root)
        except:
            # 如果失败，创建独立窗口
            cache_window = tk.Tk()

        cache_window.title("🧹 缓存清理工具")
        cache_window.geometry("800x800")
        cache_window.minsize(700, 700)

        # 设置窗口图标（如果有的话）
        try:
            if hasattr(root, 'iconbitmap'):
                cache_window.iconbitmap(root.iconbitmap())
        except:
            pass

        # 创建缓存清理器GUI
        cache_cleaner_gui = CacheCleanerGUI(cache_window)

        # 居中显示窗口
        _center_window(cache_window)

        print("✅ 缓存清理工具已打开")

    except Exception as e:
        messagebox.showerror("打开错误", f"打开缓存清理工具失败: {str(e)}")
        print(f"❌ 打开缓存清理工具失败: {str(e)}")
        import traceback
        traceback.print_exc()

def _center_window(window):
    """将窗口居中显示"""
    try:
        window.update_idletasks()
        width = window.winfo_width()
        height = window.winfo_height()
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        window.geometry(f"+{x}+{y}")
    except Exception as e:
        print(f"窗口居中失败: {str(e)}")

def start_all_accounts_draft():
    """启动所有账号的存稿任务"""
    try:
        # 导入main模块
        from 网易号存稿 import main as main_module

        # 获取主模块中的UI实例
        if hasattr(main_module, 'app') and main_module.app:
            # 调用UI实例的start_task方法，传入"all"表示处理所有账号
            main_module.app.start_task("all")
            return True
        else:
            print("未找到主应用程序实例，无法启动存稿任务")
            return False
    except Exception as e:
        print(f"启动存稿任务时出错: {str(e)}")
        return False

def load_application():
    """在后台线程中模拟加载应用程序"""
    # 设置全局变量，表示应用程序已加载
    global app_result, app_error

    # 模拟加载过程
    time.sleep(3)  # 模拟加载时间

    # 设置结果
    app_result = 0  # 设置为0表示准备好启动
    app_error = None

# 运行主函数
if __name__ == "__main__":
    # 创建全局变量
    app_result = None
    app_error = None

    # 选择动画类型 - 可以修改为 "dna", "galaxy", "matrix", "energy", "audio", "particle", "liquid", "neon", "spinner"
    animation_type = "audio"

    # 创建启动画面
    splash = SplashScreen(animation_type)

    # 启动后台线程加载应用程序
    loading_thread = threading.Thread(target=load_application, daemon=True)
    loading_thread.start()

    # 定时任务已移至主界面

    # 更新状态消息
    status_messages = [
        "正在初始化应用程序...",
        "正在加载配置...",
        "正在准备用户界面...",
        "正在连接服务...",
        "即将完成..."
    ]

    # 主循环
    try:
        i = 0
        last_update_time = time.time()
        update_interval = 0.5  # 状态消息更新间隔（秒）

        # 使用更高效的事件循环
        while loading_thread.is_alive() and app_result is None:
            # 处理所有待处理的事件
            splash.root.update()

            # 只在指定间隔更新状态消息
            current_time = time.time()
            if current_time - last_update_time >= update_interval:
                splash.update_status(status_messages[i % len(status_messages)])
                i += 1
                last_update_time = current_time

            # 短暂休眠，减少CPU使用
            time.sleep(0.01)

        # 等待线程完成
        loading_thread.join(0.5)

        # 关闭启动画面
        splash.close()

        # 检查是否有错误
        if app_error:
            # 显示错误消息框
            messagebox.showerror("程序启动错误", f"发生错误: {app_error}")
            sys.exit(1)

        # 在主线程中启动应用程序
        try:
            # 导入main模块
            # 导入main模块
            from 网易号存稿 import main as main_module

            # 运行主应用程序
            result = main_module.main()
            sys.exit(result or 0)
        except Exception as e:
            print(f"程序启动错误: {e}")
            traceback.print_exc()
            messagebox.showerror("程序启动错误", f"发生错误: {e}")
            sys.exit(1)

    except Exception as e:
        # 确保启动画面关闭
        try:
            splash.close()
        except:
            pass

        print(f"启动画面错误: {e}")
        traceback.print_exc()

        # 显示错误消息框
        try:
            messagebox.showerror("程序启动错误", f"发生错误: {e}")
        except:
            pass

        sys.exit(1)
