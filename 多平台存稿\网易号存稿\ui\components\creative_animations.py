"""
创意加载动画组件 - 提供更具创意的加载动画效果
"""

import tkinter as tk
import time
import math
import random
import string
from typing import Optional, Callable, List, Dict, Any

class BaseCreativeAnimation:
    """创意加载动画基类"""

    def __init__(self,
                 parent: tk.Widget,
                 width: int = 200,
                 height: int = 200,
                 bg_color: str = None,
                 fg_color: str = "#0d6efd",
                 text: str = "加载中...",
                 font: tuple = ("微软雅黑", 10),
                 show_text: bool = True):
        """
        初始化加载动画

        参数:
            parent: 父容器
            width: 动画宽度
            height: 动画高度
            bg_color: 背景颜色 (None表示使用父容器背景)
            fg_color: 前景颜色 (动画主色)
            text: 显示文本
            font: 文本字体
            show_text: 是否显示文本
        """
        self.parent = parent
        self.width = width
        self.height = height
        self.bg_color = bg_color or parent.cget("background")
        self.fg_color = fg_color
        self.text = text
        self.font = font
        self.show_text = show_text

        # 创建容器框架
        self.container = tk.Frame(parent, width=width, height=height, bg=self.bg_color)

        # 创建画布
        self.canvas = tk.Canvas(
            self.container,
            width=width,
            height=height if not show_text else height - 25,
            bg=self.bg_color,
            highlightthickness=0
        )
        self.canvas.pack(pady=(0, 10 if show_text else 0))

        # 创建文本标签
        if show_text:
            self.text_label = tk.Label(
                self.container,
                text=text,
                font=font,
                bg=self.bg_color,
                fg=self.fg_color
            )
            self.text_label.pack(pady=(0, 5))

        # 动画状态
        self.is_running = False
        self.animation_id = None
        self.start_time = None

    def start(self):
        """开始动画"""
        if self.is_running:
            return

        self.is_running = True
        self.start_time = time.time()
        self._animate()
        return self

    def stop(self):
        """停止动画"""
        if self.animation_id:
            self.canvas.after_cancel(self.animation_id)
            self.animation_id = None
        self.is_running = False
        return self

    def _animate(self):
        """动画帧更新方法，由子类实现具体动画效果"""
        pass

    def pack(self, **kwargs):
        """打包容器"""
        self.container.pack(**kwargs)
        return self

    def grid(self, **kwargs):
        """网格布局容器"""
        self.container.grid(**kwargs)
        return self

    def place(self, **kwargs):
        """位置布局容器"""
        self.container.place(**kwargs)
        return self

    def destroy(self):
        """销毁动画"""
        self.stop()
        self.container.destroy()

    def _hex_to_rgb(self, hex_color):
        """将十六进制颜色转换为RGB元组"""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

    def _rgb_to_hex(self, rgb):
        """将RGB元组转换为十六进制颜色"""
        return f"#{rgb[0]:02x}{rgb[1]:02x}{rgb[2]:02x}"

    def _get_color_with_alpha(self, color, alpha):
        """获取带透明度的颜色"""
        rgb = self._hex_to_rgb(color)
        return f"#{rgb[0]:02x}{rgb[1]:02x}{rgb[2]:02x}{int(alpha*255):02x}"


class ParticleFlowAnimation(BaseCreativeAnimation):
    """粒子流动画 - 模拟粒子在空间中流动的效果"""

    def __init__(self,
                 parent: tk.Widget,
                 width: int = 200,
                 height: int = 200,
                 bg_color: str = None,
                 fg_color: str = "#0d6efd",
                 text: str = "加载中...",
                 font: tuple = ("微软雅黑", 10),
                 show_text: bool = True,
                 particle_count: int = 50,
                 flow_speed: float = 1.0,
                 particle_size_range: tuple = (2, 5),
                 color_scheme: List[str] = None):
        """
        初始化粒子流动画

        参数:
            parent: 父容器
            width: 动画宽度
            height: 动画高度
            bg_color: 背景颜色
            fg_color: 前景颜色
            text: 显示文本
            font: 文本字体
            show_text: 是否显示文本
            particle_count: 粒子数量
            flow_speed: 流动速度
            particle_size_range: 粒子大小范围 (最小值, 最大值)
            color_scheme: 颜色方案列表
        """
        super().__init__(parent, width, height, bg_color, fg_color, text, font, show_text)
        self.particle_count = particle_count
        self.flow_speed = flow_speed
        self.particle_size_range = particle_size_range
        self.color_scheme = color_scheme or [fg_color, "#6610f2", "#6f42c1", "#d63384"]

        # 创建粒子
        self.particles = []
        self._create_particles()

    def _create_particles(self):
        """创建粒子"""
        canvas_height = self.height if not self.show_text else self.height - 25
        center_x = self.width // 2
        center_y = canvas_height // 2

        # 清除现有粒子
        for particle in self.particles:
            if 'id' in particle:
                self.canvas.delete(particle['id'])

        self.particles = []

        # 创建新粒子
        for _ in range(self.particle_count):
            # 随机位置 - 在中心区域周围分布
            angle = random.uniform(0, 2 * math.pi)
            distance = random.uniform(20, 80)
            x = center_x + distance * math.cos(angle)
            y = center_y + distance * math.sin(angle)

            # 随机大小
            size = random.uniform(self.particle_size_range[0], self.particle_size_range[1])

            # 随机颜色
            color = random.choice(self.color_scheme)

            # 随机速度和方向
            speed = random.uniform(0.5, 1.5) * self.flow_speed
            direction = random.uniform(0, 2 * math.pi)

            # 创建粒子对象
            particle = {
                'x': x,
                'y': y,
                'size': size,
                'color': color,
                'speed': speed,
                'direction': direction,
                'phase': random.uniform(0, 2 * math.pi),  # 用于脉动效果
                'id': None
            }

            # 绘制粒子
            particle_id = self.canvas.create_oval(
                x - size,
                y - size,
                x + size,
                y + size,
                fill=color,
                outline=""
            )
            particle['id'] = particle_id

            self.particles.append(particle)

    def _animate(self):
        """更新动画帧"""
        if not self.is_running:
            return

        elapsed = time.time() - self.start_time
        canvas_height = self.height if not self.show_text else self.height - 25
        center_x = self.width // 2
        center_y = canvas_height // 2

        # 更新每个粒子
        for particle in self.particles:
            # 更新位置 - 围绕中心点移动
            angle = elapsed * particle['speed'] + particle['direction']
            distance = 30 + 20 * math.sin(elapsed * 0.5 + particle['phase'])

            particle['x'] = center_x + distance * math.cos(angle)
            particle['y'] = center_y + distance * math.sin(angle)

            # 更新大小 - 脉动效果
            size_factor = 0.8 + 0.4 * math.sin(elapsed * 2 + particle['phase'])
            current_size = particle['size'] * size_factor

            # 更新粒子位置和大小
            self.canvas.coords(
                particle['id'],
                particle['x'] - current_size,
                particle['y'] - current_size,
                particle['x'] + current_size,
                particle['y'] + current_size
            )

            # 更新颜色 - 轻微变化
            r, g, b = self._hex_to_rgb(particle['color'])
            color_shift = math.sin(elapsed * 3 + particle['phase']) * 20
            r = max(0, min(255, r + int(color_shift)))
            g = max(0, min(255, g + int(color_shift)))
            b = max(0, min(255, b + int(color_shift)))

            current_color = f"#{r:02x}{g:02x}{b:02x}"
            self.canvas.itemconfig(particle['id'], fill=current_color)

        # 继续动画
        self.animation_id = self.canvas.after(16, self._animate)  # ~60fps


class LiquidWaveAnimation(BaseCreativeAnimation):
    """液体波浪动画 - 模拟液体波浪流动的效果"""

    def __init__(self,
                 parent: tk.Widget,
                 width: int = 200,
                 height: int = 200,
                 bg_color: str = None,
                 fg_color: str = "#0d6efd",
                 text: str = "加载中...",
                 font: tuple = ("微软雅黑", 10),
                 show_text: bool = True,
                 wave_count: int = 3,
                 wave_height: int = 10,
                 wave_speed: float = 1.0,
                 fill_level: float = 0.6,  # 0.0-1.0
                 color_gradient: bool = True):
        """
        初始化液体波浪动画

        参数:
            parent: 父容器
            width: 动画宽度
            height: 动画高度
            bg_color: 背景颜色
            fg_color: 前景颜色
            text: 显示文本
            font: 文本字体
            show_text: 是否显示文本
            wave_count: 波浪数量
            wave_height: 波浪高度
            wave_speed: 波浪速度
            fill_level: 填充水平 (0.0-1.0)
            color_gradient: 是否使用颜色渐变
        """
        super().__init__(parent, width, height, bg_color, fg_color, text, font, show_text)
        self.wave_count = wave_count
        self.wave_height = wave_height
        self.wave_speed = wave_speed
        self.fill_level = fill_level
        self.color_gradient = color_gradient

        # 波浪参数
        self.waves = []
        self.wave_points = 100  # 波浪曲线的点数

        # 创建波浪
        for i in range(wave_count):
            wave = {
                'amplitude': wave_height * (1.0 - 0.2 * i),  # 每个波浪振幅略有不同
                'frequency': 0.05 * (1.0 + 0.3 * i),  # 每个波浪频率略有不同
                'phase': 2 * math.pi * i / wave_count,  # 相位差
                'speed': wave_speed * (1.0 - 0.1 * i),  # 每个波浪速度略有不同
                'id': None,
                'fill_id': None
            }
            self.waves.append(wave)

        # 创建波浪多边形
        canvas_height = self.height if not self.show_text else self.height - 25
        base_y = canvas_height * (1.0 - self.fill_level)

        for i, wave in enumerate(self.waves):
            # 创建波浪线
            points = []
            for x in range(0, self.width + 1, self.width // self.wave_points):
                points.extend([x, base_y])

            wave_id = self.canvas.create_line(
                points,
                fill=self.fg_color,
                width=2,
                smooth=True
            )
            wave['id'] = wave_id

            # 创建填充区域
            fill_points = [0, canvas_height, 0, base_y]
            fill_points.extend(points)
            fill_points.extend([self.width, base_y, self.width, canvas_height])

            # 计算填充颜色 - 半透明
            r, g, b = self._hex_to_rgb(self.fg_color)
            opacity = 0.3 - 0.1 * i  # 每个波浪透明度不同
            fill_color = f"#{r:02x}{g:02x}{b:02x}"

            fill_id = self.canvas.create_polygon(
                fill_points,
                fill=fill_color,
                outline="",
                smooth=True
            )
            wave['fill_id'] = fill_id

            # 确保填充区域在波浪线下方
            self.canvas.tag_lower(fill_id)

    def _animate(self):
        """更新动画帧"""
        if not self.is_running:
            return

        elapsed = time.time() - self.start_time
        canvas_height = self.height if not self.show_text else self.height - 25
        base_y = canvas_height * (1.0 - self.fill_level)

        # 更新每个波浪
        for wave in self.waves:
            # 计算波浪点
            points = []
            fill_points = [0, canvas_height, 0, base_y]

            for x in range(0, self.width + 1, self.width // self.wave_points):
                # 计算y坐标 - 正弦波
                y = base_y - wave['amplitude'] * math.sin(
                    wave['frequency'] * x + elapsed * wave['speed'] + wave['phase']
                )
                points.extend([x, y])
                fill_points.extend([x, y])

            fill_points.extend([self.width, base_y, self.width, canvas_height])

            # 更新波浪线
            self.canvas.coords(wave['id'], points)

            # 更新填充区域
            self.canvas.coords(wave['fill_id'], fill_points)

            # 更新颜色 - 如果启用颜色渐变
            if self.color_gradient:
                r, g, b = self._hex_to_rgb(self.fg_color)

                # 波浪线颜色 - 轻微变化
                line_shift = math.sin(elapsed + wave['phase']) * 30
                line_r = max(0, min(255, r + int(line_shift)))
                line_g = max(0, min(255, g + int(line_shift)))
                line_b = max(0, min(255, b + int(line_shift)))

                line_color = f"#{line_r:02x}{line_g:02x}{line_b:02x}"
                self.canvas.itemconfig(wave['id'], fill=line_color)

                # 填充区域颜色 - 半透明
                fill_r = max(0, min(255, r + int(line_shift * 0.7)))
                fill_g = max(0, min(255, g + int(line_shift * 0.7)))
                fill_b = max(0, min(255, b + int(line_shift * 0.7)))

                fill_color = f"#{fill_r:02x}{fill_g:02x}{fill_b:02x}"
                self.canvas.itemconfig(wave['fill_id'], fill=fill_color)

        # 继续动画
        self.animation_id = self.canvas.after(16, self._animate)  # ~60fps


class NeonGlowAnimation(BaseCreativeAnimation):
    """霓虹灯光效果 - 带有霓虹灯光效果的加载动画"""

    def __init__(self,
                 parent: tk.Widget,
                 width: int = 200,
                 height: int = 200,
                 bg_color: str = None,
                 fg_color: str = "#0d6efd",
                 text: str = "加载中...",
                 font: tuple = ("微软雅黑", 10),
                 show_text: bool = True,
                 glow_width: int = 4,
                 glow_intensity: float = 1.0,
                 rotation_speed: float = 1.0,
                 shape: str = "circle"):  # circle, square, triangle, star
        """
        初始化霓虹灯光效果动画

        参数:
            parent: 父容器
            width: 动画宽度
            height: 动画高度
            bg_color: 背景颜色
            fg_color: 前景颜色
            text: 显示文本
            font: 文本字体
            show_text: 是否显示文本
            glow_width: 发光宽度
            glow_intensity: 发光强度
            rotation_speed: 旋转速度
            shape: 形状 ("circle", "square", "triangle", "star")
        """
        super().__init__(parent, width, height, bg_color, fg_color, text, font, show_text)
        self.glow_width = glow_width
        self.glow_intensity = glow_intensity
        self.rotation_speed = rotation_speed
        self.shape = shape

        # 霓虹灯颜色
        self.neon_colors = ["#ff00ff", "#00ffff", "#ffff00", "#ff0000", "#00ff00", "#0000ff"]

        # 创建霓虹灯对象
        self.neon_objects = []
        self._create_neon_objects()

    def _create_neon_objects(self):
        """创建霓虹灯对象"""
        canvas_height = self.height if not self.show_text else self.height - 25
        center_x = self.width // 2
        center_y = canvas_height // 2

        # 清除现有对象
        for obj in self.neon_objects:
            for layer_id in obj['layers']:
                self.canvas.delete(layer_id)

        self.neon_objects = []

        # 创建主要形状
        radius = min(self.width, canvas_height) // 3

        # 创建多层霓虹灯效果
        layers = 5  # 发光层数

        # 为每个颜色创建一个霓虹灯对象
        for i, color in enumerate(self.neon_colors):
            angle_offset = 2 * math.pi * i / len(self.neon_colors)

            # 创建多层发光效果
            layer_ids = []
            for layer in range(layers):
                # 计算当前层的参数
                layer_radius = radius - layer * (self.glow_width // 2)
                if layer_radius <= 0:
                    continue

                # 计算发光强度
                glow_factor = 1.0 - layer / layers

                # 创建形状
                shape_id = self._create_shape(
                    center_x, center_y, layer_radius,
                    color, glow_factor, angle_offset
                )

                layer_ids.append(shape_id)

            # 创建霓虹灯对象
            neon_obj = {
                'color': color,
                'radius': radius,
                'angle_offset': angle_offset,
                'layers': layer_ids
            }

            self.neon_objects.append(neon_obj)

    def _create_shape(self, x, y, radius, color, glow_factor, angle_offset=0):
        """创建形状"""
        # 计算发光颜色
        r, g, b = self._hex_to_rgb(color)
        glow_color = f"#{r:02x}{g:02x}{b:02x}"

        # 根据形状类型创建不同的形状
        if self.shape == "circle":
            # 创建圆形
            shape_id = self.canvas.create_oval(
                x - radius, y - radius,
                x + radius, y + radius,
                outline=glow_color,
                width=int(self.glow_width * glow_factor),
                fill=""
            )

        elif self.shape == "square":
            # 创建正方形
            shape_id = self.canvas.create_rectangle(
                x - radius, y - radius,
                x + radius, y + radius,
                outline=glow_color,
                width=int(self.glow_width * glow_factor),
                fill=""
            )

        elif self.shape == "triangle":
            # 创建三角形
            points = []
            for i in range(3):
                angle = angle_offset + 2 * math.pi * i / 3
                px = x + radius * math.cos(angle)
                py = y + radius * math.sin(angle)
                points.extend([px, py])

            shape_id = self.canvas.create_polygon(
                points,
                outline=glow_color,
                width=int(self.glow_width * glow_factor),
                fill="",
                smooth=True
            )

        elif self.shape == "star":
            # 创建五角星
            points = []
            for i in range(5):
                # 外部点
                angle = angle_offset + 2 * math.pi * i / 5 - math.pi / 2
                px = x + radius * math.cos(angle)
                py = y + radius * math.sin(angle)
                points.extend([px, py])

                # 内部点
                inner_angle = angle + math.pi / 5
                inner_radius = radius * 0.4
                px = x + inner_radius * math.cos(inner_angle)
                py = y + inner_radius * math.sin(inner_angle)
                points.extend([px, py])

            shape_id = self.canvas.create_polygon(
                points,
                outline=glow_color,
                width=int(self.glow_width * glow_factor),
                fill="",
                smooth=False
            )

        else:
            # 默认创建圆形
            shape_id = self.canvas.create_oval(
                x - radius, y - radius,
                x + radius, y + radius,
                outline=glow_color,
                width=int(self.glow_width * glow_factor),
                fill=""
            )

        return shape_id

    def _animate(self):
        """更新动画帧"""
        if not self.is_running:
            return

        elapsed = time.time() - self.start_time
        canvas_height = self.height if not self.show_text else self.height - 25
        center_x = self.width // 2
        center_y = canvas_height // 2

        # 更新每个霓虹灯对象
        for obj in self.neon_objects:
            # 计算旋转角度
            angle = elapsed * self.rotation_speed + obj['angle_offset']

            # 更新每一层
            for layer_id in obj['layers']:
                # 删除旧形状
                self.canvas.delete(layer_id)

            # 清空层列表
            obj['layers'] = []

            # 创建新的多层发光效果
            layers = 5  # 发光层数
            for layer in range(layers):
                # 计算当前层的参数
                layer_radius = obj['radius'] - layer * (self.glow_width // 2)
                if layer_radius <= 0:
                    continue

                # 计算发光强度 - 随时间变化
                base_glow = 1.0 - layer / layers
                time_factor = 0.5 + 0.5 * math.sin(elapsed * 2 + obj['angle_offset'])
                glow_factor = base_glow * time_factor * self.glow_intensity

                # 创建形状
                shape_id = self._create_shape(
                    center_x, center_y, layer_radius,
                    obj['color'], glow_factor, angle
                )

                obj['layers'].append(shape_id)

        # 继续动画
        self.animation_id = self.canvas.after(16, self._animate)  # ~60fps


class GeometricMorphAnimation(BaseCreativeAnimation):
    """几何变形动画 - 几何图形变形和旋转的动画"""

    def __init__(self,
                 parent: tk.Widget,
                 width: int = 200,
                 height: int = 200,
                 bg_color: str = None,
                 fg_color: str = "#0d6efd",
                 text: str = "加载中...",
                 font: tuple = ("微软雅黑", 10),
                 show_text: bool = True,
                 morph_speed: float = 1.0,
                 rotation_speed: float = 1.0,
                 shape_count: int = 3,
                 color_cycle: List[str] = None):
        """
        初始化几何变形动画

        参数:
            parent: 父容器
            width: 动画宽度
            height: 动画高度
            bg_color: 背景颜色
            fg_color: 前景颜色
            text: 显示文本
            font: 文本字体
            show_text: 是否显示文本
            morph_speed: 变形速度
            rotation_speed: 旋转速度
            shape_count: 形状数量
            color_cycle: 颜色循环列表
        """
        super().__init__(parent, width, height, bg_color, fg_color, text, font, show_text)
        self.morph_speed = morph_speed
        self.rotation_speed = rotation_speed
        self.shape_count = shape_count
        self.color_cycle = color_cycle or [fg_color, "#6610f2", "#6f42c1", "#d63384", "#dc3545"]

        # 形状参数
        self.shapes = []
        self.shape_types = ["circle", "square", "triangle", "pentagon", "hexagon", "star"]

        # 创建形状
        self._create_shapes()

    def _create_shapes(self):
        """创建形状"""
        canvas_height = self.height if not self.show_text else self.height - 25
        center_x = self.width // 2
        center_y = canvas_height // 2

        # 清除现有形状
        for shape in self.shapes:
            self.canvas.delete(shape['id'])

        self.shapes = []

        # 基础半径
        base_radius = min(self.width, canvas_height) // 4

        # 创建多个形状
        for i in range(self.shape_count):
            # 计算初始参数
            radius = base_radius * (1.0 - 0.15 * i)  # 每个形状略小一些
            rotation = 2 * math.pi * i / self.shape_count  # 初始旋转角度

            # 随机选择形状类型和颜色
            shape_type = self.shape_types[i % len(self.shape_types)]
            color = self.color_cycle[i % len(self.color_cycle)]

            # 创建形状对象
            shape = {
                'type': shape_type,
                'radius': radius,
                'rotation': rotation,
                'color': color,
                'morph_phase': 2 * math.pi * i / self.shape_count,  # 变形相位
                'id': None,
                'points': 0  # 多边形的点数
            }

            # 设置多边形的点数
            if shape_type == "circle":
                shape['points'] = 30  # 近似圆形
            elif shape_type == "square":
                shape['points'] = 4
            elif shape_type == "triangle":
                shape['points'] = 3
            elif shape_type == "pentagon":
                shape['points'] = 5
            elif shape_type == "hexagon":
                shape['points'] = 6
            elif shape_type == "star":
                shape['points'] = 10  # 5个外点和5个内点

            # 绘制形状
            shape_id = self._draw_shape(center_x, center_y, shape)
            shape['id'] = shape_id

            self.shapes.append(shape)

    def _draw_shape(self, center_x, center_y, shape):
        """绘制形状"""
        points = []

        if shape['type'] == "circle":
            # 创建近似圆形的多边形
            for i in range(shape['points']):
                angle = shape['rotation'] + 2 * math.pi * i / shape['points']
                x = center_x + shape['radius'] * math.cos(angle)
                y = center_y + shape['radius'] * math.sin(angle)
                points.extend([x, y])

        elif shape['type'] == "star":
            # 创建五角星
            for i in range(5):
                # 外部点
                angle = shape['rotation'] + 2 * math.pi * i / 5 - math.pi / 2
                x = center_x + shape['radius'] * math.cos(angle)
                y = center_y + shape['radius'] * math.sin(angle)
                points.extend([x, y])

                # 内部点
                inner_angle = angle + math.pi / 5
                inner_radius = shape['radius'] * 0.4
                x = center_x + inner_radius * math.cos(inner_angle)
                y = center_y + inner_radius * math.sin(inner_angle)
                points.extend([x, y])

        else:
            # 创建正多边形
            for i in range(shape['points']):
                angle = shape['rotation'] + 2 * math.pi * i / shape['points']
                x = center_x + shape['radius'] * math.cos(angle)
                y = center_y + shape['radius'] * math.sin(angle)
                points.extend([x, y])

        # 创建多边形
        return self.canvas.create_polygon(
            points,
            outline=shape['color'],
            fill="",
            width=2,
            smooth=shape['type'] == "circle"  # 只有圆形使用平滑
        )

    def _animate(self):
        """更新动画帧"""
        if not self.is_running:
            return

        elapsed = time.time() - self.start_time
        canvas_height = self.height if not self.show_text else self.height - 25
        center_x = self.width // 2
        center_y = canvas_height // 2

        # 更新每个形状
        for shape in self.shapes:
            # 更新旋转角度
            shape['rotation'] += 0.01 * self.rotation_speed

            # 更新变形参数 - 半径脉动
            morph_factor = math.sin(elapsed * self.morph_speed + shape['morph_phase'])
            current_radius = shape['radius'] * (1.0 + 0.2 * morph_factor)

            # 更新颜色
            color_index = int((elapsed * 0.5 + shape['morph_phase']) % len(self.color_cycle))
            next_index = (color_index + 1) % len(self.color_cycle)
            blend_factor = (elapsed * 0.5 + shape['morph_phase']) % 1.0

            current_color = self._blend_colors(
                self.color_cycle[color_index],
                self.color_cycle[next_index],
                blend_factor
            )

            # 删除旧形状
            self.canvas.delete(shape['id'])

            # 临时更新半径
            temp_radius = current_radius
            shape['radius'] = temp_radius

            # 绘制新形状
            shape_id = self._draw_shape(center_x, center_y, shape)
            shape['id'] = shape_id

            # 更新颜色
            self.canvas.itemconfig(shape['id'], outline=current_color)

            # 恢复原始半径
            shape['radius'] = temp_radius / (1.0 + 0.2 * morph_factor)

        # 继续动画
        self.animation_id = self.canvas.after(16, self._animate)  # ~60fps

    def _blend_colors(self, color1, color2, factor):
        """混合两种颜色"""
        r1, g1, b1 = self._hex_to_rgb(color1)
        r2, g2, b2 = self._hex_to_rgb(color2)

        r = int(r1 + (r2 - r1) * factor)
        g = int(g1 + (g2 - g1) * factor)
        b = int(b1 + (b2 - b1) * factor)

        return f"#{r:02x}{g:02x}{b:02x}"


class TypewriterAnimation(BaseCreativeAnimation):
    """打字机效果 - 带有打字机效果的文字加载动画"""

    def __init__(self,
                 parent: tk.Widget,
                 width: int = 300,
                 height: int = 100,
                 bg_color: str = None,
                 fg_color: str = "#0d6efd",
                 text: str = "正在加载系统...",
                 font: tuple = ("Courier New", 16, "bold"),
                 typing_speed: float = 1.0,
                 cursor_blink: bool = True,
                 random_delay: bool = True,
                 tech_style: bool = True):
        """
        初始化打字机效果动画

        参数:
            parent: 父容器
            width: 动画宽度
            height: 动画高度
            bg_color: 背景颜色
            fg_color: 前景颜色
            text: 显示文本
            font: 文本字体
            typing_speed: 打字速度
            cursor_blink: 是否显示光标闪烁
            random_delay: 是否使用随机延迟
            tech_style: 是否使用科技风格
        """
        # 不使用基类的文本标签
        super().__init__(parent, width, height, bg_color, fg_color, "", font, False)
        self.full_text = text
        self.typing_speed = typing_speed
        self.cursor_blink = cursor_blink
        self.random_delay = random_delay
        self.tech_style = tech_style

        # 打字机状态
        self.current_text = ""
        self.cursor_visible = True
        self.text_id = None
        self.cursor_id = None
        self.tech_prefix = "> "

        # 技术风格的随机字符
        self.tech_chars = string.ascii_letters + string.digits + "!@#$%^&*()_+-=[]{}|;:,./<>?"

        # 创建文本对象
        self._create_text()

    def _create_text(self):
        """创建文本对象"""
        # 计算文本位置
        x = self.width // 2
        y = self.height // 2

        # 创建文本
        prefix = self.tech_prefix if self.tech_style else ""
        self.text_id = self.canvas.create_text(
            x, y,
            text=prefix,
            font=self.font,
            fill=self.fg_color,
            anchor="center"
        )

        # 创建光标
        if self.cursor_blink:
            self.cursor_id = self.canvas.create_text(
                x + len(prefix) * 7,  # 估计每个字符宽度
                y,
                text="|",
                font=self.font,
                fill=self.fg_color,
                anchor="center"
            )

    def _animate(self):
        """更新动画帧"""
        if not self.is_running:
            return

        elapsed = time.time() - self.start_time

        # 计算当前应该显示的文本长度
        if self.random_delay:
            # 使用非线性函数使打字速度不均匀
            text_length = int(len(self.full_text) * (0.5 + 0.5 * math.sin(elapsed * self.typing_speed * 0.5)) ** 2)
        else:
            # 线性打字速度
            chars_per_second = 5 * self.typing_speed
            text_length = min(len(self.full_text), int(elapsed * chars_per_second))

        # 获取当前应该显示的文本
        new_text = self.full_text[:text_length]

        # 如果文本发生变化，更新显示
        if new_text != self.current_text:
            self.current_text = new_text

            # 添加科技风格前缀
            prefix = self.tech_prefix if self.tech_style else ""
            display_text = prefix + self.current_text

            # 如果启用科技风格，添加随机字符
            if self.tech_style and text_length < len(self.full_text):
                # 添加一个随机字符，模拟正在输入的效果
                display_text += random.choice(self.tech_chars)

            # 更新文本
            self.canvas.itemconfig(self.text_id, text=display_text)

            # 更新光标位置
            if self.cursor_blink:
                text_width = len(display_text) * 7  # 估计文本宽度
                cursor_x = self.width // 2 + text_width // 2 + 5
                cursor_y = self.height // 2
                self.canvas.coords(self.cursor_id, cursor_x, cursor_y)

        # 更新光标闪烁
        if self.cursor_blink:
            blink_period = 0.5  # 光标闪烁周期
            self.cursor_visible = (elapsed % blink_period) < (blink_period / 2)

            # 更新光标可见性
            if text_length < len(self.full_text):  # 只在打字未完成时显示光标
                self.canvas.itemconfig(
                    self.cursor_id,
                    state="normal" if self.cursor_visible else "hidden"
                )
            else:
                self.canvas.itemconfig(self.cursor_id, state="hidden")

        # 继续动画
        self.animation_id = self.canvas.after(50, self._animate)  # 降低更新频率，模拟打字机效果