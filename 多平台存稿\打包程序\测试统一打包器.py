#!/usr/bin/env python3
"""
测试统一打包器功能
"""

import sys
from pathlib import Path

# 添加当前目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def test_import():
    """测试导入"""
    try:
        from 统一自动打包 import UnifiedPackager
        print("✅ 统一打包器导入成功")
        return True
    except ImportError as e:
        print(f"❌ 统一打包器导入失败: {e}")
        return False

def test_packager():
    """测试打包器基本功能"""
    try:
        from 统一自动打包 import UnifiedPackager
        
        packager = UnifiedPackager()
        print("✅ 打包器实例创建成功")
        
        # 测试配置
        print(f"源目录: {packager.config['source_dir']}")
        print(f"目标目录: {packager.config['target_dir']}")
        
        # 测试依赖检查
        print("测试依赖检查...")
        result = packager.check_dependencies()
        if result:
            print("✅ 依赖检查通过")
        else:
            print("❌ 依赖检查失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 打包器测试失败: {e}")
        return False

def test_gui():
    """测试GUI功能"""
    try:
        from 统一自动打包 import GUI_AVAILABLE
        
        if GUI_AVAILABLE:
            print("✅ GUI模块可用")
            return True
        else:
            print("⚠️  GUI模块不可用")
            return False
            
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        return False

def main():
    print("=" * 50)
    print("统一打包器功能测试")
    print("=" * 50)
    
    tests = [
        ("导入测试", test_import),
        ("打包器测试", test_packager),
        ("GUI测试", test_gui),
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        print(f"\n🔍 {name}:")
        if test_func():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！统一打包器可以正常使用")
    else:
        print("⚠️  部分测试失败，请检查问题")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
