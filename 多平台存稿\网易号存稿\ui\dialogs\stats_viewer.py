"""
数据统计查看器模块 - 用于展示详细的数据统计信息
"""

import tkinter as tk
from tkinter import ttk, messagebox
import datetime

class StatsViewer:
    """数据统计查看器类"""

    def __init__(self, parent, account_data, log_callback=None):
        """
        初始化数据统计查看器

        Args:
            parent: 父窗口
            account_data: 账号数据管理器
            log_callback: 日志回调函数
        """
        self.parent = parent
        self.account_data = account_data
        self.log = log_callback or (lambda x: print(x))

        # 获取统计数据
        self.stats = self.account_data.calculate_statistics()

        # 创建窗口
        self.window = tk.Toplevel(parent)
        self.window.title("数据统计查看器")
        self.window.geometry("1000x700")
        self.window.minsize(800, 600)

        # 设置窗口图标
        try:
            self.window.iconbitmap("assets/icon.ico")
        except:
            pass

        # 创建UI
        self.create_ui()

        # 加载数据
        self.load_data()

    def create_ui(self):
        """创建用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.window, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建标签页
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # 创建概览标签页
        self.create_overview_tab()

        # 创建收益分析标签页
        self.create_income_tab()

        # 创建底部按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(button_frame, text="刷新数据", command=self.load_data).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="导出报告", command=self.export_report).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="关闭", command=self.window.destroy).pack(side=tk.RIGHT, padx=5)

    def create_overview_tab(self):
        """创建概览标签页"""
        overview_frame = ttk.Frame(self.notebook)
        self.notebook.add(overview_frame, text="数据概览")

        # 创建左侧统计信息区域
        stats_frame = ttk.LabelFrame(overview_frame, text="统计信息", padding=10)
        stats_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建统计信息文本区域
        self.stats_text = tk.Text(stats_frame, wrap=tk.WORD, font=("Consolas", 10))
        self.stats_text.pack(fill=tk.BOTH, expand=True)

        # 只读模式
        self.stats_text.config(state=tk.DISABLED)

        # 创建右侧图表区域
        chart_frame = ttk.LabelFrame(overview_frame, text="数据图表", padding=10)
        chart_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建图表容器
        self.overview_chart_container = ttk.Frame(chart_frame)
        self.overview_chart_container.pack(fill=tk.BOTH, expand=True)

    def create_income_tab(self):
        """创建收益分析标签页"""
        income_frame = ttk.Frame(self.notebook)
        self.notebook.add(income_frame, text="收益分析")

        # 创建控制区域
        control_frame = ttk.Frame(income_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))

        # 图表类型选择
        ttk.Label(control_frame, text="图表类型:").pack(side=tk.LEFT, padx=5)
        self.income_chart_type = tk.StringVar(value="收益排名")
        chart_types = ["收益排名", "收益分布", "收益趋势", "七日收益趋势"]
        ttk.Combobox(control_frame, textvariable=self.income_chart_type, values=chart_types, width=15).pack(side=tk.LEFT, padx=5)

        ttk.Button(control_frame, text="生成图表", command=self.generate_income_chart).pack(side=tk.LEFT, padx=5)

        # 创建图表容器
        self.income_chart_container = ttk.Frame(income_frame)
        self.income_chart_container.pack(fill=tk.BOTH, expand=True)



    def load_data(self):
        """加载数据"""
        # 更新统计数据
        self.stats = self.account_data.calculate_statistics()

        # 更新统计信息
        self.update_stats_text()

        # 生成概览图表
        self.generate_overview_chart()

        # 生成收益分析图表
        self.generate_income_chart()

    def update_stats_text(self):
        """更新统计信息文本"""
        # 启用文本编辑
        self.stats_text.config(state=tk.NORMAL)

        # 清空文本
        self.stats_text.delete(1.0, tk.END)

        # 添加统计信息
        self.stats_text.insert(tk.END, f"账号总数: {self.stats['账号总数']}\n\n")

        # 收益统计
        self.stats_text.insert(tk.END, "===== 收益统计 =====\n")
        self.stats_text.insert(tk.END, f"总收益: {self.stats['总收益']:.2f}\n")
        self.stats_text.insert(tk.END, f"平均收益: {self.stats['平均收益']:.2f}\n")
        self.stats_text.insert(tk.END, f"最高收益: {self.stats['最高收益']:.2f} ({self.stats['最高收益账号']})\n")
        self.stats_text.insert(tk.END, f"最低收益: {self.stats['最低收益']:.2f} ({self.stats['最低收益账号']})\n\n")

        # 七日收益统计
        self.stats_text.insert(tk.END, "===== 七日收益统计 =====\n")
        self.stats_text.insert(tk.END, f"七日总收益: {self.stats['七日总收益']:.2f}\n")
        self.stats_text.insert(tk.END, f"七日平均收益: {self.stats['七日平均收益']:.2f}\n")
        self.stats_text.insert(tk.END, f"七日收益增长率: {self.stats['七日收益增长率']:.2f}%\n\n")

        # 禁用文本编辑
        self.stats_text.config(state=tk.DISABLED)

    def generate_overview_chart(self):
        """生成概览数据表格"""
        # 清空表格容器
        for widget in self.overview_chart_container.winfo_children():
            widget.destroy()

        # 创建表格框架
        table_frame = ttk.Frame(self.overview_chart_container)
        table_frame.pack(fill=tk.BOTH, expand=True)

        # 创建表格
        columns = ("指标", "总收益", "七日收益", "昨日收益", "占比(%)")
        tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=10)

        # 设置列标题和宽度
        for col in columns:
            tree.heading(col, text=col)
            width = 150 if col == "指标" else 100
            tree.column(col, width=width, anchor=tk.CENTER)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        # 布局
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 获取数据
        total_income = self.stats['总收益']
        seven_day_income = self.stats['七日总收益']
        yesterday_income = self.stats.get('昨日总收益', 0)

        # 计算总和和百分比
        total = total_income + seven_day_income + yesterday_income
        total_pct = (total_income / total * 100) if total > 0 else 0
        seven_day_pct = (seven_day_income / total * 100) if total > 0 else 0
        yesterday_pct = (yesterday_income / total * 100) if total > 0 else 0

        # 添加数据行
        tree.insert("", "end", values=(
            "数值",
            f"{total_income:.2f}",
            f"{seven_day_income:.2f}",
            f"{yesterday_income:.2f}",
            "100.00"
        ))

        tree.insert("", "end", values=(
            "占比(%)",
            f"{total_pct:.2f}",
            f"{seven_day_pct:.2f}",
            f"{yesterday_pct:.2f}",
            "100.00"
        ))

        # 添加标题
        title_frame = ttk.Frame(self.overview_chart_container)
        title_frame.pack(side=tk.TOP, fill=tk.X, pady=(0, 10))
        ttk.Label(title_frame, text="收益数据分布", font=("Arial", 12, "bold")).pack()

    def generate_income_chart(self):
        """生成收益分析表格"""
        # 清空表格容器
        for widget in self.income_chart_container.winfo_children():
            widget.destroy()

        # 获取数据类型
        chart_type = self.income_chart_type.get()

        # 根据类型生成相应的表格
        if chart_type == "收益排名":
            self.generate_income_ranking_table()
        elif chart_type == "收益分布":
            self.generate_income_distribution_table()
        elif chart_type == "收益趋势":
            self.generate_income_trend_table()
        elif chart_type == "七日收益趋势":
            self.generate_seven_day_income_trend_table()

    def generate_income_ranking_table(self):
        """生成收益排名表格"""
        # 创建表格框架
        table_frame = ttk.Frame(self.income_chart_container)
        table_frame.pack(fill=tk.BOTH, expand=True)

        # 创建表格
        columns = ("排名", "账号", "总收益", "占比(%)")
        tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)

        # 设置列标题和宽度
        for col in columns:
            tree.heading(col, text=col)
            if col == "排名":
                tree.column(col, width=60, anchor=tk.CENTER)
            elif col == "账号":
                tree.column(col, width=150, anchor=tk.W)
            else:
                tree.column(col, width=100, anchor=tk.CENTER)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        # 布局
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 获取所有账号数据
        accounts_data = self.account_data.get_all_accounts_data()

        # 提取数据
        data_list = []
        total_sum = 0

        for data in accounts_data:
            account = data.get("账号", "")
            income = self.clean_numeric_value(data.get("总收益", "0"))
            data_list.append((account, income))
            total_sum += income

        # 排序
        data_list.sort(key=lambda x: x[1], reverse=True)

        # 添加到表格
        for i, (account, income) in enumerate(data_list, 1):
            percentage = (income / total_sum * 100) if total_sum > 0 else 0
            tree.insert("", "end", values=(
                i,
                account,
                f"{income:.2f}",
                f"{percentage:.2f}"
            ))

            # 为前三名添加特殊标记
            if i <= 3:
                tree.item(tree.get_children()[-1], tags=("top3",))

        # 设置标签样式
        tree.tag_configure("top3", background="#FFD700")

        # 添加总计行
        tree.insert("", "end", values=(
            "",
            "总计",
            f"{total_sum:.2f}",
            "100.00"
        ), tags=("total",))

        # 设置总计行样式
        tree.tag_configure("total", background="#E0E0E0", font=("Arial", 10, "bold"))

        # 添加标题
        title_frame = ttk.Frame(self.income_chart_container)
        title_frame.pack(side=tk.TOP, fill=tk.X, pady=(0, 10))
        ttk.Label(title_frame, text="账号收益排名", font=("Arial", 12, "bold")).pack()



    def generate_income_distribution_table(self):
        """生成收益分布表格"""
        # 创建表格框架
        table_frame = ttk.Frame(self.income_chart_container)
        table_frame.pack(fill=tk.BOTH, expand=True)

        # 创建表格
        columns = ("收益区间", "账号数量", "占比(%)")
        tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)

        # 设置列标题和宽度
        for col in columns:
            tree.heading(col, text=col)
            width = 150 if col == "收益区间" else 100
            tree.column(col, width=width, anchor=tk.CENTER)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        # 布局
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 获取所有账号数据
        accounts_data = self.account_data.get_all_accounts_data()

        # 提取收益数据
        incomes = []
        for data in accounts_data:
            income = self.clean_numeric_value(data.get("总收益", "0"))
            incomes.append(income)

        # 如果没有数据，显示提示信息
        if not incomes:
            tree.insert("", "end", values=("无数据", "", ""))
            return

        # 计算收益区间
        min_income = min(incomes)
        max_income = max(incomes)

        # 创建10个区间
        bin_count = 10
        bin_width = (max_income - min_income) / bin_count if max_income > min_income else 1

        # 初始化区间计数
        bins = [0] * bin_count

        # 统计每个区间的账号数量
        for income in incomes:
            if max_income == min_income:
                bin_index = 0
            else:
                bin_index = min(int((income - min_income) / bin_width), bin_count - 1)
            bins[bin_index] += 1

        # 添加到表格
        total_accounts = len(incomes)
        for i in range(bin_count):
            bin_start = min_income + i * bin_width
            bin_end = min_income + (i + 1) * bin_width
            count = bins[i]
            percentage = (count / total_accounts * 100) if total_accounts > 0 else 0

            # 格式化区间
            if i == bin_count - 1:  # 最后一个区间包含最大值
                range_str = f"{bin_start:.2f} - {max_income:.2f}"
            else:
                range_str = f"{bin_start:.2f} - {bin_end:.2f}"

            tree.insert("", "end", values=(
                range_str,
                count,
                f"{percentage:.2f}"
            ))

        # 添加总计行
        tree.insert("", "end", values=(
            "总计",
            total_accounts,
            "100.00"
        ), tags=("total",))

        # 设置总计行样式
        tree.tag_configure("total", background="#E0E0E0", font=("Arial", 10, "bold"))

        # 添加标题
        title_frame = ttk.Frame(self.income_chart_container)
        title_frame.pack(side=tk.TOP, fill=tk.X, pady=(0, 10))
        ttk.Label(title_frame, text="账号收益分布", font=("Arial", 12, "bold")).pack()

    def generate_income_trend_table(self):
        """生成收益趋势表格"""
        # 创建表格框架
        table_frame = ttk.Frame(self.income_chart_container)
        table_frame.pack(fill=tk.BOTH, expand=True)

        # 获取所有账号数据
        accounts_data = self.account_data.get_all_accounts_data()

        # 按更新时间排序
        accounts_data.sort(key=lambda x: x.get("更新时间", ""))

        # 提取数据
        date_income_map = {}

        for data in accounts_data:
            date = data.get("更新时间", "").split(" ")[0]  # 只取日期部分
            if not date:
                continue

            income = self.clean_numeric_value(data.get("总收益", "0"))

            if date not in date_income_map:
                date_income_map[date] = 0
            date_income_map[date] += income

        # 如果没有数据，显示提示信息
        if not date_income_map:
            ttk.Label(table_frame, text="没有收益趋势数据", font=("Arial", 14)).pack(expand=True)
            return

        # 创建表格
        columns = ("日期", "总收益", "环比增长", "增长率(%)")
        tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)

        # 设置列标题和宽度
        for col in columns:
            tree.heading(col, text=col)
            width = 100
            tree.column(col, width=width, anchor=tk.CENTER)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        # 布局
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 按日期排序
        dates = sorted(date_income_map.keys())

        # 计算总收益和平均收益
        total_income = 0
        prev_income = None

        # 添加数据到表格
        for date in dates:
            income = date_income_map[date]
            total_income += income

            # 计算环比增长
            if prev_income is not None:
                growth = income - prev_income
                growth_rate = (growth / prev_income * 100) if prev_income > 0 else 0
                growth_str = f"{growth:.2f}"
                growth_rate_str = f"{growth_rate:.2f}"
            else:
                growth_str = "-"
                growth_rate_str = "-"

            # 添加到表格
            tree.insert("", "end", values=(
                date,
                f"{income:.2f}",
                growth_str,
                growth_rate_str
            ))

            # 更新前一天收益
            prev_income = income

        # 计算平均收益
        avg_income = total_income / len(dates) if dates else 0

        # 添加总计和平均行
        tree.insert("", "end", values=(
            "总计",
            f"{total_income:.2f}",
            "",
            ""
        ), tags=("total",))

        tree.insert("", "end", values=(
            "日均",
            f"{avg_income:.2f}",
            "",
            ""
        ), tags=("avg",))

        # 设置样式
        tree.tag_configure("total", background="#E0E0E0", font=("Arial", 10, "bold"))
        tree.tag_configure("avg", background="#F0F0F0", font=("Arial", 10, "bold"))

        # 添加标题
        title_frame = ttk.Frame(self.income_chart_container)
        title_frame.pack(side=tk.TOP, fill=tk.X, pady=(0, 10))
        ttk.Label(title_frame, text="收益趋势", font=("Arial", 12, "bold")).pack()

    def generate_seven_day_income_trend_table(self):
        """生成七日收益趋势表格"""
        # 创建表格框架
        table_frame = ttk.Frame(self.income_chart_container)
        table_frame.pack(fill=tk.BOTH, expand=True)

        # 获取所有账号数据
        accounts_data = self.account_data.get_all_accounts_data()

        # 提取七日收益数据
        dates = set()
        date_income_map = {}

        for data in accounts_data:
            if "七日收益数据" in data:
                seven_day_data = data["七日收益数据"]

                for date, income in seven_day_data.items():
                    dates.add(date)
                    if date not in date_income_map:
                        date_income_map[date] = 0
                    date_income_map[date] += income

        # 如果没有数据，显示提示信息
        if not date_income_map:
            ttk.Label(table_frame, text="没有七日收益趋势数据", font=("Arial", 14)).pack(expand=True)
            return

        # 创建表格
        columns = ("日期", "总收益", "环比增长", "增长率(%)")
        tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)

        # 设置列标题和宽度
        for col in columns:
            tree.heading(col, text=col)
            width = 100
            tree.column(col, width=width, anchor=tk.CENTER)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        # 布局
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 按日期排序
        dates = sorted(list(dates))

        # 计算总收益和平均收益
        total_income = 0
        prev_income = None

        # 添加数据到表格
        for date in dates:
            income = date_income_map[date]
            total_income += income

            # 计算环比增长
            if prev_income is not None:
                growth = income - prev_income
                growth_rate = (growth / prev_income * 100) if prev_income > 0 else 0
                growth_str = f"{growth:.2f}"
                growth_rate_str = f"{growth_rate:.2f}"
            else:
                growth_str = "-"
                growth_rate_str = "-"

            # 添加到表格
            tree.insert("", "end", values=(
                date,
                f"{income:.2f}",
                growth_str,
                growth_rate_str
            ))

            # 更新前一天收益
            prev_income = income

        # 计算平均收益
        avg_income = total_income / len(dates) if dates else 0

        # 添加总计和平均行
        tree.insert("", "end", values=(
            "总计",
            f"{total_income:.2f}",
            "",
            ""
        ), tags=("total",))

        tree.insert("", "end", values=(
            "日均",
            f"{avg_income:.2f}",
            "",
            ""
        ), tags=("avg",))

        # 设置样式
        tree.tag_configure("total", background="#E0E0E0", font=("Arial", 10, "bold"))
        tree.tag_configure("avg", background="#F0F0F0", font=("Arial", 10, "bold"))

        # 添加标题
        title_frame = ttk.Frame(self.income_chart_container)
        title_frame.pack(side=tk.TOP, fill=tk.X, pady=(0, 10))
        ttk.Label(title_frame, text="七日收益趋势", font=("Arial", 12, "bold")).pack()



    def export_report(self):
        """导出统计报告"""
        from tkinter import filedialog

        # 获取当前时间作为文件名
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"netease_stats_report_{timestamp}.html"

        # 打开文件对话框
        file_path = filedialog.asksaveasfilename(
            defaultextension=".html",
            filetypes=[("HTML文件", "*.html")],
            initialfile=filename
        )

        if not file_path:
            return

        try:
            # 生成HTML报告
            html_content = self.generate_html_report()

            # 保存到文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)

            messagebox.showinfo("导出成功", f"统计报告已导出到:\n{file_path}")
            self.log(f"统计报告已导出到: {file_path}")

        except Exception as e:
            messagebox.showerror("导出失败", f"导出统计报告失败:\n{str(e)}")
            self.log(f"导出统计报告失败: {str(e)}")

    def generate_html_report(self):
        """生成HTML格式的统计报告"""
        # 获取当前时间
        now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 生成HTML内容
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>网易号数据统计报告</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                h1, h2, h3 {{ color: #333; }}
                table {{ border-collapse: collapse; width: 100%; margin-bottom: 20px; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                tr:nth-child(even) {{ background-color: #f9f9f9; }}
                .summary {{ background-color: #e6f7ff; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
            </style>
        </head>
        <body>
            <h1>网易号数据统计报告</h1>
            <p>生成时间: {now}</p>

            <div class="summary">
                <h2>数据概览</h2>
                <p>账号总数: {self.stats['账号总数']}</p>
                <p>总收益: {self.stats['总收益']:.2f}</p>
                <p>七日总收益: {self.stats['七日总收益']:.2f}</p>
                <p>七日收益增长率: {self.stats['七日收益增长率']:.2f}%</p>
            </div>

            <h2>收益统计</h2>
            <table>
                <tr>
                    <th>指标</th>
                    <th>数值</th>
                </tr>
                <tr>
                    <td>总收益</td>
                    <td>{self.stats['总收益']:.2f}</td>
                </tr>
                <tr>
                    <td>平均收益</td>
                    <td>{self.stats['平均收益']:.2f}</td>
                </tr>
                <tr>
                    <td>最高收益</td>
                    <td>{self.stats['最高收益']:.2f} ({self.stats['最高收益账号']})</td>
                </tr>
                <tr>
                    <td>最低收益</td>
                    <td>{self.stats['最低收益']:.2f} ({self.stats['最低收益账号']})</td>
                </tr>
                <tr>
                    <td>七日总收益</td>
                    <td>{self.stats['七日总收益']:.2f}</td>
                </tr>
                <tr>
                    <td>七日平均收益</td>
                    <td>{self.stats['七日平均收益']:.2f}</td>
                </tr>
                <tr>
                    <td>七日收益增长率</td>
                    <td>{self.stats['七日收益增长率']:.2f}%</td>
                </tr>
            </table>

            <h2>账号数据</h2>
            <table>
                <tr>
                    <th>账号</th>
                    <th>总收益</th>
                    <th>昨日收益</th>
                    <th>总播放</th>
                    <th>昨日播放</th>
                    <th>总粉丝</th>
                    <th>昨日粉丝</th>
                    <th>更新时间</th>
                </tr>
        """

        # 添加账号数据
        accounts_data = self.account_data.get_all_accounts_data()
        for data in accounts_data:
            html += f"""
                <tr>
                    <td>{data.get('账号', '')}</td>
                    <td>{self.clean_numeric_value(data.get('总收益', '0')):.2f}</td>
                    <td>{self.clean_numeric_value(data.get('昨日收益', '0')):.2f}</td>
                    <td>{self.clean_numeric_value(data.get('总播放', '0')):.0f}</td>
                    <td>{self.clean_numeric_value(data.get('昨日播放', '0')):.0f}</td>
                    <td>{self.clean_numeric_value(data.get('总粉丝', '0')):.0f}</td>
                    <td>{self.clean_numeric_value(data.get('昨日粉丝', '0')):.0f}</td>
                    <td>{data.get('更新时间', '')}</td>
                </tr>
            """

        html += """
            </table>

            <p><em>注: 本报告由网易号存稿工具自动生成</em></p>
        </body>
        </html>
        """

        return html

    def clean_numeric_value(self, value):
        """清理数值，转换为浮点数"""
        if not value:
            return 0

        # 移除非数字字符
        value = str(value)
        value = ''.join(c for c in value if c.isdigit() or c == '.' or c == '-')

        try:
            return float(value)
        except:
            return 0
