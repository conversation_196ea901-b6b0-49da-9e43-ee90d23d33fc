"""
类型提示文件，用于告诉IDE动画类是存在的
"""

import tkinter as tk
from typing import Any, List, Optional, Tuple, Dict, Callable

# 高级创意动画类
class DNAHelixAnimation:
    def __init__(
        self,
        parent: tk.Widget,
        width: int = 200,
        height: int = 200,
        bg_color: Optional[str] = None,
        fg_color: str = "#0d6efd",
        text: str = "加载中...",
        font: tuple = ("微软雅黑", 10),
        show_text: bool = True,
        rotation_speed: float = 1.0,
        helix_width: int = 100,
        helix_height: int = 100,
        strand_colors: Optional[List[str]] = None,
        base_pair_colors: Optional[List[str]] = None
    ) -> None: ...
    
    def start(self) -> 'DNAHelixAnimation': ...
    def stop(self) -> 'DNAHelixAnimation': ...
    def pack(self, **kwargs) -> 'DNAHelixAnimation': ...
    def grid(self, **kwargs) -> 'DNAHelixAnimation': ...
    def place(self, **kwargs) -> 'DNAHelixAnimation': ...
    def destroy(self) -> None: ...

class GalaxyAnimation:
    def __init__(
        self,
        parent: tk.Widget,
        width: int = 200,
        height: int = 200,
        bg_color: Optional[str] = None,
        fg_color: str = "#0d6efd",
        text: str = "加载中...",
        font: tuple = ("微软雅黑", 10),
        show_text: bool = True,
        rotation_speed: float = 0.5,
        star_count: int = 200,
        galaxy_colors: Optional[List[str]] = None,
        has_black_hole: bool = True
    ) -> None: ...
    
    def start(self) -> 'GalaxyAnimation': ...
    def stop(self) -> 'GalaxyAnimation': ...
    def pack(self, **kwargs) -> 'GalaxyAnimation': ...
    def grid(self, **kwargs) -> 'GalaxyAnimation': ...
    def place(self, **kwargs) -> 'GalaxyAnimation': ...
    def destroy(self) -> None: ...

class MatrixRainAnimation:
    def __init__(
        self,
        parent: tk.Widget,
        width: int = 200,
        height: int = 200,
        bg_color: Optional[str] = None,
        fg_color: str = "#00ff00",
        text: str = "加载中...",
        font: tuple = ("Courier New", 10),
        show_text: bool = True,
        drop_speed: float = 1.0,
        density: float = 1.0,
        char_set: Optional[str] = None
    ) -> None: ...
    
    def start(self) -> 'MatrixRainAnimation': ...
    def stop(self) -> 'MatrixRainAnimation': ...
    def pack(self, **kwargs) -> 'MatrixRainAnimation': ...
    def grid(self, **kwargs) -> 'MatrixRainAnimation': ...
    def place(self, **kwargs) -> 'MatrixRainAnimation': ...
    def destroy(self) -> None: ...

class EnergyPulseAnimation:
    def __init__(
        self,
        parent: tk.Widget,
        width: int = 200,
        height: int = 200,
        bg_color: Optional[str] = None,
        fg_color: str = "#0d6efd",
        text: str = "加载中...",
        font: tuple = ("微软雅黑", 10),
        show_text: bool = True,
        pulse_speed: float = 1.0,
        pulse_count: int = 3,
        pulse_colors: Optional[List[str]] = None,
        glow_effect: bool = True
    ) -> None: ...
    
    def start(self) -> 'EnergyPulseAnimation': ...
    def stop(self) -> 'EnergyPulseAnimation': ...
    def pack(self, **kwargs) -> 'EnergyPulseAnimation': ...
    def grid(self, **kwargs) -> 'EnergyPulseAnimation': ...
    def place(self, **kwargs) -> 'EnergyPulseAnimation': ...
    def destroy(self) -> None: ...

class AudioWaveAnimation:
    def __init__(
        self,
        parent: tk.Widget,
        width: int = 200,
        height: int = 200,
        bg_color: Optional[str] = None,
        fg_color: str = "#0d6efd",
        text: str = "加载中...",
        font: tuple = ("微软雅黑", 10),
        show_text: bool = True,
        bar_count: int = 20,
        wave_speed: float = 1.0,
        bar_width: int = 4,
        bar_spacing: int = 2,
        color_gradient: bool = True,
        gradient_colors: Optional[List[str]] = None
    ) -> None: ...
    
    def start(self) -> 'AudioWaveAnimation': ...
    def stop(self) -> 'AudioWaveAnimation': ...
    def pack(self, **kwargs) -> 'AudioWaveAnimation': ...
    def grid(self, **kwargs) -> 'AudioWaveAnimation': ...
    def place(self, **kwargs) -> 'AudioWaveAnimation': ...
    def destroy(self) -> None: ...

# 创意动画类
class ParticleFlowAnimation:
    def __init__(
        self,
        parent: tk.Widget,
        width: int = 200,
        height: int = 200,
        bg_color: Optional[str] = None,
        fg_color: str = "#0d6efd",
        text: str = "加载中...",
        font: tuple = ("微软雅黑", 10),
        show_text: bool = True,
        particle_count: int = 50,
        flow_speed: float = 1.0,
        particle_size_range: tuple = (2, 5),
        color_scheme: Optional[List[str]] = None
    ) -> None: ...
    
    def start(self) -> 'ParticleFlowAnimation': ...
    def stop(self) -> 'ParticleFlowAnimation': ...
    def pack(self, **kwargs) -> 'ParticleFlowAnimation': ...
    def grid(self, **kwargs) -> 'ParticleFlowAnimation': ...
    def place(self, **kwargs) -> 'ParticleFlowAnimation': ...
    def destroy(self) -> None: ...

class LiquidWaveAnimation:
    def __init__(
        self,
        parent: tk.Widget,
        width: int = 200,
        height: int = 200,
        bg_color: Optional[str] = None,
        fg_color: str = "#0d6efd",
        text: str = "加载中...",
        font: tuple = ("微软雅黑", 10),
        show_text: bool = True,
        wave_count: int = 3,
        wave_height: int = 10,
        wave_speed: float = 1.0,
        fill_level: float = 0.6,
        color_gradient: bool = True
    ) -> None: ...
    
    def start(self) -> 'LiquidWaveAnimation': ...
    def stop(self) -> 'LiquidWaveAnimation': ...
    def pack(self, **kwargs) -> 'LiquidWaveAnimation': ...
    def grid(self, **kwargs) -> 'LiquidWaveAnimation': ...
    def place(self, **kwargs) -> 'LiquidWaveAnimation': ...
    def destroy(self) -> None: ...

class NeonGlowAnimation:
    def __init__(
        self,
        parent: tk.Widget,
        width: int = 200,
        height: int = 200,
        bg_color: Optional[str] = None,
        fg_color: str = "#0d6efd",
        text: str = "加载中...",
        font: tuple = ("微软雅黑", 10),
        show_text: bool = True,
        glow_width: int = 4,
        glow_intensity: float = 1.0,
        rotation_speed: float = 1.0,
        shape: str = "circle"
    ) -> None: ...
    
    def start(self) -> 'NeonGlowAnimation': ...
    def stop(self) -> 'NeonGlowAnimation': ...
    def pack(self, **kwargs) -> 'NeonGlowAnimation': ...
    def grid(self, **kwargs) -> 'NeonGlowAnimation': ...
    def place(self, **kwargs) -> 'NeonGlowAnimation': ...
    def destroy(self) -> None: ...

# 基础动画类
class SpinnerAnimation:
    def __init__(
        self,
        parent: tk.Widget,
        width: int = 200,
        height: int = 200,
        bg_color: Optional[str] = None,
        fg_color: str = "#0d6efd",
        text: str = "加载中...",
        font: tuple = ("微软雅黑", 10),
        show_text: bool = True
    ) -> None: ...
    
    def start(self) -> 'SpinnerAnimation': ...
    def stop(self) -> 'SpinnerAnimation': ...
    def pack(self, **kwargs) -> 'SpinnerAnimation': ...
    def grid(self, **kwargs) -> 'SpinnerAnimation': ...
    def place(self, **kwargs) -> 'SpinnerAnimation': ...
    def destroy(self) -> None: ...
