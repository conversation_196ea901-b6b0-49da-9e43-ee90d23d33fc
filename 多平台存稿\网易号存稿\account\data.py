"""
账号数据处理模块 - 负责处理账号数据
"""

import os
import json
import csv
import pandas as pd
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime, timedelta

from ..common.utils import parse_income

class AccountData:
    """账号数据处理类，负责处理账号数据"""

    def __init__(self, data_file: str = "account_data.json", log_callback: Callable = None):
        """
        初始化账号数据处理器

        Args:
            data_file: 数据文件路径
            log_callback: 日志回调函数
        """
        self.data_file = data_file
        self.log_callback = log_callback
        self.account_data = {}

        # 加载账号数据
        self.load_data()

    def log(self, message: str) -> None:
        """
        记录日志

        Args:
            message: 日志消息
        """
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def load_data(self) -> Dict[str, Any]:
        """
        加载账号数据

        Returns:
            账号数据字典
        """
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    self.account_data = json.load(f)
                self.log(f"已加载账号数据: {len(self.account_data)} 条记录")
            else:
                self.account_data = {}
                self.log("账号数据文件不存在，将创建新文件")
                self.save_data()

            return self.account_data

        except Exception as e:
            self.log(f"加载账号数据失败: {str(e)}")
            self.account_data = {}
            return {}

    def save_data(self) -> bool:
        """
        保存账号数据

        Returns:
            是否成功保存
        """
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.account_data, f, ensure_ascii=False, indent=4)
            self.log(f"已保存账号数据: {len(self.account_data)} 条记录")
            return True

        except Exception as e:
            self.log(f"保存账号数据失败: {str(e)}")
            return False

    def update_account_data(self, account: str, data: Dict[str, Any]) -> bool:
        """
        更新账号数据

        Args:
            account: 账号名称
            data: 账号数据

        Returns:
            是否成功更新
        """
        try:
            # 添加更新时间
            data["更新时间"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 更新账号数据
            self.account_data[account] = data

            # 保存数据
            return self.save_data()

        except Exception as e:
            self.log(f"更新账号数据失败: {str(e)}")
            return False

    def get_account_data(self, account: str) -> Optional[Dict[str, Any]]:
        """
        获取账号数据

        Args:
            account: 账号名称

        Returns:
            账号数据，如果不存在则返回None
        """
        return self.account_data.get(account)

    def get_all_accounts_data(self, existing_accounts: set = None) -> List[Dict[str, Any]]:
        """
        获取所有账号数据

        Args:
            existing_accounts: 实际存在的账号集合，如果提供则只返回这些账号的数据

        Returns:
            所有账号数据列表
        """
        result = []
        for account, data in self.account_data.items():
            # 如果提供了existing_accounts参数，只返回实际存在的账号数据
            if existing_accounts is not None and account not in existing_accounts:
                continue

            data_copy = data.copy()
            data_copy["账号"] = account
            result.append(data_copy)
        return result

    def backup_data(self, backup_file: str = None) -> bool:
        """
        备份账号数据

        Args:
            backup_file: 备份文件路径，如果为None则自动生成

        Returns:
            是否成功备份
        """
        try:
            if not backup_file:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_file = f"account_data_backup_{timestamp}.json"

            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(self.account_data, f, ensure_ascii=False, indent=4)

            self.log(f"已备份账号数据到: {backup_file}")
            return True

        except Exception as e:
            self.log(f"备份账号数据失败: {str(e)}")
            return False

    def export_to_excel(self, excel_file: str) -> bool:
        """
        导出账号数据到Excel

        Args:
            excel_file: Excel文件路径

        Returns:
            是否成功导出
        """
        try:
            # 准备数据
            data_list = self.get_all_accounts_data()
            if not data_list:
                self.log("没有可导出的数据")
                return False

            # 创建DataFrame
            df = pd.DataFrame(data_list)

            # 导出到Excel
            with pd.ExcelWriter(excel_file) as writer:
                df.to_excel(writer, sheet_name="账号数据", index=False)

            self.log(f"已导出账号数据到: {excel_file}")
            return True

        except Exception as e:
            self.log(f"导出Excel失败: {str(e)}")
            return False

    def export_to_csv(self, csv_file: str) -> bool:
        """
        导出账号数据到CSV

        Args:
            csv_file: CSV文件路径

        Returns:
            是否成功导出
        """
        try:
            # 准备数据
            data_list = self.get_all_accounts_data()
            if not data_list:
                self.log("没有可导出的数据")
                return False

            # 确定字段
            fields = list(data_list[0].keys())

            # 导出到CSV
            with open(csv_file, 'w', encoding='utf-8', newline='') as f:
                writer = csv.DictWriter(f, fieldnames=fields)
                writer.writeheader()
                writer.writerows(data_list)

            self.log(f"已导出账号数据到: {csv_file}")
            return True

        except Exception as e:
            self.log(f"导出CSV失败: {str(e)}")
            return False

    def update_daily_income(self, account: str, income: float) -> bool:
        """
        更新账号的每日收益数据（每天只添加一次昨日收益）

        Args:
            account: 账号名称
            income: 当日收益

        Returns:
            是否成功更新
        """
        try:
            # 获取账号数据
            account_data = self.get_account_data(account)
            if not account_data:
                account_data = {}

            # 获取当前日期
            today = datetime.now().strftime("%Y-%m-%d")

            # 初始化七日收益数据
            if "七日收益数据" not in account_data:
                account_data["七日收益数据"] = {}

            # 检查今天是否已经添加过数据，如果已添加则不再添加
            if today in account_data["七日收益数据"]:
                self.log(f"今日({today})已经添加过收益数据，不再重复添加")
                return True

            # 更新当日收益（昨日收益）
            account_data["七日收益数据"][today] = income
            self.log(f"已添加{today}的收益数据: {income}")

            # 只保留最近7天的数据
            seven_days_ago = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")
            keys_to_remove = []
            for date_key in account_data["七日收益数据"].keys():
                if date_key < seven_days_ago:
                    keys_to_remove.append(date_key)

            for key in keys_to_remove:
                del account_data["七日收益数据"][key]
                self.log(f"已删除过期的收益数据: {key}")

            # 更新账号数据
            return self.update_account_data(account, account_data)

        except Exception as e:
            self.log(f"更新每日收益数据失败: {str(e)}")
            return False

    def get_seven_day_income(self, account: str) -> Dict[str, float]:
        """
        获取账号的七日收益数据

        Args:
            account: 账号名称

        Returns:
            七日收益数据字典，格式为 {日期: 收益}
        """
        try:
            # 获取账号数据
            account_data = self.get_account_data(account)
            if not account_data or "七日收益数据" not in account_data:
                return {}

            return account_data["七日收益数据"]

        except Exception as e:
            self.log(f"获取七日收益数据失败: {str(e)}")
            return {}

    def get_income_trend(self, account: str) -> List[Dict[str, Any]]:
        """
        获取账号的收益趋势数据

        Args:
            account: 账号名称

        Returns:
            收益趋势数据列表，按日期排序
        """
        try:
            # 获取七日收益数据
            seven_day_income = self.get_seven_day_income(account)
            if not seven_day_income:
                return []

            # 转换为列表并排序
            trend_data = []
            for date, income in seven_day_income.items():
                trend_data.append({
                    "日期": date,
                    "收益": income
                })

            # 按日期排序
            trend_data.sort(key=lambda x: x["日期"])

            return trend_data

        except Exception as e:
            self.log(f"获取收益趋势数据失败: {str(e)}")
            return []

    def calculate_statistics(self) -> Dict[str, Any]:
        """
        计算账号数据统计信息

        Returns:
            统计信息字典
        """
        try:
            # 准备数据
            data_list = self.get_all_accounts_data()
            if not data_list:
                return {
                    "账号总数": 0,
                    "总收益": 0,
                    "平均收益": 0,
                    "最高收益": 0,
                    "最低收益": 0,
                    "最高收益账号": "",
                    "最低收益账号": "",
                    "七日总收益": 0,
                    "七日平均收益": 0,
                    "七日收益增长率": 0
                }

            # 计算统计信息
            total_accounts = len(data_list)
            total_income = 0
            max_income = 0
            min_income = float("inf")
            max_account = ""
            min_account = ""

            # 七日收益统计
            seven_day_total = 0
            seven_day_count = 0
            yesterday_total = 0
            week_ago_total = 0

            # 当前日期
            today = datetime.now()
            yesterday = (today - timedelta(days=1)).strftime("%Y-%m-%d")
            week_ago = (today - timedelta(days=7)).strftime("%Y-%m-%d")

            for item in data_list:
                # 总收益统计
                income = parse_income(item.get("总收益", 0))
                total_income += income

                if income > max_income:
                    max_income = income
                    max_account = item.get("账号", "")

                if income < min_income:
                    min_income = income
                    min_account = item.get("账号", "")

                # 七日收益统计
                account = item.get("账号", "")
                seven_day_data = self.get_seven_day_income(account)

                for date, daily_income in seven_day_data.items():
                    seven_day_total += daily_income
                    seven_day_count += 1

                    # 昨日收益
                    if date == yesterday:
                        yesterday_total += daily_income

                    # 一周前收益
                    if date == week_ago:
                        week_ago_total += daily_income

            # 计算平均值和增长率
            avg_income = total_income / total_accounts if total_accounts else 0
            seven_day_avg = seven_day_total / seven_day_count if seven_day_count else 0

            # 计算七日收益增长率
            growth_rate = 0
            if week_ago_total > 0 and yesterday_total > 0:
                growth_rate = (yesterday_total - week_ago_total) / week_ago_total * 100

            return {
                "账号总数": total_accounts,
                "总收益": total_income,
                "平均收益": avg_income,
                "最高收益": max_income,
                "最低收益": min_income if min_income != float("inf") else 0,
                "最高收益账号": max_account,
                "最低收益账号": min_account,
                "七日总收益": seven_day_total,
                "七日平均收益": seven_day_avg,
                "七日收益增长率": growth_rate
            }

        except Exception as e:
            self.log(f"计算统计信息失败: {str(e)}")
            return {
                "账号总数": 0,
                "总收益": 0,
                "平均收益": 0,
                "最高收益": 0,
                "最低收益": 0,
                "最高收益账号": "",
                "最低收益账号": "",
                "七日总收益": 0,
                "七日平均收益": 0,
                "七日收益增长率": 0
            }
