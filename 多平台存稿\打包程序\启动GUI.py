#!/usr/bin/env python3
"""
快速启动GUI界面
"""

import sys
import subprocess
from pathlib import Path

def main():
    """启动GUI界面"""
    try:
        # 获取当前目录
        current_dir = Path(__file__).parent
        main_script = current_dir / "统一自动打包.py"
        
        if not main_script.exists():
            print("错误: 找不到统一自动打包.py文件")
            input("按回车键退出...")
            return
        
        print("启动多平台存稿工具 - 统一自动打包器 GUI...")
        
        # 启动GUI
        subprocess.run([sys.executable, str(main_script), "--gui"])
        
    except Exception as e:
        print(f"启动失败: {e}")
        print("请尝试直接运行: python 统一自动打包.py --gui")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
