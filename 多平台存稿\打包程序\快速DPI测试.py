
import sys
import tkinter as tk

# 模拟打包环境
sys.frozen = True

# 测试字体和窗口大小设置
root = tk.Tk()

# 检查字体大小
if getattr(sys, 'frozen', False):
    font_size = 12
    window_size = "1400x1200"
else:
    font_size = 17
    window_size = "1800x1600"

root.geometry(window_size)
root.title(f"DPI测试 - 字体:{font_size}px 窗口:{window_size}")

label = tk.Label(root, text=f"测试字体大小: {font_size}px\n窗口大小: {window_size}", 
                font=("Microsoft YaHei", font_size))
label.pack(expand=True)

print(f"字体大小: {font_size}px")
print(f"窗口大小: {window_size}")
print("如果界面显示正常，说明DPI修复有效")

root.mainloop()
