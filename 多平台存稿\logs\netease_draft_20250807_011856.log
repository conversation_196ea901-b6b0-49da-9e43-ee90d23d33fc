[2025-08-07 01:19:01] [INFO] 🚀 启动多平台存稿工具...
[2025-08-07 01:19:01] [INFO] ✅ 定时任务管理器UI初始化完成
[2025-08-07 01:19:02] [INFO] ✅ 定时任务管理标签页创建完成
[SUCCESS] ✅ 升级版定时任务调度管理器初始化完成
[2025-08-07 01:19:01] [INFO] 已从toutiao平台加载存稿详情数据，共 83 个账号
[2025-08-07 01:19:02] [INFO] 🚀 定时任务调度器已启动
[2025-08-07 01:19:02] [DEBUG] 🔄 调度器主循环开始
[2025-08-07 01:19:02] [INFO] 已确保所有必要目录存在
[2025-08-07 01:19:02] [DEBUG] 🔍 [调试] 当前平台: toutiao
[2025-08-07 01:19:02] [DEBUG] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-08-07 01:19:02] [DEBUG] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-08-07 01:19:02] [SUCCESS] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-08-07 01:19:02] [DEBUG] 🔍 [调试] 更新账号树 - 当前平台: toutiao
[2025-08-07 01:19:02] [DEBUG] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-08-07 01:19:02] [DEBUG] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-08-07 01:19:02] [INFO] 已加载账号数据: 85 条记录
[2025-08-07 01:19:02] [DEBUG] 🔍 [调试] 账号数据为空或未加载
[2025-08-07 01:19:02] [SUCCESS] ✅ 初始UI更新完成
[2025-08-07 01:19:18] [INFO] 已选择账号: ***********
[2025-08-07 01:19:18] [INFO] 已选择账号: ***********
[2025-08-07 01:20:12] [DEBUG] 正在登录头条账号: ***********
[2025-08-07 01:20:12] [SUCCESS] 找到头条账号 *********** 的Cookie文件: D:/网易号全自动/头条号账号\***********.txt
[2025-08-07 01:20:12] [SUCCESS] 正在使用Cookie登录头条账号: ***********
[2025-08-07 01:20:12] [SUCCESS] Cookie文件路径: D:/网易号全自动/头条号账号\***********.txt
[2025-08-07 01:20:12] [SUCCESS] ✅ Cookie预检测通过，启动浏览器验证...
[2025-08-07 01:20:19] [SUCCESS] [toutiao] 浏览器驱动创建成功，使用端口: 9515
[2025-08-07 01:20:23] [SUCCESS] 正在加载Cookie文件: D:/网易号全自动/头条号账号\***********.txt
[2025-08-07 01:20:41] [SUCCESS] ✅ Cookie添加完成
[2025-08-07 01:20:42] [SUCCESS] ✅ 头条号快速登录成功 (等待0.5秒)
[2025-08-07 01:20:42] [SUCCESS] ✅ 头条账号 *********** 登录成功
[2025-08-07 01:21:30] [INFO] 跳过数据获取，浏览器将保持打开状态
[2025-08-07 01:21:36] [INFO] 已选择账号: 15722551968
[2025-08-07 01:21:36] [INFO] 已选择账号: 15722551968
[2025-08-07 01:21:37] [DEBUG] 🚀 开始为账号 15722551968 存稿
[2025-08-07 01:21:37] [INFO] 已确保所有必要目录存在
[2025-08-07 01:21:37] [INFO] 使用单线程模式处理账号
[2025-08-07 01:21:37] [DEBUG] 🔍 [DEBUG] 准备启动单线程任务，参数: 15722551968
[2025-08-07 01:21:37] [DEBUG] 🔍 [DEBUG] 单线程任务已启动
[2025-08-07 01:21:37] [DEBUG] 🔍 [DEBUG] _single_task_thread 开始执行，参数: 15722551968
[2025-08-07 01:21:37] [DEBUG] 📋 开始处理单个账号: 15722551968
[2025-08-07 01:21:37] [INFO] 视频分配方式: 随机分配
[2025-08-07 01:21:37] [DEBUG] 开始处理账号: 15722551968
[2025-08-07 01:21:37] [SUCCESS] 正在使用Cookie登录账号: 15722551968
[2025-08-07 01:21:37] [SUCCESS] ✅ Cookie预检测通过，启动浏览器验证...
[2025-08-07 01:21:44] [SUCCESS] 开始查询账号数据: 15722551968 [toutiao] 浏览器驱动创建成功，使用端口: 9516
[2025-08-07 01:21:48] [SUCCESS] 正在加载Cookie文件: D:/网易号全自动/头条号账号\15722551968.txt
[2025-08-07 01:21:50] [SUCCESS] ✅ Cookie添加完成
[2025-08-07 01:21:51] [SUCCESS] ✅ 头条号快速登录成功 (等待0.5秒)
[2025-08-07 01:21:51] [SUCCESS] 登录成功，开始处理视频: 15722551968
[2025-08-07 01:21:51] [INFO] 找到 652 个视频文件
[2025-08-07 01:21:51] [INFO] 已随机打乱 652 个视频文件的顺序
[2025-08-07 01:21:51] [DEBUG] 开始处理视频: 在犹太传统纪念日Tisha B'Av（圣殿被毁日，阿布月初九.mp4
[2025-08-07 01:21:53] [DEBUG] 🎬 开始头条号视频存稿流程
[2025-08-07 01:21:53] [DEBUG] 🔍 正在定位视频菜单...
[2025-08-07 01:21:53] [SUCCESS] ✅ 已点击视频菜单
[2025-08-07 01:21:56] [INFO] 🌐 当前不在上传页面，导航到视频上传页面...
[2025-08-07 01:22:00] [SUCCESS] ✅ 已导航到视频上传页面
[2025-08-07 01:22:00] [INFO] 📁 准备上传视频: 在犹太传统纪念日Tisha B'Av（圣殿被毁日，阿布月初九.mp4
[2025-08-07 01:22:00] [DEBUG] 🔍 找到上传按钮，准备点击...
[2025-08-07 01:22:01] [SUCCESS] ✅ 已点击上传视频按钮
[2025-08-07 01:22:03] [SUCCESS] ✅ 视频文件已选择并开始上传
[2025-08-07 01:22:03] [DEBUG] ⏳ 开始监控视频上传状态...
[2025-08-07 01:22:13] [WARNING] ⚠️ 状态元素暂时不可见，继续等待...
