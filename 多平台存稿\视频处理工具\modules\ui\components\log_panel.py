"""
日志面板组件 - 显示处理日志
"""

import tkinter as tk
from tkinter import ttk, scrolledtext

def create_log_panel(parent, ui_instance):
    """创建日志面板"""
    # 创建日志框架 - 不再需要LabelFrame，因为父容器已经是LabelFrame
    log_frame = ttk.Frame(parent)

    # 日志控制按钮 - 放在顶部
    log_button_frame = ttk.Frame(log_frame)
    log_button_frame.pack(fill=tk.X, padx=5, pady=5)

    # 清除日志按钮
    clear_button = ttk.Button(log_button_frame, text="清除日志", command=ui_instance.clear_log)
    clear_button.pack(side=tk.RIGHT)

    # 保存日志按钮
    save_button = ttk.Button(log_button_frame, text="保存日志", command=lambda: save_log(ui_instance))
    save_button.pack(side=tk.RIGHT, padx=5)

    # 创建日志文本区域 - 增加高度，确保在按钮下方，设置更大的字体
    # 创建自定义字体 - 使用与原始工具相同的字体
    import tkinter.font as tkfont
    # 确保使用系统默认字体大小
    font_size = 9  # 统一使用系统默认字体大小
    log_font = tkfont.Font(family="Courier New", size=font_size)  # 使用Courier New字体

    log_text = tk.Text(log_frame, wrap=tk.WORD, height=15, font=log_font)
    log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    log_text.config(state=tk.DISABLED)  # 设置为只读

    # 添加滚轮支持
    def _on_mousewheel(event):
        log_text.yview_scroll(int(-1*(event.delta/120)), "units")

    def _bind_mousewheel(event):
        log_text.bind_all("<MouseWheel>", _on_mousewheel)

    def _unbind_mousewheel(event):
        log_text.unbind_all("<MouseWheel>")

    log_text.bind('<Enter>', _bind_mousewheel)
    log_text.bind('<Leave>', _unbind_mousewheel)

    # 配置标签颜色
    log_text.tag_configure("success", foreground="#008000")  # 绿色
    log_text.tag_configure("error", foreground="#FF0000")    # 红色
    log_text.tag_configure("warning", foreground="#FF8C00")  # 橙色
    log_text.tag_configure("info", foreground="#0000FF")     # 蓝色
    log_text.tag_configure("section", foreground="#800080")  # 紫色

    # 保存日志文本框引用
    ui_instance.log_text = log_text

    # 不再添加初始日志消息，保持日志面板为空

    # 确保日志面板可见
    print("日志面板已创建")

    return log_frame

def save_log(ui_instance):
    """保存日志到文件"""
    try:
        from tkinter import filedialog
        import datetime

        # 获取当前时间作为文件名
        now = datetime.datetime.now()
        default_filename = f"log_{now.strftime('%Y%m%d_%H%M%S')}.txt"

        # 打开文件对话框
        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
            initialfile=default_filename,
            title="保存日志"
        )

        if filename:
            # 获取日志内容
            ui_instance.log_text.config(state=tk.NORMAL)
            log_content = ui_instance.log_text.get(1.0, tk.END)
            ui_instance.log_text.config(state=tk.DISABLED)

            # 写入文件
            with open(filename, "w", encoding="utf-8") as f:
                f.write(log_content)

            ui_instance.log(f"✅ 日志已保存到: {filename}", "success")
    except Exception as e:
        ui_instance.log(f"❌ 保存日志失败: {str(e)}", "error")
