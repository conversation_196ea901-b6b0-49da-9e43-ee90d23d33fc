"""
GPU设置组件 - GPU加速相关设置
"""

import os
import threading
import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
from typing import Dict, List, Any, Callable

def create_gpu_settings(parent, ui_instance):
    """创建GPU设置面板"""
    # 创建GPU设置框架
    gpu_frame = ttk.LabelFrame(parent, text="GPU加速设置")
    gpu_frame.pack(fill=tk.X, padx=10, pady=10, anchor=tk.N)

    # 启用GPU加速复选框
    gpu_check = ttk.Checkbutton(gpu_frame, text="启用GPU加速",
                              variable=ui_instance.enable_gpu_acceleration,
                              command=lambda: check_gpu_availability(ui_instance))
    gpu_check.pack(anchor=tk.W, padx=10, pady=5)

    # GPU设备选择框架
    gpu_device_frame = ttk.Frame(gpu_frame)
    gpu_device_frame.pack(fill=tk.X, padx=10, pady=5)

    # GPU设备标签
    ttk.Label(gpu_device_frame, text="GPU设备:").pack(side=tk.LEFT)

    # GPU设备下拉框
    gpu_device_combo = ttk.Combobox(gpu_device_frame, textvariable=ui_instance.gpu_device,
                                  values=["auto"],
                                  state="readonly", width=15)
    gpu_device_combo.pack(side=tk.LEFT, padx=10)

    # 保存下拉框引用
    ui_instance.gpu_device_combo = gpu_device_combo

    # 检测GPU按钮
    detect_button = ttk.Button(gpu_device_frame, text="检测GPU",
                             command=lambda: detect_gpus(ui_instance), width=10)
    detect_button.pack(side=tk.LEFT, padx=5)

    # GPU状态标签
    gpu_status_label = ttk.Label(gpu_device_frame, text="GPU状态: 未检测")
    gpu_status_label.pack(side=tk.LEFT, padx=10)

    # 保存标签引用
    ui_instance.gpu_status_label = gpu_status_label

    # 添加工具提示
    if hasattr(ui_instance, 'create_tooltip'):
        ui_instance.create_tooltip(gpu_check, "启用GPU加速可以提高视频处理速度，但需要NVIDIA GPU")
        ui_instance.create_tooltip(gpu_device_combo, "选择要使用的GPU设备，auto表示自动选择")
        ui_instance.create_tooltip(detect_button, "检测系统中可用的GPU设备")

    return gpu_frame

def check_gpu_availability(ui_instance):
    """检查GPU可用性并更新UI"""
    if ui_instance.enable_gpu_acceleration.get():
        # 如果启用了GPU加速，但没有检测到GPU，只在日志中提示用户，不显示弹窗
        if len(ui_instance.gpu_device_combo['values']) <= 1:
            ui_instance.log("⚠️ 您启用了GPU加速，但尚未检测到GPU设备")
            ui_instance.log("请点击'检测GPU'按钮检测可用的GPU设备")
            # 移除了弹窗提示
    else:
        # 如果禁用了GPU加速，更新状态
        ui_instance.gpu_status_label.config(text="GPU状态: 已禁用")

    # 保存配置
    ui_instance.config_manager.save_config()

def detect_gpus(ui_instance):
    """检测可用的GPU设备"""
    ui_instance.log("正在检测GPU设备...")

    # 禁用检测按钮，防止重复点击
    for widget in ui_instance.root.winfo_children():
        if isinstance(widget, ttk.Button) and widget.cget('text') == "检测GPU":
            widget.configure(state="disabled")
            ui_instance.root.update_idletasks()
            break

    # 更新状态
    ui_instance.gpu_status_label.config(text="GPU状态: 正在检测...")
    ui_instance.root.update_idletasks()

    # 创建一个线程来执行GPU检测
    detection_thread = threading.Thread(target=lambda: _gpu_detection_worker(ui_instance))
    detection_thread.daemon = True
    detection_thread.start()

def _gpu_detection_worker(ui_instance):
    """GPU检测工作线程"""
    # 初始化检测结果
    detection_result = {
        "found": False,
        "devices": ["auto"]  # 始终包含"auto"选项
    }

    # 创建一个事件来标记检测完成
    detection_complete = threading.Event()

    # PyTorch检测函数
    def detect_pytorch():
        try:
            # 尝试导入torch检测GPU
            import torch
            if torch.cuda.is_available():
                gpu_count = torch.cuda.device_count()
                if gpu_count > 0:
                    detection_result["found"] = True
                    ui_instance.log(f"✅ 检测到 {gpu_count} 个CUDA设备")

                    # 添加每个GPU设备
                    for i in range(gpu_count):
                        device_name = torch.cuda.get_device_name(i)
                        device_id = f"cuda:{i}"
                        # 将设备名称添加到设备ID中，方便在下拉框中显示
                        device_display = f"cuda:{i} ({device_name})"
                        detection_result["devices"].append(device_display)
                        ui_instance.log(f"  - GPU {i}: {device_name}")

                        # 保存设备名称到ui_instance，以便在设置界面中使用
                        if not hasattr(ui_instance, 'gpu_device_names'):
                            ui_instance.gpu_device_names = {}
                        ui_instance.gpu_device_names[device_id] = device_name
                else:
                    ui_instance.log("❌ 未检测到CUDA设备")
            else:
                ui_instance.log("❌ CUDA不可用")
        except ImportError:
            ui_instance.log("⚠️ 未安装PyTorch")
        except Exception as e:
            ui_instance.log(f"⚠️ PyTorch检测出错: {str(e)}")
        finally:
            # 标记检测完成
            detection_complete.set()

    # 启动PyTorch检测线程
    torch_thread = threading.Thread(target=detect_pytorch)
    torch_thread.daemon = True
    torch_thread.start()

    # 等待检测完成，最多等待10秒
    detection_complete.wait(10)

    # 如果PyTorch没有找到GPU，尝试使用其他方法检测
    if not detection_result["found"]:
        # 尝试使用系统命令检测NVIDIA GPU
        try:
            # 在Windows上使用nvidia-smi命令
            result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                # 找到NVIDIA GPU
                ui_instance.log("✅ 通过nvidia-smi检测到NVIDIA GPU")
                detection_result["found"] = True

                # 提取GPU名称
                import re
                match = re.search(r'(\| NVIDIA[^\|]+)', result.stdout)
                if match:
                    gpu_name = match.group(1).strip()
                    # 将设备名称添加到设备ID中，方便在下拉框中显示
                    device_display = f"cuda:0 ({gpu_name})"
                    detection_result["devices"].append(device_display)
                    ui_instance.log(f"  - GPU 0: {gpu_name}")

                    # 保存设备名称到ui_instance，以便在设置界面中使用
                    if not hasattr(ui_instance, 'gpu_device_names'):
                        ui_instance.gpu_device_names = {}
                    ui_instance.gpu_device_names["cuda:0"] = gpu_name
                else:
                    # 如果无法提取名称，仍然添加设备ID
                    detection_result["devices"].append("cuda:0")
                    ui_instance.log(f"  - GPU 0: NVIDIA GPU")
        except (subprocess.SubprocessError, FileNotFoundError, TimeoutError):
            ui_instance.log("❌ 未通过nvidia-smi检测到NVIDIA GPU")
        except Exception as e:
            ui_instance.log(f"⚠️ nvidia-smi检测出错: {str(e)}")

    # 在主线程中更新UI
    ui_instance.root.after(0, lambda: _update_gpu_detection_ui(ui_instance, detection_result["devices"], detection_result["found"]))

def _update_gpu_detection_ui(ui_instance, gpu_devices, found):
    """更新GPU检测结果到UI"""
    # 更新GPU设备下拉列表
    ui_instance.gpu_device_combo['values'] = gpu_devices

    # 如果有GPU设备，默认选择第一个
    if len(gpu_devices) > 1:
        ui_instance.gpu_device.set(gpu_devices[1])  # 选择第一个实际GPU设备
        ui_instance.enable_gpu_acceleration.set(True)
        ui_instance.gpu_status_label.config(text=f"GPU状态: 可用 ({len(gpu_devices)-1}个设备)")
    else:
        ui_instance.gpu_device.set("auto")
        ui_instance.gpu_status_label.config(text="GPU状态: 不可用")

    # 保存配置
    ui_instance.config_manager.save_config()

    # 重新启用检测按钮
    for widget in ui_instance.root.winfo_children():
        if isinstance(widget, ttk.Button) and widget.cget('text') == "检测GPU":
            widget.configure(state="normal")
            break
