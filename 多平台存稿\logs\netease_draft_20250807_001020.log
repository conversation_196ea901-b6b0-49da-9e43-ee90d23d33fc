[2025-08-07 00:10:25] [INFO] 🚀 启动多平台存稿工具...
[2025-08-07 00:10:25] [INFO] ✅ 定时任务管理器UI初始化完成
[2025-08-07 00:10:26] [INFO] ✅ 定时任务管理标签页创建完成
[2025-08-07 00:13:03] [INFO] ✅ 定时任务管理器UI清理完成
25-08-07 00:10:25] [INFO] 已从toutiao平台加载存稿详情数据，共 83 个账号
[2025-08-07 00:10:26] [INFO] 🚀 定时任务调度器已启动
[2025-08-07 00:10:26] [DEBUG] 🔄 调度器主循环开始
[2025-08-07 00:10:26] [INFO] 已确保所有必要目录存在
[2025-08-07 00:10:26] [DEBUG] 🔍 [调试] 当前平台: toutiao
[2025-08-07 00:10:26] [DEBUG] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-08-07 00:10:26] [DEBUG] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-08-07 00:10:26] [SUCCESS] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-08-07 00:10:26] [DEBUG] 🔍 [调试] 更新账号树 - 当前平台: toutiao
[2025-08-07 00:10:26] [DEBUG] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-08-07 00:10:26] [DEBUG] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-08-07 00:10:26] [INFO] 已加载账号数据: 85 条记录
[2025-08-07 00:10:26] [DEBUG] 🔍 [调试] 账号数据为空或未加载
[2025-08-07 00:10:26] [SUCCESS] ✅ 初始UI更新完成
[2025-08-07 00:10:35] [DEBUG] 正在登录头条账号: ***********
[2025-08-07 00:10:35] [SUCCESS] 找到头条账号 *********** 的Cookie文件: D:/网易号全自动/头条号账号\***********.txt
[2025-08-07 00:10:35] [SUCCESS] 正在使用Cookie登录头条账号: ***********
[2025-08-07 00:10:35] [SUCCESS] Cookie文件路径: D:/网易号全自动/头条号账号\***********.txt
[2025-08-07 00:10:35] [SUCCESS] ✅ Cookie预检测通过，启动浏览器验证...
[2025-08-07 00:10:42] [SUCCESS] [toutiao] 浏览器驱动创建成功，使用端口: 9515
[2025-08-07 00:10:46] [SUCCESS] 正在加载Cookie文件: D:/网易号全自动/头条号账号\***********.txt
[2025-08-07 00:10:56] [SUCCESS] ✅ Cookie添加完成
[2025-08-07 00:10:57] [SUCCESS] ✅ 头条号快速登录成功 (等待0.5秒)
[2025-08-07 00:10:57] [SUCCESS] ✅ 头条账号 *********** 登录成功
[2025-08-07 00:11:13] [INFO] 跳过数据获取，浏览器将保持打开状态
[2025-08-07 00:11:53] [INFO] 已选择账号: ***********
[2025-08-07 00:11:54] [DEBUG] 🚀 开始为账号 *********** 存稿
[2025-08-07 00:11:54] [INFO] 已确保所有必要目录存在
[2025-08-07 00:11:54] [INFO] 使用单线程模式处理账号
[2025-08-07 00:11:54] [DEBUG] 🔍 [DEBUG] 准备启动单线程任务，参数: ***********
[2025-08-07 00:11:54] [DEBUG] 🔍 [DEBUG] _single_task_thread 开始执行，参数: ***********
[2025-08-07 00:11:54] [DEBUG] 📋 开始处理单个账号: ***********
[2025-08-07 00:11:54] [INFO] 视频分配方式: 随机分配
[2025-08-07 00:11:54] [DEBUG] 开始处理账号: ***********
[2025-08-07 00:11:54] [SUCCESS] 正在使用Cookie登录账号: ***********
[2025-08-07 00:11:54] [SUCCESS] ✅ Cookie预检测通过，启动浏览器验证...
[2025-08-07 00:12:01] [SUCCESS] 开始查询账号数据: *********** [toutiao] 浏览器驱动创建成功，使用端口: 9516
[2025-08-07 00:12:05] [SUCCESS] 正在加载Cookie文件: D:/网易号全自动/头条号账号\***********.txt
[2025-08-07 00:12:13] [SUCCESS] ✅ Cookie添加完成
[2025-08-07 00:12:14] [SUCCESS] ✅ 头条号快速登录成功 (等待0.5秒)
[2025-08-07 00:12:14] [SUCCESS] 登录成功，开始处理视频: ***********
[2025-08-07 00:12:14] [INFO] 找到 652 个视频文件
[2025-08-07 00:12:14] [INFO] 已随机打乱 652 个视频文件的顺序
[2025-08-07 00:12:14] [DEBUG] 开始处理视频: 俄罗斯8.7级强震引发多国海啸预警，为何中国淡定应对？(1).mp4
[2025-08-07 00:12:16] [DEBUG] 🎬 开始头条号视频存稿流程
[2025-08-07 00:12:16] [DEBUG] 🔍 正在定位视频菜单...
[2025-08-07 00:12:16] [SUCCESS] ✅ 已点击视频菜单
[2025-08-07 00:12:19] [INFO] 🌐 当前不在上传页面，导航到视频上传页面...
[2025-08-07 00:12:23] [SUCCESS] ✅ 已导航到视频上传页面
[2025-08-07 00:12:23] [INFO] 📁 准备上传视频: 俄罗斯8.7级强震引发多国海啸预警，为何中国淡定应对？(1).mp4
[2025-08-07 00:12:23] [DEBUG] 🔍 找到上传按钮，准备点击...
[2025-08-07 00:12:23] [SUCCESS] ✅ 已点击上传视频按钮
[2025-08-07 00:12:25] [SUCCESS] ✅ 视频文件已选择并开始上传
[2025-08-07 00:12:25] [DEBUG] ⏳ 开始监控视频上传状态...
[2025-08-07 00:12:25] [INFO] 📊 当前上传状态: 上传中… 0%
[2025-08-07 00:12:25] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 00:12:30] [INFO] 📊 当前上传状态: 上传中… 51.16%
[2025-08-07 00:12:30] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 00:12:35] [INFO] 📊 当前上传状态: 上传中… 100%
[2025-08-07 00:12:35] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 00:12:40] [SUCCESS] 📊 当前上传状态: 上传成功
[2025-08-07 00:12:40] [SUCCESS] ✅ 视频上传成功！
[2025-08-07 00:12:40] [DEBUG] 📷 开始上传视频封面...
[2025-08-07 00:12:40] [SUCCESS] ✅ 已点击上传封面按钮
[2025-08-07 00:12:42] [SUCCESS] ✅ 已选择本地上传
[2025-08-07 00:12:43] [SUCCESS] ✅ 封面文件已选择
[2025-08-07 00:12:43] [DEBUG] 🔍 开始检测封面canvas加载状态...
[2025-08-07 00:12:43] [ERROR] ❌ 未找到Canvas元素
[2025-08-07 00:12:43] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-07 00:12:44] [ERROR] ❌ 未找到Canvas元素
[2025-08-07 00:12:44] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-07 00:12:45] [ERROR] ❌ 未找到Canvas元素
[2025-08-07 00:12:45] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-07 00:12:46] [ERROR] ❌ 未找到Canvas元素
[2025-08-07 00:12:46] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-07 00:12:47] [ERROR] ❌ 未找到Canvas元素
[2025-08-07 00:12:47] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-07 00:12:48] [ERROR] ❌ 未找到Canvas元素
[2025-08-07 00:12:48] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-07 00:12:49] [ERROR] ❌ 未找到Canvas元素
[2025-08-07 00:12:49] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-07 00:12:50] [ERROR] ❌ 未找到Canvas元素
[2025-08-07 00:12:50] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-07 00:12:51] [ERROR] ❌ 未找到Canvas元素
[2025-08-07 00:12:51] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-07 00:12:52] [ERROR] ❌ 未找到Canvas元素
[2025-08-07 00:12:52] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-07 00:12:53] [ERROR] ❌ 未找到Canvas元素
[2025-08-07 00:12:53] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-07 00:12:54] [ERROR] ❌ 未找到Canvas元素
[2025-08-07 00:12:54] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-07 00:12:55] [ERROR] ❌ 未找到Canvas元素
[2025-08-07 00:12:56] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-07 00:12:57] [ERROR] ❌ 未找到Canvas元素
[2025-08-07 00:12:57] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-07 00:12:58] [ERROR] ❌ 未找到Canvas元素
[2025-08-07 00:12:58] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-07 00:12:59] [ERROR] ❌ canvas加载检测超时（15秒）
[2025-08-07 00:12:59] [WARNING] ⚠️ 封面canvas可能未完全加载，但继续后续操作
[2025-08-07 00:13:02] [DEBUG] 正在关闭程序，清理后台资源...
[2025-08-07 00:13:03] [DEBUG] 🔄 调度器主循环结束
[2025-08-07 00:13:03] [DEBUG] 正在关闭任务执行器...
[2025-08-07 00:13:03] [INFO] 任务执行器已关闭
[2025-08-07 00:13:03] [INFO] ⏹️ 定时任务调度器已停止
[2025-08-07 00:13:03] [SUCCESS] ✅ 调度器管理器清理完成
[2025-08-07 00:13:03] [SUCCESS] ✅ 定时任务管理器UI清理完成
[2025-08-07 00:13:03] [DEBUG] 🧹 开始清理所有资源...
[2025-08-07 00:13:03] [DEBUG] ⏹️ 正在停止存稿任务...
[2025-08-07 00:13:03] [INFO] ⏹️ 存稿任务已停止
[2025-08-07 00:13:03] [DEBUG] 正在停止并发管理器...
[2025-08-07 00:13:03] [SUCCESS] ✅ 运行中的任务已停止
[2025-08-07 00:13:03] [SUCCESS] ✅ 定时器线程已清理（升级版系统自动管理）
[2025-08-07 00:13:03] [ERROR] ❌ 上传视频封面失败: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x7ff6c2416f75+76917]
	GetHandleVerifier [0x0x7ff6c2416fd0+77008]
	(No symbol) [0x0x7ff6c21c9dea]
	(No symbol) [0x0x7ff6c21b5f15]
	(No symbol) [0x0x7ff6c21dabf4]
	(No symbol) [0x0x7ff6c224fa85]
	(No symbol) [0x0x7ff6c226ff72]
	(No symbol) [0x0x7ff6c2248243]
	(No symbol) [0x0x7ff6c2211431]
	(No symbol) [0x0x7ff6c22121c3]
	GetHandleVerifier [0x0x7ff6c26ed2ad+3051437]
	GetHandleVerifier [0x0x7ff6c26e7903+3028483]
	GetHandleVerifier [0x0x7ff6c270589d+3151261]
	GetHandleVerifier [0x0x7ff6c243183e+185662]
	GetHandleVerifier [0x0x7ff6c24396ff+218111]
	GetHandleVerifier [0x0x7ff6c241faf4+112628]
	GetHandleVerifier [0x0x7ff6c241fca9+113065]
	GetHandleVerifier [0x0x7ff6c2406c78+10616]
	BaseThreadInitThunk [0x0x7ffcc2f7e8d7+23]
	RtlUserThreadStart [0x0x7ffcc4e5c34c+44]

[2025-08-07 00:13:03] [WARNING] ⚠️ 上传封面失败，继续存稿流程
[2025-08-07 00:13:03] [DEBUG] 💾 开始保存草稿...
[2025-08-07 00:13:03] [ERROR] ❌ 保存草稿失败: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x7ff6c2416f75+76917]
	GetHandleVerifier [0x0x7ff6c2416fd0+77008]
	(No symbol) [0x0x7ff6c21c9c1c]
	(No symbol) [0x0x7ff6c221055f]
	(No symbol) [0x0x7ff6c2248332]
	(No symbol) [0x0x7ff6c2242e53]
	(No symbol) [0x0x7ff6c2241f19]
	(No symbol) [0x0x7ff6c2194b05]
	GetHandleVerifier [0x0x7ff6c26ed2ad+3051437]
	GetHandleVerifier [0x0x7ff6c26e7903+3028483]
	GetHandleVerifier [0x0x7ff6c270589d+3151261]
	GetHandleVerifier [0x0x7ff6c243183e+185662]
	GetHandleVerifier [0x0x7ff6c24396ff+218111]
	(No symbol) [0x0x7ff6c2193b00]
	GetHandleVerifier [0x0x7ff6c2805f18+4201496]
	BaseThreadInitThunk [0x0x7ffcc2f7e8d7+23]
	RtlUserThreadStart [0x0x7ffcc4e5c34c+44]

[2025-08-07 00:13:03] [WARNING] ⚠️ 第1次尝试失败，2次重试机会剩余
[2025-08-07 00:13:03] [INFO] ⏳ 等待2秒后重试...
[2025-08-07 00:13:03] [SUCCESS] ✅ Chrome进程强制清理完成
[2025-08-07 00:13:03] [SUCCESS] ✅ 浏览器进程已清理
[2025-08-07 00:13:04] [SUCCESS] ✅ 日志处理线程已清理
[2025-08-07 00:13:04] [SUCCESS] ✅ 存稿处理器已清理
[2025-08-07 00:13:04] [SUCCESS] ✅ 线程池已清理
[2025-08-07 00:13:04] [DEBUG] 正在停止串行刷新工作线程...
[2025-08-07 00:13:05] [DEBUG] 💾 开始保存草稿...
[2025-08-07 00:13:07] [WARNING] ⚠️ 仍有 3 个线程未结束
[2025-08-07 00:13:08] [SUCCESS] ✅ 临时文件已清理
[2025-08-07 00:13:08] [SUCCESS] ✅ 资源清理完成
[2025-08-07 00:13:08] [SUCCESS] ✅ 设置已静默保存
[2025-08-07 00:13:08] [SUCCESS] 程序清理完成，正在退出...
