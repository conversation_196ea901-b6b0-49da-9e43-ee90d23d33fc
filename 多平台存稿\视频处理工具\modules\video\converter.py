"""
视频格式转换模块 - 处理视频格式转换
"""

import os
import subprocess
import shutil
from typing import Dict, Any

def convert_video(processor, 
                 video_path: str, 
                 output_path: str, 
                 use_gpu: bool = False, 
                 gpu_device: str = "auto", 
                 memory_limit: int = 0) -> Dict[str, Any]:
    """
    转换视频格式
    
    Args:
        processor: 视频处理器实例
        video_path: 源视频文件路径
        output_path: 输出视频文件路径
        use_gpu: 是否使用GPU加速
        gpu_device: GPU设备
        memory_limit: 内存限制(MB)
        
    Returns:
        Dict: 转换结果，包含success、message等信息
    """
    try:
        # 优化的视频格式转换
        convert_cmd = [
            'ffmpeg',
            '-i', video_path,
            '-y'  # 覆盖输出文件
        ]

        # 如果启用了GPU加速，尝试使用GPU编码
        if use_gpu and gpu_device != "auto":
            # 检查是否是NVIDIA GPU (CUDA)
            if gpu_device.startswith("cuda:"):
                # 使用NVIDIA GPU加速
                processor.logger(f"使用NVIDIA GPU加速转换视频格式")
                convert_cmd.extend([
                    '-hwaccel', 'cuda',    # GPU解码加速
                    '-c:v', 'h264_nvenc',  # 使用NVIDIA硬件编码
                    '-preset', 'p4',       # 使用性能预设（更快）
                    '-profile:v', 'main',  # 使用main profile
                    '-c:a', 'copy'         # 复制音频流，避免重编码
                ])
            else:
                # 其他GPU类型，使用默认复制
                processor.logger(f"不支持的GPU类型: {gpu_device}，使用流复制")
                convert_cmd.extend(['-c', 'copy'])
        else:
            # 不使用GPU，直接复制流（最快的方式）
            convert_cmd.extend(['-c', 'copy'])

        # 添加输出路径
        convert_cmd.append(output_path)

        # 如果设置了内存限制，添加内存限制参数
        if memory_limit > 0:
            # 转换为字节
            mem_limit_bytes = memory_limit * 1024 * 1024
            convert_cmd.insert(-1, "-max_memory")
            convert_cmd.insert(-1, str(mem_limit_bytes))

        # 执行转换，设置超时防止卡死
        try:
            processor.logger(f"执行FFmpeg格式转换命令: {' '.join(convert_cmd)}")
            convert_result = subprocess.run(convert_cmd, capture_output=True, text=True, timeout=600)

            if convert_result.returncode == 0:
                processor.logger("FFmpeg格式转换成功")
                return {
                    'success': True,
                    'message': "视频格式转换成功"
                }
            else:
                processor.logger(f"FFmpeg格式转换失败: 返回码={convert_result.returncode}")
                if convert_result.stderr:
                    processor.logger(f"FFmpeg错误输出: {convert_result.stderr}")
                return {
                    'success': False,
                    'message': f"格式转换失败: {convert_result.stderr}"
                }
        except subprocess.TimeoutExpired:
            processor.logger(f"FFmpeg格式转换超时: {video_path}")
            return {
                'success': False,
                'message': "格式转换超时"
            }
    except Exception as e:
        return {
            'success': False,
            'message': f"格式转换失败: {str(e)}"
        }
