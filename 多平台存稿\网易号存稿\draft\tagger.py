"""
标签生成模块 - 负责从视频标题生成标签
"""

import re
import jieba
from typing import List

def generate_tags(title: str) -> List[str]:
    """
    根据标题智能生成标签

    Args:
        title: 视频标题

    Returns:
        标签列表
    """
    # 使用jieba精确模式分词
    words = list(jieba.cut(title, cut_all=False))

    # 这些词在标签中通常没有单独价值，但不再过滤掉所有词
    minimal_stop_words = ['的', '了', '和', '与', '这', '那']
    all_words = [word for word in words if len(word) >= 1 and word not in minimal_stop_words]

    # 根据标题内容提取关键词
    tags = []

    # 1. 先取标题作为第一个标签，确保有一个核心标签
    if len(title) >= 2 and len(title) <= 8:
        tags.append(title)

    # 2. 选择3-4字的长词
    long_words = [word for word in all_words if 3 <= len(word) <= 4]
    for word in long_words:
        if word not in tags and len(tags) < 5:
            tags.append(word)

    # 3. 选择2-3字的短词
    short_words = [word for word in all_words if 2 <= len(word) <= 3]
    for word in short_words:
        if word not in tags and len(tags) < 5:
            tags.append(word)

    # 4. 如果标签仍然不足5个，尝试组合相邻的词（不管长度）
    if len(tags) < 3 and len(all_words) >= 2:
        for i in range(len(all_words) - 1):
            combined = all_words[i] + all_words[i+1]
            if combined not in tags:
                tags.append(combined)
                if len(tags) >= 5:
                    break

    # 5. 再尝试作为单词添加
    if len(tags) < 3 and all_words:
        for word in all_words:
            if word not in tags and len(tags) < 5:
                tags.append(word)

    # 6. 如果还是不够，再把标题切分成几部分作为标签
    if len(tags) < 3 and len(title) > 5:
        # 将标题分成三段
        third = len(title) // 3
        if third >= 2:
            for i in range(3):
                start = i * third
                end = (i + 1) * third if i < 2 else len(title)
                segment = title[start:end]
                if len(segment) >= 2 and segment not in tags:
                    tags.append(segment)
                    if len(tags) >= 5:
                        break

    # 7. 标题中的数字可能是重要信息（如日期、时间、数量），将它们添加为标签
    number_patterns = re.findall(r'\d+', title)
    for num in number_patterns:
        # 查找数字周围的上下文
        index = title.find(num)
        start = max(0, index - 2)
        end = min(len(title), index + len(num) + 2)
        context = title[start:end]
        if context not in tags and len(tags) < 5:
            tags.append(context)

    # 确保至少有3个标签
    if len(tags) < 3:
        # 如果实在不够，重复一些标签或者使用标题的不同部分
        for i in range(min(3, len(title) - 1)):
            subset = title[i:i+min(5, len(title)-i)]
            if subset not in tags:
                tags.append(subset)
            if len(tags) >= 5:
                break

    # 调整标签顺序，使长标签在前面
    tags.sort(key=len, reverse=True)

    # 确保标签数量至少为3个，特别是在并发模式下
    if len(tags) < 3:
        # 如果经过所有处理后标签数量仍然不足3个，添加默认标签
        default_tags = ["视频", "精彩", "推荐"]
        for tag in default_tags:
            if tag not in tags and len(tags) < 3:
                tags.append(tag)

    # 返回标签，确保至少3个，最多5个
    return tags[:5]
