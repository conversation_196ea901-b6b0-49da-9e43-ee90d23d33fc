[2025-08-07 01:46:14] [INFO] 🚀 启动多平台存稿工具...
[2025-08-07 01:46:14] [INFO] ✅ 定时任务管理器UI初始化完成
[2025-08-07 01:46:15] [INFO] ✅ 定时任务管理标签页创建完成
[2025-08-07 02:46:49] [INFO] ✅ 定时任务管理器UI清理完成
25-08-07 01:46:14] [INFO] 已从toutiao平台加载存稿详情数据，共 84 个账号
[2025-08-07 01:46:15] [INFO] 🚀 定时任务调度器已启动
[2025-08-07 01:46:15] [DEBUG] 🔄 调度器主循环开始
[2025-08-07 01:46:15] [INFO] 已确保所有必要目录存在
[2025-08-07 01:46:15] [DEBUG] 🔍 [调试] 当前平台: toutiao
[2025-08-07 01:46:15] [DEBUG] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-08-07 01:46:15] [DEBUG] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-08-07 01:46:15] [SUCCESS] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-08-07 01:46:15] [DEBUG] 🔍 [调试] 更新账号树 - 当前平台: toutiao
[2025-08-07 01:46:15] [DEBUG] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-08-07 01:46:15] [DEBUG] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-08-07 01:46:15] [INFO] 已加载账号数据: 85 条记录
[2025-08-07 01:46:15] [DEBUG] 🔍 [调试] 账号数据为空或未加载
[2025-08-07 01:46:15] [SUCCESS] ✅ 初始UI更新完成
[2025-08-07 01:46:23] [INFO] 已选择账号: ***********
[2025-08-07 01:46:27] [DEBUG] 🚀 开始为账号 *********** 存稿
[2025-08-07 01:46:27] [INFO] 已确保所有必要目录存在
[2025-08-07 01:46:27] [INFO] 使用单线程模式处理账号
[2025-08-07 01:46:27] [DEBUG] 🔍 [DEBUG] 准备启动单线程任务，参数: ***********
[2025-08-07 01:46:27] [DEBUG] 🔍 [DEBUG] 单线程任务已启动
[2025-08-07 01:46:27] [DEBUG] 🔍 [DEBUG] _single_task_thread 开始执行，参数: ***********
[2025-08-07 01:46:27] [DEBUG] 📋 开始处理单个账号: ***********
[2025-08-07 01:46:27] [INFO] 视频分配方式: 随机分配
[2025-08-07 01:46:27] [DEBUG] 开始处理账号: ***********
[2025-08-07 01:46:27] [SUCCESS] 正在使用Cookie登录账号: ***********
[2025-08-07 01:46:27] [SUCCESS] ✅ Cookie预检测通过，启动浏览器验证...
[2025-08-07 01:46:28] [INFO] 打开存稿设置对话框
[2025-08-07 01:46:30] [SUCCESS] ✅ 存稿设置已成功保存
[2025-08-07 01:46:34] [SUCCESS] 开始查询账号数据: *********** [toutiao] 浏览器驱动创建成功，使用端口: 9515
[2025-08-07 01:46:38] [SUCCESS] 正在加载Cookie文件: D:/网易号全自动/头条号账号\***********.txt
[2025-08-07 01:46:47] [SUCCESS] ✅ Cookie添加完成
[2025-08-07 01:46:48] [SUCCESS] ✅ 头条号快速登录成功 (等待0.5秒)
[2025-08-07 01:46:48] [SUCCESS] 登录成功，开始处理视频: ***********
[2025-08-07 01:46:48] [INFO] 找到 652 个视频文件
[2025-08-07 01:46:48] [INFO] 已随机打乱 652 个视频文件的顺序
[2025-08-07 01:46:48] [DEBUG] 🔄 开始第1/3轮处理
[2025-08-07 01:46:48] [DEBUG] 开始处理视频: 下雨天一定要谨慎驾驶，减速慢行啊！.mp4
[2025-08-07 01:46:48] [DEBUG] 🎬 开始头条号视频存稿流程
[2025-08-07 01:46:48] [DEBUG] 🔍 正在定位视频菜单...
[2025-08-07 01:46:49] [SUCCESS] ✅ 已点击视频菜单
[2025-08-07 01:46:52] [INFO] 🌐 当前不在上传页面，导航到视频上传页面...
[2025-08-07 01:46:56] [SUCCESS] ✅ 已导航到视频上传页面
[2025-08-07 01:46:56] [INFO] 📁 准备上传视频: 下雨天一定要谨慎驾驶，减速慢行啊！.mp4
[2025-08-07 01:46:56] [DEBUG] 🔍 找到上传按钮，准备点击...
[2025-08-07 01:46:56] [SUCCESS] ✅ 已点击上传视频按钮
[2025-08-07 01:46:58] [SUCCESS] ✅ 视频文件已选择并开始上传
[2025-08-07 01:46:58] [DEBUG] ⏳ 开始监控视频上传状态...
[2025-08-07 01:46:58] [INFO] 📊 当前上传状态: 上传中… 0%
[2025-08-07 01:46:58] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 01:47:03] [INFO] 📊 当前上传状态: 上传中… 12.2%
[2025-08-07 01:47:03] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 01:47:08] [INFO] 📊 当前上传状态: 上传中… 25.17%
[2025-08-07 01:47:08] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 01:47:13] [INFO] 📊 当前上传状态: 上传中… 38.19%
[2025-08-07 01:47:13] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 01:47:18] [INFO] 📊 当前上传状态: 上传中… 51.04%
[2025-08-07 01:47:18] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 01:47:23] [INFO] 📊 当前上传状态: 上传中… 66.31%
[2025-08-07 01:47:23] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 01:47:28] [INFO] 📊 当前上传状态: 上传中… 79.39%
[2025-08-07 01:47:28] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 01:47:33] [INFO] 📊 当前上传状态: 上传中… 92.23%
[2025-08-07 01:47:33] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 01:47:38] [INFO] 📊 当前上传状态: 上传中… 100%
[2025-08-07 01:47:38] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 01:47:43] [SUCCESS] 📊 当前上传状态: 上传成功
[2025-08-07 01:47:43] [SUCCESS] ✅ 视频上传成功！
[2025-08-07 01:47:43] [DEBUG] 📷 开始上传视频封面...
[2025-08-07 01:47:43] [SUCCESS] ✅ 已点击上传封面按钮
[2025-08-07 01:47:45] [SUCCESS] ✅ 已选择本地上传
[2025-08-07 01:47:46] [DEBUG] 🔍 查找文件输入框...
[2025-08-07 01:47:46] [SUCCESS] ✅ 找到 3 个文件输入框，使用最后一个作为封面输入框
[2025-08-07 01:47:46] [SUCCESS] ✅ 封面文件已选择
[2025-08-07 01:47:46] [DEBUG] 🔍 开始检测封面canvas加载状态...
[2025-08-07 01:47:47] [SUCCESS] ✅ Canvas已加载并有内容 (592x333)
[2025-08-07 01:47:47] [SUCCESS] ✅ 封面canvas已加载完成
[2025-08-07 01:47:47] [SUCCESS] ✅ 已点击第一个确定按钮
[2025-08-07 01:47:49] [SUCCESS] ✅ 已点击第二个确定按钮（通过文字查找）
[2025-08-07 01:47:49] [INFO] ⏳ 等待上传弹窗消失...
[2025-08-07 01:47:49] [SUCCESS] ⏳ 检测到'上传中'状态，等待上传完成...
[2025-08-07 01:47:51] [SUCCESS] ✅ '上传中'状态消失，弹窗已关闭
[2025-08-07 01:47:53] [SUCCESS] ✅ 视频封面上传完成
[2025-08-07 01:47:53] [DEBUG] 💾 开始保存草稿...
[2025-08-07 01:47:53] [SUCCESS] ✅ 已点击存草稿按钮
[2025-08-07 01:47:56] [WARNING] ✅ 草稿保存成功！检测到'全部保存成功'提示
[2025-08-07 01:47:56] [SUCCESS] ✅ 头条号视频存稿成功！
[2025-08-07 01:47:56] [SUCCESS] ✅ 视频存稿成功: 下雨天一定要谨慎驾驶，减速慢行啊！.mp4
[2025-08-07 01:47:56] [INFO] 等待 4.1 秒后处理下一个视频...
[2025-08-07 01:48:01] [DEBUG] 开始处理视频: 她是红卫兵“头头”，带头捣毁孔庙孔府，多年后遭了报应下场凄惨.mp4
[2025-08-07 01:48:01] [DEBUG] 🎬 开始头条号视频存稿流程
[2025-08-07 01:48:01] [DEBUG] 🔍 正在定位视频菜单...
[2025-08-07 01:48:01] [SUCCESS] ✅ 已点击视频菜单
[2025-08-07 01:48:04] [INFO] 🌐 当前不在上传页面，导航到视频上传页面...
[2025-08-07 01:48:08] [SUCCESS] ✅ 已导航到视频上传页面
[2025-08-07 01:48:08] [INFO] 📁 准备上传视频: 她是红卫兵“头头”，带头捣毁孔庙孔府，多年后遭了报应下场凄惨.mp4
[2025-08-07 01:48:08] [DEBUG] 🔍 找到上传按钮，准备点击...
[2025-08-07 01:48:08] [SUCCESS] ✅ 已点击上传视频按钮
[2025-08-07 01:48:10] [SUCCESS] ✅ 视频文件已选择并开始上传
[2025-08-07 01:48:10] [DEBUG] ⏳ 开始监控视频上传状态...
[2025-08-07 01:48:10] [INFO] 📊 当前上传状态: 上传中… 0%
[2025-08-07 01:48:10] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 01:48:15] [SUCCESS] 📊 当前上传状态: 上传成功
[2025-08-07 01:48:15] [SUCCESS] ✅ 视频上传成功！
[2025-08-07 01:48:15] [DEBUG] 📷 开始上传视频封面...
[2025-08-07 01:48:15] [SUCCESS] ✅ 已点击上传封面按钮
[2025-08-07 01:48:17] [SUCCESS] ✅ 已选择本地上传
[2025-08-07 01:48:18] [DEBUG] 🔍 查找文件输入框...
[2025-08-07 01:48:18] [SUCCESS] ✅ 找到 3 个文件输入框，使用最后一个作为封面输入框
[2025-08-07 01:48:18] [SUCCESS] ✅ 封面文件已选择
[2025-08-07 01:48:18] [DEBUG] 🔍 开始检测封面canvas加载状态...
[2025-08-07 01:48:18] [SUCCESS] ✅ Canvas已加载并有内容 (592x333)
[2025-08-07 01:48:18] [SUCCESS] ✅ 封面canvas已加载完成
[2025-08-07 01:48:18] [SUCCESS] ✅ 已点击第一个确定按钮
[2025-08-07 01:48:21] [SUCCESS] ✅ 已点击第二个确定按钮（通过文字查找）
[2025-08-07 01:48:21] [INFO] ⏳ 等待上传弹窗消失...
[2025-08-07 01:48:21] [SUCCESS] ⏳ 检测到'上传中'状态，等待上传完成...
[2025-08-07 01:48:23] [SUCCESS] ✅ '上传中'状态消失，弹窗已关闭
[2025-08-07 01:48:25] [SUCCESS] ✅ 视频封面上传完成
[2025-08-07 01:48:25] [DEBUG] 💾 开始保存草稿...
[2025-08-07 01:48:25] [SUCCESS] ✅ 已点击存草稿按钮
[2025-08-07 01:48:26] [INFO] 打开进度条对话框
[2025-08-07 01:48:26] [SUCCESS] 进度数据已更新
[2025-08-07 01:48:26] [INFO] 已加载当前进度数据
[2025-08-07 01:48:28] [WARNING] ✅ 草稿保存成功！检测到'全部保存成功'提示
[2025-08-07 01:48:28] [SUCCESS] ✅ 头条号视频存稿成功！
[2025-08-07 01:48:28] [SUCCESS] ✅ 视频存稿成功: 她是红卫兵“头头”，带头捣毁孔庙孔府，多年后遭了报应下场凄惨.mp4
[2025-08-07 01:48:28] [INFO] 等待 2.5 秒后处理下一个视频...
[2025-08-07 01:48:30] [INFO] 当前循环已达到存稿限制 2，进入下一轮
[2025-08-07 01:48:30] [SUCCESS] 第1轮处理完成，本轮成功存稿 2 个视频
[2025-08-07 01:48:30] [DEBUG] 🔄 开始第2/3轮处理
[2025-08-07 01:48:30] [INFO] 📁 循环2: 652个视频已随机排序
[2025-08-07 01:48:30] [DEBUG] 开始处理视频: 国民党主席选举开启，岛内公布民调结果，卢秀燕不装了声援赖清德.mp4
[2025-08-07 01:48:30] [DEBUG] 🎬 开始头条号视频存稿流程
[2025-08-07 01:48:30] [DEBUG] 🔍 正在定位视频菜单...
[2025-08-07 01:48:30] [SUCCESS] ✅ 已点击视频菜单
[2025-08-07 01:48:33] [INFO] 🌐 当前不在上传页面，导航到视频上传页面...
[2025-08-07 01:48:37] [SUCCESS] ✅ 已导航到视频上传页面
[2025-08-07 01:48:37] [INFO] 📁 准备上传视频: 国民党主席选举开启，岛内公布民调结果，卢秀燕不装了声援赖清德.mp4
[2025-08-07 01:48:37] [DEBUG] 🔍 找到上传按钮，准备点击...
[2025-08-07 01:48:37] [SUCCESS] ✅ 已点击上传视频按钮
[2025-08-07 01:48:39] [SUCCESS] ✅ 视频文件已选择并开始上传
[2025-08-07 01:48:40] [DEBUG] ⏳ 开始监控视频上传状态...
[2025-08-07 01:48:40] [INFO] 📊 当前上传状态: 上传中… 0%
[2025-08-07 01:48:40] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 01:48:45] [SUCCESS] 📊 当前上传状态: 上传成功
[2025-08-07 01:48:45] [SUCCESS] ✅ 视频上传成功！
[2025-08-07 01:48:45] [DEBUG] 📷 开始上传视频封面...
[2025-08-07 01:48:45] [SUCCESS] ✅ 已点击上传封面按钮
[2025-08-07 01:48:47] [SUCCESS] ✅ 已选择本地上传
[2025-08-07 01:48:48] [DEBUG] 🔍 查找文件输入框...
[2025-08-07 01:48:48] [SUCCESS] ✅ 找到 3 个文件输入框，使用最后一个作为封面输入框
[2025-08-07 01:48:48] [SUCCESS] ✅ 封面文件已选择
[2025-08-07 01:48:48] [DEBUG] 🔍 开始检测封面canvas加载状态...
[2025-08-07 01:48:48] [SUCCESS] ✅ Canvas已加载并有内容 (592x333)
[2025-08-07 01:48:48] [SUCCESS] ✅ 封面canvas已加载完成
[2025-08-07 01:48:48] [SUCCESS] ✅ 已点击第一个确定按钮
[2025-08-07 01:48:50] [SUCCESS] ✅ 已点击第二个确定按钮（通过文字查找）
[2025-08-07 01:48:50] [INFO] ⏳ 等待上传弹窗消失...
[2025-08-07 01:48:50] [SUCCESS] ⏳ 检测到'上传中'状态，等待上传完成...
[2025-08-07 01:48:52] [INFO] 已关闭进度条对话框
[2025-08-07 01:48:53] [SUCCESS] ✅ '上传中'状态消失，弹窗已关闭
[2025-08-07 01:48:55] [SUCCESS] ✅ 视频封面上传完成
[2025-08-07 01:48:55] [DEBUG] 💾 开始保存草稿...
[2025-08-07 01:48:55] [SUCCESS] ✅ 已点击存草稿按钮
[2025-08-07 01:48:58] [WARNING] ✅ 草稿保存成功！检测到'全部保存成功'提示
[2025-08-07 01:48:58] [SUCCESS] ✅ 头条号视频存稿成功！
[2025-08-07 01:48:58] [SUCCESS] ✅ 视频存稿成功: 国民党主席选举开启，岛内公布民调结果，卢秀燕不装了声援赖清德.mp4
[2025-08-07 01:48:58] [INFO] 等待 4.3 秒后处理下一个视频...
[2025-08-07 01:49:01] [SUCCESS] ✅ 日志设置已保存
[2025-08-07 01:49:02] [DEBUG] 开始处理视频: 虚假信息莫转发 网络谣言不轻信.mp4
[2025-08-07 01:49:02] [DEBUG] 🎬 开始头条号视频存稿流程
[2025-08-07 01:49:02] [DEBUG] 🔍 正在定位视频菜单...
[2025-08-07 01:49:02] [SUCCESS] ✅ 已点击视频菜单
[2025-08-07 01:49:05] [INFO] 🌐 当前不在上传页面，导航到视频上传页面...
[2025-08-07 01:49:09] [SUCCESS] ✅ 已导航到视频上传页面
[2025-08-07 01:49:09] [INFO] 📁 准备上传视频: 虚假信息莫转发 网络谣言不轻信.mp4
[2025-08-07 01:49:09] [DEBUG] 🔍 找到上传按钮，准备点击...
[2025-08-07 01:49:09] [SUCCESS] ✅ 已点击上传视频按钮
[2025-08-07 01:49:11] [SUCCESS] ✅ 视频文件已选择并开始上传
[2025-08-07 01:49:11] [DEBUG] ⏳ 开始监控视频上传状态...
[2025-08-07 01:49:12] [INFO] 📊 当前上传状态: 上传中… 0%
[2025-08-07 01:49:12] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 01:49:17] [SUCCESS] 📊 当前上传状态: 上传成功
[2025-08-07 01:49:17] [SUCCESS] ✅ 视频上传成功！
[2025-08-07 01:49:17] [DEBUG] 📷 开始上传视频封面...
[2025-08-07 01:49:17] [SUCCESS] ✅ 已点击上传封面按钮
[2025-08-07 01:49:19] [SUCCESS] ✅ 已选择本地上传
[2025-08-07 01:49:20] [DEBUG] 🔍 查找文件输入框...
[2025-08-07 01:49:20] [SUCCESS] ✅ 找到 3 个文件输入框，使用最后一个作为封面输入框
[2025-08-07 01:49:20] [SUCCESS] ✅ 封面文件已选择
[2025-08-07 01:49:20] [DEBUG] 🔍 开始检测封面canvas加载状态...
[2025-08-07 01:49:20] [SUCCESS] ✅ Canvas已加载并有内容 (592x333)
[2025-08-07 01:49:20] [SUCCESS] ✅ 封面canvas已加载完成
[2025-08-07 01:49:20] [SUCCESS] ✅ 已点击第一个确定按钮
[2025-08-07 01:49:22] [SUCCESS] ✅ 已点击第二个确定按钮（通过文字查找）
[2025-08-07 01:49:22] [INFO] ⏳ 等待上传弹窗消失...
[2025-08-07 01:49:22] [SUCCESS] ⏳ 检测到'上传中'状态，等待上传完成...
[2025-08-07 01:49:25] [SUCCESS] ✅ '上传中'状态消失，弹窗已关闭
[2025-08-07 01:49:27] [SUCCESS] ✅ 视频封面上传完成
[2025-08-07 01:49:27] [DEBUG] 💾 开始保存草稿...
[2025-08-07 01:49:27] [SUCCESS] ✅ 已点击存草稿按钮
[2025-08-07 01:49:30] [WARNING] ✅ 草稿保存成功！检测到'全部保存成功'提示
[2025-08-07 01:49:30] [SUCCESS] ✅ 头条号视频存稿成功！
[2025-08-07 01:49:30] [SUCCESS] ✅ 视频存稿成功: 虚假信息莫转发 网络谣言不轻信.mp4
[2025-08-07 01:49:30] [INFO] 等待 3.4 秒后处理下一个视频...
[2025-08-07 01:49:33] [INFO] 打开进度条对话框
[2025-08-07 01:49:33] [SUCCESS] 进度数据已更新
[2025-08-07 01:49:33] [INFO] 已加载当前进度数据
[2025-08-07 01:49:33] [INFO] 当前循环已达到存稿限制 2，进入下一轮
[2025-08-07 01:49:33] [SUCCESS] 第2轮处理完成，本轮成功存稿 2 个视频
[2025-08-07 01:49:33] [DEBUG] 🔄 开始第3/3轮处理
[2025-08-07 01:49:33] [INFO] 📁 循环3: 652个视频已随机排序
[2025-08-07 01:49:33] [DEBUG] 开始处理视频: 以父之名，你们最喜欢哪一首？.mp4
[2025-08-07 01:49:33] [DEBUG] 🎬 开始头条号视频存稿流程
[2025-08-07 01:49:33] [DEBUG] 🔍 正在定位视频菜单...
[2025-08-07 01:49:33] [SUCCESS] ✅ 已点击视频菜单
[2025-08-07 01:49:36] [INFO] 已关闭进度条对话框
[2025-08-07 01:49:36] [INFO] 🌐 当前不在上传页面，导航到视频上传页面...
[2025-08-07 01:49:40] [SUCCESS] ✅ 已导航到视频上传页面
[2025-08-07 01:49:40] [INFO] 📁 准备上传视频: 以父之名，你们最喜欢哪一首？.mp4
[2025-08-07 01:49:40] [DEBUG] 🔍 找到上传按钮，准备点击...
[2025-08-07 01:49:40] [SUCCESS] ✅ 已点击上传视频按钮
[2025-08-07 01:49:43] [SUCCESS] ✅ 视频文件已选择并开始上传
[2025-08-07 01:49:43] [DEBUG] ⏳ 开始监控视频上传状态...
[2025-08-07 01:49:43] [INFO] 📊 当前上传状态: 上传中… 0%
[2025-08-07 01:49:43] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 01:49:46] [INFO] 打开存稿设置对话框
[2025-08-07 01:49:48] [SUCCESS] 📊 当前上传状态: 上传成功
[2025-08-07 01:49:48] [SUCCESS] ✅ 视频上传成功！
[2025-08-07 01:49:48] [DEBUG] 📷 开始上传视频封面...
[2025-08-07 01:49:48] [SUCCESS] ✅ 已点击上传封面按钮
[2025-08-07 01:49:50] [SUCCESS] ✅ 已选择本地上传
[2025-08-07 01:49:50] [SUCCESS] ✅ 存稿设置已成功保存
[2025-08-07 01:49:51] [DEBUG] 🔍 查找文件输入框...
[2025-08-07 01:49:51] [SUCCESS] ✅ 找到 3 个文件输入框，使用最后一个作为封面输入框
[2025-08-07 01:49:51] [SUCCESS] ✅ 封面文件已选择
[2025-08-07 01:49:51] [DEBUG] 🔍 开始检测封面canvas加载状态...
[2025-08-07 01:49:51] [SUCCESS] ✅ Canvas已加载并有内容 (592x333)
[2025-08-07 01:49:51] [SUCCESS] ✅ 封面canvas已加载完成
[2025-08-07 01:49:51] [SUCCESS] ✅ 已点击第一个确定按钮
[2025-08-07 01:49:53] [SUCCESS] ✅ 已点击第二个确定按钮（通过文字查找）
[2025-08-07 01:49:53] [INFO] ⏳ 等待上传弹窗消失...
[2025-08-07 01:49:53] [SUCCESS] ⏳ 检测到'上传中'状态，等待上传完成...
[2025-08-07 01:49:55] [SUCCESS] ✅ '上传中'状态消失，弹窗已关闭
[2025-08-07 01:49:57] [SUCCESS] ✅ 视频封面上传完成
[2025-08-07 01:49:57] [DEBUG] 💾 开始保存草稿...
[2025-08-07 01:49:57] [SUCCESS] ✅ 已点击存草稿按钮
[2025-08-07 01:50:00] [WARNING] ✅ 草稿保存成功！检测到'全部保存成功'提示
[2025-08-07 01:50:00] [SUCCESS] ✅ 头条号视频存稿成功！
[2025-08-07 01:50:00] [SUCCESS] ✅ 视频存稿成功: 以父之名，你们最喜欢哪一首？.mp4
[2025-08-07 01:50:00] [INFO] 等待 3.2 秒后处理下一个视频...
[2025-08-07 01:50:03] [DEBUG] 开始处理视频: 胆子真大！陈妤颉200米轻松夺冠，后半程直接放水，赛后送飞吻.mp4
[2025-08-07 01:50:03] [DEBUG] 🎬 开始头条号视频存稿流程
[2025-08-07 01:50:03] [DEBUG] 🔍 正在定位视频菜单...
[2025-08-07 01:50:03] [SUCCESS] ✅ 已点击视频菜单
[2025-08-07 01:50:06] [INFO] 🌐 当前不在上传页面，导航到视频上传页面...
[2025-08-07 01:50:10] [SUCCESS] ✅ 已导航到视频上传页面
[2025-08-07 01:50:10] [INFO] 📁 准备上传视频: 胆子真大！陈妤颉200米轻松夺冠，后半程直接放水，赛后送飞吻.mp4
[2025-08-07 01:50:10] [DEBUG] 🔍 找到上传按钮，准备点击...
[2025-08-07 01:50:10] [SUCCESS] ✅ 已点击上传视频按钮
[2025-08-07 01:50:12] [SUCCESS] ✅ 视频文件已选择并开始上传
[2025-08-07 01:50:12] [DEBUG] ⏳ 开始监控视频上传状态...
[2025-08-07 01:50:12] [INFO] 📊 当前上传状态: 上传中… 0%
[2025-08-07 01:50:12] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 01:50:17] [INFO] 📊 当前上传状态: 上传中… 100%
[2025-08-07 01:50:17] [DEBUG] ⏳ 视频正在上传中...
[2025-08-07 01:50:22] [SUCCESS] 📊 当前上传状态: 上传成功
[2025-08-07 01:50:22] [SUCCESS] ✅ 视频上传成功！
[2025-08-07 01:50:22] [DEBUG] 📷 开始上传视频封面...
[2025-08-07 01:50:22] [SUCCESS] ✅ 已点击上传封面按钮
[2025-08-07 01:50:24] [SUCCESS] ✅ 已选择本地上传
[2025-08-07 01:50:25] [DEBUG] 🔍 查找文件输入框...
[2025-08-07 01:50:25] [SUCCESS] ✅ 找到 3 个文件输入框，使用最后一个作为封面输入框
[2025-08-07 01:50:25] [SUCCESS] ✅ 封面文件已选择
[2025-08-07 01:50:25] [DEBUG] 🔍 开始检测封面canvas加载状态...
[2025-08-07 01:50:26] [SUCCESS] ✅ Canvas已加载并有内容 (592x333)
[2025-08-07 01:50:26] [SUCCESS] ✅ 封面canvas已加载完成
[2025-08-07 01:50:26] [SUCCESS] ✅ 已点击第一个确定按钮
[2025-08-07 01:50:28] [SUCCESS] ✅ 已点击第二个确定按钮（通过文字查找）
[2025-08-07 01:50:28] [INFO] ⏳ 等待上传弹窗消失...
[2025-08-07 01:50:28] [SUCCESS] ⏳ 检测到'上传中'状态，等待上传完成...
[2025-08-07 01:50:31] [SUCCESS] ✅ '上传中'状态消失，弹窗已关闭
[2025-08-07 01:50:33] [SUCCESS] ✅ 视频封面上传完成
[2025-08-07 01:50:33] [DEBUG] 💾 开始保存草稿...
[2025-08-07 01:50:33] [SUCCESS] ✅ 已点击存草稿按钮
[2025-08-07 01:50:36] [WARNING] ✅ 草稿保存成功！检测到'全部保存成功'提示
[2025-08-07 01:50:36] [SUCCESS] ✅ 头条号视频存稿成功！
[2025-08-07 01:50:36] [SUCCESS] ✅ 视频存稿成功: 胆子真大！陈妤颉200米轻松夺冠，后半程直接放水，赛后送飞吻.mp4
[2025-08-07 01:50:36] [INFO] 等待 3.5 秒后处理下一个视频...
[2025-08-07 01:50:39] [INFO] 当前循环已达到存稿限制 2，进入下一轮
[2025-08-07 01:50:39] [SUCCESS] 第3轮处理完成，本轮成功存稿 2 个视频
[2025-08-07 01:50:39] [DEBUG] 🔄 开始第4/3轮处理
[2025-08-07 01:50:39] [INFO] 已达到循环限制 3，停止处理
[2025-08-07 01:50:39] [INFO] [toutiao] 已释放端口: 9515
[2025-08-07 01:50:41] [SUCCESS] 账号 *********** 处理完成
[2025-08-07 01:50:41] [SUCCESS] 账号 *********** 处理完成
[2025-08-07 01:50:41] [SUCCESS] 已更新账号 *********** 的进度信息: 存稿成功数=0, 状态=
[2025-08-07 01:50:41] [SUCCESS] 已更新账号 *********** 状态到账号管理器: 
[2025-08-07 01:50:41] [SUCCESS] 已更新账号 *********** 的进度信息: 存稿成功数=0, 状态=
[2025-08-07 01:50:41] [SUCCESS] 已更新账号 *********** 状态到账号管理器: 
[2025-08-07 01:50:41] [SUCCESS] 已更新账号 *********** 的进度信息: 存稿成功数=0, 状态=
[2025-08-07 01:50:41] [SUCCESS] 已更新账号 *********** 状态到账号管理器: 
[2025-08-07 01:50:42] [SUCCESS] 已更新账号 *********** 的进度信息: 存稿成功数=0, 状态=
[2025-08-07 01:50:42] [SUCCESS] 已更新账号 *********** 状态到账号管理器: 
[2025-08-07 01:50:42] [SUCCESS] 已更新账号 *********** 的进度信息: 存稿成功数=0, 状态=
[2025-08-07 01:50:42] [SUCCESS] 已更新账号 *********** 状态到账号管理器: 
[2025-08-07 01:50:42] [SUCCESS] 已更新账号 *********** 的进度信息: 存稿成功数=0, 状态=
[2025-08-07 01:50:42] [SUCCESS] 已更新账号 *********** 状态到账号管理器: 
[2025-08-07 01:50:42] [SUCCESS] ✅ 账号 *********** 处理成功
[2025-08-07 01:50:42] [SUCCESS] 📊 任务进度完成: 1/1
[2025-08-07 01:50:42] [SUCCESS] ✅ 存稿任务已完成
[2025-08-07 01:51:23] [INFO] 打开进度条对话框
[2025-08-07 01:51:23] [SUCCESS] 进度数据已更新
[2025-08-07 01:51:23] [INFO] 已加载当前进度数据
[2025-08-07 01:51:24] [INFO] 已关闭进度条对话框
[2025-08-07 02:46:48] [DEBUG] 正在关闭程序，清理后台资源...
[2025-08-07 02:46:49] [DEBUG] 🔄 调度器主循环结束
[2025-08-07 02:46:49] [DEBUG] 正在关闭任务执行器...
[2025-08-07 02:46:49] [INFO] 任务执行器已关闭
[2025-08-07 02:46:49] [INFO] ⏹️ 定时任务调度器已停止
[2025-08-07 02:46:49] [SUCCESS] ✅ 调度器管理器清理完成
[2025-08-07 02:46:49] [SUCCESS] ✅ 定时任务管理器UI清理完成
[2025-08-07 02:46:49] [DEBUG] 🧹 开始清理所有资源...
[2025-08-07 02:46:49] [DEBUG] 正在停止并发管理器...
[2025-08-07 02:46:49] [SUCCESS] ✅ 运行中的任务已停止
[2025-08-07 02:46:49] [SUCCESS] ✅ 定时器线程已清理（升级版系统自动管理）
[2025-08-07 02:46:51] [SUCCESS] ✅ Chrome进程强制清理完成
[2025-08-07 02:46:51] [SUCCESS] ✅ 浏览器进程已清理
[2025-08-07 02:46:52] [SUCCESS] ✅ 日志处理线程已清理
[2025-08-07 02:46:52] [SUCCESS] ✅ 存稿处理器已清理
[2025-08-07 02:46:52] [SUCCESS] ✅ 线程池已清理
[2025-08-07 02:46:52] [DEBUG] 正在停止串行刷新工作线程...
[2025-08-07 02:46:55] [WARNING] ⚠️ 仍有 2 个线程未结束
[2025-08-07 02:46:55] [SUCCESS] ✅ 临时文件已清理
[2025-08-07 02:46:55] [SUCCESS] ✅ 资源清理完成
[2025-08-07 02:46:55] [SUCCESS] ✅ 设置已静默保存
[2025-08-07 02:46:55] [SUCCESS] 程序清理完成，正在退出...
