#!/usr/bin/env python3
"""
自动打包程序 - GUI版本
提供图形界面的打包工具
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, filedialog
import threading
import subprocess
import sys
import os
from pathlib import Path
import time
import datetime

class PackageGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("多平台存稿工具 - 自动打包器")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 设置样式
        self.setup_styles()
        
        # 创建界面
        self.create_widgets()
        
        # 打包状态
        self.is_packaging = False
        self.package_thread = None
        
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # 配置样式
        style.configure('Title.TLabel', font=('微软雅黑', 16, 'bold'))
        style.configure('Heading.TLabel', font=('微软雅黑', 12, 'bold'))
        style.configure('Success.TLabel', foreground='green', font=('微软雅黑', 10, 'bold'))
        style.configure('Error.TLabel', foreground='red', font=('微软雅黑', 10, 'bold'))
        
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="多平台存稿工具 - 自动打包器", style='Title.TLabel')
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 配置区域
        config_frame = ttk.LabelFrame(main_frame, text="打包配置", padding="10")
        config_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)
        
        # 源目录
        ttk.Label(config_frame, text="源目录:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.source_dir_var = tk.StringVar(value=r"C:\Users\<USER>\Downloads\网易\多平台存稿")
        source_entry = ttk.Entry(config_frame, textvariable=self.source_dir_var, width=50)
        source_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        ttk.Button(config_frame, text="浏览", command=self.browse_source_dir).grid(row=0, column=2)
        
        # 目标目录
        ttk.Label(config_frame, text="目标目录:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        self.target_dir_var = tk.StringVar(value=r"D:\多平台存稿")
        target_entry = ttk.Entry(config_frame, textvariable=self.target_dir_var, width=50)
        target_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 10), pady=(10, 0))
        ttk.Button(config_frame, text="浏览", command=self.browse_target_dir).grid(row=1, column=2, pady=(10, 0))
        
        # 打包选项
        options_frame = ttk.LabelFrame(main_frame, text="打包选项", padding="10")
        options_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.clean_build_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="清理旧文件", variable=self.clean_build_var).grid(row=0, column=0, sticky=tk.W)
        
        self.include_deps_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="包含所有依赖", variable=self.include_deps_var).grid(row=0, column=1, sticky=tk.W, padx=(20, 0))
        
        self.create_launcher_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="创建启动脚本", variable=self.create_launcher_var).grid(row=0, column=2, sticky=tk.W, padx=(20, 0))
        
        # 控制按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=3, pady=(0, 10))
        
        self.start_button = ttk.Button(button_frame, text="开始打包", command=self.start_packaging, style='Accent.TButton')
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_button = ttk.Button(button_frame, text="停止打包", command=self.stop_packaging, state='disabled')
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="安装依赖", command=self.install_dependencies).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="验证结果", command=self.verify_result).pack(side=tk.LEFT)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 状态标签
        self.status_var = tk.StringVar(value="就绪")
        self.status_label = ttk.Label(main_frame, textvariable=self.status_var)
        self.status_label.grid(row=5, column=0, columnspan=3, pady=(0, 10))
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="打包日志", padding="5")
        log_frame.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 清除日志按钮
        ttk.Button(log_frame, text="清除日志", command=self.clear_log).grid(row=1, column=0, pady=(5, 0))
        
    def browse_source_dir(self):
        """浏览源目录"""
        directory = filedialog.askdirectory(initialdir=self.source_dir_var.get())
        if directory:
            self.source_dir_var.set(directory)
    
    def browse_target_dir(self):
        """浏览目标目录"""
        directory = filedialog.askdirectory(initialdir=self.target_dir_var.get())
        if directory:
            self.target_dir_var.set(directory)
    
    def log_message(self, message, level="INFO"):
        """添加日志消息"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {level}: {message}\n"
        
        self.log_text.insert(tk.END, formatted_message)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
        # 根据级别设置状态
        if level == "ERROR":
            self.status_var.set(f"错误: {message}")
        elif level == "SUCCESS":
            self.status_var.set(f"成功: {message}")
        else:
            self.status_var.set(message)
    
    def clear_log(self):
        """清除日志"""
        self.log_text.delete(1.0, tk.END)
    
    def update_progress(self, value, message=""):
        """更新进度条"""
        self.progress_var.set(value)
        if message:
            self.status_var.set(message)
        self.root.update_idletasks()
    
    def start_packaging(self):
        """开始打包"""
        if self.is_packaging:
            return
        
        # 验证输入
        source_dir = Path(self.source_dir_var.get())
        if not source_dir.exists():
            messagebox.showerror("错误", "源目录不存在！")
            return
        
        if not (source_dir / "run_app.py").exists():
            messagebox.showerror("错误", "源目录中未找到 run_app.py 文件！")
            return
        
        # 开始打包
        self.is_packaging = True
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')
        
        self.clear_log()
        self.log_message("开始打包过程...")
        self.update_progress(0, "准备打包...")
        
        # 在新线程中执行打包
        self.package_thread = threading.Thread(target=self.package_worker, daemon=True)
        self.package_thread.start()
    
    def stop_packaging(self):
        """停止打包"""
        self.is_packaging = False
        self.start_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.log_message("打包已停止", "WARNING")
        self.update_progress(0, "已停止")
    
    def package_worker(self):
        """打包工作线程"""
        try:
            # 导入打包模块
            from 自动打包 import (
                check_dependencies, setup_directories, generate_spec_file,
                run_pyinstaller, finalize_package
            )
            
            source_dir = self.source_dir_var.get()
            target_dir = self.target_dir_var.get()
            
            # 步骤1: 检查依赖
            self.update_progress(10, "检查依赖...")
            self.log_message("检查打包依赖...")
            if not check_dependencies():
                self.log_message("依赖检查失败", "ERROR")
                return
            self.log_message("依赖检查完成", "SUCCESS")
            
            # 步骤2: 设置目录
            self.update_progress(20, "设置目录...")
            self.log_message("设置打包目录...")
            package_dir, dist_dir = setup_directories(target_dir)
            self.log_message(f"打包目录: {package_dir}", "SUCCESS")
            
            # 步骤3: 生成spec文件
            self.update_progress(40, "生成配置文件...")
            self.log_message("生成PyInstaller配置文件...")
            spec_file = generate_spec_file(package_dir)
            self.log_message(f"配置文件: {spec_file}", "SUCCESS")
            
            # 步骤4: 执行打包
            self.update_progress(60, "执行打包...")
            self.log_message("开始PyInstaller打包...")
            if not run_pyinstaller(spec_file, package_dir):
                self.log_message("PyInstaller打包失败", "ERROR")
                return
            self.log_message("PyInstaller打包完成", "SUCCESS")
            
            # 步骤5: 整理结果
            self.update_progress(80, "整理结果...")
            self.log_message("整理打包结果...")
            if not finalize_package(package_dir):
                self.log_message("结果整理失败", "ERROR")
                return
            self.log_message("结果整理完成", "SUCCESS")
            
            # 完成
            self.update_progress(100, "打包完成！")
            self.log_message("打包成功完成！", "SUCCESS")
            self.log_message(f"程序位置: {target_dir}", "SUCCESS")
            
            messagebox.showinfo("成功", f"打包完成！\n程序位置: {target_dir}")
            
        except Exception as e:
            self.log_message(f"打包过程出错: {e}", "ERROR")
            messagebox.showerror("错误", f"打包失败: {e}")
        finally:
            self.is_packaging = False
            self.start_button.config(state='normal')
            self.stop_button.config(state='disabled')
    
    def install_dependencies(self):
        """安装依赖"""
        self.log_message("开始安装依赖...")
        
        def install_worker():
            try:
                # 运行依赖安装脚本
                script_path = Path(__file__).parent / "安装缺失依赖.py"
                if script_path.exists():
                    result = subprocess.run([sys.executable, str(script_path)], 
                                          capture_output=True, text=True)
                    if result.returncode == 0:
                        self.log_message("依赖安装完成", "SUCCESS")
                    else:
                        self.log_message(f"依赖安装失败: {result.stderr}", "ERROR")
                else:
                    self.log_message("依赖安装脚本不存在", "ERROR")
            except Exception as e:
                self.log_message(f"安装依赖时出错: {e}", "ERROR")
        
        threading.Thread(target=install_worker, daemon=True).start()
    
    def verify_result(self):
        """验证打包结果"""
        target_dir = Path(self.target_dir_var.get())
        if not target_dir.exists():
            messagebox.showwarning("警告", "目标目录不存在！")
            return
        
        exe_file = target_dir / "多平台存稿工具.exe"
        if exe_file.exists():
            messagebox.showinfo("验证结果", f"✅ 打包成功！\n可执行文件: {exe_file}")
        else:
            messagebox.showwarning("验证结果", "❌ 未找到可执行文件")

def main():
    root = tk.Tk()
    app = PackageGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
