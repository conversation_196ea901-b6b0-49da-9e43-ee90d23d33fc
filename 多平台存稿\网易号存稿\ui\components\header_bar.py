"""
顶部标题栏组件 - 遵循MECE原则的独立UI组件

职责：
- 创建和管理顶部标题栏
- 显示平台状态指示器
- 提供主题切换功能
- 管理快速操作按钮

独立性：不依赖其他UI组件，只依赖配置管理器
"""

import tkinter as tk
from tkinter import ttk
from typing import Callable, Optional


class HeaderBar:
    """顶部标题栏组件"""
    
    def __init__(self, parent: tk.Widget, config_manager, theme_callback: Optional[Callable] = None):
        """
        初始化顶部标题栏
        
        Args:
            parent: 父容器
            config_manager: 配置管理器
            theme_callback: 主题切换回调函数
        """
        self.parent = parent
        self.config_manager = config_manager
        self.theme_callback = theme_callback
        
        # 创建主容器
        self.container = ttk.Frame(parent)
        
        # 主题切换按钮引用
        self.theme_bulb_button = None
        
        # 创建组件
        self._create_header_components()
    
    def _create_header_components(self):
        """创建标题栏组件"""
        # 左侧标题区域
        left_frame = ttk.Frame(self.container)
        left_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(20, 10), pady=10)
        
        # 主标题
        title_label = ttk.Label(
            left_frame,
            text="多平台存稿工具",
            font=("微软雅黑", 16, "bold")
        )
        title_label.pack(side=tk.LEFT)
        
        # 副标题
        subtitle_label = ttk.Label(
            left_frame,
            text="智能化内容分发管理平台",
            font=("微软雅黑", 10),
            foreground="#666666"
        )
        subtitle_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # 右侧区域
        right_frame = ttk.Frame(self.container)
        right_frame.pack(side=tk.RIGHT, padx=(10, 20), pady=10)
        
        # 平台状态指示器
        self._create_platform_indicator(right_frame)
    
    def _create_platform_indicator(self, parent):
        """创建平台状态指示器"""
        indicator_frame = ttk.Frame(parent)  # 移除LabelFrame的框架
        indicator_frame.pack(side=tk.RIGHT, padx=10, pady=5)
        
        # 平台名称和状态行
        platform_row = ttk.Frame(indicator_frame)
        platform_row.pack(fill=tk.X, pady=(0, 5))
        
        # 主题切换小灯泡按钮 - 放在当前平台行的左边（使用Label去掉外框）
        self.theme_bulb_button = ttk.Label(
            platform_row,
            text="💡",  # 小灯泡图标
            font=("微软雅黑", 14),
            cursor="hand2"
        )
        self.theme_bulb_button.pack(side=tk.LEFT, padx=(0, 10))

        # 绑定点击事件
        self.theme_bulb_button.bind("<Button-1>", lambda e: self._on_theme_toggle())
        
        # 状态指示灯
        status_indicator = ttk.Label(
            platform_row,
            text="●",
            font=("微软雅黑", 12),
            foreground="#28A745"  # 绿色表示正常
        )
        status_indicator.pack(side=tk.LEFT, padx=(0, 5))
        
        # 平台名称
        self.platform_label = ttk.Label(
            platform_row,
            text=self._get_platform_display_name(),
            font=("微软雅黑", 10, "bold")
        )
        self.platform_label.pack(side=tk.LEFT)
        
        # 初始化灯泡颜色
        self._update_theme_bulb_color()
    
    def _get_platform_display_name(self) -> str:
        """获取平台显示名称"""
        current_platform = self.config_manager.get_current_platform()
        platform_names = {
            "netease": "网易号",
            "toutiao": "今日头条", 
            "dayu": "大鱼号"
        }
        return platform_names.get(current_platform, "未知平台")
    
    def _on_theme_toggle(self):
        """主题切换事件处理"""
        if self.theme_callback:
            self.theme_callback()
        # 颜色更新由主UI负责，不在这里重复调用
    
    def _update_theme_bulb_color(self):
        """更新主题灯泡的颜色"""
        if self.theme_bulb_button:
            try:
                dark_mode = self.config_manager.get("dark_mode", False, platform="common")
                if dark_mode:
                    # 深色模式 - 亮黄色灯泡表示开启
                    self.theme_bulb_button.configure(foreground="#FFA500")  # 橙黄色，更醒目
                else:
                    # 浅色模式 - 淡灰色灯泡表示关闭
                    self.theme_bulb_button.configure(foreground="#B0B0B0")  # 淡灰色
            except:
                pass  # 忽略配置错误
    
    def update_platform_label(self, platform_name: str = None):
        """更新平台标签显示"""
        if hasattr(self, 'platform_label'):
            if platform_name is None:
                platform_name = self._get_platform_display_name()
            self.platform_label.configure(text=platform_name)

    def refresh_platform_display(self):
        """刷新平台显示（用于平台切换后的更新）"""
        self.update_platform_label()
    
    def pack(self, **kwargs):
        """打包容器"""
        self.container.pack(**kwargs)
    
    def grid(self, **kwargs):
        """网格布局容器"""
        self.container.grid(**kwargs)
    
    def place(self, **kwargs):
        """绝对定位容器"""
        self.container.place(**kwargs)
    
    def destroy(self):
        """销毁组件"""
        if hasattr(self, 'container'):
            self.container.destroy()
