#!/usr/bin/env python3
"""
多平台存稿工具 - 统一自动打包系统
集成所有打包相关功能：命令行模式、GUI模式、依赖检查、结果验证等
"""

import os
import sys
import shutil
import subprocess
import datetime
import threading
import importlib
import ast
import json
import time
from pathlib import Path

# GUI相关导入（可选）
try:
    import tkinter as tk
    from tkinter import ttk, messagebox, scrolledtext, filedialog
    GUI_AVAILABLE = True
except ImportError:
    GUI_AVAILABLE = False

class UnifiedPackager:
    """统一打包器类 - 集成所有打包功能"""
    
    def __init__(self, gui_mode=False):
        self.gui_mode = gui_mode
        self.is_packaging = False
        self.log_callback = None
        self.progress_callback = None

        # 默认配置
        self.config = {
            'source_dir': r"C:\Users\<USER>\Downloads\网易\多平台存稿",
            'target_dir': r"D:\多平台存稿",
            'clean_build': True,
            'include_deps': True,
            'create_launcher': True,
            'app_name': "多平台存稿工具",
            'package_video_tool': True  # 新增：是否打包视频处理工具
        }
    
    def log_message(self, message, level="INFO"):
        """记录日志消息"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {level}: {message}"
        
        if self.log_callback:
            self.log_callback(formatted_message, level)
        else:
            print(formatted_message)
    
    def update_progress(self, value, message=""):
        """更新进度"""
        if self.progress_callback:
            self.progress_callback(value, message)
    
    def check_dependencies(self):
        """检查打包依赖"""
        self.log_message("检查打包依赖...")
        
        # 检查PyInstaller
        try:
            import PyInstaller

            # 获取版本信息 - 使用多种方法确保兼容性
            version = "已安装"
            try:
                # 方法1: 直接获取__version__
                if hasattr(PyInstaller, '__version__'):
                    version = PyInstaller.__version__
                else:
                    # 方法2: 通过importlib.metadata
                    import importlib.metadata
                    version = importlib.metadata.version('pyinstaller')
            except Exception:
                # 方法3: 通过命令行检查
                try:
                    result = subprocess.run([sys.executable, '-m', 'PyInstaller', '--version'],
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        version = result.stdout.strip()
                except Exception:
                    pass

            self.log_message(f"PyInstaller 版本: {version}")

        except ImportError:
            self.log_message("PyInstaller 未安装，正在安装...", "WARNING")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
                self.log_message("PyInstaller 安装成功")
            except subprocess.CalledProcessError as e:
                self.log_message(f"PyInstaller 安装失败: {e}", "ERROR")
                return False
        
        # 检查源文件
        source_dir = Path(self.config['source_dir'])
        if not source_dir.exists():
            self.log_message(f"源目录不存在: {source_dir}", "ERROR")
            return False
        
        run_app = source_dir / "run_app.py"
        if not run_app.exists():
            self.log_message(f"主程序文件不存在: {run_app}", "ERROR")
            return False
        
        self.log_message("依赖检查完成")
        return True
    
    def install_missing_dependencies(self):
        """安装缺失的依赖"""
        self.log_message("开始安装缺失依赖...")
        
        packages = [
            'jaraco.text', 'setuptools', 'packaging', 'pyinstaller',
            'selenium', 'Pillow', 'requests', 'jieba', 'moviepy',
            'pyautogui', 'openpyxl', 'certifi', 'urllib3',
            'platformdirs', 'appdirs', 'importlib-metadata',
            'zipp', 'more-itertools'
        ]
        
        success_count = 0
        for package in packages:
            try:
                self.log_message(f"安装 {package}...")
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package], 
                                    capture_output=True)
                success_count += 1
                self.log_message(f"✅ {package} 安装成功")
            except subprocess.CalledProcessError:
                self.log_message(f"❌ {package} 安装失败", "WARNING")
        
        self.log_message(f"依赖安装完成: {success_count}/{len(packages)} 成功")
        return success_count > len(packages) * 0.8  # 80%成功率认为可接受
    
    def scan_project_imports(self):
        """扫描项目导入"""
        self.log_message("扫描项目导入...")
        
        source_dir = Path(self.config['source_dir'])
        all_imports = set()
        
        # 扫描Python文件
        for py_file in source_dir.rglob("*.py"):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    tree = ast.parse(f.read())
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            all_imports.add(alias.name.split('.')[0])
                    elif isinstance(node, ast.ImportFrom):
                        if node.module:
                            all_imports.add(node.module.split('.')[0])
            except Exception:
                continue
        
        self.log_message(f"发现 {len(all_imports)} 个导入模块")
        return all_imports
    
    def setup_directories(self):
        """设置打包目录"""
        self.log_message("设置打包目录...")
        
        target_dir = Path(self.config['target_dir'])
        
        # 清理旧目录
        if self.config['clean_build'] and target_dir.exists():
            self.log_message("清理现有目录...")
            try:
                shutil.rmtree(target_dir)
                self.log_message("旧目录已清理")
            except Exception as e:
                self.log_message(f"清理目录时出错: {e}", "WARNING")
        
        # 创建新目录
        target_dir.mkdir(parents=True, exist_ok=True)
        self.log_message(f"创建程序目录: {target_dir}")
        
        return target_dir
    
    def generate_spec_file(self, package_dir):
        """生成PyInstaller spec文件"""
        self.log_message("生成spec文件...")
        
        source_dir = Path(self.config['source_dir'])
        hooks_dir = Path(__file__).parent.absolute()
        
        # 构建数据文件列表
        datas = []
        
        # 配置文件
        config_files = ["config.json", "directory_config.json", "timer_config.json"]
        for config_file in config_files:
            config_path = source_dir / config_file
            if config_path.exists():
                # 转换为正斜杠避免转义问题
                path_str = str(config_path).replace('\\', '/')
                datas.append(f'("{path_str}", ".")')

        # 目录
        directories = ["config", "网易号存稿", "视频处理工具", "logs"]
        for directory in directories:
            dir_path = source_dir / directory
            if dir_path.exists():
                # 转换为正斜杠避免转义问题
                path_str = str(dir_path).replace('\\', '/')
                datas.append(f'("{path_str}", "{directory}")')

        # 特别添加视频处理工具的Python模块
        video_tool_dir = source_dir / "视频处理工具"
        if video_tool_dir.exists():
            # 添加整个视频处理工具目录作为Python模块
            path_str = str(video_tool_dir).replace('\\', '/')
            datas.append(f'("{path_str}", "视频处理工具")')
        
        # 隐藏导入列表
        hiddenimports = [
            # 基础模块
            "'tkinter', 'tkinter.ttk', 'tkinter.messagebox', 'tkinter.simpledialog'",
            "'tkinter.filedialog', 'tkinter.scrolledtext', 'tkinter.font'",

            # 第三方库
            "'selenium', 'selenium.webdriver', 'selenium.webdriver.chrome'",
            "'PIL', 'PIL.Image', 'PIL.ImageTk'",
            "'requests', 'urllib3', 'certifi'",
            "'jieba', 'moviepy', 'pyautogui'",

            # 视频处理工具模块
            "'视频处理工具', '视频处理工具.modules', '视频处理工具.modules.ui'",
            "'视频处理工具.modules.ui.main', '视频处理工具.modules.video_processor'",
            "'视频处理工具.modules.common', '视频处理工具.modules.icons'",

            # 依赖修复
            "'jaraco', 'jaraco.text', 'jaraco.functools'",
            "'pkg_resources', 'setuptools', 'packaging'",
            "'platformdirs', 'importlib_metadata', 'zipp'"
        ]
        
        # 转换路径为正斜杠格式
        source_dir_str = str(source_dir).replace('\\', '/')
        hooks_dir_str = str(hooks_dir).replace('\\', '/')

        # 生成spec内容
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

import os
import sys
from pathlib import Path

# 设置基础路径
base_path = Path(r"{source_dir_str}")
app_path = base_path / "run_app.py"

# 数据文件
datas = [
    {', '.join(datas)}
]

# 隐藏导入
hiddenimports = [
    {', '.join(hiddenimports)}
]

# 排除模块
excludes = ['matplotlib', 'scipy', 'jupyter', 'IPython', 'notebook', 'pytest']

a = Analysis(
    [str(app_path)],
    pathex=[str(base_path)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[r"{hooks_dir_str}"],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name="{self.config['app_name']}",
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name="{self.config['app_name']}",
)
'''
        
        spec_file_path = package_dir / "auto_app.spec"
        with open(spec_file_path, 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        self.log_message(f"spec文件已生成: {spec_file_path}")
        return spec_file_path

    def generate_video_tool_spec_file(self, package_dir):
        """生成视频处理工具的PyInstaller spec文件"""
        self.log_message("生成视频处理工具spec文件...")

        source_dir = Path(self.config['source_dir'])
        video_tool_dir = source_dir / "视频处理工具"
        hooks_dir = Path(__file__).parent.absolute()

        if not video_tool_dir.exists():
            self.log_message("视频处理工具目录不存在", "ERROR")
            return None

        # 构建数据文件列表
        datas = []

        # 视频处理工具的配置文件
        config_files = ["preprocess_config.ini", "preprocess_config.ini.backup"]
        for config_file in config_files:
            config_path = video_tool_dir / config_file
            if config_path.exists():
                path_str = str(config_path).replace('\\', '/')
                datas.append(f'("{path_str}", ".")')

        # 视频处理工具的目录
        directories = ["modules", "ffmpeg", "tests"]
        for directory in directories:
            dir_path = video_tool_dir / directory
            if dir_path.exists():
                path_str = str(dir_path).replace('\\', '/')
                datas.append(f'("{path_str}", "{directory}")')

        # 隐藏导入列表
        hiddenimports = [
            # 基础模块
            "'tkinter', 'tkinter.ttk', 'tkinter.messagebox', 'tkinter.simpledialog'",
            "'tkinter.filedialog', 'tkinter.scrolledtext', 'tkinter.font'",

            # 视频处理相关
            "'moviepy', 'moviepy.editor', 'moviepy.video.io.VideoFileClip'",
            "'PIL', 'PIL.Image', 'PIL.ImageTk', 'PIL.ImageDraw', 'PIL.ImageFont'",
            "'cv2', 'numpy', 'scipy'",

            # 视频处理工具模块
            "'modules', 'modules.common', 'modules.common.system'",
            "'modules.ui', 'modules.ui.main', 'modules.video_processor'",
            "'modules.config_manager', 'modules.utils', 'modules.icons'",
            "'modules.ui_base', 'modules.ui_processing', 'modules.ui_settings'",
            "'modules.ui_theme', 'modules.ui_utils'",

            # 其他依赖
            "'requests', 'urllib3', 'certifi'",
            "'setuptools', 'packaging', 'platformdirs'"
        ]

        # 转换路径为正斜杠格式
        video_tool_dir_str = str(video_tool_dir).replace('\\', '/')
        hooks_dir_str = str(hooks_dir).replace('\\', '/')
        main_py_path = video_tool_dir / "main.py"

        # 生成spec内容
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

import os
import sys
from pathlib import Path

# 设置基础路径
base_path = Path(r"{video_tool_dir_str}")
app_path = base_path / "main.py"

# 数据文件
datas = [
    {', '.join(datas)}
]

# 隐藏导入
hiddenimports = [
    {', '.join(hiddenimports)}
]

# 排除模块
excludes = ['matplotlib', 'jupyter', 'IPython', 'notebook', 'pytest']

a = Analysis(
    [str(app_path)],
    pathex=[str(base_path)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[r"{hooks_dir_str}"],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name="视频处理工具",
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name="视频处理工具",
)
'''

        spec_file_path = package_dir / "video_tool.spec"
        with open(spec_file_path, 'w', encoding='utf-8') as f:
            f.write(spec_content)

        self.log_message(f"视频处理工具spec文件已生成: {spec_file_path}")
        return spec_file_path
    
    def run_pyinstaller(self, spec_file_path, package_dir):
        """运行PyInstaller打包"""
        self.log_message("开始PyInstaller打包...")
        
        try:
            cmd = [
                sys.executable, "-m", "PyInstaller",
                "--clean", "--noconfirm", "--log-level=INFO",
                str(spec_file_path)
            ]
            
            self.log_message(f"执行命令: {' '.join(cmd)}")
            
            # 设置环境变量
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONLEGACYWINDOWSSTDIO'] = '1'
            
            result = subprocess.run(
                cmd, cwd=str(package_dir), capture_output=True,
                text=True, encoding='utf-8', errors='replace', env=env
            )
            
            if result.returncode == 0:
                self.log_message("PyInstaller打包完成")
                return True
            else:
                self.log_message(f"PyInstaller打包失败，返回码: {result.returncode}", "ERROR")
                # 显示错误信息
                if result.stderr:
                    for line in result.stderr.split('\n')[-10:]:
                        if line.strip():
                            self.log_message(f"  {line.strip()}", "ERROR")
                return False
                
        except Exception as e:
            self.log_message(f"PyInstaller执行出错: {e}", "ERROR")
            return False

    def finalize_package(self, package_dir):
        """整理打包结果"""
        self.log_message("整理打包结果...")

        try:
            # 查找生成的应用程序目录
            dist_dir = package_dir / "dist"
            if not dist_dir.exists():
                self.log_message("dist目录不存在", "ERROR")
                return False

            app_dirs = [d for d in dist_dir.iterdir() if d.is_dir()]

            if not app_dirs:
                self.log_message("未找到打包结果目录", "ERROR")
                return False

            # 处理主程序
            main_app_dir = None
            video_tool_dir = None

            for app_dir in app_dirs:
                if app_dir.name == self.config['app_name']:
                    main_app_dir = app_dir
                elif app_dir.name == "视频处理工具":
                    video_tool_dir = app_dir

            # 移动主程序文件到根目录
            if main_app_dir:
                self.log_message(f"找到主程序目录: {main_app_dir}")
                self.log_message("移动主程序文件到根目录...")
                for item in main_app_dir.iterdir():
                    target_path = package_dir / item.name
                    if target_path.exists():
                        if target_path.is_dir():
                            shutil.rmtree(target_path)
                        else:
                            target_path.unlink()
                    shutil.move(str(item), str(target_path))

            # 处理视频处理工具
            if video_tool_dir and self.config.get('package_video_tool', True):
                self.log_message(f"找到视频处理工具目录: {video_tool_dir}")

                # 创建视频处理工具目录在_internal下
                internal_dir = package_dir / "_internal"
                if not internal_dir.exists():
                    internal_dir.mkdir(parents=True)

                video_target_dir = internal_dir / "视频处理工具"
                if video_target_dir.exists():
                    shutil.rmtree(video_target_dir)

                # 移动视频处理工具到_internal/视频处理工具
                shutil.move(str(video_tool_dir), str(video_target_dir))
                self.log_message(f"视频处理工具已移动到: {video_target_dir}")

            # 清理临时目录
            self.log_message("清理临时目录结构")
            shutil.rmtree(dist_dir)
            build_dir = package_dir / "build"
            if build_dir.exists():
                shutil.rmtree(build_dir)

            # 复制配置文件
            self.copy_config_files(package_dir)

            # 创建启动脚本
            if self.config['create_launcher']:
                self.create_launch_script(package_dir)

            self.log_message("整理完成")
            return True

        except Exception as e:
            self.log_message(f"整理过程出错: {e}", "ERROR")
            return False

    def copy_config_files(self, target_dir):
        """复制配置文件到目标目录"""
        self.log_message("复制配置文件...")

        source_dir = Path(self.config['source_dir'])

        try:
            # 复制配置文件
            config_files = ["config.json", "directory_config.json", "timer_config.json"]
            for config_file in config_files:
                source_file = source_dir / config_file
                target_file = target_dir / config_file
                if source_file.exists():
                    shutil.copy2(source_file, target_file)
                    self.log_message(f"复制配置文件: {config_file}")

            # 复制config目录
            source_config_dir = source_dir / "config"
            target_config_dir = target_dir / "config"
            if source_config_dir.exists():
                if target_config_dir.exists():
                    shutil.rmtree(target_config_dir)
                shutil.copytree(source_config_dir, target_config_dir)
                self.log_message("复制config目录")

        except Exception as e:
            self.log_message(f"复制配置文件出错: {e}", "WARNING")

    def create_launch_script(self, target_dir):
        """创建启动脚本"""
        self.log_message("创建启动脚本...")

        try:
            # 创建改进的启动脚本，确保程序正确关闭
            bat_content = f'''@echo off
title {self.config['app_name']}
cd /d "%~dp0"

REM 设置环境变量，确保程序正确运行
set PYTHONIOENCODING=utf-8
set PYTHONLEGACYWINDOWSSTDIO=1

REM 启动程序
"{self.config['app_name']}.exe"

REM 检查退出代码
if errorlevel 1 (
    echo 程序运行出错，请检查日志
    pause
) else (
    echo 程序正常退出
)

REM 清理可能的残留进程
taskkill /f /im "{self.config['app_name']}.exe" >nul 2>&1

exit /b 0
'''

            bat_file = target_dir / f"启动{self.config['app_name']}.bat"
            with open(bat_file, 'w', encoding='gbk') as f:
                f.write(bat_content)

            self.log_message(f"创建启动脚本: 启动{self.config['app_name']}.bat")

            # 创建直接启动脚本（无控制台窗口）
            vbs_content = f'''Set WshShell = CreateObject("WScript.Shell")
WshShell.Run chr(34) & "{target_dir}\\{self.config['app_name']}.exe" & Chr(34), 0
Set WshShell = Nothing
'''

            vbs_file = target_dir / f"静默启动{self.config['app_name']}.vbs"
            with open(vbs_file, 'w', encoding='utf-8') as f:
                f.write(vbs_content)

            self.log_message(f"创建静默启动脚本: 静默启动{self.config['app_name']}.vbs")

        except Exception as e:
            self.log_message(f"创建启动脚本出错: {e}", "WARNING")

    def verify_package_result(self, target_dir=None):
        """验证打包结果"""
        if target_dir is None:
            target_dir = Path(self.config['target_dir'])
        else:
            target_dir = Path(target_dir)

        self.log_message("验证打包结果...")

        if not target_dir.exists():
            self.log_message("目标目录不存在", "ERROR")
            return False

        # 检查关键文件
        exe_file = target_dir / f"{self.config['app_name']}.exe"
        if not exe_file.exists():
            self.log_message("可执行文件不存在", "ERROR")
            return False

        # 检查目录结构
        required_dirs = ["_internal"]
        for dir_name in required_dirs:
            if not (target_dir / dir_name).exists():
                self.log_message(f"缺少目录: {dir_name}", "WARNING")

        # 统计文件
        file_count = len(list(target_dir.rglob("*")))
        self.log_message(f"打包结果验证完成，共 {file_count} 个文件")

        return True

    def package_command_line(self):
        """命令行模式打包"""
        print("=" * 60)
        print("多平台存稿工具自动打包器")
        print("=" * 60)

        try:
            # 步骤1: 检查依赖
            self.update_progress(5, "检查依赖...")
            if not self.check_dependencies():
                return False

            # 步骤2: 设置目录
            self.update_progress(10, "设置目录...")
            package_dir = self.setup_directories()

            # 步骤3: 生成主程序spec文件
            self.update_progress(20, "生成主程序配置文件...")
            spec_file = self.generate_spec_file(package_dir)

            # 步骤4: 执行主程序打包
            self.update_progress(35, "执行主程序打包...")
            if not self.run_pyinstaller(spec_file, package_dir):
                return False

            # 步骤5: 打包视频处理工具（如果启用）
            if self.config.get('package_video_tool', True):
                self.update_progress(50, "生成视频处理工具配置文件...")
                video_spec_file = self.generate_video_tool_spec_file(package_dir)
                if video_spec_file:
                    self.update_progress(65, "执行视频处理工具打包...")
                    if not self.run_pyinstaller(video_spec_file, package_dir):
                        self.log_message("视频处理工具打包失败，但继续主程序打包", "WARNING")

            # 步骤6: 整理结果
            self.update_progress(80, "整理结果...")
            if not self.finalize_package(package_dir):
                return False

            # 步骤7: 验证结果
            self.update_progress(100, "验证结果...")
            if not self.verify_package_result():
                return False

            print("=" * 60)
            self.log_message("打包完成！")
            self.log_message(f"程序已部署到: {self.config['target_dir']}")
            print("=" * 60)

            print("\n✅ 打包成功完成！")
            print(f"📁 程序位置: {self.config['target_dir']}")
            print("🚀 您现在可以运行打包好的程序了！")

            return True

        except Exception as e:
            self.log_message(f"打包过程出错: {e}", "ERROR")
            return False

class PackageGUI:
    """GUI界面类"""

    def __init__(self, root):
        if not GUI_AVAILABLE:
            raise ImportError("GUI模块不可用")

        self.root = root
        self.packager = UnifiedPackager(gui_mode=True)
        self.setup_gui()

        # 设置回调
        self.packager.log_callback = self.log_message
        self.packager.progress_callback = self.update_progress

    def setup_gui(self):
        """设置GUI界面"""
        self.root.title("多平台存稿工具 - 统一自动打包器")
        self.root.geometry("900x700")

        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(6, weight=1)

        # 标题
        title_label = ttk.Label(main_frame, text="多平台存稿工具 - 统一自动打包器",
                               font=('微软雅黑', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))

        # 配置区域
        self.create_config_section(main_frame)

        # 控制按钮
        self.create_control_section(main_frame)

        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 5))

        # 状态标签
        self.status_var = tk.StringVar(value="就绪")
        self.status_label = ttk.Label(main_frame, textvariable=self.status_var)
        self.status_label.grid(row=5, column=0, columnspan=3, pady=(0, 10))

        # 日志区域
        self.create_log_section(main_frame)

    def create_config_section(self, parent):
        """创建配置区域"""
        config_frame = ttk.LabelFrame(parent, text="打包配置", padding="10")
        config_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)

        # 源目录
        ttk.Label(config_frame, text="源目录:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.source_dir_var = tk.StringVar(value=self.packager.config['source_dir'])
        source_entry = ttk.Entry(config_frame, textvariable=self.source_dir_var, width=60)
        source_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        ttk.Button(config_frame, text="浏览", command=self.browse_source_dir).grid(row=0, column=2)

        # 目标目录
        ttk.Label(config_frame, text="目标目录:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        self.target_dir_var = tk.StringVar(value=self.packager.config['target_dir'])
        target_entry = ttk.Entry(config_frame, textvariable=self.target_dir_var, width=60)
        target_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 10), pady=(10, 0))
        ttk.Button(config_frame, text="浏览", command=self.browse_target_dir).grid(row=1, column=2, pady=(10, 0))

        # 选项
        options_frame = ttk.Frame(config_frame)
        options_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))

        self.clean_build_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="清理旧文件", variable=self.clean_build_var).pack(side=tk.LEFT)

        self.include_deps_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="包含依赖", variable=self.include_deps_var).pack(side=tk.LEFT, padx=(20, 0))

        self.create_launcher_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="创建启动脚本", variable=self.create_launcher_var).pack(side=tk.LEFT, padx=(20, 0))

        self.package_video_tool_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="打包视频处理工具", variable=self.package_video_tool_var).pack(side=tk.LEFT, padx=(20, 0))

    def create_control_section(self, parent):
        """创建控制区域"""
        button_frame = ttk.Frame(parent)
        button_frame.grid(row=3, column=0, columnspan=3, pady=(0, 10))

        self.start_button = ttk.Button(button_frame, text="开始打包", command=self.start_packaging)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))

        self.stop_button = ttk.Button(button_frame, text="停止打包", command=self.stop_packaging, state='disabled')
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(button_frame, text="安装依赖", command=self.install_dependencies).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="验证结果", command=self.verify_result).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="扫描导入", command=self.scan_imports).pack(side=tk.LEFT)

    def create_log_section(self, parent):
        """创建日志区域"""
        log_frame = ttk.LabelFrame(parent, text="打包日志", padding="5")
        log_frame.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=20, wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        ttk.Button(log_frame, text="清除日志", command=self.clear_log).grid(row=1, column=0, pady=(5, 0))

    def browse_source_dir(self):
        """浏览源目录"""
        directory = filedialog.askdirectory(initialdir=self.source_dir_var.get())
        if directory:
            self.source_dir_var.set(directory)
            self.packager.config['source_dir'] = directory

    def browse_target_dir(self):
        """浏览目标目录"""
        directory = filedialog.askdirectory(initialdir=self.target_dir_var.get())
        if directory:
            self.target_dir_var.set(directory)
            self.packager.config['target_dir'] = directory

    def log_message(self, message, level="INFO"):
        """添加日志消息"""
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()

        if level == "ERROR":
            self.status_var.set(f"错误: {message.split(': ', 1)[-1]}")
        else:
            self.status_var.set(message.split(': ', 1)[-1])

    def clear_log(self):
        """清除日志"""
        self.log_text.delete(1.0, tk.END)

    def update_progress(self, value, message=""):
        """更新进度条"""
        self.progress_var.set(value)
        if message:
            self.status_var.set(message)
        self.root.update_idletasks()

    def start_packaging(self):
        """开始打包"""
        # 更新配置
        self.packager.config.update({
            'source_dir': self.source_dir_var.get(),
            'target_dir': self.target_dir_var.get(),
            'clean_build': self.clean_build_var.get(),
            'include_deps': self.include_deps_var.get(),
            'create_launcher': self.create_launcher_var.get(),
            'package_video_tool': self.package_video_tool_var.get()
        })

        # 验证输入
        if not Path(self.packager.config['source_dir']).exists():
            messagebox.showerror("错误", "源目录不存在！")
            return

        # 开始打包
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')
        self.clear_log()

        def package_worker():
            try:
                success = self.packager.package_command_line()
                if success:
                    messagebox.showinfo("成功", f"打包完成！\n程序位置: {self.packager.config['target_dir']}")
                else:
                    messagebox.showerror("失败", "打包失败，请查看日志")
            except Exception as e:
                messagebox.showerror("错误", f"打包过程出错: {e}")
            finally:
                self.start_button.config(state='normal')
                self.stop_button.config(state='disabled')

        threading.Thread(target=package_worker, daemon=True).start()

    def stop_packaging(self):
        """停止打包"""
        self.packager.is_packaging = False
        self.start_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.log_message("打包已停止", "WARNING")

    def install_dependencies(self):
        """安装依赖"""
        def install_worker():
            self.packager.install_missing_dependencies()

        threading.Thread(target=install_worker, daemon=True).start()

    def verify_result(self):
        """验证结果"""
        success = self.packager.verify_package_result()
        if success:
            messagebox.showinfo("验证结果", "✅ 打包结果验证成功！")
        else:
            messagebox.showwarning("验证结果", "❌ 打包结果验证失败")

    def scan_imports(self):
        """扫描导入"""
        def scan_worker():
            imports = self.packager.scan_project_imports()
            self.log_message(f"扫描完成，发现导入: {', '.join(sorted(imports))}")

        threading.Thread(target=scan_worker, daemon=True).start()

def main():
    """主函数"""
    import argparse

    # 如果没有命令行参数，显示选择菜单
    if len(sys.argv) == 1:
        show_menu()
        return

    parser = argparse.ArgumentParser(description="多平台存稿工具 - 统一自动打包器")
    parser.add_argument("--gui", action="store_true", help="启动GUI模式")
    parser.add_argument("--install-deps", action="store_true", help="安装依赖")
    parser.add_argument("--verify", action="store_true", help="验证打包结果")
    parser.add_argument("--scan-imports", action="store_true", help="扫描项目导入")
    parser.add_argument("--source", help="源目录路径")
    parser.add_argument("--target", help="目标目录路径")

    args = parser.parse_args()

    # 创建打包器
    packager = UnifiedPackager()

    # 更新配置
    if args.source:
        packager.config['source_dir'] = args.source
    if args.target:
        packager.config['target_dir'] = args.target

    # 执行操作
    if args.gui:
        launch_gui()

    elif args.install_deps:
        packager.install_missing_dependencies()

    elif args.verify:
        packager.verify_package_result()

    elif args.scan_imports:
        imports = packager.scan_project_imports()
        print(f"发现导入: {', '.join(sorted(imports))}")

    else:
        # 默认命令行打包
        success = packager.package_command_line()
        if success:
            print("\n按回车键退出...")
            input()

def show_menu():
    """显示选择菜单"""
    print("=" * 60)
    print("多平台存稿工具 - 统一自动打包器")
    print("=" * 60)

    print("请选择运行模式:")
    print("1. GUI界面模式 (推荐)")
    print("2. 命令行打包模式")
    print("3. 安装依赖")
    print("4. 验证打包结果")
    print("5. 扫描项目导入")
    print("0. 退出")

    while True:
        try:
            choice = input("\n请输入选择 (0-5): ").strip()

            if choice == "0":
                print("退出程序")
                break
            elif choice == "1":
                launch_gui()
                break
            elif choice == "2":
                packager = UnifiedPackager()
                success = packager.package_command_line()
                if success:
                    input("\n按回车键退出...")
                break
            elif choice == "3":
                packager = UnifiedPackager()
                packager.install_missing_dependencies()
                input("\n按回车键退出...")
                break
            elif choice == "4":
                packager = UnifiedPackager()
                packager.verify_package_result()
                input("\n按回车键退出...")
                break
            elif choice == "5":
                packager = UnifiedPackager()
                imports = packager.scan_project_imports()
                print(f"发现导入: {', '.join(sorted(imports))}")
                input("\n按回车键退出...")
                break
            else:
                print("无效选择，请重新输入！")

        except KeyboardInterrupt:
            print("\n\n程序被中断")
            break
        except Exception as e:
            print(f"错误: {e}")
            break

def launch_gui():
    """启动GUI界面"""
    try:
        if not GUI_AVAILABLE:
            print("错误: GUI模块不可用")
            print("可能的原因:")
            print("- tkinter模块未安装")
            print("- 在某些Linux系统中需要安装python3-tk")
            print("\n建议使用命令行模式")
            return

        print("启动GUI界面...")
        root = tk.Tk()

        try:
            app = PackageGUI(root)
            print("GUI界面启动成功")
            root.mainloop()
        except Exception as e:
            print(f"GUI启动失败: {e}")
            root.destroy()
            print("请尝试使用命令行模式")

    except Exception as e:
        print(f"GUI启动出错: {e}")
        print("请尝试使用命令行模式")

if __name__ == "__main__":
    main()
