{"version": "1.0", "last_updated": "2025-07-21T00:10:16.184850", "tasks": [{"id": "07afde20-706c-409d-abab-37b394311214", "name": "网易号", "description": "", "task_type": "one_time", "status": "enabled", "action_type": "data_query", "action_params": {"platforms": ["netease"], "accounts": "all"}, "trigger_config": {"run_date": "2025-07-21T00:11:00+08:00"}, "created_time": "2025-07-21T00:10:16.178037+08:00", "last_run_time": null, "next_run_time": null, "execution_history": [], "max_history_records": 100}]}