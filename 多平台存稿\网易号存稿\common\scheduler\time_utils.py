"""
时间处理工具模块
提供北京时间处理、时间格式化、时间验证等功能
"""

import datetime
from typing import Optional, Union, Tuple
import re


class TimeUtils:
    """时间处理工具类"""

    # 北京时区偏移（UTC+8）
    BEIJING_OFFSET = datetime.timedelta(hours=8)
    
    @classmethod
    def get_beijing_now(cls) -> datetime.datetime:
        """获取当前北京时间"""
        utc_now = datetime.datetime.utcnow()
        return utc_now + cls.BEIJING_OFFSET

    @classmethod
    def to_beijing_time(cls, dt: datetime.datetime) -> datetime.datetime:
        """转换为北京时间"""
        if dt.tzinfo is None:
            # 如果没有时区信息，假设是本地时间，直接返回
            return dt
        else:
            # 如果有时区信息，转换为UTC再加上北京偏移
            utc_dt = dt.utctimetuple()
            utc_datetime = datetime.datetime(*utc_dt[:6])
            return utc_datetime + cls.BEIJING_OFFSET

    @classmethod
    def to_utc_time(cls, dt: datetime.datetime) -> datetime.datetime:
        """转换为UTC时间"""
        if dt.tzinfo is None:
            # 如果没有时区信息，假设是北京时间
            return dt - cls.BEIJING_OFFSET
        else:
            # 如果有时区信息，转换为UTC
            utc_dt = dt.utctimetuple()
            return datetime.datetime(*utc_dt[:6])
    
    @classmethod
    def parse_time_string(cls, time_str: str) -> Optional[datetime.datetime]:
        """
        解析时间字符串，支持多种格式
        
        支持的格式：
        - "2025-07-21 08:00:00"
        - "2025-07-21 08:00"
        - "07-21 08:00"
        - "08:00"
        - "08:00:30"
        
        Args:
            time_str: 时间字符串
            
        Returns:
            解析后的datetime对象，失败返回None
        """
        if not time_str:
            return None
        
        time_str = time_str.strip()
        now = cls.get_beijing_now()
        
        # 完整日期时间格式
        patterns = [
            (r'(\d{4})-(\d{1,2})-(\d{1,2})\s+(\d{1,2}):(\d{1,2}):(\d{1,2})',
             lambda m: datetime.datetime(int(m[0]), int(m[1]), int(m[2]),
                                       int(m[3]), int(m[4]), int(m[5]))),
            (r'(\d{4})-(\d{1,2})-(\d{1,2})\s+(\d{1,2}):(\d{1,2})',
             lambda m: datetime.datetime(int(m[0]), int(m[1]), int(m[2]),
                                       int(m[3]), int(m[4]))),
            (r'(\d{1,2})-(\d{1,2})\s+(\d{1,2}):(\d{1,2}):(\d{1,2})',
             lambda m: datetime.datetime(now.year, int(m[0]), int(m[1]),
                                       int(m[2]), int(m[3]), int(m[4]))),
            (r'(\d{1,2})-(\d{1,2})\s+(\d{1,2}):(\d{1,2})',
             lambda m: datetime.datetime(now.year, int(m[0]), int(m[1]),
                                       int(m[2]), int(m[3]))),
            (r'(\d{1,2}):(\d{1,2}):(\d{1,2})',
             lambda m: datetime.datetime(now.year, now.month, now.day,
                                       int(m[0]), int(m[1]), int(m[2]))),
            (r'(\d{1,2}):(\d{1,2})',
             lambda m: datetime.datetime(now.year, now.month, now.day,
                                       int(m[0]), int(m[1])))
        ]
        
        for pattern, converter in patterns:
            match = re.match(pattern, time_str)
            if match:
                try:
                    dt = converter(match.groups())
                    # 确保时间在未来
                    if dt <= now.replace(tzinfo=None):
                        if 'year' not in time_str and 'month' not in time_str:
                            # 如果只指定了时间，且已过去，则设为明天
                            dt += datetime.timedelta(days=1)
                    
                    # 返回北京时间
                    return dt
                except ValueError:
                    continue
        
        return None
    
    @classmethod
    def format_datetime(cls, dt: datetime.datetime, format_type: str = 'full') -> str:
        """
        格式化日期时间
        
        Args:
            dt: 日期时间对象
            format_type: 格式类型 ('full', 'date', 'time', 'short')
            
        Returns:
            格式化后的字符串
        """
        if not dt:
            return ""
        
        # 转换为北京时间
        beijing_dt = cls.to_beijing_time(dt)
        
        if format_type == 'full':
            return beijing_dt.strftime('%Y-%m-%d %H:%M:%S')
        elif format_type == 'date':
            return beijing_dt.strftime('%Y-%m-%d')
        elif format_type == 'time':
            return beijing_dt.strftime('%H:%M:%S')
        elif format_type == 'short':
            return beijing_dt.strftime('%m-%d %H:%M')
        else:
            return beijing_dt.strftime('%Y-%m-%d %H:%M:%S')
    
    @classmethod
    def get_time_until(cls, target_time: datetime.datetime) -> Tuple[bool, str]:
        """
        获取距离目标时间的描述
        
        Args:
            target_time: 目标时间
            
        Returns:
            (是否已过期, 时间描述)
        """
        if not target_time:
            return True, "无效时间"
        
        now = cls.get_beijing_now()
        target_beijing = cls.to_beijing_time(target_time)
        
        if target_beijing <= now:
            return True, "已过期"
        
        delta = target_beijing - now
        
        if delta.days > 0:
            return False, f"{delta.days}天后"
        elif delta.seconds > 3600:
            hours = delta.seconds // 3600
            return False, f"{hours}小时后"
        elif delta.seconds > 60:
            minutes = delta.seconds // 60
            return False, f"{minutes}分钟后"
        else:
            return False, f"{delta.seconds}秒后"
    
    @classmethod
    def validate_time_range(cls, start_time: datetime.datetime, 
                          end_time: Optional[datetime.datetime] = None) -> Tuple[bool, str]:
        """
        验证时间范围
        
        Args:
            start_time: 开始时间
            end_time: 结束时间（可选）
            
        Returns:
            (是否有效, 错误信息)
        """
        if not start_time:
            return False, "开始时间不能为空"
        
        now = cls.get_beijing_now()
        
        # 检查开始时间是否在过去（允许1分钟的误差）
        if start_time < now - datetime.timedelta(minutes=1):
            return False, "开始时间不能早于当前时间"
        
        if end_time:
            if end_time <= start_time:
                return False, "结束时间必须晚于开始时间"
        
        return True, ""
    
    @classmethod
    def get_next_weekday(cls, weekday: int, from_date: Optional[datetime.datetime] = None) -> datetime.datetime:
        """
        获取下一个指定星期几的日期
        
        Args:
            weekday: 星期几 (0=周一, 6=周日)
            from_date: 起始日期，默认为今天
            
        Returns:
            下一个指定星期几的日期
        """
        if from_date is None:
            from_date = cls.get_beijing_now().replace(hour=0, minute=0, second=0, microsecond=0)
        
        days_ahead = weekday - from_date.weekday()
        if days_ahead <= 0:  # 目标日期已过或就是今天
            days_ahead += 7
        
        return from_date + datetime.timedelta(days=days_ahead)
    
    @classmethod
    def get_next_month_day(cls, day: int, from_date: Optional[datetime.datetime] = None) -> datetime.datetime:
        """
        获取下一个指定日期的月份日期
        
        Args:
            day: 月份中的第几天 (1-31)
            from_date: 起始日期，默认为今天
            
        Returns:
            下一个指定日期
        """
        if from_date is None:
            from_date = cls.get_beijing_now().replace(hour=0, minute=0, second=0, microsecond=0)
        
        # 尝试当月
        try:
            target_date = from_date.replace(day=day)
            if target_date > from_date:
                return target_date
        except ValueError:
            pass  # 当月没有这一天
        
        # 尝试下个月
        next_month = from_date.month + 1
        next_year = from_date.year
        if next_month > 12:
            next_month = 1
            next_year += 1
        
        try:
            return from_date.replace(year=next_year, month=next_month, day=day)
        except ValueError:
            # 如果下个月也没有这一天，返回下个月的最后一天
            import calendar
            last_day = calendar.monthrange(next_year, next_month)[1]
            return from_date.replace(year=next_year, month=next_month, day=last_day)
    
    @classmethod
    def is_business_hours(cls, dt: Optional[datetime.datetime] = None) -> bool:
        """
        判断是否在工作时间内（9:00-18:00）
        
        Args:
            dt: 要检查的时间，默认为当前时间
            
        Returns:
            是否在工作时间内
        """
        if dt is None:
            dt = cls.get_beijing_now()
        
        beijing_dt = cls.to_beijing_time(dt)
        hour = beijing_dt.hour
        
        # 工作日的9:00-18:00
        if beijing_dt.weekday() < 5:  # 周一到周五
            return 9 <= hour < 18
        
        return False
