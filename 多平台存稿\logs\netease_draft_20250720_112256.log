[2025-07-20 11:22:56] [INFO] 平台切换前保存 toutiao 的设置和存稿详情数据
[2025-07-20 11:22:56] [INFO] ✅ 设置已静默保存
[2025-07-20 11:22:56] [INFO] 已确保所有必要目录存在
[2025-07-20 11:22:56] [INFO] 正在更新账号管理器，当前平台: dayu
[2025-07-20 11:22:56] [INFO] 账号目录: D:\网易号全自动\大鱼号账号
[2025-07-20 11:22:56] [INFO] 已加载 0 个dayu平台账号
[2025-07-20 11:22:56] [INFO] 🔍 [调试] 当前平台: dayu
[2025-07-20 11:22:56] [INFO] 🔍 [调试] 配置的账号目录: D:\网易号全自动\大鱼号账号
[2025-07-20 11:22:56] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\大鱼号账号
[2025-07-20 11:22:56] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\大鱼号账号\dayu_account_data.json
[2025-07-20 11:22:56] [INFO] 已加载账号数据: 0 条记录
[2025-07-20 11:22:56] [INFO] 账号数据文件路径: D:\网易号全自动\大鱼号账号\dayu_account_data.json
[2025-07-20 11:22:56] [INFO] 已更新数据查询管理器的AccountData引用
[2025-07-20 11:22:56] [INFO] 已更新Excel导出管理器的AccountData引用
[2025-07-20 11:22:56] [INFO] 已从dayu平台加载存稿详情数据，共 9 个账号
[2025-07-20 11:22:56] [INFO] 已切换到平台: 大鱼号平台
[2025-07-20 11:22:56] [INFO] 🔍 [调试] 当前平台: dayu
[2025-07-20 11:22:56] [INFO] 🔍 [调试] 配置的账号目录: D:\网易号全自动\大鱼号账号
[2025-07-20 11:22:56] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\大鱼号账号
[2025-07-20 11:22:56] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\大鱼号账号\dayu_account_data.json
[2025-07-20 11:22:56] [INFO] 🔍 [调试] 更新账号树 - 当前平台: dayu
[2025-07-20 11:22:56] [INFO] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\大鱼号账号\dayu_account_data.json
[2025-07-20 11:22:56] [INFO] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-07-20 11:22:56] [INFO] 已加载账号数据: 0 条记录
[2025-07-20 11:22:56] [INFO] 🔍 [调试] 账号数据为空或未加载
[2025-07-20 11:22:56] [INFO] ✅ 设置已静默保存
[2025-07-20 11:22:57] [INFO] 平台切换前保存 dayu 的设置和存稿详情数据
[2025-07-20 11:22:57] [INFO] ✅ 设置已静默保存
[2025-07-20 11:22:57] [INFO] 已确保所有必要目录存在
[2025-07-20 11:22:57] [INFO] 正在更新账号管理器，当前平台: toutiao
[2025-07-20 11:22:57] [INFO] 账号目录: D:/网易号全自动/头条号账号
[2025-07-20 11:22:57] [INFO] 已加载 59 个toutiao平台账号
[2025-07-20 11:22:57] [INFO] 🔍 [调试] 当前平台: toutiao
[2025-07-20 11:22:57] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-07-20 11:22:57] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-07-20 11:22:57] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-20 11:22:57] [INFO] 已加载账号数据: 79 条记录
[2025-07-20 11:22:57] [INFO] 账号数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-20 11:22:57] [INFO] 已更新数据查询管理器的AccountData引用
[2025-07-20 11:22:57] [INFO] 已更新Excel导出管理器的AccountData引用
[2025-07-20 11:22:57] [INFO] 已从toutiao平台加载存稿详情数据，共 83 个账号
[2025-07-20 11:22:57] [INFO] 已切换到平台: 今日头条平台
[2025-07-20 11:22:57] [INFO] 🔍 [调试] 当前平台: toutiao
[2025-07-20 11:22:57] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-07-20 11:22:57] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-07-20 11:22:57] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-20 11:22:57] [INFO] 🔍 [调试] 更新账号树 - 当前平台: toutiao
[2025-07-20 11:22:57] [INFO] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-20 11:22:57] [INFO] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-07-20 11:22:57] [INFO] 已加载账号数据: 79 条记录
[2025-07-20 11:22:57] [INFO] 🔍 [调试] 账号数据为空或未加载
[2025-07-20 11:22:57] [INFO] ✅ 设置已静默保存
[2025-07-20 11:22:58] [INFO] 已加载 59 个账号
[2025-07-20 11:22:58] [INFO] 🔍 [调试] 当前平台: toutiao
[2025-07-20 11:22:58] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-07-20 11:22:58] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-07-20 11:22:58] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-20 11:22:58] [INFO] 🔍 [调试] 更新账号树 - 当前平台: toutiao
[2025-07-20 11:22:58] [INFO] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-20 11:22:58] [INFO] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-07-20 11:22:58] [INFO] 已加载账号数据: 79 条记录
[2025-07-20 11:22:58] [INFO] 🔍 [调试] 账号数据为空或未加载
[2025-07-20 11:22:58] [INFO] 已加载 59 个账号
[2025-07-20 11:22:58] [INFO] 🔍 [调试] 当前平台: toutiao
[2025-07-20 11:22:58] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-07-20 11:22:58] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-07-20 11:22:58] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-20 11:22:58] [INFO] 🔍 [调试] 更新账号树 - 当前平台: toutiao
[2025-07-20 11:22:58] [INFO] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-20 11:22:58] [INFO] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-07-20 11:22:58] [INFO] 已加载账号数据: 79 条记录
[2025-07-20 11:22:58] [INFO] 🔍 [调试] 账号数据为空或未加载
[2025-07-20 11:22:59] [INFO] 正在关闭程序，清理后台资源...
[2025-07-20 11:22:59] [INFO] 🧹 开始清理所有资源...
[2025-07-20 11:22:59] [INFO] 正在停止并发管理器...
[2025-07-20 11:22:59] [INFO] ✅ 运行中的任务已停止
[2025-07-20 11:22:59] [INFO] ✅ 定时器线程已清理
[2025-07-20 11:23:00] [INFO] ✅ Chrome进程强制清理完成
[2025-07-20 11:23:00] [INFO] ✅ 浏览器进程已清理
[2025-07-20 11:23:01] [INFO] ✅ 日志处理线程已清理
[2025-07-20 11:23:01] [INFO] ✅ 存稿处理器已清理
[2025-07-20 11:23:01] [INFO] ✅ 线程池已清理
[2025-07-20 11:23:01] [INFO] 正在停止串行刷新工作线程...
[2025-07-20 11:23:04] [INFO] ⚠️ 仍有 3 个线程未结束
[2025-07-20 11:23:04] [INFO] ✅ 临时文件已清理
[2025-07-20 11:23:04] [INFO] ✅ 资源清理完成
[2025-07-20 11:23:04] [INFO] ✅ 设置已静默保存
[2025-07-20 11:23:04] [INFO] 程序清理完成，正在退出...
