"""
封面水印处理模块 - 处理封面水印的添加和编辑
"""

import os
from typing import Dict, Tuple, Optional, Callable, Union
from PIL import Image

def add_watermark_to_cover(processor,
                          cover_path: str,
                          watermark_color: str = "#FFFFFF",
                          opacity: float = 0.1,
                          position: str = "全屏",
                          quantity: int = 1) -> bool:
    """
    为封面添加纯色水印

    Args:
        processor: 封面处理器实例
        cover_path: 封面图片路径
        watermark_color: 水印颜色（十六进制）
        opacity: 水印透明度（0.0-1.0）
        position: 水印位置（"全屏", "左上角", "右上角", "左下角", "右下角", "中心"）
        quantity: 水印数量（仅在非全屏模式下有效）

    Returns:
        bool: 是否成功添加水印
    """
    try:
        # 导入需要的模块
        import random
        import math

        # 打开封面图片
        cover_img = Image.open(cover_path)
        width, height = cover_img.size

        # 将16进制颜色转换为RGB - 优化：使用更高效的16进制转换
        color_hex = watermark_color.lstrip('#')
        r, g, b = tuple(int(color_hex[i:i+2], 16) for i in (0, 2, 4))
        alpha = int(opacity * 255)

        # 确保图像是RGBA模式 - 优化：提前转换，避免后续重复检查
        if cover_img.mode != 'RGBA':
            cover_img = cover_img.convert('RGBA')

        # 全屏水印模式 - 优化：直接使用NumPy操作
        if position == "全屏":
            # 创建全屏水印层
            watermark_layer = Image.new('RGBA', (width, height), (r, g, b, alpha))

            # 直接合成并保存
            result = Image.alpha_composite(cover_img, watermark_layer)
            result.convert('RGB').save(cover_path, quality=95)
            return True

        # 非全屏模式 - 优化多水印生成算法
        # 限制水印数量在合理范围内
        quantity = max(1, min(10, quantity))

        # 计算水印尺寸（屏幕的1/4）
        wm_width = width // 4
        wm_height = height // 4

        # 创建透明的全屏图层
        full_layer = Image.new('RGBA', (width, height), (0, 0, 0, 0))

        # 创建水印图层 - 优化：只创建一次水印图层
        watermark_layer = Image.new('RGBA', (wm_width, wm_height), (r, g, b, alpha))

        # 单个水印的情况 - 优化：直接使用预定义位置
        if quantity == 1:
            # 预定义位置映射
            position_map = {
                "左上角": (0, 0),
                "右上角": (width - wm_width, 0),
                "左下角": (0, height - wm_height),
                "右下角": (width - wm_width, height - wm_height),
                "中心": ((width - wm_width) // 2, (height - wm_height) // 2)
            }

            # 获取位置或使用默认位置
            pos = position_map.get(position, ((width - wm_width) // 2, (height - wm_height) // 2))

            # 将水印放到指定位置
            full_layer.paste(watermark_layer, pos)
        else:
            # 多个水印 - 优化：使用网格布局算法代替随机位置

            # 设置随机种子，确保每次生成相同的随机位置
            random.seed(hash(cover_path) % 10000)

            # 定义区域范围
            region_map = {
                "左上角": {"x": (0, width // 2), "y": (0, height // 2)},
                "右上角": {"x": (width // 2, width), "y": (0, height // 2)},
                "左下角": {"x": (0, width // 2), "y": (height // 2, height)},
                "右下角": {"x": (width // 2, width), "y": (height // 2, height)},
                "中心": {"x": (width // 4, width * 3 // 4), "y": (height // 4, height * 3 // 4)}
            }

            # 获取区域或使用全图
            region = region_map.get(position, {"x": (0, width), "y": (0, height)})
            x_min, x_max = region["x"]
            y_min, y_max = region["y"]

            # 优化：使用网格布局算法
            if quantity <= 4:
                # 4个或更少水印时使用网格布局
                positions = []

                # 计算网格大小
                grid_width = (x_max - x_min) // 2
                grid_height = (y_max - y_min) // 2

                # 生成网格位置
                grid_positions = [
                    (x_min + grid_width // 2, y_min + grid_height // 2),
                    (x_max - grid_width // 2 - wm_width, y_min + grid_height // 2),
                    (x_min + grid_width // 2, y_max - grid_height // 2 - wm_height),
                    (x_max - grid_width // 2 - wm_width, y_max - grid_height // 2 - wm_height)
                ]

                # 随机选择quantity个位置
                positions = random.sample(grid_positions, quantity)
            else:
                # 优化：使用分区算法，避免重叠检测的O(n²)复杂度
                # 将区域分成quantity个子区域
                positions = []

                # 计算每个维度需要的分区数
                grid_size = math.ceil(math.sqrt(quantity))

                # 计算每个分区的大小
                cell_width = (x_max - x_min) / grid_size
                cell_height = (y_max - y_min) / grid_size

                # 生成分区中心点
                for i in range(min(quantity, grid_size * grid_size)):
                    row = i // grid_size
                    col = i % grid_size

                    # 计算分区中心
                    center_x = x_min + (col + 0.5) * cell_width
                    center_y = y_min + (row + 0.5) * cell_height

                    # 添加随机偏移（不超过分区大小的1/4）
                    offset_x = random.uniform(-cell_width/4, cell_width/4)
                    offset_y = random.uniform(-cell_height/4, cell_height/4)

                    # 计算最终位置，确保水印完全在图像内
                    pos_x = max(x_min, min(x_max - wm_width, center_x + offset_x - wm_width/2))
                    pos_y = max(y_min, min(y_max - wm_height, center_y + offset_y - wm_height/2))

                    positions.append((int(pos_x), int(pos_y)))

            # 将水印放到生成的位置
            for pos in positions:
                full_layer.paste(watermark_layer, pos)

        # 合成图像并保存 - 优化：使用正确的图层
        result = Image.alpha_composite(cover_img, full_layer)
        result.convert('RGB').save(cover_path, quality=95)

        return True
    except Exception as e:
        processor.logger(f"添加封面水印失败: {str(e)}")
        return False
