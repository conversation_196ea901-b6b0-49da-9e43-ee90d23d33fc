[2025-08-04 00:39:21] [INFO] 🚀 启动多平台存稿工具...
[2025-08-04 00:39:21] [INFO] ✅ 定时任务管理器UI初始化完成
[2025-08-04 00:39:21] [INFO] ✅ 定时任务管理标签页创建完成
[2025-08-04 00:41:22] [INFO] ✅ 定时任务管理器UI清理完成
25-08-04 00:39:21] [INFO] 已从toutiao平台加载存稿详情数据，共 83 个账号
[2025-08-04 00:39:21] [INFO] 🚀 定时任务调度器已启动
[2025-08-04 00:39:21] [DEBUG] 🔄 调度器主循环开始
[2025-08-04 00:39:22] [INFO] 已确保所有必要目录存在
[2025-08-04 00:39:22] [DEBUG] 🔍 [调试] 当前平台: toutiao
[2025-08-04 00:39:22] [DEBUG] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-08-04 00:39:22] [DEBUG] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-08-04 00:39:22] [SUCCESS] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-08-04 00:39:22] [DEBUG] 🔍 [调试] 更新账号树 - 当前平台: toutiao
[2025-08-04 00:39:22] [DEBUG] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-08-04 00:39:22] [DEBUG] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-08-04 00:39:22] [INFO] 已加载账号数据: 85 条记录
[2025-08-04 00:39:22] [DEBUG] 🔍 [调试] 账号数据为空或未加载
[2025-08-04 00:39:22] [SUCCESS] ✅ 初始UI更新完成
[2025-08-04 00:39:43] [INFO] 已选择账号: ***********
[2025-08-04 00:39:44] [DEBUG] 🚀 开始为账号 *********** 存稿
[2025-08-04 00:39:44] [INFO] 已确保所有必要目录存在
[2025-08-04 00:39:44] [INFO] 使用单线程模式处理账号
[2025-08-04 00:39:44] [DEBUG] 🔍 [DEBUG] 准备启动单线程任务，参数: ***********
[2025-08-04 00:39:44] [DEBUG] 🔍 [DEBUG] _single_task_thread 开始执行，参数: ***********
[2025-08-04 00:39:44] [DEBUG] 🔍 [DEBUG] 单线程任务已启动
[2025-08-04 00:39:44] [DEBUG] 📋 开始处理单个账号: ***********
[2025-08-04 00:39:44] [INFO] 视频分配方式: 随机分配
[2025-08-04 00:39:44] [DEBUG] 开始处理账号: ***********
[2025-08-04 00:39:44] [SUCCESS] 正在使用Cookie登录账号: ***********
[2025-08-04 00:39:44] [SUCCESS] ✅ Cookie预检测通过，启动浏览器验证...
[2025-08-04 00:40:03] [SUCCESS] 开始查询账号数据: *********** [toutiao] 浏览器驱动创建成功，使用端口: 9515
[2025-08-04 00:40:07] [SUCCESS] 正在加载Cookie文件: D:/网易号全自动/头条号账号\***********.txt
[2025-08-04 00:40:20] [SUCCESS] ✅ Cookie添加完成
[2025-08-04 00:40:21] [SUCCESS] ✅ 头条号快速登录成功 (等待0.5秒)
[2025-08-04 00:40:21] [SUCCESS] 登录成功，开始处理视频: ***********
[2025-08-04 00:40:21] [INFO] 找到 3 个视频文件
[2025-08-04 00:40:21] [INFO] 已随机打乱 3 个视频文件的顺序
[2025-08-04 00:40:21] [DEBUG] 开始处理视频: 19岁少年错付千元轻生，司机否认：他要套现！死无对证引哗然！.mp4
[2025-08-04 00:40:21] [DEBUG] 🎬 开始头条号视频存稿流程
[2025-08-04 00:40:21] [DEBUG] 🔍 正在定位视频菜单...
[2025-08-04 00:40:22] [SUCCESS] ✅ 已点击视频菜单
[2025-08-04 00:40:25] [INFO] 🌐 当前不在上传页面，导航到视频上传页面...
[2025-08-04 00:40:29] [SUCCESS] ✅ 已导航到视频上传页面
[2025-08-04 00:40:29] [INFO] 📁 准备上传视频: 19岁少年错付千元轻生，司机否认：他要套现！死无对证引哗然！.mp4
[2025-08-04 00:40:29] [DEBUG] 🔍 找到上传按钮，准备点击...
[2025-08-04 00:40:29] [SUCCESS] ✅ 已点击上传视频按钮
[2025-08-04 00:40:31] [SUCCESS] ✅ 视频文件已选择并开始上传
[2025-08-04 00:40:31] [DEBUG] ⏳ 开始监控视频上传状态...
[2025-08-04 00:40:31] [INFO] 📊 当前上传状态: 上传中… 0%
[2025-08-04 00:40:31] [DEBUG] ⏳ 视频正在上传中...
[2025-08-04 00:40:36] [INFO] 📊 当前上传状态: 上传中… 59.1%
[2025-08-04 00:40:36] [DEBUG] ⏳ 视频正在上传中...
[2025-08-04 00:40:41] [INFO] 📊 当前上传状态: 上传中… 72.32%
[2025-08-04 00:40:41] [DEBUG] ⏳ 视频正在上传中...
[2025-08-04 00:40:46] [INFO] 📊 当前上传状态: 上传中… 79.07%
[2025-08-04 00:40:46] [DEBUG] ⏳ 视频正在上传中...
[2025-08-04 00:40:51] [SUCCESS] 📊 当前上传状态: 上传成功
[2025-08-04 00:40:51] [SUCCESS] ✅ 视频上传成功！
[2025-08-04 00:40:51] [DEBUG] 📷 开始上传视频封面...
[2025-08-04 00:40:51] [SUCCESS] ✅ 已点击上传封面按钮
[2025-08-04 00:40:53] [SUCCESS] ✅ 已选择本地上传
[2025-08-04 00:41:04] [WARNING] ⚠️ 未找到指定上传区域，尝试直接查找文件输入
[2025-08-04 00:41:04] [SUCCESS] ✅ 封面文件已选择（备用方案）
[2025-08-04 00:41:04] [DEBUG] 🔍 开始检测封面canvas加载状态...
[2025-08-04 00:41:05] [ERROR] ❌ 未找到Canvas元素
[2025-08-04 00:41:05] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-04 00:41:06] [ERROR] ❌ 未找到Canvas元素
[2025-08-04 00:41:06] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-04 00:41:07] [ERROR] ❌ 未找到Canvas元素
[2025-08-04 00:41:07] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-04 00:41:08] [ERROR] ❌ 未找到Canvas元素
[2025-08-04 00:41:08] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-04 00:41:09] [ERROR] ❌ 未找到Canvas元素
[2025-08-04 00:41:09] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-04 00:41:10] [ERROR] ❌ 未找到Canvas元素
[2025-08-04 00:41:10] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-04 00:41:11] [ERROR] ❌ 未找到Canvas元素
[2025-08-04 00:41:11] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-04 00:41:12] [ERROR] ❌ 未找到Canvas元素
[2025-08-04 00:41:12] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-04 00:41:13] [ERROR] ❌ 未找到Canvas元素
[2025-08-04 00:41:13] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-04 00:41:14] [ERROR] ❌ 未找到Canvas元素
[2025-08-04 00:41:14] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-04 00:41:15] [ERROR] ❌ 未找到Canvas元素
[2025-08-04 00:41:15] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-04 00:41:16] [ERROR] ❌ 未找到Canvas元素
[2025-08-04 00:41:16] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-04 00:41:17] [ERROR] ❌ 未找到Canvas元素
[2025-08-04 00:41:17] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-04 00:41:18] [ERROR] ❌ 未找到Canvas元素
[2025-08-04 00:41:18] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-04 00:41:19] [ERROR] ❌ 未找到Canvas元素
[2025-08-04 00:41:19] [INFO] ⏳ canvas尚未完全加载，继续等待...
[2025-08-04 00:41:20] [ERROR] ❌ canvas加载检测超时（15秒）
[2025-08-04 00:41:20] [WARNING] ⚠️ 封面canvas可能未完全加载，但继续后续操作
[2025-08-04 00:41:22] [DEBUG] 正在关闭程序，清理后台资源...
[2025-08-04 00:41:22] [DEBUG] 🔄 调度器主循环结束
[2025-08-04 00:41:22] [DEBUG] 正在关闭任务执行器...
[2025-08-04 00:41:22] [INFO] 任务执行器已关闭
[2025-08-04 00:41:22] [INFO] ⏹️ 定时任务调度器已停止
[2025-08-04 00:41:22] [SUCCESS] ✅ 调度器管理器清理完成
[2025-08-04 00:41:22] [SUCCESS] ✅ 定时任务管理器UI清理完成
[2025-08-04 00:41:22] [DEBUG] 🧹 开始清理所有资源...
[2025-08-04 00:41:22] [DEBUG] ⏹️ 正在停止存稿任务...
[2025-08-04 00:41:22] [INFO] ⏹️ 存稿任务已停止
[2025-08-04 00:41:22] [DEBUG] 正在停止并发管理器...
[2025-08-04 00:41:22] [SUCCESS] ✅ 运行中的任务已停止
[2025-08-04 00:41:22] [SUCCESS] ✅ 定时器线程已清理（升级版系统自动管理）
[2025-08-04 00:41:23] [WARNING] ⚠️ 未找到确定按钮，封面可能已自动确认
[2025-08-04 00:41:23] [SUCCESS] ✅ Chrome进程强制清理完成
[2025-08-04 00:41:23] [SUCCESS] ✅ 浏览器进程已清理
[2025-08-04 00:41:24] [SUCCESS] ✅ 日志处理线程已清理
[2025-08-04 00:41:24] [SUCCESS] ✅ 存稿处理器已清理
[2025-08-04 00:41:24] [SUCCESS] ✅ 线程池已清理
[2025-08-04 00:41:24] [DEBUG] 正在停止串行刷新工作线程...
[2025-08-04 00:41:25] [SUCCESS] ✅ 视频封面上传完成
[2025-08-04 00:41:25] [DEBUG] 💾 开始保存草稿...
