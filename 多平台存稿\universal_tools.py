#!/usr/bin/env python3
"""
多平台存稿通用工具集
集成了所有实用工具的统一入口
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import traceback

# 获取当前脚本所在目录
current_dir = os.path.dirname(os.path.abspath(__file__))

# 添加当前目录到Python路径
sys.path.insert(0, current_dir)

class UniversalToolsGUI:
    """通用工具集图形界面"""
    
    def __init__(self):
        """初始化通用工具集"""
        self.root = tk.Tk()
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        self.root.title("🛠️ 多平台存稿通用工具集")
        self.root.geometry("900x700")
        self.root.minsize(800, 600)
        
        # 设置窗口居中
        self._center_window()
        
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建标题
        title_label = ttk.Label(main_frame, 
                               text="🛠️ 多平台存稿通用工具集", 
                               font=("Microsoft YaHei", 20, "bold"))
        title_label.pack(pady=(0, 10))
        
        # 创建副标题
        subtitle_label = ttk.Label(main_frame, 
                                  text="集成所有实用工具的统一管理平台",
                                  font=("Microsoft YaHei", 12))
        subtitle_label.pack(pady=(0, 30))
        
        # 创建工具分类
        self.create_tool_categories(main_frame)
        
        # 创建底部信息
        self.create_bottom_info(main_frame)
    
    def create_tool_categories(self, parent):
        """创建工具分类"""
        # 创建笔记本控件
        notebook = ttk.Notebook(parent)
        notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # 主要工具标签页
        main_tools_frame = ttk.Frame(notebook)
        notebook.add(main_tools_frame, text="🚀 主要工具")
        self.create_main_tools(main_tools_frame)
        
        # 系统工具标签页
        system_tools_frame = ttk.Frame(notebook)
        notebook.add(system_tools_frame, text="🔧 系统工具")
        self.create_system_tools(system_tools_frame)
        
        # 辅助工具标签页
        helper_tools_frame = ttk.Frame(notebook)
        notebook.add(helper_tools_frame, text="🎯 辅助工具")
        self.create_helper_tools(helper_tools_frame)
    
    def create_main_tools(self, parent):
        """创建主要工具"""
        # 创建滚动框架（删除滚动条）
        canvas = tk.Canvas(parent)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        
        # 主要工具列表
        main_tools = [
            {
                "title": "🌐 网易号存稿工具",
                "description": "网易号平台的自动存稿工具，支持批量操作和定时任务",
                "command": self.launch_netease_tool,
                "color": "#0d6efd"
            },
            {
                "title": "📰 头条存稿工具", 
                "description": "今日头条平台的自动存稿工具，支持多账号管理",
                "command": self.launch_toutiao_tool,
                "color": "#dc3545"
            },
            {
                "title": "🎬 视频处理工具",
                "description": "专业的视频处理工具，支持格式转换、剪辑等功能",
                "command": self.launch_video_processor,
                "color": "#6f42c1"
            },
            {
                "title": "📥 视频下载工具",
                "description": "支持多平台视频下载，高质量视频获取",
                "command": self.launch_video_downloader,
                "color": "#fd7e14"
            },
            {
                "title": "🔥 每日爆文工具",
                "description": "获取热门内容和爆文数据，助力内容创作",
                "command": self.launch_daily_hot,
                "color": "#20c997"
            }
        ]
        
        # 创建工具卡片
        for i, tool in enumerate(main_tools):
            self.create_tool_card(scrollable_frame, tool, row=i//2, column=i%2)
        
        # 布局（删除滚动条）
        canvas.pack(fill=tk.BOTH, expand=True)

        # 绑定鼠标滚轮事件以支持滚动
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        def _bind_mousewheel(event):
            canvas.bind_all("<MouseWheel>", _on_mousewheel)

        def _unbind_mousewheel(event):
            canvas.unbind_all("<MouseWheel>")

        canvas.bind('<Enter>', _bind_mousewheel)
        canvas.bind('<Leave>', _unbind_mousewheel)
    
    def create_system_tools(self, parent):
        """创建系统工具"""
        # 创建滚动框架（删除滚动条）
        canvas = tk.Canvas(parent)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        
        # 系统工具列表
        system_tools = [
            {
                "title": "🧹 缓存清理工具",
                "description": "清理软件运行产生的缓存文件，释放磁盘空间",
                "command": self.launch_cache_cleaner,
                "color": "#198754"
            },
            {
                "title": "📊 系统监控工具",
                "description": "监控系统资源使用情况和程序运行状态",
                "command": self.launch_system_monitor,
                "color": "#6610f2"
            },
            {
                "title": "🔐 账号管理工具",
                "description": "统一管理所有平台账号信息和登录状态",
                "command": self.launch_account_manager,
                "color": "#d63384"
            },
            {
                "title": "📝 日志查看工具",
                "description": "查看和分析程序运行日志，快速定位问题",
                "command": self.launch_log_viewer,
                "color": "#0dcaf0"
            }
        ]
        
        # 创建工具卡片
        for i, tool in enumerate(system_tools):
            self.create_tool_card(scrollable_frame, tool, row=i//2, column=i%2)
        
        # 布局（删除滚动条）
        canvas.pack(fill=tk.BOTH, expand=True)

        # 绑定鼠标滚轮事件以支持滚动
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        def _bind_mousewheel(event):
            canvas.bind_all("<MouseWheel>", _on_mousewheel)

        def _unbind_mousewheel(event):
            canvas.unbind_all("<MouseWheel>")

        canvas.bind('<Enter>', _bind_mousewheel)
        canvas.bind('<Leave>', _unbind_mousewheel)
    
    def create_helper_tools(self, parent):
        """创建辅助工具"""
        # 创建滚动框架（删除滚动条）
        canvas = tk.Canvas(parent)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        
        # 辅助工具列表
        helper_tools = [
            {
                "title": "📋 配置管理工具",
                "description": "管理所有工具的配置文件和参数设置",
                "command": self.launch_config_manager,
                "color": "#495057"
            },
            {
                "title": "🔄 数据同步工具",
                "description": "同步不同平台的数据和配置信息",
                "command": self.launch_data_sync,
                "color": "#6c757d"
            },
            {
                "title": "📈 数据分析工具",
                "description": "分析账号数据和运营效果，生成报表",
                "command": self.launch_data_analyzer,
                "color": "#adb5bd"
            },
            {
                "title": "🛠️ 开发者工具",
                "description": "开发和调试相关的实用工具集合",
                "command": self.launch_dev_tools,
                "color": "#343a40"
            }
        ]
        
        # 创建工具卡片
        for i, tool in enumerate(helper_tools):
            self.create_tool_card(scrollable_frame, tool, row=i//2, column=i%2)
        
        # 布局（删除滚动条）
        canvas.pack(fill=tk.BOTH, expand=True)

        # 绑定鼠标滚轮事件以支持滚动
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        def _bind_mousewheel(event):
            canvas.bind_all("<MouseWheel>", _on_mousewheel)

        def _unbind_mousewheel(event):
            canvas.unbind_all("<MouseWheel>")

        canvas.bind('<Enter>', _bind_mousewheel)
        canvas.bind('<Leave>', _unbind_mousewheel)
    
    def create_tool_card(self, parent, tool_info, row, column):
        """创建工具卡片"""
        # 创建卡片框架
        card_frame = ttk.LabelFrame(parent, text="", padding=15)
        card_frame.grid(row=row, column=column, padx=10, pady=10, sticky="nsew")
        
        # 配置网格权重
        parent.grid_columnconfigure(column, weight=1)
        parent.grid_rowconfigure(row, weight=1)
        
        # 工具标题
        title_label = ttk.Label(card_frame, 
                               text=tool_info["title"],
                               font=("Microsoft YaHei", 14, "bold"))
        title_label.pack(anchor=tk.W, pady=(0, 8))
        
        # 工具描述
        desc_label = ttk.Label(card_frame, 
                              text=tool_info["description"],
                              font=("Microsoft YaHei", 10),
                              wraplength=300,
                              justify=tk.LEFT)
        desc_label.pack(anchor=tk.W, pady=(0, 15))
        
        # 启动按钮
        launch_button = ttk.Button(card_frame, 
                                  text="🚀 启动工具",
                                  command=tool_info["command"])
        launch_button.pack(anchor=tk.W)
    
    def create_bottom_info(self, parent):
        """创建底部信息"""
        info_frame = ttk.Frame(parent)
        info_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 版本信息
        version_label = ttk.Label(info_frame, 
                                 text="版本: v2.0.0 | 更新时间: 2024-12-28",
                                 font=("Microsoft YaHei", 9))
        version_label.pack(side=tk.LEFT)
        
        # 关闭按钮
        close_button = ttk.Button(info_frame,
                                 text="❌ 关闭",
                                 command=self.root.destroy)
        close_button.pack(side=tk.RIGHT)

    def _center_window(self):
        """将窗口居中显示"""
        try:
            self.root.update_idletasks()
            width = self.root.winfo_width()
            height = self.root.winfo_height()
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()
            x = (screen_width - width) // 2
            y = (screen_height - height) // 2
            self.root.geometry(f"+{x}+{y}")
        except Exception as e:
            print(f"窗口居中失败: {str(e)}")

    # ==================== 主要工具启动方法 ====================

    def launch_netease_tool(self):
        """启动网易号存稿工具"""
        try:
            # 启动网易号存稿工具
            python_exe = sys.executable
            script_path = os.path.join(current_dir, "run_app.py")

            if os.path.exists(script_path):
                subprocess.Popen([python_exe, script_path])
                messagebox.showinfo("启动成功", "网易号存稿工具已启动")
            else:
                messagebox.showerror("启动失败", "找不到网易号存稿工具文件")

        except Exception as e:
            messagebox.showerror("启动错误", f"启动网易号存稿工具失败: {str(e)}")

    def launch_toutiao_tool(self):
        """启动头条存稿工具"""
        try:
            # 启动头条存稿工具（通过主程序切换平台）
            python_exe = sys.executable
            script_path = os.path.join(current_dir, "run_app.py")

            if os.path.exists(script_path):
                subprocess.Popen([python_exe, script_path])
                messagebox.showinfo("启动成功", "多平台存稿工具已启动\n请在程序中切换到头条平台")
            else:
                messagebox.showerror("启动失败", "找不到头条存稿工具文件")

        except Exception as e:
            messagebox.showerror("启动错误", f"启动头条存稿工具失败: {str(e)}")

    def launch_video_processor(self, auto_mode=False, status_file=None):
        """启动视频处理工具

        Args:
            auto_mode (bool): 是否启用自动模式
            status_file (str): 状态文件路径（可选）
        """
        try:
            # 检查是否在打包环境中
            if getattr(sys, 'frozen', False):
                # 在打包环境中，使用exe文件
                video_processor_exe = os.path.join(current_dir, "_internal", "视频处理工具", "视频处理工具.exe")

                if os.path.exists(video_processor_exe):
                    # 构建命令参数
                    cmd = [video_processor_exe]

                    # 添加自动模式参数
                    if auto_mode:
                        cmd.append("--auto-mode")
                        print("🚀 启动视频处理工具（自动模式）")
                    else:
                        print("🚀 启动视频处理工具（手动模式）")

                    # 添加状态文件参数
                    if status_file:
                        cmd.extend(["--status-file", status_file])
                        print(f"📊 状态文件: {status_file}")

                    print(f"执行命令: {' '.join(cmd)}")

                    # 启动进程，不创建新控制台
                    process = subprocess.Popen(cmd)

                    success_msg = f"视频处理工具已启动，进程ID: {process.pid}"
                    print(f"✅ {success_msg}")
                    messagebox.showinfo("启动成功", success_msg)
                    return True
                else:
                    error_msg = f"找不到视频处理工具exe文件: {video_processor_exe}"
                    print(f"❌ {error_msg}")
                    messagebox.showerror("启动失败", error_msg)
                    return False
            else:
                # 在开发环境中，使用Python脚本
                python_exe = sys.executable
                script_path = os.path.join(current_dir, "视频处理工具", "main.py")

                if not os.path.exists(script_path):
                    script_path = os.path.join(current_dir, "视频处理工具", "视频处理工具.py")

                if os.path.exists(script_path):
                    # 构建命令参数
                    cmd = [python_exe, script_path]

                    # 添加自动模式参数
                    if auto_mode:
                        cmd.append("--auto-mode")
                        print("🚀 启动视频处理工具（自动模式）")
                    else:
                        print("🚀 启动视频处理工具（手动模式）")

                    # 添加状态文件参数
                    if status_file:
                        cmd.extend(["--status-file", status_file])
                        print(f"📊 状态文件: {status_file}")

                    print(f"执行命令: {' '.join(cmd)}")

                    # 启动进程，不创建新控制台
                    process = subprocess.Popen(cmd)

                    success_msg = f"视频处理工具已启动，进程ID: {process.pid}"
                    print(f"✅ {success_msg}")
                    messagebox.showinfo("启动成功", success_msg)
                    return True
                else:
                    error_msg = "找不到视频处理工具文件"
                    print(f"❌ {error_msg}")
                    messagebox.showerror("启动失败", error_msg)
                    return False

        except Exception as e:
            error_msg = f"启动视频处理工具失败: {str(e)}"
            print(f"❌ {error_msg}")
            messagebox.showerror("启动错误", error_msg)
            return False

    def launch_video_downloader(self):
        """启动视频下载工具"""
        try:
            # 导入视频下载工具对话框
            from 网易号存稿.ui.dialogs.video_downloader_dialog import VideoDownloaderDialog
            from 网易号存稿.common.config import ConfigManager

            # 创建配置管理器
            config_file = os.path.join(current_dir, "config.json")
            config_manager = ConfigManager(config_file, base_dir=current_dir)

            # 创建对话框
            video_downloader_dialog = VideoDownloaderDialog(self.root, config_manager)

            # 等待对话框关闭
            self.root.wait_window(video_downloader_dialog.dialog)

        except Exception as e:
            messagebox.showerror("启动错误", f"启动视频下载工具失败: {str(e)}")

    def launch_daily_hot(self):
        """启动每日爆文工具"""
        try:
            # 导入每日爆文工具对话框
            from 网易号存稿.ui.dialogs.daily_hot_dialog import DailyHotDialog
            from 网易号存稿.common.config import ConfigManager

            # 创建配置管理器
            config_file = os.path.join(current_dir, "config.json")
            config_manager = ConfigManager(config_file, base_dir=current_dir)

            # 创建对话框
            daily_hot_dialog = DailyHotDialog(self.root, config_manager)

            # 等待对话框关闭
            self.root.wait_window(daily_hot_dialog.dialog)

        except Exception as e:
            messagebox.showerror("启动错误", f"启动每日爆文工具失败: {str(e)}")

    # ==================== 系统工具启动方法 ====================

    def launch_cache_cleaner(self):
        """启动缓存清理工具"""
        try:
            # 导入缓存清理工具
            from 网易号存稿.tools.cache_cleaner import CacheCleanerGUI

            # 创建缓存清理工具窗口
            cache_window = tk.Toplevel(self.root)
            cache_window.title("🧹 缓存清理工具")
            cache_window.geometry("800x800")
            cache_window.minsize(700, 700)

            # 创建缓存清理器GUI
            cache_cleaner_gui = CacheCleanerGUI(cache_window)

            # 居中显示窗口
            self._center_child_window(cache_window)

            print("✅ 缓存清理工具已启动")

        except Exception as e:
            messagebox.showerror("启动错误", f"启动缓存清理工具失败: {str(e)}")
            print(f"❌ 启动缓存清理工具失败: {str(e)}")
            traceback.print_exc()

    def launch_system_monitor(self):
        """启动系统监控工具"""
        messagebox.showinfo("开发中", "系统监控工具正在开发中，敬请期待！")

    def launch_account_manager(self):
        """启动账号管理工具"""
        messagebox.showinfo("开发中", "账号管理工具正在开发中，敬请期待！")

    def launch_log_viewer(self):
        """启动日志查看工具"""
        messagebox.showinfo("开发中", "日志查看工具正在开发中，敬请期待！")

    # ==================== 辅助工具启动方法 ====================

    def launch_config_manager(self):
        """启动配置管理工具"""
        messagebox.showinfo("开发中", "配置管理工具正在开发中，敬请期待！")

    def launch_data_sync(self):
        """启动数据同步工具"""
        messagebox.showinfo("开发中", "数据同步工具正在开发中，敬请期待！")

    def launch_data_analyzer(self):
        """启动数据分析工具"""
        messagebox.showinfo("开发中", "数据分析工具正在开发中，敬请期待！")

    def launch_dev_tools(self):
        """启动开发者工具"""
        messagebox.showinfo("开发中", "开发者工具正在开发中，敬请期待！")

    def _center_child_window(self, window):
        """将子窗口居中显示"""
        try:
            window.update_idletasks()
            width = window.winfo_width()
            height = window.winfo_height()
            screen_width = window.winfo_screenwidth()
            screen_height = window.winfo_screenheight()
            x = (screen_width - width) // 2
            y = (screen_height - height) // 2
            window.geometry(f"+{x}+{y}")
        except Exception as e:
            print(f"子窗口居中失败: {str(e)}")

    def run(self):
        """运行通用工具集"""
        self.root.mainloop()


def main():
    """主函数"""
    try:
        # 创建通用工具集GUI
        app = UniversalToolsGUI()

        # 运行应用程序
        app.run()

    except Exception as e:
        print(f"启动通用工具集失败: {str(e)}")
        traceback.print_exc()
        messagebox.showerror("启动错误", f"启动通用工具集失败: {str(e)}")


if __name__ == "__main__":
    main()
