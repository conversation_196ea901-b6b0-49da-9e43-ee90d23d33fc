"""
配置管理模块 - 处理配置的加载和保存
支持环境变量和相对路径
"""

import os
import json
import re
import time
import threading
from pathlib import Path
from typing import Dict, Any, Callable, Optional, Union

def resolve_path(path: Union[str, Path], base_dir: Optional[Union[str, Path]] = None) -> str:
    """
    解析路径，支持环境变量和相对路径

    Args:
        path: 要解析的路径
        base_dir: 相对路径的基准目录，如果为None则使用当前工作目录

    Returns:
        解析后的绝对路径
    """
    # 转换为字符串
    path_str = str(path)

    # 解析环境变量
    # 支持 ${ENV_VAR} 和 %ENV_VAR% 两种格式
    # 例如: ${USERPROFILE}/Documents 或 %USERPROFILE%/Documents
    if '${' in path_str and '}' in path_str:
        # 匹配 ${ENV_VAR} 格式
        for match in re.finditer(r'\${([^}]+)}', path_str):
            env_var = match.group(1)
            env_value = os.environ.get(env_var, '')
            path_str = path_str.replace(match.group(0), env_value)

    if '%' in path_str:
        # 匹配 %ENV_VAR% 格式
        for match in re.finditer(r'%([^%]+)%', path_str):
            env_var = match.group(1)
            env_value = os.environ.get(env_var, '')
            path_str = path_str.replace(match.group(0), env_value)

    # 创建Path对象
    path_obj = Path(path_str)

    # 如果是相对路径，则相对于base_dir或当前工作目录
    if not path_obj.is_absolute():
        if base_dir:
            base_path = Path(base_dir)
            path_obj = base_path / path_obj
        else:
            path_obj = Path.cwd() / path_obj

    # 返回规范化的绝对路径
    return str(path_obj.resolve())


class ConfigManager:
    """配置管理器类，负责加载和保存配置，支持环境变量和相对路径"""

    def __init__(self, config_file: str = "config.json", log_callback: Callable = None, base_dir: Optional[str] = None):
        """
        初始化配置管理器

        Args:
            config_file: 配置文件路径，支持相对路径和环境变量
            log_callback: 日志回调函数
            base_dir: 相对路径的基准目录，如果为None则使用当前工作目录
        """
        # 解析配置文件路径
        self.config_file = resolve_path(config_file, base_dir)
        self.log_callback = log_callback
        self.base_dir = base_dir or os.path.dirname(self.config_file)
        self.config = self._get_default_config()

        # 延迟保存相关属性
        self.save_timer = None
        self.save_lock = threading.Lock()
        self.save_delay = 2.0  # 延迟保存时间（秒）
        self.changes_pending = False

        # 加载配置
        self.load_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """
        获取默认配置，使用相对路径和环境变量

        Returns:
            默认配置字典
        """
        # 获取用户文档目录作为默认基础目录
        user_docs = os.path.expanduser("~/Documents")

        # 尝试从环境变量获取基础目录
        base_dir = os.environ.get("NETEASE_BASE_DIR", user_docs)

        # 创建默认配置
        default_config = {
            # 多平台支持
            "current_platform": "netease",  # 默认为网易号平台
            "available_platforms": ["netease", "toutiao", "dayu"],  # 可用平台列表
            "platform_names": {  # 平台显示名称
                "netease": "网易号平台",
                "toutiao": "今日头条平台",
                "dayu": "大鱼号平台"
            },

            # 网易号平台配置
            "netease": {
                # 使用相对路径或环境变量
                "account_dir": "${NETEASE_BASE_DIR}/网易号全自动/账号" if "NETEASE_BASE_DIR" in os.environ else f"{base_dir}/网易号全自动/账号",
                "video_dir": "${NETEASE_BASE_DIR}/网易号全自动/未处理" if "NETEASE_BASE_DIR" in os.environ else f"{base_dir}/网易号全自动/未处理",
                "cover_dir": "${NETEASE_BASE_DIR}/网易号全自动/未处理" if "NETEASE_BASE_DIR" in os.environ else f"{base_dir}/网易号全自动/未处理",
                "processed_dir": "${NETEASE_BASE_DIR}/网易号全自动/已处理" if "NETEASE_BASE_DIR" in os.environ else f"{base_dir}/网易号全自动/已处理",
                "processed_covers_dir": "${NETEASE_BASE_DIR}/网易号全自动/已处理/封面" if "NETEASE_BASE_DIR" in os.environ else f"{base_dir}/网易号全自动/已处理/封面",
                "violation_dir": "${NETEASE_BASE_DIR}/网易号全自动/违规" if "NETEASE_BASE_DIR" in os.environ else f"{base_dir}/网易号全自动/违规",
                "screenshots_dir": "D:/网易号全自动/截图",

                # 运行选项
                "archive_completed": True,
                "headless_mode": False,
                "draft_limit": 0,
                "loop_limit": 0,
                "concurrent_accounts": 1,
                "random_video_allocation": True,

                # 数据查询选项
                "query_headless_mode": False,  # 网易号数据查询默认不使用无头模式
                "query_max_threads": 10,  # 网易号数据查询默认并发线程数
                "query_batch_size": 10,  # 网易号数据查询批处理大小

                # 其他选项
                "account_progress": {}
            },

            # 今日头条平台配置
            "toutiao": {
                # 使用相对路径或环境变量
                "account_dir": "${TOUTIAO_BASE_DIR}/头条全自动/视频搬运/有效账号" if "TOUTIAO_BASE_DIR" in os.environ else "D:/头条全自动/视频搬运/有效账号",
                "video_dir": "${TOUTIAO_BASE_DIR}/头条全自动/未处理" if "TOUTIAO_BASE_DIR" in os.environ else f"{base_dir}/头条全自动/未处理",
                "cover_dir": "${TOUTIAO_BASE_DIR}/头条全自动/未处理" if "TOUTIAO_BASE_DIR" in os.environ else f"{base_dir}/头条全自动/未处理",
                "processed_dir": "${TOUTIAO_BASE_DIR}/头条全自动/已处理" if "TOUTIAO_BASE_DIR" in os.environ else f"{base_dir}/头条全自动/已处理",
                "processed_covers_dir": "${TOUTIAO_BASE_DIR}/头条全自动/已处理/封面" if "TOUTIAO_BASE_DIR" in os.environ else f"{base_dir}/头条全自动/已处理/封面",
                "violation_dir": "${TOUTIAO_BASE_DIR}/头条全自动/违规" if "TOUTIAO_BASE_DIR" in os.environ else f"{base_dir}/头条全自动/违规",
                "screenshots_dir": "D:/头条全自动/截图",

                # 运行选项
                "archive_completed": True,
                "headless_mode": True,  # 头条平台默认使用无头模式
                "draft_limit": 0,
                "loop_limit": 0,
                "concurrent_accounts": 3,  # 头条平台默认支持3个并发
                "random_video_allocation": True,

                # 数据查询选项
                "query_headless_mode": True,  # 数据查询专用无头模式设置
                "query_max_threads": 10,  # 数据查询最大并发线程数
                "query_batch_size": 10,  # 数据查询批处理大小

                # 其他选项
                "account_progress": {}
            },

            # 大鱼号平台配置
            "dayu": {
                # 使用相对路径或环境变量
                "account_dir": "${DAYU_BASE_DIR}/大鱼号全自动/账号" if "DAYU_BASE_DIR" in os.environ else f"{base_dir}/大鱼号全自动/账号",
                "video_dir": "${DAYU_BASE_DIR}/大鱼号全自动/未处理" if "DAYU_BASE_DIR" in os.environ else f"{base_dir}/大鱼号全自动/未处理",
                "cover_dir": "${DAYU_BASE_DIR}/大鱼号全自动/未处理" if "DAYU_BASE_DIR" in os.environ else f"{base_dir}/大鱼号全自动/未处理",
                "processed_dir": "${DAYU_BASE_DIR}/大鱼号全自动/已处理" if "DAYU_BASE_DIR" in os.environ else f"{base_dir}/大鱼号全自动/已处理",
                "processed_covers_dir": "${DAYU_BASE_DIR}/大鱼号全自动/已处理/封面" if "DAYU_BASE_DIR" in os.environ else f"{base_dir}/大鱼号全自动/已处理/封面",
                "violation_dir": "${DAYU_BASE_DIR}/大鱼号全自动/违规" if "DAYU_BASE_DIR" in os.environ else f"{base_dir}/大鱼号全自动/违规",
                "screenshots_dir": "D:/大鱼号全自动/截图",

                # 运行选项
                "archive_completed": True,
                "headless_mode": False,  # 大鱼号平台默认不使用无头模式
                "draft_limit": 0,
                "loop_limit": 0,
                "concurrent_accounts": 1,  # 大鱼号平台默认单账号处理
                "random_video_allocation": True,

                # 数据查询选项
                "query_headless_mode": False,  # 大鱼号数据查询默认不使用无头模式
                "query_max_threads": 10,  # 大鱼号数据查询默认并发线程数
                "query_batch_size": 10,  # 大鱼号数据查询批处理大小

                # 其他选项
                "account_progress": {}
            },

            # 通用配置（所有平台共享）
            "common": {
                # 日志选项
                "enable_level_filter": False,
                "enable_keyword_filter": False,
                "min_log_level": "INFO",

                # UI选项
                "dark_mode": False,
                "font_size": 17,

                # 旧定时任务配置已移除，现在使用升级版定时任务系统
                # 升级版系统使用独立的配置存储机制
            }
        }

        return default_config

    def load_config(self) -> bool:
        """
        从文件加载配置

        Returns:
            是否成功加载配置
        """
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    # 更新配置，保留默认值
                    for key, value in loaded_config.items():
                        self.config[key] = value
                self.log(f"已加载配置文件: {self.config_file}")
                return True
            else:
                self.log(f"配置文件不存在，将使用默认配置")
                self.save_config(force=True)  # 创建默认配置文件，强制立即保存
                return False
        except Exception as e:
            self.log(f"加载配置文件失败: {str(e)}")
            return False

    def save_config(self, force: bool = False) -> bool:
        """
        保存配置到文件，支持延迟保存

        Args:
            force: 是否强制立即保存，不使用延迟机制

        Returns:
            是否成功保存配置
        """
        # 如果强制保存，立即执行保存操作
        if force:
            self.log(f"正在强制立即保存配置到: {self.config_file}")
            result = self._do_save_config()
            if result:
                self.log(f"✅ 配置已成功保存到: {self.config_file}")
            else:
                self.log(f"❌ 配置保存失败: {self.config_file}")
            return result

        # 否则使用延迟保存机制
        with self.save_lock:
            self.changes_pending = True
            self.log(f"已标记配置变更，将在 {self.save_delay} 秒后保存")

            # 如果已经有定时器在运行，取消它
            if self.save_timer:
                self.save_timer.cancel()
                self.log("已取消之前的保存定时器")

            # 创建新的定时器
            self.save_timer = threading.Timer(self.save_delay, self._delayed_save)
            self.save_timer.daemon = True
            self.save_timer.start()
            self.log(f"已启动新的保存定时器，将在 {self.save_delay} 秒后保存")

        return True

    def _delayed_save(self) -> None:
        """延迟保存的内部方法"""
        with self.save_lock:
            if self.changes_pending:
                self.log("执行延迟保存操作...")
                result = self._do_save_config()
                if result:
                    self.log("✅ 延迟保存成功完成")
                else:
                    self.log("❌ 延迟保存失败")
                self.changes_pending = False
            else:
                self.log("没有待保存的更改，跳过保存")

    def _do_save_config(self) -> bool:
        """实际执行保存配置的内部方法"""
        try:
            # 确保配置文件目录存在
            config_dir = os.path.dirname(self.config_file)
            if not os.path.exists(config_dir):
                os.makedirs(config_dir)
                self.log(f"已创建配置目录: {config_dir}")

            # 保存前记录配置内容摘要
            config_summary = {
                "draft_limit": self.config.get("draft_limit", "未设置"),
                "loop_limit": self.config.get("loop_limit", "未设置"),
                "concurrent_accounts": self.config.get("concurrent_accounts", "未设置"),
                "headless_mode": self.config.get("headless_mode", "未设置"),
                "archive_completed": self.config.get("archive_completed", "未设置"),
                "random_video_allocation": self.config.get("random_video_allocation", "未设置")
            }
            self.log(f"准备保存配置，关键设置: {config_summary}")

            # 执行保存
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=4)

            self.log(f"✅ 已成功保存配置到: {self.config_file}")
            return True
        except Exception as e:
            self.log(f"❌ 保存配置文件失败: {str(e)}")
            import traceback
            self.log(f"错误详情: {traceback.format_exc()}")
            return False

    def get(self, key: str, default: Any = None, resolve: bool = False, platform: str = None) -> Any:
        """
        获取配置项，可选择解析路径

        Args:
            key: 配置项键名
            default: 默认值
            resolve: 是否解析路径（如果值是字符串且看起来像路径）
            platform: 平台标识，如果为None则使用当前平台

        Returns:
            配置项值，如果resolve=True且值是路径，则返回解析后的路径
        """
        # 如果未指定平台，使用当前平台
        if platform is None:
            platform = self.config.get("current_platform", "netease")

        # 检查是否是通用配置
        if key in self.config.get("common", {}):
            value = self.config["common"].get(key, default)
        # 检查是否是平台特定配置
        elif platform in self.config and key in self.config[platform]:
            value = self.config[platform].get(key, default)
        # 检查是否是顶级配置
        else:
            value = self.config.get(key, default)

        # 如果需要解析路径，且值是字符串，且看起来像路径
        if resolve and isinstance(value, str) and ('/' in value or '\\' in value or ':' in value):
            return resolve_path(value, self.base_dir)

        return value

    def get_path(self, key: str, default: Any = None, platform: str = None) -> str:
        """
        获取配置项并解析为路径

        Args:
            key: 配置项键名
            default: 默认值
            platform: 平台标识，如果为None则使用当前平台

        Returns:
            解析后的路径
        """
        return self.get(key, default, resolve=True, platform=platform)

    def get_current_platform(self) -> str:
        """
        获取当前平台标识

        Returns:
            当前平台标识
        """
        return self.config.get("current_platform", "netease")

    def get_platform_name(self, platform: str = None) -> str:
        """
        获取平台显示名称

        Args:
            platform: 平台标识，如果为None则使用当前平台

        Returns:
            平台显示名称
        """
        if platform is None:
            platform = self.get_current_platform()

        platform_names = self.config.get("platform_names", {})
        return platform_names.get(platform, platform)

    def set(self, key: str, value: Any, force_save: bool = False, platform: str = None) -> None:
        """
        设置配置项

        Args:
            key: 配置项键名
            value: 配置项值
            force_save: 是否强制立即保存
            platform: 平台标识，如果为None则使用当前平台
        """
        # 如果未指定平台，使用当前平台
        if platform is None:
            platform = self.config.get("current_platform", "netease")

        # 确定配置项应该保存在哪个位置
        if key in ["current_platform", "available_platforms", "platform_names"]:
            # 顶级配置
            if key in self.config and self.config[key] == value:
                return  # 值未变化，不需要保存
            self.config[key] = value
        elif key in self.config.get("common", {}):
            # 通用配置
            if key in self.config["common"] and self.config["common"][key] == value:
                return  # 值未变化，不需要保存
            self.config["common"][key] = value
        elif platform in self.config:
            # 平台特定配置
            if key in self.config[platform] and self.config[platform][key] == value:
                return  # 值未变化，不需要保存
            self.config[platform][key] = value
        else:
            # 默认为顶级配置
            if key in self.config and self.config[key] == value:
                return  # 值未变化，不需要保存
            self.config[key] = value

        self.save_config(force=force_save)

    def update(self, config_dict: Dict[str, Any], force_save: bool = False, platform: str = None) -> bool:
        """
        批量更新配置

        Args:
            config_dict: 配置字典
            force_save: 是否强制立即保存
            platform: 平台标识，如果为None则使用当前平台

        Returns:
            是否成功保存配置
        """
        # 如果未指定平台，使用当前平台
        if platform is None:
            platform = self.config.get("current_platform", "netease")

        # 检查是否有值发生变化
        has_changes = False
        changed_keys = []

        for key, value in config_dict.items():
            # 确定配置项应该保存在哪个位置
            if key in ["current_platform", "available_platforms", "platform_names"]:
                # 顶级配置
                if key not in self.config or self.config[key] != value:
                    has_changes = True
                    changed_keys.append(key)
                    self.config[key] = value
            elif key in self.config.get("common", {}):
                # 通用配置
                if key not in self.config["common"] or self.config["common"][key] != value:
                    has_changes = True
                    changed_keys.append(f"common.{key}")
                    self.config["common"][key] = value
            elif platform in self.config:
                # 平台特定配置
                if key not in self.config[platform] or self.config[platform][key] != value:
                    has_changes = True
                    changed_keys.append(f"{platform}.{key}")
                    self.config[platform][key] = value
            else:
                # 默认为顶级配置
                if key not in self.config or self.config[key] != value:
                    has_changes = True
                    changed_keys.append(key)
                    self.config[key] = value

        # 打印调试信息
        if has_changes:
            self.log(f"检测到配置变化，变化的键: {changed_keys}")
        else:
            self.log("未检测到配置变化")

        # 即使没有变化，如果强制保存，也保存配置
        if force_save or has_changes:
            result = self.save_config(force=force_save)
            if result:
                self.log(f"配置已{'强制' if force_save else ''}保存")
            else:
                self.log("配置保存失败")
            return result

        return True  # 没有变化，不需要保存，视为成功

    def set_current_platform(self, platform: str, force_save: bool = False) -> bool:
        """
        设置当前平台

        Args:
            platform: 平台标识
            force_save: 是否强制立即保存

        Returns:
            是否成功设置
        """
        # 检查平台是否有效
        available_platforms = self.config.get("available_platforms", ["netease"])
        if platform not in available_platforms:
            self.log(f"警告: 尝试设置无效的平台 {platform}，可用平台: {available_platforms}")
            return False

        # 设置当前平台
        self.set("current_platform", platform, force_save=force_save)
        self.log(f"已切换到平台: {self.get_platform_name(platform)}")
        return True

    def log(self, message: str) -> None:
        """
        记录日志

        Args:
            message: 日志消息
        """
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def flush_pending_changes(self) -> bool:
        """
        强制保存所有待保存的配置

        Returns:
            是否成功保存配置
        """
        self.log("正在强制保存所有待保存的配置...")

        with self.save_lock:
            # 如果有定时器在运行，取消它
            if self.save_timer:
                self.save_timer.cancel()
                self.save_timer = None
                self.log("已取消待执行的保存定时器")

            # 无论是否有待保存的更改，都强制保存一次
            # 这确保在应用关闭时所有配置都被保存
            self.log("执行最终配置保存...")
            result = self._do_save_config()
            self.changes_pending = False

            if result:
                self.log("✅ 所有配置已成功保存")
            else:
                self.log("❌ 最终配置保存失败")

            return result
