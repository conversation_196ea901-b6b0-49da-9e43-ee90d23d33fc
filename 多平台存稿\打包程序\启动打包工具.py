"""
启动多平台存稿工具打包器 - 支持多种模式
"""

import sys
import os
import subprocess
from pathlib import Path

def main():
    print("=" * 60)
    print("多平台存稿工具 - 打包器启动")
    print("=" * 60)

    # 获取当前目录
    current_dir = Path(__file__).parent

    # 检查可用的打包工具
    gui_tool = current_dir / "自动打包GUI.py"
    auto_tool = current_dir / "自动打包.py"
    old_gui_tool = current_dir / "package_app.py"

    print("请选择打包模式:")
    print("1. GUI界面模式 (推荐)")
    print("2. 命令行模式")
    print("3. 旧版GUI模式")
    print("0. 退出")

    while True:
        try:
            choice = input("\n请输入选择 (0-3): ").strip()

            if choice == "0":
                print("退出程序")
                break
            elif choice == "1":
                if gui_tool.exists():
                    print("启动GUI界面模式...")
                    subprocess.run([sys.executable, str(gui_tool)])
                else:
                    print("错误：GUI工具不存在！")
            elif choice == "2":
                if auto_tool.exists():
                    print("启动命令行模式...")
                    subprocess.run([sys.executable, str(auto_tool)])
                else:
                    print("错误：自动打包工具不存在！")
            elif choice == "3":
                if old_gui_tool.exists():
                    print("启动旧版GUI模式...")
                    subprocess.run([sys.executable, str(old_gui_tool)])
                else:
                    print("错误：旧版GUI工具不存在！")
            else:
                print("无效选择，请重新输入！")
                continue

            break

        except KeyboardInterrupt:
            print("\n\n程序被中断")
            break
        except Exception as e:
            print(f"错误: {e}")
            break

    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
