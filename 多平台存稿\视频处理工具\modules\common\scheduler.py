"""
定时任务模块 - 处理定时任务的创建和执行
"""

import os
import time
import threading
import datetime
from typing import Dict, List, Tuple, Optional, Any, Union, Callable
from queue import Queue, Empty

class Task:
    """定时任务类，表示一个需要在特定时间执行的任务"""
    
    def __init__(self, 
                task_id: str, 
                callback: Callable, 
                args: tuple = (), 
                kwargs: dict = None,
                schedule_time: Optional[datetime.datetime] = None,
                repeat_interval: Optional[int] = None,
                description: str = ""):
        """
        初始化定时任务
        
        Args:
            task_id: 任务唯一标识符
            callback: 要执行的回调函数
            args: 回调函数的位置参数
            kwargs: 回调函数的关键字参数
            schedule_time: 计划执行时间，如果为None则立即执行
            repeat_interval: 重复间隔（秒），如果为None则不重复
            description: 任务描述
        """
        self.task_id = task_id
        self.callback = callback
        self.args = args
        self.kwargs = kwargs or {}
        self.schedule_time = schedule_time
        self.repeat_interval = repeat_interval
        self.description = description
        self.is_cancelled = False
        self.last_run_time = None
        self.next_run_time = schedule_time
    
    def should_run(self, current_time: datetime.datetime) -> bool:
        """
        检查任务是否应该在当前时间运行
        
        Args:
            current_time: 当前时间
            
        Returns:
            bool: 是否应该运行
        """
        if self.is_cancelled:
            return False
            
        if self.schedule_time is None:
            # 没有设置时间，立即执行
            return True
            
        return current_time >= self.schedule_time
    
    def execute(self) -> Any:
        """
        执行任务
        
        Returns:
            Any: 任务执行结果
        """
        try:
            self.last_run_time = datetime.datetime.now()
            result = self.callback(*self.args, **self.kwargs)
            
            # 如果是重复任务，更新下次执行时间
            if self.repeat_interval is not None:
                self.schedule_time = datetime.datetime.now() + datetime.timedelta(seconds=self.repeat_interval)
                self.next_run_time = self.schedule_time
            else:
                # 非重复任务，标记为已取消
                self.is_cancelled = True
                
            return result
        except Exception as e:
            # 记录错误但不中断调度器
            print(f"任务 {self.task_id} 执行出错: {str(e)}")
            return None
    
    def cancel(self) -> None:
        """取消任务"""
        self.is_cancelled = True
    
    def __str__(self) -> str:
        """返回任务的字符串表示"""
        status = "已取消" if self.is_cancelled else "活跃"
        schedule = self.schedule_time.strftime("%Y-%m-%d %H:%M:%S") if self.schedule_time else "立即执行"
        repeat = f"每 {self.repeat_interval} 秒重复" if self.repeat_interval else "不重复"
        return f"任务 {self.task_id}: {self.description} [{status}] - 计划时间: {schedule}, {repeat}"


class Scheduler:
    """调度器类，管理和执行定时任务"""
    
    def __init__(self, logger: Callable = print):
        """
        初始化调度器
        
        Args:
            logger: 日志记录函数
        """
        self.logger = logger
        self.tasks = {}  # 任务字典，键为任务ID
        self.task_queue = Queue()  # 任务队列
        self.running = False  # 调度器是否正在运行
        self.scheduler_thread = None  # 调度器线程
        self.check_interval = 1  # 检查间隔（秒）
    
    def start(self) -> None:
        """启动调度器"""
        if self.running:
            self.logger("调度器已经在运行")
            return
            
        self.running = True
        self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.scheduler_thread.start()
        self.logger("✅ 调度器已启动")
    
    def stop(self) -> None:
        """停止调度器"""
        if not self.running:
            return
            
        self.running = False
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=2)
        self.logger("✅ 调度器已停止")
    
    def _scheduler_loop(self) -> None:
        """调度器主循环"""
        while self.running:
            try:
                current_time = datetime.datetime.now()
                
                # 检查所有任务
                tasks_to_run = []
                for task_id, task in list(self.tasks.items()):
                    if task.should_run(current_time):
                        tasks_to_run.append(task)
                
                # 执行需要运行的任务
                for task in tasks_to_run:
                    self.logger(f"执行任务: {task.description}")
                    task.execute()
                    
                    # 如果任务被取消，从任务字典中移除
                    if task.is_cancelled:
                        self.tasks.pop(task.task_id, None)
                    
                # 检查任务队列中的新任务
                try:
                    while True:
                        task = self.task_queue.get_nowait()
                        self.tasks[task.task_id] = task
                        self.logger(f"添加新任务: {task.description}")
                        self.task_queue.task_done()
                except Empty:
                    pass
                    
                # 等待下一个检查间隔
                time.sleep(self.check_interval)
            except Exception as e:
                self.logger(f"调度器循环出错: {str(e)}")
                time.sleep(self.check_interval)  # 出错后等待一段时间再继续
    
    def schedule_task(self, 
                     task_id: str, 
                     callback: Callable, 
                     args: tuple = (), 
                     kwargs: dict = None,
                     schedule_time: Optional[datetime.datetime] = None,
                     repeat_interval: Optional[int] = None,
                     description: str = "") -> Task:
        """
        调度一个新任务
        
        Args:
            task_id: 任务唯一标识符
            callback: 要执行的回调函数
            args: 回调函数的位置参数
            kwargs: 回调函数的关键字参数
            schedule_time: 计划执行时间，如果为None则立即执行
            repeat_interval: 重复间隔（秒），如果为None则不重复
            description: 任务描述
            
        Returns:
            Task: 创建的任务对象
        """
        # 创建新任务
        task = Task(
            task_id=task_id,
            callback=callback,
            args=args,
            kwargs=kwargs,
            schedule_time=schedule_time,
            repeat_interval=repeat_interval,
            description=description
        )
        
        # 将任务添加到队列
        self.task_queue.put(task)
        
        return task
    
    def cancel_task(self, task_id: str) -> bool:
        """
        取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功取消
        """
        if task_id in self.tasks:
            self.tasks[task_id].cancel()
            return True
        return False
    
    def get_all_tasks(self) -> List[Task]:
        """
        获取所有任务
        
        Returns:
            List[Task]: 任务列表
        """
        return list(self.tasks.values())
    
    def get_task(self, task_id: str) -> Optional[Task]:
        """
        获取指定任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[Task]: 任务对象，如果不存在则返回None
        """
        return self.tasks.get(task_id)
