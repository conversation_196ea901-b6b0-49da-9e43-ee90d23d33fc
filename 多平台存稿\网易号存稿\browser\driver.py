"""
浏览器驱动管理模块 - 负责创建和管理浏览器驱动
"""

import os
import time
import traceback
import threading
import socket
import random
from typing import Optional, Dict, Any, Callable, Set
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import WebDriverException


class PortManager:
    """端口管理器，用于管理Chrome驱动的端口分配，避免多线程冲突"""

    _lock = threading.Lock()
    _used_ports: Set[int] = set()
    _port_range_start = 9515  # Chrome默认端口是9515
    _port_range_end = 9615    # 预留100个端口

    @classmethod
    def get_available_port(cls) -> Optional[int]:
        """
        获取一个可用的端口

        Returns:
            可用端口号，如果没有可用端口则返回None
        """
        with cls._lock:
            # 首先尝试找一个未被使用的端口
            for port in range(cls._port_range_start, cls._port_range_end + 1):
                if port not in cls._used_ports and cls._is_port_available(port):
                    cls._used_ports.add(port)
                    return port

            # 如果没有找到，尝试清理一些可能已经释放的端口
            cls._cleanup_unused_ports()

            # 再次尝试
            for port in range(cls._port_range_start, cls._port_range_end + 1):
                if port not in cls._used_ports and cls._is_port_available(port):
                    cls._used_ports.add(port)
                    return port

            return None

    @classmethod
    def release_port(cls, port: int) -> None:
        """
        释放一个端口

        Args:
            port: 要释放的端口号
        """
        with cls._lock:
            cls._used_ports.discard(port)

    @classmethod
    def _is_port_available(cls, port: int) -> bool:
        """
        检查端口是否可用

        Args:
            port: 端口号

        Returns:
            端口是否可用
        """
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(1)
                result = sock.connect_ex(('localhost', port))
                return result != 0  # 如果连接失败，说明端口可用
        except Exception:
            return False

    @classmethod
    def _cleanup_unused_ports(cls) -> None:
        """清理已经不再使用的端口"""
        ports_to_remove = []
        for port in cls._used_ports:
            if cls._is_port_available(port):
                ports_to_remove.append(port)

        for port in ports_to_remove:
            cls._used_ports.discard(port)


class DriverManager:
    """浏览器驱动管理器类，负责创建和管理浏览器驱动"""

    def __init__(self, log_callback: Callable = None):
        """
        初始化驱动管理器

        Args:
            log_callback: 日志回调函数
        """
        self.log_callback = log_callback
        self.driver: Optional[webdriver.Chrome] = None
        self.assigned_port: Optional[int] = None

    def log(self, message: str) -> None:
        """
        记录日志

        Args:
            message: 日志消息
        """
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def create_driver(self, headless: bool = False, user_data_dir: Optional[str] = None, account: str = "") -> Optional[webdriver.Chrome]:
        """
        创建Chrome浏览器驱动，支持多线程端口管理

        Args:
            headless: 是否使用无头模式
            user_data_dir: 用户数据目录，用于保存登录状态
            account: 账号名称（用于日志显示）

        Returns:
            Chrome浏览器驱动实例，如果创建失败则返回None
        """
        # 获取可用端口（静默）
        port = PortManager.get_available_port()
        if port is None:
            self.log("❌ 无法获取可用端口，所有端口都被占用")
            return None

        self.assigned_port = port
        # 不单独输出端口分配日志，将在创建成功时一起输出

        try:
            # 创建Chrome选项
            chrome_options = Options()

            # 设置无头模式
            if headless:
                chrome_options.add_argument("--headless")
                chrome_options.add_argument("--disable-gpu")

            # 设置窗口大小为全屏
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--start-maximized")

            # 禁用扩展和沙盒
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")

            # 设置用户数据目录
            if user_data_dir:
                chrome_options.add_argument(f"--user-data-dir={user_data_dir}")

            # 设置语言为中文
            chrome_options.add_argument("--lang=zh-CN")

            # 设置编码
            chrome_options.add_argument("--charset=UTF-8")

            # 禁用自动化控制提示
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option("useAutomationExtension", False)

            # 添加多线程相关选项
            chrome_options.add_argument("--disable-background-timer-throttling")
            chrome_options.add_argument("--disable-backgrounding-occluded-windows")
            chrome_options.add_argument("--disable-renderer-backgrounding")

            # 创建Service对象，指定端口
            service = Service(port=port)

            # 创建Chrome驱动
            self.driver = webdriver.Chrome(service=service, options=chrome_options)

            # 设置页面加载超时
            self.driver.set_page_load_timeout(60)

            # 设置脚本执行超时
            self.driver.set_script_timeout(30)

            # 设置窗口大小为全屏
            if not headless:
                self.driver.maximize_window()

            # 合并登录、端口分配和浏览器创建为一条简洁日志
            if account:
                self.log(f"🔐 {account} 正在登录 (端口: {port})")
            else:
                self.log(f"🌐 浏览器已启动 (端口: {port})")
            return self.driver

        except WebDriverException as e:
            self.log(f"❌ 浏览器启动失败: {str(e)}")
            # 释放端口
            if self.assigned_port:
                PortManager.release_port(self.assigned_port)
                self.assigned_port = None
            traceback.print_exc()
            return None

        except Exception as e:
            self.log(f"❌ 浏览器启动异常: {str(e)}")
            # 释放端口
            if self.assigned_port:
                PortManager.release_port(self.assigned_port)
                self.assigned_port = None
            traceback.print_exc()
            return None

    def close_driver(self) -> None:
        """关闭浏览器驱动并释放端口"""
        if self.driver:
            try:
                # 检查浏览器是否仍然活动
                if self.is_driver_alive():
                    # 设置超时，防止quit操作卡死
                    quit_timeout = 5  # 5秒超时

                    def quit_driver():
                        try:
                            self.driver.quit()
                        except Exception as e:
                            self.log(f"关闭浏览器驱动时发生错误: {str(e)}")

                    # 创建线程执行quit操作
                    quit_thread = threading.Thread(target=quit_driver)
                    quit_thread.daemon = True
                    quit_thread.start()

                    # 等待线程完成，最多等待超时时间
                    quit_thread.join(quit_timeout)

                    # 如果线程仍在运行，说明quit操作可能卡住了
                    if quit_thread.is_alive():
                        self.log("关闭浏览器超时，可能浏览器已被手动关闭")
                else:
                    self.log("浏览器已不再活动，可能已被手动关闭")

                self.log("浏览器驱动已关闭")
            except Exception as e:
                self.log(f"关闭浏览器驱动时发生错误: {str(e)}")
            finally:
                self.driver = None

        # 释放分配的端口
        if self.assigned_port:
            PortManager.release_port(self.assigned_port)
            self.log(f"已释放端口: {self.assigned_port}")
            self.assigned_port = None

    def take_screenshot(self, save_path: str) -> bool:
        """
        截取当前页面的屏幕截图

        Args:
            save_path: 截图保存路径

        Returns:
            是否成功截图
        """
        if not self.driver:
            self.log("无法截图：浏览器驱动未初始化")
            return False

        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(save_path), exist_ok=True)

            # 截图
            self.driver.save_screenshot(save_path)
            self.log(f"截图已保存到: {save_path}")
            return True

        except Exception as e:
            self.log(f"截图失败: {str(e)}")
            return False

    def is_driver_alive(self) -> bool:
        """
        检查浏览器驱动是否仍然活动

        Returns:
            驱动是否活动
        """
        if not self.driver:
            return False

        try:
            # 尝试获取当前URL，如果成功则驱动仍然活动
            _ = self.driver.current_url
            return True
        except:
            return False
