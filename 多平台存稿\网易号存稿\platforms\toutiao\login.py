"""
今日头条平台登录模块 - 负责处理今日头条平台的账号登录
"""

import os
import json
import time
import pickle
from typing import Tuple, Dict, Any, Optional

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

class ToutiaoLogin:
    """今日头条平台登录类"""

    # 平台标识符 - 用于模块识别和配置加载
    PLATFORM_ID = "toutiao"
    PLATFORM_NAME = "今日头条平台"

    def __init__(self, log_callback=None):
        """
        初始化今日头条登录类

        Args:
            log_callback: 日志回调函数
        """
        self.log_callback = log_callback

        # 设置平台标识
        self.platform_id = self.PLATFORM_ID
        self.platform_name = self.PLATFORM_NAME

    def log(self, message: str, internal: bool = False) -> None:
        """
        记录日志

        Args:
            message: 日志消息
            internal: 是否为内部日志（内部日志会添加平台标记，但不显示给用户）
        """
        if internal:
            # 内部日志添加平台标记，用于调试和模块识别
            formatted_message = f"[{self.platform_id}] {message}"
        else:
            # 用户日志不显示平台标记
            formatted_message = message

        if self.log_callback:
            self.log_callback(formatted_message)
        else:
            print(formatted_message)

    def init_driver(self, headless: bool = False, account: str = "") -> webdriver.Chrome:
        """
        初始化Chrome浏览器驱动，支持多线程端口管理

        Args:
            headless: 是否使用无头模式
            account: 账号名称（用于日志显示，可选）

        Returns:
            Chrome浏览器驱动实例
        """
        # 导入端口管理器
        try:
            from 网易号存稿.browser.driver import PortManager
        except ImportError:
            # 如果相对导入失败，尝试绝对导入
            import sys
            import os
            sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
            from browser.driver import PortManager

        # 获取可用端口
        port = PortManager.get_available_port()
        if port is None:
            self.log("无法获取可用端口，所有端口都被占用", internal=True)
            raise Exception("无法获取可用端口")

        # 移除单独的端口分配日志，将在驱动创建成功时一起显示

        try:
            # 设置Chrome选项
            chrome_options = Options()

            # 设置无头模式
            if headless:
                chrome_options.add_argument("--headless")

            # 设置窗口大小
            chrome_options.add_argument("--window-size=1920,1080")

            # 禁用GPU加速
            chrome_options.add_argument("--disable-gpu")

            # 禁用扩展
            chrome_options.add_argument("--disable-extensions")

            # 禁用沙盒
            chrome_options.add_argument("--no-sandbox")

            # 禁用开发者工具
            chrome_options.add_argument("--disable-dev-shm-usage")

            # 添加多线程相关选项
            chrome_options.add_argument("--disable-background-timer-throttling")
            chrome_options.add_argument("--disable-backgrounding-occluded-windows")
            chrome_options.add_argument("--disable-renderer-backgrounding")

            # 创建Service对象，指定端口
            service = Service(port=port)

            # 创建Chrome浏览器驱动
            driver = webdriver.Chrome(service=service, options=chrome_options)

            # 设置窗口大小
            driver.set_window_size(1920, 1080)

            # 保存端口信息到驱动对象，用于后续释放
            driver._assigned_port = port

            # 输出合并的日志信息
            if account:
                self.log(f"开始查询账号数据: {account} [toutiao] 浏览器驱动创建成功，使用端口: {port}")
            else:
                self.log(f"浏览器驱动创建成功，使用端口: {port}", internal=True)
            return driver

        except Exception as e:
            # 释放端口
            PortManager.release_port(port)
            self.log(f"创建驱动失败，已释放端口: {port}", internal=True)
            raise e

    def login_with_cookies(self, cookie_path: str, headless: bool = False, account: str = "") -> Tuple[bool, Optional[webdriver.Chrome]]:
        """
        使用Cookie登录今日头条

        Args:
            cookie_path: Cookie文件路径
            headless: 是否使用无头模式
            account: 账号名称（用于日志显示，可选）

        Returns:
            登录结果和浏览器驱动实例
        """
        # 🚀 闪电预检测 - 0.1秒内快速检测Cookie有效性
        lightning_check = self._lightning_cookie_detection(cookie_path, account)
        if not lightning_check[0]:
            self.log(f"❌ 登录失败: {lightning_check[1]}")
            return False, None

        # 预检测通过，继续浏览器验证
        self.log("✅ Cookie预检测通过，启动浏览器验证...")

        # 检查Cookie文件是否存在（双重保险）
        if not os.path.exists(cookie_path):
            self.log(f"Cookie文件不存在: {cookie_path}")
            return False, None

        try:
            # 初始化浏览器驱动
            driver = self.init_driver(headless, account)

            # 打开今日头条创作者平台
            driver.get("https://mp.toutiao.com/profile_v4/index")

            # 等待页面加载
            time.sleep(2)

            # 加载Cookie - 支持多种格式
            cookies = self._load_cookies(cookie_path)
            if not cookies:
                self.log("加载Cookie失败")
                driver.quit()
                return False, None

            # 添加Cookie
            success_count = 0
            for i, cookie in enumerate(cookies):
                try:
                    # 确保Cookie格式正确
                    if not isinstance(cookie, dict):
                        self.log(f"跳过无效Cookie格式: {cookie}")
                        continue

                    if 'name' not in cookie or 'value' not in cookie:
                        self.log(f"跳过缺少name或value的Cookie: {cookie}")
                        continue

                    # 清理Cookie数据
                    clean_cookie = {
                        'name': str(cookie['name']),
                        'value': str(cookie['value']),
                        'domain': cookie.get('domain', '.toutiao.com'),
                        'path': cookie.get('path', '/')
                    }

                    # 处理可选字段
                    if 'expiry' in cookie:
                        try:
                            clean_cookie['expiry'] = int(float(cookie['expiry']))
                        except (ValueError, TypeError):
                            pass  # 忽略无效的expiry值

                    if 'secure' in cookie:
                        clean_cookie['secure'] = bool(cookie['secure'])

                    if 'httpOnly' in cookie:
                        clean_cookie['httpOnly'] = bool(cookie['httpOnly'])

                    # 添加Cookie到浏览器
                    driver.add_cookie(clean_cookie)
                    success_count += 1
                    # 移除详细的cookie添加日志

                except Exception as e:
                    self.log(f"添加Cookie {i+1}/{len(cookies)} 失败: {str(e)}")
                    self.log(f"Cookie内容: {cookie}")

            # 只显示最终结果，不显示详细过程
            self.log("✅ Cookie添加完成")

            # 🚀 快速验证优化：智能等待 + 渐进式检测
            driver.refresh()

            try:
                # 🚀 渐进式等待检测：先快速检测，再逐步增加等待时间
                target_url = "https://mp.toutiao.com/profile_v4/index"

                for wait_time in [0.5, 1.0, 2.0, 3.0]:  # 渐进式等待
                    time.sleep(wait_time)
                    current_url = driver.current_url

                    # 检查是否登录成功
                    if current_url == target_url or "profile_v4/index" in current_url:
                        self.log(f"✅ 头条号快速登录成功 (等待{wait_time}秒)")
                        return True, driver

                    # 🚀 早期失败检测：如果在登录页面停留太久，提前结束
                    if wait_time >= 2.0 and ("login" in current_url.lower() or "auth" in current_url.lower()):
                        self.log("❌ 头条号登录失败：停留在登录页面")
                        driver.quit()
                        return False, None

                # 🚀 最后的快速检测（减少等待时间）
                for i in range(3):  # 最多再等3秒
                    time.sleep(1)
                    current_url = driver.current_url

                    if current_url == target_url or "profile_v4/index" in current_url:
                        self.log(f"✅ 头条号延迟登录成功 (额外等待{i+1}秒)")
                        return True, driver

                # 最终检查
                current_url = driver.current_url
                if current_url == target_url or "profile_v4/index" in current_url:
                    self.log("✅ 头条号最终检测登录成功")
                    return True, driver
                else:
                    self.log(f"❌ 头条号登录失败，最终URL: {current_url}")
                    driver.quit()
                    return False, None

            except Exception as e:
                self.log(f"❌ 头条号快速验证时出错: {str(e)}")
                driver.quit()
                return False, None

        except Exception as e:
            self.log(f"Cookie登录异常: {str(e)}")
            if 'driver' in locals() and driver:
                driver.quit()
            return False, None

    def _load_cookies(self, cookie_path: str):
        """
        加载Cookie文件，支持多种格式

        Args:
            cookie_path: Cookie文件路径

        Returns:
            Cookie列表
        """
        try:
            self.log(f"正在加载Cookie文件: {cookie_path}")

            # 根据文件扩展名确定cookie格式
            if cookie_path.lower().endswith('.txt'):
                # 读取txt格式的cookie文件
                # 移除详细的加载过程日志

                # 尝试不同的编码格式
                encodings = ['utf-8', 'gbk', 'utf-8-sig', 'latin1']
                cookie_content = None

                for encoding in encodings:
                    try:
                        with open(cookie_path, 'r', encoding=encoding) as f:
                            cookie_content = f.read().strip()
                        break
                    except UnicodeDecodeError:
                        continue

                if cookie_content is None:
                    self.log("所有编码格式都无法读取文件")
                    return None

                # 解析cookie字符串
                cookies = self._parse_cookie_string(cookie_content)
                return cookies

            elif cookie_path.lower().endswith('.json'):
                # 读取json格式的cookie文件
                with open(cookie_path, 'r', encoding='utf-8') as f:
                    cookies = json.load(f)
                return cookies

            elif cookie_path.lower().endswith('.pkl'):
                # 读取pickle格式的cookie文件（原有格式）
                with open(cookie_path, "rb") as f:
                    cookies = pickle.load(f)
                return cookies

            else:
                self.log(f"不支持的Cookie文件格式: {cookie_path}")
                return None

        except Exception as e:
            self.log(f"加载Cookie文件失败: {str(e)}")
            return None

    def _parse_cookie_string(self, cookie_str):
        """解析cookie字符串为cookie对象列表"""
        try:
            cookies = []

            # 移除详细的解析过程日志

            # 首先尝试直接解析为JSON
            try:
                parsed_data = json.loads(cookie_str)

                # 检查JSON结构，提取实际的cookies
                if isinstance(parsed_data, dict):
                    # 如果是包含cookies字段的结构
                    if 'cookies' in parsed_data:
                        cookies_data = parsed_data['cookies']

                        if isinstance(cookies_data, dict):
                            # 将字典格式的cookies转换为列表格式
                            cookies = []
                            for name, value in cookies_data.items():
                                cookie = {
                                    'name': name,
                                    'value': str(value),
                                    'domain': '.toutiao.com',
                                    'path': '/'
                                }
                                cookies.append(cookie)
                            return cookies
                        elif isinstance(cookies_data, list):
                            # 如果cookies字段本身就是列表
                            return cookies_data
                    else:
                        # 如果JSON本身就是cookies列表
                        if isinstance(parsed_data, list):
                            self.log(f"JSON本身是cookies列表，共 {len(parsed_data)} 个")
                            return parsed_data
                        else:
                            self.log("JSON格式不符合预期，尝试其他解析方式")
                elif isinstance(parsed_data, list):
                    # 如果JSON直接是cookies列表
                    self.log(f"JSON直接是cookies列表，共 {len(parsed_data)} 个")
                    return parsed_data

            except Exception as e:
                self.log(f"JSON解析失败: {str(e)}，尝试其他解析方式")

            # 尝试解析为简单的name=value格式（用分号分隔）
            if ';' in cookie_str:
                cookie_pairs = cookie_str.split(';')

                for pair in cookie_pairs:
                    pair = pair.strip()
                    if not pair:
                        continue

                    if '=' in pair:
                        name, value = pair.split('=', 1)
                        name = name.strip()
                        value = value.strip()

                        # 跳过一些常见的Cookie属性
                        if name.lower() in ['domain', 'path', 'expires', 'max-age', 'secure', 'httponly', 'samesite']:
                            continue

                        cookie = {
                            'name': name,
                            'value': value,
                            'domain': '.toutiao.com',
                            'path': '/'
                        }
                        cookies.append(cookie)

                if cookies:
                    return cookies

            # 尝试解析为Netscape格式（制表符分隔）
            if '\t' in cookie_str:
                lines = cookie_str.strip().split('\n')
                for line in lines:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        parts = line.split('\t')
                        if len(parts) >= 7:
                            domain, flag, path, secure, expires, name, value = parts
                            cookie = {
                                'domain': domain,
                                'path': path,
                                'name': name,
                                'value': value,
                                'secure': secure.lower() == 'true'
                            }
                            cookies.append(cookie)

                if cookies:
                    return cookies

            # 尝试解析为单行Cookie字符串（浏览器复制的格式）
            self.log("尝试解析为单行Cookie字符串格式")
            # 这种格式通常是: name1=value1; name2=value2; name3=value3
            if '=' in cookie_str:
                # 按分号分割，但要小心处理值中可能包含分号的情况
                parts = []
                current_part = ""
                in_quotes = False

                for char in cookie_str:
                    if char == '"':
                        in_quotes = not in_quotes
                    elif char == ';' and not in_quotes:
                        if current_part.strip():
                            parts.append(current_part.strip())
                        current_part = ""
                        continue
                    current_part += char

                if current_part.strip():
                    parts.append(current_part.strip())

                for part in parts:
                    if '=' in part:
                        name, value = part.split('=', 1)
                        name = name.strip()
                        value = value.strip()

                        # 移除可能的引号
                        if value.startswith('"') and value.endswith('"'):
                            value = value[1:-1]

                        cookie = {
                            'name': name,
                            'value': value,
                            'domain': '.toutiao.com',
                            'path': '/'
                        }
                        cookies.append(cookie)

                if cookies:
                    return cookies

            return []

        except Exception as e:
            self.log(f"解析cookie字符串失败: {str(e)}")
            import traceback
            self.log(f"错误详情: {traceback.format_exc()}")
            return []

    def login_with_phone(self, headless: bool = False) -> Tuple[bool, Optional[webdriver.Chrome], Optional[list]]:
        """
        使用手机号登录今日头条

        Args:
            headless: 是否使用无头模式

        Returns:
            登录结果、浏览器驱动实例和Cookie
        """
        try:
            # 初始化浏览器驱动
            driver = self.init_driver(headless)

            # 打开今日头条登录页面
            driver.get("https://mp.toutiao.com/auth/page/login")

            # 等待页面加载
            time.sleep(2)

            # 等待用户手动登录
            self.log("请在浏览器中手动完成登录操作...")

            # 等待创作者中心元素出现，同时检测浏览器是否被关闭
            try:
                # 使用循环检测，避免长时间阻塞
                max_wait_time = 300  # 最大等待5分钟
                check_interval = 2   # 每2秒检查一次
                elapsed_time = 0

                while elapsed_time < max_wait_time:
                    try:
                        # 检查浏览器是否还活着
                        driver.current_url  # 这会在浏览器关闭时抛出异常

                        # 检查是否已经登录成功
                        try:
                            driver.find_element(By.XPATH, "//span[contains(text(), '创作者中心')]")
                            # 找到了创作者中心元素，登录成功
                            cookies = driver.get_cookies()
                            self.log("手机号登录成功")
                            return True, driver, cookies
                        except:
                            # 还没有找到创作者中心元素，继续等待
                            pass

                        time.sleep(check_interval)
                        elapsed_time += check_interval

                    except Exception as e:
                        # 浏览器可能被用户关闭了
                        self.log("检测到浏览器已关闭，取消登录操作")
                        return False, None, None

                # 超时
                self.log("等待登录超时，请重试")
                driver.quit()
                return False, None, None

            except Exception as e:
                self.log(f"登录检测过程中发生错误: {str(e)}")
                if 'driver' in locals() and driver:
                    try:
                        driver.quit()
                    except:
                        pass
                return False, None, None

        except Exception as e:
            self.log(f"手机号登录异常: {str(e)}")
            if 'driver' in locals() and driver:
                driver.quit()
            return False, None, None

    def _lightning_cookie_detection(self, cookie_path: str, account: str = "") -> Tuple[bool, str]:
        """
        🚀 闪电Cookie检测 - 0.1秒内快速检测Cookie有效性

        Args:
            cookie_path: Cookie文件路径
            account: 账号名称（用于日志）

        Returns:
            (是否有效, 检测结果描述)
        """
        import time
        import json

        start_time = time.time()

        try:
            # 第1层: 文件存在性检查 (0.001秒)
            if not os.path.exists(cookie_path):
                return False, "Cookie文件不存在"

            # 第2层: 文件内容检查 (0.01秒)
            try:
                with open(cookie_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                if not content:
                    return False, "Cookie文件为空"
            except Exception as e:
                return False, f"Cookie文件读取失败: {str(e)}"

            # 第3层: JSON格式检查 (0.01秒)
            try:
                data = json.loads(content)

                # 检查是否是账号格式 {"accountId": "...", "cookies": {...}}
                if isinstance(data, dict) and 'cookies' in data:
                    cookies_dict = data['cookies']
                    if not isinstance(cookies_dict, dict) or len(cookies_dict) == 0:
                        return False, "Cookie数据为空"

                    # 第4层: 关键Cookie检查 (0.03秒) - 字典格式
                    essential_check = self._check_essential_toutiao_cookies_dict(cookies_dict)
                    if not essential_check[0]:
                        return False, essential_check[1]

                    # 第5层: 基本有效性检查 (0.02秒)
                    validity_check = self._check_toutiao_cookies_validity(cookies_dict)
                    if not validity_check[0]:
                        return False, validity_check[1]

                # 检查是否是Cookie数组格式 [{"name": "...", "value": "...", ...}]
                elif isinstance(data, list):
                    if len(data) == 0:
                        return False, "Cookie数组为空"

                    # 第4层: 关键Cookie检查 (0.03秒) - 数组格式
                    essential_check = self._check_essential_toutiao_cookies(data)
                    if not essential_check[0]:
                        return False, essential_check[1]

                    # 第5层: 过期时间检查 (0.02秒)
                    expiry_check = self._check_cookie_expiry_fast(data)
                    if not expiry_check[0]:
                        return False, expiry_check[1]

                    # 第6层: 域名匹配检查 (0.01秒)
                    domain_check = self._check_toutiao_domain_match(data)
                    if not domain_check[0]:
                        return False, domain_check[1]
                else:
                    return False, "Cookie数据格式不支持"

            except json.JSONDecodeError:
                return False, "Cookie文件JSON格式错误"

            elapsed = time.time() - start_time
            return True, f"Cookie预检测通过 (耗时: {elapsed:.3f}秒)"

        except Exception as e:
            elapsed = time.time() - start_time
            return False, f"预检测异常: {str(e)} (耗时: {elapsed:.3f}秒)"

    def _check_essential_toutiao_cookies(self, cookies: list) -> Tuple[bool, str]:
        """检查头条号关键Cookie"""
        try:
            # 头条号关键Cookie名称（至少需要其中一些）
            essential_names = [
                'sessionid', 'sid', 'uid', 'user_id', 'csrf_token',
                'tt_webid', 'ttwid', 'passport_csrf_token',
                'odin_tt', 'install_id', 'ttreq'
            ]

            cookie_names = [cookie.get('name', '').lower() for cookie in cookies if isinstance(cookie, dict)]

            # 检查是否有基本的认证Cookie
            found_essential = []
            for name in essential_names:
                if any(name in cookie_name for cookie_name in cookie_names):
                    found_essential.append(name)

            if len(found_essential) < 2:  # 至少需要2个关键Cookie
                return False, f"缺少关键认证Cookie，仅找到: {found_essential}"

            # 检查Cookie基本格式
            valid_cookies = 0
            for cookie in cookies:
                if isinstance(cookie, dict) and cookie.get('name') and cookie.get('value'):
                    valid_cookies += 1

            if valid_cookies < 3:  # 至少需要3个有效Cookie
                return False, f"有效Cookie数量不足: {valid_cookies}/3"

            return True, f"关键Cookie检查通过，找到 {len(found_essential)} 个关键Cookie"

        except Exception as e:
            return False, f"关键Cookie检查异常: {str(e)}"

    def _check_cookie_expiry_fast(self, cookies: list) -> Tuple[bool, str]:
        """快速检查Cookie过期时间"""
        try:
            current_time = time.time()
            total_cookies = 0
            expired_cookies = 0
            valid_cookies = 0

            for cookie in cookies:
                if not isinstance(cookie, dict):
                    continue

                total_cookies += 1

                if 'expiry' in cookie:
                    try:
                        expiry_time = int(float(cookie['expiry']))
                        if expiry_time < current_time:
                            expired_cookies += 1
                        else:
                            valid_cookies += 1
                    except (ValueError, TypeError):
                        valid_cookies += 1  # 无法解析的当作有效
                else:
                    valid_cookies += 1  # 会话Cookie当作有效

            # 如果所有有过期时间的Cookie都过期了，判定为失效
            if expired_cookies > 0 and valid_cookies == 0:
                return False, f"所有Cookie已过期 ({expired_cookies}个)"

            # 如果过期Cookie太多，也可能有问题
            if total_cookies > 0 and expired_cookies / total_cookies > 0.8:
                return False, f"大部分Cookie已过期 ({expired_cookies}/{total_cookies})"

            return True, f"过期检查通过 (有效: {valid_cookies}, 过期: {expired_cookies})"

        except Exception as e:
            return False, f"过期检查异常: {str(e)}"

    def _check_toutiao_domain_match(self, cookies: list) -> Tuple[bool, str]:
        """检查头条号域名匹配"""
        try:
            # 头条号相关域名
            valid_domains = [
                '.toutiao.com', 'toutiao.com', '.mp.toutiao.com', 'mp.toutiao.com',
                '.bytedance.com', 'bytedance.com', '.snssdk.com', 'snssdk.com'
            ]

            toutiao_cookies = 0
            total_cookies = 0

            for cookie in cookies:
                if not isinstance(cookie, dict):
                    continue

                total_cookies += 1
                domain = cookie.get('domain', '')

                if any(valid_domain in domain for valid_domain in valid_domains):
                    toutiao_cookies += 1

            if total_cookies == 0:
                return False, "没有找到任何Cookie"

            # 至少50%的Cookie应该是头条相关的
            if toutiao_cookies / total_cookies < 0.3:
                return False, f"头条相关Cookie比例过低 ({toutiao_cookies}/{total_cookies})"

            return True, f"域名检查通过 (头条Cookie: {toutiao_cookies}/{total_cookies})"

        except Exception as e:
            return False, f"域名检查异常: {str(e)}"

    def _check_essential_toutiao_cookies_dict(self, cookies_dict: dict) -> Tuple[bool, str]:
        """检查头条号关键Cookie（字典格式）"""
        try:
            # 头条号关键Cookie名称
            essential_names = [
                'sessionid', 'sid', 'uid', 'user_id', 'csrf_token',
                'tt_webid', 'ttwid', 'passport_csrf_token',
                'odin_tt', 'install_id', 'ttreq', 'sessionid_ss',
                'sid_tt', 'uid_tt_ss', 'toutiao_sso_user'
            ]

            cookie_names = [name.lower() for name in cookies_dict.keys()]

            # 检查是否有基本的认证Cookie
            found_essential = []
            for name in essential_names:
                if any(name in cookie_name for cookie_name in cookie_names):
                    found_essential.append(name)

            if len(found_essential) < 2:  # 至少需要2个关键Cookie
                return False, f"缺少关键认证Cookie，仅找到: {found_essential}"

            # 检查Cookie值是否有效
            valid_cookies = 0
            for name, value in cookies_dict.items():
                if name and value and len(str(value)) > 5:  # 基本的值检查
                    valid_cookies += 1

            if valid_cookies < 3:  # 至少需要3个有效Cookie
                return False, f"有效Cookie数量不足: {valid_cookies}/3"

            return True, f"关键Cookie检查通过，找到 {len(found_essential)} 个关键Cookie"

        except Exception as e:
            return False, f"关键Cookie检查异常: {str(e)}"

    def _check_toutiao_cookies_validity(self, cookies_dict: dict) -> Tuple[bool, str]:
        """检查头条号Cookie有效性（字典格式）"""
        try:
            # 检查一些关键Cookie的值格式
            critical_cookies = ['sessionid', 'sessionid_ss', 'sid_tt']

            found_critical = 0
            for cookie_name in critical_cookies:
                if cookie_name in cookies_dict:
                    value = cookies_dict[cookie_name]
                    # 检查sessionid类型的值格式（通常是32位十六进制）
                    if value and len(str(value)) >= 20:
                        found_critical += 1

            if found_critical == 0:
                return False, "未找到有效的会话Cookie"

            # 检查是否有明显的过期标识
            for name, value in cookies_dict.items():
                if 'expired' in str(value).lower() or 'invalid' in str(value).lower():
                    return False, f"发现过期标识: {name}"

            return True, f"Cookie有效性检查通过 (关键会话Cookie: {found_critical}个)"

        except Exception as e:
            return False, f"有效性检查异常: {str(e)}"
