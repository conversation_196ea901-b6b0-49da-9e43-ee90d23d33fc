[2025-07-16 01:48:44] [INFO] 🚀 启动多平台存稿工具...
[2025-07-16 01:48:46] [ERROR] 程序发生未捕获的异常: invalid syntax (login.py, line 310)
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\main.py", line 77, in main
    app = NeteaseDraftUI(root, config_manager)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\ui\main.py", line 188, in __init__
    self.init_platform_processors()
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\ui\main.py", line 1557, in init_platform_processors
    from 网易号存稿.platforms.toutiao import ToutiaoDraftProcessor
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\platforms\__init__.py", line 7, in <module>
    from . import toutiao
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\platforms\toutiao\__init__.py", line 5, in <module>
    from .login import ToutiaoLogin
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\platforms\toutiao\login.py", line 310
    except Exception as e:
    ^^^^^^
SyntaxError: invalid syntax

[2025-07-16 01:48:46] [ERROR] 程序发生未捕获的异常: invalid syntax (login.py, line 310)
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\main.py", line 77, in main
    app = NeteaseDraftUI(root, config_manager)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\ui\main.py", line 188, in __init__
    self.init_platform_processors()
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\ui\main.py", line 1557, in init_platform_processors
    from 网易号存稿.platforms.toutiao import ToutiaoDraftProcessor
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\platforms\__init__.py", line 7, in <module>
    from . import toutiao
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\platforms\toutiao\__init__.py", line 5, in <module>
    from .login import ToutiaoLogin
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\platforms\toutiao\login.py", line 310
    except Exception as e:
    ^^^^^^
SyntaxError: invalid syntax

[2025-07-16 01:48:47] [INFO] 🔍 [调试] 当前平台: toutiao
[2025-07-16 01:48:48] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-07-16 01:48:49] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-07-16 01:48:50] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-16 01:48:51] [INFO] 🔄 发现旧数据文件: C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\account_data.json
[2025-07-16 01:48:52] [INFO] ℹ️ 新数据文件已存在且更新，跳过迁移: C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\account_data.json
[2025-07-16 01:48:53] [INFO] 已从toutiao平台加载存稿详情数据，共 27 个账号
[2025-07-16 01:48:54] [ERROR] 程序发生未捕获的异常: invalid syntax (login.py, line 310)
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\main.py", line 77, in main
    app = NeteaseDraftUI(root, config_manager)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\ui\main.py", line 188, in __init__
    self.init_platform_processors()
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\ui\main.py", line 1557, in init_platform_processors
    from 网易号存稿.platforms.toutiao import ToutiaoDraftProcessor
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\platforms\__init__.py", line 7, in <module>
    from . import toutiao
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\platforms\toutiao\__init__.py", line 5, in <module>
    from .login import ToutiaoLogin
  File "C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\platforms\toutiao\login.py", line 310
    except Exception as e:
    ^^^^^^
SyntaxError: invalid syntax

