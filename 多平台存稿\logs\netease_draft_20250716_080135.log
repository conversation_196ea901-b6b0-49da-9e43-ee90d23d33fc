[2025-07-16 08:01:36] [INFO] 🚀 启动多平台存稿工具...
[2025-07-16 08:01:36] [INFO] 🔍 [调试] 当前平台: netease
[2025-07-16 08:01:36] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/网易号账号
[2025-07-16 08:01:36] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\网易号账号
[2025-07-16 08:01:36] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\网易号账号\netease_account_data.json
[2025-07-16 08:01:36] [INFO] 🔄 发现旧数据文件: C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\account_data.json
[2025-07-16 08:01:36] [INFO] ℹ️ 新数据文件已存在且更新，跳过迁移: C:\Users\<USER>\Downloads\网易\多平台存稿\网易号存稿\account_data.json
[2025-07-16 08:01:36] [INFO] 已从netease平台加载存稿详情数据，共 24 个账号
[2025-07-16 08:01:37] [INFO] [netease] ✅ 视频分配跟踪器初始化成功
[2025-07-16 08:01:37] [INFO] 已确保所有必要目录存在
[2025-07-16 08:01:37] [INFO] 🔍 [调试] 当前平台: netease
[2025-07-16 08:01:37] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/网易号账号
[2025-07-16 08:01:37] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\网易号账号
[2025-07-16 08:01:37] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\网易号账号\netease_account_data.json
[2025-07-16 08:01:37] [INFO] 🔍 [调试] 更新账号树 - 当前平台: netease
[2025-07-16 08:01:37] [INFO] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\网易号账号\netease_account_data.json
[2025-07-16 08:01:37] [INFO] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-07-16 08:01:37] [INFO] 已加载账号数据: 25 条记录
[2025-07-16 08:01:37] [INFO] 🔍 [调试] 账号数据为空或未加载
[2025-07-16 08:01:37] [INFO] ✅ 初始UI更新完成
[2025-07-16 08:01:39] [INFO] 平台切换前保存 netease 的设置和存稿详情数据
[2025-07-16 08:01:39] [INFO] ✅ 设置已静默保存
[2025-07-16 08:01:39] [INFO] 已确保所有必要目录存在
[2025-07-16 08:01:39] [INFO] 正在更新账号管理器，当前平台: toutiao
[2025-07-16 08:01:39] [INFO] 账号目录: D:/网易号全自动/头条号账号
[2025-07-16 08:01:39] [INFO] 已加载 70 个toutiao平台账号
[2025-07-16 08:01:39] [INFO] 🔍 [调试] 当前平台: toutiao
[2025-07-16 08:01:39] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-07-16 08:01:39] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-07-16 08:01:39] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-16 08:01:39] [INFO] 已加载账号数据: 86 条记录
[2025-07-16 08:01:39] [INFO] 账号数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-16 08:01:39] [INFO] 已更新数据查询管理器的AccountData引用
[2025-07-16 08:01:39] [INFO] 已更新Excel导出管理器的AccountData引用
[2025-07-16 08:01:39] [INFO] 已从toutiao平台加载存稿详情数据，共 27 个账号
[2025-07-16 08:01:39] [INFO] 已切换到平台: 今日头条平台
[2025-07-16 08:01:39] [INFO] 🔍 [调试] 当前平台: toutiao
[2025-07-16 08:01:39] [INFO] 🔍 [调试] 配置的账号目录: D:/网易号全自动/头条号账号
[2025-07-16 08:01:39] [INFO] 🔍 [调试] 标准化后的目录: D:\网易号全自动\头条号账号
[2025-07-16 08:01:39] [INFO] ✅ 使用数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-16 08:01:39] [INFO] 🔍 [调试] 更新账号树 - 当前平台: toutiao
[2025-07-16 08:01:39] [INFO] 🔍 [调试] 更新账号树 - 数据文件路径: D:\网易号全自动\头条号账号\toutiao_account_data.json
[2025-07-16 08:01:39] [INFO] 🔍 [调试] 更新账号树 - 文件是否存在: True
[2025-07-16 08:01:39] [INFO] 已加载账号数据: 86 条记录
[2025-07-16 08:01:39] [INFO] 🔍 [调试] 账号数据为空或未加载
[2025-07-16 08:01:39] [INFO] ✅ 设置已静默保存
[2025-07-16 08:01:44] [INFO] 开始查询头条号账号: ***********
[2025-07-16 08:01:44] [INFO] 🔍 已启动账号 *********** 的数据查询
[2025-07-16 08:01:44] [INFO] [头条查询管理器] 🚀 开始并发查询 1 个账号，最大并发数: 1
[2025-07-16 08:01:44] [INFO] ✅ Cookie预检测通过，启动浏览器验证...
[2025-07-16 08:01:47] [INFO] 开始查询账号数据: *********** [toutiao] 浏览器驱动创建成功，使用端口: 9515
[2025-07-16 08:01:51] [INFO] 正在登录头条账号: ***********
[2025-07-16 08:01:51] [INFO] 找到头条账号 *********** 的Cookie文件: D:/网易号全自动/头条号账号\***********.txt
[2025-07-16 08:01:51] [INFO] 正在使用Cookie登录头条账号: ***********
[2025-07-16 08:01:51] [INFO] Cookie文件路径: D:/网易号全自动/头条号账号\***********.txt
[2025-07-16 08:01:51] [INFO] ✅ Cookie预检测通过，启动浏览器验证...
[2025-07-16 08:01:51] [INFO] 正在加载Cookie文件: D:/网易号全自动/头条号账号\***********.txt
[2025-07-16 08:01:52] [INFO] ✅ Cookie添加完成
[2025-07-16 08:01:53] [INFO] ✅ 头条号快速登录成功 (等待0.5秒)
[2025-07-16 08:01:53] [INFO] 正在获取首页数据...
[2025-07-16 08:01:54] [INFO] 正在等待首页加载...
[2025-07-16 08:01:54] [INFO] [toutiao] 浏览器驱动创建成功，使用端口: 9516
[2025-07-16 08:01:57] [INFO] 正在加载Cookie文件: D:/网易号全自动/头条号账号\***********.txt
[2025-07-16 08:01:59] [INFO] 首页主要内容已加载
[2025-07-16 08:01:59] [INFO] 用户名: 好学无聊七仔的生活
[2025-07-16 08:01:59] [INFO] 累计收益: 41.51元 -> 41.51
