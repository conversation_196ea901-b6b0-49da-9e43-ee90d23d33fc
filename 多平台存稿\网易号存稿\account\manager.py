"""
账号管理器模块 - 负责管理多平台账号
"""

import os
import json
import shutil
import re
import time
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime

class AccountManager:
    """账号管理器类，负责管理多平台账号"""

    def __init__(self, account_dir: str, log_callback: Callable = None, platform: str = "netease"):
        """
        初始化账号管理器

        Args:
            account_dir: 账号目录
            log_callback: 日志回调函数
            platform: 平台标识，默认为网易号平台
        """
        self.account_dir = account_dir
        self.log_callback = log_callback
        self.platform = platform
        self.accounts = []
        self.selected_account = ""

        # 确保账号目录存在
        os.makedirs(self.account_dir, exist_ok=True)

        # 初始化时不自动加载，由调用方决定何时加载
        # self.load_accounts()  # 移除自动加载

    def set_platform(self, platform: str) -> None:
        """
        设置当前平台

        Args:
            platform: 平台标识
        """
        self.platform = platform
        # 移除重复的日志输出，由主UI统一记录平台切换日志
        # self.log(f"账号管理器已切换到平台: {platform}")

        # 平台切换时不自动重新加载，由调用方决定
        # self.load_accounts()  # 移除自动加载

    def set_account_dir(self, account_dir: str) -> None:
        """
        设置账号目录

        Args:
            account_dir: 账号目录
        """
        self.account_dir = account_dir

        # 确保账号目录存在
        os.makedirs(self.account_dir, exist_ok=True)

        # 目录更新时不自动重新加载，由调用方决定
        # self.load_accounts()  # 移除自动加载

    def log(self, message: str) -> None:
        """
        记录日志

        Args:
            message: 日志消息
        """
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def load_accounts(self) -> List[str]:
        """
        加载保存的账号

        Returns:
            账号列表
        """
        try:
            self.accounts = []

            if not os.path.exists(self.account_dir):
                os.makedirs(self.account_dir)

            # 扫描顶级账号文件（支持直接文件格式，如 账号名.txt, 账号名.json）
            # 排除系统文件和数据文件
            excluded_files = {
                'accounts', 'account_data', 'toutiao_account_data', 'netease_account_data',
                'dayu_account_data', 'data', 'backup', 'config', 'settings'
            }

            for file in os.listdir(self.account_dir):
                # 只扫描.txt格式的账号文件
                if file.endswith(".txt"):
                    # 排除状态文件
                    if file.endswith("_status.txt"):
                        continue

                    account = os.path.splitext(file)[0]
                    # 排除系统文件
                    if account.lower() not in excluded_files and account not in self.accounts:
                        self.accounts.append(account)

            # 不再扫描子目录格式，只识别账号名.txt格式的账号文件

            # 移除重复的日志输出，由调用方统一记录
            # self.log(f"已加载 {len(self.accounts)} 个账号")
            return self.accounts

        except Exception as e:
            self.log(f"加载账号失败: {str(e)}")
            return []

    def add_account(self, account_name: str, cookies: Dict[str, Any] = None, cookie_file: str = None) -> bool:
        """
        添加账号

        Args:
            account_name: 账号名称
            cookies: Cookie数据
            cookie_file: Cookie文件路径

        Returns:
            是否成功添加
        """
        try:
            # 验证账号名称
            if not account_name:
                self.log("账号名称不能为空")
                return False

            # 保存Cookie（统一使用TXT格式，所有平台保持一致）
            # 注意：以后添加账号创建的格式就是这种TXT格式
            if cookies:
                # 直接保存为TXT格式（内容为JSON格式）
                cookie_path = os.path.join(self.account_dir, f"{account_name}.txt")
                with open(cookie_path, 'w', encoding='utf-8') as f:
                    json.dump(cookies, f, ensure_ascii=False, indent=4)
                self.log(f"已创建TXT格式账号文件: {account_name}.txt")
            elif cookie_file:
                # 复制Cookie文件为TXT格式
                dest_path = os.path.join(self.account_dir, f"{account_name}.txt")
                shutil.copy2(cookie_file, dest_path)
                self.log(f"已复制为TXT格式账号文件: {account_name}.txt")
            else:
                # 创建空的Cookie文件（TXT格式）
                cookie_path = os.path.join(self.account_dir, f"{account_name}.txt")
                with open(cookie_path, 'w', encoding='utf-8') as f:
                    json.dump([], f, ensure_ascii=False, indent=4)
                self.log(f"已创建空的TXT格式账号文件: {account_name}.txt")

            # 更新账号列表
            if account_name not in self.accounts:
                self.accounts.append(account_name)
                self.log(f"已添加账号: {account_name}")

            return True

        except Exception as e:
            self.log(f"添加账号失败: {str(e)}")
            return False

    def delete_account(self, account_name: str) -> bool:
        """
        删除账号

        Args:
            account_name: 账号名称

        Returns:
            是否成功删除
        """
        try:
            if not account_name:
                self.log("账号名称不能为空")
                return False

            # 检查账号是否存在
            if account_name not in self.accounts:
                self.log(f"账号不存在: {account_name}")
                return False

            # 删除账号目录
            account_dir = os.path.join(self.account_dir, account_name)
            if os.path.exists(account_dir) and os.path.isdir(account_dir):
                shutil.rmtree(account_dir)

            # 删除账号文件
            account_file = os.path.join(self.account_dir, f"{account_name}.txt")
            if os.path.exists(account_file):
                os.remove(account_file)

            # 更新账号列表
            self.accounts.remove(account_name)
            self.log(f"已删除账号: {account_name}")

            # 如果删除的是当前选中的账号，清除选中状态
            if account_name == self.selected_account:
                self.selected_account = ""

            return True

        except Exception as e:
            self.log(f"删除账号失败: {str(e)}")
            return False

    def select_account(self, account_name: str) -> bool:
        """
        选择账号

        Args:
            account_name: 账号名称

        Returns:
            是否成功选择
        """
        try:
            if not account_name:
                self.selected_account = ""
                self.log("已清除选中账号")
                return True

            # 检查账号是否存在
            if account_name not in self.accounts:
                self.log(f"账号不存在: {account_name}")
                return False

            # 设置选中账号
            self.selected_account = account_name
            self.log(f"已选择账号: {account_name}")
            return True

        except Exception as e:
            self.log(f"选择账号失败: {str(e)}")
            return False

    def get_account_cookie_path(self, account_name: str) -> Optional[str]:
        """
        获取账号Cookie文件路径，只支持账号名.txt格式

        Args:
            account_name: 账号名称

        Returns:
            Cookie文件路径，如果不存在则返回None
        """
        try:
            if not account_name:
                return None

            # 只检查账号名.txt格式
            account_file = os.path.join(self.account_dir, f"{account_name}.txt")
            if os.path.exists(account_file):
                return account_file

            return None

        except Exception as e:
            self.log(f"获取账号Cookie路径失败: {str(e)}")
            return None

    def get_account_cookies(self, account_name: str) -> Optional[list]:
        """
        获取账号的Cookie数据

        注意：兼容新的合并格式和旧的独立格式

        Args:
            account_name: 账号名称

        Returns:
            Cookie数据列表，如果不存在则返回None
        """
        try:
            account_file = self.get_account_cookie_path(account_name)
            if not account_file or not os.path.exists(account_file):
                return None

            with open(account_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()

            if not content:
                return []

            try:
                data = json.loads(content)

                # 新格式：包含cookies字段的字典
                if isinstance(data, dict):
                    if "cookies" in data:
                        cookies = data["cookies"]
                        # 如果cookies是字符串，尝试解析
                        if isinstance(cookies, str):
                            try:
                                return json.loads(cookies)
                            except:
                                return []
                        # 如果cookies是列表，直接返回
                        elif isinstance(cookies, list):
                            return cookies
                    # 如果是旧格式的字典但没有cookies字段，可能整个字典就是cookie数据
                    else:
                        return [data]

                # 旧格式：直接是Cookie数组
                elif isinstance(data, list):
                    return data

                return []

            except json.JSONDecodeError:
                # 不是JSON格式，可能是原始文本
                return []

        except Exception as e:
            self.log(f"获取账号Cookie失败: {str(e)}")
            return None

    def get_account_data(self, account_name: str) -> Optional[Dict[str, Any]]:
        """
        获取账号数据

        Args:
            account_name: 账号名称

        Returns:
            账号数据，如果不存在则返回None
        """
        try:
            if not account_name:
                return None

            # 检查账号数据文件
            account_dir = os.path.join(self.account_dir, account_name)
            data_path = os.path.join(account_dir, "account_data.json")
            if os.path.exists(data_path):
                with open(data_path, 'r', encoding='utf-8') as f:
                    return json.load(f)

            return {
                "账号": account_name,
                "总收益": "0",
                "昨日收益": "0",
                "总播放": "0",
                "昨日播放": "0",
                "总粉丝": "0",
                "昨日粉丝": "0",
                "状态": "未查询",
                "更新时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

        except Exception as e:
            self.log(f"获取账号数据失败: {str(e)}")
            return None

    def save_account_data(self, account_name: str, data: Dict[str, Any]) -> bool:
        """
        保存账号数据

        Args:
            account_name: 账号名称
            data: 账号数据

        Returns:
            是否成功保存
        """
        try:
            if not account_name:
                return False

            # 确保账号目录存在
            account_dir = os.path.join(self.account_dir, account_name)
            os.makedirs(account_dir, exist_ok=True)

            # 保存账号数据
            data_path = os.path.join(account_dir, "account_data.json")
            with open(data_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=4)

            return True

        except Exception as e:
            self.log(f"保存账号数据失败: {str(e)}")
            return False

    def get_all_accounts_data(self) -> List[Dict[str, Any]]:
        """
        获取所有账号数据

        Returns:
            所有账号数据列表
        """
        result = []
        for account in self.accounts:
            data = self.get_account_data(account)
            if data:
                result.append(data)
        return result

    def find_duplicate_accounts(self) -> Dict[str, List[str]]:
        """
        查找重复的账号

        Returns:
            重复账号字典，格式为 {基础账号名: [重复账号列表]}
        """
        duplicates = {}
        account_groups = {}

        # 按基础名称分组账号
        for account in self.accounts:
            # 提取基础账号名（去除数字后缀等）
            base_name = self._extract_base_account_name(account)
            if base_name not in account_groups:
                account_groups[base_name] = []
            account_groups[base_name].append(account)

        # 找出有多个账号的组
        for base_name, accounts in account_groups.items():
            if len(accounts) > 1:
                duplicates[base_name] = accounts

        return duplicates

    def _extract_base_account_name(self, account_name: str) -> str:
        """
        提取账号的基础名称

        Args:
            account_name: 完整账号名

        Returns:
            基础账号名
        """
        import re

        # 移除常见的后缀模式
        patterns = [
            r'\s*\(\d+\)$',  # 移除 (1), (2) 等
            r'\s*-\d+$',     # 移除 -1, -2 等
            r'\s*_\d+$',     # 移除 _1, _2 等
            r'\s*\d+$',      # 移除末尾数字
        ]

        base_name = account_name
        for pattern in patterns:
            base_name = re.sub(pattern, '', base_name)

        return base_name.strip()

    def check_account_cookie_status(self, account: str) -> Dict[str, Any]:
        """
        检查账号的cookie状态

        Args:
            account: 账号名称

        Returns:
            cookie状态信息
        """
        try:
            # 只检查账号名.txt格式
            cookie_file = os.path.join(self.account_dir, f"{account}.txt")
            if not os.path.exists(cookie_file):
                cookie_file = None

            if not cookie_file:
                return {"valid": False, "reason": "cookie文件不存在"}

            # 检查cookie内容
            try:
                with open(cookie_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()

                if not content:
                    return {"valid": False, "reason": "cookie文件为空"}

                # 尝试解析cookie
                if cookie_file.endswith('.json'):
                    import json
                    cookies = json.loads(content)
                    if not cookies or len(cookies) == 0:
                        return {"valid": False, "reason": "cookie数据为空"}

                    # 检查cookie是否过期
                    import time
                    current_time = time.time()
                    expired_count = 0
                    valid_count = 0

                    for cookie in cookies:
                        if isinstance(cookie, dict) and 'expiry' in cookie:
                            try:
                                expiry_time = int(float(cookie['expiry']))
                                if expiry_time < current_time:
                                    expired_count += 1
                                else:
                                    valid_count += 1
                            except:
                                pass
                        else:
                            valid_count += 1

                    if expired_count > 0 and valid_count == 0:
                        return {"valid": False, "reason": f"所有cookie已过期 ({expired_count}个)"}
                    elif expired_count > 0:
                        return {"valid": True, "reason": f"部分cookie过期 ({expired_count}/{expired_count + valid_count})"}
                    else:
                        return {"valid": True, "reason": f"cookie有效 ({valid_count}个)"}
                else:
                    return {"valid": True, "reason": "cookie文件存在"}

            except Exception as e:
                return {"valid": False, "reason": f"读取cookie失败: {str(e)}"}

        except Exception as e:
            return {"valid": False, "reason": f"检查失败: {str(e)}"}

    def clean_duplicate_accounts(self, keep_strategy: str = "newest") -> Dict[str, Any]:
        """
        清理重复账号

        Args:
            keep_strategy: 保留策略 ("newest", "oldest", "valid_cookie", "manual")

        Returns:
            清理结果
        """
        try:
            duplicates = self.find_duplicate_accounts()
            if not duplicates:
                return {"success": True, "message": "没有发现重复账号", "cleaned": 0}

            cleaned_count = 0
            results = []

            for base_name, accounts in duplicates.items():
                self.log(f"处理重复账号组: {base_name} ({len(accounts)}个)")

                # 根据策略选择要保留的账号
                keep_account = self._select_account_to_keep(accounts, keep_strategy)
                accounts_to_remove = [acc for acc in accounts if acc != keep_account]

                self.log(f"保留账号: {keep_account}")
                self.log(f"删除账号: {', '.join(accounts_to_remove)}")

                # 删除重复账号
                for account in accounts_to_remove:
                    if self.delete_account(account):
                        cleaned_count += 1
                        results.append(f"已删除: {account}")
                    else:
                        results.append(f"删除失败: {account}")

            # 重新加载账号列表
            self.load_accounts()

            return {
                "success": True,
                "message": f"清理完成，删除了 {cleaned_count} 个重复账号",
                "cleaned": cleaned_count,
                "details": results
            }

        except Exception as e:
            self.log(f"清理重复账号失败: {str(e)}")
            return {"success": False, "message": f"清理失败: {str(e)}", "cleaned": 0}

    def _select_account_to_keep(self, accounts: List[str], strategy: str) -> str:
        """
        根据策略选择要保留的账号

        Args:
            accounts: 账号列表
            strategy: 保留策略

        Returns:
            要保留的账号名
        """
        if strategy == "newest":
            # 保留最新的（按目录修改时间）
            newest_account = accounts[0]
            newest_time = 0

            for account in accounts:
                account_dir = os.path.join(self.account_dir, account)
                if os.path.exists(account_dir):
                    mtime = os.path.getmtime(account_dir)
                    if mtime > newest_time:
                        newest_time = mtime
                        newest_account = account

            return newest_account

        elif strategy == "oldest":
            # 保留最旧的
            oldest_account = accounts[0]
            oldest_time = float('inf')

            for account in accounts:
                account_dir = os.path.join(self.account_dir, account)
                if os.path.exists(account_dir):
                    mtime = os.path.getmtime(account_dir)
                    if mtime < oldest_time:
                        oldest_time = mtime
                        oldest_account = account

            return oldest_account

        elif strategy == "valid_cookie":
            # 保留cookie有效的账号，并记录诊断结果
            valid_accounts = []
            invalid_accounts = []

            for account in accounts:
                status = self.check_account_cookie_status(account)
                if status.get("valid", False):
                    valid_accounts.append(account)
                    # 更新账号状态为Cookie有效
                    self.update_account_status(account, "Cookie有效", status.get("reason", ""))
                else:
                    invalid_accounts.append(account)
                    # 更新账号状态为Cookie失效
                    self.update_account_status(account, "Cookie失效", status.get("reason", ""))
                    self.log(f"❌ 账号 {account} Cookie失效: {status.get('reason', '未知原因')}")

            # 优先返回有效的账号
            if valid_accounts:
                return valid_accounts[0]

            # 如果都无效，返回第一个
            return accounts[0]

        else:
            # 默认返回第一个
            return accounts[0]

    def update_account_status(self, account: str, status: str, reason: str = "") -> bool:
        """
        更新账号状态信息

        注意：状态信息直接合并到账号TXT文件中，不再使用单独的status.json文件

        Args:
            account: 账号名
            status: 状态描述
            reason: 状态原因

        Returns:
            是否更新成功
        """
        try:
            # 查找账号文件
            account_file = self.get_account_cookie_path(account)
            if not account_file or not os.path.exists(account_file):
                self.log(f"账号文件不存在: {account}")
                return False

            # 读取现有账号数据
            try:
                with open(account_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()

                if content:
                    # 尝试解析为JSON
                    try:
                        account_data = json.loads(content)
                    except json.JSONDecodeError:
                        # 如果不是JSON格式，创建新的数据结构
                        account_data = {
                            "cookies": content,  # 保存原始内容
                            "format": "raw"
                        }
                else:
                    account_data = {"cookies": []}
            except Exception as e:
                self.log(f"读取账号文件失败: {str(e)}")
                return False

            # 确保account_data是字典格式
            if isinstance(account_data, list):
                # 如果是Cookie数组，转换为新格式
                account_data = {
                    "cookies": account_data,
                    "format": "cookies_array"
                }
            elif not isinstance(account_data, dict):
                # 其他格式，保存为原始内容
                account_data = {
                    "cookies": str(account_data),
                    "format": "raw"
                }

            # 更新状态信息
            account_data["status"] = {
                "status": status,
                "reason": reason,
                "update_time": time.time(),
                "update_date": time.strftime("%Y-%m-%d %H:%M:%S")
            }

            # 保存更新后的账号数据
            with open(account_file, 'w', encoding='utf-8') as f:
                json.dump(account_data, f, ensure_ascii=False, indent=2)

            return True

        except Exception as e:
            self.log(f"更新账号状态失败: {str(e)}")
            return False

    def get_account_status(self, account: str) -> dict:
        """
        获取账号状态信息

        注意：优先从账号TXT文件中读取状态信息，兼容旧的独立status.json文件

        Args:
            account: 账号名

        Returns:
            状态信息字典
        """
        try:
            # 优先从账号TXT文件中读取状态信息
            account_file = self.get_account_cookie_path(account)
            if account_file and os.path.exists(account_file):
                try:
                    with open(account_file, 'r', encoding='utf-8') as f:
                        content = f.read().strip()

                    if content:
                        try:
                            account_data = json.loads(content)
                            # 如果是新格式（包含status字段）
                            if isinstance(account_data, dict) and "status" in account_data:
                                return account_data["status"]
                        except json.JSONDecodeError:
                            pass  # 不是JSON格式，继续检查其他方式
                except Exception:
                    pass

            # 兼容性：检查旧的独立状态文件
            # 1. 子目录格式
            account_dir = os.path.join(self.account_dir, account)
            status_file = os.path.join(account_dir, "status.json")
            if os.path.exists(status_file):
                with open(status_file, 'r', encoding='utf-8') as f:
                    return json.load(f)

            # 2. 直接文件格式
            direct_status_file = os.path.join(self.account_dir, f"{account}_status.json")
            if os.path.exists(direct_status_file):
                with open(direct_status_file, 'r', encoding='utf-8') as f:
                    return json.load(f)

            # 3. 旧的data子目录格式
            data_status_file = os.path.join(self.account_dir, "data", account, "status.json")
            if os.path.exists(data_status_file):
                with open(data_status_file, 'r', encoding='utf-8') as f:
                    return json.load(f)

            # 如果没有状态信息，返回默认状态
            return {"status": "未查询", "reason": "", "update_time": 0}

        except Exception as e:
            return {"status": "读取失败", "reason": str(e), "update_time": 0}
