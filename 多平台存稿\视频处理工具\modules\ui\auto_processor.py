"""
自动处理模块 - 用于自动模式下的视频处理
"""

import os
import time
import threading
from typing import Callable, Dict, Any, List

class AutoProcessor:
    """自动处理类，用于自动模式下的视频处理"""

    def __init__(self, ui, process_callback: Callable):
        """
        初始化自动处理器

        Args:
            ui: UI实例
            process_callback: 处理回调函数
        """
        self.ui = ui
        self.process_callback = process_callback
        self.is_processing = False
        self.processed_files = []
        self.failed_files = []
        self.progress_callback = None

    def set_progress_callback(self, callback):
        """设置进度回调函数"""
        self.progress_callback = callback

    def start_processing(self):
        """开始自动处理"""
        if self.is_processing:
            return

        self.is_processing = True

        # 创建处理线程
        threading.Thread(target=self._process_thread, daemon=True).start()

    def _process_thread(self):
        """处理线程"""
        try:
            # 使用默认设置
            self._load_default_settings()

            # 获取视频文件列表
            video_files = self._get_video_files()

            if not video_files:
                print("没有找到视频文件")
                self.is_processing = False
                return

            print(f"找到 {len(video_files)} 个视频文件")

            # 处理视频文件
            for i, video_file in enumerate(video_files):
                if not self.is_processing:
                    break

                # 计算进度百分比
                progress_percent = int((i / len(video_files)) * 100)

                # 更新进度
                print(f"处理视频 {i+1}/{len(video_files)}: {video_file} - 进度: {progress_percent}%")

                # 调用进度回调函数
                if self.progress_callback:
                    self.progress_callback(progress_percent, f"处理视频 {i+1}/{len(video_files)}")

                # 调用处理回调函数
                result = self.process_callback(
                    video_file=video_file,
                    video_dir=self.ui.video_dir.get(),
                    processed_videos_dir=self.ui.processed_videos_dir.get(),
                    processed_covers_dir=self.ui.processed_covers_dir.get(),
                    min_duration=self.ui.video_min_duration.get(),
                    max_duration=self.ui.video_max_duration.get(),
                    target_ratio=self.ui.video_ratio.get(),
                    enable_deduplication=self.ui.enable_deduplication.get(),
                    cover_settings={
                        "enable_text": True,  # 默认启用封面文字
                        "text": self.ui.cover_text_top.get(),  # 使用上方文字
                        "font_size": self.ui.cover_size.get(),  # 使用共用的文字大小
                        "font_color": self.ui.cover_color_top.get(),  # 使用上方文字颜色
                        "position": "top"  # 默认位置为顶部
                    },
                    watermark_settings={
                        "enable": self.ui.enable_watermark.get(),
                        "opacity": self.ui.watermark_opacity.get(),
                        "position": self.ui.watermark_position.get(),
                        "color": self.ui.watermark_color.get(),
                        "quantity": self.ui.watermark_quantity.get()
                    },
                    output_format=self.ui.output_format.get() if self.ui.enable_format_conversion.get() else None,
                    cover_resolution=self._get_cover_resolution() if self.ui.enable_custom_cover_resolution.get() else None,
                    gpu_settings={
                        "use_gpu": self.ui.enable_gpu_acceleration.get(),
                        "gpu_device": self.ui.gpu_device.get(),
                    },
                    resource_settings={
                        "memory_limit": self.ui.memory_limit.get(),
                        "thread_count": self.ui.thread_num.get()
                    }
                )

                # 处理结果
                if result.get('success', False):
                    self.processed_files.append(video_file)
                    print(f"✅ 视频处理成功: {video_file}")
                else:
                    self.failed_files.append({
                        'file': video_file,
                        'message': result.get('message', '未知错误')
                    })
                    print(f"❌ 视频处理失败: {video_file} - {result.get('message', '未知错误')}")

                # 短暂暂停，避免CPU占用过高
                time.sleep(0.1)

            # 处理完成
            print("\n===== 处理完成 =====")
            print(f"成功处理: {len(self.processed_files)} 个文件")
            print(f"处理失败: {len(self.failed_files)} 个文件")

            # 调用进度回调函数，通知处理完成
            if self.progress_callback:
                self.progress_callback(100, f"处理完成: 成功 {len(self.processed_files)} 个, 失败 {len(self.failed_files)} 个")

            if self.failed_files:
                print("\n失败文件列表:")
                for fail in self.failed_files:
                    print(f"- {fail['file']}: {fail['message']}")

        except Exception as e:
            print(f"自动处理过程中出错: {str(e)}")
        finally:
            self.is_processing = False

    def _load_default_settings(self):
        """加载默认设置"""
        print("=== 自动模式：检查目录设置 ===")

        # 显示当前配置的目录
        current_video_dir = self.ui.video_dir.get()
        current_processed_dir = self.ui.processed_videos_dir.get()
        current_covers_dir = self.ui.processed_covers_dir.get()

        print(f"配置文件中的视频目录: {current_video_dir}")
        print(f"配置文件中的处理后视频目录: {current_processed_dir}")
        print(f"配置文件中的处理后封面目录: {current_covers_dir}")

        # 只有在配置文件中没有设置目录时才使用默认值
        if not current_video_dir:
            print("⚠️ 配置文件中没有视频目录，使用默认设置")
            default_video_dir = os.path.join(os.path.expanduser("~"), "Videos")
            if os.path.exists(default_video_dir):
                self.ui.video_dir.set(default_video_dir)
                print(f"设置默认视频目录: {default_video_dir}")
            else:
                fallback_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "视频")
                self.ui.video_dir.set(fallback_dir)
                print(f"设置备用视频目录: {fallback_dir}")
        else:
            print(f"✅ 使用配置文件中的视频目录: {current_video_dir}")

        # 只有在配置文件中没有设置输出目录时才使用默认值
        if not current_processed_dir:
            print("⚠️ 配置文件中没有处理后视频目录，使用默认设置")
            default_processed_dir = os.path.join(os.path.dirname(self.ui.video_dir.get()), "已处理")
            self.ui.processed_videos_dir.set(default_processed_dir)
            print(f"设置默认处理后视频目录: {default_processed_dir}")
        else:
            print(f"✅ 使用配置文件中的处理后视频目录: {current_processed_dir}")

        # 只有在配置文件中没有设置封面目录时才使用默认值
        if not current_covers_dir:
            print("⚠️ 配置文件中没有处理后封面目录，使用默认设置")
            default_covers_dir = os.path.join(self.ui.processed_videos_dir.get(), "封面")
            self.ui.processed_covers_dir.set(default_covers_dir)
            print(f"设置默认处理后封面目录: {default_covers_dir}")
        else:
            print(f"✅ 使用配置文件中的处理后封面目录: {current_covers_dir}")

        # 确保目录存在
        final_video_dir = self.ui.video_dir.get()
        final_processed_dir = self.ui.processed_videos_dir.get()
        final_covers_dir = self.ui.processed_covers_dir.get()

        print(f"\n=== 创建必要的目录 ===")
        try:
            os.makedirs(final_video_dir, exist_ok=True)
            print(f"✅ 视频目录已确保存在: {final_video_dir}")
        except Exception as e:
            print(f"❌ 创建视频目录失败: {e}")

        try:
            os.makedirs(final_processed_dir, exist_ok=True)
            print(f"✅ 处理后视频目录已确保存在: {final_processed_dir}")
        except Exception as e:
            print(f"❌ 创建处理后视频目录失败: {e}")

        try:
            os.makedirs(final_covers_dir, exist_ok=True)
            print(f"✅ 处理后封面目录已确保存在: {final_covers_dir}")
        except Exception as e:
            print(f"❌ 创建处理后封面目录失败: {e}")

        print(f"=== 目录设置完成 ===\n")

    def _get_video_files(self) -> List[str]:
        """获取视频文件列表"""
        video_dir = self.ui.video_dir.get()
        print(f"=== 搜索视频文件 ===")
        print(f"搜索目录: {video_dir}")

        if not os.path.exists(video_dir):
            print(f"❌ 视频目录不存在: {video_dir}")
            return []

        # 支持的视频格式
        video_extensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.webm', '.m4v']
        print(f"支持的视频格式: {', '.join(video_extensions)}")

        # 获取视频文件列表
        video_files = []
        try:
            all_files = os.listdir(video_dir)
            print(f"目录中总文件数: {len(all_files)}")

            for file in all_files:
                file_path = os.path.join(video_dir, file)
                if os.path.isfile(file_path):
                    file_ext = os.path.splitext(file)[1].lower()
                    if file_ext in video_extensions:
                        video_files.append(file)
                        print(f"✅ 找到视频文件: {file}")

            print(f"📊 总共找到 {len(video_files)} 个视频文件")

            if len(video_files) == 0:
                print("⚠️ 没有找到任何视频文件")
                print("请检查:")
                print("1. 视频文件是否在正确的目录中")
                print("2. 视频文件格式是否支持")
                print("3. 文件权限是否正确")

        except Exception as e:
            print(f"❌ 读取视频目录失败: {e}")
            return []

        return video_files

    def _get_cover_resolution(self) -> tuple:
        """获取封面分辨率"""
        # 直接使用自定义分辨率
        try:
            width = self.ui.cover_width.get()
            height = self.ui.cover_height.get()
            if width > 0 and height > 0:
                return (width, height)
        except Exception as e:
            print(f"获取自定义分辨率失败: {e}")
            pass

        # 默认分辨率
        return (1280, 720)
