"""
目录管理工具模块 - 为网易号存稿系统提供目录管理功能
"""

import os
import sys
import json
import shutil
from pathlib import Path
from datetime import datetime
from typing import Union, Optional

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

try:
    from directory_manager import DirectoryManager, DirectoryHelper
except ImportError:
    # 如果无法导入，提供基本的目录管理功能
    DirectoryManager = None
    DirectoryHelper = None

from .constants import SHARED_DIRECTORIES, ACCOUNT_DIRECTORIES, SUBDIRECTORIES


class NetEaseDirectoryManager:
    """网易号专用目录管理器"""
    
    def __init__(self):
        """初始化目录管理器"""
        self.config_file = os.path.join(project_root, "directory_config.json")
        
        # 尝试使用完整的目录管理器
        if DirectoryManager:
            try:
                self.manager = DirectoryManager(self.config_file)
                self.helper = DirectoryHelper(self.manager)
                self.use_full_manager = True
            except Exception:
                self.use_full_manager = False
        else:
            self.use_full_manager = False
    
    def get_screenshots_dir(self, screenshot_type: str = "operation") -> str:
        """
        获取截图目录路径
        
        Args:
            screenshot_type: 截图类型 (operation, error, success, debug)
            
        Returns:
            截图目录路径
        """
        base_dir = SHARED_DIRECTORIES["screenshots"]
        if screenshot_type in SUBDIRECTORIES["screenshots"]:
            subdir = SUBDIRECTORIES["screenshots"][screenshot_type]
            return os.path.join(base_dir, subdir)
        return base_dir
    
    def get_data_dir(self, data_type: str = "logs") -> str:
        """
        获取数据目录路径
        
        Args:
            data_type: 数据类型 (config, logs, cache, statistics)
            
        Returns:
            数据目录路径
        """
        base_dir = SHARED_DIRECTORIES["data"]
        if data_type in SUBDIRECTORIES["data"]:
            subdir = SUBDIRECTORIES["data"][data_type]
            return os.path.join(base_dir, subdir)
        return base_dir
    
    def get_pending_dir(self, content_type: str = "videos") -> str:
        """
        获取待处理目录路径
        
        Args:
            content_type: 内容类型 (articles, videos, images, tasks)
            
        Returns:
            待处理目录路径
        """
        base_dir = SHARED_DIRECTORIES["pending"]
        if content_type in SUBDIRECTORIES["pending"]:
            subdir = SUBDIRECTORIES["pending"][content_type]
            return os.path.join(base_dir, subdir)
        return base_dir
    
    def get_processed_dir(self, content_type: str = "videos") -> str:
        """
        获取已处理目录路径
        
        Args:
            content_type: 内容类型 (articles, videos, images, tasks)
            
        Returns:
            已处理目录路径
        """
        base_dir = SHARED_DIRECTORIES["processed"]
        if content_type in SUBDIRECTORIES["processed"]:
            subdir = SUBDIRECTORIES["processed"][content_type]
            return os.path.join(base_dir, subdir)
        return base_dir
    
    def get_completed_dir(self, content_type: str = "uploaded") -> str:
        """
        获取已完成目录路径
        
        Args:
            content_type: 内容类型 (published, uploaded, finished, archive)
            
        Returns:
            已完成目录路径
        """
        base_dir = SHARED_DIRECTORIES["completed"]
        if content_type in SUBDIRECTORIES["completed"]:
            subdir = SUBDIRECTORIES["completed"][content_type]
            return os.path.join(base_dir, subdir)
        return base_dir
    
    def get_violations_dir(self, violation_type: str = "content") -> str:
        """
        获取违规目录路径
        
        Args:
            violation_type: 违规类型 (content, records, appeals)
            
        Returns:
            违规目录路径
        """
        base_dir = SHARED_DIRECTORIES["violations"]
        if violation_type in SUBDIRECTORIES["violations"]:
            subdir = SUBDIRECTORIES["violations"][violation_type]
            return os.path.join(base_dir, subdir)
        return base_dir
    
    def get_account_dir(self, platform: str = "netease", data_type: str = "accounts") -> str:
        """
        获取账号目录路径
        
        Args:
            platform: 平台名称 (netease, toutiao, dayu)
            data_type: 数据类型 (accounts, publish_history, statistics, settings)
            
        Returns:
            账号目录路径
        """
        if platform not in ACCOUNT_DIRECTORIES:
            platform = "netease"  # 默认使用网易号
        
        base_dir = ACCOUNT_DIRECTORIES[platform]
        if data_type in SUBDIRECTORIES["accounts"]:
            subdir = SUBDIRECTORIES["accounts"][data_type]
            return os.path.join(base_dir, subdir)
        return base_dir
    
    def ensure_directory_exists(self, directory_path: str) -> bool:
        """
        确保目录存在，如果不存在则创建
        
        Args:
            directory_path: 目录路径
            
        Returns:
            是否成功创建或已存在
        """
        try:
            os.makedirs(directory_path, exist_ok=True)
            return True
        except Exception as e:
            print(f"创建目录失败: {directory_path}, 错误: {e}")
            return False
    
    def save_screenshot(self, screenshot_path: Union[str, Path], 
                       screenshot_type: str = "operation",
                       custom_name: str = None) -> Optional[str]:
        """
        保存截图到指定目录
        
        Args:
            screenshot_path: 截图文件路径
            screenshot_type: 截图类型
            custom_name: 自定义文件名
            
        Returns:
            保存后的文件路径，失败返回None
        """
        try:
            target_dir = self.get_screenshots_dir(screenshot_type)
            self.ensure_directory_exists(target_dir)
            
            source_path = Path(screenshot_path)
            if not source_path.exists():
                return None
            
            if custom_name:
                target_name = custom_name
                if not target_name.endswith(source_path.suffix):
                    target_name += source_path.suffix
            else:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                target_name = f"{screenshot_type}_{timestamp}{source_path.suffix}"
            
            target_path = os.path.join(target_dir, target_name)
            
            if self.use_full_manager and self.helper:
                success = self.helper.save_screenshot(screenshot_path, screenshot_type)
                return target_path if success else None
            else:
                shutil.copy2(str(source_path), target_path)
                return target_path
                
        except Exception as e:
            print(f"保存截图失败: {e}")
            return None
    
    def move_file_to_stage(self, file_path: Union[str, Path], 
                          from_stage: str, to_stage: str,
                          content_type: str = "videos") -> bool:
        """
        在工作流阶段间移动文件
        
        Args:
            file_path: 文件路径
            from_stage: 源阶段 (pending, processed, completed)
            to_stage: 目标阶段 (pending, processed, completed)
            content_type: 内容类型
            
        Returns:
            是否移动成功
        """
        try:
            source_path = Path(file_path)
            if not source_path.exists():
                return False
            
            # 获取目标目录
            if to_stage == "pending":
                target_dir = self.get_pending_dir(content_type)
            elif to_stage == "processed":
                target_dir = self.get_processed_dir(content_type)
            elif to_stage == "completed":
                target_dir = self.get_completed_dir(content_type)
            else:
                return False
            
            self.ensure_directory_exists(target_dir)
            target_path = os.path.join(target_dir, source_path.name)
            
            if self.use_full_manager and self.helper:
                if to_stage == "processed":
                    return self.helper.move_to_processed(file_path)
                elif to_stage == "completed":
                    return self.helper.move_to_completed(file_path)
            
            # 使用基本的文件移动
            shutil.move(str(source_path), target_path)
            return True
            
        except Exception as e:
            print(f"移动文件失败: {e}")
            return False
    
    def backup_violation_content(self, file_path: Union[str, Path]) -> bool:
        """
        备份违规内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否备份成功
        """
        try:
            if self.use_full_manager and self.helper:
                return self.helper.backup_to_violations(file_path)
            
            # 使用基本的文件复制
            source_path = Path(file_path)
            if not source_path.exists():
                return False
            
            target_dir = self.get_violations_dir("content")
            self.ensure_directory_exists(target_dir)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            target_name = f"violation_{timestamp}_{source_path.name}"
            target_path = os.path.join(target_dir, target_name)
            
            shutil.copy2(str(source_path), target_path)
            return True
            
        except Exception as e:
            print(f"备份违规内容失败: {e}")
            return False
    
    def get_log_file_path(self, log_name: str) -> str:
        """
        获取日志文件路径
        
        Args:
            log_name: 日志文件名
            
        Returns:
            日志文件完整路径
        """
        log_dir = self.get_data_dir("logs")
        self.ensure_directory_exists(log_dir)
        
        if not log_name.endswith('.log'):
            log_name += '.log'
        
        return os.path.join(log_dir, log_name)
    
    def get_config_file_path(self, config_name: str) -> str:
        """
        获取配置文件路径
        
        Args:
            config_name: 配置文件名
            
        Returns:
            配置文件完整路径
        """
        config_dir = self.get_data_dir("config")
        self.ensure_directory_exists(config_dir)
        
        if not config_name.endswith(('.json', '.yaml', '.yml', '.ini')):
            config_name += '.json'
        
        return os.path.join(config_dir, config_name)
    
    def initialize_directories(self) -> bool:
        """
        初始化所有必要的目录
        
        Returns:
            是否初始化成功
        """
        try:
            if self.use_full_manager:
                return self.manager.create_directory_structure()
            
            # 使用基本的目录创建
            all_dirs = []
            
            # 添加共用目录
            for base_dir in SHARED_DIRECTORIES.values():
                all_dirs.append(base_dir)
            
            # 添加账号目录
            for base_dir in ACCOUNT_DIRECTORIES.values():
                all_dirs.append(base_dir)
            
            # 添加子目录
            for main_dir, subdirs in SUBDIRECTORIES.items():
                if main_dir == "accounts":
                    # 账号子目录
                    for account_dir in ACCOUNT_DIRECTORIES.values():
                        for subdir in subdirs.values():
                            all_dirs.append(os.path.join(account_dir, subdir))
                else:
                    # 共用子目录
                    if main_dir in SHARED_DIRECTORIES:
                        base_dir = SHARED_DIRECTORIES[main_dir]
                        for subdir in subdirs.values():
                            all_dirs.append(os.path.join(base_dir, subdir))
            
            # 创建所有目录
            for directory in all_dirs:
                self.ensure_directory_exists(directory)
            
            return True
            
        except Exception as e:
            print(f"初始化目录失败: {e}")
            return False


# 创建全局实例
directory_manager = NetEaseDirectoryManager()
