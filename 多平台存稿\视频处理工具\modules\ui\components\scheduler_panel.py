"""
定时任务面板组件 - 用于管理定时任务的UI组件
"""

import os
import tkinter as tk
from tkinter import ttk
import datetime
from typing import Dict, List, Any, Optional, Callable

from ...common.scheduler import Scheduler, Task

def create_scheduler_panel(parent: ttk.Frame, ui_instance) -> ttk.Frame:
    """
    创建定时任务面板

    Args:
        parent: 父容器
        ui_instance: UI实例

    Returns:
        ttk.Frame: 定时任务面板框架
    """
    # 创建定时任务面板框架
    scheduler_frame = ttk.LabelFrame(parent, text="定时任务")
    scheduler_frame.pack(fill=tk.X, padx=10, pady=10)

    # 添加一个标签，使面板更加明显
    ttk.Label(scheduler_frame, text="这里可以设置定时处理任务", foreground="blue").pack(pady=5)

    # 确保UI实例有调度器属性
    if not hasattr(ui_instance, 'scheduler'):
        ui_instance.scheduler = Scheduler(logger=ui_instance.log)
        ui_instance.scheduler.start()  # 启动调度器

    # 创建定时任务控制区域
    control_frame = ttk.Frame(scheduler_frame)
    control_frame.pack(fill=tk.X, padx=10, pady=5)

    # 创建启用定时任务复选框
    enable_check = ttk.Checkbutton(control_frame, text="启用定时任务",
                                 variable=ui_instance.enable_scheduler,
                                 command=lambda: toggle_scheduler(ui_instance))
    enable_check.pack(side=tk.LEFT, padx=5)

    # 创建定时处理设置区域 - 使用更紧凑的布局
    settings_frame = ttk.Frame(scheduler_frame)
    settings_frame.pack(fill=tk.X, padx=10, pady=5)

    # 创建左侧时间设置区域
    time_frame = ttk.Frame(settings_frame)
    time_frame.pack(side=tk.LEFT, fill=tk.Y)

    # 创建定时处理时间设置
    ttk.Label(time_frame, text="定时处理时间:").pack(side=tk.LEFT, padx=5)

    # 创建小时和分钟选择
    hour_spinner = ttk.Spinbox(time_frame, from_=0, to=23, width=3,
                             textvariable=ui_instance.schedule_hour, format="%02.0f")
    hour_spinner.pack(side=tk.LEFT)

    ttk.Label(time_frame, text=":").pack(side=tk.LEFT)

    minute_spinner = ttk.Spinbox(time_frame, from_=0, to=59, width=3,
                               textvariable=ui_instance.schedule_minute, format="%02.0f")
    minute_spinner.pack(side=tk.LEFT)

    # 创建右侧按钮区域
    button_frame = ttk.Frame(settings_frame)
    button_frame.pack(side=tk.RIGHT, fill=tk.Y)

    # 创建定时处理按钮 - 使用更大的按钮和更明显的样式
    schedule_button = ttk.Button(button_frame, text="设置定时处理",
                               command=lambda: schedule_processing_task(ui_instance))
    schedule_button.pack(side=tk.RIGHT, padx=5)

    return scheduler_frame

def toggle_scheduler(ui_instance):
    """
    切换调度器状态

    Args:
        ui_instance: UI实例
    """
    if ui_instance.enable_scheduler.get():
        if not ui_instance.scheduler.running:
            ui_instance.scheduler.start()
            ui_instance.log("✅ 调度器已启动", "success")
    else:
        if ui_instance.scheduler.running:
            ui_instance.scheduler.stop()
            ui_instance.log("✅ 调度器已停止", "info")

def schedule_processing_task(ui_instance):
    """
    设置定时处理任务

    Args:
        ui_instance: UI实例
    """
    try:
        # 获取时间设置
        hour_str = ui_instance.schedule_hour.get()
        minute_str = ui_instance.schedule_minute.get()

        # 确保时间格式正确
        try:
            hour = int(hour_str)
            minute = int(minute_str)
        except ValueError:
            # 如果转换失败，使用默认值
            hour = 0
            minute = 0
            ui_instance.schedule_hour.set("00")
            ui_instance.schedule_minute.set("00")
            ui_instance.log("⚠️ 时间格式不正确，已重置为00:00", "warning")

        # 创建今天的日期
        today = datetime.datetime.now().date()

        # 创建计划时间
        schedule_time = datetime.datetime.combine(today, datetime.time(hour, minute))

        # 如果时间已经过去，设置为明天
        if schedule_time < datetime.datetime.now():
            schedule_time = schedule_time + datetime.timedelta(days=1)

        # 创建任务ID
        task_id = f"process_{int(datetime.datetime.now().timestamp())}"

        # 创建任务
        ui_instance.scheduler.schedule_task(
            task_id=task_id,
            callback=lambda: schedule_processing_callback(ui_instance),
            schedule_time=schedule_time,
            description=f"定时处理 {schedule_time.strftime('%H:%M')}"
        )

        ui_instance.log(f"✅ 已设置定时处理任务: {schedule_time.strftime('%Y-%m-%d %H:%M')}", "success")
    except Exception as e:
        ui_instance.log(f"❌ 设置定时处理任务失败: {str(e)}", "error")

def schedule_processing_callback(ui_instance):
    """
    定时处理回调函数

    Args:
        ui_instance: UI实例
    """
    try:
        ui_instance.log("⏰ 定时处理任务开始执行", "info")

        # 调用处理函数
        ui_instance.start_preprocess()

        ui_instance.log("✅ 定时处理任务执行完成", "success")
        return True
    except Exception as e:
        ui_instance.log(f"❌ 定时处理任务执行失败: {str(e)}", "error")
        return False
