"""
日志面板模块 - 负责显示和管理日志
支持更灵活的日志过滤控制
"""

import os
import queue
import threading
import tkinter as tk
from tkinter import ttk, scrolledtext, filedialog, messagebox, simpledialog
from typing import Callable, Optional, Dict, Any, List, Set
import datetime

from 网易号存稿.common.logger import logger, LOG_LEVELS
from 网易号存稿.ui.styles.checkbox_styles import create_beautiful_checkbox

class LogPanel:
    """日志面板类，负责显示和管理日志，支持更灵活的日志过滤控制"""

    def __init__(self, parent: ttk.Frame, log_queue: Optional[queue.Queue] = None,
                 enable_level_filter: Optional[tk.BooleanVar] = None,
                 enable_keyword_filter: Optional[tk.BooleanVar] = None,
                 min_log_level: Optional[tk.StringVar] = None):
        """
        初始化日志面板

        Args:
            parent: 父容器
            log_queue: 日志队列，如果为None则创建新队列
            enable_level_filter: 是否启用日志级别过滤的变量
            enable_keyword_filter: 是否启用关键词过滤的变量
            min_log_level: 最小日志级别的变量
        """
        self.parent = parent
        self.log_queue = log_queue or queue.Queue()

        # 日志过滤设置
        self.enable_level_filter = enable_level_filter or tk.BooleanVar(value=logger.enable_level_filter)
        self.enable_keyword_filter = enable_keyword_filter or tk.BooleanVar(value=logger.enable_keyword_filter)
        self.min_log_level = min_log_level or tk.StringVar(value=next((k for k, v in LOG_LEVELS.items() if v == logger.min_level), "INFO"))

        # 本地日志显示设置
        self.log_level = tk.StringVar(value="ALL")  # 本地日志显示级别
        self.auto_scroll = tk.BooleanVar(value=True)  # 自动滚动
        self.filtered_keywords = set(logger.filtered_keywords)  # 过滤关键词的本地副本

        # 日志压缩和行数限制设置
        self.max_log_lines = 1000  # 最大日志行数
        self.last_log_message = ""  # 上一条日志消息
        self.repeat_count = 0  # 重复次数
        self.repeat_tag = "repeat"  # 重复日志的标签名

        # 创建UI
        self.create_ui()

        # 启动日志处理线程
        self.start_log_handler()

        # 绑定变量变化事件
        self.enable_level_filter.trace_add("write", self.on_filter_setting_changed)
        self.enable_keyword_filter.trace_add("write", self.on_filter_setting_changed)
        self.min_log_level.trace_add("write", self.on_filter_setting_changed)

    def create_ui(self) -> None:
        """创建用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.parent)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建顶部设置面板 - 使用现代化卡片式设计
        settings_container = ttk.Frame(main_frame)
        settings_container.pack(fill=tk.X, padx=10, pady=10)

        # 创建设置卡片框架
        settings_card = ttk.LabelFrame(settings_container, text="🔧 日志设置", padding=15)
        settings_card.pack(fill=tk.X)

        # 第一行：过滤开关
        filter_switches_frame = ttk.Frame(settings_card)
        filter_switches_frame.pack(fill=tk.X, pady=(0, 10))

        # 左侧：日志级别过滤
        level_section = ttk.Frame(filter_switches_frame)
        level_section.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 使用美化复选框 - 启用日志级别过滤
        level_filter_checkbox = create_beautiful_checkbox(
            level_section,
            text="📊 启用日志级别过滤",
            variable=self.enable_level_filter,
            style_type="modern"
        )
        level_filter_checkbox.pack(anchor=tk.W)

        # 右侧：关键词过滤
        keyword_section = ttk.Frame(filter_switches_frame)
        keyword_section.pack(side=tk.RIGHT, fill=tk.X, expand=True)

        # 使用美化复选框 - 启用关键词过滤
        keyword_filter_checkbox = create_beautiful_checkbox(
            keyword_section,
            text="🔍 启用关键词过滤",
            variable=self.enable_keyword_filter,
            style_type="accent"
        )
        keyword_filter_checkbox.pack(anchor=tk.W)

        # 第二行：详细设置
        details_frame = ttk.Frame(settings_card)
        details_frame.pack(fill=tk.X, pady=(5, 0))

        # 左侧：日志级别选择
        level_config_frame = ttk.Frame(details_frame)
        level_config_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        level_label = ttk.Label(level_config_frame, text="最小日志级别:", font=("微软雅黑", 9))
        level_label.pack(anchor=tk.W, pady=(0, 3))

        min_level_combobox = ttk.Combobox(level_config_frame, textvariable=self.min_log_level,
                                         width=12, state="readonly", font=("微软雅黑", 9))
        min_level_combobox["values"] = tuple(LOG_LEVELS.keys())
        min_level_combobox.pack(anchor=tk.W)

        # 右侧：关键词管理
        keyword_config_frame = ttk.Frame(details_frame)
        keyword_config_frame.pack(side=tk.RIGHT, fill=tk.X, expand=True)

        keyword_label = ttk.Label(keyword_config_frame, text="关键词管理:", font=("微软雅黑", 9))
        keyword_label.pack(anchor=tk.W, pady=(0, 3))

        keyword_btn = ttk.Button(keyword_config_frame, text="🛠️ 管理过滤关键词",
                               command=self.manage_filtered_keywords, width=18)
        keyword_btn.pack(anchor=tk.W)

        # 创建日志显示区域
        log_display_container = ttk.Frame(main_frame)
        log_display_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

        # 日志显示卡片
        log_card = ttk.LabelFrame(log_display_container, text="📋 日志输出", padding=10)
        log_card.pack(fill=tk.BOTH, expand=True)

        # 日志控制工具栏
        log_toolbar = ttk.Frame(log_card)
        log_toolbar.pack(fill=tk.X, pady=(0, 8))

        # 左侧：操作按钮组 - 改进响应式布局
        action_buttons_frame = ttk.Frame(log_toolbar)
        action_buttons_frame.pack(side=tk.LEFT, fill=tk.Y)

        # 使用图标美化按钮 - 减少按钮宽度以适应小窗口
        clear_btn = ttk.Button(action_buttons_frame, text="🗑️ 清空", command=self.clear_log, width=7)
        clear_btn.pack(side=tk.LEFT, padx=(0, 3))

        save_btn = ttk.Button(action_buttons_frame, text="💾 保存", command=self.save_log, width=7)
        save_btn.pack(side=tk.LEFT, padx=(0, 3))

        copy_btn = ttk.Button(action_buttons_frame, text="📋 复制", command=self.copy_log, width=7)
        copy_btn.pack(side=tk.LEFT, padx=(0, 3))

        # 中间：分隔线
        ttk.Separator(log_toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, padx=15, fill=tk.Y)

        # 右侧：显示控制组 - 改进响应式布局
        display_controls_frame = ttk.Frame(log_toolbar)
        display_controls_frame.pack(side=tk.RIGHT, fill=tk.Y)

        # 显示级别过滤 - 减少间距以适应小窗口
        level_filter_frame = ttk.Frame(display_controls_frame)
        level_filter_frame.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Label(level_filter_frame, text="级别:", font=("微软雅黑", 9)).pack(side=tk.LEFT, padx=(0, 3))
        level_combobox = ttk.Combobox(level_filter_frame, textvariable=self.log_level,
                                     width=7, state="readonly", font=("微软雅黑", 9))
        level_combobox["values"] = ("ALL", "INFO", "WARNING", "ERROR", "DEBUG")
        level_combobox.pack(side=tk.LEFT)
        level_combobox.bind("<<ComboboxSelected>>", self.on_log_level_changed)

        # 自动滚动选项 - 减少间距
        self.auto_scroll = tk.BooleanVar(value=True)
        auto_scroll_checkbox = create_beautiful_checkbox(
            display_controls_frame,
            text="📜 自动滚动",
            variable=self.auto_scroll,
            style_type="success"
        )
        auto_scroll_checkbox.pack(side=tk.LEFT, padx=(5, 0))

        # 日志文本框 - 删除滚动条，只保留滚轮滚动，使用系统默认字体大小
        self.log_text = tk.Text(
            log_card,
            height=18,
            font=("Consolas", 9),  # 使用系统默认字体大小
            wrap=tk.WORD,
            relief=tk.FLAT,
            borderwidth=1
        )

        # 添加滚轮支持
        def _on_mousewheel(event):
            self.log_text.yview_scroll(int(-1*(event.delta/120)), "units")

        def _bind_mousewheel(event):
            self.log_text.bind_all("<MouseWheel>", _on_mousewheel)

        def _unbind_mousewheel(event):
            self.log_text.unbind_all("<MouseWheel>")

        self.log_text.bind('<Enter>', _bind_mousewheel)
        self.log_text.bind('<Leave>', _unbind_mousewheel)

        self.log_text.pack(fill=tk.BOTH, expand=True)

    def start_log_handler(self) -> None:
        """启动日志处理线程"""
        def log_handler():
            while True:
                try:
                    # 从队列获取日志消息
                    message = self.log_queue.get(block=True, timeout=0.1)

                    # 在UI线程中更新日志文本框
                    self.parent.after(0, self._update_log_text, message)

                    # 标记任务完成
                    self.log_queue.task_done()

                except queue.Empty:
                    # 队列为空，继续等待
                    continue

                except Exception as e:
                    print(f"日志处理异常: {str(e)}")

        # 启动日志处理线程
        threading.Thread(target=log_handler, daemon=True).start()

    def _update_log_text(self, message: str) -> None:
        """
        更新日志文本框

        Args:
            message: 日志消息
        """
        # 检查日志级别过滤
        if self.log_level.get() != "ALL":
            # 如果设置了特定级别，只显示该级别的日志
            if f"[{self.log_level.get()}]" not in message:
                return

        # 启用文本框编辑
        self.log_text.configure(state=tk.NORMAL)

        # 检查是否需要限制日志行数
        if self.max_log_lines > 0:
            # 获取当前行数
            current_lines = int(self.log_text.index('end-1c').split('.')[0])

            # 如果超过最大行数，删除旧日志
            if current_lines >= self.max_log_lines:
                # 计算需要删除的行数 (删除10%的旧日志)
                lines_to_delete = int(self.max_log_lines * 0.1)
                self.log_text.delete("1.0", f"{lines_to_delete + 1}.0")

                # 添加一条提示消息，表明有日志被删除
                self.log_text.insert("1.0", f"[系统] 已删除 {lines_to_delete} 行旧日志以节省内存...\n")
                self.log_text.tag_add("system", "1.0", "1.end")
                self.log_text.tag_configure("system", foreground="#808080", font=("Consolas", 10, "italic"))

        # 日志压缩 - 检查是否与上一条消息相同
        # 提取时间戳后的实际消息内容进行比较
        message_parts = message.split("] ", 2)
        if len(message_parts) >= 3:
            # 提取实际消息内容（不包括时间戳和日志级别）
            actual_message = message_parts[2]

            if actual_message == self.last_log_message:
                # 如果与上一条消息相同，增加重复计数
                self.repeat_count += 1

                # 删除上一行的重复计数（如果有）
                last_line_index = self.log_text.index("end-2c linestart")
                if self.repeat_count > 1:
                    # 查找上一行是否已有重复标记
                    last_line = self.log_text.get(last_line_index, "end-1c")
                    if " (重复 " in last_line:
                        # 删除上一行
                        self.log_text.delete(last_line_index, "end-1c")

                # 添加带有重复计数的消息
                repeat_message = f"{message} (重复 {self.repeat_count}次)"
                self.log_text.insert(tk.END, repeat_message + "\n")

                # 设置重复消息的标签
                self.log_text.tag_add(self.repeat_tag, f"end-{len(repeat_message)+1}c linestart", "end-1c")
                self.log_text.tag_configure(self.repeat_tag, font=("Consolas", 10, "italic"))

                # 根据日志级别设置颜色
                self._apply_color_tag(message, repeat_message)

                # 如果启用了自动滚动，滚动到底部
                if self.auto_scroll.get():
                    self.log_text.see(tk.END)

                # 禁用文本框编辑
                self.log_text.configure(state=tk.DISABLED)
                return
            else:
                # 如果与上一条消息不同，重置重复计数
                self.last_log_message = actual_message
                self.repeat_count = 0
        else:
            # 如果消息格式不符合预期，重置重复计数
            self.last_log_message = ""
            self.repeat_count = 0

        # 添加消息
        self.log_text.insert(tk.END, message + "\n")

        # 根据日志级别设置颜色
        self._apply_color_tag(message, message)

        # 如果启用了自动滚动，滚动到底部
        if self.auto_scroll.get():
            self.log_text.see(tk.END)

        # 禁用文本框编辑
        self.log_text.configure(state=tk.DISABLED)

    def _apply_color_tag(self, original_message: str, display_message: str) -> None:
        """
        根据日志级别应用颜色标签

        Args:
            original_message: 原始日志消息
            display_message: 显示的日志消息（可能包含重复计数）
        """
        if "[ERROR]" in original_message:
            self.log_text.tag_add("error", f"end-{len(display_message)+1}c linestart", "end-1c")
            self.log_text.tag_configure("error", foreground="red")
        elif "[WARNING]" in original_message:
            self.log_text.tag_add("warning", f"end-{len(display_message)+1}c linestart", "end-1c")
            self.log_text.tag_configure("warning", foreground="orange")
        elif "[INFO]" in original_message:
            self.log_text.tag_add("info", f"end-{len(display_message)+1}c linestart", "end-1c")
            self.log_text.tag_configure("info", foreground="blue")
        elif "[DEBUG]" in original_message:
            self.log_text.tag_add("debug", f"end-{len(display_message)+1}c linestart", "end-1c")
            self.log_text.tag_configure("debug", foreground="gray")

    def log(self, message: str, level: str = "INFO") -> None:
        """
        记录日志

        Args:
            message: 日志消息
            level: 日志级别，如INFO、WARNING、ERROR等
        """
        # 将日志消息放入队列
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}"
        self.log_queue.put(log_entry)

    def clear_log(self) -> None:
        """清空日志"""
        self.log_text.configure(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.configure(state=tk.DISABLED)

    def save_log(self) -> None:
        """保存日志到文件"""
        # 获取当前时间作为文件名
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"netease_draft_log_{timestamp}.txt"

        # 打开文件对话框
        file_path = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt")],
            initialfile=filename
        )

        if file_path:
            try:
                # 获取日志内容 - 包括压缩后的日志
                log_content = self.log_text.get(1.0, tk.END)

                # 添加保存信息到日志内容
                save_info = f"\n\n[保存信息] 此日志文件包含压缩后的日志内容，'(重复 X次)' 表示该消息重复出现了X次。\n"
                save_info += f"[保存信息] 保存时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                log_content += save_info

                # 保存到文件
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(log_content)

                self.log(f"日志已保存到: {file_path}")
                messagebox.showinfo("保存成功", f"日志已保存到:\n{file_path}")

            except Exception as e:
                self.log(f"保存日志失败: {str(e)}", "ERROR")
                messagebox.showerror("保存失败", f"保存日志失败:\n{str(e)}")

    def copy_log(self) -> None:
        """复制日志到剪贴板"""
        # 获取日志内容 - 包括压缩后的日志
        log_content = self.log_text.get(1.0, tk.END)

        # 添加复制信息
        copy_info = f"\n\n[复制信息] 此日志内容包含压缩后的日志，'(重复 X次)' 表示该消息重复出现了X次。\n"
        copy_info += f"[复制信息] 复制时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        log_content += copy_info

        # 复制到剪贴板
        self.parent.clipboard_clear()
        self.parent.clipboard_append(log_content)

        self.log("日志已复制到剪贴板")

    def manage_filtered_keywords(self) -> None:
        """管理过滤关键词"""
        # 创建关键词管理窗口
        keyword_window = tk.Toplevel(self.parent)
        keyword_window.title("🔍 过滤关键词管理")
        keyword_window.geometry("600x500")
        keyword_window.resizable(True, True)

        # 居中窗口
        window_width = 600
        window_height = 500
        screen_width = keyword_window.winfo_screenwidth()
        screen_height = keyword_window.winfo_screenheight()
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        keyword_window.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # 创建主框架
        main_frame = ttk.Frame(keyword_window, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题区域
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 15))

        title_label = ttk.Label(title_frame, text="🔍 过滤关键词管理",
                               font=("微软雅黑", 12, "bold"))
        title_label.pack(side=tk.LEFT)

        count_label = ttk.Label(title_frame, text=f"共 {len(self.filtered_keywords)} 个关键词",
                               font=("微软雅黑", 9), foreground="gray")
        count_label.pack(side=tk.RIGHT)

        # 创建关键词列表卡片
        list_card = ttk.LabelFrame(main_frame, text="📋 关键词列表", padding=15)
        list_card.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # 创建列表框容器
        list_container = ttk.Frame(list_card)
        list_container.pack(fill=tk.BOTH, expand=True)

        # 关键词列表框 - 保持当前功能样式
        keyword_list = tk.Listbox(list_container, height=12, width=60,
                                 font=("微软雅黑", 10), relief=tk.FLAT, borderwidth=1)
        keyword_list.pack(fill=tk.BOTH, expand=True)

        # 删除滚动条，添加鼠标滚轮支持（保持当前功能）
        def _on_mousewheel(event):
            keyword_list.yview_scroll(int(-1*(event.delta/120)), "units")

        def _bind_mousewheel(event):
            keyword_list.bind_all("<MouseWheel>", _on_mousewheel)

        def _unbind_mousewheel(event):
            keyword_list.unbind_all("<MouseWheel>")

        keyword_list.bind('<Enter>', _bind_mousewheel)
        keyword_list.bind('<Leave>', _unbind_mousewheel)

        # 填充关键词列表（保持当前功能）
        for keyword in sorted(self.filtered_keywords):
            keyword_list.insert(tk.END, keyword)

        # 更新计数显示的函数
        def update_count():
            count_label.config(text=f"共 {keyword_list.size()} 个关键词")

        # 创建操作按钮区域
        action_frame = ttk.LabelFrame(main_frame, text="🛠️ 操作", padding=15)
        action_frame.pack(fill=tk.X, pady=(0, 15))

        # 按钮容器
        button_container = ttk.Frame(action_frame)
        button_container.pack(fill=tk.X)

        # 添加关键词（保持当前功能）
        def add_keyword():
            keyword = simpledialog.askstring("添加关键词", "请输入要过滤的关键词:", parent=keyword_window)
            if keyword and keyword.strip():
                keyword = keyword.strip()
                if keyword not in self.filtered_keywords:
                    # 添加到本地列表
                    self.filtered_keywords.add(keyword)
                    # 添加到全局日志记录器
                    logger.add_filtered_keyword(keyword)
                    # 更新列表显示
                    keyword_list.insert(tk.END, keyword)
                    # 重新排序列表
                    items = list(keyword_list.get(0, tk.END))
                    keyword_list.delete(0, tk.END)
                    for item in sorted(items):
                        keyword_list.insert(tk.END, item)
                    update_count()
                    self.log(f"✅ 已添加过滤关键词: {keyword}")
                else:
                    messagebox.showinfo("提示", "该关键词已存在", parent=keyword_window)

        # 删除关键词（保持当前功能）
        def remove_keyword():
            selected = keyword_list.curselection()
            if selected:
                keyword = keyword_list.get(selected[0])
                # 从本地列表移除
                if keyword in self.filtered_keywords:
                    self.filtered_keywords.remove(keyword)
                # 从全局日志记录器移除
                logger.remove_filtered_keyword(keyword)
                # 更新列表显示
                keyword_list.delete(selected[0])
                update_count()
                self.log(f"🗑️ 已移除过滤关键词: {keyword}")
            else:
                messagebox.showinfo("提示", "请先选择要删除的关键词", parent=keyword_window)

        # 清空关键词（保持当前功能）
        def clear_keywords():
            if keyword_list.size() == 0:
                messagebox.showinfo("提示", "关键词列表已为空", parent=keyword_window)
                return

            if messagebox.askyesno("确认", "确定要清空所有过滤关键词吗?", parent=keyword_window):
                # 清空本地列表
                self.filtered_keywords.clear()
                # 清空全局日志记录器
                logger.clear_filtered_keywords()
                # 更新列表显示
                keyword_list.delete(0, tk.END)
                update_count()
                self.log("🧹 已清空所有过滤关键词")

        # 左侧按钮组
        left_buttons = ttk.Frame(button_container)
        left_buttons.pack(side=tk.LEFT)

        add_btn = ttk.Button(left_buttons, text="➕ 添加关键词", command=add_keyword, width=15)
        add_btn.pack(side=tk.LEFT, padx=(0, 8))

        remove_btn = ttk.Button(left_buttons, text="🗑️ 删除选中", command=remove_keyword, width=15)
        remove_btn.pack(side=tk.LEFT, padx=(0, 8))

        clear_btn = ttk.Button(left_buttons, text="🧹 清空所有", command=clear_keywords, width=15)
        clear_btn.pack(side=tk.LEFT)

        # 右侧按钮组
        right_buttons = ttk.Frame(button_container)
        right_buttons.pack(side=tk.RIGHT)

        close_btn = ttk.Button(right_buttons, text="❌ 关闭", command=keyword_window.destroy, width=12)
        close_btn.pack(side=tk.RIGHT)

        # 设置模态窗口
        keyword_window.transient(self.parent)
        keyword_window.grab_set()
        self.parent.wait_window(keyword_window)

    def on_filter_setting_changed(self, *args) -> None:
        """
        过滤设置变化事件处理

        Args:
            *args: 事件参数
        """
        # 更新全局日志记录器设置
        logger.enable_level_filter = self.enable_level_filter.get()
        logger.enable_keyword_filter = self.enable_keyword_filter.get()
        logger.set_min_level(self.min_log_level.get())

        # 保存日志配置
        if hasattr(logger, 'save_config'):
            logger.save_config()

        # 记录日志
        self.log(f"日志过滤设置已更新: 级别过滤={self.enable_level_filter.get()}, 关键词过滤={self.enable_keyword_filter.get()}, 最小级别={self.min_log_level.get()}")

    def on_log_level_changed(self, event=None) -> None:
        """
        日志级别变化事件处理

        Args:
            event: 事件对象
        """
        # 获取当前日志内容
        current_log = self.log_text.get(1.0, tk.END)

        # 清空日志
        self.clear_log()

        # 重置日志压缩状态
        self.last_log_message = ""
        self.repeat_count = 0

        # 如果选择了ALL，显示所有日志
        if self.log_level.get() == "ALL":
            self.log_text.configure(state=tk.NORMAL)
            self.log_text.insert(tk.END, current_log)
            self.log_text.configure(state=tk.DISABLED)
            return

        # 否则，只显示选定级别的日志
        self.log_text.configure(state=tk.NORMAL)

        # 设置最大显示行数
        max_filtered_lines = self.max_log_lines

        # 从最新的日志开始处理，确保显示最新的日志
        lines = current_log.splitlines()
        filtered_lines = []

        for line in lines:
            if f"[{self.log_level.get()}]" in line:
                filtered_lines.append(line)

        # 如果过滤后的行数超过最大行数，只保留最新的日志
        if len(filtered_lines) > max_filtered_lines:
            # 添加一条提示消息
            self.log_text.insert(tk.END, f"[系统] 仅显示最新的 {max_filtered_lines} 条 {self.log_level.get()} 级别日志...\n")
            self.log_text.tag_add("system", "end-2c linestart", "end-1c")
            self.log_text.tag_configure("system", foreground="#808080", font=("Consolas", 10, "italic"))

            # 只显示最新的日志
            filtered_lines = filtered_lines[-max_filtered_lines:]

        # 显示过滤后的日志
        for line in filtered_lines:
            self.log_text.insert(tk.END, line + "\n")

            # 应用颜色标签
            if "[ERROR]" in line:
                self.log_text.tag_add("error", f"end-{len(line)+1}c linestart", "end-1c")
                self.log_text.tag_configure("error", foreground="red")
            elif "[WARNING]" in line:
                self.log_text.tag_add("warning", f"end-{len(line)+1}c linestart", "end-1c")
                self.log_text.tag_configure("warning", foreground="orange")
            elif "[INFO]" in line:
                self.log_text.tag_add("info", f"end-{len(line)+1}c linestart", "end-1c")
                self.log_text.tag_configure("info", foreground="blue")
            elif "[DEBUG]" in line:
                self.log_text.tag_add("debug", f"end-{len(line)+1}c linestart", "end-1c")
                self.log_text.tag_configure("debug", foreground="gray")

            # 检查是否有重复标记
            if " (重复 " in line:
                self.log_text.tag_add(self.repeat_tag, f"end-{len(line)+1}c linestart", "end-1c")
                self.log_text.tag_configure(self.repeat_tag, font=("Consolas", 10, "italic"))

        # 滚动到底部
        if self.auto_scroll.get():
            self.log_text.see(tk.END)

        self.log_text.configure(state=tk.DISABLED)
