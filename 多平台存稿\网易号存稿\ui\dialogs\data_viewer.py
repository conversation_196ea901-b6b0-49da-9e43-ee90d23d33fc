"""
账号数据查看器模块 - 用于显示和分析账号数据，包括存稿详情

【作废计划 目前仍在使用 但不更新】
注意：此模块为详细数据查看器的实现，虽然标记为作废计划，但目前仍在使用中。
该模块不再进行功能更新和维护，仅保持现有功能的稳定运行。
新的数据查看功能请使用其他模块实现。
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from datetime import datetime, timedelta
import os
import json
import csv
import re
import time
import traceback

# 尝试导入PIL库，用于图片处理
try:
    from PIL import Image, ImageTk
except ImportError:
    # 如果导入失败，记录错误但不中断程序
    traceback.print_exc()
    print("警告: PIL库导入失败，图片显示功能可能不可用")

class ImageViewer:
    """
    图片查看器类 - 支持缩放、拖拽和滚轮操作

    【作废计划 目前仍在使用 但不更新】
    注意：此类为图片查看器的实现，虽然标记为作废计划，但目前仍在使用中。
    该类不再进行功能更新和维护，仅保持现有图片查看功能的稳定运行。
    """

    def __init__(self, parent_window, original_image, image_path, log_callback):
        """
        初始化图片查看器

        Args:
            parent_window: 父窗口
            original_image: PIL图片对象
            image_path: 图片文件路径
            log_callback: 日志回调函数
        """
        self.parent_window = parent_window
        self.original_image = original_image
        self.image_path = image_path
        self.log = log_callback

        # 缩放和拖拽相关变量
        self.scale_factor = 1.0
        self.min_scale = 0.1
        self.max_scale = 10.0
        self.drag_start_x = 0
        self.drag_start_y = 0
        self.is_dragging = False

        # 图片显示相关变量
        self.canvas_width = 0
        self.canvas_height = 0
        self.image_id = None
        self.current_photo = None

        # 创建UI
        self.create_ui()

        # 初始化显示
        self.fit_to_window()

    def create_ui(self):
        """创建用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.parent_window, padding=5)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建工具栏
        toolbar = ttk.Frame(main_frame)
        toolbar.pack(fill=tk.X, pady=(0, 5))

        # 缩放控制按钮
        ttk.Button(toolbar, text="🔍+", command=self.zoom_in, width=4).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="🔍-", command=self.zoom_out, width=4).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="适应窗口", command=self.fit_to_window).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar, text="实际大小", command=self.actual_size).pack(side=tk.LEFT, padx=2)

        # 缩放比例显示
        self.scale_label = ttk.Label(toolbar, text="100%")
        self.scale_label.pack(side=tk.LEFT, padx=10)

        # 右侧按钮
        ttk.Button(toolbar, text="打开文件位置", command=self.open_file_location).pack(side=tk.RIGHT, padx=2)
        ttk.Button(toolbar, text="关闭", command=self.parent_window.destroy).pack(side=tk.RIGHT, padx=5)

        # 创建画布和滚动条
        canvas_frame = ttk.Frame(main_frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True)

        # 创建画布
        self.canvas = tk.Canvas(canvas_frame, bg="white", highlightthickness=0)

        # 删除滚动条，只保留鼠标滚轮滚动

        # 布局（删除滚动条）
        self.canvas.pack(fill=tk.BOTH, expand=True)

        # 绑定鼠标滚轮事件以支持滚动
        def _on_mousewheel(event):
            self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        def _on_horizontal_mousewheel(event):
            self.canvas.xview_scroll(int(-1*(event.delta/120)), "units")

        def _bind_mousewheel(event):
            self.canvas.bind_all("<MouseWheel>", _on_mousewheel)
            # 绑定Shift+滚轮进行水平滚动
            self.canvas.bind_all("<Shift-MouseWheel>", _on_horizontal_mousewheel)

        def _unbind_mousewheel(event):
            self.canvas.unbind_all("<MouseWheel>")
            self.canvas.unbind_all("<Shift-MouseWheel>")

        self.canvas.bind('<Enter>', _bind_mousewheel)
        self.canvas.bind('<Leave>', _unbind_mousewheel)

        # 绑定事件
        self.bind_events()

        # 绑定窗口大小变化事件
        self.parent_window.bind("<Configure>", self.on_window_resize)

    def bind_events(self):
        """绑定鼠标和键盘事件"""
        # 鼠标滚轮缩放
        self.canvas.bind("<MouseWheel>", self.on_mouse_wheel)
        self.canvas.bind("<Button-4>", self.on_mouse_wheel)  # Linux
        self.canvas.bind("<Button-5>", self.on_mouse_wheel)  # Linux

        # 鼠标拖拽
        self.canvas.bind("<Button-1>", self.on_drag_start)
        self.canvas.bind("<B1-Motion>", self.on_drag_motion)
        self.canvas.bind("<ButtonRelease-1>", self.on_drag_end)

        # 键盘快捷键
        self.parent_window.bind("<Control-plus>", lambda e: self.zoom_in())
        self.parent_window.bind("<Control-minus>", lambda e: self.zoom_out())
        self.parent_window.bind("<Control-0>", lambda e: self.fit_to_window())
        self.parent_window.bind("<Control-1>", lambda e: self.actual_size())

        # 确保窗口可以接收键盘事件
        self.parent_window.focus_set()

    def update_image(self):
        """更新图片显示"""
        try:
            # 计算缩放后的图片尺寸
            orig_width, orig_height = self.original_image.size
            new_width = int(orig_width * self.scale_factor)
            new_height = int(orig_height * self.scale_factor)

            # 缩放图片
            if self.scale_factor == 1.0:
                # 原始大小，不需要缩放
                scaled_image = self.original_image
            else:
                # 选择合适的重采样方法
                try:
                    if self.scale_factor > 1.0:
                        # 放大时使用LANCZOS
                        scaled_image = self.original_image.resize((new_width, new_height), Image.LANCZOS)
                    else:
                        # 缩小时使用LANCZOS
                        scaled_image = self.original_image.resize((new_width, new_height), Image.LANCZOS)
                except AttributeError:
                    try:
                        # 旧版PIL使用ANTIALIAS
                        scaled_image = self.original_image.resize((new_width, new_height), Image.ANTIALIAS)
                    except AttributeError:
                        # 如果都不支持，使用默认方法
                        scaled_image = self.original_image.resize((new_width, new_height))

            # 转换为PhotoImage
            self.current_photo = ImageTk.PhotoImage(scaled_image)

            # 更新画布
            if self.image_id:
                self.canvas.delete(self.image_id)

            # 在画布中心显示图片
            self.image_id = self.canvas.create_image(
                new_width // 2, new_height // 2,
                image=self.current_photo,
                anchor=tk.CENTER
            )

            # 更新滚动区域
            self.canvas.configure(scrollregion=(0, 0, new_width, new_height))

            # 更新缩放比例显示
            self.scale_label.config(text=f"{int(self.scale_factor * 100)}%")

        except Exception as e:
            self.log(f"更新图片显示失败: {str(e)}")

    def zoom_in(self):
        """放大图片"""
        new_scale = self.scale_factor * 1.2
        if new_scale <= self.max_scale:
            self.scale_factor = new_scale
            self.update_image()

    def zoom_out(self):
        """缩小图片"""
        new_scale = self.scale_factor / 1.2
        if new_scale >= self.min_scale:
            self.scale_factor = new_scale
            self.update_image()

    def fit_to_window(self):
        """适应窗口大小"""
        try:
            # 获取画布大小
            self.canvas.update_idletasks()
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()

            if canvas_width <= 1 or canvas_height <= 1:
                # 画布还没有正确初始化，延迟执行
                self.parent_window.after(100, self.fit_to_window)
                return

            # 计算适应窗口的缩放比例
            orig_width, orig_height = self.original_image.size
            scale_x = (canvas_width - 20) / orig_width  # 留一些边距
            scale_y = (canvas_height - 20) / orig_height

            # 选择较小的缩放比例以确保图片完全显示
            self.scale_factor = min(scale_x, scale_y, 1.0)  # 不超过原始大小
            self.scale_factor = max(self.scale_factor, self.min_scale)

            self.update_image()

        except Exception as e:
            self.log(f"适应窗口失败: {str(e)}")

    def actual_size(self):
        """显示实际大小"""
        self.scale_factor = 1.0
        self.update_image()

    def on_mouse_wheel(self, event):
        """处理鼠标滚轮事件"""
        try:
            # 获取鼠标位置
            mouse_x = self.canvas.canvasx(event.x)
            mouse_y = self.canvas.canvasy(event.y)

            # 确定缩放方向
            if event.delta > 0 or event.num == 4:
                # 向上滚动，放大
                scale_change = 1.1
            else:
                # 向下滚动，缩小
                scale_change = 0.9

            # 计算新的缩放比例
            new_scale = self.scale_factor * scale_change
            new_scale = max(self.min_scale, min(self.max_scale, new_scale))

            if new_scale != self.scale_factor:
                # 计算缩放中心点
                old_scale = self.scale_factor
                self.scale_factor = new_scale

                # 更新图片
                self.update_image()

                # 调整滚动位置以保持鼠标位置不变
                scale_ratio = new_scale / old_scale
                new_mouse_x = mouse_x * scale_ratio
                new_mouse_y = mouse_y * scale_ratio

                # 计算滚动偏移
                canvas_width = self.canvas.winfo_width()
                canvas_height = self.canvas.winfo_height()

                scroll_x = (new_mouse_x - mouse_x) / (self.canvas.winfo_width())
                scroll_y = (new_mouse_y - mouse_y) / (self.canvas.winfo_height())

                # 应用滚动
                current_x = self.canvas.canvasx(0) / self.canvas.winfo_width()
                current_y = self.canvas.canvasy(0) / self.canvas.winfo_height()

                self.canvas.xview_moveto(current_x + scroll_x)
                self.canvas.yview_moveto(current_y + scroll_y)

        except Exception as e:
            self.log(f"鼠标滚轮缩放失败: {str(e)}")

    def on_drag_start(self, event):
        """开始拖拽"""
        self.is_dragging = True
        self.drag_start_x = event.x
        self.drag_start_y = event.y
        self.canvas.config(cursor="fleur")  # 改变鼠标样式

    def on_drag_motion(self, event):
        """拖拽移动"""
        if self.is_dragging:
            try:
                # 计算移动距离
                dx = event.x - self.drag_start_x
                dy = event.y - self.drag_start_y

                # 获取当前滚动位置
                canvas_width = self.canvas.winfo_width()
                canvas_height = self.canvas.winfo_height()

                # 计算新的滚动位置
                scroll_x = -dx / canvas_width
                scroll_y = -dy / canvas_height

                # 应用滚动
                self.canvas.xview_scroll(int(scroll_x * 10), "units")
                self.canvas.yview_scroll(int(scroll_y * 10), "units")

                # 更新起始位置
                self.drag_start_x = event.x
                self.drag_start_y = event.y

            except Exception as e:
                self.log(f"拖拽移动失败: {str(e)}")

    def on_drag_end(self, event):
        """结束拖拽"""
        self.is_dragging = False
        self.canvas.config(cursor="")  # 恢复鼠标样式

    def on_window_resize(self, event):
        """窗口大小变化事件"""
        # 只处理主窗口的大小变化事件
        if event.widget == self.parent_window:
            # 延迟一点时间再更新，避免频繁更新
            self.parent_window.after(100, self._delayed_resize)

    def _delayed_resize(self):
        """延迟的窗口大小调整"""
        # 这里可以添加一些窗口大小变化后的处理逻辑
        pass

    def open_file_location(self):
        """打开文件位置"""
        try:
            import subprocess
            import os

            # 确保路径是绝对路径
            abs_path = os.path.abspath(self.image_path)

            # 获取文件所在目录
            dir_path = os.path.dirname(abs_path)

            # 检查目录是否存在
            if not os.path.exists(dir_path):
                messagebox.showwarning("警告", f"目录不存在: {dir_path}")
                return

            # 使用 explorer 打开目录并选中文件
            self.log(f"正在打开目录: {dir_path}")
            subprocess.run(["explorer", "/select,", abs_path], shell=True)

        except Exception as e:
            try:
                # 如果选中文件失败，尝试只打开目录
                dir_path = os.path.dirname(os.path.abspath(self.image_path))
                subprocess.run(["explorer", dir_path], shell=True)
            except Exception as e2:
                messagebox.showerror("错误", f"打开文件位置失败: {str(e2)}")
                self.log(f"打开文件位置失败: {str(e2)}")

class AccountDataViewer:
    """
    账号数据查看器类

    【作废计划 目前仍在使用 但不更新】
    注意：此类为详细数据查看器的核心实现，虽然标记为作废计划，但目前仍在使用中。
    该类不再进行功能更新和维护，仅保持现有功能的稳定运行。
    包含的功能：
    - 账号数据表格显示
    - 数据导出功能
    - 账号详情查看
    - 右键菜单操作
    - 数据查询集成
    新的数据查看功能请使用其他类实现。
    """

    def __init__(self, parent, accounts_data, log_callback=None, account_details=None, current_platform=None, config_manager=None, data_query_manager=None):
        """
        初始化账号数据查看器

        Args:
            parent: 父窗口
            accounts_data: 账号数据列表
            log_callback: 日志回调函数
            account_details: 存稿详情数据，如果为None则从文件加载
            current_platform: 当前平台
            config_manager: 配置管理器
            data_query_manager: 数据查询管理器
        """
        self.parent = parent
        self.accounts_data = accounts_data
        self.log = log_callback or (lambda x: print(x))
        self.current_platform = current_platform
        self.config_manager = config_manager
        self.data_query_manager = data_query_manager

        # 存稿详情数据结构
        self.draft_details = account_details or {}

        # 如果没有传入存稿详情数据，则从文件加载
        if not account_details:
            self.load_draft_details()

        # 计算居中位置
        width = 1200
        height = 800

        # 获取根窗口用于创建Toplevel和获取屏幕尺寸
        root_window = parent.root if hasattr(parent, 'root') else parent
        screen_width = root_window.winfo_screenwidth()
        screen_height = root_window.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2

        # 创建窗口并直接设置居中位置
        self.window = tk.Toplevel(root_window)
        self.window.title("账号数据查看器")
        self.window.geometry(f"{width}x{height}+{x}+{y}")  # 直接设置大小和位置
        self.window.minsize(800, 600)

        # 设置窗口图标
        try:
            self.window.iconbitmap("assets/icon.ico")
        except:
            pass

        # 创建UI
        self.create_ui()

        # 加载数据
        self.load_data()

    def create_ui(self):
        """
        创建用户界面

        【作废计划 目前仍在使用 但不更新】
        注意：此方法为详细数据查看器界面创建的核心实现，虽然标记为作废计划，但目前仍在使用中。
        该方法不再进行功能更新和维护，仅保持现有界面的稳定显示。
        """
        # 创建主框架
        main_frame = ttk.Frame(self.window, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建标签页
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # 创建数据表格标签页
        self.create_data_tab()

        # 创建底部按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(button_frame, text="刷新数据", command=self.load_data).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="导出Excel", command=self.export_to_excel).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="关闭", command=self.window.destroy).pack(side=tk.RIGHT, padx=5)

    def load_draft_details(self):
        """加载存稿详情数据 - 按平台分离加载"""
        try:
            # 使用配置管理器获取项目根目录
            if hasattr(self, 'config_manager') and self.config_manager and hasattr(self.config_manager, 'base_dir'):
                root_dir = self.config_manager.base_dir
            else:
                root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

            # 根据当前平台确定加载路径
            platform_draft_details_path = None
            legacy_draft_details_path = os.path.join(root_dir, "draft_details.json")  # 旧版本通用文件

            if self.current_platform == "netease":
                platform_draft_details_path = os.path.join(root_dir, "netease_draft_details.json")
            elif self.current_platform == "toutiao":
                platform_draft_details_path = os.path.join(root_dir, "toutiao_draft_details.json")
            elif self.current_platform == "dayu":
                platform_draft_details_path = os.path.join(root_dir, "dayu_draft_details.json")

            # 优先加载平台特定的文件
            if platform_draft_details_path and os.path.exists(platform_draft_details_path):
                with open(platform_draft_details_path, 'r', encoding='utf-8') as f:
                    loaded_details = json.load(f)

                # 过滤掉测试账号
                self.draft_details = {}
                for account, details in loaded_details.items():
                    # 检查是否为测试账号（这里可以根据实际情况调整判断条件）
                    if account.lower() != "测试账号" and not account.lower().startswith("test"):
                        self.draft_details[account] = details

                self.log(f"已加载{self.current_platform}平台存稿详情数据，共 {len(self.draft_details)} 个账号")

            # 如果平台特定文件不存在，尝试从旧版本通用文件加载
            elif os.path.exists(legacy_draft_details_path):
                with open(legacy_draft_details_path, 'r', encoding='utf-8') as f:
                    loaded_details = json.load(f)

                # 过滤掉测试账号，并且只保留当前平台的账号
                self.draft_details = {}
                current_accounts = set()

                # 获取当前平台的账号列表
                try:
                    current_accounts = set(account['账号'] for account in self.accounts_data)
                except:
                    pass

                for account, details in loaded_details.items():
                    # 检查是否为测试账号和是否为当前平台账号
                    if (account.lower() != "测试账号" and
                        not account.lower().startswith("test") and
                        (not current_accounts or account in current_accounts)):
                        self.draft_details[account] = details

                self.log(f"已从通用文件加载{self.current_platform}平台存稿详情数据，共 {len(self.draft_details)} 个账号")
            else:
                self.log(f"未找到{self.current_platform}平台存稿详情数据文件，将创建新的数据结构")
                self.draft_details = {}
        except Exception as e:
            self.log(f"加载存稿详情数据失败: {str(e)}")
            self.draft_details = {}

    def save_draft_details(self):
        """保存存稿详情数据 - 按平台分离存储"""
        try:
            # 使用配置管理器获取项目根目录
            if hasattr(self, 'config_manager') and self.config_manager and hasattr(self.config_manager, 'base_dir'):
                root_dir = self.config_manager.base_dir
            else:
                root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

            # 根据当前平台确定保存路径
            if self.current_platform == "netease":
                draft_details_path = os.path.join(root_dir, "netease_draft_details.json")
            elif self.current_platform == "toutiao":
                draft_details_path = os.path.join(root_dir, "toutiao_draft_details.json")
            elif self.current_platform == "dayu":
                draft_details_path = os.path.join(root_dir, "dayu_draft_details.json")
            else:
                # 默认保存到通用文件（兼容旧版本）
                draft_details_path = os.path.join(root_dir, "draft_details.json")

            with open(draft_details_path, 'w', encoding='utf-8') as f:
                json.dump(self.draft_details, f, ensure_ascii=False, indent=2)
            self.log(f"已保存{self.current_platform}平台存稿详情数据")
        except Exception as e:
            self.log(f"保存存稿详情数据失败: {str(e)}")

    def create_data_tab(self):
        """创建数据表格标签页"""
        data_frame = ttk.Frame(self.notebook)
        self.notebook.add(data_frame, text="数据表格")

        # 创建工具栏
        toolbar = ttk.Frame(data_frame)
        toolbar.pack(fill=tk.X, pady=(0, 10))

        # 搜索功能已移除，已整合到主页面

        # 初始化排序状态
        self.data_sort_reverse = {}

        # 加载排序配置
        self.load_sort_config()

        # 重复按钮已移除，功能已整合到主页面

        # 添加存稿详情按钮
        ttk.Button(toolbar, text="存稿详情", command=self.show_draft_details).pack(side=tk.LEFT, padx=5)

        # 添加账号数量标签
        self.account_count_label = ttk.Label(toolbar, text="", font=("Arial", 10))
        self.account_count_label.pack(side=tk.RIGHT, padx=10)

        # 创建表格
        table_frame = ttk.Frame(data_frame)
        table_frame.pack(fill=tk.BOTH, expand=True)

        # 定义列
        columns = ("账号", "用户名", "七日收益", "七日详情", "总收益", "昨日收益", "总提现", "待提现", "最近提现日", "总播放", "昨日播放", "总粉丝", "昨日粉丝", "草稿箱", "状态", "更新时间")
        self.data_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=20, selectmode="extended")

        # 设置列标题和宽度
        for col in columns:
            if col == "七日详情":
                # 七日详情列不支持排序，只显示按钮
                self.data_tree.heading(col, text="")
            else:
                # 其他列支持排序
                self.data_tree.heading(col, text=col, command=lambda c=col: self.sort_data_by_column(c))

            if col == "账号":
                self.data_tree.column(col, width=120, anchor=tk.W)
            elif col == "用户名":
                self.data_tree.column(col, width=100, anchor=tk.W)
            elif col == "总收益" or col == "昨日收益" or col == "七日收益" or col == "总提现" or col == "待提现":
                self.data_tree.column(col, width=80, anchor=tk.CENTER)
            elif col == "七日详情":
                self.data_tree.column(col, width=60, anchor=tk.CENTER)
            elif col == "最近提现日":
                self.data_tree.column(col, width=100, anchor=tk.CENTER)
            elif col == "更新时间":
                self.data_tree.column(col, width=150, anchor=tk.CENTER)  # 更新时间列宽度不变
            elif col == "草稿箱":
                self.data_tree.column(col, width=60, anchor=tk.CENTER)
            elif col == "状态":
                self.data_tree.column(col, width=70, anchor=tk.CENTER)
            elif col == "总播放" or col == "昨日播放" or col == "总粉丝" or col == "昨日粉丝":
                self.data_tree.column(col, width=80, anchor=tk.CENTER)
            else:
                self.data_tree.column(col, width=80, anchor=tk.CENTER)

        # 添加滚轮支持
        def _on_mousewheel(event):
            self.data_tree.yview_scroll(int(-1*(event.delta/120)), "units")

        def _bind_mousewheel(event):
            self.data_tree.bind_all("<MouseWheel>", _on_mousewheel)

        def _unbind_mousewheel(event):
            self.data_tree.unbind_all("<MouseWheel>")

        self.data_tree.bind('<Enter>', _bind_mousewheel)
        self.data_tree.bind('<Leave>', _unbind_mousewheel)

        # 布局
        self.data_tree.pack(fill=tk.BOTH, expand=True)

        # 注意：双击、单击和右键菜单功能已移植到主界面账号列表

        # 注意：右键菜单功能已移植到主界面账号列表





    def show_seven_day_income(self, account=None):
        """显示七天收益历史窗口

        Args:
            account: 可选，指定要显示的账号。如果为None，则显示所有账号。
        """
        # 保存当前选中的账号，用于存稿详情按钮
        self.current_income_account = account

        # 计算居中位置
        width = 1000
        height = 600
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2

        # 创建新窗口并直接设置居中位置
        income_window = tk.Toplevel(self.window)
        income_window.title("七天收益历史" + (f" - {account}" if account else ""))
        income_window.geometry(f"{width}x{height}+{x}+{y}")  # 直接设置大小和位置
        income_window.minsize(800, 500)

        # 设置窗口图标
        try:
            income_window.iconbitmap("icon.ico")
        except:
            pass

        # 创建七天收益历史窗口
        self.create_seven_day_income_window(income_window)

        # 加载七天收益历史数据
        self.load_seven_day_income_data(account)



    def show_draft_details(self, account=None):
        """
        显示存稿详情窗口

        【作废计划 目前仍在使用 但不更新】
        注意：此方法为存稿详情窗口显示的实现，虽然标记为作废计划，但目前仍在使用中。
        该方法不再进行功能更新和维护，仅保持现有功能的稳定运行。

        Args:
            account: 可选，指定要显示的账号。如果为None，则显示所有账号。
        """
        # 计算居中位置
        width = 1200
        height = 900
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2

        # 创建新窗口并直接设置居中位置
        details_window = tk.Toplevel(self.window)
        details_window.title("存稿详情" + (f" - {account}" if account else ""))
        details_window.geometry(f"{width}x{height}+{x}+{y}")  # 直接设置大小和位置
        details_window.minsize(1000, 900)  # 增加最小高度

        # 设置窗口图标
        try:
            # 使用与主窗口相同的图标
            details_window.iconbitmap(self.window.iconbitmap())
        except:
            # 尝试使用相对路径
            try:
                details_window.iconbitmap("assets/icon.ico")
            except:
                pass

        # 创建存稿详情窗口
        self.create_draft_details_window(details_window, account)

    def create_draft_details_window(self, parent_window, filter_account=None):
        """创建存稿详情窗口

        Args:
            parent_window: 父窗口
            filter_account: 可选，指定要显示的账号。如果为None，则显示所有账号。
        """
        # 创建主框架
        main_frame = ttk.Frame(parent_window, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建工具栏
        toolbar = ttk.Frame(main_frame)
        toolbar.pack(fill=tk.X, pady=(0, 10))

        # 添加账号选择下拉框
        ttk.Label(toolbar, text="选择账号:").pack(side=tk.LEFT, padx=5)

        # 获取所有账号列表
        accounts = ["全部账号"]
        for data in self.accounts_data:
            account = data.get("账号", "")
            if account and account != "总计":
                accounts.append(account)

        # 创建账号选择下拉框
        self.selected_account = tk.StringVar(value=filter_account if filter_account else "全部账号")
        account_combo = ttk.Combobox(toolbar, textvariable=self.selected_account, values=accounts, width=20)
        account_combo.pack(side=tk.LEFT, padx=5)
        account_combo.bind("<<ComboboxSelected>>", lambda _: self.load_draft_details_data())

        # 添加搜索框
        ttk.Label(toolbar, text="搜索:").pack(side=tk.LEFT, padx=(20, 5))
        self.draft_search_var = tk.StringVar()
        self.draft_search_var.trace_add("write", lambda *_: self.load_draft_details_data())
        ttk.Entry(toolbar, textvariable=self.draft_search_var, width=30).pack(side=tk.LEFT, padx=5)

        # 添加排序选项
        ttk.Label(toolbar, text="排序:").pack(side=tk.LEFT, padx=(20, 5))
        self.draft_sort_order = tk.StringVar(value="降序")
        ttk.Combobox(toolbar, textvariable=self.draft_sort_order, values=["升序", "降序"], width=10).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar, text="应用排序", command=self.apply_draft_sort).pack(side=tk.LEFT, padx=5)

        # 添加按钮
        ttk.Button(toolbar, text="添加记录", command=self.add_draft_detail).pack(side=tk.LEFT, padx=(20, 5))
        ttk.Button(toolbar, text="删除选中", command=self.delete_draft_detail).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar, text="导出CSV", command=self.export_draft_details).pack(side=tk.LEFT, padx=5)

        # 创建状态栏 - 显示成功存稿数量
        status_frame = ttk.Frame(main_frame)
        status_frame.pack(fill=tk.X, pady=(5, 10))

        # 创建状态标签
        self.status_label = ttk.Label(status_frame, text="", font=("Arial", 10))
        self.status_label.pack(side=tk.LEFT, padx=5)

        # 创建表格框架
        table_frame = ttk.Frame(main_frame)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建表格
        columns = ("账号", "时间", "视频", "状态", "存稿成功", "原因", "截图")
        self.draft_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=20)

        # 设置列标题和宽度
        self.draft_tree.heading("账号", text="账号", command=lambda: self.sort_draft_by_column("账号"))
        self.draft_tree.column("账号", width=150, anchor=tk.W)

        self.draft_tree.heading("时间", text="时间", command=lambda: self.sort_draft_by_column("时间"))
        self.draft_tree.column("时间", width=150, anchor=tk.CENTER)

        self.draft_tree.heading("视频", text="视频", command=lambda: self.sort_draft_by_column("视频"))
        self.draft_tree.column("视频", width=250, anchor=tk.W)

        self.draft_tree.heading("状态", text="状态", command=lambda: self.sort_draft_by_column("状态"))
        self.draft_tree.column("状态", width=80, anchor=tk.CENTER)

        self.draft_tree.heading("存稿成功", text="存稿成功", command=lambda: self.sort_draft_by_column("存稿成功"))
        self.draft_tree.column("存稿成功", width=80, anchor=tk.CENTER)

        self.draft_tree.heading("原因", text="失败原因", command=lambda: self.sort_draft_by_column("原因"))
        self.draft_tree.column("原因", width=250, anchor=tk.W)  # 调整宽度以适应新增列

        self.draft_tree.heading("截图", text="截图", command=lambda: self.sort_draft_by_column("截图"))
        self.draft_tree.column("截图", width=80, anchor=tk.CENTER)

        # 添加滚动条
        y_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.draft_tree.yview)
        x_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.draft_tree.xview)
        self.draft_tree.configure(yscrollcommand=y_scrollbar.set, xscrollcommand=x_scrollbar.set)

        # 布局
        self.draft_tree.pack(side=tk.TOP, fill=tk.BOTH, expand=True)
        y_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        x_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

        # 设置截图列的样式
        self.draft_tree.tag_configure("screenshot", foreground="blue")
        # 设置成功状态的样式
        self.draft_tree.tag_configure("success", foreground="green")
        # 设置失败状态的样式
        self.draft_tree.tag_configure("failure", foreground="red")

        # 绑定点击事件
        self.draft_tree.bind("<ButtonRelease-1>", self.on_draft_tree_click)

        # 添加底部按钮 - 使用固定高度的框架确保按钮始终可见
        button_frame = ttk.Frame(main_frame, height=50)  # 设置固定高度
        button_frame.pack(fill=tk.X, pady=(30, 10))  # 增加上边距，确保按钮不被遮挡
        button_frame.pack_propagate(False)  # 防止框架被内部组件压缩

        # 刷新数据按钮 - 使用更明显的样式
        refresh_button = ttk.Button(button_frame, text="刷新数据", command=self.load_draft_details_data, width=15)
        refresh_button.pack(side=tk.LEFT, padx=10, pady=10)

        # 尝试设置刷新按钮的样式为强调样式
        try:
            style = ttk.Style()
            style.configure("Accent.TButton", font=("微软雅黑", 10, "bold"))
            refresh_button.configure(style="Accent.TButton")
        except Exception as e:
            print(f"设置按钮样式失败: {e}")

        # 关闭按钮
        ttk.Button(button_frame, text="关闭", command=parent_window.destroy, width=15).pack(side=tk.RIGHT, padx=10, pady=10)

        # 加载数据
        self.load_draft_details_data(filter_account)

    def load_draft_details_data(self, filter_account=None):
        """加载存稿详情数据

        Args:
            filter_account: 可选，指定要显示的账号。如果为None，则使用当前选中的账号。
        """
        # 重新从文件加载存稿详情数据以获取最新数据
        self.load_draft_details()

        # 如果未指定账号，使用当前选中的账号
        if filter_account is None:
            filter_account = self.selected_account.get()
            if filter_account == "全部账号":
                filter_account = None

        # 获取搜索文本
        search_text = self.draft_search_var.get().lower()

        # 清空现有数据
        for item in self.draft_tree.get_children():
            self.draft_tree.delete(item)

        # 如果没有存稿详情数据，显示提示信息
        if not self.draft_details:
            self.draft_tree.insert("", "end", values=("暂无存稿详情数据", "", "", "", "", "", ""))
            self.status_label.config(text="暂无存稿详情数据")
            return

        # 统计变量
        total_count = 0  # 总记录数
        success_count = 0  # 成功记录数
        failure_count = 0  # 失败记录数
        account_stats = {}  # 按账号统计: {账号: {"总数": 0, "成功": 0, "失败": 0}}

        # 准备数据列表，用于排序
        all_records = []

        # 收集符合条件的数据
        for account, details in self.draft_details.items():
            # 初始化账号统计
            if account not in account_stats:
                account_stats[account] = {"总数": 0, "成功": 0, "失败": 0}

            # 如果指定了过滤账号，只显示该账号的数据
            if filter_account and account != filter_account:
                continue

            for detail in details:
                # 更新账号统计
                account_stats[account]["总数"] += 1
                status = detail.get("状态", "").lower()
                if status == "成功":
                    account_stats[account]["成功"] += 1
                elif status == "失败":
                    account_stats[account]["失败"] += 1

                # 获取状态和视频信息
                status = detail.get("状态", "")
                video = detail.get("视频", "").lower()
                reason = detail.get("原因", "").lower()

                # 检查是否匹配搜索条件
                if search_text and not (search_text in account.lower() or search_text in video or search_text in status.lower() or search_text in reason):
                    continue

                # 处理截图列
                screenshot = detail.get("截图", "")
                screenshot_text = "查看" if screenshot else ""

                # 确定行标签
                tags = []
                if screenshot:
                    tags.append("screenshot")

                # 获取该账号的成功存稿数（从记录中获取）
                draft_success_count = 0
                # 直接使用当前记录中的存稿成功数，不再重新计算
                if "存稿成功" in detail:
                    draft_success_count = detail.get("存稿成功", 0)
                    # 确保是整数
                    if isinstance(draft_success_count, str) and draft_success_count.strip():
                        try:
                            draft_success_count = int(draft_success_count)
                        except ValueError:
                            draft_success_count = 0

                # 设置标签和样式
                if status.lower() == "成功":
                    tags.append("success")
                elif status.lower() == "失败":
                    tags.append("failure")

                # 添加到记录列表
                all_records.append({
                    "account": account,
                    "time": detail.get("时间", ""),
                    "video": detail.get("视频", ""),
                    "status": status,
                    "success_count": draft_success_count,
                    "reason": detail.get("原因", ""),
                    "screenshot": screenshot_text,
                    "tags": tuple(tags)
                })

        # 按时间排序（默认降序，即最新的在前面）
        is_reverse = self.draft_sort_order.get() == "降序"
        all_records.sort(key=lambda x: x["time"], reverse=is_reverse)

        # 插入排序后的数据
        count = 0
        for record in all_records:
            self.draft_tree.insert("", "end", values=(
                record["account"],
                record["time"],
                record["video"],
                record["status"],
                record["success_count"],
                record["reason"],
                record["screenshot"]
            ), tags=record["tags"])
            count += 1

        # 如果没有匹配的数据，显示提示信息
        if count == 0:
            self.draft_tree.insert("", "end", values=("没有匹配的存稿详情数据", "", "", "", "", "", ""))
            self.status_label.config(text="没有匹配的存稿详情数据")
            return

        # 计算总统计数据
        for acc_stat in account_stats.values():
            total_count += acc_stat["总数"]
            success_count += acc_stat["成功"]
            failure_count += acc_stat["失败"]

        # 更新状态栏显示
        if filter_account and filter_account != "全部账号":
            # 显示特定账号
            status_text = f"当前显示: 账号 {filter_account}"
        else:
            # 显示所有账号的统计
            account_count = len([acc for acc in self.draft_details.keys() if acc.lower() != "测试账号" and not acc.lower().startswith("test")])
            status_text = f"账号数量: {account_count}"

        self.status_label.config(text=status_text)

    def add_draft_detail(self):
        """添加存稿详情记录"""
        # 获取当前选中的账号
        account = self.selected_account.get()
        if account == "全部账号":
            # 如果选择了"全部账号"，弹出对话框让用户选择账号
            accounts = []
            for data in self.accounts_data:
                acc = data.get("账号", "")
                if acc and acc != "总计":
                    accounts.append(acc)

            if not accounts:
                messagebox.showwarning("警告", "没有可用的账号")
                return

            account = simpledialog.askstring("选择账号", "请输入账号名称:", initialvalue=accounts[0])
            if not account:
                return

        # 创建新窗口
        add_window = tk.Toplevel(self.window)
        add_window.title(f"添加存稿详情 - {account}")
        add_window.geometry("500x300")
        add_window.minsize(400, 300)
        add_window.transient(self.window)  # 设置为模态窗口
        add_window.grab_set()  # 阻止用户与其他窗口交互

        # 创建表单
        form_frame = ttk.Frame(add_window, padding=10)
        form_frame.pack(fill=tk.BOTH, expand=True)

        # 时间
        ttk.Label(form_frame, text="时间:").grid(row=0, column=0, sticky=tk.W, pady=5)
        time_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        ttk.Entry(form_frame, textvariable=time_var, width=30).grid(row=0, column=1, sticky=tk.W, pady=5)

        # 视频
        ttk.Label(form_frame, text="视频:").grid(row=1, column=0, sticky=tk.W, pady=5)
        video_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=video_var, width=30).grid(row=1, column=1, sticky=tk.W, pady=5)

        # 状态
        ttk.Label(form_frame, text="状态:").grid(row=2, column=0, sticky=tk.W, pady=5)
        status_var = tk.StringVar()
        ttk.Combobox(form_frame, textvariable=status_var, values=["成功", "失败", "警告", "进行中"], width=28).grid(row=2, column=1, sticky=tk.W, pady=5)

        # 存稿成功数
        ttk.Label(form_frame, text="存稿成功:").grid(row=3, column=0, sticky=tk.W, pady=5)
        success_count_var = tk.StringVar(value="0")
        ttk.Entry(form_frame, textvariable=success_count_var, width=30).grid(row=3, column=1, sticky=tk.W, pady=5)

        # 原因
        ttk.Label(form_frame, text="原因:").grid(row=4, column=0, sticky=tk.W, pady=5)
        reason_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=reason_var, width=30).grid(row=4, column=1, sticky=tk.W, pady=5)

        # 截图
        ttk.Label(form_frame, text="截图:").grid(row=5, column=0, sticky=tk.W, pady=5)
        screenshot_var = tk.StringVar()
        screenshot_entry = ttk.Entry(form_frame, textvariable=screenshot_var, width=30)
        screenshot_entry.grid(row=5, column=1, sticky=tk.W, pady=5)

        # 添加浏览按钮
        def browse_screenshot():
            from tkinter import filedialog
            file_path = filedialog.askopenfilename(
                filetypes=[("图片文件", "*.png;*.jpg;*.jpeg;*.bmp")]
            )
            if file_path:
                screenshot_var.set(file_path)

        browse_button = ttk.Button(form_frame, text="浏览...", command=browse_screenshot)
        browse_button.grid(row=5, column=2, padx=5, pady=5)

        # 按钮
        button_frame = ttk.Frame(form_frame)
        button_frame.grid(row=6, column=0, columnspan=2, pady=20)

        def save_detail():
            # 获取表单数据
            time_str = time_var.get()
            video = video_var.get()
            status = status_var.get()
            success_count = success_count_var.get()
            reason = reason_var.get()
            screenshot = screenshot_var.get()

            # 验证截图路径
            if screenshot and not os.path.exists(screenshot):
                messagebox.showwarning("警告", f"截图文件不存在:\n{screenshot}")
                return

            # 验证数据
            if not video:
                messagebox.showwarning("警告", "请输入视频名称")
                return

            if not status:
                messagebox.showwarning("警告", "请选择状态")
                return

            # 添加到存稿详情数据结构
            if account not in self.draft_details:
                self.draft_details[account] = []

            # 创建记录字典
            record = {
                "时间": time_str,
                "视频": video,
                "状态": status,
                "原因": reason,
                "截图": screenshot
            }

            # 如果存稿成功数不为空，添加到记录中
            if success_count:
                record["存稿成功"] = success_count

            self.draft_details[account].append(record)

            # 保存数据
            self.save_draft_details()

            # 刷新显示
            self.load_draft_details_data()

            # 关闭窗口
            add_window.destroy()

        ttk.Button(button_frame, text="保存", command=save_detail).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=add_window.destroy).pack(side=tk.LEFT, padx=5)

    def apply_draft_sort(self):
        """应用存稿详情排序"""
        # 重新加载数据，排序将在load_draft_details_data方法中应用
        self.load_draft_details_data()

    def sort_draft_by_column(self, column):
        """按列排序存稿详情

        Args:
            column: 列名
        """
        # 获取当前排序顺序
        current_order = self.draft_sort_order.get()

        # 如果点击的是时间列，切换排序顺序
        if column == "时间":
            # 切换排序顺序
            new_order = "升序" if current_order == "降序" else "降序"
            self.draft_sort_order.set(new_order)

        # 应用排序
        self.apply_draft_sort()

    def delete_draft_detail(self):
        """删除选中的存稿详情记录"""
        # 获取选中的项目
        selected = self.draft_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择要删除的记录")
            return

        # 确认删除
        if not messagebox.askyesno("确认", "确定要删除选中的记录吗？"):
            return

        # 删除选中的记录
        for item in selected:
            values = self.draft_tree.item(item, "values")
            account = values[0]
            time_str = values[1]
            video = values[2]

            # 从数据结构中删除
            if account in self.draft_details:
                # 查找匹配的记录
                for i, detail in enumerate(self.draft_details[account]):
                    if detail.get("时间") == time_str and detail.get("视频") == video:
                        del self.draft_details[account][i]
                        break

                # 如果账号没有记录了，删除账号
                if not self.draft_details[account]:
                    del self.draft_details[account]

        # 保存数据
        self.save_draft_details()

        # 刷新显示
        self.load_draft_details_data()

    def on_draft_tree_click(self, event):
        """处理存稿详情表格的点击事件"""
        # 获取点击的行和列
        region = self.draft_tree.identify_region(event.x, event.y)
        if region != "cell":
            return

        # 获取点击的列
        column = self.draft_tree.identify_column(event.x)
        column_index = int(column.replace("#", "")) - 1

        # 如果不是点击截图列，直接返回
        if column_index != 6:  # 截图列的索引是6（第7列）
            return

        # 获取选中的行
        item = self.draft_tree.identify_row(event.y)
        if not item:
            return

        # 获取行数据
        values = self.draft_tree.item(item, "values")
        if not values or len(values) < 7 or values[6] != "查看":
            return

        # 获取账号和视频名称
        account = values[0]
        video = values[2]

        # 查找对应的截图路径
        screenshot_path = ""
        if account in self.draft_details:
            for detail in self.draft_details[account]:
                if detail.get("视频") == video:
                    screenshot_path = detail.get("截图", "")
                    break

        if not screenshot_path:
            messagebox.showwarning("警告", "未找到截图路径信息")
            return

        # 检查截图文件是否存在
        if not os.path.exists(screenshot_path):
            # 尝试在配置的截图目录中查找
            config_screenshots_dir = ""

            try:
                # 尝试获取配置的截图目录
                import json
                # 使用配置管理器获取配置文件路径
                if hasattr(self, 'config_manager') and self.config_manager:
                    config_path = self.config_manager.config_file
                else:
                    config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "config.json")
                if os.path.exists(config_path):
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                        config_screenshots_dir = config.get("screenshots_dir", "")
            except Exception as e:
                print(f"获取配置的截图目录失败: {str(e)}")

            # 如果找到了配置的截图目录，尝试在该目录中查找截图
            if config_screenshots_dir:
                screenshot_filename = os.path.basename(screenshot_path)
                new_screenshot_path = os.path.join(config_screenshots_dir, screenshot_filename)

                if os.path.exists(new_screenshot_path):
                    screenshot_path = new_screenshot_path
                    self.log(f"在配置的截图目录中找到截图: {new_screenshot_path}")
                else:
                    # 尝试在其他可能的目录中查找
                    possible_dirs = [
                        "D:/网易号全自动/截图",  # 默认截图目录
                        "D:\\网易号全自动\\截图",  # 使用反斜杠的路径格式
                        "C:/网易号全自动/截图",
                        os.path.join(self.config_manager.base_dir if hasattr(self, 'config_manager') and self.config_manager and hasattr(self.config_manager, 'base_dir') else os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "screenshots")
                    ]

                    found = False
                    for dir_path in possible_dirs:
                        if dir_path == config_screenshots_dir:
                            continue  # 跳过已经检查过的目录

                        test_path = os.path.join(dir_path, screenshot_filename)
                        if os.path.exists(test_path):
                            screenshot_path = test_path
                            self.log(f"在备用目录中找到截图: {test_path}")
                            found = True
                            break

                    if not found:
                        messagebox.showwarning("警告", f"找不到截图文件:\n{screenshot_path}\n\n已尝试在多个目录中查找，但未找到。")
                        return
            else:
                messagebox.showwarning("警告", f"找不到截图文件:\n{screenshot_path}\n\n未配置截图目录，无法查找。")
                return

        # 打开截图
        self.show_screenshot(screenshot_path, f"{account} - {video}")

    def show_screenshot(self, screenshot_path, title):
        """显示截图 - 支持缩放和拖拽"""
        try:
            # 创建新窗口
            screenshot_window = tk.Toplevel(self.window)
            screenshot_window.title(f"截图查看 - {title}")
            screenshot_window.geometry("1024x768")
            screenshot_window.minsize(800, 600)

            # 加载原始图片
            try:
                # 使用前面已导入的PIL库
                original_image = Image.open(screenshot_path)
            except NameError:
                # 如果PIL库未成功导入，显示错误消息
                messagebox.showerror("错误", "无法显示图片: PIL库未安装或导入失败")
                self.log("无法显示图片: PIL库未安装或导入失败")
                return

            # 创建图片查看器类
            viewer = ImageViewer(screenshot_window, original_image, screenshot_path, self.log)

        except Exception as e:
            messagebox.showerror("错误", f"显示截图失败:\n{str(e)}")
            self.log(f"显示截图失败: {str(e)}")

    def export_draft_details(self):
        """导出存稿详情数据到CSV文件"""
        from tkinter import filedialog

        # 获取当前时间作为文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"netease_draft_details_{timestamp}.csv"

        # 打开文件对话框
        file_path = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("CSV文件", "*.csv")],
            initialfile=filename
        )

        if not file_path:
            return

        try:
            # 写入CSV文件
            with open(file_path, 'w', encoding='utf-8-sig', newline='') as f:
                writer = csv.writer(f)

                # 写入表头
                writer.writerow(["账号", "时间", "视频", "状态", "存稿成功", "原因", "截图"])

                # 写入数据
                for account, details in self.draft_details.items():
                    for detail in details:
                        # 获取该记录的存稿成功数
                        success_count = detail.get("存稿成功", 0)
                        # 如果记录中没有存稿成功数，则使用默认值0
                        if not success_count:
                            success_count = 0

                        writer.writerow([
                            account,
                            detail.get("时间", ""),
                            detail.get("视频", ""),
                            detail.get("状态", ""),
                            success_count,  # 添加存稿成功数量
                            detail.get("原因", ""),
                            detail.get("截图", "")
                        ])

            messagebox.showinfo("导出成功", f"存稿详情数据已导出到:\n{file_path}")
            self.log(f"存稿详情数据已导出到: {file_path}")
        except Exception as e:
            messagebox.showerror("导出失败", f"导出数据失败:\n{str(e)}")
            self.log(f"导出存稿详情数据失败: {str(e)}")

    def create_seven_day_income_window(self, parent_window):
        """创建七天收益历史窗口"""
        # 保存当前选中的账号，用于存稿详情按钮
        self.current_income_account = None

        # 创建主框架
        main_frame = ttk.Frame(parent_window, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 添加标题和状态栏
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(side=tk.TOP, fill=tk.X, pady=(0, 10))
        ttk.Label(title_frame, text="七天收益历史", font=("Arial", 12, "bold")).pack(side=tk.LEFT)

        # 添加账号数量标签
        self.income_status_label = ttk.Label(title_frame, text="", font=("Arial", 10))
        self.income_status_label.pack(side=tk.LEFT, padx=20)

        # 添加存稿详情按钮
        ttk.Button(title_frame, text="查看存稿详情", command=lambda: self.show_draft_details(self.current_income_account)).pack(side=tk.RIGHT, padx=5)

        # 创建表格框架
        table_frame = ttk.Frame(main_frame)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建表格 - 先创建一个空表格，列将在加载数据时动态生成
        self.seven_day_tree = ttk.Treeview(table_frame, show="headings", height=20)

        # 删除滚动条，只保留鼠标滚轮滚动

        # 布局
        self.seven_day_tree.pack(fill=tk.BOTH, expand=True)

        # 绑定鼠标滚轮事件以支持滚动
        def _on_mousewheel(event):
            self.seven_day_tree.yview_scroll(int(-1*(event.delta/120)), "units")

        def _on_horizontal_mousewheel(event):
            self.seven_day_tree.xview_scroll(int(-1*(event.delta/120)), "units")

        def _bind_mousewheel(event):
            self.seven_day_tree.bind_all("<MouseWheel>", _on_mousewheel)
            # 绑定Shift+滚轮进行水平滚动
            self.seven_day_tree.bind_all("<Shift-MouseWheel>", _on_horizontal_mousewheel)

        def _unbind_mousewheel(event):
            self.seven_day_tree.unbind_all("<MouseWheel>")
            self.seven_day_tree.unbind_all("<Shift-MouseWheel>")

        self.seven_day_tree.bind('<Enter>', _bind_mousewheel)
        self.seven_day_tree.bind('<Leave>', _unbind_mousewheel)

        # 添加底部按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(button_frame, text="刷新数据", command=self.load_seven_day_income_data).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="关闭", command=parent_window.destroy).pack(side=tk.RIGHT, padx=5)

    def load_data(self):
        """
        加载账号数据

        【作废计划 目前仍在使用 但不更新】
        注意：此方法为账号数据加载的实现，虽然标记为作废计划，但目前仍在使用中。
        该方法不再进行功能更新和维护，仅保持现有数据加载功能的稳定运行。
        """
        # 从父窗口重新获取最新的账号数据
        self.refresh_accounts_data()
        # 加载数据表格
        self.load_all_data()

    def refresh_accounts_data(self):
        """从父窗口刷新账号数据"""
        try:
            # 如果父窗口有account_data属性，从中获取最新数据
            if hasattr(self.parent, 'account_data') and self.parent.account_data:
                # 获取现有账号列表
                existing_accounts = None
                if hasattr(self.parent, 'accounts') and self.parent.accounts:
                    existing_accounts = set(self.parent.accounts)
                elif hasattr(self.parent, 'account_manager') and self.parent.account_manager:
                    existing_accounts = set(self.parent.account_manager.accounts)

                # 获取最新的账号数据
                latest_data = self.parent.account_data.get_all_accounts_data(existing_accounts)
                if latest_data:
                    self.accounts_data = latest_data
                    self.log(f"✅ 已从父窗口刷新账号数据，共 {len(latest_data)} 个账号")
                else:
                    self.log("⚠️ 父窗口中没有账号数据")
            # 如果父窗口有accounts_data属性，直接使用
            elif hasattr(self.parent, 'accounts_data') and self.parent.accounts_data:
                self.accounts_data = self.parent.accounts_data
                self.log("✅ 已从父窗口刷新账号数据")
            else:
                self.log("⚠️ 无法从父窗口获取最新数据，使用当前数据")
        except Exception as e:
            self.log(f"❌ 刷新账号数据时发生错误: {str(e)}")

    # 重复的 load_data_table 方法已删除，使用 load_all_data 方法



    def load_seven_day_income_data(self, filter_account=None):
        """加载七天收益历史数据

        Args:
            filter_account: 可选，指定要显示的账号。如果为None，则显示所有账号。
        """
        # 刷新账号数据以获取最新数据
        self.refresh_accounts_data()

        # 清空现有数据
        for item in self.seven_day_tree.get_children():
            self.seven_day_tree.delete(item)

        # 重新配置列
        self.seven_day_tree["columns"] = ()

        # 获取所有账号
        accounts = []
        account_income_data = {}

        # 收集所有账号
        for data in self.accounts_data:
            account = data.get("账号", "")
            if not account or account == "总计":
                continue

            # 如果指定了过滤账号，只处理该账号
            if filter_account and account != filter_account:
                continue

            accounts.append(account)
            account_income_data[account] = {}

            # 收集七日收益数据 - 修复头条平台支持
            if "七日收益" in data and isinstance(data["七日收益"], list):
                # 头条平台格式：七日收益是包含字典的列表
                for day_data in data["七日收益"]:
                    if isinstance(day_data, dict) and "日期" in day_data and "收益" in day_data:
                        try:
                            date = day_data["日期"]
                            income_value = float(day_data["收益"])
                            account_income_data[account][date] = income_value
                        except:
                            pass
            elif "七日收益数据" in data and isinstance(data["七日收益数据"], dict) and data["七日收益数据"]:
                # 使用新的七日收益数据格式（从网站直接获取的七天数据）
                for date, income in data["七日收益数据"].items():
                    try:
                        # 使用parse_income函数解析收益值
                        from 网易号存稿.common.utils import parse_income
                        income_value = parse_income(income)
                        account_income_data[account][date] = income_value
                    except Exception as e:
                        # 忽略解析错误，使用0作为默认值
                        account_income_data[account][date] = 0

            # 如果没有七日收益数据，尝试使用昨日收益
            elif "昨日收益" in data:
                try:
                    yesterday = datetime.now() - timedelta(days=1)
                    yesterday_str = yesterday.strftime("%Y-%m-%d")

                    income_value = self.clean_numeric_value(data.get("昨日收益", "0"))
                    account_income_data[account][yesterday_str] = income_value
                except:
                    pass

        # 如果没有账号数据，显示提示信息
        if not accounts:
            # 配置一个简单的列
            self.seven_day_tree["columns"] = ("信息",)
            self.seven_day_tree.heading("信息", text="信息")
            self.seven_day_tree.column("信息", width=400, anchor=tk.CENTER)

            # 添加提示信息
            self.seven_day_tree.insert("", "end", values=("没有可用的账号数据",))

            # 更新状态标签
            self.income_status_label.config(text="账号数量: 0")
            return

        # 配置列 - 只显示账号和七天总收益
        columns = ["账号", "七天总收益"]
        self.seven_day_tree["columns"] = columns

        # 设置列标题和宽度
        self.seven_day_tree.heading("账号", text="账号")
        self.seven_day_tree.column("账号", width=200, anchor=tk.W)

        # 设置七天总收益列
        self.seven_day_tree.heading("七天总收益", text="七天总收益")
        self.seven_day_tree.column("七天总收益", width=150, anchor=tk.CENTER)

        # 按账号排序
        accounts.sort()

        # 生成最近七天的日期列表（用于计算总收益）
        today = datetime.now()
        date_list = []
        for i in range(0, 7):  # 从今天开始，往前推7天
            day = today - timedelta(days=i)
            date_list.append(day.strftime("%Y-%m-%d"))

        # 添加每个账号的数据行
        for account in accounts:
            # 计算该账号七天总收益
            account_total = 0
            for date in date_list:
                income = account_income_data.get(account, {}).get(date, 0)
                account_total += income

            # 插入行
            self.seven_day_tree.insert("", "end", values=(account, f"{account_total:.2f}"))

        # 计算七天总收益
        grand_total = 0
        for account in accounts:
            for date in date_list:
                income = account_income_data.get(account, {}).get(date, 0)
                grand_total += income

        # 添加总计行
        total_row = ["总计", f"{grand_total:.2f}"]

        # 添加总计行
        self.seven_day_tree.insert("", "end", values=tuple(total_row), tags=("total",))

        # 设置总计行样式
        self.seven_day_tree.tag_configure("total", background="#E0E0E0", font=("Arial", 10, "bold"))

        # 更新状态标签显示账号数量
        account_count = len(accounts)
        self.income_status_label.config(text=f"账号数量: {account_count}")



    def load_all_data(self):
        """
        加载所有数据（搜索功能已移除）

        【作废计划 目前仍在使用 但不更新】
        注意：此方法为数据表格加载的核心实现，虽然标记为作废计划，但目前仍在使用中。
        该方法不再进行功能更新和维护，仅保持现有数据显示功能的稳定运行。
        """
        # 清空现有数据
        for item in self.data_tree.get_children():
            self.data_tree.delete(item)

        # 确保显示所有已加载的账号，包括没有数据的账号
        if hasattr(self, 'account_manager') and self.account_manager:
            # 获取所有已加载的账号
            all_accounts = set(self.account_manager.load_accounts())

            # 创建账号数据字典
            accounts_data_dict = {}
            for data in self.accounts_data:
                accounts_data_dict[data.get("账号", "")] = data

            # 确保所有账号都有数据显示
            complete_accounts_data = []
            for account in all_accounts:
                if account in accounts_data_dict:
                    complete_accounts_data.append(accounts_data_dict[account])
                else:
                    # 创建默认数据
                    default_data = {
                        "账号": account,
                        "用户名": "",
                        "总收益": "0",
                        "累计收益": "0",
                        "昨日收益": "0",
                        "总播放": "0",
                        "昨日播放": "0",
                        "总粉丝": "0",
                        "昨日粉丝": "0",
                        "草稿箱": "0",
                        "可提现": "0",
                        "待提现": "0",
                        "状态": "未查询",
                        "更新时间": ""
                    }
                    complete_accounts_data.append(default_data)

            # 使用完整的账号数据
            self.accounts_data = complete_accounts_data

        # 初始化总计数据
        total_seven_day_income = 0
        total_income = 0
        total_yesterday_income = 0
        total_withdraw = 0
        total_pending_withdraw = 0
        total_plays = 0
        total_yesterday_plays = 0
        total_fans = 0
        total_yesterday_fans = 0

        # 账号数量
        matched_count = 0

        # 加载所有数据
        for data in self.accounts_data:
            matched_count += 1

            # 计算七日收益总和
            seven_day_income = 0

            # 修复七日收益数据处理 - 支持头条平台的七日收益格式
            if "七日收益" in data and isinstance(data["七日收益"], list):
                # 头条平台格式：七日收益是包含字典的列表
                for day_data in data["七日收益"]:
                    if isinstance(day_data, dict) and "收益" in day_data:
                        try:
                            income_value = float(day_data["收益"])
                            seven_day_income += income_value
                        except:
                            pass
            elif "七日收益数据" in data and isinstance(data["七日收益数据"], dict) and data["七日收益数据"]:
                # 使用新的七日收益数据格式（从网站直接获取的七天数据）
                for date, income in data["七日收益数据"].items():
                    try:
                        # 使用parse_income函数解析收益值
                        from 网易号存稿.common.utils import parse_income
                        income_value = parse_income(income)
                        seven_day_income += income_value
                    except Exception as e:
                        # 忽略解析错误
                        pass
            # 兼容旧的七日收益数据格式
            elif "七日收益数据" in data:
                for income in data["七日收益数据"].values():
                    income_value = float(income) if isinstance(income, (int, float, str)) else 0
                    seven_day_income += income_value

            # 解析数值数据，使用统一字段映射标准
            # 统一标准字段：总收益、待提现、草稿箱
            current_income = self.clean_numeric_value(data.get("总收益", "0"))
            yesterday_income = self.clean_numeric_value(data.get("昨日收益", "0"))
            withdraw = self.clean_numeric_value(data.get("总提现", "0"))
            pending_withdraw = self.clean_numeric_value(data.get("待提现", "0"))
            plays = self.clean_numeric_value(data.get("总播放", "0"))
            yesterday_plays = self.clean_numeric_value(data.get("昨日播放", "0"))
            fans = self.clean_numeric_value(data.get("总粉丝", "0"))
            yesterday_fans = self.clean_numeric_value(data.get("昨日粉丝", "0"))

            # 累加总计
            total_seven_day_income += seven_day_income
            total_income += current_income
            total_yesterday_income += yesterday_income
            total_withdraw += withdraw
            total_pending_withdraw += pending_withdraw
            total_plays += plays
            total_yesterday_plays += yesterday_plays
            total_fans += fans
            total_yesterday_fans += yesterday_fans

            # 导入格式化函数
            from 网易号存稿.common.utils import format_number

            # 获取最近提现日 - 支持多种字段名映射
            recent_withdraw_date = data.get("最近提现日期", data.get("最近提现日", data.get("最近提现时间", "")))

            # 插入账号数据行，使用format_number格式化数字
            item_id = self.data_tree.insert("", "end", values=(
                    data.get("账号", ""),
                    data.get("用户名", ""),
                    f"{seven_day_income:.2f}",
                    "查看",  # 七日详情按钮文本
                    format_number(current_income),
                    format_number(yesterday_income),
                    format_number(withdraw),
                    format_number(pending_withdraw),
                    recent_withdraw_date,  # 最近提现日
                    format_number(plays),
                    format_number(yesterday_plays),
                    format_number(fans),
                    format_number(yesterday_fans),
                    data.get("草稿箱", "0"),  # 统一标准字段
                    data.get("状态", "未查询"),
                    data.get("更新时间", "")
                ))

            # 为七日详情按钮添加标签
            self.data_tree.item(item_id, tags=())

        # 如果有匹配的账号，添加总计行
        if matched_count > 0:
            # 导入格式化函数
            from 网易号存稿.common.utils import format_number

            # 添加总计行，使用format_number格式化数字
            self.data_tree.insert("", "end", values=(
                "总计",
                "",
                f"{total_seven_day_income:.2f}",
                "",  # 七日详情列留空
                format_number(total_income),
                format_number(total_yesterday_income),
                format_number(total_withdraw),
                format_number(total_pending_withdraw),
                "",  # 最近提现日列留空
                format_number(total_plays),
                format_number(total_yesterday_plays),
                format_number(total_fans),
                format_number(total_yesterday_fans),
                "",  # 草稿箱列留空
                "",
                ""
            ), tags=("total",))

            # 设置总计行样式
            self.data_tree.tag_configure("total", background="#E0E0E0", font=("Arial", 10, "bold"))

        # 更新账号数量标签
        self.account_count_label.config(text=f"账号数量: {matched_count}")

    def sort_data_by_column(self, column):
        """数据查看器按列排序"""
        # 切换排序顺序
        if column not in self.data_sort_reverse:
            self.data_sort_reverse[column] = False
        else:
            self.data_sort_reverse[column] = not self.data_sort_reverse[column]

        reverse = self.data_sort_reverse[column]

        # 记录最后排序的列和方向
        self.data_last_sort_column = column
        self.data_last_sort_reverse = reverse

        # 定义数值列，用于数值排序 - 修复列名映射
        numeric_columns = ["七日收益", "总收益", "昨日收益", "总提现", "待提现", "总播放", "昨日播放", "总粉丝", "昨日粉丝", "草稿箱"]

        # 获取所有数据，分离总计行和普通数据行
        all_items = self.data_tree.get_children('')
        data_items = []
        total_item = None

        for item in all_items:
            values = self.data_tree.item(item, "values")
            if len(values) > 0 and values[0] == "总计":
                total_item = item
            else:
                data_items.append((self.data_tree.set(item, column), item))

        # 排序普通数据行
        if column in numeric_columns:
            def numeric_sort_key(x):
                value = x[0]
                if value == "" or value == "查看":  # 处理空值和按钮文本
                    return float('-inf') if reverse else float('inf')
                try:
                    # 使用clean_numeric_value解析数值
                    return self.clean_numeric_value(value)
                except:
                    return 0
            data_items.sort(key=numeric_sort_key, reverse=reverse)
        else:
            # 文本排序，处理空值
            def text_sort_key(x):
                value = x[0]
                if value == "" or value == "查看":
                    return "zzz" if not reverse else ""  # 空值排在最后
                return str(value)
            data_items.sort(key=text_sort_key, reverse=reverse)

        # 重新排列：先排列普通数据行，总计行始终在最后
        for index, (_, item) in enumerate(data_items):
            self.data_tree.move(item, '', index)

        # 总计行移到最后
        if total_item:
            self.data_tree.move(total_item, '', len(data_items))

        # 更新列标题显示排序方向
        columns = ["账号", "用户名", "七日收益", "七日详情", "总收益", "昨日收益", "总提现", "待提现", "最近提现日", "总播放", "昨日播放", "总粉丝", "昨日粉丝", "草稿箱", "状态", "更新时间"]
        for col in columns:
            if col == column:
                direction = " ↓" if reverse else " ↑"
                self.data_tree.heading(col, text=f"{col}{direction}")
            else:
                self.data_tree.heading(col, text=col)

        # 保存排序配置
        self.save_sort_config()

    # 注意：右键菜单功能已移植到主界面账号列表，以下方法已删除：
    # - show_context_menu
    # - copy_account_name
    # - copy_username

    def delete_selected_account(self):
        """删除选中的账号"""
        # 获取选中的项目
        selected_items = self.data_tree.selection()
        if not selected_items:
            messagebox.showwarning("警告", "请先选择要删除的账号")
            return

        # 获取选中的账号
        item = selected_items[0]
        values = self.data_tree.item(item, "values")
        if len(values) < 1:
            return

        account = values[0]  # 账号列的索引为0

        if account == "总计":
            messagebox.showwarning("警告", "不能删除总计行")
            return

        # 确认删除
        if not messagebox.askyesno("确认删除", f"确定要删除账号 {account} 吗？\n\n注意：此操作将删除账号的Cookie文件和所有相关数据，且无法恢复！"):
            return

        try:
            # 使用账号管理器来删除账号
            if hasattr(self, 'account_manager') and self.account_manager:
                if self.account_manager.delete_account(account):
                    self.log(f"账号 {account} 删除成功")

                    # 从当前数据中移除该账号
                    self.accounts_data = [data for data in self.accounts_data if data.get("账号") != account]

                    # 刷新界面
                    self.load_data()

                    # 更新父窗口的账号列表（如果父窗口有load_accounts方法）
                    if hasattr(self.parent, 'load_accounts'):
                        self.parent.load_accounts()

                    messagebox.showinfo("删除成功", f"账号 {account} 已成功删除")
                else:
                    self.log(f"账号 {account} 删除失败")
                    messagebox.showerror("删除失败", f"账号 {account} 删除失败")
            else:
                messagebox.showerror("错误", "无法访问账号管理器，删除失败\n\n请确保从主界面打开数据查看器")

        except Exception as e:
            self.log(f"删除账号 {account} 时发生错误: {str(e)}")
            messagebox.showerror("删除失败", f"删除账号时发生错误:\n{str(e)}")

    def delete_selected_accounts(self):
        """删除选中的账号（支持多选）"""
        # 获取选中的项目
        selected_items = self.data_tree.selection()
        if not selected_items:
            messagebox.showwarning("警告", "请先选择要删除的账号")
            return

        # 获取选中的账号列表
        accounts_to_delete = []
        for item in selected_items:
            values = self.data_tree.item(item, "values")
            if len(values) >= 1:
                account = values[0]  # 账号列的索引为0
                if account != "总计":
                    accounts_to_delete.append(account)

        if not accounts_to_delete:
            messagebox.showwarning("警告", "没有有效的账号可以删除")
            return

        # 确认删除
        if len(accounts_to_delete) == 1:
            confirm_msg = f"确定要删除账号 {accounts_to_delete[0]} 吗？\n\n注意：此操作将删除账号的Cookie文件和所有相关数据，且无法恢复！"
        else:
            account_list = "\n".join([f"• {acc}" for acc in accounts_to_delete])
            confirm_msg = f"确定要删除以下 {len(accounts_to_delete)} 个账号吗？\n\n{account_list}\n\n注意：此操作将删除账号的Cookie文件和所有相关数据，且无法恢复！"

        if not messagebox.askyesno("确认删除", confirm_msg):
            return

        # 执行删除操作
        success_count = 0
        failed_accounts = []

        try:
            # 使用账号管理器来删除账号
            if hasattr(self, 'account_manager') and self.account_manager:
                for account in accounts_to_delete:
                    try:
                        if self.account_manager.delete_account(account):
                            self.log(f"账号 {account} 删除成功")
                            success_count += 1

                            # 从当前数据中移除该账号
                            self.accounts_data = [data for data in self.accounts_data if data.get("账号") != account]
                        else:
                            self.log(f"账号 {account} 删除失败")
                            failed_accounts.append(account)
                    except Exception as e:
                        self.log(f"删除账号 {account} 时发生错误: {str(e)}")
                        failed_accounts.append(account)

                # 刷新界面
                self.load_data()

                # 更新父窗口的账号列表（如果父窗口有load_accounts方法）
                if hasattr(self.parent, 'load_accounts'):
                    self.parent.load_accounts()

                # 显示结果
                if success_count > 0 and not failed_accounts:
                    messagebox.showinfo("删除成功", f"成功删除 {success_count} 个账号")
                elif success_count > 0 and failed_accounts:
                    failed_list = "\n".join([f"• {acc}" for acc in failed_accounts])
                    messagebox.showwarning("部分删除成功",
                        f"成功删除 {success_count} 个账号\n\n"
                        f"删除失败的账号：\n{failed_list}")
                else:
                    failed_list = "\n".join([f"• {acc}" for acc in failed_accounts])
                    messagebox.showerror("删除失败", f"所有账号删除失败：\n{failed_list}")
            else:
                messagebox.showerror("错误", "无法访问账号管理器，删除失败\n\n请确保从主界面打开数据查看器")

        except Exception as e:
            self.log(f"批量删除账号时发生错误: {str(e)}")
            messagebox.showerror("删除失败", f"批量删除账号时发生错误:\n{str(e)}")

    def batch_delete_accounts(self):
        """批量删除账号（通过对话框选择）"""
        # 获取所有可删除的账号
        available_accounts = []
        for data in self.accounts_data:
            account = data.get("账号", "")
            if account and account != "总计":
                available_accounts.append(account)

        if not available_accounts:
            messagebox.showwarning("警告", "没有可删除的账号")
            return

        # 创建批量删除对话框
        self.show_batch_delete_dialog(available_accounts)

    def show_batch_delete_dialog(self, accounts):
        """
        显示批量删除对话框

        【作废计划 目前仍在使用 但不更新】
        注意：此方法为批量删除账号对话框的实现，虽然标记为作废计划，但目前仍在使用中。
        该方法不再进行功能更新和维护，仅保持现有删除功能的稳定运行。
        """
        # 创建对话框窗口
        dialog = tk.Toplevel(self.window)
        dialog.title("批量删除账号")
        dialog.geometry("500x600")
        dialog.minsize(400, 500)
        dialog.transient(self.window)
        dialog.grab_set()

        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        # 创建主框架
        main_frame = ttk.Frame(dialog, padding=15)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(
            main_frame,
            text="选择要删除的账号",
            font=("微软雅黑", 14, "bold"),
            foreground="#DC3545"
        )
        title_label.pack(pady=(0, 15))

        # 警告信息
        warning_label = ttk.Label(
            main_frame,
            text="⚠️ 警告：删除操作将永久删除账号的Cookie文件和所有相关数据，且无法恢复！",
            font=("微软雅黑", 10),
            foreground="#DC3545",
            wraplength=450
        )
        warning_label.pack(pady=(0, 15))

        # 操作按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(0, 10))

        # 全选按钮
        def select_all():
            for i in range(listbox.size()):
                listbox.selection_set(i)

        # 全不选按钮
        def select_none():
            listbox.selection_clear(0, tk.END)

        # 反选按钮
        def select_inverse():
            current_selection = listbox.curselection()
            listbox.selection_clear(0, tk.END)
            for i in range(listbox.size()):
                if i not in current_selection:
                    listbox.selection_set(i)

        ttk.Button(button_frame, text="全选", command=select_all).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="全不选", command=select_none).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="反选", command=select_inverse).pack(side=tk.LEFT, padx=5)

        # 账号列表框架
        list_frame = ttk.Frame(main_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # 创建列表框和滚动条
        listbox_frame = ttk.Frame(list_frame)
        listbox_frame.pack(fill=tk.BOTH, expand=True)

        listbox = tk.Listbox(listbox_frame, selectmode=tk.MULTIPLE, font=("微软雅黑", 10))
        scrollbar = ttk.Scrollbar(listbox_frame, orient=tk.VERTICAL, command=listbox.yview)
        listbox.configure(yscrollcommand=scrollbar.set)

        # 添加账号到列表
        for account in accounts:
            listbox.insert(tk.END, account)

        listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 底部按钮框架
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.pack(fill=tk.X)

        # 删除按钮
        def confirm_delete():
            selected_indices = listbox.curselection()
            if not selected_indices:
                messagebox.showwarning("警告", "请选择要删除的账号")
                return

            selected_accounts = [accounts[i] for i in selected_indices]

            # 关闭对话框
            dialog.destroy()

            # 执行删除（模拟选中这些账号）
            # 先清除当前选择
            self.data_tree.selection_remove(self.data_tree.selection())

            # 选中要删除的账号
            for item in self.data_tree.get_children():
                values = self.data_tree.item(item, "values")
                if len(values) >= 1 and values[0] in selected_accounts:
                    self.data_tree.selection_add(item)

            # 调用删除方法
            self.delete_selected_accounts()

        delete_btn = ttk.Button(
            bottom_frame,
            text="🗑️ 删除选中账号",
            command=confirm_delete
        )
        delete_btn.pack(side=tk.LEFT, padx=5)

        # 取消按钮
        ttk.Button(
            bottom_frame,
            text="取消",
            command=dialog.destroy
        ).pack(side=tk.RIGHT, padx=5)

    def copy_text_to_clipboard(self, text):
        """复制文本到剪贴板"""
        self.window.clipboard_clear()
        self.window.clipboard_append(text)
        self.log(f"已复制: {text}")

    def load_sort_config(self):
        """加载排序配置"""
        try:
            config_file = "config/data_viewer_sort.json"
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.data_sort_reverse = config.get("data_sort_reverse", {})
                    # 加载最后排序的列和方向
                    self.data_last_sort_column = config.get("last_sort_column", None)
                    self.data_last_sort_reverse = config.get("last_sort_reverse", False)
                    self.log("已加载数据查看器排序配置")
        except Exception as e:
            self.log(f"加载排序配置失败: {e}")
            self.data_sort_reverse = {}
            self.data_last_sort_column = None
            self.data_last_sort_reverse = False

    def save_sort_config(self):
        """保存排序配置"""
        try:
            config_dir = "config"
            if not os.path.exists(config_dir):
                os.makedirs(config_dir)

            config_file = "config/data_viewer_sort.json"
            config = {
                "data_sort_reverse": self.data_sort_reverse,
                "last_sort_column": getattr(self, 'data_last_sort_column', None),
                "last_sort_reverse": getattr(self, 'data_last_sort_reverse', False)
            }

            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.log(f"保存排序配置失败: {e}")

    def apply_saved_sort(self):
        """应用已保存的排序配置"""
        try:
            # 使用保存的最后排序列，如果没有则不应用排序
            if hasattr(self, 'data_last_sort_column') and self.data_last_sort_column:
                # 应用排序，但不改变排序状态（因为已经从配置中加载了）
                self._apply_sort_without_toggle(self.data_last_sort_column)
        except Exception as e:
            self.log(f"应用排序配置失败: {e}")

    def _apply_sort_without_toggle(self, column):
        """应用排序但不切换排序状态"""
        if column not in self.data_sort_reverse:
            return

        reverse = self.data_sort_reverse[column]

        # 定义数值列，用于数值排序 - 修复列名映射
        numeric_columns = ["七日收益", "总收益", "昨日收益", "总提现", "待提现", "总播放", "昨日播放", "总粉丝", "昨日粉丝", "草稿箱"]

        # 获取所有数据，分离总计行和普通数据行
        all_items = self.data_tree.get_children('')
        data_items = []
        total_item = None

        for item in all_items:
            values = self.data_tree.item(item, "values")
            if len(values) > 0 and values[0] == "总计":
                total_item = item
            else:
                data_items.append((self.data_tree.set(item, column), item))

        # 排序普通数据行
        if column in numeric_columns:
            def numeric_sort_key(x):
                value = x[0]
                if value == "" or value == "查看":  # 处理空值和按钮文本
                    return float('-inf') if reverse else float('inf')
                try:
                    # 使用clean_numeric_value解析数值
                    return self.clean_numeric_value(value)
                except:
                    return 0
            data_items.sort(key=numeric_sort_key, reverse=reverse)
        else:
            # 文本排序，处理空值
            def text_sort_key(x):
                value = x[0]
                if value == "" or value == "查看":
                    return "zzz" if not reverse else ""  # 空值排在最后
                return str(value)
            data_items.sort(key=text_sort_key, reverse=reverse)

        # 重新排列：先排列普通数据行，总计行始终在最后
        for index, (_, item) in enumerate(data_items):
            self.data_tree.move(item, '', index)

        # 总计行移到最后
        if total_item:
            self.data_tree.move(total_item, '', len(data_items))

        # 更新列标题显示排序方向
        columns = ["账号", "用户名", "七日收益", "七日详情", "总收益", "昨日收益", "总提现", "待提现", "最近提现日", "总播放", "昨日播放", "总粉丝", "昨日粉丝", "草稿箱", "状态", "更新时间"]
        for col in columns:
            if col == column:
                direction = " ↓" if reverse else " ↑"
                self.data_tree.heading(col, text=f"{col}{direction}")
            else:
                self.data_tree.heading(col, text=col)

    # 注意：query_selected_account_data 方法已删除，功能已移植到主界面账号列表

    # 注意：login_selected_account_from_menu 方法已删除，功能已移植到主界面账号列表

    def _login_account(self, account):
        """登录指定账号"""
        if self.current_platform not in ["toutiao", "netease", "dayu"]:
            messagebox.showwarning("警告", "当前平台不支持登录功能")
            return

        # 根据平台显示不同的确认信息
        if self.current_platform == "toutiao":
            platform_name = "头条"
            login_url = "https://mp.toutiao.com/auth/page/login"
        elif self.current_platform == "dayu":
            platform_name = "大鱼号"
            login_url = "https://mp.dayu.com/"
        else:  # netease
            platform_name = "网易号"
            login_url = "https://mp.163.com/"

        # 确认登录
        if not messagebox.askyesno("确认登录", f"确定要登录{platform_name}账号 {account} 吗？\n\n登录地址：{login_url}\n\n登录后浏览器将保持打开状态，您可以随时手动关闭。"):
            return

        self.log(f"正在登录{platform_name}账号: {account}")

        # 在后台线程中执行登录，避免阻塞UI
        import threading
        threading.Thread(target=self._login_account_thread, args=(account,), daemon=True).start()

    def _login_account_thread(self, account):
        """在后台线程中执行登录"""
        if self.current_platform == "toutiao":
            self._login_toutiao_account_thread(account)
        elif self.current_platform == "dayu":
            self._login_dayu_account_thread(account)
        elif self.current_platform == "netease":
            self._login_netease_account_thread(account)

    def _login_toutiao_account_thread(self, account):
        """在后台线程中执行头条账号登录"""
        # 导入必要的模块
        from 网易号存稿.platforms.toutiao.login import ToutiaoLogin

        try:
            # 获取账号目录和Cookie文件路径（支持多种目录结构）
            if not self.config_manager:
                self.window.after(0, lambda: messagebox.showerror("错误", "配置管理器未初始化"))
                return

            base_account_dir = self.config_manager.get("account_dir", platform=self.current_platform)
            if not base_account_dir:
                self.window.after(0, lambda: messagebox.showerror("错误", "未配置账号目录"))
                return

            # 只查找账号名.txt格式
            account_dir = base_account_dir
            cookie_path = os.path.join(base_account_dir, f"{account}.txt")
            if not os.path.exists(cookie_path):
                cookie_path = None

            # 如果仍然没有找到Cookie文件
            if not cookie_path:
                self.log(f"❌ 未找到头条账号 {account} 的Cookie文件")
                self.log(f"检查的路径: {os.path.join(base_account_dir, f'{account}.txt')}")
                self.window.after(0, lambda: messagebox.showerror("错误", f"未找到头条账号 {account} 的Cookie文件\n\n请检查账号目录: {base_account_dir}"))
                return

            self.log(f"找到头条账号 {account} 的Cookie文件: {cookie_path}")

            # 创建头条登录对象
            toutiao_login = ToutiaoLogin(self.log)

            # 使用Cookie登录
            self.log(f"正在使用Cookie登录头条账号: {account}")
            self.log(f"Cookie文件路径: {cookie_path}")

            # 先测试Cookie文件是否可以正常读取
            try:
                with open(cookie_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # 移除详细的cookie文件信息日志
            except Exception as e:
                self.log(f"读取Cookie文件时出错: {str(e)}")
                self.window.after(0, lambda: messagebox.showerror("错误", f"读取Cookie文件时出错: {str(e)}"))
                return

            success, driver = toutiao_login.login_with_cookies(cookie_path, headless=False)

            if success:
                self.log(f"✅ 头条账号 {account} 登录成功")

                # 在主线程中询问是否立即获取账号数据
                self.window.after(0, lambda: self._ask_for_data_query_after_login(account, driver))
            else:
                self.log(f"❌ 头条账号 {account} 登录失败")

                # 在主线程中询问是否尝试手机号登录
                self.window.after(0, lambda: self._ask_for_phone_login(account, toutiao_login, cookie_path))

        except Exception as e:
            self.log(f"❌ 头条账号登录过程中发生错误: {str(e)}")
            self.window.after(0, lambda: messagebox.showerror("错误", f"头条账号登录过程中发生错误: {str(e)}"))
            import traceback
            traceback.print_exc()

    def _login_netease_account_thread(self, account):
        """在后台线程中执行网易号账号登录"""
        # 导入必要的模块
        from 网易号存稿.account.login import AccountLogin
        from 网易号存稿.browser.driver import DriverManager

        try:
            # 获取账号目录和Cookie文件路径（支持多种目录结构）
            if not self.config_manager:
                self.log("❌ 配置管理器未初始化")
                self.window.after(0, lambda: messagebox.showerror("错误", "配置管理器未初始化"))
                return

            # 获取账号目录
            base_account_dir = self.config_manager.get("account_dir", "")
            if not base_account_dir or not os.path.exists(base_account_dir):
                self.log(f"❌ 账号目录不存在: {base_account_dir}")
                self.window.after(0, lambda: messagebox.showerror("错误", f"账号目录不存在: {base_account_dir}"))
                return

            # 只查找账号名.txt格式
            account_dir = base_account_dir
            cookie_path = os.path.join(base_account_dir, f"{account}.txt")
            if not os.path.exists(cookie_path):
                cookie_path = None

            if not cookie_path:
                self.log(f"❌ 找不到网易号账号 {account} 的Cookie文件")
                self.window.after(0, lambda: messagebox.showerror("错误", f"找不到网易号账号 {account} 的Cookie文件"))
                return

            self.log(f"找到网易号账号 {account} 的Cookie文件: {cookie_path}")

            # 创建驱动管理器
            driver_manager = DriverManager(self.log)

            # 创建网易号登录对象
            account_login = AccountLogin(driver_manager, self.log)

            # 使用Cookie登录
            self.log(f"正在使用Cookie登录网易号账号: {account}")
            self.log(f"Cookie文件路径: {cookie_path}")

            # 先测试Cookie文件是否可以正常读取
            try:
                with open(cookie_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # 移除详细的cookie文件信息日志
            except Exception as e:
                self.log(f"读取Cookie文件时出错: {str(e)}")
                self.window.after(0, lambda: messagebox.showerror("错误", f"读取Cookie文件时出错: {str(e)}"))
                return

            success, driver = account_login.login_with_cookies(cookie_path, headless=False, account=account)

            if success:
                self.log(f"✅ 网易号账号 {account} 登录成功")

                # 在主线程中询问是否立即获取账号数据
                self.window.after(0, lambda: self._ask_for_netease_data_query_after_login(account, driver))
            else:
                self.log(f"❌ 网易号账号 {account} 登录失败")

                # 在主线程中询问是否尝试手机号登录
                self.window.after(0, lambda: self._ask_for_netease_phone_login(account, account_login, cookie_path))

        except Exception as e:
            self.log(f"❌ 网易号账号登录过程中发生错误: {str(e)}")
            self.window.after(0, lambda: messagebox.showerror("错误", f"网易号账号登录过程中发生错误: {str(e)}"))
            import traceback
            traceback.print_exc()

    def _login_dayu_account_thread(self, account):
        """在后台线程中执行大鱼号账号登录"""
        # 导入必要的模块
        from 网易号存稿.platforms.dayu.login import DayuLogin

        try:
            # 获取账号目录和Cookie文件路径（支持多种目录结构）
            if not self.config_manager:
                self.window.after(0, lambda: messagebox.showerror("错误", "配置管理器未初始化"))
                return

            base_account_dir = self.config_manager.get("account_dir", platform=self.current_platform)
            if not base_account_dir:
                self.window.after(0, lambda: messagebox.showerror("错误", "未配置账号目录"))
                return

            # 只查找账号名.txt格式
            account_dir = base_account_dir
            cookie_path = os.path.join(base_account_dir, f"{account}.txt")
            if not os.path.exists(cookie_path):
                cookie_path = None

            if not cookie_path:
                self.log(f"❌ 未找到大鱼号账号 {account} 的Cookie文件")
                self.window.after(0, lambda: messagebox.showerror("错误", f"未找到大鱼号账号 {account} 的Cookie文件"))
                return

            self.log(f"找到大鱼号账号 {account} 的Cookie文件: {cookie_path}")

            # 创建大鱼号登录对象
            dayu_login = DayuLogin(self.log)

            # 使用Cookie登录
            self.log(f"正在使用Cookie登录大鱼号账号: {account}")
            self.log(f"Cookie文件路径: {cookie_path}")

            success, driver = dayu_login.login_with_cookies(cookie_path, headless=False)

            if success:
                self.log(f"✅ 大鱼号账号 {account} 登录成功")

                # 在主线程中询问是否立即获取账号数据
                self.window.after(0, lambda: self._ask_for_dayu_data_query_after_login(account, driver))
            else:
                self.log(f"❌ 大鱼号账号 {account} 登录失败")

                # 在主线程中询问是否尝试手机号登录
                self.window.after(0, lambda: self._ask_for_dayu_phone_login(account, dayu_login, cookie_path))

        except Exception as e:
            self.log(f"❌ 大鱼号账号登录过程中发生错误: {str(e)}")
            self.window.after(0, lambda: messagebox.showerror("错误", f"大鱼号账号登录过程中发生错误: {str(e)}"))
            import traceback
            traceback.print_exc()

    def _ask_for_netease_data_query_after_login(self, account, driver):
        """询问是否在网易号登录后立即获取账号数据"""
        if messagebox.askyesno("数据获取", f"网易号账号 {account} 登录成功！\n\n是否立即获取账号数据？"):
            # 在后台线程中获取数据
            import threading
            threading.Thread(target=self._get_netease_account_data_thread, args=(account, driver), daemon=True).start()
        else:
            self.log(f"浏览器将保持打开状态，您可以随时手动关闭")
            messagebox.showinfo("提示", "浏览器将保持打开状态，您可以随时手动关闭。")

    def _ask_for_netease_phone_login(self, account, account_login, cookie_path):
        """询问是否尝试网易号手机号登录"""
        if messagebox.askyesno("登录失败", f"网易号账号 {account} Cookie登录失败，可能Cookie已过期。\n\n是否尝试手机号登录？"):
            # 在后台线程中执行手机号登录
            import threading
            threading.Thread(target=self._netease_phone_login_thread, args=(account, account_login, cookie_path), daemon=True).start()
        else:
            self.log(f"已取消网易号账号 {account} 的手机号登录")

    def _ask_for_dayu_data_query_after_login(self, account, driver):
        """询问是否在大鱼号登录后立即获取账号数据"""
        if messagebox.askyesno("数据获取", f"大鱼号账号 {account} 登录成功！\n\n是否立即获取账号数据？"):
            self.log(f"开始获取大鱼号账号 {account} 的数据...")

            # 在新线程中获取数据，避免阻塞UI
            import threading
            threading.Thread(target=self._get_dayu_account_data_thread,
                           args=(account, driver), daemon=True).start()
        else:
            self.log(f"跳过数据获取，浏览器将保持打开状态")
            messagebox.showinfo("登录成功", f"大鱼号账号 {account} 登录成功\n\n浏览器将保持打开状态，您可以随时手动关闭。")

    def _ask_for_dayu_phone_login(self, account, dayu_login, cookie_path):
        """询问是否尝试大鱼号手机号登录"""
        if not messagebox.askyesno("登录失败", "Cookie登录失败，是否尝试使用手机号登录？"):
            messagebox.showerror("登录失败", f"大鱼号账号 {account} 登录失败")
            return

        self.log(f"正在尝试使用手机号登录大鱼号账号: {account}")

        # 显示提示对话框
        messagebox.showinfo("操作提示", "请在打开的浏览器中完成大鱼号账号登录操作。\n\n登录地址：https://mp.dayu.com/\n\n浏览器将保持打开状态，您可以随时手动关闭。")

        # 在后台线程中执行手机号登录
        import threading
        threading.Thread(target=self._dayu_phone_login_thread, args=(account, dayu_login, cookie_path), daemon=True).start()

    def _dayu_phone_login_thread(self, account, dayu_login, cookie_path):
        """在后台线程中执行大鱼号手机号登录"""
        try:
            # 使用手机号登录
            login_success, driver, cookies = dayu_login.login_with_phone(False)

            if login_success and cookies:
                self.log(f"✅ 大鱼号账号 {account} 手机号登录成功")

                # 在主线程中询问是否更新Cookie
                self.window.after(0, lambda: self._ask_for_dayu_cookie_update(account, cookies, cookie_path, driver))
            else:
                self.log(f"❌ 大鱼号账号 {account} 手机号登录失败")
                self.window.after(0, lambda: messagebox.showerror("登录失败", f"大鱼号账号 {account} 手机号登录失败"))

                # 如果浏览器仍然活动，提示用户可以手动关闭
                if driver:
                    self.log(f"浏览器仍然处于打开状态，您可以随时手动关闭")
                    self.window.after(0, lambda: messagebox.showinfo("提示", "浏览器仍然处于打开状态，您可以随时手动关闭。"))

        except Exception as e:
            self.log(f"❌ 大鱼号账号 {account} 手机号登录过程中发生错误: {str(e)}")
            self.window.after(0, lambda: messagebox.showerror("错误", f"大鱼号账号 {account} 手机号登录过程中发生错误: {str(e)}"))
            import traceback
            traceback.print_exc()

    def _ask_for_dayu_cookie_update(self, account, cookies, cookie_path, driver):
        """询问是否更新大鱼号Cookie"""
        if messagebox.askyesno("更新Cookie", f"大鱼号账号 {account} 手机号登录成功！\n\n是否更新保存的Cookie？"):
            try:
                # 保存新的Cookie - 统一保存为账号名.txt格式
                import json
                # 获取账号目录
                base_account_dir = os.path.dirname(cookie_path) if os.path.dirname(cookie_path) else self.config_manager.get("account_dir", platform=self.current_platform)
                save_path = os.path.join(base_account_dir, f"{account}.txt")
                with open(save_path, 'w', encoding='utf-8') as f:
                    json.dump(cookies, f, ensure_ascii=False, indent=4)

                self.log(f"✅ 大鱼号账号 {account} Cookie已更新")
                messagebox.showinfo("更新成功", f"大鱼号账号 {account} Cookie已更新")

                # 询问是否立即获取数据
                self._ask_for_dayu_data_query_after_login(account, driver)

            except Exception as e:
                self.log(f"❌ 更新大鱼号账号 {account} Cookie失败: {str(e)}")
                messagebox.showerror("更新失败", f"更新大鱼号账号 {account} Cookie失败: {str(e)}")
        else:
            self.log(f"已取消大鱼号账号 {account} 的Cookie更新")
            messagebox.showinfo("登录成功", f"大鱼号账号 {account} 登录成功\n\n浏览器将保持打开状态，您可以随时手动关闭。")

    def _get_dayu_account_data_thread(self, account, driver):
        """
        在后台线程中获取大鱼号账号数据

        【作废计划 目前仍在使用 但不更新】
        注意：此方法为大鱼号数据获取的实现，虽然标记为作废计划，但目前仍在使用中。
        该方法不再进行功能更新和维护，仅保持现有数据获取功能的稳定运行。
        """
        try:
            self.log(f"正在获取大鱼号账号 {account} 的数据...")

            # 初始化结果
            result = {
                "账号": account,
                "用户名": "",
                "总收益": 0.0,
                "昨日收益": 0.0,
                "总播放": 0.0,
                "昨日播放": 0.0,
                "总粉丝": 0.0,
                "昨日粉丝": 0.0,
                "草稿箱数量": 0,
                "状态": "失败",
                "更新时间": time.strftime("%Y-%m-%d %H:%M:%S")
            }

            try:
                # 简化的数据获取：只是标记登录成功，实际数据获取可以通过主界面的数据查询功能进行
                self.log(f"大鱼号账号 {account} 登录成功，可以通过主界面的数据查询功能获取详细数据")

                result["状态"] = "登录成功"
                result["用户名"] = f"{account}(已登录)"

                # 更新当前数据查看器的数据
                self._update_local_account_data(account, result)

                # 显示成功消息
                self.window.after(0, lambda: messagebox.showinfo("登录成功",
                    f"大鱼号账号 {account} 登录成功！\n\n"
                    f"浏览器将保持打开状态，您可以：\n"
                    f"1. 手动查看账号数据\n"
                    f"2. 使用主界面的数据查询功能获取详细数据\n"
                    f"3. 随时手动关闭浏览器"))

            except Exception as e:
                self.log(f"❌ 获取大鱼号账号 {account} 数据时发生错误: {str(e)}")
                result["错误信息"] = str(e)

                # 显示错误消息
                self.window.after(0, lambda: messagebox.showerror("数据获取失败",
                    f"获取大鱼号账号 {account} 数据时发生错误:\n\n{str(e)}\n\n"
                    f"浏览器将保持打开状态，您可以手动查看或关闭。"))

        except Exception as e:
            self.log(f"❌ 大鱼号账号数据获取过程中发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def _get_netease_account_data_thread(self, account, driver):
        """在后台线程中获取网易号账号数据"""
        try:
            self.log(f"正在获取网易号账号 {account} 的数据...")

            # 初始化结果
            result = {
                "账号": account,
                "用户名": "",
                "总收益": 0.0,
                "昨日收益": 0.0,
                "总播放": 0.0,
                "昨日播放": 0.0,
                "总粉丝": 0.0,
                "昨日粉丝": 0.0,
                "草稿箱数量": 0,
                "状态": "失败",
                "更新时间": time.strftime("%Y-%m-%d %H:%M:%S")
            }

            try:
                # 简化的数据获取：只是标记登录成功，实际数据获取可以通过主界面的数据查询功能进行
                self.log(f"网易号账号 {account} 登录成功，可以通过主界面的数据查询功能获取详细数据")

                result["状态"] = "登录成功"
                result["用户名"] = f"{account}(已登录)"

                # 更新当前数据查看器的数据
                self._update_local_account_data(account, result)

                # 显示成功消息
                self.window.after(0, lambda: messagebox.showinfo("登录成功",
                    f"网易号账号 {account} 登录成功！\n\n"
                    f"浏览器将保持打开状态，您可以：\n"
                    f"1. 手动查看账号数据\n"
                    f"2. 使用主界面的数据查询功能获取详细数据\n"
                    f"3. 随时手动关闭浏览器"))

            except Exception as e:
                self.log(f"❌ 处理网易号账号 {account} 登录后操作时发生错误: {str(e)}")
                result["错误信息"] = str(e)

                # 显示错误消息
                self.window.after(0, lambda: messagebox.showerror("处理失败",
                    f"处理网易号账号 {account} 登录后操作时发生错误:\n\n{str(e)}\n\n"
                    f"浏览器将保持打开状态，您可以手动查看或关闭。"))

        except Exception as e:
            self.log(f"❌ 网易号账号数据获取过程中发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def _netease_phone_login_thread(self, account, account_login, cookie_path):
        """在后台线程中执行网易号手机号登录"""
        try:
            # 使用手机号登录
            login_success, driver, cookies = account_login.login_with_phone(False)

            if login_success and cookies:
                self.log(f"✅ 网易号账号 {account} 手机号登录成功")

                # 在主线程中询问是否更新Cookie
                self.window.after(0, lambda: self._ask_for_netease_cookie_update(account, cookies, cookie_path, driver))
            else:
                self.log(f"❌ 网易号账号 {account} 手机号登录失败")
                self.window.after(0, lambda: messagebox.showerror("登录失败", f"网易号账号 {account} 手机号登录失败"))

                # 如果浏览器仍然活动，提示用户可以手动关闭
                if driver:
                    self.log(f"浏览器仍然处于打开状态，您可以随时手动关闭")
                    self.window.after(0, lambda: messagebox.showinfo("提示", "浏览器仍然处于打开状态，您可以随时手动关闭。"))

        except Exception as e:
            self.log(f"❌ 网易号账号手机号登录过程中发生错误: {str(e)}")
            self.window.after(0, lambda: messagebox.showerror("错误", f"网易号账号手机号登录过程中发生错误: {str(e)}"))
            import traceback
            traceback.print_exc()

    def _ask_for_netease_cookie_update(self, account, cookies, cookie_path, driver):
        """询问是否更新网易号Cookie"""
        if messagebox.askyesno("更新Cookie", f"网易号账号 {account} 手机号登录成功！\n\n是否更新Cookie文件？"):
            try:
                # 保存新的Cookie - 统一保存为账号名.txt格式
                import json
                # 获取账号目录
                base_account_dir = os.path.dirname(cookie_path) if os.path.dirname(cookie_path) else self.config_manager.get("account_dir", platform=self.current_platform)
                save_path = os.path.join(base_account_dir, f"{account}.txt")
                with open(save_path, 'w', encoding='utf-8') as f:
                    json.dump(cookies, f, ensure_ascii=False, indent=4)

                self.log(f"✅ 网易号账号 {account} Cookie已更新")
                messagebox.showinfo("Cookie更新成功", f"网易号账号 {account} Cookie已成功更新到文件:\n{save_path}")

                # 询问是否立即获取账号数据
                self._ask_for_netease_data_query_after_login(account, driver)

            except Exception as e:
                self.log(f"❌ 更新网易号账号 {account} Cookie失败: {str(e)}")
                messagebox.showerror("Cookie更新失败", f"更新网易号账号 {account} Cookie失败:\n{str(e)}")
        else:
            self.log(f"已跳过网易号账号 {account} Cookie更新")
            # 询问是否立即获取账号数据
            self._ask_for_netease_data_query_after_login(account, driver)

    # 注意：handle_tree_click 方法已删除，七日详情按钮点击功能已移植到主界面账号列表

    def clean_numeric_value(self, value):
        """清理数值，将字符串转换为浮点数，支持带"万"单位的数字"""
        # 使用公共工具函数解析数字
        from 网易号存稿.common.utils import parse_number
        return parse_number(value)


    def export_to_excel(self):
        """
        导出美化的Excel数据报告

        【作废计划 目前仍在使用 但不更新】
        注意：此方法为Excel数据导出功能的实现，虽然标记为作废计划，但目前仍在使用中。
        该方法不再进行功能更新和维护，仅保持现有导出功能的稳定运行。
        """
        from tkinter import filedialog

        # 尝试导入必要的库
        try:
            import pandas as pd
            from openpyxl import Workbook, load_workbook
            from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
            from openpyxl.formatting.rule import ColorScaleRule, DataBarRule
            from openpyxl.chart import BarChart, PieChart, LineChart, Reference
            from openpyxl.utils.dataframe import dataframe_to_rows
            from openpyxl.utils import get_column_letter
        except ImportError as e:
            messagebox.showerror("错误", f"无法导出Excel: 缺少必要的库\n{str(e)}\n\n请安装必要的库:\npip install pandas openpyxl")
            self.log(f"无法导出Excel: 缺少必要的库 - {str(e)}")
            return

        # 获取当前时间作为文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        platform_name = self.current_platform if hasattr(self, 'current_platform') else "多平台"
        filename = f"美化数据报告_{platform_name}_{timestamp}.xlsx"

        # 打开文件对话框
        file_path = filedialog.asksaveasfilename(
            defaultextension=".xlsx",
            filetypes=[("Excel文件", "*.xlsx")],
            initialfile=filename
        )

        if not file_path:
            return

        try:
            # 创建美化的Excel报告
            self._create_beautiful_excel_report(file_path, pd)

            messagebox.showinfo("导出成功", f"美化数据报告已导出到:\n{file_path}")
            self.log(f"美化数据报告已导出到: {file_path}")
        except Exception as e:
            messagebox.showerror("导出失败", f"导出数据失败:\n{str(e)}")
            self.log(f"导出数据失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def _create_beautiful_excel_report(self, file_path: str, pd):
        """创建美化的Excel报告"""
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
        from openpyxl.formatting.rule import ColorScaleRule, DataBarRule
        from openpyxl.chart import BarChart, PieChart, Reference
        from openpyxl.utils.dataframe import dataframe_to_rows
        from openpyxl.utils import get_column_letter

        # 创建工作簿
        wb = Workbook()

        # 删除默认工作表
        if 'Sheet' in wb.sheetnames:
            wb.remove(wb['Sheet'])

        # 创建主报告工作表（集成单页设计）
        ws = wb.create_sheet("📊 数据报告")

        current_row = 1

        # 1. 添加报告标题和概览
        current_row = self._add_excel_header(ws, current_row)
        current_row = self._add_excel_overview(ws, current_row)

        # 2. 添加账号详细数据表
        current_row = self._add_excel_accounts_table(ws, current_row)

        # 3. 添加七天收益数据（如果有）
        current_row = self._add_excel_seven_day_data(ws, current_row)

        # 4. 应用全局样式和格式
        self._apply_excel_global_styles(ws)

        # 保存文件
        wb.save(file_path)

    def _add_excel_header(self, ws, current_row):
        """添加Excel报告标题"""
        from openpyxl.styles import Font, PatternFill, Alignment

        # 主标题
        platform_name = self.current_platform if hasattr(self, 'current_platform') else "多平台"
        title = f"📊 {platform_name}存稿工具 - 数据报告"

        ws.cell(row=current_row, column=1, value=title)
        title_cell = ws.cell(row=current_row, column=1)
        title_cell.font = Font(name='Microsoft YaHei UI', size=18, bold=True, color='1E3A8A')
        title_cell.alignment = Alignment(horizontal='center', vertical='center')

        # 合并标题单元格
        ws.merge_cells(f'A{current_row}:H{current_row}')

        current_row += 1

        # 生成时间
        timestamp = datetime.now().strftime("%Y年%m月%d日 %H:%M:%S")
        ws.cell(row=current_row, column=1, value=f"生成时间: {timestamp}")
        time_cell = ws.cell(row=current_row, column=1)
        time_cell.font = Font(name='Microsoft YaHei UI', size=10, color='6B7280')
        time_cell.alignment = Alignment(horizontal='center')

        # 合并时间单元格
        ws.merge_cells(f'A{current_row}:H{current_row}')

        return current_row + 2

    def _add_excel_overview(self, ws, current_row):
        """添加数据概览"""
        from openpyxl.styles import Font, PatternFill, Border, Side, Alignment

        # 概览标题
        ws.cell(row=current_row, column=1, value="📋 数据概览")
        overview_title = ws.cell(row=current_row, column=1)
        overview_title.font = Font(name='Microsoft YaHei UI', size=14, bold=True, color='1E3A8A')

        current_row += 1

        # 计算统计数据
        total_accounts = len([data for data in self.accounts_data if data.get("账号") != "总计"])
        total_income = sum(self.clean_numeric_value(data.get("累计收益", data.get("总收益", 0))) for data in self.accounts_data if data.get("账号") != "总计")
        total_fans = sum(self.clean_numeric_value(data.get("总粉丝", 0)) for data in self.accounts_data if data.get("账号") != "总计")

        # 概览数据
        overview_data = [
            ("总账号数", total_accounts, "个"),
            ("累计收益", f"{total_income:.2f}", "元"),
            ("总粉丝数", f"{total_fans:.0f}", "人"),
        ]

        # 创建概览表格
        for i, (label, value, unit) in enumerate(overview_data):
            col = i * 2 + 1

            # 标签
            label_cell = ws.cell(row=current_row, column=col, value=label)
            label_cell.font = Font(name='Microsoft YaHei UI', size=10, bold=True)
            label_cell.fill = PatternFill(start_color='F3F4F6', end_color='F3F4F6', fill_type='solid')
            label_cell.alignment = Alignment(horizontal='center', vertical='center')

            # 值
            value_cell = ws.cell(row=current_row + 1, column=col, value=f"{value} {unit}")
            value_cell.font = Font(name='Microsoft YaHei UI', size=12, bold=True, color='059669')
            value_cell.alignment = Alignment(horizontal='center', vertical='center')

            # 添加边框
            border = Border(
                left=Side(style='thin', color='D1D5DB'),
                right=Side(style='thin', color='D1D5DB'),
                top=Side(style='thin', color='D1D5DB'),
                bottom=Side(style='thin', color='D1D5DB')
            )
            label_cell.border = border
            value_cell.border = border

        return current_row + 4

    def _add_excel_accounts_table(self, ws, current_row):
        """添加账号详细数据表"""
        from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
        from openpyxl.formatting.rule import ColorScaleRule
        from openpyxl.utils import get_column_letter

        # 表格标题
        ws.cell(row=current_row, column=1, value="📊 账号详细数据")
        table_title = ws.cell(row=current_row, column=1)
        table_title.font = Font(name='Microsoft YaHei UI', size=14, bold=True, color='1E3A8A')

        current_row += 2

        # 定义表头
        headers = ["账号", "用户名", "累计收益", "昨日收益", "总播放", "总粉丝", "草稿箱", "可提现", "状态"]

        # 添加表头
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=current_row, column=col, value=header)
            cell.font = Font(name='Microsoft YaHei UI', size=11, bold=True, color='FFFFFF')
            cell.fill = PatternFill(start_color='1E3A8A', end_color='1E3A8A', fill_type='solid')
            cell.alignment = Alignment(horizontal='center', vertical='center')
            cell.border = Border(
                left=Side(style='thin', color='FFFFFF'),
                right=Side(style='thin', color='FFFFFF'),
                top=Side(style='thin', color='FFFFFF'),
                bottom=Side(style='thin', color='FFFFFF')
            )

        current_row += 1
        start_data_row = current_row

        # 添加数据行
        for data in self.accounts_data:
            if data.get("账号") == "总计":
                continue

            row_data = [
                data.get("账号", ""),
                data.get("用户名", ""),
                self.clean_numeric_value(data.get("累计收益", data.get("总收益", 0))),
                self.clean_numeric_value(data.get("昨日收益", 0)),
                self.clean_numeric_value(data.get("总播放", 0)),
                self.clean_numeric_value(data.get("总粉丝", 0)),
                data.get("草稿箱数量", data.get("草稿箱", 0)),
                self.clean_numeric_value(data.get("可提现", data.get("待提现", 0))),
                data.get("状态", "未知")
            ]

            for col, value in enumerate(row_data, 1):
                cell = ws.cell(row=current_row, column=col, value=value)
                cell.font = Font(name='Microsoft YaHei UI', size=10)
                cell.alignment = Alignment(horizontal='center', vertical='center')

                # 交替行颜色
                if current_row % 2 == 0:
                    cell.fill = PatternFill(start_color='F8FAFC', end_color='F8FAFC', fill_type='solid')

                # 添加边框
                cell.border = Border(
                    left=Side(style='thin', color='E5E7EB'),
                    right=Side(style='thin', color='E5E7EB'),
                    top=Side(style='thin', color='E5E7EB'),
                    bottom=Side(style='thin', color='E5E7EB')
                )

                # 状态列特殊颜色
                if col == 9:  # 状态列
                    if value == "成功":
                        cell.font = Font(name='Microsoft YaHei UI', size=10, color='059669', bold=True)
                    elif value == "失败":
                        cell.font = Font(name='Microsoft YaHei UI', size=10, color='DC2626', bold=True)

            current_row += 1

        # 设置列宽
        column_widths = [15, 20, 12, 12, 15, 12, 10, 12, 10]
        for i, width in enumerate(column_widths, 1):
            ws.column_dimensions[get_column_letter(i)].width = width

        # 添加条件格式（收益列的颜色渐变）
        if current_row > start_data_row:
            income_range = f"C{start_data_row}:C{current_row-1}"
            color_scale = ColorScaleRule(
                start_type='min', start_color='FEF3C7',
                mid_type='percentile', mid_value=50, mid_color='FDE047',
                end_type='max', end_color='059669'
            )
            ws.conditional_formatting.add(income_range, color_scale)

        return current_row + 2

    def _add_excel_seven_day_data(self, ws, current_row):
        """添加七天收益数据"""
        from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
        from openpyxl.chart import LineChart, Reference
        from openpyxl.utils import get_column_letter

        # 收集七天数据
        accounts = []
        account_income_data = {}

        for data in self.accounts_data:
            account = data.get("账号", "")
            if not account or account == "总计":
                continue

            accounts.append(account)
            account_income_data[account] = {}

            # 收集七日收益数据
            if "七日收益数据" in data and isinstance(data["七日收益数据"], dict) and data["七日收益数据"]:
                for date, income in data["七日收益数据"].items():
                    try:
                        from 网易号存稿.common.utils import parse_income
                        income_value = parse_income(income)
                        account_income_data[account][date] = income_value
                    except:
                        account_income_data[account][date] = 0

        if not accounts:
            return current_row

        # 七天数据标题
        ws.cell(row=current_row, column=1, value="📈 七天收益趋势")
        trend_title = ws.cell(row=current_row, column=1)
        trend_title.font = Font(name='Microsoft YaHei UI', size=14, bold=True, color='1E3A8A')

        current_row += 2

        # 生成最近七天的日期列表
        today = datetime.now()
        date_list = []
        for i in range(6, -1, -1):  # 从7天前到今天
            day = today - timedelta(days=i)
            date_list.append(day.strftime("%Y-%m-%d"))

        # 创建表头
        headers = ["账号"] + [date.split("-")[1] + "-" + date.split("-")[2] for date in date_list] + ["七天总计"]

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=current_row, column=col, value=header)
            cell.font = Font(name='Microsoft YaHei UI', size=10, bold=True, color='FFFFFF')
            cell.fill = PatternFill(start_color='3B82F6', end_color='3B82F6', fill_type='solid')
            cell.alignment = Alignment(horizontal='center', vertical='center')
            cell.border = Border(
                left=Side(style='thin', color='FFFFFF'),
                right=Side(style='thin', color='FFFFFF'),
                top=Side(style='thin', color='FFFFFF'),
                bottom=Side(style='thin', color='FFFFFF')
            )

        current_row += 1

        # 添加数据行
        for account in accounts[:10]:  # 限制显示前10个账号
            row_data = [account]
            seven_day_total = 0

            for date in date_list:
                income = account_income_data.get(account, {}).get(date, 0)
                row_data.append(income)
                seven_day_total += income

            row_data.append(seven_day_total)

            for col, value in enumerate(row_data, 1):
                cell = ws.cell(row=current_row, column=col, value=value)
                cell.font = Font(name='Microsoft YaHei UI', size=9)
                cell.alignment = Alignment(horizontal='center', vertical='center')

                # 交替行颜色
                if current_row % 2 == 0:
                    cell.fill = PatternFill(start_color='F8FAFC', end_color='F8FAFC', fill_type='solid')

                # 添加边框
                cell.border = Border(
                    left=Side(style='thin', color='E5E7EB'),
                    right=Side(style='thin', color='E5E7EB'),
                    top=Side(style='thin', color='E5E7EB'),
                    bottom=Side(style='thin', color='E5E7EB')
                )

            current_row += 1

        return current_row + 2

    def _apply_excel_global_styles(self, ws):
        """应用全局样式"""
        from openpyxl.styles import Font

        # 设置默认字体
        for row in ws.iter_rows():
            for cell in row:
                if cell.font.name == 'Calibri':  # 默认字体
                    cell.font = Font(name='Microsoft YaHei UI', size=10)

        # 冻结首行
        ws.freeze_panes = 'A4'

    def _get_toutiao_account_data_after_login(self, account: str, driver):
        """
        登录后获取头条账号数据

        【作废计划 目前仍在使用 但不更新】
        注意：此方法为头条号数据获取的实现，虽然标记为作废计划，但目前仍在使用中。
        该方法不再进行功能更新和维护，仅保持现有数据获取功能的稳定运行。

        Args:
            account: 账号名称
            driver: 浏览器驱动对象
        """
        try:
            from 网易号存稿.platforms.toutiao.data_query import ToutiaoDataQuery
            import datetime

            self.log(f"开始获取头条账号 {account} 的数据...")

            # 获取账号目录
            if not self.config_manager:
                self.log("❌ 配置管理器未初始化")
                return

            base_account_dir = self.config_manager.get("account_dir", platform=self.current_platform)
            if not base_account_dir:
                self.log("❌ 未配置账号目录")
                return

            # 创建数据查询对象，但使用已有的driver
            query = ToutiaoDataQuery(base_account_dir, self.log)

            # 使用已有的driver进行数据获取
            query.driver = driver

            # 初始化结果
            result = {
                "账号": account,
                "用户名": "",
                "累计收益": 0.0,
                "昨日收益": 0.0,
                "总播放": 0.0,
                "昨日播放": 0.0,
                "总粉丝": 0.0,
                "昨日粉丝": 0.0,
                "草稿箱数量": 0,
                "可提现": 0.0,
                "总提现": 0.0,
                "最近提现日期": "",
                "最近提现金额": 0.0,
                "七日收益": [],
                "查询时间": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "状态": "失败",
                "错误信息": ""
            }

            try:
                # 1. 获取首页数据
                self.log("正在获取首页数据...")
                home_data = query._get_home_data()
                result.update(home_data)

                # 2. 获取草稿箱数据
                self.log("正在获取草稿箱数据...")
                draft_data = query._get_draft_data()
                result.update(draft_data)

                # 3. 获取提现数据
                self.log("正在获取提现数据...")
                withdraw_data = query._get_withdraw_data()
                result.update(withdraw_data)

                result["状态"] = "成功"
                self.log(f"✅ 头条账号 {account} 数据获取完成")

                # 更新账号数据到父窗口的数据结构
                if hasattr(self.parent, 'account_data'):
                    update_success = self.parent.account_data.update_account_data(account, result)
                    if update_success:
                        self.log("✅ 头条账号数据已成功保存")

                        # 使用增量更新而不是全表刷新
                        if hasattr(self.parent, 'root'):
                            self.parent.root.after(200, lambda: self.parent._incremental_update_account(account, result))
                            self.log("✅ 已触发界面增量更新")
                    else:
                        self.log("❌ 保存头条账号数据时出错")

                # 更新当前数据查看器的数据
                self._update_local_account_data(account, result)

                # 显示成功消息
                self.window.after(0, lambda: messagebox.showinfo("数据获取成功",
                    f"头条账号 {account} 数据获取成功！\n\n"
                    f"用户名: {result.get('用户名', 'N/A')}\n"
                    f"累计收益: {result.get('累计收益', 0):.2f}\n"
                    f"总粉丝: {result.get('总粉丝', 0):.0f}\n"
                    f"草稿箱数量: {result.get('草稿箱数量', 0)}\n\n"
                    f"浏览器将保持打开状态，您可以随时手动关闭。"))

            except Exception as e:
                self.log(f"❌ 获取头条账号 {account} 数据时发生错误: {str(e)}")
                result["错误信息"] = str(e)

                # 显示错误消息
                self.window.after(0, lambda: messagebox.showerror("数据获取失败",
                    f"获取头条账号 {account} 数据时发生错误:\n\n{str(e)}\n\n"
                    f"浏览器将保持打开状态，您可以手动查看或关闭。"))

        except Exception as e:
            self.log(f"❌ 头条账号数据获取过程中发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def _update_local_account_data(self, account: str, result: dict):
        """
        更新本地账号数据并刷新界面

        Args:
            account: 账号名称
            result: 查询结果数据
        """
        try:
            # 查找并更新账号数据
            for i, data in enumerate(self.accounts_data):
                if data.get("账号") == account:
                    # 更新数据
                    self.accounts_data[i].update(result)
                    break
            else:
                # 如果没有找到账号，添加新的账号数据
                self.accounts_data.append(result)

            # 刷新界面数据
            self.window.after(0, self.load_data)
            self.log(f"✅ 已更新账号 {account} 的本地数据并刷新界面")

        except Exception as e:
            self.log(f"❌ 更新本地账号数据时发生错误: {str(e)}")

    def _ask_for_data_query_after_login(self, account, driver):
        """询问是否在登录后获取数据"""
        if messagebox.askyesno("获取数据", f"头条账号 {account} 登录成功！\n\n是否立即获取账号数据？\n\n注意：获取数据需要访问多个页面，大约需要1-2分钟。"):
            self.log(f"开始获取头条账号 {account} 的数据...")

            # 在新线程中获取数据，避免阻塞UI
            import threading
            threading.Thread(target=self._get_toutiao_account_data_after_login,
                           args=(account, driver), daemon=True).start()
        else:
            self.log(f"跳过数据获取，浏览器将保持打开状态")
            messagebox.showinfo("登录成功", f"头条账号 {account} 登录成功\n\n浏览器将保持打开状态，您可以随时手动关闭。")

    def _ask_for_phone_login(self, account, toutiao_login, cookie_path):
        """询问是否尝试手机号登录"""
        if not messagebox.askyesno("登录失败", "Cookie登录失败，是否尝试使用手机号登录？"):
            messagebox.showerror("登录失败", f"头条账号 {account} 登录失败")
            return

        self.log(f"正在尝试使用手机号登录头条账号: {account}")

        # 显示提示对话框
        messagebox.showinfo("操作提示", "请在打开的浏览器中完成头条账号登录操作。\n\n登录地址：https://mp.toutiao.com/auth/page/login\n\n浏览器将保持打开状态，您可以随时手动关闭。")

        # 在后台线程中执行手机号登录
        import threading
        threading.Thread(target=self._phone_login_thread, args=(account, toutiao_login, cookie_path), daemon=True).start()

    def _phone_login_thread(self, account, toutiao_login, cookie_path):
        """在后台线程中执行手机号登录"""
        try:
            # 使用手机号登录
            login_success, driver, cookies = toutiao_login.login_with_phone(False)

            if login_success and cookies:
                self.log(f"✅ 头条账号 {account} 手机号登录成功")

                # 在主线程中询问是否更新Cookie
                self.window.after(0, lambda: self._ask_for_cookie_update(account, cookies, cookie_path, driver))
            else:
                self.log(f"❌ 头条账号 {account} 手机号登录失败")
                self.window.after(0, lambda: messagebox.showerror("登录失败", f"头条账号 {account} 手机号登录失败"))

                # 如果浏览器仍然活动，提示用户可以手动关闭
                if driver:
                    self.log(f"浏览器仍然处于打开状态，您可以随时手动关闭")
                    self.window.after(0, lambda: messagebox.showinfo("提示", "浏览器仍然处于打开状态，您可以随时手动关闭。"))

        except Exception as e:
            self.log(f"❌ 手机号登录过程中发生错误: {str(e)}")
            self.window.after(0, lambda: messagebox.showerror("错误", f"手机号登录过程中发生错误: {str(e)}"))

    def _ask_for_cookie_update(self, account, cookies, cookie_path, driver):
        """询问是否更新Cookie"""
        if messagebox.askyesno("更新Cookie", f"是否使用新的登录信息更新头条账号 {account} 的Cookie？"):
            # 更新Cookie - 统一保存为账号名.txt格式
            try:
                # 获取账号目录
                base_account_dir = os.path.dirname(cookie_path) if os.path.dirname(cookie_path) else self.config_manager.get("account_dir", platform=self.current_platform)
                save_path = os.path.join(base_account_dir, f"{account}.txt")
                with open(save_path, 'w', encoding='utf-8') as f:
                    json.dump(cookies, f, ensure_ascii=False, indent=4)
                self.log(f"✅ 已更新头条账号 {account} 的Cookie")
                messagebox.showinfo("更新成功", f"已更新头条账号 {account} 的Cookie")
            except Exception as e:
                self.log(f"❌ 更新Cookie失败: {str(e)}")
                messagebox.showerror("更新失败", f"更新Cookie失败: {str(e)}")

        # 询问是否立即获取账号数据
        self._ask_for_data_query_after_login(account, driver)


