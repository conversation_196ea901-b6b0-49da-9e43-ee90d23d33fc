#!/usr/bin/env python3
"""
测试增强版定时任务UI
"""

import os
import sys
import tkinter as tk
from tkinter import ttk

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def test_enhanced_task_dialog():
    """测试增强版任务创建对话框"""
    print("🔍 测试增强版任务创建对话框...")
    
    try:
        # 创建测试窗口
        root = tk.Tk()
        root.title("测试增强版定时任务UI")
        root.geometry("400x300")
        
        # 创建测试按钮
        def test_dialog():
            try:
                from 网易号存稿.ui.managers.enhanced_task_dialog import EnhancedTaskCreateDialog
                
                # 创建模拟回调函数
                def mock_callback():
                    print("✅ 模拟存稿任务回调")
                
                def mock_video_processor_callback():
                    print("✅ 模拟视频处理工具回调")
                
                def mock_log_callback(message):
                    print(f"📝 日志: {message}")
                
                # 创建模拟调度器集成
                class MockSchedulerIntegration:
                    def create_task(self, task_data):
                        print(f"✅ 模拟创建任务: {task_data}")
                        return {"id": "test_task", "name": task_data.get('name', '测试任务')}
                
                scheduler_integration = MockSchedulerIntegration()
                
                # 创建对话框
                dialog = EnhancedTaskCreateDialog(
                    root,
                    mock_callback,
                    mock_video_processor_callback,
                    scheduler_integration,
                    mock_log_callback
                )
                
                if dialog.result:
                    print("✅ 对话框测试成功，任务已创建")
                else:
                    print("ℹ️ 对话框被取消")
                    
            except Exception as e:
                print(f"❌ 对话框测试失败: {e}")
                import traceback
                traceback.print_exc()
        
        # 创建测试界面
        main_frame = ttk.Frame(root, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(
            main_frame, 
            text="增强版定时任务UI测试", 
            font=("微软雅黑", 14, "bold")
        ).pack(pady=(0, 20))
        
        ttk.Button(
            main_frame,
            text="🧪 测试任务创建对话框",
            command=test_dialog,
            width=25
        ).pack(pady=10)
        
        ttk.Button(
            main_frame,
            text="❌ 退出测试",
            command=root.destroy,
            width=25
        ).pack(pady=10)
        
        # 显示测试说明
        info_text = """
测试说明：
1. 点击"测试任务创建对话框"按钮
2. 在弹出的对话框中配置任务参数
3. 测试各种功能：
   - 基本信息输入
   - 任务模式选择
   - 视频路径选择
   - 账号选择
   - 调度配置
4. 点击"创建任务"测试任务创建功能
        """
        
        info_label = ttk.Label(
            main_frame,
            text=info_text,
            font=("微软雅黑", 9),
            foreground="gray",
            justify=tk.LEFT
        )
        info_label.pack(pady=(20, 0))
        
        print("✅ 测试界面已创建")
        print("💡 请点击按钮测试增强版定时任务UI功能")
        
        # 运行测试界面
        root.mainloop()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_imports():
    """测试导入"""
    print("🔍 测试模块导入...")
    
    try:
        from 网易号存稿.ui.managers.enhanced_task_dialog import EnhancedTaskCreateDialog
        print("✅ EnhancedTaskCreateDialog 导入成功")
        
        # 测试类的基本属性
        print(f"✅ 类名: {EnhancedTaskCreateDialog.__name__}")
        print(f"✅ 文档: {EnhancedTaskCreateDialog.__doc__}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 增强版定时任务UI测试")
    print("=" * 60)
    
    # 测试导入
    if not test_imports():
        print("❌ 导入测试失败，无法继续")
        return
    
    print("-" * 60)
    
    # 测试UI
    test_enhanced_task_dialog()
    
    print("=" * 60)
    print("🎉 测试完成")

if __name__ == "__main__":
    main()
