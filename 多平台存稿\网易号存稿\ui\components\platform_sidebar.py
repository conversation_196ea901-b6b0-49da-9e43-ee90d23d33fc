"""
平台侧边栏组件 - 用于多平台切换的侧边栏导航
"""

import os
import tkinter as tk
from tkinter import ttk
from typing import Callable, Dict, List, Optional

class PlatformSidebar:
    """平台侧边栏类，负责显示和管理多平台切换"""

    def __init__(self, parent: ttk.Frame, config_manager, log_callback: Callable = None, on_platform_change: Callable = None):
        """
        初始化平台侧边栏

        Args:
            parent: 父容器
            config_manager: 配置管理器
            log_callback: 日志回调函数
            on_platform_change: 平台切换回调函数
        """
        self.parent = parent
        self.config_manager = config_manager
        self.log_callback = log_callback
        self.on_platform_change = on_platform_change

        # 初始化变量
        self.current_platform = tk.StringVar(value=self.config_manager.get_current_platform())
        self.platform_buttons = {}

        # 创建UI
        self.create_ui()

    def log(self, message: str) -> None:
        """
        记录日志

        Args:
            message: 日志消息
        """
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def create_ui(self) -> None:
        """创建用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.parent)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建标题 - 进一步减少间距让侧边栏更紧凑
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(3, 5))  # 进一步减少到(3, 5)

        title_label = ttk.Label(title_frame, text="平台导航", font=("微软雅黑", 10, "bold"))  # 字体进一步减小到10
        title_label.pack(side=tk.LEFT, padx=3)  # 进一步减少到3

        # 创建平台按钮容器 - 垂直排列，最小边距
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)  # 进一步减少到2

        # 获取可用平台列表
        available_platforms = self.config_manager.get("available_platforms", ["netease"])
        platform_names = self.config_manager.get("platform_names", {})

        # 创建平台按钮 - 使用小尺寸按钮，垂直排列
        for i, platform in enumerate(available_platforms):
            platform_name = platform_names.get(platform, platform)

            # 创建按钮框架
            button_frame = ttk.Frame(buttons_frame)
            button_frame.pack(fill=tk.X, pady=1)  # 进一步减少垂直间距

            # 创建平台按钮 - 使用小尺寸样式
            button = ttk.Button(
                button_frame,
                text=platform_name,
                style="PlatformSmall.TButton",  # 使用小尺寸样式
                command=lambda p=platform: self.switch_platform(p)
            )
            button.pack(fill=tk.X, padx=4, pady=1, ipady=0)  # 进一步减少内边距

            # 保存按钮引用
            self.platform_buttons[platform] = button

        # 添加分隔线 - 最小间距
        ttk.Separator(main_frame, orient="horizontal").pack(fill=tk.X, padx=3, pady=5)  # 进一步减少

        # 添加通用工具按钮区域 - 最小边距
        tools_frame = ttk.Frame(main_frame)
        tools_frame.pack(fill=tk.X, padx=2, pady=2)  # 进一步减少到2

        # 添加通用工具标题 - 最小字体和间距
        tools_label = ttk.Label(tools_frame, text="通用工具", font=("微软雅黑", 9, "bold"))  # 字体进一步减小到9
        tools_label.pack(anchor=tk.W, padx=3, pady=(0, 3))  # 进一步减少间距

        # 添加视频处理工具按钮
        video_process_button = ttk.Button(
            tools_frame,
            text="🎬 视频处理工具",
            style="Tool.TButton",
            command=self.open_video_processor
        )
        video_process_button.pack(fill=tk.X, padx=4, pady=1, ipady=0)  # 进一步减少间距和内边距

        # 添加视频下载工具按钮
        video_download_button = ttk.Button(
            tools_frame,
            text="📥 视频下载工具",
            style="Tool.TButton",
            command=self.open_video_downloader
        )
        video_download_button.pack(fill=tk.X, padx=4, pady=1, ipady=0)  # 进一步减少间距和内边距

        # 添加每日爆文按钮
        daily_hot_button = ttk.Button(
            tools_frame,
            text="🔥 每日爆文",
            style="Tool.TButton",
            command=self.open_daily_hot
        )
        daily_hot_button.pack(fill=tk.X, padx=4, pady=1, ipady=0)  # 进一步减少间距和内边距

        # 更新按钮状态
        self.update_button_states()

        # 配置自定义样式
        self.configure_styles()

    def configure_styles(self):
        """配置自定义按钮样式"""
        style = ttk.Style()

        # 小尺寸平台按钮样式 - 极紧凑的设计
        style.configure(
            "PlatformSmall.TButton",
            font=("微软雅黑", 8),  # 保持小字体
            padding=(4, 1)  # 进一步减小内边距
        )

        # 工具按钮样式 - 极小的尺寸
        style.configure(
            "Tool.TButton",
            font=("微软雅黑", 8),  # 进一步减小字体
            padding=(4, 2)  # 进一步减小内边距
        )

    def update_button_states(self) -> None:
        """更新按钮状态"""
        current_platform = self.config_manager.get_current_platform()

        # 更新所有按钮状态
        for platform, button in self.platform_buttons.items():
            if platform == current_platform:
                button.state(["disabled"])  # 禁用当前平台按钮
                button.configure(text=f"✓ {self.config_manager.get_platform_name(platform)}")
            else:
                button.state(["!disabled"])  # 启用其他平台按钮
                button.configure(text=self.config_manager.get_platform_name(platform))

    def switch_platform(self, platform: str) -> None:
        """
        切换平台

        Args:
            platform: 平台标识
        """
        # 检查是否已经是当前平台
        current_platform = self.config_manager.get_current_platform()
        if platform == current_platform:
            return

        # 设置新平台
        success = self.config_manager.set_current_platform(platform, force_save=True)

        if success:
            # 更新按钮状态
            self.update_button_states()

            # 调用平台切换回调
            if self.on_platform_change:
                self.on_platform_change(platform)
        else:
            self.log(f"切换到平台 {platform} 失败")

    def open_video_processor(self):
        """打开视频处理工具"""
        # 调用主程序的视频处理工具启动方法
        if hasattr(self, 'main_ui') and self.main_ui:
            self.main_ui.launch_video_processor()
        else:
            # 回退到旧的启动方式
            from 网易号存稿.common.utils import launch_video_processor
            launch_video_processor(self.log)

    def open_video_downloader(self):
        """打开视频下载工具"""
        # 导入视频下载工具对话框
        from 网易号存稿.ui.dialogs.video_downloader_dialog import VideoDownloaderDialog

        # 创建对话框
        dialog = VideoDownloaderDialog(self.parent.winfo_toplevel(), self.config_manager)

    def open_daily_hot(self):
        """打开每日爆文工具"""
        # 导入每日爆文工具对话框
        from 网易号存稿.ui.dialogs.daily_hot_dialog import DailyHotDialog

        # 创建对话框
        dialog = DailyHotDialog(self.parent.winfo_toplevel(), self.config_manager)
