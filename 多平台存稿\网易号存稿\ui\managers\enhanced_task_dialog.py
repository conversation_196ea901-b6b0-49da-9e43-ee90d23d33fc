"""
增强版任务创建对话框
包含视频路径选择、账号选择、视频处理工具集成等功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import datetime
import os
from typing import Dict, Any, Optional, Callable, List


class EnhancedTaskCreateDialog:
    """增强版任务创建对话框"""
    
    def __init__(self, parent_window, callback, video_processor_callback, scheduler_integration, log_callback):
        """
        初始化增强版任务创建对话框
        
        Args:
            parent_window: 父窗口
            callback: 存稿任务回调函数
            video_processor_callback: 视频处理工具回调函数
            scheduler_integration: 调度器集成对象
            log_callback: 日志回调函数
        """
        self.parent_window = parent_window
        self.callback = callback
        self.video_processor_callback = video_processor_callback
        self.scheduler_integration = scheduler_integration
        self.log_callback = log_callback
        
        # 对话框结果
        self.result = None
        
        # UI变量
        self.name_var = tk.StringVar(value="新建定时任务")
        self.description_var = tk.StringVar()
        self.task_type_var = tk.StringVar(value="daily")
        self.platform_var = tk.StringVar(value="all")
        self.schedule_date_var = tk.StringVar()
        self.schedule_time_var = tk.StringVar(value="09:00:00")
        self.enabled_var = tk.BooleanVar(value=True)
        
        # 任务模式变量
        self.task_mode_var = tk.StringVar(value="video_and_draft")  # video_and_draft 或 draft_only
        
        # 路径和账号变量
        self.video_path_var = tk.StringVar()
        self.selected_accounts_var = tk.StringVar(value="all")
        
        # 间隔配置变量
        self.interval_days_var = tk.IntVar(value=1)
        self.interval_hours_var = tk.IntVar(value=0)
        self.interval_minutes_var = tk.IntVar(value=0)
        
        # 创建对话框
        self.create_dialog()
    
    def create_dialog(self):
        """创建对话框"""
        self.dialog = tk.Toplevel(self.parent_window)
        self.dialog.title("📝 创建定时任务")
        self.dialog.resizable(False, False)
        self.dialog.transient(self.parent_window)
        self.dialog.grab_set()

        # 设置窗口大小并居中显示
        width, height = 650, 750
        screen_width = self.dialog.winfo_screenwidth()
        screen_height = self.dialog.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")

        # 确保窗口更新
        self.dialog.update_idletasks()
        
        # 创建主框架
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建滚动框架
        canvas = tk.Canvas(main_frame)
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 创建各个配置区域
        self.create_basic_info_section(scrollable_frame)
        self.create_task_mode_section(scrollable_frame)
        self.create_path_section(scrollable_frame)
        self.create_account_section(scrollable_frame)
        self.create_schedule_section(scrollable_frame)
        self.create_buttons_section(scrollable_frame)
        
        # 设置默认值
        self.set_default_values()
        
        # 绑定鼠标滚轮事件
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)
        
        # 等待对话框关闭
        self.dialog.wait_window()
    
    def create_basic_info_section(self, parent):
        """创建基本信息区域"""
        # 基本信息框架
        info_frame = ttk.LabelFrame(parent, text="📋 基本信息", padding=15)
        info_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 任务名称
        name_frame = ttk.Frame(info_frame)
        name_frame.pack(fill=tk.X, pady=(0, 10))
        ttk.Label(name_frame, text="任务名称:", width=12).pack(side=tk.LEFT)
        ttk.Entry(name_frame, textvariable=self.name_var, width=40).pack(side=tk.LEFT, padx=(10, 0))
        
        # 任务描述
        desc_frame = ttk.Frame(info_frame)
        desc_frame.pack(fill=tk.X, pady=(0, 10))
        ttk.Label(desc_frame, text="任务描述:", width=12).pack(side=tk.LEFT)
        ttk.Entry(desc_frame, textvariable=self.description_var, width=40).pack(side=tk.LEFT, padx=(10, 0))
        
        # 启用状态
        enable_frame = ttk.Frame(info_frame)
        enable_frame.pack(fill=tk.X)
        ttk.Checkbutton(enable_frame, text="启用任务", variable=self.enabled_var).pack(side=tk.LEFT)
    
    def create_task_mode_section(self, parent):
        """创建任务模式区域"""
        mode_frame = ttk.LabelFrame(parent, text="🎯 任务模式", padding=15)
        mode_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 模式选择
        ttk.Radiobutton(
            mode_frame, 
            text="🎬 视频处理 + 存稿模式（先处理视频，再执行存稿）", 
            variable=self.task_mode_var, 
            value="video_and_draft",
            command=self.on_mode_change
        ).pack(anchor=tk.W, pady=(0, 5))
        
        ttk.Radiobutton(
            mode_frame, 
            text="📝 直接存稿模式（跳过视频处理，直接存稿）", 
            variable=self.task_mode_var, 
            value="draft_only",
            command=self.on_mode_change
        ).pack(anchor=tk.W)
        
        # 模式说明
        self.mode_info_label = ttk.Label(
            mode_frame, 
            text="视频处理+存稿模式：系统将先启动视频处理工具处理视频，完成后自动执行存稿操作",
            foreground="blue",
            font=("微软雅黑", 9)
        )
        self.mode_info_label.pack(anchor=tk.W, pady=(10, 0))
    
    def create_path_section(self, parent):
        """创建路径配置区域"""
        self.path_frame = ttk.LabelFrame(parent, text="📁 视频路径配置", padding=15)
        self.path_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 视频路径选择
        path_select_frame = ttk.Frame(self.path_frame)
        path_select_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(path_select_frame, text="视频目录:", width=12).pack(side=tk.LEFT)
        ttk.Entry(path_select_frame, textvariable=self.video_path_var, width=35).pack(side=tk.LEFT, padx=(10, 5))
        ttk.Button(path_select_frame, text="浏览", command=self.select_video_path, width=8).pack(side=tk.LEFT)
        
        # 路径说明
        ttk.Label(
            self.path_frame, 
            text="选择包含待处理视频文件的目录。视频处理工具将处理该目录下的所有视频文件。",
            foreground="gray",
            font=("微软雅黑", 8)
        ).pack(anchor=tk.W, pady=(0, 5))
        
        # 视频处理工具测试按钮
        test_frame = ttk.Frame(self.path_frame)
        test_frame.pack(fill=tk.X)
        
        ttk.Button(
            test_frame, 
            text="🔧 测试视频处理工具", 
            command=self.test_video_processor,
            width=20
        ).pack(side=tk.LEFT)
        
        ttk.Label(
            test_frame, 
            text="点击测试视频处理工具是否可以正常启动",
            foreground="gray",
            font=("微软雅黑", 8)
        ).pack(side=tk.LEFT, padx=(10, 0))
    
    def create_account_section(self, parent):
        """创建账号选择区域"""
        account_frame = ttk.LabelFrame(parent, text="👥 账号选择", padding=15)
        account_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 账号选择选项
        ttk.Radiobutton(
            account_frame, 
            text="🌐 所有账号（推荐）", 
            variable=self.selected_accounts_var, 
            value="all"
        ).pack(anchor=tk.W, pady=(0, 5))
        
        ttk.Radiobutton(
            account_frame, 
            text="🎯 指定账号", 
            variable=self.selected_accounts_var, 
            value="selected"
        ).pack(anchor=tk.W, pady=(0, 10))
        
        # 账号列表框架（暂时简化）
        self.account_list_frame = ttk.Frame(account_frame)
        self.account_list_frame.pack(fill=tk.X)
        
        ttk.Label(
            self.account_list_frame,
            text="💡 提示：当前版本推荐使用'所有账号'选项，系统将为所有配置的账号执行存稿任务",
            foreground="blue",
            font=("微软雅黑", 9)
        ).pack(anchor=tk.W)

    def create_schedule_section(self, parent):
        """创建调度配置区域"""
        schedule_frame = ttk.LabelFrame(parent, text="⏰ 调度配置", padding=15)
        schedule_frame.pack(fill=tk.X, pady=(0, 15))

        # 任务类型选择
        type_frame = ttk.Frame(schedule_frame)
        type_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(type_frame, text="任务类型:", width=12).pack(side=tk.LEFT)
        type_combo = ttk.Combobox(
            type_frame,
            textvariable=self.task_type_var,
            values=["once", "daily", "weekly", "monthly"],
            state="readonly",
            width=15
        )
        type_combo.pack(side=tk.LEFT, padx=(10, 0))

        # 类型说明
        type_descriptions = {
            "once": "一次性任务",
            "daily": "每日重复",
            "weekly": "每周重复",
            "monthly": "每月重复"
        }

        self.type_desc_label = ttk.Label(
            type_frame,
            text=f"({type_descriptions.get(self.task_type_var.get(), '')})",
            foreground="gray"
        )
        self.type_desc_label.pack(side=tk.LEFT, padx=(10, 0))

        # 绑定类型变化事件
        type_combo.bind("<<ComboboxSelected>>", self.on_type_change)

        # 执行时间
        time_frame = ttk.Frame(schedule_frame)
        time_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(time_frame, text="执行时间:", width=12).pack(side=tk.LEFT)

        # 日期选择（仅一次性任务显示）
        self.date_frame = ttk.Frame(time_frame)
        self.date_frame.pack(side=tk.LEFT, padx=(10, 5))

        ttk.Entry(self.date_frame, textvariable=self.schedule_date_var, width=12).pack(side=tk.LEFT)
        ttk.Label(self.date_frame, text="日期").pack(side=tk.LEFT, padx=(5, 10))

        # 时间选择
        ttk.Entry(time_frame, textvariable=self.schedule_time_var, width=10).pack(side=tk.LEFT, padx=(5, 0))
        ttk.Label(time_frame, text="时间 (HH:MM:SS)").pack(side=tk.LEFT, padx=(5, 0))

        # 间隔配置（重复任务显示）
        self.interval_frame = ttk.Frame(schedule_frame)
        self.interval_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(self.interval_frame, text="重复间隔:", width=12).pack(side=tk.LEFT)

        # 天数
        ttk.Entry(self.interval_frame, textvariable=self.interval_days_var, width=5).pack(side=tk.LEFT, padx=(10, 2))
        ttk.Label(self.interval_frame, text="天").pack(side=tk.LEFT, padx=(0, 10))

        # 小时
        ttk.Entry(self.interval_frame, textvariable=self.interval_hours_var, width=5).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Label(self.interval_frame, text="小时").pack(side=tk.LEFT, padx=(0, 10))

        # 分钟
        ttk.Entry(self.interval_frame, textvariable=self.interval_minutes_var, width=5).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Label(self.interval_frame, text="分钟").pack(side=tk.LEFT)

    def create_buttons_section(self, parent):
        """创建按钮区域"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(20, 0))

        # 创建任务按钮
        ttk.Button(
            button_frame,
            text="✅ 创建任务",
            command=self.create_task,
            width=15
        ).pack(side=tk.RIGHT, padx=(10, 0))

        # 取消按钮
        ttk.Button(
            button_frame,
            text="❌ 取消",
            command=self.cancel,
            width=15
        ).pack(side=tk.RIGHT)

    def set_default_values(self):
        """设置默认值"""
        # 设置默认日期为明天
        tomorrow = datetime.datetime.now() + datetime.timedelta(days=1)
        self.schedule_date_var.set(tomorrow.strftime("%Y-%m-%d"))

        # 根据任务类型显示/隐藏相关控件
        self.on_type_change()
        self.on_mode_change()

    def on_mode_change(self):
        """任务模式变化处理"""
        mode = self.task_mode_var.get()

        if mode == "video_and_draft":
            self.mode_info_label.config(
                text="视频处理+存稿模式：系统将先启动视频处理工具处理视频，完成后自动执行存稿操作"
            )
            # 显示路径配置
            self.path_frame.pack(fill=tk.X, pady=(0, 15))
        else:
            self.mode_info_label.config(
                text="直接存稿模式：系统将跳过视频处理步骤，直接为选定账号执行存稿操作"
            )
            # 隐藏路径配置
            self.path_frame.pack_forget()

    def on_type_change(self, event=None):
        """任务类型变化处理"""
        task_type = self.task_type_var.get()

        # 更新类型说明
        type_descriptions = {
            "once": "一次性任务",
            "daily": "每日重复",
            "weekly": "每周重复",
            "monthly": "每月重复"
        }
        self.type_desc_label.config(text=f"({type_descriptions.get(task_type, '')})")

        # 根据类型显示/隐藏相关控件
        if task_type == "once":
            self.date_frame.pack(side=tk.LEFT, padx=(10, 5))
            self.interval_frame.pack_forget()
        else:
            self.date_frame.pack_forget()
            self.interval_frame.pack(fill=tk.X, pady=(0, 10))

    def select_video_path(self):
        """选择视频路径"""
        path = filedialog.askdirectory(title="选择视频目录")
        if path:
            self.video_path_var.set(path)
            self.log_callback(f"✅ 已选择视频目录: {path}")

    def test_video_processor(self):
        """测试视频处理工具"""
        try:
            if self.video_processor_callback:
                self.log_callback("🔧 正在测试视频处理工具...")
                self.video_processor_callback()
                messagebox.showinfo("测试成功", "视频处理工具启动成功！")
            else:
                messagebox.showwarning("警告", "视频处理工具回调函数未配置")
        except Exception as e:
            self.log_callback(f"❌ 视频处理工具测试失败: {e}")
            messagebox.showerror("测试失败", f"视频处理工具启动失败：{e}")

    def create_task(self):
        """创建任务"""
        try:
            # 验证输入
            if not self.name_var.get().strip():
                messagebox.showerror("错误", "请输入任务名称")
                return

            if not self.schedule_time_var.get().strip():
                messagebox.showerror("错误", "请输入执行时间")
                return

            # 验证时间格式
            try:
                datetime.datetime.strptime(self.schedule_time_var.get(), "%H:%M:%S")
            except ValueError:
                messagebox.showerror("错误", "时间格式不正确，请使用 HH:MM:SS 格式")
                return

            # 如果是视频处理模式，验证路径
            if self.task_mode_var.get() == "video_and_draft":
                if not self.video_path_var.get().strip():
                    messagebox.showerror("错误", "视频处理模式需要选择视频目录")
                    return

                if not os.path.exists(self.video_path_var.get()):
                    messagebox.showerror("错误", "选择的视频目录不存在")
                    return

            # 构建任务数据
            task_data = self.build_task_data()

            # 创建任务
            task = self.scheduler_integration.create_task(task_data)

            if task:
                self.result = task
                self.log_callback(f"✅ 任务创建成功: {self.name_var.get()}")
                messagebox.showinfo("成功", "任务创建成功！")
                self.dialog.destroy()
            else:
                messagebox.showerror("错误", "任务创建失败")

        except Exception as e:
            self.log_callback(f"❌ 创建任务失败: {e}")
            messagebox.showerror("错误", f"创建任务失败：{e}")

    def build_task_data(self):
        """构建任务数据"""
        # 基础任务数据
        task_data = {
            "name": self.name_var.get().strip(),
            "description": self.description_var.get().strip() or f"定时任务 - {self.name_var.get()}",
            "task_type": self.task_type_var.get(),
            "target_platform": self.platform_var.get(),
            "enabled": self.enabled_var.get(),
            "schedule_time": self.schedule_time_var.get(),
        }

        # 添加日期（一次性任务）
        if self.task_type_var.get() == "once":
            task_data["schedule_date"] = self.schedule_date_var.get()

        # 添加间隔配置（重复任务）
        if self.task_type_var.get() in ["daily", "weekly", "monthly"]:
            task_data["interval_config"] = {
                "days": self.interval_days_var.get(),
                "hours": self.interval_hours_var.get(),
                "minutes": self.interval_minutes_var.get(),
                "seconds": 0
            }

        # 添加任务模式和路径配置
        task_data["task_mode"] = self.task_mode_var.get()
        if self.task_mode_var.get() == "video_and_draft":
            task_data["video_path"] = self.video_path_var.get()

        # 添加账号配置
        task_data["selected_accounts"] = self.selected_accounts_var.get()

        return task_data

    def cancel(self):
        """取消对话框"""
        self.result = None
        self.dialog.destroy()
