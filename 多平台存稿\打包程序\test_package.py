"""
测试打包工具的基本功能
"""

import sys
import os
from pathlib import Path

def test_dependencies():
    """测试依赖检查"""
    print("测试依赖检查...")
    
    # 检查源目录
    source_dir = Path(r"C:\Users\<USER>\Downloads\网易\多平台存稿")
    if not source_dir.exists():
        print(f"❌ 源目录不存在: {source_dir}")
        return False
    else:
        print(f"✅ 源目录存在: {source_dir}")
    
    # 检查主程序文件
    run_app_path = source_dir / "run_app.py"
    if not run_app_path.exists():
        print(f"❌ 主程序文件不存在: {run_app_path}")
        return False
    else:
        print(f"✅ 主程序文件存在: {run_app_path}")
    
    # 检查Python环境
    try:
        import tkinter
        print("✅ tkinter 模块可用")
    except ImportError:
        print("❌ tkinter 模块不可用")
        return False
    
    return True

def test_directories():
    """测试目录创建"""
    print("\n测试目录创建...")
    
    target_dir = Path(r"D:\多平台存稿")
    package_dir = Path(r"D:\打包程序")
    
    try:
        # 创建目录（如果不存在）
        target_dir.mkdir(parents=True, exist_ok=True)
        package_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"✅ 目标目录可创建: {target_dir}")
        print(f"✅ 打包目录可创建: {package_dir}")
        return True
        
    except Exception as e:
        print(f"❌ 目录创建失败: {e}")
        return False

def test_pyinstaller():
    """测试PyInstaller"""
    print("\n测试PyInstaller...")
    
    try:
        import PyInstaller
        print(f"✅ PyInstaller 已安装，版本: {PyInstaller.__version__}")
        return True
    except ImportError:
        print("⚠️ PyInstaller 未安装，打包时会自动安装")
        return True

def main():
    """主测试函数"""
    print("=" * 50)
    print("多平台存稿工具打包器 - 环境测试")
    print("=" * 50)
    
    tests = [
        ("依赖检查", test_dependencies),
        ("目录创建", test_directories),
        ("PyInstaller检查", test_pyinstaller),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试出错: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✅ 所有测试通过，可以开始打包")
    else:
        print("⚠️ 部分测试失败，请检查环境配置")
    
    print("=" * 50)
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
