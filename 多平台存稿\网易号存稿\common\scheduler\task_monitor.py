"""
任务监控和日志记录模块
负责监控任务执行状态、记录执行日志、提供性能统计
"""

import datetime
import threading
import time
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from collections import defaultdict, deque

from .task_model import ScheduledTask, TaskStatus
from .time_utils import TimeUtils


@dataclass
class TaskExecutionLog:
    """任务执行日志记录"""
    task_id: str
    task_name: str
    execution_time: datetime.datetime
    status: TaskStatus
    duration: float
    error_message: Optional[str] = None
    result_data: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'task_id': self.task_id,
            'task_name': self.task_name,
            'execution_time': self.execution_time.isoformat(),
            'status': self.status.value,
            'duration': self.duration,
            'error_message': self.error_message,
            'result_data': self.result_data
        }


@dataclass
class TaskPerformanceMetrics:
    """任务性能指标"""
    task_id: str
    task_name: str
    total_executions: int = 0
    successful_executions: int = 0
    failed_executions: int = 0
    average_duration: float = 0.0
    min_duration: float = float('inf')
    max_duration: float = 0.0
    last_execution: Optional[datetime.datetime] = None
    last_success: Optional[datetime.datetime] = None
    last_failure: Optional[datetime.datetime] = None
    
    def update_metrics(self, duration: float, success: bool, execution_time: datetime.datetime):
        """更新性能指标"""
        self.total_executions += 1
        self.last_execution = execution_time
        
        if success:
            self.successful_executions += 1
            self.last_success = execution_time
        else:
            self.failed_executions += 1
            self.last_failure = execution_time
        
        # 更新持续时间统计
        self.min_duration = min(self.min_duration, duration)
        self.max_duration = max(self.max_duration, duration)
        
        # 计算平均持续时间
        if self.total_executions > 0:
            total_duration = (self.average_duration * (self.total_executions - 1)) + duration
            self.average_duration = total_duration / self.total_executions
    
    def get_success_rate(self) -> float:
        """获取成功率"""
        if self.total_executions == 0:
            return 0.0
        return self.successful_executions / self.total_executions
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'task_id': self.task_id,
            'task_name': self.task_name,
            'total_executions': self.total_executions,
            'successful_executions': self.successful_executions,
            'failed_executions': self.failed_executions,
            'success_rate': self.get_success_rate(),
            'average_duration': self.average_duration,
            'min_duration': self.min_duration if self.min_duration != float('inf') else 0.0,
            'max_duration': self.max_duration,
            'last_execution': self.last_execution.isoformat() if self.last_execution else None,
            'last_success': self.last_success.isoformat() if self.last_success else None,
            'last_failure': self.last_failure.isoformat() if self.last_failure else None
        }


class TaskMonitor:
    """任务监控器"""
    
    def __init__(self, logger: Optional[Callable] = None, max_logs: int = 1000):
        """
        初始化任务监控器
        
        Args:
            logger: 日志记录函数
            max_logs: 最大日志记录数量
        """
        self.logger = logger or print
        self.max_logs = max_logs
        
        # 执行日志（使用deque限制大小）
        self.execution_logs: deque[TaskExecutionLog] = deque(maxlen=max_logs)
        
        # 性能指标
        self.performance_metrics: Dict[str, TaskPerformanceMetrics] = {}
        
        # 实时监控数据
        self.running_tasks: Dict[str, datetime.datetime] = {}  # 正在运行的任务
        self.task_start_times: Dict[str, datetime.datetime] = {}  # 任务开始时间
        
        # 统计数据
        self.daily_stats: Dict[str, Dict[str, int]] = defaultdict(lambda: defaultdict(int))
        self.hourly_stats: Dict[str, Dict[int, int]] = defaultdict(lambda: defaultdict(int))
        
        # 线程锁
        self._lock = threading.Lock()
        
        self.logger("✅ 任务监控器初始化完成")
    
    def task_started(self, task: ScheduledTask):
        """记录任务开始执行"""
        with self._lock:
            start_time = TimeUtils.get_beijing_now()
            self.running_tasks[task.id] = start_time
            self.task_start_times[task.id] = start_time
            
            self.logger(f"🚀 任务开始执行: {task.name} (ID: {task.id})")
    
    def task_completed(self, task: ScheduledTask, success: bool, duration: float, 
                      error_message: str = None, result_data: Dict[str, Any] = None):
        """记录任务执行完成"""
        with self._lock:
            execution_time = TimeUtils.get_beijing_now()
            
            # 从运行任务列表中移除
            self.running_tasks.pop(task.id, None)
            
            # 创建执行日志
            status = TaskStatus.COMPLETED if success else TaskStatus.FAILED
            log_entry = TaskExecutionLog(
                task_id=task.id,
                task_name=task.name,
                execution_time=execution_time,
                status=status,
                duration=duration,
                error_message=error_message,
                result_data=result_data
            )
            
            # 添加到执行日志
            self.execution_logs.append(log_entry)
            
            # 更新性能指标
            if task.id not in self.performance_metrics:
                self.performance_metrics[task.id] = TaskPerformanceMetrics(
                    task_id=task.id,
                    task_name=task.name
                )
            
            self.performance_metrics[task.id].update_metrics(duration, success, execution_time)
            
            # 更新统计数据
            date_key = execution_time.strftime("%Y-%m-%d")
            hour_key = execution_time.hour
            
            self.daily_stats[date_key]['total'] += 1
            self.hourly_stats[date_key][hour_key] += 1
            
            if success:
                self.daily_stats[date_key]['success'] += 1
                self.logger(f"✅ 任务执行成功: {task.name} (耗时: {duration:.2f}秒)")
            else:
                self.daily_stats[date_key]['failed'] += 1
                self.logger(f"❌ 任务执行失败: {task.name} - {error_message}")
    
    def task_cancelled(self, task: ScheduledTask):
        """记录任务被取消"""
        with self._lock:
            # 从运行任务列表中移除
            start_time = self.running_tasks.pop(task.id, None)
            self.task_start_times.pop(task.id, None)
            
            if start_time:
                duration = (TimeUtils.get_beijing_now() - start_time).total_seconds()
                
                # 创建取消日志
                log_entry = TaskExecutionLog(
                    task_id=task.id,
                    task_name=task.name,
                    execution_time=TimeUtils.get_beijing_now(),
                    status=TaskStatus.CANCELLED,
                    duration=duration,
                    error_message="任务被用户取消"
                )
                
                self.execution_logs.append(log_entry)
                
                self.logger(f"🚫 任务已取消: {task.name}")
    
    def get_running_tasks(self) -> List[Dict[str, Any]]:
        """获取正在运行的任务列表"""
        with self._lock:
            running_list = []
            current_time = TimeUtils.get_beijing_now()
            
            for task_id, start_time in self.running_tasks.items():
                duration = (current_time - start_time).total_seconds()
                
                # 获取任务名称
                task_name = "未知任务"
                if task_id in self.performance_metrics:
                    task_name = self.performance_metrics[task_id].task_name
                
                running_list.append({
                    'task_id': task_id,
                    'task_name': task_name,
                    'start_time': start_time.isoformat(),
                    'running_duration': duration
                })
            
            return running_list
    
    def get_recent_logs(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取最近的执行日志"""
        with self._lock:
            recent_logs = list(self.execution_logs)[-limit:]
            return [log.to_dict() for log in reversed(recent_logs)]
    
    def get_task_performance(self, task_id: str = None) -> Dict[str, Any]:
        """获取任务性能指标"""
        with self._lock:
            if task_id:
                metrics = self.performance_metrics.get(task_id)
                return metrics.to_dict() if metrics else {}
            else:
                return {
                    task_id: metrics.to_dict() 
                    for task_id, metrics in self.performance_metrics.items()
                }
    
    def get_daily_statistics(self, days: int = 7) -> Dict[str, Dict[str, int]]:
        """获取每日统计数据"""
        with self._lock:
            current_date = TimeUtils.get_beijing_now().date()
            stats = {}
            
            for i in range(days):
                date = current_date - datetime.timedelta(days=i)
                date_key = date.strftime("%Y-%m-%d")
                stats[date_key] = dict(self.daily_stats.get(date_key, {'total': 0, 'success': 0, 'failed': 0}))
            
            return stats
    
    def get_hourly_statistics(self, date: str = None) -> Dict[int, int]:
        """获取指定日期的小时统计数据"""
        with self._lock:
            if not date:
                date = TimeUtils.get_beijing_now().strftime("%Y-%m-%d")
            
            return dict(self.hourly_stats.get(date, {}))
    
    def get_system_health(self) -> Dict[str, Any]:
        """获取系统健康状态"""
        with self._lock:
            current_time = TimeUtils.get_beijing_now()
            
            # 计算总体统计
            total_tasks = len(self.performance_metrics)
            total_executions = sum(m.total_executions for m in self.performance_metrics.values())
            total_successes = sum(m.successful_executions for m in self.performance_metrics.values())
            total_failures = sum(m.failed_executions for m in self.performance_metrics.values())
            
            # 计算平均成功率
            if total_executions > 0:
                overall_success_rate = total_successes / total_executions
            else:
                overall_success_rate = 0.0
            
            # 检查长时间运行的任务
            long_running_tasks = []
            for task_id, start_time in self.running_tasks.items():
                duration = (current_time - start_time).total_seconds()
                if duration > 3600:  # 超过1小时
                    long_running_tasks.append({
                        'task_id': task_id,
                        'duration': duration
                    })
            
            # 检查最近的失败任务
            recent_failures = []
            for log in reversed(list(self.execution_logs)[-20:]):
                if log.status == TaskStatus.FAILED:
                    recent_failures.append({
                        'task_name': log.task_name,
                        'execution_time': log.execution_time.isoformat(),
                        'error_message': log.error_message
                    })
                    if len(recent_failures) >= 5:
                        break
            
            return {
                'total_tasks': total_tasks,
                'running_tasks': len(self.running_tasks),
                'total_executions': total_executions,
                'successful_executions': total_successes,
                'failed_executions': total_failures,
                'overall_success_rate': overall_success_rate,
                'long_running_tasks': long_running_tasks,
                'recent_failures': recent_failures,
                'log_count': len(self.execution_logs),
                'last_update': current_time.isoformat()
            }
    
    def cleanup_old_data(self, days: int = 30):
        """清理旧数据"""
        with self._lock:
            cutoff_date = TimeUtils.get_beijing_now() - datetime.timedelta(days=days)
            
            # 清理旧的日志记录
            old_logs = []
            for log in self.execution_logs:
                if log.execution_time < cutoff_date:
                    old_logs.append(log)
            
            for log in old_logs:
                self.execution_logs.remove(log)
            
            # 清理旧的统计数据
            old_dates = []
            for date_key in self.daily_stats.keys():
                try:
                    date = datetime.datetime.strptime(date_key, "%Y-%m-%d").date()
                    if date < cutoff_date.date():
                        old_dates.append(date_key)
                except ValueError:
                    continue
            
            for date_key in old_dates:
                del self.daily_stats[date_key]
                if date_key in self.hourly_stats:
                    del self.hourly_stats[date_key]
            
            if old_logs or old_dates:
                self.logger(f"🧹 清理了 {len(old_logs)} 条旧日志和 {len(old_dates)} 天的旧统计数据")
    
    def export_logs(self, start_date: str = None, end_date: str = None) -> List[Dict[str, Any]]:
        """导出日志数据"""
        with self._lock:
            logs = []
            
            for log in self.execution_logs:
                # 日期过滤
                if start_date:
                    try:
                        start = datetime.datetime.strptime(start_date, "%Y-%m-%d").date()
                        if log.execution_time.date() < start:
                            continue
                    except ValueError:
                        pass
                
                if end_date:
                    try:
                        end = datetime.datetime.strptime(end_date, "%Y-%m-%d").date()
                        if log.execution_time.date() > end:
                            continue
                    except ValueError:
                        pass
                
                logs.append(log.to_dict())
            
            return logs
