"""
原始存稿上传模块 - 直接使用源文件中的存稿相关代码
"""

import os
import time
import traceback
import random
from datetime import datetime
from typing import Dict, Any, Optional, Callable, List
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains

from 网易号存稿.common.utils import random_sleep
from 网易号存稿.common.constants import (
    NETEASE_PUBLISH_URL,
    VIDEO_UPLOAD_AREA,
    VIDEO_UPLOAD_SUCCESS,
    UPLOAD_COVER_BUTTON,
    LOCAL_UPLOAD_BUTTON,
    COVER_UPLOAD_INPUT,
    COVER_CONFIRM_BUTTON,
    TITLE_INPUT,
    DECLARATION_BUTTON,
    DECLARATION_CONTENT,
    CATEGORY_INPUT,
    TAG_INPUT,
    SAVE_DRAFT_BUTTON
)
# 导入所需的库
try:
    import jieba
except ImportError:
    pass

# 忽略未使用的导入警告
_ = Dict, Any, List, NoSuchElementException, random_sleep, LOCAL_UPLOAD_BUTTON, random, Keys, ActionChains, TITLE_INPUT, TimeoutException, VIDEO_UPLOAD_SUCCESS, jieba

class OriginalVideoUploader:
    """原始视频上传类，直接使用源文件中的存稿相关代码"""

    def __init__(self, driver: webdriver.Chrome, log_callback: Callable = None, headless_mode: bool = False, screenshots_dir: str = None):
        """
        初始化原始视频上传器

        Args:
            driver: 浏览器驱动
            log_callback: 日志回调函数
            headless_mode: 是否为无头模式
            screenshots_dir: 截图保存目录
        """
        self.driver = driver
        self.log_callback = log_callback
        self.headless_mode = headless_mode
        self.screenshots_dir = screenshots_dir
        self.last_error = ""  # 最后一次错误信息
        self.last_screenshot = ""  # 最后一次截图路径

    def log(self, message: str) -> None:
        """
        记录日志

        Args:
            message: 日志消息
        """
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def draft_video(self, video_path: str, cover_path: Optional[str] = None) -> bool:
        """
        使用指定驱动上传视频并存稿，完整实现所有必要步骤

        Args:
            video_path: 视频文件路径
            cover_path: 封面文件路径

        Returns:
            是否成功存稿
        """
        try:
            # 打开存稿页面
            self.driver.get(NETEASE_PUBLISH_URL)
            self.log("正在打开网易视频上传页面")

            # 等待页面加载完成
            WebDriverWait(self.driver, 30).until(
                EC.presence_of_element_located((By.XPATH, VIDEO_UPLOAD_AREA))
            )

            # 上传视频文件
            result = self.upload_video(video_path)
            if not result:
                self.log("❌ 视频上传失败")
                return False

            # 等待视频上传完成
            self.log("⏳ 等待视频上传完成...")

            # 使用改进的检测方法
            success = self._wait_for_upload_completion_improved(timeout=180)

            if not success:
                # 截图保存并记录错误
                if self.headless_mode:
                    self.log("无头模式下视频上传失败，正在保存截图...")

                    # 确定截图目录
                    screenshots_dir = ""
                    default_screenshots_dir = "D:/网易号全自动/截图"  # 默认截图目录

                    # 优先使用实例的screenshots_dir
                    if hasattr(self, 'screenshots_dir') and self.screenshots_dir:
                        screenshots_dir = self.screenshots_dir
                        self.log(f"使用实例配置的截图目录: {screenshots_dir}")
                    else:
                        # 如果没有设置，使用默认目录
                        screenshots_dir = default_screenshots_dir
                        self.log(f"使用默认截图目录: {screenshots_dir}")

                    # 确保截图目录存在
                    os.makedirs(screenshots_dir, exist_ok=True)

                    # 生成截图文件名
                    timestamp = int(time.time())
                    screenshot_path = os.path.join(screenshots_dir, f"upload_failed_{timestamp}.png")

                    # 保存截图
                    self.driver.save_screenshot(screenshot_path)

                    # 确保截图路径是绝对路径
                    if not os.path.isabs(screenshot_path):
                        screenshot_path = os.path.abspath(screenshot_path)

                    if os.path.exists(screenshot_path):
                        self.log(f"已保存失败截图到: {screenshot_path}")
                        # 保存截图路径到实例变量，以便外部访问
                        self.last_screenshot = screenshot_path

                # 记录失败原因
                self.log("❌ 视频上传失败或超时")
                return False

            # 视频上传成功后，确保页面稳定
            self.log("视频上传成功，等待页面稳定...")
            time.sleep(2)  # 增加缓冲时间确保界面加载完成

            # 获取视频标题（仅用于日志记录和标签生成）
            video_title = os.path.splitext(os.path.basename(video_path))[0]
            self.log(f"视频标题: {video_title}")

            # 上传封面
            if cover_path:
                try:
                    self.log("准备上传封面，等待页面完全加载...")
                    # 再次确认页面已加载完成
                    time.sleep(1)

                    # 针对无头模式和可视化模式分别处理
                    if self.headless_mode:
                        # 无头模式下的封面上传逻辑
                        self.log("无头模式: 尝试上传封面")
                        upload_cover_success = False  # 添加变量定义

                        # 方法1: 直接通过XPATH找到上传封面元素并点击
                        try:
                            # 使用JavaScript查找和点击上传封面按钮
                            js_find_cover_btn = """
                                // 首先通过常规XPATH查找上传按钮
                                let btn = document.evaluate(
                                    '//*[@id="root"]/div/div[2]/div/div[2]/div/div/div[1]/div/div[2]/div[1]/div[2]/div[1]',
                                    document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null
                                ).singleNodeValue;

                                // 如果找不到，则查找包含"上传图片"文本的元素
                                if (!btn) {
                                    const elements = document.querySelectorAll('*');
                                    for (let el of elements) {
                                        if (el.textContent && el.textContent.includes('上传图片') &&
                                            (el.tagName === 'BUTTON' || el.tagName === 'DIV')) {
                                            btn = el;
                                            break;
                                        }
                                    }
                                }

                                // 返回找到的元素
                                return btn;
                            """
                            cover_btn = self.driver.execute_script(js_find_cover_btn)

                            if cover_btn:
                                self.driver.execute_script("arguments[0].click();", cover_btn)
                                self.log("无头模式: 已找到并点击上传封面按钮")

                                # 等待上传对话框出现
                                time.sleep(1)

                                # 查找本地上传按钮 - 使用JavaScript
                                js_find_local_upload = """
                                    // 尝试查找本地上传按钮
                                    let uploadBtn = null;

                                    // 方法2: 查找包含"本地上传"文本的元素
                                    if (!uploadBtn) {
                                        const elements = document.querySelectorAll('*');
                                        for (let el of elements) {
                                            if (el.textContent && el.textContent.includes('本地上传')) {
                                                uploadBtn = el;
                                                break;
                                            }
                                        }
                                    }

                                    // 返回找到的按钮
                                    return uploadBtn;
                                """

                                local_upload_btn = self.driver.execute_script(js_find_local_upload)

                                if local_upload_btn:
                                    self.driver.execute_script("arguments[0].click();", local_upload_btn)
                                    self.log("无头模式: 已找到并点击本地上传按钮")

                                    # 寻找文件上传输入框
                                    try:
                                        # 等待文件上传输入框出现
                                        time.sleep(1)
                                        # 查找封面上传输入框
                                        cover_input = None
                                        try:
                                            cover_input = self.driver.find_element(By.XPATH, COVER_UPLOAD_INPUT)
                                        except:
                                            # 如果找不到，尝试查找所有文件输入框
                                            file_inputs = self.driver.find_elements(By.CSS_SELECTOR, 'input[type="file"]')
                                            if file_inputs:
                                                cover_input = file_inputs[0]

                                        if cover_input:
                                            # 使用封面上传输入框上传文件
                                            cover_input.send_keys(cover_path.replace('\\', '/'))
                                            self.log("无头模式: 封面文件已上传")

                                            # 等待图片缩略图出现，然后点击确认按钮
                                            self.log("无头模式: 等待图片缩略图出现")

                                            # 使用新方法检查图片是否加载，包括检查{图片中}文本和图片src
                                            self.log("无头模式: 等待图片加载完成...")

                                            # 使用新的等待图片加载方法
                                            cropper_found = self.wait_for_image_loaded(timeout=10)

                                            if cropper_found:
                                                self.log("无头模式: ✅ 检测到图片已完全加载")
                                            else:
                                                self.log("无头模式: ⚠️ 未检测到完全加载的图片，但仍将继续")

                                            if not cropper_found:
                                                self.log("无头模式: 警告 - 未检测到完全加载的图片缩略图，但仍将尝试点击确认按钮")
                                                # 额外等待时间，以防图片仍在加载
                                                time.sleep(2)

                                            # 等待缩略图完全加载
                                            time.sleep(0.5)

                                            js_click_confirm = """
                                                // 尝试查找确认按钮
                                                let confirmBtn = null;

                                                // 查找包含"确定"或"确认"文本的按钮
                                                const buttons = document.querySelectorAll('button');
                                                for (let btn of buttons) {
                                                    if (btn.textContent &&
                                                        (btn.textContent.includes('确定') ||
                                                         btn.textContent.includes('确认'))) {
                                                        confirmBtn = btn;
                                                        break;
                                                    }
                                                }

                                                // 如果找到了确认按钮，点击它
                                                if (confirmBtn) {
                                                    confirmBtn.click();
                                                    return true;
                                                }

                                                // 如果没找到确认按钮，尝试按Escape键关闭对话框
                                                return false;
                                            """

                                            confirm_clicked = self.driver.execute_script(js_click_confirm)
                                            if confirm_clicked:
                                                self.log("无头模式: 已点击确认按钮")
                                            else:
                                                self.log("无头模式: 未找到确认按钮，尝试按ESC关闭对话框")
                                                ActionChains(self.driver).send_keys(Keys.ESCAPE).perform()
                                        else:
                                            self.log("无头模式: 未找到封面上传输入框")
                                    except Exception as e:
                                        self.log(f"无头模式: 上传封面文件时出错: {str(e)}")
                                else:
                                    self.log("无头模式: 未找到本地上传按钮")
                            else:
                                self.log("无头模式: 未找到上传封面按钮")
                        except Exception as e:
                            self.log(f"无头模式: 上传封面过程中出错: {str(e)}")
                    else:
                        # 可视化模式下的封面上传逻辑(原有代码)
                        # 尝试最多3次查找并点击上传封面按钮
                        upload_cover_success = False
                        for attempt in range(1):
                            try:
                                # 使用不同的等待策略查找上传封面按钮
                                try:
                                    # 方法1: 使用XPath直接定位
                                    wait = WebDriverWait(self.driver, 10)
                                    upload_cover_btn = wait.until(EC.element_to_be_clickable((By.XPATH, UPLOAD_COVER_BUTTON)))
                                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", upload_cover_btn)
                                    time.sleep(0.5)
                                    self.driver.execute_script("arguments[0].click();", upload_cover_btn)
                                    self.log(f"尝试 {attempt+1}: 已通过XPath找到并点击上传封面按钮")
                                    upload_cover_success = True
                                    break
                                except:
                                    # 方法2: 通过封面区域的识别
                                    elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '封面') or contains(@alt, '封面')]")
                                    if elements:
                                        for elem in elements:
                                            try:
                                                self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", elem)
                                                time.sleep(0.5)
                                                # 先尝试点击元素本身
                                                self.driver.execute_script("arguments[0].click();", elem)
                                                self.log(f"尝试 {attempt+1}: 已通过'封面'文本找到并点击相关元素")
                                                upload_cover_success = True
                                                break
                                            except:
                                                # 如果元素本身不可点击，尝试点击其父元素
                                                try:
                                                    parent = self.driver.execute_script("return arguments[0].parentNode;", elem)
                                                    self.driver.execute_script("arguments[0].click();", parent)
                                                    self.log(f"尝试 {attempt+1}: 通过点击'封面'元素的父元素上传封面")
                                                    upload_cover_success = True
                                                    break
                                                except:
                                                    continue

                                if not upload_cover_success:
                                    self.log(f"尝试 {attempt+1}: 未能通过常规方法找到上传封面按钮")

                            except Exception as e:
                                self.log(f"尝试 {attempt+1}: 点击上传封面按钮时出错: {str(e)}")

                            if upload_cover_success:
                                break

                            # 如果失败，等待并重试
                            time.sleep(1)

                        if not upload_cover_success and not self.headless_mode:
                            self.log("多次尝试后仍无法点击上传封面按钮，将使用默认封面")
                            # 继续后续步骤而不是直接返回，因为即使没有自定义封面，存稿仍可以进行
                        else:
                            # 点击成功，执行后续的封面上传步骤
                            # 点击本地上传按钮
                            if not self.headless_mode:  # 在可视化模式下继续执行原有逻辑
                                self.log("准备点击本地上传按钮")
                                try:
                                    # 等待对话框加载
                                    time.sleep(2)

                                    # 通过文本内容直接查找"本地上传"按钮
                                    elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '本地上传')]")
                                    if elements:
                                        self.driver.execute_script("arguments[0].click();", elements[0])
                                        self.log("已通过文本内容找到并点击本地上传按钮")

                                        # 上传封面
                                        try:
                                            wait = WebDriverWait(self.driver, 10)
                                            cover_input = wait.until(EC.presence_of_element_located((By.XPATH, COVER_UPLOAD_INPUT)))
                                            self.upload_file(cover_path, cover_input)
                                            self.log("封面上传成功")
                                            # 不需要设置变量，只记录日志

                                            # 等待图片缩略图出现，然后点击确认按钮
                                            self.log("等待图片缩略图出现，然后点击确认按钮")
                                            try:
                                                # 等待图片缩略图元素出现，最多等待10秒
                                                wait = WebDriverWait(self.driver, 10)
                                                cropper_input = wait.until(
                                                    EC.presence_of_element_located((By.XPATH, '//*[@id="cropper-input"]'))
                                                )
                                                self.log("✅ 检测到图片缩略图元素已出现")

                                                # 检查图片是否实际加载完成
                                                try:
                                                    # 查找图片元素
                                                    img_elements = cropper_input.find_elements(By.TAG_NAME, "img")
                                                    if img_elements:
                                                        img_element = img_elements[0]

                                                        # 等待图片加载完成
                                                        if img_element.is_displayed():
                                                            # 使用JavaScript检查图片是否完全加载
                                                            is_complete = self.driver.execute_script(
                                                                "return arguments[0].complete && "
                                                                "typeof arguments[0].naturalWidth != 'undefined' && "
                                                                "arguments[0].naturalWidth > 0", img_element
                                                            )

                                                            if is_complete:
                                                                self.log("✅ 图片缩略图已完全加载")
                                                            else:
                                                                self.log("⚠️ 图片缩略图元素存在但图像尚未加载完成")
                                                                # 等待额外时间让图片加载
                                                                time.sleep(2)
                                                        else:
                                                            self.log("⚠️ 图片缩略图元素存在但不可见")
                                                            # 等待额外时间让图片显示
                                                            time.sleep(2)
                                                    else:
                                                        self.log("⚠️ 未找到图片元素，可能是裁剪区域尚未初始化")
                                                        # 等待额外时间
                                                        time.sleep(2)
                                                except Exception as e:
                                                    self.log(f"⚠️ 检测图片加载状态时出错: {str(e)}")
                                                    # 等待额外时间以防万一
                                                    time.sleep(2)

                                                # 等待缩略图完全加载
                                                time.sleep(0.5)

                                                # 尝试通过XPath找到确认按钮
                                                confirm_button = wait.until(EC.element_to_be_clickable((By.XPATH, COVER_CONFIRM_BUTTON)))
                                                self.driver.execute_script("arguments[0].scrollIntoView(true);", confirm_button)
                                                self.driver.execute_script("arguments[0].click();", confirm_button)
                                                self.log("已点击确认按钮（通过XPath）")
                                            except Exception as e:
                                                self.log("通过XPath点击确认按钮失败，立即尝试备用方法...")

                                                # 添加截图功能
                                                if self.headless_mode:
                                                    self.log("无头模式下点击确认按钮失败，正在保存截图...")
                                                    screenshot_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "screenshots")
                                                    if not os.path.exists(screenshot_dir):
                                                        os.makedirs(screenshot_dir)
                                                    screenshot_path = os.path.join(screenshot_dir, f"confirm_failed_{int(time.time())}.png")
                                                    self.driver.save_screenshot(screenshot_path)
                                                    if os.path.exists(screenshot_path):
                                                        self.log(f"已保存失败截图到: {screenshot_path}")

                                            # 备用方法：通过文本内容查找确认按钮
                                            try:
                                                # 查找包含"确定"或"确认"文本的按钮
                                                confirm_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(), '确定') or contains(text(), '确认')]")
                                                if confirm_buttons:
                                                    # 尝试点击找到的按钮
                                                    click_successful = False
                                                    for button in confirm_buttons:
                                                        try:
                                                            self.driver.execute_script("arguments[0].scrollIntoView(true);", button)
                                                            time.sleep(0.5)
                                                            self.driver.execute_script("arguments[0].click();", button)
                                                            self.log("已点击确认按钮（通过文本内容查找）")
                                                            click_successful = True
                                                            break
                                                        except Exception:
                                                            continue

                                                    if not click_successful:
                                                        # 如果点击失败，尝试查找对话框中的按钮
                                                        self.log("尝试通过对话框查找确认按钮")
                                                        dialogs = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'dialog') or contains(@class, 'modal')]")
                                                        if dialogs:
                                                            for dialog in dialogs:
                                                                try:
                                                                    # 查找对话框中的所有按钮
                                                                    buttons = dialog.find_elements(By.TAG_NAME, "button")
                                                                    if buttons:
                                                                        # 尝试点击最后一个按钮（通常是确认按钮）
                                                                        self.driver.execute_script("arguments[0].click();", buttons[-1])
                                                                        self.log("已点击对话框中的最后一个按钮")
                                                                        click_successful = True
                                                                        break
                                                                except Exception:
                                                                    continue

                                                    if not click_successful:
                                                        self.log("警告：找到了确认按钮，但无法点击")
                                                else:
                                                    self.log("警告：未找到包含'确定'或'确认'文本的按钮")

                                                # 无论确认按钮点击是否成功，等待一会儿让页面稳定，然后尝试检查和关闭可能存在的遮挡元素
                                                time.sleep(1)
                                            except Exception as inner_e:
                                                self.log(f"使用备用方法点击确认按钮失败: {str(inner_e)}")

                                                # 最后的应急措施：尝试按Escape键关闭弹窗
                                                try:
                                                    ActionChains(self.driver).send_keys(Keys.ESCAPE).perform()
                                                    self.log("已尝试按ESC键关闭弹窗")
                                                    time.sleep(1)
                                                except:
                                                    pass
                                        except Exception as e:
                                            self.log(f"封面上传失败: {str(e)}")
                                    else:
                                        self.log("未找到本地上传按钮")
                                except Exception as e:
                                    self.log(f"点击本地上传按钮失败: {str(e)}")
                except Exception as e:
                    self.log(f"❌ 上传封面时发生错误: {str(e)}")
                    # 即使封面上传失败，也继续后续步骤
            else:
                self.log("未提供封面路径，将使用默认封面")

            # 设置声明
            self.set_declaration()

            # 设置分类
            self.set_category()

            # 设置标签
            self.set_tags(video_title)

            # 滚动到页面底部
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(1)

            # 保存草稿
            try:
                # 尝试多种方法找到并点击保存草稿按钮
                try:
                    save_draft_btn = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, SAVE_DRAFT_BUTTON))
                    )
                    self.driver.execute_script("arguments[0].click();", save_draft_btn)
                    self.log("✅ 已点击保存草稿按钮")
                    # 强制等待页面响应
                    time.sleep(2)
                except:
                    # 备用方法：通过文本查找
                    draft_buttons = self.driver.find_elements(By.XPATH, "//*[contains(text(), '存草稿') or contains(text(), '保存草稿')]")
                    if draft_buttons:
                        for btn in draft_buttons:
                            try:
                                self.driver.execute_script("arguments[0].click();", btn)
                                self.log("✅ 通过文本查找点击了保存草稿按钮")
                                # 强制等待页面响应
                                time.sleep(3)
                                break
                            except:
                                continue
                    else:
                        self.log("❌ 未找到保存草稿按钮")
                        return False

                # 等待存稿成功提示
                wait_time = 15  # 等待15秒
                success = False
                for _ in range(wait_time):
                    # 检查页面源码中是否包含成功标志
                    page_source = self.driver.page_source
                    if "草稿箱" in page_source or "存稿成功" in page_source or "视频内容已存入草稿箱" in page_source or "保存成功" in page_source:
                        success = True
                        break

                    # 检查是否有成功提示元素
                    try:
                        success_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '保存成功') or contains(text(), '草稿箱') or contains(text(), '存稿成功')]")
                        if success_elements:
                            for elem in success_elements:
                                if elem.is_displayed():
                                    success = True
                                    break
                        if success:
                            break
                    except:
                        pass
                    time.sleep(1)  # 每秒检查一次

                # 保存截图用于调试
                screenshot_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "screenshots")
                if not os.path.exists(screenshot_dir):
                    os.makedirs(screenshot_dir)
                self.driver.save_screenshot(os.path.join(screenshot_dir, f"draft_result_{int(time.time())}.png"))

                if success:
                    self.log("✅ 存稿成功！视频已保存到草稿箱")
                    # 这条消息是存稿成功的标志，在manager.py和processor.py中会检测这条消息来增加存稿成功数量
                    return True
                else:
                    # 使用多种方法检测存稿状态
                    popup_text_found = False
                    draft_saved = False

                    # 查找所有可能包含提示文字的元素
                    popup_elements = self.driver.find_elements(By.XPATH, "//span | //div | //p")
                    self.log(f"发现 {len(popup_elements)} 个可能包含提示文字的元素")

                    # 遍历所有元素查找"保存成功"或"保存失败"的文字
                    for element in popup_elements:
                        try:
                            element_text = element.text.strip()
                            if element_text:
                                if "保存成功" in element_text:
                                    self.log(f"✅ 检测到存稿成功提示: '{element_text}'")
                                    draft_saved = True
                                    popup_text_found = True
                                    break
                                elif "保存失败" in element_text:
                                    self.log(f"❌ 检测到存稿失败提示: '{element_text}'")
                                    draft_saved = False
                                    popup_text_found = True

                                    # 检测到存稿失败后，查找指定元素中的错误信息
                                    error_reason = "未知原因"
                                    try:
                                        # 查找多种可能包含错误信息的元素
                                        error_xpath_list = [
                                            '//*[@id="root"]/div/div[2]/div/div[1]/div[1]/div[2]/div/ul/li/div[3]',  # 原有的路径
                                            '//*[contains(text(), "操作失败") or contains(text(), "已超过") or contains(text(), "失败")]',  # 通用错误文本
                                            '//div[contains(@class, "error") or contains(@class, "alert")]',  # 错误提示框
                                            '//span[contains(text(), "操作失败") or contains(text(), "已超过")]'  # 特定错误文本
                                        ]

                                        # 循环查找所有可能的错误元素
                                        for xpath in error_xpath_list:
                                            error_elements = self.driver.find_elements(By.XPATH, xpath)
                                            for error_element in error_elements:
                                                if error_element.is_displayed():
                                                    error_text = error_element.text.strip()
                                                    # 放宽条件，只要有文本内容就记录为错误原因
                                                    if error_text and len(error_text) > 3:  # 确保文本有意义
                                                        error_reason = error_text
                                                        self.log(f"⚠️ 存稿失败原因: {error_reason}")
                                                        break
                                                if error_reason != "未知原因":
                                                    break

                                        # 如果仍未找到错误原因，使用更广泛的搜索方法
                                        if error_reason == "未知原因":
                                            # 检查页面中的所有文本元素，寻找关键字
                                            try:
                                                # 扩展错误关键词列表，提高匹配率
                                                error_keywords = ["操作失败", "已超过", "草稿数", "失败", "错误", "异常",
                                                                 "请重试", "不符合要求", "不支持", "不允许", "上传失败",
                                                                 "网络异常", "服务器", "超时", "格式不正确", "请稍后",
                                                                 "无法", "禁止", "限制", "审核", "违规", "敏感"]

                                                # 首先尝试使用关键词搜索
                                                for keyword in error_keywords:
                                                    elements = self.driver.find_elements(By.XPATH, f"//*[contains(text(), '{keyword}')]")
                                                    for element in elements:
                                                        try:
                                                            if element.is_displayed():
                                                                text = element.text.strip()
                                                                if text and len(text) < 100:  # 避免获取过长的文本
                                                                    error_reason = text
                                                                    self.log(f"⚠️ 通过关键字搜索找到存稿失败原因: {error_reason}")
                                                                    break
                                                        except:
                                                            continue
                                                    if error_reason != "未知原因":
                                                        break

                                                # 如果关键词搜索未找到，尝试获取所有可见文本元素
                                                if error_reason == "未知原因":
                                                    all_text_elements = self.driver.find_elements(By.XPATH, "//*[text()]")
                                                    for elem in all_text_elements:
                                                        try:
                                                            if elem.is_displayed():
                                                                text = elem.text.strip()
                                                                # 检查文本是否包含任何错误关键词
                                                                for keyword in error_keywords:
                                                                    if keyword in text:
                                                                        error_reason = text
                                                                        self.log(f"⚠️ 通过文本元素搜索找到存稿失败原因: {error_reason}")
                                                                        break
                                                                if error_reason != "未知原因":
                                                                    break
                                                        except:
                                                            continue
                                            except Exception as e:
                                                self.log(f"搜索错误信息时出错: {str(e)}")
                                    except Exception as e:
                                        self.log(f"获取存稿失败原因时出错: {str(e)}")

                                    # 尝试截图记录失败状态
                                    screenshot_path = ""
                                    try:
                                        # 确定截图目录
                                        screenshots_dir = ""
                                        default_screenshots_dir = "D:/网易号全自动/截图"  # 默认截图目录

                                        # 优先使用实例的screenshots_dir
                                        if hasattr(self, 'screenshots_dir') and self.screenshots_dir:
                                            screenshots_dir = self.screenshots_dir
                                            self.log(f"使用实例配置的截图目录: {screenshots_dir}")
                                        else:
                                            # 如果没有设置，使用默认目录
                                            screenshots_dir = default_screenshots_dir
                                            self.log(f"使用默认截图目录: {screenshots_dir}")

                                        # 确保截图目录存在
                                        os.makedirs(screenshots_dir, exist_ok=True)

                                        # 不再滚动页面，使用全屏模式确保所有元素可见
                                        self.log("准备截图：使用全屏模式确保所有元素可见")

                                        # 生成截图文件名
                                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                                        screenshot_file = os.path.join(screenshots_dir, f"draft_failed_{timestamp}.png")

                                        # 截图
                                        self.driver.save_screenshot(screenshot_file)
                                        self.log(f"已保存存稿失败截图: {screenshot_file}")

                                        # 确保截图路径是绝对路径
                                        if not os.path.isabs(screenshot_file):
                                            screenshot_file = os.path.abspath(screenshot_file)

                                        # 返回截图路径
                                        screenshot_path = screenshot_file
                                    except Exception as e:
                                        self.log(f"保存截图时出错: {str(e)}")

                                    # 如果仍未找到错误原因，使用默认错误信息
                                    if error_reason == "未知原因":
                                        error_reason = "存稿失败，未能找到具体原因，请查看截图" if screenshot_path else "存稿失败，未能找到具体原因"

                                    # 记录失败原因
                                    self.log(f"存稿失败原因: {error_reason}")

                                    # 设置返回值，包含错误原因和截图路径
                                    self.last_error = error_reason
                                    self.last_screenshot = screenshot_path

                                    return False
                        except Exception:
                            # 忽略获取文本时的错误
                            pass

                    # 如果没有找到明确的提示文字，记录日志并默认为失败
                    if not popup_text_found:
                        # 再次检查页面源码中是否包含成功标志
                        page_source = self.driver.page_source
                        if "草稿箱" in page_source or "存稿成功" in page_source or "视频内容已存入草稿箱" in page_source or "保存成功" in page_source:
                            self.log("✅ 在页面源码中检测到存稿成功标志")
                            return True

                        self.log("⚠️ 未检测到明确的'保存成功'或'保存失败'提示文字，默认为存稿失败")
                        draft_saved = False

                        # 即使没有明确的失败提示，也执行查找详细失败原因的代码
                        error_reason = "未知原因"
                        try:
                            # 查找多种可能包含错误信息的元素
                            error_xpath_list = [
                                '//*[@id="root"]/div/div[2]/div/div[1]/div[1]/div[2]/div/ul/li/div[3]',  # 原有的路径
                                '//*[contains(text(), "操作失败") or contains(text(), "已超过") or contains(text(), "失败")]',  # 通用错误文本
                                '//div[contains(@class, "error") or contains(@class, "alert")]',  # 错误提示框
                                '//span[contains(text(), "操作失败") or contains(text(), "已超过")]'  # 特定错误文本
                            ]

                            # 循环查找所有可能的错误元素
                            for xpath in error_xpath_list:
                                error_elements = self.driver.find_elements(By.XPATH, xpath)
                                for error_element in error_elements:
                                    if error_element.is_displayed():
                                        error_text = error_element.text.strip()
                                        if error_text and "操作失败" in error_text:
                                            error_reason = error_text
                                            self.log(f"⚠️ 存稿失败原因: {error_reason}")
                                            break
                                    if error_reason != "未知原因":
                                        break

                            # 如果仍未找到错误原因，使用更广泛的搜索方法
                            if error_reason == "未知原因":
                                # 扩展错误关键词列表，提高匹配率
                                error_keywords = ["操作失败", "已超过", "草稿数", "失败", "错误", "异常",
                                                 "请重试", "不符合要求", "不支持", "不允许", "上传失败",
                                                 "网络异常", "服务器", "超时", "格式不正确", "请稍后",
                                                 "无法", "禁止", "限制", "审核", "违规", "敏感"]

                                # 首先尝试使用关键词搜索
                                for keyword in error_keywords:
                                    elements = self.driver.find_elements(By.XPATH, f"//*[contains(text(), '{keyword}')]")
                                    for element in elements:
                                        try:
                                            if element.is_displayed():
                                                text = element.text.strip()
                                                if text and len(text) < 100:  # 避免获取过长的文本
                                                    error_reason = text
                                                    self.log(f"⚠️ 通过关键字搜索找到存稿失败原因: {error_reason}")
                                                    break
                                        except:
                                            continue
                                    if error_reason != "未知原因":
                                        break

                                # 如果关键词搜索未找到，尝试获取所有可见文本元素
                                if error_reason == "未知原因":
                                    all_text_elements = self.driver.find_elements(By.XPATH, "//*[text()]")
                                    for elem in all_text_elements:
                                        try:
                                            if elem.is_displayed():
                                                text = elem.text.strip()
                                                # 检查文本是否包含任何错误关键词
                                                for keyword in error_keywords:
                                                    if keyword in text:
                                                        error_reason = text
                                                        self.log(f"⚠️ 通过文本元素搜索找到存稿失败原因: {error_reason}")
                                                        break
                                                if error_reason != "未知原因":
                                                    break
                                        except:
                                            continue
                        except Exception as e:
                            self.log(f"获取存稿失败原因时出错: {str(e)}")

                        # 尝试截图记录失败状态
                        screenshot_path = ""
                        try:
                            # 确定截图目录
                            screenshots_dir = ""
                            default_screenshots_dir = "D:/网易号全自动/截图"  # 默认截图目录

                            # 优先使用实例的screenshots_dir
                            if hasattr(self, 'screenshots_dir') and self.screenshots_dir:
                                screenshots_dir = self.screenshots_dir
                                self.log(f"使用实例配置的截图目录: {screenshots_dir}")
                            else:
                                # 如果没有设置，使用默认目录
                                screenshots_dir = default_screenshots_dir
                                self.log(f"使用默认截图目录: {screenshots_dir}")

                            # 确保截图目录存在
                            os.makedirs(screenshots_dir, exist_ok=True)

                            # 不再滚动页面，使用全屏模式确保所有元素可见
                            self.log("准备截图：使用全屏模式确保所有元素可见")

                            # 生成截图文件名
                            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                            screenshot_file = os.path.join(screenshots_dir, f"draft_failed_{timestamp}.png")

                            # 截图
                            self.driver.save_screenshot(screenshot_file)
                            self.log(f"已保存存稿失败截图: {screenshot_file}")

                            # 确保截图路径是绝对路径
                            if not os.path.isabs(screenshot_file):
                                screenshot_file = os.path.abspath(screenshot_file)

                            # 返回截图路径
                            screenshot_path = screenshot_file
                        except Exception as e:
                            self.log(f"保存截图时出错: {str(e)}")

                        # 如果仍未找到错误原因，使用默认错误信息
                        if error_reason == "未知原因":
                            error_reason = "存稿失败，未能找到具体原因，请查看截图" if screenshot_path else "存稿失败，未能找到具体原因"

                        # 记录失败原因
                        self.log(f"存稿失败原因: {error_reason}")

                        # 设置返回值，包含错误原因和截图路径
                        self.last_error = error_reason
                        self.last_screenshot = screenshot_path

                    return draft_saved
            except Exception as e:
                self.log(f"❌ 保存草稿时出错: {str(e)}")
                return False

        except Exception as e:
            self.log(f"❌ 存稿过程中发生错误: {str(e)}")
            traceback.print_exc()
            return False

    def upload_video(self, video_path: str) -> bool:
        """使用指定驱动上传视频"""
        try:
            # 检查文件是否存在
            if not os.path.exists(video_path):
                self.log(f"❌ 视频文件不存在: {video_path}")
                return False

            # 确保文件路径是绝对路径
            video_path = os.path.abspath(video_path)
            self.log(f"准备上传视频: {video_path}")

            # 使用JavaScript模拟点击上传区域
            upload_area = WebDriverWait(self.driver, 30).until(
                EC.presence_of_element_located((By.XPATH, VIDEO_UPLOAD_AREA))
            )
            self.driver.execute_script("arguments[0].click();", upload_area)
            time.sleep(2)

            # 查找文件输入元素
            try:
                file_input = self.driver.find_element(By.XPATH, "//input[@type='file']")
                self.log("找到文件输入元素")
            except Exception as e:
                self.log(f"找不到文件输入元素，尝试备用方法: {str(e)}")
                # 备用方法：查找所有文件输入框
                file_inputs = self.driver.find_elements(By.CSS_SELECTOR, 'input[type="file"]')
                if file_inputs:
                    file_input = file_inputs[0]
                    self.log("使用备用方法找到文件输入元素")
                else:
                    # 如果还是找不到，创建一个新的文件输入元素
                    self.log("创建新的文件输入元素")
                    js_code = """
                        const input = document.createElement('input');
                        input.type = 'file';
                        input.style.position = 'absolute';
                        input.style.left = '-9999px';
                        input.style.opacity = '1';
                        input.style.display = 'block';
                        input.id = 'custom-file-input-' + Date.now();
                        document.body.appendChild(input);
                        return input;
                    """
                    file_input = self.driver.execute_script(js_code)

            # 格式化文件路径（处理Windows路径斜杠）
            video_path_formatted = video_path.replace('\\', '/')
            self.log(f"格式化后的文件路径: {video_path_formatted}")

            # 传入文件路径
            try:
                # 确保元素可见和可交互
                self.driver.execute_script("arguments[0].style.display = 'block'; arguments[0].style.opacity = '1';", file_input)
                file_input.send_keys(video_path_formatted)
                self.log(f"✅ 已选择视频文件: {os.path.basename(video_path)}")
                return True
            except Exception as e:
                self.log(f"❌ 发送文件路径失败: {str(e)}")
                # 尝试使用JavaScript设置文件
                try:
                    self.log("尝试使用JavaScript设置文件")
                    self.driver.execute_script(f"arguments[0].value = '{video_path_formatted}';", file_input)
                    self.driver.execute_script("arguments[0].dispatchEvent(new Event('change'));", file_input)
                    self.log(f"✅ 已通过JavaScript选择视频文件: {os.path.basename(video_path)}")
                    return True
                except Exception as js_error:
                    self.log(f"❌ JavaScript设置文件也失败: {str(js_error)}")
                    return False

        except Exception as e:
            self.log(f"❌ 上传视频时发生错误: {str(e)}")
            traceback.print_exc()
            return False

    def set_title(self, _: str) -> bool:
        """设置视频标题 (已弃用 - 根据源文件要求不再设置标题)"""
        self.log("标题设置功能已禁用，使用网站默认标题")
        return True

    def upload_cover(self, cover_path: str) -> bool:
        """上传视频封面"""
        try:
            self.log("准备上传封面，等待页面完全加载...")
            # 再次确认页面已加载完成
            time.sleep(1)

            # 针对无头模式和可视化模式分别处理
            if self.headless_mode:
                # 无头模式下的封面上传逻辑
                self.log("无头模式: 尝试上传封面")
                upload_cover_success = False  # 添加变量定义

                # 方法1: 直接通过XPATH找到上传封面元素并点击
                try:
                    # 使用JavaScript查找和点击上传封面按钮
                    js_find_cover_btn = """
                        // 首先通过常规XPATH查找上传按钮
                        let btn = document.evaluate(
                            '//*[@id="root"]/div/div[2]/div/div[2]/div/div/div[1]/div/div[2]/div[1]/div[2]/div[1]',
                            document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null
                        ).singleNodeValue;

                        // 如果找不到，则查找包含"上传图片"文本的元素
                        if (!btn) {
                            const elements = document.querySelectorAll('*');
                            for (let el of elements) {
                                if (el.textContent && el.textContent.includes('上传图片') &&
                                    (el.tagName === 'BUTTON' || el.tagName === 'DIV')) {
                                    btn = el;
                                    break;
                                }
                            }
                        }

                        // 返回找到的元素
                        return btn;
                    """
                    cover_btn = self.driver.execute_script(js_find_cover_btn)

                    if cover_btn:
                        self.driver.execute_script("arguments[0].click();", cover_btn)
                        self.log("无头模式: 已找到并点击上传封面按钮")

                        # 等待上传对话框出现
                        time.sleep(1)

                        # 查找本地上传按钮 - 使用JavaScript
                        js_find_local_upload = """
                            // 尝试查找本地上传按钮
                            let uploadBtn = null;

                            // 方法2: 查找包含"本地上传"文本的元素
                            if (!uploadBtn) {
                                const elements = document.querySelectorAll('*');
                                for (let el of elements) {
                                    if (el.textContent && el.textContent.includes('本地上传')) {
                                        uploadBtn = el;
                                        break;
                                    }
                                }
                            }

                            // 返回找到的按钮
                            return uploadBtn;
                        """

                        local_upload_btn = self.driver.execute_script(js_find_local_upload)

                        if local_upload_btn:
                            self.driver.execute_script("arguments[0].click();", local_upload_btn)
                            self.log("无头模式: 已找到并点击本地上传按钮")

                            # 寻找文件上传输入框
                            try:
                                # 等待文件上传输入框出现
                                time.sleep(1)
                                # 查找封面上传输入框
                                cover_input = None
                                try:
                                    cover_input = self.driver.find_element(By.XPATH, COVER_UPLOAD_INPUT)
                                except:
                                    # 如果找不到，尝试查找所有文件输入框
                                    file_inputs = self.driver.find_elements(By.CSS_SELECTOR, 'input[type="file"]')
                                    if file_inputs:
                                        cover_input = file_inputs[0]

                                if cover_input:
                                    # 使用封面上传输入框上传文件
                                    cover_input.send_keys(cover_path.replace('\\', '/'))
                                    self.log("无头模式: 封面文件已上传")

                                    # 等待图片缩略图出现，然后点击确认按钮
                                    self.log("无头模式: 等待图片缩略图出现")

                                    # 使用新方法检查图片是否加载，包括检查{图片中}文本和图片src
                                    self.log("无头模式: 等待图片加载完成...")

                                    # 使用新的等待图片加载方法
                                    cropper_found = self.wait_for_image_loaded(timeout=10)

                                    if cropper_found:
                                        self.log("无头模式: ✅ 检测到图片已完全加载")
                                    else:
                                        self.log("无头模式: ⚠️ 未检测到完全加载的图片，但仍将继续")

                                    if not cropper_found:
                                        self.log("无头模式: 警告 - 未检测到完全加载的图片缩略图，但仍将尝试点击确认按钮")
                                        # 额外等待时间，以防图片仍在加载
                                        time.sleep(2)

                                    # 等待缩略图完全加载
                                    time.sleep(0.5)

                                    js_click_confirm = """
                                        // 尝试查找确认按钮
                                        let confirmBtn = null;

                                        // 查找包含"确定"或"确认"文本的按钮
                                        const buttons = document.querySelectorAll('button');
                                        for (let btn of buttons) {
                                            if (btn.textContent &&
                                                (btn.textContent.includes('确定') ||
                                                 btn.textContent.includes('确认'))) {
                                                confirmBtn = btn;
                                                break;
                                            }
                                        }

                                        // 如果找到了确认按钮，点击它
                                        if (confirmBtn) {
                                            confirmBtn.click();
                                            return true;
                                        }

                                        // 如果没找到确认按钮，尝试按Escape键关闭对话框
                                        return false;
                                    """

                                    confirm_clicked = self.driver.execute_script(js_click_confirm)
                                    if confirm_clicked:
                                        self.log("无头模式: 已点击确认按钮")
                                    else:
                                        self.log("无头模式: 未找到确认按钮，尝试按ESC关闭对话框")
                                        ActionChains(self.driver).send_keys(Keys.ESCAPE).perform()
                                else:
                                    self.log("无头模式: 未找到封面上传输入框")
                            except Exception as e:
                                self.log(f"无头模式: 上传封面文件时出错: {str(e)}")
                        else:
                            self.log("无头模式: 未找到本地上传按钮")
                    else:
                        self.log("无头模式: 未找到上传封面按钮")
                except Exception as e:
                    self.log(f"无头模式: 上传封面过程中出错: {str(e)}")
            else:
                # 可视化模式下的封面上传逻辑(原有代码)
                # 尝试最多3次查找并点击上传封面按钮
                upload_cover_success = False
                for attempt in range(1):
                    try:
                        # 使用不同的等待策略查找上传封面按钮
                        try:
                            # 方法1: 使用XPath直接定位
                            wait = WebDriverWait(self.driver, 10)
                            upload_cover_btn = wait.until(EC.element_to_be_clickable((By.XPATH, UPLOAD_COVER_BUTTON)))
                            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", upload_cover_btn)
                            time.sleep(0.5)
                            self.driver.execute_script("arguments[0].click();", upload_cover_btn)
                            self.log(f"尝试 {attempt+1}: 已通过XPath找到并点击上传封面按钮")
                            upload_cover_success = True
                            break
                        except:
                            # 方法2: 通过封面区域的识别
                            elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '封面') or contains(@alt, '封面')]")
                            if elements:
                                for elem in elements:
                                    try:
                                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", elem)
                                        time.sleep(0.5)
                                        # 先尝试点击元素本身
                                        self.driver.execute_script("arguments[0].click();", elem)
                                        self.log(f"尝试 {attempt+1}: 已通过'封面'文本找到并点击相关元素")
                                        upload_cover_success = True
                                        break
                                    except:
                                        # 如果元素本身不可点击，尝试点击其父元素
                                        try:
                                            parent = self.driver.execute_script("return arguments[0].parentNode;", elem)
                                            self.driver.execute_script("arguments[0].click();", parent)
                                            self.log(f"尝试 {attempt+1}: 通过点击'封面'元素的父元素上传封面")
                                            upload_cover_success = True
                                            break
                                        except:
                                            continue

                        if not upload_cover_success:
                            self.log(f"尝试 {attempt+1}: 未能通过常规方法找到上传封面按钮")

                    except Exception as e:
                        self.log(f"尝试 {attempt+1}: 点击上传封面按钮时出错: {str(e)}")

                    if upload_cover_success:
                        break

                    # 如果失败，等待并重试
                    time.sleep(1)

                if not upload_cover_success and not self.headless_mode:
                    self.log("多次尝试后仍无法点击上传封面按钮，将使用默认封面")
                    # 继续后续步骤而不是直接返回，因为即使没有自定义封面，存稿仍可以进行
                else:
                    # 点击成功，执行后续的封面上传步骤
                    # 点击本地上传按钮
                    if not self.headless_mode:  # 在可视化模式下继续执行原有逻辑
                        self.log("准备点击本地上传按钮")
                        try:
                            # 等待对话框加载
                            time.sleep(2)

                            # 通过文本内容直接查找"本地上传"按钮
                            elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '本地上传')]")
                            if elements:
                                self.driver.execute_script("arguments[0].click();", elements[0])
                                self.log("已通过文本内容找到并点击本地上传按钮")

                                # 上传封面
                                try:
                                    wait = WebDriverWait(self.driver, 10)
                                    cover_input = wait.until(EC.presence_of_element_located((By.XPATH, COVER_UPLOAD_INPUT)))
                                    self.upload_file(cover_path, cover_input)
                                    self.log("封面上传成功")
                                    # 不需要设置变量，只记录日志

                                    # 等待图片缩略图出现，然后点击确认按钮
                                    self.log("等待图片缩略图出现，然后点击确认按钮")
                                    try:
                                        # 等待图片缩略图元素出现，最多等待10秒
                                        wait = WebDriverWait(self.driver, 10)
                                        wait.until(
                                            EC.presence_of_element_located((By.XPATH, '//*[@id="cropper-input"]'))
                                        )
                                        self.log("✅ 检测到图片缩略图元素已出现")

                                        # 使用新方法检查图片是否加载，包括检查{图片中}文本和图片src
                                        self.log("等待图片加载完成...")

                                        # 使用新的等待图片加载方法
                                        image_loaded = self.wait_for_image_loaded(timeout=10)

                                        if image_loaded:
                                            self.log("✅ 检测到图片已完全加载")
                                        else:
                                            self.log("⚠️ 未检测到完全加载的图片，但仍将继续")
                                            # 等待额外时间以防万一
                                            time.sleep(2)

                                        # 等待缩略图完全加载
                                        time.sleep(0.5)

                                        # 尝试通过XPath找到确认按钮
                                        confirm_button = wait.until(EC.element_to_be_clickable((By.XPATH, COVER_CONFIRM_BUTTON)))
                                        self.driver.execute_script("arguments[0].scrollIntoView(true);", confirm_button)
                                        self.driver.execute_script("arguments[0].click();", confirm_button)
                                        self.log("已点击确认按钮（通过XPath）")
                                    except Exception as e:
                                        self.log("通过XPath点击确认按钮失败，立即尝试备用方法...")

                                        # 添加截图功能
                                        if self.headless_mode:
                                            self.log("无头模式下点击确认按钮失败，正在保存截图...")

                                            # 确定截图目录
                                            screenshots_dir = ""
                                            default_screenshots_dir = "D:/网易号全自动/截图"  # 默认截图目录

                                            # 优先使用实例的screenshots_dir
                                            if hasattr(self, 'screenshots_dir') and self.screenshots_dir:
                                                screenshots_dir = self.screenshots_dir
                                                self.log(f"使用实例配置的截图目录: {screenshots_dir}")
                                            else:
                                                # 如果没有设置，使用默认目录
                                                screenshots_dir = default_screenshots_dir
                                                self.log(f"使用默认截图目录: {screenshots_dir}")

                                            # 确保截图目录存在
                                            os.makedirs(screenshots_dir, exist_ok=True)

                                            # 生成截图文件名
                                            timestamp = int(time.time())
                                            screenshot_path = os.path.join(screenshots_dir, f"confirm_failed_{timestamp}.png")

                                            # 保存截图
                                            self.driver.save_screenshot(screenshot_path)

                                            # 确保截图路径是绝对路径
                                            if not os.path.isabs(screenshot_path):
                                                screenshot_path = os.path.abspath(screenshot_path)

                                            if os.path.exists(screenshot_path):
                                                self.log(f"已保存失败截图到: {screenshot_path}")
                                                # 保存截图路径到实例变量，以便外部访问
                                                self.last_screenshot = screenshot_path

                                    # 备用方法：通过文本内容查找确认按钮
                                    try:
                                        # 查找包含"确定"或"确认"文本的按钮
                                        confirm_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(), '确定') or contains(text(), '确认')]")
                                        if confirm_buttons:
                                            # 尝试点击找到的按钮
                                            click_successful = False
                                            for button in confirm_buttons:
                                                try:
                                                    # 确保按钮可见并可点击
                                                    self.driver.execute_script("""
                                                        // 滚动到按钮位置
                                                        arguments[0].scrollIntoView({block: 'center', inline: 'center'});

                                                        // 确保按钮不被其他元素遮挡
                                                        arguments[0].style.position = 'relative';
                                                        arguments[0].style.zIndex = '9999';

                                                        // 如果按钮在弹窗中，确保弹窗可见
                                                        let parent = arguments[0].closest('.dialog, .modal, .popup');
                                                        if (parent) {
                                                            parent.style.zIndex = '10000';
                                                            parent.style.display = 'block';
                                                            parent.style.visibility = 'visible';
                                                            parent.style.opacity = '1';
                                                        }
                                                    """, button)

                                                    time.sleep(0.5)
                                                    # 使用JavaScript点击按钮
                                                    self.driver.execute_script("arguments[0].click();", button)
                                                    self.log("已点击确认按钮（通过文本内容查找）")
                                                    click_successful = True
                                                    break
                                                except Exception:
                                                    continue

                                            if not click_successful:
                                                # 如果点击失败，尝试查找对话框中的按钮
                                                self.log("尝试通过对话框查找确认按钮")
                                                dialogs = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'dialog') or contains(@class, 'modal')]")
                                                if dialogs:
                                                    for dialog in dialogs:
                                                        try:
                                                            # 查找对话框中的所有按钮
                                                            buttons = dialog.find_elements(By.TAG_NAME, "button")
                                                            if buttons:
                                                                # 尝试点击最后一个按钮（通常是确认按钮）
                                                                self.driver.execute_script("arguments[0].click();", buttons[-1])
                                                                self.log("已点击对话框中的最后一个按钮")
                                                                click_successful = True
                                                                break
                                                        except Exception:
                                                            continue

                                            if not click_successful:
                                                self.log("警告：找到了确认按钮，但无法点击")
                                        else:
                                            self.log("警告：未找到包含'确定'或'确认'文本的按钮")

                                        # 无论确认按钮点击是否成功，等待一会儿让页面稳定，然后尝试检查和关闭可能存在的遮挡元素
                                        time.sleep(1)
                                    except Exception as inner_e:
                                        self.log(f"使用备用方法点击确认按钮失败: {str(inner_e)}")

                                        # 最后的应急措施：尝试按Escape键关闭弹窗
                                        try:
                                            ActionChains(self.driver).send_keys(Keys.ESCAPE).perform()
                                            self.log("已尝试按ESC键关闭弹窗")
                                            time.sleep(1)
                                        except:
                                            pass
                                except Exception as e:
                                    self.log(f"封面上传失败: {str(e)}")
                            else:
                                self.log("未找到本地上传按钮")
                        except Exception as e:
                            self.log(f"点击本地上传按钮失败: {str(e)}")

            return True

        except Exception as e:
            self.log(f"❌ 上传封面时发生错误: {str(e)}")
            # 即使封面上传失败，也继续后续步骤
            return True

    def set_declaration(self) -> bool:
        """设置声明"""
        try:
            # 关闭可能存在的窗口（无论是否为无头模式）
            try:
                # 查找并关闭所有可能的弹窗
                close_buttons = self.driver.find_elements(By.XPATH, "//button[contains(@class, 'close') or contains(@class, 'cancel') or contains(text(), '关闭') or contains(text(), '取消')]")
                for button in close_buttons:
                    try:
                        self.driver.execute_script("arguments[0].click();", button)
                        self.log("已关闭弹窗")
                        time.sleep(0.5)  # 给予短暂延迟以确保窗口关闭
                    except:
                        continue

                # 尝试按ESC键关闭弹窗
                ActionChains(self.driver).send_keys(Keys.ESCAPE).perform()
                self.log("已尝试按ESC键关闭弹窗")
                time.sleep(1)  # 等待弹窗关闭
            except Exception as e:
                self.log(f"关闭弹窗时出错: {str(e)}")

            # 第一步：点击声明按钮
            declaration_button = self.driver.find_element(By.XPATH, DECLARATION_BUTTON)
            declaration_button.click()
            self.log("已点击声明按钮")

            # 第二步：点击声明内容按钮
            time.sleep(0.5)  # 等待声明按钮点击后的UI响应
            declaration_content = self.driver.find_element(By.XPATH, DECLARATION_CONTENT)
            declaration_content.click()
            self.log("已点击声明内容按钮")

            # 第三步：选择取材网络 - 先尝试直接点击
            click_success = False
            try:
                # 仅通过文本内容查找"取材网络"选项
                internet_options = self.driver.find_elements(By.XPATH, "//*[contains(text(), '取材网络') or contains(text(), '网络取材')]")
                if internet_options:
                    # 尝试点击找到的元素
                    for option in internet_options:
                        try:
                            self.driver.execute_script("arguments[0].scrollIntoView(true);", option)
                            self.driver.execute_script("arguments[0].click();", option)

                            # 检测点击是否成功
                            click_success = True
                            self.log("已选择'取材网络'选项 (通过文本内容查找)")
                            break
                        except:
                            continue

                    if not click_success:
                        self.log("警告：找到了包含'取材网络'文本的元素，但无法点击成功")
                else:
                    self.log("警告：未找到包含'取材网络'文本的元素")
            except Exception as e:
                self.log(f"设置取材网络失败: {str(e)}")

            if not click_success:
                self.log("警告：未能成功设置声明为'取材网络'，将继续后续步骤")

            return True

        except Exception as e:
            self.log(f"❌ 设置声明时发生错误: {str(e)}")
            # 继续执行后续步骤，即使声明设置失败
            return True

    def set_category(self) -> bool:
        """设置分类"""
        try:
            # 第一步：点击分类输入框
            click_success = False
            try:
                # 首先尝试直接点击
                category_input = self.driver.find_element(By.XPATH, CATEGORY_INPUT)
                category_input.click()
                time.sleep(0.5)  # 等待分类选项显示
                self.log("已点击分类输入框")
                click_success = True
            except Exception as e:
                self.log(f"直接点击分类输入框失败: {str(e)}，尝试备用方法...")

                # 备用方法: 仅保留JavaScript执行点击
                try:
                    element = self.driver.find_element(By.XPATH, CATEGORY_INPUT)
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                    self.driver.execute_script("arguments[0].click();", element)
                    self.log("已通过JavaScript点击分类输入框")
                    click_success = True
                except Exception as js_error:
                    self.log(f"JavaScript点击分类输入框也失败: {str(js_error)}")

            if not click_success:
                self.log("警告: 点击分类输入框失败，将尝试继续进行后续步骤")

            # 第二步：通过文本查找方式点击包含"要闻"的元素
            second_step_success = False
            try:
                # 查找包含"要闻"文本的元素
                variety_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '要闻')]")
                if variety_elements:
                    for elem in variety_elements:
                        try:
                            self.driver.execute_script("arguments[0].scrollIntoView(true);", elem)
                            self.driver.execute_script("arguments[0].click();", elem)
                            self.log("已选择第一级分类'要闻'(通过文本内容查找)")
                            second_step_success = True
                            break
                        except:
                            continue

                    if not second_step_success:
                        self.log("警告：找到了包含'要闻'文本的元素，但无法点击成功")
                else:
                    self.log("警告：未找到包含'要闻'文本的元素")
            except Exception as e:
                self.log(f"选择第一级分类失败: {str(e)}")

            # 第三步：通过文本查找方式点击包含"国际"的元素
            third_step_success = False
            try:
                # 查找包含"国际"文本的元素
                variety_talkshow_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '国际')]")
                if variety_talkshow_elements:
                    for elem in variety_talkshow_elements:
                        try:
                            self.driver.execute_script("arguments[0].scrollIntoView(true);", elem)
                            self.driver.execute_script("arguments[0].click();", elem)
                            self.log("已选择第二级分类'国际'(通过文本内容查找)")
                            third_step_success = True
                            break
                        except:
                            continue

                    if not third_step_success:
                        self.log("警告：找到了包含'国际'文本的元素，但无法点击成功")
                else:
                    self.log("警告：未找到包含'国际'文本的元素")
            except Exception as e:
                self.log(f"选择第二级分类失败: {str(e)}")

            # 综合判断分类设置是否成功
            if second_step_success and third_step_success:
                self.log("分类设置完成：要闻 > 国际")
            else:
                self.log("警告：分类设置可能不完整")

            return True

        except Exception as e:
            self.log(f"❌ 设置分类时发生错误: {str(e)}")
            # 继续执行后续步骤，即使分类设置失败
            return True

    def set_tags(self, title: str) -> bool:
        """设置标签"""
        try:
            # 标签处理
            tags = self.generate_tags_from_title(title)

            # 确保标签数量至少为3个，特别是在并发模式下
            if len(tags) < 3:
                self.log(f"警告: 生成的标签数量不足3个，当前只有 {len(tags)} 个")
                # 添加默认标签补充到3个
                default_tags = ["视频", "精彩", "推荐"]
                for tag in default_tags:
                    if tag not in tags and len(tags) < 3:
                        tags.append(tag)
                self.log(f"已补充默认标签，现在有 {len(tags)} 个标签")

            self.log(f"已生成标签: {', '.join(tags)}")

            # 查找标签输入框元素
            tag_input = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, TAG_INPUT))
            )
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", tag_input)
            time.sleep(0.5)

            # 点击标签输入框，确保聚焦
            tag_input.click()
            time.sleep(0.5)
            self.log("准备输入标签")

            # 逐个标签输入，模拟人工手动输入
            for tag in tags:
                # 一个字符一个字符地输入，模拟真人输入速度
                for char in tag:
                    ActionChains(self.driver).send_keys(char).perform()
                    # 较慢的随机打字速度，模拟人工输入
                    time.sleep(0.1 + random.random() * 0.2)

                # 输入空格分隔标签 - 按一次空格，根据平台要求
                ActionChains(self.driver).send_keys(Keys.SPACE).perform()
                time.sleep(0.3)

            # 验证标签是否成功设置 - 检查输入框中是否有内容
            try:
                # 等待标签显示出来
                time.sleep(1)

                # 获取标签区域的文本
                tag_area_text = tag_input.text

                # 如果标签区域为空，尝试获取父元素的文本
                if not tag_area_text:
                    tag_area_text = tag_input.get_attribute("innerHTML")

                # 如果仍然为空，可能标签没有正确设置，重试一次
                if not tag_area_text or tag_area_text.strip() == "":
                    self.log("⚠️ 标签可能未正确设置，尝试重新输入...")

                    # 再次点击输入框
                    tag_input.click()
                    time.sleep(0.8)

                    # 清空可能存在的内容
                    ActionChains(self.driver).key_down(Keys.CONTROL).send_keys('a').key_up(Keys.CONTROL).send_keys(Keys.DELETE).perform()
                    time.sleep(0.5)

                    # 直接输入完整标签字符串，用空格分隔
                    tag_string = " ".join(tags)
                    ActionChains(self.driver).send_keys(tag_string).perform()
                    time.sleep(0.5)

                    # 按一次空格和回车确认
                    ActionChains(self.driver).send_keys(Keys.SPACE).perform()
                    time.sleep(0.3)
                    self.log("已在备用方法中输入标签并按空格键")

                    ActionChains(self.driver).send_keys(Keys.ENTER).perform()
                    time.sleep(0.5)

                    self.log("已尝试重新设置标签")
            except Exception as verify_error:
                self.log(f"验证标签设置时出错: {str(verify_error)}")
                # 继续执行，不要因为验证失败而中断流程

            self.log(f"已设置标签: {', '.join(tags)}")
            return True

        except Exception as e:
            self.log(f"设置标签时发生错误: {str(e)}")
            return True

    def check_image_loaded(self):
        """检查页面中是否有已加载的图片，包括检查{图片中}文本和图片src"""
        js_check_image = """
        function checkImageBySource() {
            // 查找所有图片元素
            const images = document.querySelectorAll('img');

            // 检查是否有任何图片的src包含特定的模式
            for (const img of images) {
                if (img.src &&
                    (img.src.includes('dingyue.ws.126.net') ||
                     img.src.includes('.jpg') ||
                     img.src.includes('.png') ||
                     img.src.includes('.jpeg'))) {

                    // 检查图片是否已加载
                    if (img.complete && typeof img.naturalWidth !== 'undefined' && img.naturalWidth > 0) {
                        return {
                            found: true,
                            loaded: true,
                            src: img.src,
                            alt: img.alt || '图片',
                            dimensions: {
                                width: img.naturalWidth,
                                height: img.naturalHeight
                            }
                        };
                    } else {
                        return {
                            found: true,
                            loaded: false,
                            src: img.src,
                            alt: img.alt || '图片'
                        };
                    }
                }
            }

            // 检查是否有包含"{图片中}"文本的元素
            const elements = document.querySelectorAll('*');
            for (const el of elements) {
                if (el.textContent && el.textContent.includes('{图片中}')) {
                    return {
                        found: true,
                        isTextMarker: true,
                        element: el.tagName,
                        text: el.textContent
                    };
                }
            }

            return { found: false };
        }

        return checkImageBySource();
        """

        result = self.driver.execute_script(js_check_image)

        if result.get('found'):
            if result.get('loaded'):
                self.log(f"✅ 找到并加载了图片: {result.get('src')}")
                return True
            elif result.get('isTextMarker'):
                self.log(f"✅ 找到图片标记文本: {result.get('text')}")
                return True
            else:
                self.log(f"⚠️ 找到图片但未完全加载: {result.get('src')}")
                return False
        else:
            self.log("❌ 未找到任何图片")
            return False

    def wait_for_image_loaded(self, timeout=15, poll_frequency=0.5):
        """等待图片加载完成"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            if self.check_image_loaded():
                return True
            time.sleep(poll_frequency)

        self.log(f"❌ 等待图片加载超时")
        return False

    def upload_file(self, file_path, input_element=None):
        """上传文件（封面图片等）- 优化版，避免打开文件窗口"""
        try:
            # 格式化文件路径 - 处理Windows路径斜杠
            file_path_formatted = file_path.replace('\\', '/')

            # 方法1：如果提供了输入元素，直接使用它
            if input_element:
                # 确保元素可见
                self.driver.execute_script("arguments[0].style.display = 'block';", input_element)
                input_element.send_keys(file_path_formatted)
                self.log("文件已通过提供的输入元素上传")
                return

            # 方法2：查找现有的文件输入框
            file_inputs = self.driver.find_elements(By.CSS_SELECTOR, 'input[type="file"]')
            if file_inputs:
                # 如果有多个文件输入框，尝试找到当前可见的或最可能是正确的那个
                for input_elem in file_inputs:
                    try:
                        # 确保元素可交互
                        self.driver.execute_script("arguments[0].style.display = 'block';", input_elem)
                        self.driver.execute_script("arguments[0].style.opacity = '1';", input_elem)
                        input_elem.send_keys(file_path_formatted)
                        self.log("文件已通过找到的输入框上传")
                        return
                    except Exception as e:
                        continue

                # 如果上面的循环未成功，尝试使用第一个输入框
                try:
                    file_inputs[0].send_keys(file_path_formatted)
                    self.log("文件已通过第一个输入框上传")
                    return
                except Exception as e:
                    self.log(f"使用第一个输入框上传失败: {str(e)}")

            # 方法3：创建新的文件输入框
            self.log("未找到可用的文件输入框，创建新的输入框")
            js_code = """
                const input = document.createElement('input');
                input.type = 'file';
                input.style.position = 'absolute';
                input.style.left = '-9999px';
                input.style.opacity = '1';
                input.style.display = 'block';
                input.id = 'custom-file-input-' + Date.now();
                document.body.appendChild(input);
                return input;
            """
            file_input = self.driver.execute_script(js_code)
            file_input.send_keys(file_path_formatted)
            self.driver.execute_script("arguments[0].dispatchEvent(new Event('change'));", file_input)
            self.log("文件已通过创建的输入框上传")
        except Exception as e:
            self.log(f"文件上传失败: {str(e)}")
            raise

    def generate_tags_from_title(self, title: str) -> list:
        """根据标题智能生成标签"""
        try:
            # 使用jieba分词
            import jieba
            import re

            # 使用jieba精确模式分词
            words = list(jieba.cut(title, cut_all=False))

            # 这些词在标签中通常没有单独价值，但不再过滤掉所有词
            minimal_stop_words = ['的', '了', '和', '与', '这', '那']
            all_words = [word for word in words if len(word) >= 1 and word not in minimal_stop_words]

            # 根据标题内容提取关键词
            tags = []

            # 1. 先取标题作为第一个标签，确保有一个核心标签
            if len(title) >= 2 and len(title) <= 8:
                tags.append(title)

            # 2. 选择3-4字的长词
            long_words = [word for word in all_words if 3 <= len(word) <= 4]
            for word in long_words:
                if word not in tags and len(tags) < 5:
                    tags.append(word)

            # 3. 选择2-3字的短词
            short_words = [word for word in all_words if 2 <= len(word) <= 3]
            for word in short_words:
                if word not in tags and len(tags) < 5:
                    tags.append(word)

            # 4. 如果标签仍然不足5个，尝试组合相邻的词（不管长度）
            if len(tags) < 3 and len(all_words) >= 2:
                for i in range(len(all_words) - 1):
                    combined = all_words[i] + all_words[i+1]
                    if combined not in tags:
                        tags.append(combined)
                        if len(tags) >= 5:
                            break

            # 5. 再尝试作为单词添加
            if len(tags) < 3 and all_words:
                for word in all_words:
                    if word not in tags and len(tags) < 5:
                        tags.append(word)

            # 6. 如果还是不够，再把标题切分成几部分作为标签
            if len(tags) < 3 and len(title) > 5:
                # 将标题分成三段
                third = len(title) // 3
                if third >= 2:
                    for i in range(3):
                        start = i * third
                        end = (i + 1) * third if i < 2 else len(title)
                        segment = title[start:end]
                        if len(segment) >= 2 and segment not in tags:
                            tags.append(segment)
                            if len(tags) >= 5:
                                break

            # 7. 标题中的数字可能是重要信息（如日期、时间、数量），将它们添加为标签
            number_patterns = re.findall(r'\d+', title)
            for num in number_patterns:
                # 查找数字周围的上下文
                index = title.find(num)
                start = max(0, index - 2)
                end = min(len(title), index + len(num) + 2)
                context = title[start:end]
                if context not in tags and len(tags) < 5:
                    tags.append(context)

            # 确保至少有3个标签
            if len(tags) < 3:
                # 如果实在不够，重复一些标签或者使用标题的不同部分
                for i in range(min(3, len(title) - 1)):
                    subset = title[i:i+min(5, len(title)-i)]
                    if subset not in tags:
                        tags.append(subset)
                    if len(tags) >= 5:
                        break

            # 调整标签顺序，使长标签在前面
            tags.sort(key=len, reverse=True)

            # 返回标签，确保至少3个，最多5个
            return tags[:5]

        except Exception as e:
            self.log(f"生成标签失败: {str(e)}")
            # 返回固定的三个标签，确保标签数量足够
            return ["视频", "精彩", "推荐"]

    def _wait_for_upload_completion_improved(self, timeout: int = 180) -> bool:
        """
        改进的视频上传完成检测方法 - 专注于文字检测

        检测成功判定：元素中的文字变更为"推荐封面"即为成功

        特性:
        1. 浏览器隔离检测 - 确保每个浏览器实例独立检测
        2. 二次检测机制 - 每3秒检测一次，并进行二次验证
        3. 纯文字检测 - 专注于检测元素文字内容，不依赖JavaScript
        4. 多种文字检测方式 - 使用XPath和备用方法确保检测准确性

        Args:
            timeout: 超时时间（秒）

        Returns:
            是否检测到上传完成（文字变为"推荐封面"）
        """
        try:
            self.log(f"🔍 开始改进的视频上传完成检测，超时时间: {timeout}秒")

            # 目标XPath - 用户提供的检测路径
            target_xpath = '//*[@id="root"]/div/div[2]/div/div[2]/div/div/div[1]/div/div[2]/div[1]/div[2]/div[2]/div'
            target_text = "推荐封面"

            # 检测间隔为3秒
            check_interval = 3
            max_checks = timeout // check_interval

            self.log(f"🔍 检测配置: 每{check_interval}秒检测一次，最多检测{max_checks}次")

            for check_count in range(max_checks):
                try:
                    current_time = (check_count + 1) * check_interval
                    self.log(f"🔍 第{check_count + 1}次检测 ({current_time}秒)")

                    # 方法1: 使用精确XPath文字检测
                    success_detected = self._check_upload_success_by_text(target_xpath, target_text)

                    if success_detected:
                        self.log(f"✅ 第{check_count + 1}次检测: 通过文字检测到上传完成")

                        # 进行二次验证 - 等待3秒后再次检测
                        self.log("🔍 进行二次验证...")
                        time.sleep(3)

                        # 二次检测
                        second_check = self._check_upload_success_by_text(target_xpath, target_text)
                        if second_check:
                            self.log("✅ 二次验证成功: 确认视频上传完成")
                            return True
                        else:
                            self.log("⚠️ 二次验证失败: 可能是临时状态，继续检测")

                    # 方法2: 备用文字检测方法
                    backup_success = self._check_upload_success_backup_text()
                    if backup_success:
                        self.log(f"✅ 第{check_count + 1}次检测: 通过备用文字检测到上传完成")

                        # 进行二次验证
                        self.log("🔍 进行二次验证...")
                        time.sleep(3)

                        second_backup_check = self._check_upload_success_backup_text()
                        if second_backup_check:
                            self.log("✅ 二次验证成功: 确认视频上传完成")
                            return True
                        else:
                            self.log("⚠️ 二次验证失败: 可能是临时状态，继续检测")

                    # 如果都没有检测到，等待下一次检测
                    if check_count < max_checks - 1:  # 不是最后一次检测
                        self.log(f"⏳ 第{check_count + 1}次检测未发现上传完成，等待{check_interval}秒后继续...")
                        time.sleep(check_interval)

                except Exception as e:
                    self.log(f"⚠️ 第{check_count + 1}次检测时发生错误: {str(e)}")
                    if check_count < max_checks - 1:
                        time.sleep(check_interval)
                    continue

            self.log(f"❌ 检测超时: 在{timeout}秒内未检测到视频上传完成")
            return False

        except Exception as e:
            self.log(f"❌ 改进检测方法执行失败: {str(e)}")
            return False

    def _check_upload_success_by_text(self, xpath: str, target_text: str) -> bool:
        """
        使用文字检测上传成功状态（浏览器隔离）
        专注于检测元素中的文字是否变更为目标文字

        Args:
            xpath: 目标元素的XPath
            target_text: 目标文本内容

        Returns:
            是否检测到成功状态
        """
        try:
            # 记录检测开始
            self.log(f"🔍 文字检测开始: 查找路径 {xpath}")
            self.log(f"🔍 目标文字: '{target_text}'")

            # 确保只在当前浏览器窗口中查找，避免多浏览器干扰
            elements = self.driver.find_elements(By.XPATH, xpath)
            self.log(f"🔍 文字检测: 找到 {len(elements)} 个匹配元素")

            if not elements:
                self.log(f"🔍 文字检测: 未找到匹配的元素")
                return False

            for i, element in enumerate(elements):
                try:
                    # 获取元素文字信息
                    element_tag = element.tag_name
                    element_text = element.text.strip()
                    is_displayed = element.is_displayed()

                    self.log(f"🔍 文字检测: 元素 {i+1} - 标签: {element_tag}, 可见: {is_displayed}")
                    self.log(f"🔍 文字检测: 元素 {i+1} - 实际文字: '{element_text}'")

                    # 检查元素是否可见且文字完全匹配目标文字
                    if is_displayed and element_text == target_text:
                        self.log(f"✅ 文字检测成功: 元素文字完全匹配目标文字 '{target_text}'")
                        return True
                    elif is_displayed and target_text in element_text:
                        self.log(f"🔍 文字检测: 元素文字包含目标文字但不完全匹配")
                        self.log(f"🔍 实际文字: '{element_text}' | 目标文字: '{target_text}'")
                        # 如果包含目标文字，也认为是成功的
                        return True
                    elif target_text in element_text:
                        self.log(f"⚠️ 文字检测: 找到包含目标文字的元素但不可见")
                        self.log(f"⚠️ 元素文字: '{element_text}'")
                    else:
                        self.log(f"🔍 文字检测: 元素文字不匹配目标文字")
                        if len(element_text) > 0:
                            self.log(f"🔍 实际文字: '{element_text}' | 目标文字: '{target_text}'")
                        else:
                            self.log(f"🔍 元素文字为空")

                except Exception as element_error:
                    self.log(f"⚠️ 文字检测: 处理元素 {i+1} 时出错: {str(element_error)}")
                    continue

            self.log(f"🔍 文字检测: 所有元素检查完毕，未找到匹配的目标文字")
            return False

        except Exception as e:
            self.log(f"❌ 文字检测失败: {str(e)}")
            # 记录更详细的错误信息
            import traceback
            self.log(f"❌ 文字检测错误详情: {traceback.format_exc()}")
            return False



    def _check_upload_success_backup_text(self) -> bool:
        """
        备用文字检测方法 - 使用多种方式检测"推荐封面"文字
        专注于文字内容检测，不依赖JavaScript

        Returns:
            是否检测到成功状态
        """
        try:
            self.log("🔍 备用文字检测开始: 使用多种方式检测'推荐封面'文字")
            target_text = "推荐封面"

            # 方法1: 检测页面源码中是否包含"推荐封面"文字
            try:
                page_source = self.driver.page_source
                page_source_length = len(page_source)
                self.log(f"🔍 备用文字检测方法1: 检查页面源码 (长度: {page_source_length})")

                if target_text in page_source:
                    self.log(f"✅ 备用文字检测方法1成功: 在页面源码中发现'{target_text}'文字")
                    return True
                else:
                    self.log(f"🔍 备用文字检测方法1: 页面源码中未发现'{target_text}'文字")
            except Exception as e:
                self.log(f"⚠️ 备用文字检测方法1失败: {str(e)}")

            # 方法2: 查找所有包含"推荐封面"的元素
            try:
                self.log(f"🔍 备用文字检测方法2: 查找包含'{target_text}'的元素")
                elements_with_text = self.driver.find_elements(
                    By.XPATH,
                    f"//*[contains(text(), '{target_text}')]"
                )

                self.log(f"🔍 备用文字检测方法2: 找到 {len(elements_with_text)} 个包含'{target_text}'的元素")

                for i, element in enumerate(elements_with_text):
                    try:
                        element_tag = element.tag_name
                        element_text = element.text.strip()
                        is_displayed = element.is_displayed()

                        self.log(f"🔍 备用文字检测方法2: 元素 {i+1} - 标签: {element_tag}, 可见: {is_displayed}")
                        self.log(f"🔍 备用文字检测方法2: 元素 {i+1} - 文字: '{element_text}'")

                        if is_displayed and target_text in element_text:
                            self.log(f"✅ 备用文字检测方法2成功: 找到可见的'{target_text}'元素")
                            return True
                    except Exception as element_error:
                        self.log(f"⚠️ 备用文字检测方法2: 处理元素 {i+1} 时出错: {str(element_error)}")
                        continue

                if elements_with_text:
                    self.log(f"🔍 备用文字检测方法2: 找到包含'{target_text}'的元素但都不可见")
                else:
                    self.log(f"🔍 备用文字检测方法2: 未找到包含'{target_text}'的元素")

            except Exception as e:
                self.log(f"⚠️ 备用文字检测方法2失败: {str(e)}")

            # 方法3: 使用更宽泛的XPath查找包含目标文字的元素
            try:
                self.log(f"🔍 备用文字检测方法3: 使用宽泛XPath查找'{target_text}'")

                # 尝试多种XPath模式
                xpath_patterns = [
                    f"//div[contains(text(), '{target_text}')]",
                    f"//span[contains(text(), '{target_text}')]",
                    f"//p[contains(text(), '{target_text}')]",
                    f"//button[contains(text(), '{target_text}')]",
                    f"//a[contains(text(), '{target_text}')]",
                    f"//*[text()='{target_text}']",  # 完全匹配
                ]

                for j, xpath_pattern in enumerate(xpath_patterns):
                    try:
                        elements = self.driver.find_elements(By.XPATH, xpath_pattern)
                        self.log(f"🔍 备用文字检测方法3: XPath模式 {j+1} 找到 {len(elements)} 个元素")

                        for i, element in enumerate(elements):
                            try:
                                if element.is_displayed():
                                    element_text = element.text.strip()
                                    self.log(f"🔍 备用文字检测方法3: 可见元素文字: '{element_text}'")

                                    if element_text == target_text:
                                        self.log(f"✅ 备用文字检测方法3成功: 找到完全匹配的'{target_text}'文字")
                                        return True
                                    elif target_text in element_text:
                                        self.log(f"✅ 备用文字检测方法3成功: 找到包含'{target_text}'的文字")
                                        return True
                            except:
                                continue

                    except Exception as xpath_error:
                        self.log(f"⚠️ 备用文字检测方法3: XPath模式 {j+1} 失败: {str(xpath_error)}")
                        continue

            except Exception as e:
                self.log(f"⚠️ 备用文字检测方法3失败: {str(e)}")

            self.log(f"🔍 备用文字检测: 所有方法都未检测到'{target_text}'文字")
            return False

        except Exception as e:
            self.log(f"❌ 备用文字检测失败: {str(e)}")
            # 记录更详细的错误信息
            import traceback
            self.log(f"❌ 备用文字检测错误详情: {traceback.format_exc()}")
            return False
