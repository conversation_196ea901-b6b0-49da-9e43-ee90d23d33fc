"""
升级版定时任务调度器模块
支持用户自主任务管理、一次性任务和常驻任务
"""

from .manager import AdvancedSchedulerManager
from .task_model import ScheduledTask, TaskType, TaskStatus, RepeatRule, RepeatType
from .task_storage import TaskStorage
from .task_executor import TaskExecutor
from .platform_integration import PlatformIntegration
from .time_utils import TimeUtils
from .task_monitor import TaskMonitor

__all__ = [
    'AdvancedSchedulerManager',
    'ScheduledTask',
    'TaskType',
    'TaskStatus',
    'RepeatRule',
    'RepeatType',
    'TaskStorage',
    'TaskExecutor',
    'PlatformIntegration',
    'TimeUtils',
    'TaskMonitor'
]

__version__ = '2.0.0'
__author__ = 'Augment Agent'
__description__ = '多平台存稿系统升级版定时任务调度器'
