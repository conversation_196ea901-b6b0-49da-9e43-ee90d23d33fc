"""
进度面板模块 - 负责显示任务进度
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Any, Callable, List, Optional
import time
import threading

class ProgressPanel:
    """进度面板类，负责显示任务进度"""
    
    def __init__(self, parent: ttk.Frame, log_callback: Callable = None):
        """
        初始化进度面板
        
        Args:
            parent: 父容器
            log_callback: 日志回调函数
        """
        self.parent = parent
        self.log_callback = log_callback
        
        # 初始化变量
        self.progress_data = {}  # 存储各账号的进度数据
        self.is_updating = False  # 是否正在更新进度
        self.update_interval = 1.0  # 进度更新间隔（秒）
        
        # 创建UI
        self.create_ui()
    
    def log(self, message: str) -> None:
        """
        记录日志
        
        Args:
            message: 日志消息
        """
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)
    
    def create_ui(self) -> None:
        """创建用户界面"""
        # 创建工具栏
        toolbar = ttk.Frame(self.parent)
        toolbar.pack(fill=tk.X, pady=(0, 5))
        
        # 标题
        ttk.Label(toolbar, text="任务进度", font=("宋体", 10, "bold")).pack(side=tk.LEFT, padx=5)
        
        # 刷新按钮
        ttk.Button(toolbar, text="刷新", command=self.refresh_progress).pack(side=tk.RIGHT, padx=5)
        
        # 自动刷新选项
        self.auto_refresh = tk.BooleanVar(value=True)
        ttk.Checkbutton(toolbar, text="自动刷新", variable=self.auto_refresh, command=self.toggle_auto_refresh).pack(side=tk.RIGHT, padx=5)
        
        # 刷新间隔
        ttk.Label(toolbar, text="刷新间隔:").pack(side=tk.RIGHT, padx=(10, 0))
        self.refresh_interval = tk.StringVar(value="1.0")
        interval_combobox = ttk.Combobox(toolbar, textvariable=self.refresh_interval, width=5, state="readonly")
        interval_combobox["values"] = ("0.5", "1.0", "2.0", "5.0")
        interval_combobox.pack(side=tk.RIGHT, padx=(0, 5))
        interval_combobox.bind("<<ComboboxSelected>>", self.on_interval_changed)
        
        # 创建进度列表框架
        progress_frame = ttk.Frame(self.parent)
        progress_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建进度列表（使用Treeview）
        columns = ("账号", "进度", "状态", "详情", "最后更新")
        self.progress_tree = ttk.Treeview(progress_frame, columns=columns, show="headings", height=10)
        
        # 设置列标题和宽度
        for col in columns:
            self.progress_tree.heading(col, text=col)
            if col == "账号":
                self.progress_tree.column(col, width=150, anchor=tk.W)
            elif col == "进度":
                self.progress_tree.column(col, width=100, anchor=tk.CENTER)
            elif col == "状态":
                self.progress_tree.column(col, width=80, anchor=tk.CENTER)
            elif col == "详情":
                self.progress_tree.column(col, width=300, anchor=tk.W)
            elif col == "最后更新":
                self.progress_tree.column(col, width=150, anchor=tk.CENTER)
        
        # 添加滚轮支持
        def _on_mousewheel(event):
            self.progress_tree.yview_scroll(int(-1*(event.delta/120)), "units")

        def _bind_mousewheel(event):
            self.progress_tree.bind_all("<MouseWheel>", _on_mousewheel)

        def _unbind_mousewheel(event):
            self.progress_tree.unbind_all("<MouseWheel>")

        self.progress_tree.bind('<Enter>', _bind_mousewheel)
        self.progress_tree.bind('<Leave>', _unbind_mousewheel)

        # 布局
        self.progress_tree.pack(fill=tk.BOTH, expand=True)
        
        # 创建总进度框架
        total_progress_frame = ttk.LabelFrame(self.parent, text="总体进度", padding=10)
        total_progress_frame.pack(fill=tk.X, pady=5)
        
        # 总进度条
        ttk.Label(total_progress_frame, text="总进度:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.total_progress_bar = ttk.Progressbar(total_progress_frame, orient=tk.HORIZONTAL, length=400, mode="determinate")
        self.total_progress_bar.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        self.total_progress_label = ttk.Label(total_progress_frame, text="0%")
        self.total_progress_label.grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        
        # 总状态
        ttk.Label(total_progress_frame, text="总状态:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.total_status_label = ttk.Label(total_progress_frame, text="就绪")
        self.total_status_label.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 启动自动刷新线程
        if self.auto_refresh.get():
            self.start_auto_refresh()
    
    def toggle_auto_refresh(self) -> None:
        """切换自动刷新状态"""
        if self.auto_refresh.get():
            self.start_auto_refresh()
            self.log("已启用自动刷新进度")
        else:
            self.stop_auto_refresh()
            self.log("已禁用自动刷新进度")
    
    def start_auto_refresh(self) -> None:
        """启动自动刷新线程"""
        if not self.is_updating:
            self.is_updating = True
            threading.Thread(target=self._auto_refresh_thread, daemon=True).start()
    
    def stop_auto_refresh(self) -> None:
        """停止自动刷新线程"""
        self.is_updating = False
    
    def _auto_refresh_thread(self) -> None:
        """自动刷新线程"""
        while self.is_updating and self.auto_refresh.get():
            # 刷新进度
            self.parent.after(0, self.refresh_progress)
            
            # 等待指定时间
            try:
                interval = float(self.refresh_interval.get())
            except ValueError:
                interval = 1.0
            
            # 睡眠
            for _ in range(int(interval * 10)):
                if not self.is_updating or not self.auto_refresh.get():
                    break
                time.sleep(0.1)
        
        self.is_updating = False
    
    def on_interval_changed(self, event) -> None:
        """
        刷新间隔变化事件处理
        
        Args:
            event: 事件对象
        """
        try:
            self.update_interval = float(self.refresh_interval.get())
            self.log(f"进度刷新间隔已设置为 {self.update_interval} 秒")
        except ValueError:
            self.refresh_interval.set("1.0")
            self.update_interval = 1.0
    
    def refresh_progress(self) -> None:
        """刷新进度显示"""
        # 这个方法可以被子类重写，用于实现刷新进度的功能
        # 在这里，我们只是更新UI，实际数据应该从并发管理器获取
        self.update_progress_tree()
        self.update_total_progress()
    
    def update_progress_data(self, progress_data: Dict[str, Dict[str, Any]]) -> None:
        """
        更新进度数据
        
        Args:
            progress_data: 进度数据字典，格式为 {账号: {progress: 进度, status: 状态, details: 详情, last_update: 最后更新时间}}
        """
        self.progress_data = progress_data
        self.update_progress_tree()
        self.update_total_progress()
    
    def update_progress_tree(self) -> None:
        """更新进度列表"""
        # 清空现有项目
        for item in self.progress_tree.get_children():
            self.progress_tree.delete(item)
        
        # 添加进度数据
        for account, data in self.progress_data.items():
            progress = data.get("progress", 0)
            status = data.get("status", "未知")
            details = data.get("details", "")
            last_update = data.get("last_update", "")
            
            # 插入数据
            item = self.progress_tree.insert("", "end", values=(
                account,
                f"{progress}%",
                status,
                details,
                last_update
            ))
            
            # 根据状态设置标签
            if status == "完成":
                self.progress_tree.item(item, tags=("success",))
            elif status == "错误" or status == "失败":
                self.progress_tree.item(item, tags=("error",))
            elif status == "警告":
                self.progress_tree.item(item, tags=("warning",))
            elif status == "处理中" or status == "上传中" or status == "设置中":
                self.progress_tree.item(item, tags=("processing",))
        
        # 配置标签样式
        self.progress_tree.tag_configure("success", foreground="green")
        self.progress_tree.tag_configure("error", foreground="red")
        self.progress_tree.tag_configure("warning", foreground="orange")
        self.progress_tree.tag_configure("processing", foreground="blue")
    
    def update_total_progress(self) -> None:
        """更新总进度"""
        if not self.progress_data:
            self.total_progress_bar["value"] = 0
            self.total_progress_label.config(text="0%")
            self.total_status_label.config(text="就绪")
            return
        
        # 计算总进度
        total_progress = 0
        completed_count = 0
        error_count = 0
        processing_count = 0
        
        for data in self.progress_data.values():
            progress = data.get("progress", 0)
            status = data.get("status", "")
            
            total_progress += progress
            
            if status == "完成":
                completed_count += 1
            elif status == "错误" or status == "失败":
                error_count += 1
            elif status == "处理中" or status == "上传中" or status == "设置中":
                processing_count += 1
        
        # 计算平均进度
        avg_progress = total_progress / len(self.progress_data) if self.progress_data else 0
        
        # 更新进度条
        self.total_progress_bar["value"] = avg_progress
        self.total_progress_label.config(text=f"{int(avg_progress)}%")
        
        # 更新状态
        if error_count > 0:
            self.total_status_label.config(text=f"错误 ({error_count}/{len(self.progress_data)})", foreground="red")
        elif processing_count > 0:
            self.total_status_label.config(text=f"处理中 ({processing_count}/{len(self.progress_data)})", foreground="blue")
        elif completed_count == len(self.progress_data):
            self.total_status_label.config(text="全部完成", foreground="green")
        else:
            self.total_status_label.config(text=f"部分完成 ({completed_count}/{len(self.progress_data)})", foreground="orange")
    
    def clear_progress(self) -> None:
        """清空进度数据"""
        self.progress_data = {}
        self.update_progress_tree()
        self.update_total_progress()
        self.log("已清空进度数据")
