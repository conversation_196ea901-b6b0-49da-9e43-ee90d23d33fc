"""
设置对话框模块 - 负责修改设置
"""

import os
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from typing import Dict, Any, Callable, Optional

from ...common.config import ConfigManager

class SettingsDialog:
    """设置对话框类，负责修改设置"""

    def __init__(self, parent: tk.Tk, config_manager: ConfigManager, log_callback: Callable = None):
        """
        初始化设置对话框

        Args:
            parent: 父窗口
            config_manager: 配置管理器
            log_callback: 日志回调函数
        """
        self.parent = parent
        self.config_manager = config_manager
        self.log_callback = log_callback

        # 初始化变量
        self.init_variables()

        # 计算居中位置
        width = 600
        height = 400
        screen_width = parent.winfo_screenwidth()
        screen_height = parent.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2

        # 创建对话框并直接设置居中位置
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("设置")
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")  # 直接设置大小和位置
        self.dialog.resizable(True, True)
        self.dialog.transient(parent)  # 设置为父窗口的临时窗口
        self.dialog.grab_set()  # 模态对话框

        # 设置窗口图标
        try:
            self.dialog.iconbitmap(parent.iconbitmap())
        except:
            pass

        # 创建UI
        self.create_ui()

        # 加载配置
        self.load_config()

    def log(self, message: str) -> None:
        """
        记录日志

        Args:
            message: 日志消息
        """
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def init_variables(self) -> None:
        """初始化变量"""
        # 目录变量
        self.account_dir = tk.StringVar()
        self.video_dir = tk.StringVar()
        self.cover_dir = tk.StringVar()
        self.processed_dir = tk.StringVar()
        self.processed_covers_dir = tk.StringVar()
        self.violation_dir = tk.StringVar()

        # 运行选项变量
        self.headless_mode = tk.BooleanVar(value=False)
        self.archive_completed = tk.BooleanVar(value=True)
        self.draft_limit = tk.IntVar(value=0)
        self.loop_limit = tk.IntVar(value=0)
        self.concurrent_accounts = tk.IntVar(value=1)

        # 界面选项变量
        self.font_size = tk.IntVar(value=10)
        self.auto_save_config = tk.BooleanVar(value=True)
        self.confirm_on_exit = tk.BooleanVar(value=True)
        self.show_tooltips = tk.BooleanVar(value=True)

    def create_ui(self) -> None:
        """创建用户界面"""
        # 创建标签页控件
        notebook = ttk.Notebook(self.dialog)
        notebook.pack(fill=tk.BOTH, expand=True, pady=5, padx=5)

        # 创建目录设置标签页
        dir_settings_tab = ttk.Frame(notebook, padding=10)
        notebook.add(dir_settings_tab, text="目录设置")
        self.create_dir_settings(dir_settings_tab)

        # 创建运行选项标签页
        run_settings_tab = ttk.Frame(notebook, padding=10)
        notebook.add(run_settings_tab, text="运行选项")
        self.create_run_settings(run_settings_tab)

        # 创建界面选项标签页
        ui_settings_tab = ttk.Frame(notebook, padding=10)
        notebook.add(ui_settings_tab, text="界面选项")
        self.create_ui_settings(ui_settings_tab)

        # 创建底部按钮
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, pady=5, padx=5)

        ttk.Button(button_frame, text="确定", command=self.on_ok).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="取消", command=self.on_cancel).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="应用", command=self.on_apply).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="重置", command=self.on_reset).pack(side=tk.LEFT, padx=5)

    def create_dir_settings(self, parent: ttk.Frame) -> None:
        """
        创建目录设置界面

        Args:
            parent: 父容器
        """
        # 账号目录
        ttk.Label(parent, text="账号目录:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(parent, textvariable=self.account_dir, width=50).grid(row=0, column=1, sticky=tk.W, pady=5)
        ttk.Button(parent, text="浏览", command=lambda: self.select_dir(self.account_dir)).grid(row=0, column=2, padx=5, pady=5)

        # 视频目录
        ttk.Label(parent, text="视频目录:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(parent, textvariable=self.video_dir, width=50).grid(row=1, column=1, sticky=tk.W, pady=5)
        ttk.Button(parent, text="浏览", command=lambda: self.select_dir(self.video_dir)).grid(row=1, column=2, padx=5, pady=5)

        # 封面目录
        ttk.Label(parent, text="封面目录:").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Entry(parent, textvariable=self.cover_dir, width=50).grid(row=2, column=1, sticky=tk.W, pady=5)
        ttk.Button(parent, text="浏览", command=lambda: self.select_dir(self.cover_dir)).grid(row=2, column=2, padx=5, pady=5)

        # 已处理目录
        ttk.Label(parent, text="待存稿视频目录:").grid(row=3, column=0, sticky=tk.W, pady=5)
        ttk.Entry(parent, textvariable=self.processed_dir, width=50).grid(row=3, column=1, sticky=tk.W, pady=5)
        ttk.Button(parent, text="浏览", command=lambda: self.select_dir(self.processed_dir)).grid(row=3, column=2, padx=5, pady=5)

        # 已处理封面目录
        ttk.Label(parent, text="待存稿封面目录:").grid(row=4, column=0, sticky=tk.W, pady=5)
        ttk.Entry(parent, textvariable=self.processed_covers_dir, width=50).grid(row=4, column=1, sticky=tk.W, pady=5)
        ttk.Button(parent, text="浏览", command=lambda: self.select_dir(self.processed_covers_dir)).grid(row=4, column=2, padx=5, pady=5)

        # 违规账号目录
        ttk.Label(parent, text="违规账号目录:").grid(row=5, column=0, sticky=tk.W, pady=5)
        ttk.Entry(parent, textvariable=self.violation_dir, width=50).grid(row=5, column=1, sticky=tk.W, pady=5)
        ttk.Button(parent, text="浏览", command=lambda: self.select_dir(self.violation_dir)).grid(row=5, column=2, padx=5, pady=5)

        # 创建目录按钮
        ttk.Button(parent, text="创建所有目录", command=self.create_all_dirs).grid(row=6, column=1, sticky=tk.W, pady=10)

    def create_run_settings(self, parent: ttk.Frame) -> None:
        """
        创建运行选项界面

        Args:
            parent: 父容器
        """
        # 导入美化复选框
        from ..styles.checkbox_styles import create_beautiful_checkbox

        # 无头模式
        headless_cb = create_beautiful_checkbox(parent, text="无头模式（不显示浏览器窗口）", variable=self.headless_mode, style_type="modern")
        headless_cb.grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=5)

        # 归档模式
        archive_cb = create_beautiful_checkbox(parent, text="归档视频（不删除）", variable=self.archive_completed, style_type="success")
        archive_cb.grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=5)

        # 存稿数量限制
        ttk.Label(parent, text="存稿数量限制:").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Spinbox(parent, from_=0, to=1000, textvariable=self.draft_limit, width=5).grid(row=2, column=1, sticky=tk.W, pady=5)
        ttk.Label(parent, text="（0表示不限制）").grid(row=2, column=2, sticky=tk.W, pady=5)

        # 循环次数限制
        ttk.Label(parent, text="循环次数限制:").grid(row=3, column=0, sticky=tk.W, pady=5)
        ttk.Spinbox(parent, from_=0, to=1000, textvariable=self.loop_limit, width=5).grid(row=3, column=1, sticky=tk.W, pady=5)
        ttk.Label(parent, text="（0表示不限制）").grid(row=3, column=2, sticky=tk.W, pady=5)

        # 并发账号数量
        ttk.Label(parent, text="并发账号数量:").grid(row=4, column=0, sticky=tk.W, pady=5)
        ttk.Spinbox(parent, from_=1, to=20, textvariable=self.concurrent_accounts, width=5).grid(row=4, column=1, sticky=tk.W, pady=5)
        ttk.Label(parent, text="（同时处理的账号数量，推荐根据电脑性能设置1-5）").grid(row=4, column=2, sticky=tk.W, pady=5)

    def create_ui_settings(self, parent: ttk.Frame) -> None:
        """
        创建界面选项界面

        Args:
            parent: 父容器
        """
        # 字体大小
        ttk.Label(parent, text="界面字体大小:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Spinbox(parent, from_=8, to=20, textvariable=self.font_size, width=5).grid(row=0, column=1, sticky=tk.W, pady=5)
        ttk.Label(parent, text="（需要重启程序生效）").grid(row=0, column=2, sticky=tk.W, pady=5)

        # 自动保存配置
        ttk.Checkbutton(parent, text="自动保存配置", variable=self.auto_save_config).grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=5)

        # 退出确认
        ttk.Checkbutton(parent, text="退出时确认", variable=self.confirm_on_exit).grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=5)

        # 显示工具提示
        ttk.Checkbutton(parent, text="显示工具提示", variable=self.show_tooltips).grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=5)



    def select_dir(self, var: tk.StringVar) -> None:
        """
        选择目录

        Args:
            var: 存储目录路径的变量
        """
        # 打开目录对话框
        dir_path = filedialog.askdirectory()
        if dir_path:
            var.set(dir_path)

    def create_all_dirs(self) -> None:
        """创建所有目录"""
        dirs = [
            self.account_dir.get(),
            self.video_dir.get(),
            self.cover_dir.get(),
            self.processed_dir.get(),
            self.processed_covers_dir.get(),
            self.violation_dir.get()
        ]

        created_count = 0
        for dir_path in dirs:
            if dir_path and not os.path.exists(dir_path):
                try:
                    os.makedirs(dir_path, exist_ok=True)
                    created_count += 1
                    self.log(f"已创建目录: {dir_path}")
                except Exception as e:
                    self.log(f"创建目录失败: {dir_path}, 错误: {str(e)}")

        if created_count > 0:
            messagebox.showinfo("创建目录", f"已成功创建 {created_count} 个目录")
        else:
            messagebox.showinfo("创建目录", "所有目录已存在，无需创建")

    def load_config(self) -> None:
        """加载配置"""
        # 加载目录配置
        self.account_dir.set(self.config_manager.get("account_dir", ""))
        self.video_dir.set(self.config_manager.get("video_dir", ""))
        self.cover_dir.set(self.config_manager.get("cover_dir", ""))
        self.processed_dir.set(self.config_manager.get("processed_dir", ""))
        self.processed_covers_dir.set(self.config_manager.get("processed_covers_dir", ""))
        self.violation_dir.set(self.config_manager.get("violation_dir", ""))

        # 加载运行选项配置
        self.headless_mode.set(self.config_manager.get("headless_mode", False))
        self.archive_completed.set(self.config_manager.get("archive_completed", True))
        self.draft_limit.set(self.config_manager.get("draft_limit", 0))
        self.loop_limit.set(self.config_manager.get("loop_limit", 0))
        self.concurrent_accounts.set(self.config_manager.get("concurrent_accounts", 1))

        # 加载界面选项配置
        self.font_size.set(self.config_manager.get("font_size", 10))
        self.auto_save_config.set(self.config_manager.get("auto_save_config", True))
        self.confirm_on_exit.set(self.config_manager.get("confirm_on_exit", True))
        self.show_tooltips.set(self.config_manager.get("show_tooltips", True))

    def save_config(self) -> None:
        """保存配置"""
        # 更新配置
        config_dict = {
            # 目录配置
            "account_dir": self.account_dir.get(),
            "video_dir": self.video_dir.get(),
            "cover_dir": self.cover_dir.get(),
            "processed_dir": self.processed_dir.get(),
            "processed_covers_dir": self.processed_covers_dir.get(),
            "violation_dir": self.violation_dir.get(),

            # 运行选项配置
            "headless_mode": self.headless_mode.get(),
            "archive_completed": self.archive_completed.get(),
            "draft_limit": self.draft_limit.get(),
            "loop_limit": self.loop_limit.get(),
            "concurrent_accounts": self.concurrent_accounts.get(),

            # 界面选项配置
            "font_size": self.font_size.get(),
            "auto_save_config": self.auto_save_config.get(),
            "confirm_on_exit": self.confirm_on_exit.get(),
            "show_tooltips": self.show_tooltips.get()
        }

        # 保存配置
        self.config_manager.update(config_dict)
        self.log("已保存配置")

    def on_ok(self) -> None:
        """确定按钮事件处理"""
        # 保存配置
        self.save_config()

        # 关闭对话框
        self.dialog.destroy()

    def on_cancel(self) -> None:
        """取消按钮事件处理"""
        # 关闭对话框
        self.dialog.destroy()

    def on_apply(self) -> None:
        """应用按钮事件处理"""
        # 保存配置
        self.save_config()

    def on_reset(self) -> None:
        """重置按钮事件处理"""
        # 确认重置
        if not messagebox.askyesno("确认重置", "确定要重置所有设置吗？"):
            return

        # 重置配置
        self.config_manager.config = self.config_manager._get_default_config()

        # 重新加载配置
        self.load_config()

        self.log("已重置配置")

    @staticmethod
    def show_dialog(parent: tk.Tk, config_manager: ConfigManager, log_callback: Callable = None) -> None:
        """
        显示设置对话框

        Args:
            parent: 父窗口
            config_manager: 配置管理器
            log_callback: 日志回调函数
        """
        dialog = SettingsDialog(parent, config_manager, log_callback)
        parent.wait_window(dialog.dialog)
