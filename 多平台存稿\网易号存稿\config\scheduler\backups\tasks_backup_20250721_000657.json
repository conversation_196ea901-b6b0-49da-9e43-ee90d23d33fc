{"version": "1.0", "last_updated": "2025-07-21T00:06:55.469715", "tasks": [{"id": "157d92d0-0893-4cbe-898a-2e7253becda5", "name": "测试任务 - 每分钟执行", "description": "用于测试定时任务功能的示例任务", "task_type": "interval", "status": "disabled", "action_type": "data_query", "action_params": {"platforms": ["<PERSON><PERSON><PERSON>", "netease"], "accounts": "all"}, "trigger_config": {"weeks": 0, "days": 0, "hours": 0, "minutes": 1, "seconds": 0}, "created_time": "2025-07-21T00:04:41.950269+08:00", "last_run_time": null, "next_run_time": null, "execution_history": [], "max_history_records": 100}]}