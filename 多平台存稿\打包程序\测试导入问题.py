#!/usr/bin/env python3
"""
测试统一自动打包.py的导入问题
"""

import sys
from pathlib import Path

def test_basic_imports():
    """测试基础导入"""
    print("🔍 测试基础导入...")
    
    try:
        import os, sys, shutil, subprocess, datetime, threading
        import importlib, ast, json, time
        from pathlib import Path
        print("✅ 基础模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ 基础模块导入失败: {e}")
        return False

def test_gui_imports():
    """测试GUI导入"""
    print("🔍 测试GUI导入...")
    
    try:
        import tkinter as tk
        from tkinter import ttk, messagebox, scrolledtext, filedialog
        print("✅ GUI模块导入成功")
        return True
    except ImportError as e:
        print(f"⚠️  GUI模块导入失败: {e}")
        print("  这在某些环境中是正常的")
        return True  # GUI不可用不算错误

def test_pyinstaller_import():
    """测试PyInstaller导入"""
    print("🔍 测试PyInstaller导入...")
    
    # 测试正确的导入方式
    try:
        import PyInstaller
        try:
            version = PyInstaller.__version__
        except AttributeError:
            version = "已安装"
        print(f"✅ PyInstaller导入成功，版本: {version}")
        return True
    except ImportError:
        print("⚠️  PyInstaller未安装")
        print("  这是正常的，程序会自动安装")
        return True  # 未安装不算错误，程序会自动处理

def test_unified_packager_import():
    """测试统一打包器导入"""
    print("🔍 测试统一打包器导入...")
    
    try:
        # 添加当前目录到路径
        sys.path.insert(0, str(Path(__file__).parent))
        
        from 统一自动打包 import UnifiedPackager, GUI_AVAILABLE
        print("✅ 统一打包器导入成功")
        
        # 测试创建实例
        packager = UnifiedPackager()
        print("✅ 打包器实例创建成功")
        
        return True
    except ImportError as e:
        print(f"❌ 统一打包器导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 统一打包器测试失败: {e}")
        return False

def test_dependency_check():
    """测试依赖检查功能"""
    print("🔍 测试依赖检查功能...")
    
    try:
        sys.path.insert(0, str(Path(__file__).parent))
        from 统一自动打包 import UnifiedPackager
        
        packager = UnifiedPackager()
        
        # 测试依赖检查方法（不实际执行安装）
        print("  测试check_dependencies方法...")
        # 这里只测试方法存在，不实际执行
        if hasattr(packager, 'check_dependencies'):
            print("✅ check_dependencies方法存在")
        else:
            print("❌ check_dependencies方法不存在")
            return False
        
        return True
    except Exception as e:
        print(f"❌ 依赖检查测试失败: {e}")
        return False

def test_syntax_errors():
    """测试语法错误"""
    print("🔍 测试语法错误...")
    
    try:
        import py_compile
        script_path = Path(__file__).parent / "统一自动打包.py"
        
        if not script_path.exists():
            print("❌ 统一自动打包.py文件不存在")
            return False
        
        # 编译检查语法
        py_compile.compile(str(script_path), doraise=True)
        print("✅ 语法检查通过")
        return True
    except py_compile.PyCompileError as e:
        print(f"❌ 语法错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 语法检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("统一自动打包器 - 导入问题检查")
    print("=" * 60)
    
    tests = [
        ("基础导入", test_basic_imports),
        ("GUI导入", test_gui_imports),
        ("PyInstaller导入", test_pyinstaller_import),
        ("统一打包器导入", test_unified_packager_import),
        ("依赖检查功能", test_dependency_check),
        ("语法错误检查", test_syntax_errors),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 出错: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有导入测试通过！")
        print("✅ 第67行PyInstaller导入问题已修复")
        print("✅ 统一自动打包器可以正常使用")
    else:
        print("⚠️  部分测试失败，需要进一步检查")
    
    print("=" * 60)
    
    return passed == total

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
