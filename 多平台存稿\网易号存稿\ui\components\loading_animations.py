"""
加载动画组件 - 提供多种加载动画效果
"""

import tkinter as tk
from tkinter import ttk
import time
import math
from typing import Optional, Callable, List, Dict, Any

class BaseLoadingAnimation:
    """加载动画基类"""
    
    def __init__(self, 
                 parent: tk.Widget, 
                 width: int = 100, 
                 height: int = 100,
                 bg_color: str = None,
                 fg_color: str = "#0d6efd",
                 text: str = "加载中...",
                 font: tuple = ("微软雅黑", 10),
                 show_text: bool = True):
        """
        初始化加载动画
        
        参数:
            parent: 父容器
            width: 动画宽度
            height: 动画高度
            bg_color: 背景颜色 (None表示使用父容器背景)
            fg_color: 前景颜色 (动画主色)
            text: 显示文本
            font: 文本字体
            show_text: 是否显示文本
        """
        self.parent = parent
        self.width = width
        self.height = height
        self.bg_color = bg_color or parent.cget("background")
        self.fg_color = fg_color
        self.text = text
        self.font = font
        self.show_text = show_text
        
        # 创建容器框架
        self.container = tk.Frame(parent, width=width, height=height, bg=self.bg_color)
        
        # 创建画布
        self.canvas = tk.Canvas(
            self.container, 
            width=width, 
            height=height if not show_text else height - 20,
            bg=self.bg_color,
            highlightthickness=0
        )
        self.canvas.pack(pady=(0, 10 if show_text else 0))
        
        # 创建文本标签
        if show_text:
            self.text_label = tk.Label(
                self.container,
                text=text,
                font=font,
                bg=self.bg_color,
                fg=self.fg_color
            )
            self.text_label.pack(pady=(0, 5))
        
        # 动画状态
        self.is_running = False
        self.animation_id = None
        self.start_time = None
    
    def start(self):
        """开始动画"""
        if self.is_running:
            return
        
        self.is_running = True
        self.start_time = time.time()
        self._animate()
        return self
    
    def stop(self):
        """停止动画"""
        if self.animation_id:
            self.canvas.after_cancel(self.animation_id)
            self.animation_id = None
        self.is_running = False
        return self
    
    def _animate(self):
        """动画帧更新方法，由子类实现具体动画效果"""
        pass
    
    def pack(self, **kwargs):
        """打包容器"""
        self.container.pack(**kwargs)
        return self
    
    def grid(self, **kwargs):
        """网格布局容器"""
        self.container.grid(**kwargs)
        return self
    
    def place(self, **kwargs):
        """位置布局容器"""
        self.container.place(**kwargs)
        return self
    
    def destroy(self):
        """销毁动画"""
        self.stop()
        self.container.destroy()


class SpinnerAnimation(BaseLoadingAnimation):
    """旋转圆点动画"""
    
    def __init__(self, 
                 parent: tk.Widget, 
                 width: int = 100, 
                 height: int = 100,
                 bg_color: str = None,
                 fg_color: str = "#0d6efd",
                 text: str = "加载中...",
                 font: tuple = ("微软雅黑", 10),
                 show_text: bool = True,
                 dot_count: int = 8,
                 dot_radius: int = 4,
                 circle_radius: int = 30,
                 speed: float = 1.0):
        """
        初始化旋转圆点动画
        
        参数:
            parent: 父容器
            width: 动画宽度
            height: 动画高度
            bg_color: 背景颜色
            fg_color: 前景颜色
            text: 显示文本
            font: 文本字体
            show_text: 是否显示文本
            dot_count: 圆点数量
            dot_radius: 圆点半径
            circle_radius: 旋转半径
            speed: 旋转速度
        """
        super().__init__(parent, width, height, bg_color, fg_color, text, font, show_text)
        self.dot_count = dot_count
        self.dot_radius = dot_radius
        self.circle_radius = circle_radius
        self.speed = speed
        self.dots = []
        
        # 创建圆点
        center_x = width // 2
        center_y = (height if not show_text else height - 20) // 2
        
        for i in range(dot_count):
            angle = 2 * math.pi * i / dot_count
            x = center_x + circle_radius * math.cos(angle)
            y = center_y + circle_radius * math.sin(angle)
            
            dot = self.canvas.create_oval(
                x - dot_radius, 
                y - dot_radius, 
                x + dot_radius, 
                y + dot_radius, 
                fill=self.fg_color,
                outline=""
            )
            self.dots.append(dot)
    
    def _animate(self):
        """更新动画帧"""
        if not self.is_running:
            return
        
        elapsed = time.time() - self.start_time
        center_x = self.width // 2
        center_y = (self.height if not self.show_text else self.height - 20) // 2
        
        for i, dot in enumerate(self.dots):
            # 计算当前角度
            angle = 2 * math.pi * i / self.dot_count + elapsed * self.speed * 2
            
            # 计算新位置
            x = center_x + self.circle_radius * math.cos(angle)
            y = center_y + self.circle_radius * math.sin(angle)
            
            # 计算不透明度 (0.3-1.0)
            opacity = 0.3 + 0.7 * (0.5 + 0.5 * math.sin(angle))
            
            # 创建带透明度的颜色
            r, g, b = self._hex_to_rgb(self.fg_color)
            color = f"#{r:02x}{g:02x}{b:02x}"
            
            # 更新圆点位置和颜色
            self.canvas.coords(
                dot, 
                x - self.dot_radius, 
                y - self.dot_radius, 
                x + self.dot_radius, 
                y + self.dot_radius
            )
            self.canvas.itemconfig(dot, fill=color)
        
        # 继续动画
        self.animation_id = self.canvas.after(16, self._animate)  # ~60fps
    
    def _hex_to_rgb(self, hex_color):
        """将十六进制颜色转换为RGB元组"""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))


class PulseAnimation(BaseLoadingAnimation):
    """脉冲波动动画"""
    
    def __init__(self, 
                 parent: tk.Widget, 
                 width: int = 100, 
                 height: int = 100,
                 bg_color: str = None,
                 fg_color: str = "#0d6efd",
                 text: str = "加载中...",
                 font: tuple = ("微软雅黑", 10),
                 show_text: bool = True,
                 min_radius: int = 20,
                 max_radius: int = 40,
                 pulse_speed: float = 1.0,
                 wave_count: int = 3):
        """
        初始化脉冲波动动画
        
        参数:
            parent: 父容器
            width: 动画宽度
            height: 动画高度
            bg_color: 背景颜色
            fg_color: 前景颜色
            text: 显示文本
            font: 文本字体
            show_text: 是否显示文本
            min_radius: 最小半径
            max_radius: 最大半径
            pulse_speed: 脉冲速度
            wave_count: 波浪数量
        """
        super().__init__(parent, width, height, bg_color, fg_color, text, font, show_text)
        self.min_radius = min_radius
        self.max_radius = max_radius
        self.pulse_speed = pulse_speed
        self.wave_count = wave_count
        self.waves = []
        
        # 创建波浪圆
        center_x = width // 2
        center_y = (height if not show_text else height - 20) // 2
        
        for i in range(wave_count):
            wave = self.canvas.create_oval(
                center_x - min_radius, 
                center_y - min_radius, 
                center_x + min_radius, 
                center_y + min_radius, 
                outline=self.fg_color,
                width=2,
                fill=""
            )
            self.waves.append(wave)
    
    def _animate(self):
        """更新动画帧"""
        if not self.is_running:
            return
        
        elapsed = time.time() - self.start_time
        center_x = self.width // 2
        center_y = (self.height if not self.show_text else self.height - 20) // 2
        
        for i, wave in enumerate(self.waves):
            # 计算当前波浪的相位偏移
            phase = 2 * math.pi * i / self.wave_count
            
            # 计算当前半径
            t = (elapsed * self.pulse_speed + phase) % (2 * math.pi)
            radius_range = self.max_radius - self.min_radius
            current_radius = self.min_radius + radius_range * (0.5 + 0.5 * math.sin(t))
            
            # 计算不透明度 (0.2-1.0)
            opacity = 1.0 - 0.8 * (current_radius - self.min_radius) / radius_range
            
            # 更新波浪位置和透明度
            self.canvas.coords(
                wave, 
                center_x - current_radius, 
                center_y - current_radius, 
                center_x + current_radius, 
                center_y + current_radius
            )
            
            # 更新线条宽度和颜色
            line_width = max(1, int(3 * opacity))
            self.canvas.itemconfig(wave, width=line_width)
        
        # 继续动画
        self.animation_id = self.canvas.after(16, self._animate)  # ~60fps


class ProgressBarAnimation(BaseLoadingAnimation):
    """进度条动画"""
    
    def __init__(self, 
                 parent: tk.Widget, 
                 width: int = 200, 
                 height: int = 60,
                 bg_color: str = None,
                 fg_color: str = "#0d6efd",
                 text: str = "加载中...",
                 font: tuple = ("微软雅黑", 10),
                 show_text: bool = True,
                 bar_height: int = 10,
                 progress: float = 0.0,
                 indeterminate: bool = True,
                 animation_speed: float = 1.0):
        """
        初始化进度条动画
        
        参数:
            parent: 父容器
            width: 动画宽度
            height: 动画高度
            bg_color: 背景颜色
            fg_color: 前景颜色
            text: 显示文本
            font: 文本字体
            show_text: 是否显示文本
            bar_height: 进度条高度
            progress: 初始进度 (0.0-1.0)
            indeterminate: 是否为不确定进度模式
            animation_speed: 动画速度
        """
        super().__init__(parent, width, height, bg_color, fg_color, text, font, show_text)
        self.bar_height = bar_height
        self.progress = progress
        self.indeterminate = indeterminate
        self.animation_speed = animation_speed
        
        # 创建进度条背景
        center_y = (self.height if not self.show_text else self.height - 20) // 2
        self.bar_bg = self.canvas.create_rectangle(
            10, 
            center_y - bar_height // 2, 
            self.width - 10, 
            center_y + bar_height // 2, 
            fill="#e9ecef",
            outline=""
        )
        
        # 创建进度条
        self.bar = self.canvas.create_rectangle(
            10, 
            center_y - bar_height // 2, 
            10 + (self.width - 20) * self.progress, 
            center_y + bar_height // 2, 
            fill=self.fg_color,
            outline=""
        )
        
        # 创建百分比文本
        if not self.indeterminate:
            self.percent_text = self.canvas.create_text(
                self.width // 2,
                center_y,
                text=f"{int(self.progress * 100)}%",
                fill="#ffffff",
                font=("微软雅黑", 9, "bold")
            )
    
    def set_progress(self, progress: float):
        """设置进度值 (0.0-1.0)"""
        self.progress = max(0.0, min(1.0, progress))
        if not self.indeterminate:
            center_y = (self.height if not self.show_text else self.height - 20) // 2
            self.canvas.coords(
                self.bar,
                10, 
                center_y - self.bar_height // 2, 
                10 + (self.width - 20) * self.progress, 
                center_y + self.bar_height // 2
            )
            self.canvas.itemconfig(self.percent_text, text=f"{int(self.progress * 100)}%")
    
    def _animate(self):
        """更新动画帧"""
        if not self.is_running:
            return
        
        elapsed = time.time() - self.start_time
        center_y = (self.height if not self.show_text else self.height - 20) // 2
        
        if self.indeterminate:
            # 不确定模式 - 显示移动的进度块
            t = (elapsed * self.animation_speed) % 1.0
            bar_width = min(80, (self.width - 20) // 3)
            start_x = 10 + (self.width - 20 - bar_width) * t
            
            self.canvas.coords(
                self.bar,
                start_x, 
                center_y - self.bar_height // 2, 
                start_x + bar_width, 
                center_y + self.bar_height // 2
            )
        
        # 继续动画
        self.animation_id = self.canvas.after(16, self._animate)  # ~60fps


class TextFadeAnimation(BaseLoadingAnimation):
    """文字渐变动画"""
    
    def __init__(self, 
                 parent: tk.Widget, 
                 width: int = 150, 
                 height: int = 50,
                 bg_color: str = None,
                 fg_color: str = "#0d6efd",
                 text: str = "加载中...",
                 font: tuple = ("微软雅黑", 14, "bold"),
                 color_cycle: List[str] = None,
                 fade_speed: float = 1.0):
        """
        初始化文字渐变动画
        
        参数:
            parent: 父容器
            width: 动画宽度
            height: 动画高度
            bg_color: 背景颜色
            fg_color: 前景颜色
            text: 显示文本
            font: 文本字体
            color_cycle: 颜色循环列表
            fade_speed: 渐变速度
        """
        # 不使用基类的文本标签
        super().__init__(parent, width, height, bg_color, fg_color, text, font, False)
        
        self.color_cycle = color_cycle or ["#0d6efd", "#6610f2", "#6f42c1", "#d63384", "#dc3545"]
        self.fade_speed = fade_speed
        
        # 创建文本
        self.text_id = self.canvas.create_text(
            self.width // 2,
            self.height // 2,
            text=text,
            font=font,
            fill=self.fg_color
        )
    
    def _animate(self):
        """更新动画帧"""
        if not self.is_running:
            return
        
        elapsed = time.time() - self.start_time
        
        # 计算当前颜色索引和混合比例
        cycle_time = 2.0 / self.fade_speed  # 每个颜色周期的时间
        cycle_position = (elapsed % cycle_time) / cycle_time
        color_index = int(cycle_position * len(self.color_cycle))
        next_color_index = (color_index + 1) % len(self.color_cycle)
        blend_factor = cycle_position * len(self.color_cycle) - color_index
        
        # 获取当前颜色和下一个颜色
        current_color = self._hex_to_rgb(self.color_cycle[color_index])
        next_color = self._hex_to_rgb(self.color_cycle[next_color_index])
        
        # 混合颜色
        blended_color = tuple(
            int(current_color[i] * (1 - blend_factor) + next_color[i] * blend_factor)
            for i in range(3)
        )
        
        # 转换回十六进制
        hex_color = f"#{blended_color[0]:02x}{blended_color[1]:02x}{blended_color[2]:02x}"
        
        # 更新文本颜色
        self.canvas.itemconfig(self.text_id, fill=hex_color)
        
        # 继续动画
        self.animation_id = self.canvas.after(16, self._animate)  # ~60fps
    
    def _hex_to_rgb(self, hex_color):
        """将十六进制颜色转换为RGB元组"""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))


class BouncingDotsAnimation(BaseLoadingAnimation):
    """弹跳点动画"""
    
    def __init__(self, 
                 parent: tk.Widget, 
                 width: int = 100, 
                 height: int = 100,
                 bg_color: str = None,
                 fg_color: str = "#0d6efd",
                 text: str = "加载中...",
                 font: tuple = ("微软雅黑", 10),
                 show_text: bool = True,
                 dot_count: int = 3,
                 dot_radius: int = 5,
                 bounce_height: int = 20,
                 bounce_speed: float = 1.0):
        """
        初始化弹跳点动画
        
        参数:
            parent: 父容器
            width: 动画宽度
            height: 动画高度
            bg_color: 背景颜色
            fg_color: 前景颜色
            text: 显示文本
            font: 文本字体
            show_text: 是否显示文本
            dot_count: 点的数量
            dot_radius: 点的半径
            bounce_height: 弹跳高度
            bounce_speed: 弹跳速度
        """
        super().__init__(parent, width, height, bg_color, fg_color, text, font, show_text)
        self.dot_count = dot_count
        self.dot_radius = dot_radius
        self.bounce_height = bounce_height
        self.bounce_speed = bounce_speed
        self.dots = []
        
        # 创建弹跳点
        canvas_height = self.height if not show_text else self.height - 20
        center_y = canvas_height // 2
        total_width = (2 * dot_radius + 10) * dot_count - 10  # 点之间的间距为10
        start_x = (self.width - total_width) // 2
        
        for i in range(dot_count):
            x = start_x + i * (2 * dot_radius + 10) + dot_radius
            y = center_y
            
            dot = self.canvas.create_oval(
                x - dot_radius, 
                y - dot_radius, 
                x + dot_radius, 
                y + dot_radius, 
                fill=self.fg_color,
                outline=""
            )
            self.dots.append((dot, x))  # 存储点和其x坐标
    
    def _animate(self):
        """更新动画帧"""
        if not self.is_running:
            return
        
        elapsed = time.time() - self.start_time
        canvas_height = self.height if not self.show_text else self.height - 20
        center_y = canvas_height // 2
        
        for i, (dot, x) in enumerate(self.dots):
            # 添加相位差，使点按顺序弹跳
            phase = i * 0.2
            t = (elapsed * self.bounce_speed + phase) % 1.0
            
            # 使用正弦函数计算y位置
            y_offset = -self.bounce_height * math.sin(t * math.pi)
            y = center_y + y_offset
            
            # 更新点的位置
            self.canvas.coords(
                dot, 
                x - self.dot_radius, 
                y - self.dot_radius, 
                x + self.dot_radius, 
                y + self.dot_radius
            )
        
        # 继续动画
        self.animation_id = self.canvas.after(16, self._animate)  # ~60fps
