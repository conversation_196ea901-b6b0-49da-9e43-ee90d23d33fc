"""
共享并发池管理器 - 确保多个平台共享同一个线程池
避免重复创建线程池，提高资源利用效率
"""

import threading
import time
from typing import Dict, Any, Optional, Callable, List
from concurrent.futures import ThreadPoolExecutor
from enum import Enum


class PoolStatus(Enum):
    """线程池状态枚举"""
    IDLE = "idle"           # 空闲
    RUNNING = "running"     # 运行中
    STOPPING = "stopping"  # 正在停止
    STOPPED = "stopped"     # 已停止


class SharedConcurrentPool:
    """共享并发池管理器"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化共享并发池"""
        if hasattr(self, '_initialized'):
            return
            
        self._initialized = True
        self.executor = None
        self.max_workers = 5
        self.status = PoolStatus.IDLE
        self.active_tasks = {}  # 活跃任务字典
        self.task_counter = 0
        self.log_callback = None
        
        # 线程安全锁
        self.pool_lock = threading.Lock()
        
    def set_log_callback(self, callback: Callable) -> None:
        """
        设置日志回调函数
        
        Args:
            callback: 日志回调函数
        """
        self.log_callback = callback
        
    def log(self, message: str) -> None:
        """
        记录日志
        
        Args:
            message: 日志消息
        """
        if self.log_callback:
            self.log_callback(f"[共享并发池] {message}")
        else:
            print(f"[共享并发池] {message}")
    
    def initialize_pool(self, max_workers: int = 5) -> bool:
        """
        初始化线程池
        
        Args:
            max_workers: 最大工作线程数
            
        Returns:
            是否初始化成功
        """
        with self.pool_lock:
            try:
                if self.executor is None:
                    self.max_workers = max_workers
                    self.executor = ThreadPoolExecutor(max_workers=max_workers)
                    self.status = PoolStatus.IDLE
                    self.log(f"共享线程池已初始化，最大工作线程数: {max_workers}")
                    return True
                else:
                    self.log("共享线程池已存在，无需重复初始化")
                    return True
                    
            except Exception as e:
                self.log(f"初始化共享线程池失败: {str(e)}")
                return False
    
    def submit_task(self, platform: str, task_func: Callable, *args, **kwargs) -> Optional[str]:
        """
        提交任务到共享线程池
        
        Args:
            platform: 平台名称（如 "netease", "toutiao"）
            task_func: 任务函数
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            任务ID，如果提交失败则返回None
        """
        with self.pool_lock:
            if self.executor is None:
                self.log("线程池未初始化，无法提交任务")
                return None
                
            if self.status == PoolStatus.STOPPING:
                self.log("线程池正在停止，无法提交新任务")
                return None
                
            try:
                # 生成任务ID
                self.task_counter += 1
                task_id = f"{platform}_{self.task_counter}_{int(time.time())}"
                
                # 提交任务
                future = self.executor.submit(task_func, *args, **kwargs)
                
                # 记录活跃任务
                self.active_tasks[task_id] = {
                    "platform": platform,
                    "future": future,
                    "submit_time": time.time(),
                    "status": "running"
                }
                
                # 更新状态
                if self.status == PoolStatus.IDLE:
                    self.status = PoolStatus.RUNNING
                
                self.log(f"任务已提交到共享线程池: {task_id} (平台: {platform})")
                return task_id
                
            except Exception as e:
                self.log(f"提交任务到共享线程池失败: {str(e)}")
                return None
    
    def get_task_result(self, task_id: str, timeout: Optional[float] = None) -> Any:
        """
        获取任务结果
        
        Args:
            task_id: 任务ID
            timeout: 超时时间
            
        Returns:
            任务结果
        """
        if task_id not in self.active_tasks:
            self.log(f"任务不存在: {task_id}")
            return None
            
        try:
            future = self.active_tasks[task_id]["future"]
            result = future.result(timeout=timeout)
            
            # 更新任务状态
            self.active_tasks[task_id]["status"] = "completed"
            
            return result
            
        except Exception as e:
            self.log(f"获取任务结果失败: {task_id}, 错误: {str(e)}")
            self.active_tasks[task_id]["status"] = "failed"
            return None
    
    def cancel_task(self, task_id: str) -> bool:
        """
        取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否取消成功
        """
        if task_id not in self.active_tasks:
            self.log(f"任务不存在: {task_id}")
            return False
            
        try:
            future = self.active_tasks[task_id]["future"]
            cancelled = future.cancel()
            
            if cancelled:
                self.active_tasks[task_id]["status"] = "cancelled"
                self.log(f"任务已取消: {task_id}")
            else:
                self.log(f"任务无法取消（可能已在执行中）: {task_id}")
                
            return cancelled
            
        except Exception as e:
            self.log(f"取消任务失败: {task_id}, 错误: {str(e)}")
            return False
    
    def get_pool_status(self) -> Dict[str, Any]:
        """
        获取线程池状态
        
        Returns:
            线程池状态信息
        """
        with self.pool_lock:
            # 清理已完成的任务
            self._cleanup_completed_tasks()
            
            active_count = len([task for task in self.active_tasks.values() 
                              if task["status"] == "running"])
            
            platform_stats = {}
            for task in self.active_tasks.values():
                platform = task["platform"]
                if platform not in platform_stats:
                    platform_stats[platform] = {"running": 0, "completed": 0, "failed": 0, "cancelled": 0}
                platform_stats[platform][task["status"]] += 1
            
            return {
                "status": self.status.value,
                "max_workers": self.max_workers,
                "active_tasks": active_count,
                "total_tasks": len(self.active_tasks),
                "platform_stats": platform_stats,
                "pool_initialized": self.executor is not None
            }
    
    def _cleanup_completed_tasks(self) -> None:
        """清理已完成的任务"""
        completed_tasks = []
        current_time = time.time()
        
        for task_id, task_info in self.active_tasks.items():
            # 清理超过1小时的已完成任务
            if (task_info["status"] in ["completed", "failed", "cancelled"] and 
                current_time - task_info["submit_time"] > 3600):
                completed_tasks.append(task_id)
        
        for task_id in completed_tasks:
            del self.active_tasks[task_id]
    
    def shutdown_pool(self, wait: bool = True) -> None:
        """
        关闭线程池
        
        Args:
            wait: 是否等待所有任务完成
        """
        with self.pool_lock:
            if self.executor is None:
                self.log("线程池未初始化，无需关闭")
                return
                
            self.status = PoolStatus.STOPPING
            self.log("正在关闭共享线程池...")
            
            try:
                self.executor.shutdown(wait=wait)
                self.executor = None
                self.status = PoolStatus.STOPPED
                self.active_tasks.clear()
                self.log("共享线程池已关闭")
                
            except Exception as e:
                self.log(f"关闭共享线程池时发生错误: {str(e)}")
    
    def is_available(self) -> bool:
        """
        检查线程池是否可用
        
        Returns:
            是否可用
        """
        return (self.executor is not None and 
                self.status in [PoolStatus.IDLE, PoolStatus.RUNNING])


# 全局共享并发池实例
shared_pool = SharedConcurrentPool()


def get_shared_pool() -> SharedConcurrentPool:
    """
    获取共享并发池实例
    
    Returns:
        共享并发池实例
    """
    return shared_pool
