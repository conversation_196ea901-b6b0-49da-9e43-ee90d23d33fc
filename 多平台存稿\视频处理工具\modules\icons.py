"""
图标模块 - 提供应用程序图标
"""

from tkinter import PhotoImage
from typing import Dict, Optional

# 图标缓存
_icon_cache: Dict[str, PhotoImage] = {}

def create_settings_icon(_):
    """创建设置图标 - 现代化设计"""
    icon = PhotoImage(width=16, height=16)

    # 绘制一个现代化的齿轮图标
    # 外圆
    for x in range(16):
        for y in range(16):
            # 创建一个圆形
            dist = (x-8)**2 + (y-8)**2
            if dist <= 36 and dist >= 16:
                # 添加渐变效果
                if x < 8 and y < 8:  # 左上角 - 亮色
                    icon.put("#777777", (x, y))
                elif x >= 8 and y >= 8:  # 右下角 - 暗色
                    icon.put("#333333", (x, y))
                else:  # 其他位置 - 中间色
                    icon.put("#555555", (x, y))

            # 添加内圆
            if dist <= 9:
                if x < 8 and y < 8:  # 左上角 - 亮色
                    icon.put("#777777", (x, y))
                elif x >= 8 and y >= 8:  # 右下角 - 暗色
                    icon.put("#333333", (x, y))
                else:  # 其他位置 - 中间色
                    icon.put("#555555", (x, y))

    return icon

def create_folder_icon(_):
    """创建文件夹图标 - 现代化设计"""
    icon = PhotoImage(width=16, height=16)

    # 绘制一个现代化的文件夹图标
    for x in range(16):
        for y in range(16):
            # 文件夹主体
            if y >= 5 and y <= 13 and x >= 2 and x <= 14:
                # 添加渐变效果
                if y < 9:  # 上半部分 - 亮色
                    icon.put("#4dabf7", (x, y))
                else:  # 下半部分 - 暗色
                    icon.put("#228be6", (x, y))

            # 文件夹顶部
            if y >= 3 and y <= 5 and x >= 2 and x <= 8:
                icon.put("#4dabf7", (x, y))

            # 添加边框效果
            if ((y == 5 or y == 13) and x >= 2 and x <= 14) or \
               ((x == 2 or x == 14) and y >= 5 and y <= 13) or \
               ((y == 3 or y == 5) and x >= 2 and x <= 8) or \
               ((x == 2 or x == 8) and y >= 3 and y <= 5):
                icon.put("#1971c2", (x, y))

    return icon

def create_save_icon(_):
    """创建保存图标 - 现代化设计"""
    icon = PhotoImage(width=16, height=16)

    # 绘制一个现代化的保存图标
    for x in range(16):
        for y in range(16):
            # 外框
            if x >= 3 and x <= 13 and y >= 3 and y <= 13:
                if x == 3 or x == 13 or y == 3 or y == 13:
                    icon.put("#555555", (x, y))

                # 磁盘主体
                elif y >= 4 and y <= 12 and x >= 4 and x <= 12:
                    # 添加渐变效果
                    if y < 8:  # 上半部分 - 亮色
                        icon.put("#40c057", (x, y))
                    else:  # 下半部分 - 暗色
                        icon.put("#2b8a3e", (x, y))

                # 磁盘中心
                if x >= 6 and x <= 10 and y >= 6 and y <= 10:
                    if x == 6 or x == 10 or y == 6 or y == 10:
                        icon.put("#555555", (x, y))
                    else:
                        icon.put("#f8f9fa", (x, y))

                # 添加保存指示器
                if y >= 4 and y <= 6 and x >= 9 and x <= 12:
                    icon.put("#f8f9fa", (x, y))

    return icon

def create_dark_mode_icon(_):
    """创建黑暗模式图标 - 现代化设计"""
    icon = PhotoImage(width=16, height=16)

    # 绘制一个现代化的月亮图标
    for x in range(16):
        for y in range(16):
            # 月亮主体 - 圆形
            dist = (x-5)**2 + (y-8)**2
            if dist <= 25:
                # 添加渐变效果
                if x < 5 and y < 8:  # 左上角 - 亮色
                    icon.put("#5c7cfa", (x, y))
                elif x >= 5 and y >= 8:  # 右下角 - 暗色
                    icon.put("#3b5bdb", (x, y))
                else:  # 其他位置 - 中间色
                    icon.put("#4263eb", (x, y))

                # 添加月亮阴影部分
                if (x-9)**2 + (y-8)**2 <= 16:
                    icon.put("#000000", (x, y))

            # 添加星星
            if ((x == 12 and y == 4) or (x == 13 and y == 5) or (x == 11 and y == 5) or (x == 12 and y == 6)):
                icon.put("#f8f9fa", (x, y))

    return icon

def create_play_icon(_):
    """创建播放图标 - 现代化设计"""
    icon = PhotoImage(width=16, height=16)

    # 绘制一个现代化的播放按钮
    for x in range(16):
        for y in range(16):
            # 圆形背景
            dist = (x-8)**2 + (y-8)**2
            if dist <= 49:  # 半径7的圆
                # 添加渐变效果
                if x < 8 and y < 8:  # 左上角 - 亮色
                    icon.put("#40c057", (x, y))
                elif x >= 8 and y >= 8:  # 右下角 - 暗色
                    icon.put("#2b8a3e", (x, y))
                else:  # 其他位置 - 中间色
                    icon.put("#37b24d", (x, y))

                # 播放三角形 - 白色
                if x >= 7 and x <= 11 and y >= 5 and y <= 11:
                    if (x == 7 and y >= 5 and y <= 11) or \
                       (x == 8 and y >= 6 and y <= 10) or \
                       (x == 9 and y >= 7 and y <= 9) or \
                       (x == 10 and y == 8):
                        icon.put("#ffffff", (x, y))

    return icon

def create_stop_icon(_):
    """创建停止图标 - 现代化设计"""
    icon = PhotoImage(width=16, height=16)

    # 绘制一个现代化的停止按钮
    for x in range(16):
        for y in range(16):
            # 圆形背景
            dist = (x-8)**2 + (y-8)**2
            if dist <= 49:  # 半径7的圆
                # 添加渐变效果
                if x < 8 and y < 8:  # 左上角 - 亮色
                    icon.put("#fa5252", (x, y))
                elif x >= 8 and y >= 8:  # 右下角 - 暗色
                    icon.put("#c92a2a", (x, y))
                else:  # 其他位置 - 中间色
                    icon.put("#e03131", (x, y))

                # 停止方块 - 白色
                if x >= 6 and x <= 10 and y >= 6 and y <= 10:
                    icon.put("#ffffff", (x, y))

    return icon

def create_font_increase_icon(_):
    """创建字体增大图标 - 现代化设计"""
    icon = PhotoImage(width=16, height=16)

    # 绘制一个现代化的A+图标
    for x in range(16):
        for y in range(16):
            # 绘制A - 使用渐变效果
            if (x == 4 and y >= 8 and y <= 12) or (x == 8 and y >= 8 and y <= 12) or (y == 8 and x >= 4 and x <= 8) or (y == 10 and x >= 4 and x <= 8):
                if y < 10:  # 上半部分 - 亮色
                    icon.put("#4dabf7", (x, y))
                else:  # 下半部分 - 暗色
                    icon.put("#1971c2", (x, y))

            # 绘制+ - 使用绿色渐变
            if (x == 12 and y >= 8 and y <= 12) or (y == 10 and x >= 10 and x <= 14):
                if x < 12 or y < 10:  # 左上部分 - 亮色
                    icon.put("#40c057", (x, y))
                else:  # 右下部分 - 暗色
                    icon.put("#2b8a3e", (x, y))

    return icon

def create_font_decrease_icon(_):
    """创建字体减小图标 - 现代化设计"""
    icon = PhotoImage(width=16, height=16)

    # 绘制一个现代化的A-图标
    for x in range(16):
        for y in range(16):
            # 绘制A - 使用渐变效果
            if (x == 4 and y >= 8 and y <= 12) or (x == 8 and y >= 8 and y <= 12) or (y == 8 and x >= 4 and x <= 8) or (y == 10 and x >= 4 and x <= 8):
                if y < 10:  # 上半部分 - 亮色
                    icon.put("#4dabf7", (x, y))
                else:  # 下半部分 - 暗色
                    icon.put("#1971c2", (x, y))

            # 绘制- - 使用红色渐变
            if (y == 10 and x >= 10 and x <= 14):
                if x < 12:  # 左半部分 - 亮色
                    icon.put("#fa5252", (x, y))
                else:  # 右半部分 - 暗色
                    icon.put("#c92a2a", (x, y))

    return icon

def create_gpu_icon(_):
    """创建GPU图标 - 现代化设计"""
    icon = PhotoImage(width=16, height=16)

    # 绘制一个现代化的GPU图标
    for x in range(16):
        for y in range(16):
            # 外框
            if x >= 3 and x <= 13 and y >= 3 and y <= 13:
                if x == 3 or x == 13 or y == 3 or y == 13:
                    icon.put("#555555", (x, y))

                # 填充背景 - 使用渐变效果
                elif x >= 4 and x <= 12 and y >= 4 and y <= 12:
                    if x < 8 and y < 8:  # 左上角 - 亮色
                        icon.put("#15aabf", (x, y))
                    elif x >= 8 and y >= 8:  # 右下角 - 暗色
                        icon.put("#0b7285", (x, y))
                    else:  # 其他位置 - 中间色
                        icon.put("#0c8599", (x, y))

                # 内部网格线
                if ((x == 5 or x == 11) and y >= 5 and y <= 11) or \
                   ((y == 5 or y == 11) and x >= 5 and x <= 11):
                    icon.put("#ffffff", (x, y))

                # 添加芯片中心点
                if x >= 7 and x <= 9 and y >= 7 and y <= 9:
                    if x == 7 or x == 9 or y == 7 or y == 9:
                        icon.put("#ffffff", (x, y))
                    else:
                        icon.put("#ffd43b", (x, y))  # 黄色中心

    return icon

def create_category_icon(_):
    """创建分类标签图标 - 现代化设计"""
    icon = PhotoImage(width=16, height=16)

    # 绘制一个现代化的标签图标
    for x in range(16):
        for y in range(16):
            # 标签主体 - 矩形
            if x >= 3 and x <= 13 and y >= 5 and y <= 11:
                # 添加渐变效果
                if y < 8:  # 上半部分 - 亮色
                    icon.put("#748ffc", (x, y))
                else:  # 下半部分 - 暗色
                    icon.put("#4c6ef5", (x, y))

                # 添加标签孔
                if x >= 4 and x <= 6 and y >= 7 and y <= 9:
                    icon.put("#ffffff", (x, y))

            # 添加标签角
            if (x == 13 and y == 4) or (x == 14 and y == 5) or (x == 13 and y == 12) or (x == 14 and y == 11):
                icon.put("#4c6ef5", (x, y))

    return icon

def get_icon(name: str, size: Optional[tuple] = None) -> PhotoImage:
    """
    获取指定名称的图标

    Args:
        name: 图标名称
        size: 可选的尺寸调整 (width, height)

    Returns:
        PhotoImage 对象
    """
    # 缓存键
    cache_key = name
    if size is not None:
        try:
            cache_key = f"{name}_{size[0]}_{size[1]}"
        except (IndexError, TypeError):
            # 如果size不是有效的元组，使用默认缓存键
            pass

    # 检查缓存
    if cache_key in _icon_cache:
        return _icon_cache[cache_key]

    # 创建一个简单的图标
    icon = None
    if name == "settings":
        icon = create_settings_icon(size)
    elif name == "folder":
        icon = create_folder_icon(size)
    elif name == "save":
        icon = create_save_icon(size)
    elif name == "dark_mode":
        icon = create_dark_mode_icon(size)
    elif name == "play":
        icon = create_play_icon(size)
    elif name == "stop":
        icon = create_stop_icon(size)
    elif name == "font_increase":
        icon = create_font_increase_icon(size)
    elif name == "font_decrease":
        icon = create_font_decrease_icon(size)
    elif name == "gpu":
        icon = create_gpu_icon(size)
    elif name == "category":
        icon = create_category_icon(size)
    else:
        # 如果找不到图标，返回空图标
        icon = PhotoImage(width=1, height=1)

    # 缓存图标
    _icon_cache[cache_key] = icon
    return icon
