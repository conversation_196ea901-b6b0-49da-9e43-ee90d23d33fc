#!/usr/bin/env python3
"""
检查PyInstaller的正确导入方式
"""

def test_pyinstaller_imports():
    """测试不同的PyInstaller导入方式"""
    print("🔍 测试PyInstaller导入方式...")
    
    # 方式1: 导入PyInstaller主模块
    try:
        import PyInstaller
        print("✅ import PyInstaller - 成功")
        
        # 尝试获取版本
        if hasattr(PyInstaller, '__version__'):
            print(f"  版本: {PyInstaller.__version__}")
        else:
            print("  无__version__属性")
    except ImportError:
        print("❌ import PyInstaller - 失败")
    
    # 方式2: 导入PyInstaller.__main__
    try:
        import PyInstaller.__main__
        print("✅ import PyInstaller.__main__ - 成功")
    except ImportError:
        print("❌ import PyInstaller.__main__ - 失败")
    
    # 方式3: 尝试获取版本的其他方式
    try:
        import pkg_resources
        version = pkg_resources.get_distribution('pyinstaller').version
        print(f"✅ 通过pkg_resources获取版本: {version}")
    except:
        print("❌ 通过pkg_resources获取版本 - 失败")
    
    # 方式4: 通过importlib获取版本
    try:
        import importlib.metadata
        version = importlib.metadata.version('pyinstaller')
        print(f"✅ 通过importlib.metadata获取版本: {version}")
    except:
        print("❌ 通过importlib.metadata获取版本 - 失败")
    
    # 方式5: 检查是否可以运行PyInstaller命令
    try:
        import subprocess
        import sys
        result = subprocess.run([sys.executable, '-m', 'PyInstaller', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ 通过命令行获取版本: {version}")
        else:
            print("❌ 通过命令行获取版本 - 失败")
    except:
        print("❌ 通过命令行获取版本 - 失败")

def recommend_best_approach():
    """推荐最佳的检查方式"""
    print("\n💡 推荐的PyInstaller检查方式:")
    
    code = '''
def check_pyinstaller():
    """检查PyInstaller是否可用"""
    try:
        # 方法1: 尝试导入PyInstaller
        import PyInstaller
        
        # 方法2: 尝试获取版本
        version = "已安装"
        try:
            # 优先使用__version__
            if hasattr(PyInstaller, '__version__'):
                version = PyInstaller.__version__
            else:
                # 备用方法：通过importlib.metadata
                import importlib.metadata
                version = importlib.metadata.version('pyinstaller')
        except:
            # 如果都失败，通过命令行检查
            try:
                import subprocess, sys
                result = subprocess.run([sys.executable, '-m', 'PyInstaller', '--version'], 
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    version = result.stdout.strip()
            except:
                pass
        
        print(f"PyInstaller 版本: {version}")
        return True
        
    except ImportError:
        print("PyInstaller 未安装")
        return False
'''
    
    print(code)

def main():
    print("=" * 60)
    print("PyInstaller 导入方式检查")
    print("=" * 60)
    
    test_pyinstaller_imports()
    recommend_best_approach()
    
    print("=" * 60)

if __name__ == "__main__":
    main()
