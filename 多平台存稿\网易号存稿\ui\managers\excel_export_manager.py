#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel导出管理器 - 负责Excel导出相关功能
遵循MECE原则：与Excel导出相关的所有功能集中管理
"""

import datetime
from tkinter import filedialog, messagebox
from typing import Dict, Any, Optional, List

from 网易号存稿.common.logger import logger


class ExcelExportManager:
    """Excel导出管理器 - 负责所有Excel导出相关功能"""
    
    def __init__(self, parent_ui, config_manager, account_data):
        """
        初始化Excel导出管理器
        
        Args:
            parent_ui: 主UI实例
            config_manager: 配置管理器
            account_data: 账号数据处理器
        """
        self.parent_ui = parent_ui
        self.config_manager = config_manager
        self.account_data = account_data
    
    def export_accounts_to_excel(self):
        """导出美化的账号数据到Excel - 根据当前平台智能选择数据"""
        try:
            # 获取当前平台
            current_platform = getattr(self.parent_ui, 'current_platform', 'netease')
            platform_names = {
                'netease': '网易号',
                'toutiao': '头条号',
                'dayu': '大鱼号'
            }
            platform_name = platform_names.get(current_platform, '未知平台')

            # 选择保存位置 - 文件名包含平台信息
            default_filename = f"{platform_name}账号数据_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            file_path = filedialog.asksaveasfilename(
                title=f"导出{platform_name}账号数据",
                defaultextension=".xlsx",
                filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")],
                initialfile=default_filename
            )

            if not file_path:
                return

            # 记录导出的平台信息
            self.parent_ui.log(f"📊 开始导出{platform_name}平台账号数据...")

            # 创建美化的Excel报告
            self._create_beautiful_main_excel_report(file_path, current_platform)

            self.parent_ui.log(f"✅ {platform_name}账号数据已导出到: {file_path}")

        except Exception as e:
            error_msg = f"导出Excel失败: {str(e)}"
            self.parent_ui.log(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)
    
    def _create_beautiful_main_excel_report(self, file_path: str, platform: str = None):
        """创建美化的主界面Excel报告 - 支持平台智能选择"""
        try:
            from openpyxl import Workbook
            from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
            from openpyxl.utils import get_column_letter
        except ImportError:
            raise ImportError("需要安装openpyxl库: pip install openpyxl")
        
        # 创建工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = "账号数据报告"
        
        # 定义样式
        header_font = Font(name="微软雅黑", size=12, bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
        data_font = Font(name="微软雅黑", size=10)
        border = Border(
            left=Side(style="thin"),
            right=Side(style="thin"),
            top=Side(style="thin"),
            bottom=Side(style="thin")
        )
        center_alignment = Alignment(horizontal="center", vertical="center")
        
        # 获取平台信息
        platform_names = {
            'netease': '网易号',
            'toutiao': '头条号',
            'dayu': '大鱼号'
        }
        current_platform = platform or getattr(self.parent_ui, 'current_platform', 'netease')
        platform_name = platform_names.get(current_platform, '未知平台')

        # 设置标题
        ws.merge_cells("A1:K1")
        title_cell = ws["A1"]
        title_cell.value = f"{platform_name}账号数据报告 - {datetime.datetime.now().strftime('%Y年%m月%d日')}"
        title_cell.font = Font(name="微软雅黑", size=16, bold=True, color="4472C4")
        title_cell.alignment = center_alignment
        
        # 设置表头 - 与详细数据界面的表格列一致
        headers = ["账号", "用户名", "七日收益", "总收益", "昨日收益", "总提现", "待提现", "最近提现日", "总播放", "昨日播放", "总粉丝", "昨日粉丝", "草稿箱", "状态", "更新时间"]
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=3, column=col)
            cell.value = header
            cell.font = header_font
            cell.fill = header_fill
            cell.border = border
            cell.alignment = center_alignment
        
        # 获取当前平台的账号数据
        accounts_data_list = self._get_platform_accounts_data(current_platform)

        # 调试信息
        self.parent_ui.log(f"📊 Excel导出调试: 获取到 {len(accounts_data_list) if accounts_data_list else 0} 条{platform_name}账号数据")
        if accounts_data_list:
            self.parent_ui.log(f"📊 Excel导出调试: 数据示例 - {accounts_data_list[0] if accounts_data_list else '无数据'}")

        # 如果没有数据，尝试从账号管理器获取已加载的账号并创建默认数据
        if not accounts_data_list and hasattr(self.parent_ui, 'account_manager'):
            self.parent_ui.log("📊 Excel导出: 从账号管理器获取账号列表创建默认数据")
            loaded_accounts = self.parent_ui.account_manager.load_accounts()
            accounts_data_list = []
            for account in loaded_accounts:
                default_data = {
                    "账号": account,
                    "用户名": "",
                    "总收益": "0",
                    "累计收益": "0",
                    "昨日收益": "0",
                    "总播放": "0",
                    "昨日播放": "0",
                    "总粉丝": "0",
                    "昨日粉丝": "0",
                    "草稿箱": "0",
                    "草稿箱数量": "0",
                    "可提现": "0",
                    "待提现": "0",
                    "状态": "未查询",
                    "更新时间": "",
                    "七天收益": {}
                }
                accounts_data_list.append(default_data)
            self.parent_ui.log(f"📊 Excel导出: 创建了 {len(accounts_data_list)} 条默认数据")

        # 将列表转换为字典格式，以账号名为键
        accounts_data = {}
        if accounts_data_list:
            for account_data in accounts_data_list:
                account_name = account_data.get("账号", "")
                if account_name:
                    accounts_data[account_name] = account_data

        # 调试信息
        self.parent_ui.log(f"📊 Excel导出调试: 转换后字典包含 {len(accounts_data)} 个账号")
        if not accounts_data:
            self.parent_ui.log("⚠️ Excel导出警告: 没有可导出的账号数据！")
            # 检查原始数据文件
            if hasattr(self.account_data, 'data_file'):
                import os
                file_exists = os.path.exists(self.account_data.data_file)
                self.parent_ui.log(f"📊 数据文件状态: {self.account_data.data_file} - {'存在' if file_exists else '不存在'}")
                if file_exists:
                    try:
                        import json
                        with open(self.account_data.data_file, 'r', encoding='utf-8') as f:
                            raw_data = json.load(f)
                        self.parent_ui.log(f"📊 原始数据文件包含 {len(raw_data)} 条记录")
                    except Exception as e:
                        self.parent_ui.log(f"❌ 读取数据文件失败: {e}")

        # 清理数值函数
        def clean_value_for_sum(value):
            """清理数值用于求和"""
            if value is None:
                return 0
            try:
                # 移除货币符号和逗号
                cleaned = str(value).replace("¥", "").replace("元", "").replace(",", "").strip()
                if cleaned == "" or cleaned == "-" or cleaned == "未查询":
                    return 0
                
                # 处理"万"单位
                if "万" in cleaned:
                    num_str = cleaned.replace("万", "")
                    if num_str:
                        return float(num_str) * 10000
                    else:
                        return 0
                
                return float(cleaned)
            except (ValueError, TypeError):
                return 0
        
        # 填充数据 - 与详细数据界面表格结构一致
        row = 4
        total_seven_day = 0
        total_income = 0
        total_yesterday = 0
        total_withdraw_amount = 0
        total_pending_withdraw = 0
        total_plays = 0
        total_yesterday_plays = 0
        total_fans = 0
        total_yesterday_fans = 0
        total_drafts = 0

        for account, data in accounts_data.items():
            # 清理数值数据
            def clean_value(value):
                """清理数值，移除货币符号和逗号"""
                if value is None:
                    return 0
                try:
                    cleaned = str(value).replace("¥", "").replace("元", "").replace(",", "").strip()
                    if cleaned == "" or cleaned == "-" or cleaned == "未查询":
                        return 0

                    # 处理"万"单位
                    if "万" in cleaned:
                        num_str = cleaned.replace("万", "")
                        if num_str:
                            return float(num_str) * 10000
                        else:
                            return 0

                    return float(cleaned)
                except (ValueError, TypeError):
                    return 0

            # 计算七日收益 - 与详细数据界面逻辑一致
            seven_day_income = 0
            if "七日收益" in data and isinstance(data["七日收益"], list):
                # 头条平台格式：七日收益是包含字典的列表
                for day_data in data["七日收益"]:
                    if isinstance(day_data, dict) and "收益" in day_data:
                        try:
                            income_value = float(day_data["收益"])
                            seven_day_income += income_value
                        except:
                            pass
            elif "七日收益数据" in data and isinstance(data["七日收益数据"], dict) and data["七日收益数据"]:
                # 网易号平台格式
                for date, income in data["七日收益数据"].items():
                    try:
                        from 网易号存稿.common.utils import parse_income
                        income_value = parse_income(income)
                        seven_day_income += income_value
                    except:
                        pass

            # 获取其他数据 - 支持多种字段名映射
            income = clean_value(data.get("累计收益", data.get("总收益", "0")))
            yesterday = clean_value(data.get("昨日收益", "0"))
            withdraw_amount = clean_value(data.get("总提现", "0"))
            pending_withdraw = clean_value(data.get("可提现", data.get("待提现", "0")))
            plays = clean_value(data.get("总播放", "0"))
            yesterday_plays = clean_value(data.get("昨日播放", "0"))
            fans = clean_value(data.get("总粉丝", "0"))
            yesterday_fans = clean_value(data.get("昨日粉丝", "0"))
            drafts = clean_value(data.get("草稿箱数量", data.get("草稿箱", "0")))

            # 获取最近提现日
            recent_withdraw_date = data.get("最近提现日期", data.get("最近提现日", data.get("最近提现时间", "")))

            # 累计总计
            total_seven_day += seven_day_income
            total_income += income
            total_yesterday += yesterday
            total_withdraw_amount += withdraw_amount
            total_pending_withdraw += pending_withdraw
            total_plays += plays
            total_yesterday_plays += yesterday_plays
            total_fans += fans
            total_yesterday_fans += yesterday_fans
            total_drafts += drafts

            # 填充行数据 - 与表头顺序一致
            row_data = [
                account,  # 账号
                data.get("用户名", ""),  # 用户名
                f"{seven_day_income:.2f}",  # 七日收益
                f"¥{income:,.2f}" if income > 0 else "¥0.00",  # 总收益
                f"¥{yesterday:,.2f}" if yesterday > 0 else "¥0.00",  # 昨日收益
                f"¥{withdraw_amount:,.2f}" if withdraw_amount > 0 else "¥0.00",  # 总提现
                f"¥{pending_withdraw:,.2f}" if pending_withdraw > 0 else "¥0.00",  # 待提现
                recent_withdraw_date,  # 最近提现日
                f"{plays:,.0f}" if plays > 0 else "0",  # 总播放
                f"{yesterday_plays:,.0f}" if yesterday_plays > 0 else "0",  # 昨日播放
                f"{fans:,.0f}" if fans > 0 else "0",  # 总粉丝
                f"{yesterday_fans:,.0f}" if yesterday_fans > 0 else "0",  # 昨日粉丝
                f"{drafts:,.0f}" if drafts > 0 else "0",  # 草稿箱
                data.get("状态", "未查询"),  # 状态
                data.get("更新时间", "")  # 更新时间
            ]

            for col, value in enumerate(row_data, 1):
                cell = ws.cell(row=row, column=col)
                cell.value = value
                cell.font = data_font
                cell.border = border
                cell.alignment = center_alignment

            row += 1
        
        # 添加总计行 - 与表头结构一致
        total_row_data = [
            "总计",  # 账号
            f"{len(accounts_data)}个账号",  # 用户名
            f"{total_seven_day:.2f}",  # 七日收益
            f"¥{total_income:,.2f}",  # 总收益
            f"¥{total_yesterday:,.2f}",  # 昨日收益
            f"¥{total_withdraw_amount:,.2f}",  # 总提现
            f"¥{total_pending_withdraw:,.2f}",  # 待提现
            "",  # 最近提现日
            f"{total_plays:,.0f}",  # 总播放
            f"{total_yesterday_plays:,.0f}",  # 昨日播放
            f"{total_fans:,.0f}",  # 总粉丝
            f"{total_yesterday_fans:,.0f}",  # 昨日粉丝
            f"{total_drafts:,.0f}",  # 草稿箱
            "",  # 状态
            ""  # 更新时间
        ]
        
        for col, value in enumerate(total_row_data, 1):
            cell = ws.cell(row=row, column=col)
            cell.value = value
            cell.font = Font(name="微软雅黑", size=10, bold=True)
            cell.fill = PatternFill(start_color="E7E6E6", end_color="E7E6E6", fill_type="solid")
            cell.border = border
            cell.alignment = center_alignment
        
        # 调整列宽 - 与详细数据界面表格列对应
        column_widths = [20, 15, 12, 12, 12, 12, 12, 15, 12, 12, 12, 12, 10, 10, 15]
        for i, width in enumerate(column_widths, 1):
            ws.column_dimensions[get_column_letter(i)].width = width
        
        # 设置行高
        for row_num in range(1, row + 1):
            ws.row_dimensions[row_num].height = 25
        
        # 保存文件
        wb.save(file_path)
    
    def export_seven_day_income_to_excel(self, accounts_data):
        """导出七天收益历史到Excel - 根据当前平台智能选择数据"""
        try:
            # 获取当前平台信息
            current_platform = getattr(self.parent_ui, 'current_platform', 'netease')
            platform_names = {
                'netease': '网易号',
                'toutiao': '头条号',
                'dayu': '大鱼号'
            }
            platform_name = platform_names.get(current_platform, '未知平台')

            # 选择保存位置 - 文件名包含平台信息
            default_filename = f"{platform_name}七天收益历史_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            file_path = filedialog.asksaveasfilename(
                title=f"导出{platform_name}七天收益历史",
                defaultextension=".xlsx",
                filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")],
                initialfile=default_filename
            )
            
            if not file_path:
                return
            
            # 获取当前平台的账号数据
            platform_accounts_data = self._get_platform_accounts_data(current_platform)

            # 记录导出信息
            self.parent_ui.log(f"📊 开始导出{platform_name}平台七天收益历史...")

            # 创建七天收益Excel报告
            self._create_seven_day_income_excel(file_path, platform_accounts_data, current_platform)

            self.parent_ui.log(f"✅ {platform_name}七天收益历史已导出到: {file_path}")
            
        except Exception as e:
            error_msg = f"导出七天收益历史失败: {str(e)}"
            self.parent_ui.log(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)
    
    def _create_seven_day_income_excel(self, file_path: str, accounts_data, platform: str = None):
        """创建七天收益Excel报告 - 支持平台智能选择"""
        try:
            from openpyxl import Workbook
            from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
            from openpyxl.utils import get_column_letter
        except ImportError:
            raise ImportError("需要安装openpyxl库: pip install openpyxl")

        # 处理数据格式 - 如果是列表则转换为字典
        if isinstance(accounts_data, list):
            accounts_dict = {}
            for account_data in accounts_data:
                account_name = account_data.get("账号", "")
                if account_name:
                    accounts_dict[account_name] = account_data
            accounts_data = accounts_dict
        elif not isinstance(accounts_data, dict):
            accounts_data = {}

        # 获取平台信息
        platform_names = {
            'netease': '网易号',
            'toutiao': '头条号',
            'dayu': '大鱼号'
        }
        current_platform = platform or getattr(self.parent_ui, 'current_platform', 'netease')
        platform_name = platform_names.get(current_platform, '未知平台')

        # 创建工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = f"{platform_name}七天收益历史"
        
        # 定义样式
        header_font = Font(name="微软雅黑", size=12, bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
        data_font = Font(name="微软雅黑", size=10)
        border = Border(
            left=Side(style="thin"),
            right=Side(style="thin"),
            top=Side(style="thin"),
            bottom=Side(style="thin")
        )
        center_alignment = Alignment(horizontal="center", vertical="center")
        
        # 设置标题
        ws.merge_cells("A1:I1")
        title_cell = ws["A1"]
        title_cell.value = f"七天收益历史报告 - {datetime.datetime.now().strftime('%Y年%m月%d日')}"
        title_cell.font = Font(name="微软雅黑", size=16, bold=True, color="4472C4")
        title_cell.alignment = center_alignment
        
        # 设置表头
        headers = ["账号", "用户名", "今日", "昨日", "前日", "3天前", "4天前", "5天前", "6天前"]
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=3, column=col)
            cell.value = header
            cell.font = header_font
            cell.fill = header_fill
            cell.border = border
            cell.alignment = center_alignment
        
        # 填充数据
        row = 4
        for account, data in accounts_data.items():
            # 获取七天收益数据
            seven_day_data = data.get("七天收益", {})
            
            row_data = [
                account,
                data.get("用户名", ""),
                seven_day_data.get("今日", "¥0.00"),
                seven_day_data.get("昨日", "¥0.00"),
                seven_day_data.get("前日", "¥0.00"),
                seven_day_data.get("3天前", "¥0.00"),
                seven_day_data.get("4天前", "¥0.00"),
                seven_day_data.get("5天前", "¥0.00"),
                seven_day_data.get("6天前", "¥0.00")
            ]
            
            for col, value in enumerate(row_data, 1):
                cell = ws.cell(row=row, column=col)
                cell.value = value
                cell.font = data_font
                cell.border = border
                cell.alignment = center_alignment
            
            row += 1
        
        # 调整列宽
        column_widths = [20, 15, 12, 12, 12, 12, 12, 12, 12]
        for i, width in enumerate(column_widths, 1):
            ws.column_dimensions[get_column_letter(i)].width = width
        
        # 设置行高
        for row_num in range(1, row + 1):
            ws.row_dimensions[row_num].height = 25
        
        # 保存文件
        wb.save(file_path)
    
    def _get_platform_accounts_data(self, platform: str):
        """获取指定平台的账号数据 - 从详细数据表格获取"""
        try:
            # 优先从详细数据界面获取数据
            detailed_data = self._get_data_from_detail_viewer(platform)
            if detailed_data:
                self.parent_ui.log(f"📊 Excel导出: 从详细数据界面获取到 {len(detailed_data)} 条{platform}平台数据")
                return detailed_data

            # 回退到从AccountData获取数据
            all_accounts_data = self.account_data.get_all_accounts_data()

            # 如果没有数据，从账号管理器获取当前平台的账号
            if not all_accounts_data and hasattr(self.parent_ui, 'account_manager'):
                self.parent_ui.log(f"📊 Excel导出: 从账号管理器获取{platform}平台账号列表创建默认数据")

                # 获取当前平台的账号目录
                platform_account_dirs = {
                    'netease': self.config_manager.get("account_dir", "", platform="netease"),
                    'toutiao': self.config_manager.get("account_dir", "", platform="toutiao"),
                    'dayu': self.config_manager.get("account_dir", "", platform="dayu")
                }

                account_dir = platform_account_dirs.get(platform, "")
                loaded_accounts = self.parent_ui.account_manager.load_accounts()

                # 创建默认数据
                all_accounts_data = []
                for account in loaded_accounts:
                    default_data = {
                        "账号": account,
                        "用户名": "",
                        "总收益": "0",
                        "累计收益": "0",
                        "昨日收益": "0",
                        "总播放": "0",
                        "昨日播放": "0",
                        "总粉丝": "0",
                        "昨日粉丝": "0",
                        "草稿箱": "0",
                        "草稿箱数量": "0",
                        "可提现": "0",
                        "待提现": "0",
                        "状态": "未查询",
                        "更新时间": "",
                        "七天收益": {},
                        "平台": platform
                    }
                    all_accounts_data.append(default_data)

                self.parent_ui.log(f"📊 Excel导出: 为{platform}平台创建了 {len(all_accounts_data)} 条默认数据")

            # 过滤当前平台的数据
            platform_data = []
            for account_data in all_accounts_data:
                # 检查数据是否属于当前平台
                data_platform = account_data.get("平台", "")

                # 如果数据没有平台标识，根据数据文件来源判断
                if not data_platform:
                    # 根据账号数据的特征判断平台
                    if self._is_platform_data(account_data, platform):
                        platform_data.append(account_data)
                elif data_platform == platform:
                    platform_data.append(account_data)

            # 如果没有找到平台特定数据，返回所有数据（向后兼容）
            if not platform_data and all_accounts_data:
                self.parent_ui.log(f"⚠️ 未找到{platform}平台特定数据，返回所有数据")
                return all_accounts_data

            self.parent_ui.log(f"📊 Excel导出: 筛选出 {len(platform_data)} 条{platform}平台数据")
            return platform_data

        except Exception as e:
            self.parent_ui.log(f"❌ 获取平台数据失败: {e}")
            # 出错时返回所有数据作为回退
            return self.account_data.get_all_accounts_data()

    def _get_data_from_detail_viewer(self, platform: str):
        """从详细数据界面获取数据"""
        try:
            # 检查是否有打开的详细数据界面
            if not hasattr(self.parent_ui, 'data_viewer_window') or not self.parent_ui.data_viewer_window:
                return None

            # 获取详细数据界面的数据
            data_viewer = getattr(self.parent_ui.data_viewer_window, 'data_viewer', None)
            if not data_viewer or not hasattr(data_viewer, 'accounts_data'):
                return None

            # 获取详细数据界面中的账号数据
            accounts_data = data_viewer.accounts_data
            if not accounts_data:
                return None

            # 过滤当前平台的数据
            platform_data = []
            for account_data in accounts_data:
                # 检查数据是否属于当前平台
                data_platform = account_data.get("平台", "")

                # 如果数据没有平台标识，根据当前平台判断
                if not data_platform:
                    # 如果详细数据界面的current_platform与导出平台一致，则认为是当前平台数据
                    if hasattr(data_viewer, 'current_platform') and data_viewer.current_platform == platform:
                        platform_data.append(account_data)
                    # 或者根据账号数据特征判断
                    elif self._is_platform_data(account_data, platform):
                        platform_data.append(account_data)
                elif data_platform == platform:
                    platform_data.append(account_data)

            return platform_data if platform_data else accounts_data

        except Exception as e:
            self.parent_ui.log(f"❌ 从详细数据界面获取数据失败: {e}")
            return None

    def _is_platform_data(self, account_data: dict, platform: str) -> bool:
        """判断账号数据是否属于指定平台"""
        try:
            # 根据数据特征判断平台
            account_name = account_data.get("账号", "")

            # 简单的平台判断逻辑（可以根据实际情况调整）
            if platform == "toutiao":
                # 头条账号可能包含特定关键词或格式
                return "头条" in account_name or "toutiao" in account_name.lower()
            elif platform == "dayu":
                # 大鱼号账号可能包含特定关键词或格式
                return "大鱼" in account_name or "dayu" in account_name.lower()
            elif platform == "netease":
                # 网易号是默认平台
                return True

            return True  # 默认返回True，向后兼容

        except Exception:
            return True  # 出错时返回True，向后兼容

    def cleanup(self):
        """清理Excel导出管理器资源"""
        # Excel导出管理器无需特殊清理
        pass
