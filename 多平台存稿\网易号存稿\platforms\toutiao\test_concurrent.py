"""
头条号并发功能测试模块
验证头条号并发存稿功能的正确性、稳定性和性能表现
"""

import os
import sys
import time
import threading
from typing import List, Dict, Any

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try:
    from .processor import ToutiaoDraftProcessor
    from .concurrency import ToutiaoConcurrentManager
    from ...common.concurrent_pool import get_shared_pool
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
    from 网易号存稿.platforms.toutiao.processor import ToutiaoDraftProcessor
    from 网易号存稿.platforms.toutiao.concurrency import ToutiaoConcurrentManager
    from 网易号存稿.common.concurrent_pool import get_shared_pool


class ToutiaoConcurrentTester:
    """头条号并发功能测试器"""

    def __init__(self, test_config: Dict[str, Any]):
        """
        初始化测试器

        Args:
            test_config: 测试配置
        """
        self.test_config = test_config
        self.test_results = {}
        self.log_messages = []

    def log(self, message: str) -> None:
        """
        记录日志

        Args:
            message: 日志消息
        """
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        log_msg = f"[{timestamp}] {message}"
        self.log_messages.append(log_msg)
        print(log_msg)

    def test_concurrent_manager_initialization(self) -> bool:
        """
        测试并发管理器初始化

        Returns:
            测试是否通过
        """
        self.log("开始测试并发管理器初始化...")
        
        try:
            # 创建并发管理器
            manager = ToutiaoConcurrentManager(
                account_dir=self.test_config.get("account_dir", "./test_accounts"),
                processed_dir=self.test_config.get("processed_dir", "./test_videos"),
                processed_covers_dir=self.test_config.get("processed_covers_dir", "./test_covers"),
                archive_completed=True,
                headless_mode=True,
                draft_limit=2,
                loop_limit=1,
                log_callback=self.log,
                max_workers=3
            )

            # 检查管理器属性
            assert manager.account_dir == self.test_config.get("account_dir", "./test_accounts")
            assert manager.max_workers == 3
            assert manager.draft_limit == 2
            assert manager.is_running == False

            self.log("✅ 并发管理器初始化测试通过")
            return True

        except Exception as e:
            self.log(f"❌ 并发管理器初始化测试失败: {str(e)}")
            return False

    def test_shared_pool_integration(self) -> bool:
        """
        测试共享并发池集成

        Returns:
            测试是否通过
        """
        self.log("开始测试共享并发池集成...")
        
        try:
            # 获取共享池实例
            shared_pool = get_shared_pool()
            
            # 初始化共享池
            success = shared_pool.initialize_pool(max_workers=5)
            assert success, "共享池初始化失败"

            # 检查池状态
            status = shared_pool.get_pool_status()
            assert status["pool_initialized"] == True
            assert status["max_workers"] == 5

            # 提交测试任务
            def test_task(task_id: str) -> str:
                time.sleep(1)
                return f"Task {task_id} completed"

            task_id = shared_pool.submit_task("toutiao", test_task, "test_1")
            assert task_id is not None, "任务提交失败"

            # 获取任务结果
            result = shared_pool.get_task_result(task_id, timeout=5)
            assert result == "Task test_1 completed", f"任务结果不正确: {result}"

            self.log("✅ 共享并发池集成测试通过")
            return True

        except Exception as e:
            self.log(f"❌ 共享并发池集成测试失败: {str(e)}")
            return False

    def test_concurrent_processing_simulation(self) -> bool:
        """
        测试并发处理模拟

        Returns:
            测试是否通过
        """
        self.log("开始测试并发处理模拟...")
        
        try:
            # 创建测试账号列表
            test_accounts = ["test_account_1", "test_account_2", "test_account_3"]
            
            # 创建并发管理器
            manager = ToutiaoConcurrentManager(
                account_dir=self.test_config.get("account_dir", "./test_accounts"),
                processed_dir=self.test_config.get("processed_dir", "./test_videos"),
                processed_covers_dir=self.test_config.get("processed_covers_dir", "./test_covers"),
                archive_completed=False,  # 测试时不归档
                headless_mode=True,
                draft_limit=1,
                loop_limit=1,
                log_callback=self.log,
                max_workers=2
            )

            # 设置进度回调
            progress_updates = {}
            def progress_callback(account: str, progress: int, status: str, details: str, progress_data: Dict = None):
                progress_updates[account] = {
                    "progress": progress,
                    "status": status,
                    "details": details,
                    "data": progress_data or {}
                }
                self.log(f"进度更新 - {account}: {progress}% ({status}) - {details}")

            manager.set_progress_callback(progress_callback)

            # 模拟启动并发处理（不实际处理视频）
            # 这里我们只测试管理器的逻辑，不涉及实际的浏览器操作
            self.log("模拟并发处理启动...")
            
            # 检查初始状态
            assert manager.is_running == False
            
            # 测试进度获取
            progress = manager.get_progress()
            assert isinstance(progress, dict)

            self.log("✅ 并发处理模拟测试通过")
            return True

        except Exception as e:
            self.log(f"❌ 并发处理模拟测试失败: {str(e)}")
            return False

    def test_processor_concurrent_integration(self) -> bool:
        """
        测试处理器并发集成

        Returns:
            测试是否通过
        """
        self.log("开始测试处理器并发集成...")
        
        try:
            # 创建头条号处理器
            processor = ToutiaoDraftProcessor(
                account_dir=self.test_config.get("account_dir", "./test_accounts"),
                processed_dir=self.test_config.get("processed_dir", "./test_videos"),
                processed_covers_dir=self.test_config.get("processed_covers_dir", "./test_covers"),
                archive_completed=False,
                headless_mode=True,
                draft_limit=1,
                loop_limit=1,
                log_callback=self.log
            )

            # 检查并发方法是否存在
            assert hasattr(processor, 'start_concurrent'), "处理器缺少start_concurrent方法"
            assert hasattr(processor, 'stop_concurrent'), "处理器缺少stop_concurrent方法"
            assert hasattr(processor, 'get_concurrent_progress'), "处理器缺少get_concurrent_progress方法"
            assert hasattr(processor, 'is_concurrent_running'), "处理器缺少is_concurrent_running方法"

            # 检查初始状态
            assert processor.is_concurrent_running() == False
            
            # 获取初始进度
            progress = processor.get_concurrent_progress()
            assert isinstance(progress, dict)

            self.log("✅ 处理器并发集成测试通过")
            return True

        except Exception as e:
            self.log(f"❌ 处理器并发集成测试失败: {str(e)}")
            return False

    def run_all_tests(self) -> Dict[str, bool]:
        """
        运行所有测试

        Returns:
            测试结果字典
        """
        self.log("🚀 开始头条号并发功能测试")
        
        tests = [
            ("并发管理器初始化", self.test_concurrent_manager_initialization),
            ("共享并发池集成", self.test_shared_pool_integration),
            ("并发处理模拟", self.test_concurrent_processing_simulation),
            ("处理器并发集成", self.test_processor_concurrent_integration)
        ]

        results = {}
        passed = 0
        total = len(tests)

        for test_name, test_func in tests:
            self.log(f"\n--- 执行测试: {test_name} ---")
            try:
                result = test_func()
                results[test_name] = result
                if result:
                    passed += 1
            except Exception as e:
                self.log(f"❌ 测试 {test_name} 执行异常: {str(e)}")
                results[test_name] = False

        # 输出测试总结
        self.log(f"\n🎯 测试总结:")
        self.log(f"总测试数: {total}")
        self.log(f"通过测试: {passed}")
        self.log(f"失败测试: {total - passed}")
        self.log(f"通过率: {passed / total * 100:.1f}%")

        if passed == total:
            self.log("🎉 所有测试通过！头条号并发功能正常")
        else:
            self.log("⚠️ 部分测试失败，请检查相关功能")

        return results


def main():
    """主函数"""
    # 测试配置
    test_config = {
        "account_dir": "./test_accounts",
        "processed_dir": "./test_videos", 
        "processed_covers_dir": "./test_covers"
    }

    # 创建测试器
    tester = ToutiaoConcurrentTester(test_config)
    
    # 运行测试
    results = tester.run_all_tests()
    
    # 返回测试结果
    return results


if __name__ == "__main__":
    main()
